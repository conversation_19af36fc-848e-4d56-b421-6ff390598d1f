module.exports = {
  prompt: ({ inquirer }) => {
    const questions = [
      {
        type: 'select',
        name: 'category',
        message: 'what type of component?',
        choices: ['UI', 'components', 'modules', 'pages'],
      },
      {
        type: 'input',
        name: 'component_name',
        message: 'What is the component name?',
      },
      {
        type: 'input',
        name: 'path_to_folders',
        message: 'Path to folders? (относительно папки компонента)',
      },
    ]
    return inquirer.prompt(questions).then(answers => {
      const { category, component_name, path_to_folders } = answers
      const path = `${component_name}`
      const absPath = `src/shared/${category}/${
        path_to_folders ? `${path_to_folders}/` : ''
      }${path}`
      return { ...answers, path, absPath, category }
    })
  },
}
