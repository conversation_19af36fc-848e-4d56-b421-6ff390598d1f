---
to: <%= absPath %>/<%= component_name %>.stories.tsx
---
// import React from 'react'
// import { <%= component_name %> } from './<%= component_name %>'
// import { Meta, ComponentStory } from '@storybook/react'

// export default {
// 	title: '<%= component_name %>',
// 	component: <%= component_name %>,
// 	// decorators: [story => <div style={{ padding: '3rem' }}>{story()}</div>],
// 	argTypes: {
// 		className: {
// 			table: {
// 				disable: true,
// 			},
// 		},
// 	},
// } as Meta<typeof <%= component_name %>>

// const Template: ComponentStory<typeof <%= component_name %>> = args => <<%= component_name %> {...args} />

// export const Default = Template.bind({})
