---
to: <%= absPath %>/<%= component_name %>.tsx
---
import React, { FC } from 'react'
import styles from './<%= component_name %>.module.scss'
import classNamesBind from 'classnames/bind'
import { <%= component_name %>Props } from './<%= component_name %>.d'
import { use<%= component_name %> } from './use<%= component_name %>'

const cx = classNamesBind.bind(styles)

export const <%= component_name %>: FC<<%= component_name %>Props.Props> = props => {
    const { className } = props

    const {} = use<%= component_name %>()

    return <div className={cx('wrapper', className)}></div>
}