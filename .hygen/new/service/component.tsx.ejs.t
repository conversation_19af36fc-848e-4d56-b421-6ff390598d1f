---
to: <%= absPath %>/<%= component_name %>Service.ts
---
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { UUID } from 'react-app/new/types/IUser'
import { getBaseQuery } from './helper'

export const <%= var_name %>API = createApi({
	reducerPath: '<%= var_name %>API',
	baseQuery: fetchBaseQuery(getBaseQuery()),
	tagTypes: [],
	endpoints: build => ({
		getPost: build.query<any[], UUID>({
			query: (id) => ({
				url: `posts/${id}`,
			}),
			transformResponse: (response: { data: any[] }, meta, arg) => response.data,
			providesTags: result => [],
        })
    })
})

export const { useGetPostQuery } = <%= var_name %>API