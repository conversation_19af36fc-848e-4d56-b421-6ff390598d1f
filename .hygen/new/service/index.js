module.exports = {
	prompt: ({ inquirer }) => {
		const questions = [
			{
				type: 'input',
				name: 'component_name',
				message: 'What is the service name? (capitalized)',
			},
			{
				type: 'input',
				name: 'var_name',
				message: 'What is the service name? (lowercase)',
			},
		]
		return inquirer.prompt(questions).then(answers => {
			const { component_name, var_name } = answers
			const path = `${component_name}`
			const absPath = `src/react/new/store/services`
			return { ...answers, path, absPath }
		})
	},
}
