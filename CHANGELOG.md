Changelog
### [0.0.25](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.24...v0.0.25) (2024-04-19)


### Features

* **TSK-363:** add error cleaning ([#899](https://github.com/Secure-T-Team/edu-frontend/issues/899)) ([8cc0999](https://github.com/Secure-T-Team/edu-frontend/commit/8cc09993a005ff5c75e7c09a925389f97bb97485))
* **TSK-363:** added checks ([#885](https://github.com/Secure-T-Team/edu-frontend/issues/885)) ([ba3136e](https://github.com/Secure-T-Team/edu-frontend/commit/ba3136e6d57f31675d7c2609fdeb33b4daeb3ecf))
* **TSK-363:** update description length ([#922](https://github.com/Secure-T-Team/edu-frontend/issues/922)) ([4c75959](https://github.com/Secure-T-Team/edu-frontend/commit/4c75959c3210537a4ba61855bff91594615e8d8b))
* **TSK-575:** Added new blocks to org statistics ([#933](https://github.com/Secure-T-Team/edu-frontend/issues/933)) ([affb983](https://github.com/Secure-T-Team/edu-frontend/commit/affb983fc8bfb36b83049fbaaff9f5cf323ec485))
* **TSK-575:** Some widget fixes ([#934](https://github.com/Secure-T-Team/edu-frontend/issues/934)) ([73b4c3c](https://github.com/Secure-T-Team/edu-frontend/commit/73b4c3cce92501f8671bb386c45c125ca1743030))
* **TSK-764:** добавлены таблицы для журнала действий ([073666d](https://github.com/Secure-T-Team/edu-frontend/commit/073666d0b0e752210012e856c9f9853b5054308f))
* **TSK-764:** пару изменений ([e2452c8](https://github.com/Secure-T-Team/edu-frontend/commit/e2452c87a974dfd401cbaa3a08681ca5eb87c5f9))
* **TSK-764:** поправил импорты ([a9baa0e](https://github.com/Secure-T-Team/edu-frontend/commit/a9baa0ece7c92284c27100f7bfbf8d202dab9568))
* **TSK-769:** Added 'uncheck' users when All selected ([#936](https://github.com/Secure-T-Team/edu-frontend/issues/936)) ([423c905](https://github.com/Secure-T-Team/edu-frontend/commit/423c905c48fd8aa8d1c08b1efaa75ee39ca113a5))
* **TSK-769:** Added search and filter fields to reinvite and delete requests ([#947](https://github.com/Secure-T-Team/edu-frontend/issues/947)) ([328512f](https://github.com/Secure-T-Team/edu-frontend/commit/328512f09f554e331e2064c3c710b3ed9f213b03))
* **TSK-788:** add events for button and input cleaner for editor ([#920](https://github.com/Secure-T-Team/edu-frontend/issues/920)) ([a1dcf02](https://github.com/Secure-T-Team/edu-frontend/commit/a1dcf02fbaa7c71ece751a8a575521d75d15acf3))
* **TSK-836:** fix sort multiselect in firefox ([#915](https://github.com/Secure-T-Team/edu-frontend/issues/915)) ([4017140](https://github.com/Secure-T-Team/edu-frontend/commit/401714035a2b1ef266f6b72f1c54043d81476692))
* **TSK-836:** multiple select phish rules ([#908](https://github.com/Secure-T-Team/edu-frontend/issues/908)) ([b6d686d](https://github.com/Secure-T-Team/edu-frontend/commit/b6d686d87961a66f294ae5ab43c297391afaa679))
* **TSK-864:** Fix bug with instant button loading ([#929](https://github.com/Secure-T-Team/edu-frontend/issues/929)) ([b4fca0c](https://github.com/Secure-T-Team/edu-frontend/commit/b4fca0c74b3d37e01779f551348af146905eaac1))
* **TSK-865:** Added max quiz attempts field ([#925](https://github.com/Secure-T-Team/edu-frontend/issues/925)) ([54c6ccf](https://github.com/Secure-T-Team/edu-frontend/commit/54c6ccf5592ea23b0caa1c2d466a8a54f99f6d1e))
* **TSK-900:** add end phishing campaign email date ([7843ccf](https://github.com/Secure-T-Team/edu-frontend/commit/7843ccfa0a3848a39be0d32b7ff8f93f63312cfb))
* **TSK-906:** fix pagination ([#949](https://github.com/Secure-T-Team/edu-frontend/issues/949)) ([fcf8701](https://github.com/Secure-T-Team/edu-frontend/commit/fcf8701a6d89e56a04c9c117489a6cda895f0303))
* **TSK-906:** search and filter for phish statistics page ([#932](https://github.com/Secure-T-Team/edu-frontend/issues/932)) ([573a23a](https://github.com/Secure-T-Team/edu-frontend/commit/573a23ad35c9584d6fad254417cac8c2f8593f0c))
* **TSK-911:** ckeditor ([#886](https://github.com/Secure-T-Team/edu-frontend/issues/886)) ([6baab88](https://github.com/Secure-T-Team/edu-frontend/commit/6baab8871e09c7a69d2f2c3c271eed82c3720faf))
* **TSK-911:** fix and refactor editor ([#894](https://github.com/Secure-T-Team/edu-frontend/issues/894)) ([88c0642](https://github.com/Secure-T-Team/edu-frontend/commit/88c0642ef9c9b909f6a404cc977486da37675319))
* **TSK-912:** add custom for editor ([#897](https://github.com/Secure-T-Team/edu-frontend/issues/897)) ([f079242](https://github.com/Secure-T-Team/edu-frontend/commit/f07924238dfe7b99070e909008ce1c3d1f57b398))
* **TSK-914:** Added course form submit disable if no chosen modules ([#938](https://github.com/Secure-T-Team/edu-frontend/issues/938)) ([6c4e3ec](https://github.com/Secure-T-Team/edu-frontend/commit/6c4e3eca958710deaafd7afeabb45dcee47c83ea))
* **TSK-916:** auto formatting for froms in phish pages ([#924](https://github.com/Secure-T-Team/edu-frontend/issues/924)) ([774c921](https://github.com/Secure-T-Team/edu-frontend/commit/774c921bf31decd57a55e010165db41fd7cb2a2c))
* **TSK-924:** полное название шаблона по наведению ([#903](https://github.com/Secure-T-Team/edu-frontend/issues/903)) ([a2d176d](https://github.com/Secure-T-Team/edu-frontend/commit/a2d176d79d8be24f134f3cd2132cccf23d804d91))
* **TSK-926:** Employess in group style change ([#892](https://github.com/Secure-T-Team/edu-frontend/issues/892)) ([8645fa1](https://github.com/Secure-T-Team/edu-frontend/commit/8645fa1c207098a2918fad9842dbf067c1d0bfda))
* **tsk-929:** redirect pages ([#935](https://github.com/Secure-T-Team/edu-frontend/issues/935)) ([f6a21da](https://github.com/Secure-T-Team/edu-frontend/commit/f6a21da0a91262e81604161ac92b0a3c1b9aa6cb))
* **TSK-933:** add hint ([011055d](https://github.com/Secure-T-Team/edu-frontend/commit/011055d71d07a20ba9274481142c548903a91cba))
* **TSK-939:** Change default showAllAnswers state ([#923](https://github.com/Secure-T-Team/edu-frontend/issues/923)) ([833f7ee](https://github.com/Secure-T-Team/edu-frontend/commit/833f7ee06e3ea4e993d19bab0a679abcfdaac5d7))
* **TSK-940:** Added tooltip hint to Show all answers switch ([#937](https://github.com/Secure-T-Team/edu-frontend/issues/937)) ([599b333](https://github.com/Secure-T-Team/edu-frontend/commit/599b333b89f2febc03a65bc8b0fccab83912b76c))
* **TSK-946:** Added new GeneralInformation widget ([#914](https://github.com/Secure-T-Team/edu-frontend/issues/914)) ([a71e057](https://github.com/Secure-T-Team/edu-frontend/commit/a71e0579f7ff81075805018462935d27f7692f68))
* **TSK-947:** Added risk level dangerous widget ([#916](https://github.com/Secure-T-Team/edu-frontend/issues/916)) ([4cc2dad](https://github.com/Secure-T-Team/edu-frontend/commit/4cc2dadb19d57512e9759a09945b2286ff2c898a))
* **TSK-949:** Added some new statistics blocks ([#921](https://github.com/Secure-T-Team/edu-frontend/issues/921)) ([ef93b6e](https://github.com/Secure-T-Team/edu-frontend/commit/ef93b6e4fb5f634a7ed62ad186397020c80f82e4))
* **TSK-966:** assign scorm courses by tag ([#948](https://github.com/Secure-T-Team/edu-frontend/issues/948)) ([f256548](https://github.com/Secure-T-Team/edu-frontend/commit/f2565486269e8c70b5bdd1d0a5a81f21cdba1c70))
* **TSK-977:** add time range to the graphics ([#941](https://github.com/Secure-T-Team/edu-frontend/issues/941)) ([9482de1](https://github.com/Secure-T-Team/edu-frontend/commit/9482de11ce23cf1e0b82fa3534302bd892e300fb))
* **TSK-980:** Changed getActiveCourses response ([#943](https://github.com/Secure-T-Team/edu-frontend/issues/943)) ([6b53f93](https://github.com/Secure-T-Team/edu-frontend/commit/6b53f9379894003fb513f5446bbe181a5070f46d))
* **TSK-995:** added color calculations for graphics ([#950](https://github.com/Secure-T-Team/edu-frontend/issues/950)) ([56fb72b](https://github.com/Secure-T-Team/edu-frontend/commit/56fb72b94d4f2367ff24ffbf0e58d4b76461e119))
* **TSK-996:** add date sorting ([#951](https://github.com/Secure-T-Team/edu-frontend/issues/951)) ([048b956](https://github.com/Secure-T-Team/edu-frontend/commit/048b956454302ed640c03f246868a3301fe011e1))
* **TSK974:** Removed ahead and far_behind groups ([#944](https://github.com/Secure-T-Team/edu-frontend/issues/944)) ([1076c93](https://github.com/Secure-T-Team/edu-frontend/commit/1076c93cdbb24fcff9b3d6f08e75901ebe414167))


### Bug Fixes

* (TSK-764) employee email when name is empty ([bfb3d2f](https://github.com/Secure-T-Team/edu-frontend/commit/bfb3d2f53182cc6b0d19161d03575fd1c1068268))
* AD redirect after ldap request ([d2fdde5](https://github.com/Secure-T-Team/edu-frontend/commit/d2fdde5cf3facbfbd1eacf60c9c31db4c747f518))
* change log pre-wrap ([f723da8](https://github.com/Secure-T-Team/edu-frontend/commit/f723da86c86ab1d40e345ac7abfe4e4a77fef402))
* change log table correct date offset ([3294ad6](https://github.com/Secure-T-Team/edu-frontend/commit/3294ad6617a326dee6d802bc630c1e2b0b12208f))
* change-log markdown ([8e05e0f](https://github.com/Secure-T-Team/edu-frontend/commit/8e05e0f3c51b635ae4cd421403609abf41a0302a))
* delete animation for tooltip ([#940](https://github.com/Secure-T-Team/edu-frontend/issues/940)) ([c88a1be](https://github.com/Secure-T-Team/edu-frontend/commit/c88a1be947557e3915adf6e5b267f9f89e160ec3))
* education graphic purple ([23c8a26](https://github.com/Secure-T-Team/edu-frontend/commit/23c8a26761df026235bb9911a527bf912388954b))
* employee graphic ([a633c2b](https://github.com/Secure-T-Team/edu-frontend/commit/a633c2bb76ebb43cb5563e0c517fca50197abc74))
* fix risk success range ([#954](https://github.com/Secure-T-Team/edu-frontend/issues/954)) ([6b55a70](https://github.com/Secure-T-Team/edu-frontend/commit/6b55a70f846fa42b71dcb705a9fd4ccc1e79fe27))
* increase count of ticks in report modal to 1 000 ([97895ed](https://github.com/Secure-T-Team/edu-frontend/commit/97895edb3218caf7b656b5d9079a3ea5b13101c9))
* isUnmount flag add to modal ([33c46d8](https://github.com/Secure-T-Team/edu-frontend/commit/33c46d884390a8c230b0a4215fc6c77a642fb4b5))
* merge conflicts ([2f22eae](https://github.com/Secure-T-Team/edu-frontend/commit/2f22eae19baabf1c7eaeaad7957e9508fc1dab19))
* modal btn and dimension risk level graphic ([e197bca](https://github.com/Secure-T-Team/edu-frontend/commit/e197bca8fd3f17f136acd3baf89af372cecb8699))
* no active course ([27727c8](https://github.com/Secure-T-Team/edu-frontend/commit/27727c8165dd382c0033e7488484c631661e26d2))
* optional show arrow ([a1ab40b](https://github.com/Secure-T-Team/edu-frontend/commit/a1ab40b96deb18ae72981018462c5c755987d7af))
* organizational structures fixes ([469a6b6](https://github.com/Secure-T-Team/edu-frontend/commit/469a6b699a2e4ff5802c5f97fe9fedbc4ef906c6))
* padding sidebar links content ([8851f66](https://github.com/Secure-T-Team/edu-frontend/commit/8851f66bcab9d5ca1b51b494b2b54473d0f9ae1b))
* select all btn ([0a68535](https://github.com/Secure-T-Team/edu-frontend/commit/0a68535f48f3ce0c1c22afea51987f8d96bffc7f))
* statistics page title skeleton when title is empty/null ([3702d1a](https://github.com/Secure-T-Team/edu-frontend/commit/3702d1abd7d855cdc1fcaab0f6a4a10706473f0a))
* task 929 ([84cf3e9](https://github.com/Secure-T-Team/edu-frontend/commit/84cf3e913d2102b97da74d974b10aba80fb33792))
* **TSK-476:** add option_delete and option_update and ([0da356a](https://github.com/Secure-T-Team/edu-frontend/commit/0da356ad02478fe7de9c300e8793913c1cfb312e))
* **TSK-575:** Some fixes ([#952](https://github.com/Secure-T-Team/edu-frontend/issues/952)) ([d370dfe](https://github.com/Secure-T-Team/edu-frontend/commit/d370dfe29889f6a8bfe449d6afb7ebb59476e55b))
* **TSK-892:** fix select departments while searching ([#926](https://github.com/Secure-T-Team/edu-frontend/issues/926)) ([26bd297](https://github.com/Secure-T-Team/edu-frontend/commit/26bd297308729715d19fdb435b383e1897c5ea6f))
* **TSK-896:** имя у сотр не задано ([#902](https://github.com/Secure-T-Team/edu-frontend/issues/902)) ([1d01b52](https://github.com/Secure-T-Team/edu-frontend/commit/1d01b520d307c3f11992ac1054f50feb38e2f6c2))
* **TSK-901:** Employess and Department statistics table refactoring ([#889](https://github.com/Secure-T-Team/edu-frontend/issues/889)) ([e3bcde1](https://github.com/Secure-T-Team/edu-frontend/commit/e3bcde14231792df7b44da363c2ef5e3da85ee1f))
* **TSK-901:** Fix employess table bugs ([#891](https://github.com/Secure-T-Team/edu-frontend/issues/891)) ([f6f3326](https://github.com/Secure-T-Team/edu-frontend/commit/f6f3326ca311e9b20ae29cf95673eaddc5de4403))
* **TSK-902:** Create and edit quiz dialogs refactoring ([#890](https://github.com/Secure-T-Team/edu-frontend/issues/890)) ([5a1bf24](https://github.com/Secure-T-Team/edu-frontend/commit/5a1bf240074a88f7693c461494143200a7661025))
* **TSK-905:** replace old checkbox with new checkbox in progress ([#930](https://github.com/Secure-T-Team/edu-frontend/issues/930)) ([ae349ed](https://github.com/Secure-T-Team/edu-frontend/commit/ae349eddd953deb7a3be664e9f212d8a228a1890))
* **TSK-913:** fix replace button ([#910](https://github.com/Secure-T-Team/edu-frontend/issues/910)) ([5470712](https://github.com/Secure-T-Team/edu-frontend/commit/5470712124af498fc993043f948ba383ea6824e3))
* **TSK-920:** Fix bug with slide upload cancel ([#896](https://github.com/Secure-T-Team/edu-frontend/issues/896)) ([e9d316b](https://github.com/Secure-T-Team/edu-frontend/commit/e9d316bd19f4676ae7fd5f047e3f695d3dfc9db0))
* **TSK-920:** Fix File uploader cancel bug ([#898](https://github.com/Secure-T-Team/edu-frontend/issues/898)) ([dccefd7](https://github.com/Secure-T-Team/edu-frontend/commit/dccefd77985f807eacad2ab92874d08945f9e45a))
* **TSK-922:** set correct cat and domain ([#913](https://github.com/Secure-T-Team/edu-frontend/issues/913)) ([ebac8c9](https://github.com/Secure-T-Team/edu-frontend/commit/ebac8c9c432092891f3930b052ca5b74270e6cc1))
* **TSK-923:** высота модалки ([#901](https://github.com/Secure-T-Team/edu-frontend/issues/901)) ([93e6d56](https://github.com/Secure-T-Team/edu-frontend/commit/93e6d56b20298856db74ae34c47c7bcac86b3e39))
* **TSK-925:** Removed extra create department success notify ([#895](https://github.com/Secure-T-Team/edu-frontend/issues/895)) ([3f4248a](https://github.com/Secure-T-Team/edu-frontend/commit/3f4248a634d76b71a526a915f5637ebaa172d06e))
* **TSK-935:** removed the extra fetchDepartmentUsers call ([#900](https://github.com/Secure-T-Team/edu-frontend/issues/900)) ([2059bab](https://github.com/Secure-T-Team/edu-frontend/commit/2059babec59b9766eca429fc012fa47df6cfa11b))
* **TSK-938:** Fix employess table fast scroll bug ([#907](https://github.com/Secure-T-Team/edu-frontend/issues/907)) ([3cacee7](https://github.com/Secure-T-Team/edu-frontend/commit/3cacee7f5151cc53b25d135f264d6ec30580ebcd))
* **TSK-938:** Removed wrong placeholder while switching tabs in statistics ([#909](https://github.com/Secure-T-Team/edu-frontend/issues/909)) ([174d57d](https://github.com/Secure-T-Team/edu-frontend/commit/174d57d81243e3e1adb40bb4b17605e6d430a3f0))
* **TSK-942:** Return by_tag query param to employess and department tables ([#905](https://github.com/Secure-T-Team/edu-frontend/issues/905)) ([5b735af](https://github.com/Secure-T-Team/edu-frontend/commit/5b735afea1482b18966fd56e735cbc7043d67508))
* **TSK-944:** Refetch departments after import employess from file ([#906](https://github.com/Secure-T-Team/edu-frontend/issues/906)) ([7dfc7c8](https://github.com/Secure-T-Team/edu-frontend/commit/7dfc7c8d2a794e4dbf35ac2137db2dba462f1bea))
* **TSK-946:** Change risk level color condition ([#939](https://github.com/Secure-T-Team/edu-frontend/issues/939)) ([9470f55](https://github.com/Secure-T-Team/edu-frontend/commit/9470f5553ce7b12af5dec8ef1ec740a25c2094f1))
* **TSK-949:** Fix some statistics widget ([#945](https://github.com/Secure-T-Team/edu-frontend/issues/945)) ([54fc8f3](https://github.com/Secure-T-Team/edu-frontend/commit/54fc8f3b4c8b4f8a9b86091188d2ca82ba348ccb))
* **TSK-957:** fix twofa modal width to fit-content ([b214eec](https://github.com/Secure-T-Team/edu-frontend/commit/b214eec5913b5f0673db88c013ed837af0047779))
* **TSK-969:** Change delete and reinvite body requests ([#931](https://github.com/Secure-T-Team/edu-frontend/issues/931)) ([2858e9d](https://github.com/Secure-T-Team/edu-frontend/commit/2858e9d5b899bb27265844425fdbafacabb96009))
* **TSL-932:** fixed the lack of rerender ([#911](https://github.com/Secure-T-Team/edu-frontend/issues/911)) ([ff72ea9](https://github.com/Secure-T-Team/edu-frontend/commit/ff72ea9e477fabd4e3040d6af167164fbd8e84f1))


### Refactoring

* **TSK-363:** delete extra code ([#893](https://github.com/Secure-T-Team/edu-frontend/issues/893)) ([c6db055](https://github.com/Secure-T-Team/edu-frontend/commit/c6db055ca19c78f23819a2e0ea57552a3a5d2910))
* **TSK-905:** implementation of a checkbox via input and label ([#917](https://github.com/Secure-T-Team/edu-frontend/issues/917)) ([6408289](https://github.com/Secure-T-Team/edu-frontend/commit/6408289e373003d488cea8ff762ec2c7c5571d68))

### [0.0.24](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.23...v0.0.24) (2024-04-16)

### Bug Fixes

- **correct-otp-image:** add feat 476 ([668c878](https://github.com/Secure-T-Team/edu-frontend/commit/668c878587f3b91c5f7771062bbfc9ef632a6129))
- **correct-otp-image:** swap data in employee graphic ([2aac279](https://github.com/Secure-T-Team/edu-frontend/commit/2aac279478e44655c5fcb924aa73bc0ec2a9bf38))
- **TSK-476:** add option_delete and option_update and ([998c281](https://github.com/Secure-T-Team/edu-frontend/commit/998c281327faf69bca490ed011dba67a60f2b29f))

### [0.0.23](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.22...v0.0.23) (2024-04-15)

### Bug Fixes

- **TSK-957:** fix twofa modal width to fit-content ([f82b23b](https://github.com/Secure-T-Team/edu-frontend/commit/f82b23bf802d4dd3dc678ed9e70b49041fdde2db))

### [0.0.22](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.21...v0.0.22) (2024-03-22)

### [0.0.21](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.20...v0.0.21) (2024-03-11)

### Features

- **tsk 336:** add favicon ([#884](https://github.com/Secure-T-Team/edu-frontend/issues/884)) ([af0ecc3](https://github.com/Secure-T-Team/edu-frontend/commit/af0ecc30a88457fb8726bcf3e10e23199c3e2b4f))

### Bug Fixes

- add condition unless ([28b0644](https://github.com/Secure-T-Team/edu-frontend/commit/28b0644b194d8b36e983495cd40d5bfa80ecd0d3))
- favicon condition ([b01116c](https://github.com/Secure-T-Team/edu-frontend/commit/b01116c8b33e2afcc2b4e3512e717df450c9f732))
- **TSK-909:** добавлен тип для кнопки ([9ca5924](https://github.com/Secure-T-Team/edu-frontend/commit/9ca5924b72848910cf35f8023f91a1c0475ae463))
- **TSK-917:** Fix module preview carousel bug ([#887](https://github.com/Secure-T-Team/edu-frontend/issues/887)) ([26776eb](https://github.com/Secure-T-Team/edu-frontend/commit/26776ebec66b6200e50a125dcb8a8725e3d8fbaf))
- вернул метаинфу для handlebars ([7075b90](https://github.com/Secure-T-Team/edu-frontend/commit/7075b904a84b977aea0575b4484241dca9d8be28))
- возвращаю скрытие ([b66b302](https://github.com/Secure-T-Team/edu-frontend/commit/b66b3023391c1eab20216fac67c2441536a5946c))
- скрыл метаинфу для лого в отображении ([b55aef5](https://github.com/Secure-T-Team/edu-frontend/commit/b55aef5f40e3453bb8e76532a48569efdf0abb84))
- скрытие метаинфы по логотипу ([f5e7917](https://github.com/Secure-T-Team/edu-frontend/commit/f5e7917c3988aba48265ad7dd3939615b92675b8))
- убрал скрытие дисплея для fav icon ([a77b321](https://github.com/Secure-T-Team/edu-frontend/commit/a77b321082b39fbd126c5a4f53f70a1a782763fb))

### [0.0.20](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.19...v0.0.20) (2024-03-07)

### Features

- add husky build project before each commit to check errors ([3fed1ab](https://github.com/Secure-T-Team/edu-frontend/commit/3fed1abab031e4e0d63d4202c1eac12e2c8c0a05))
- **TSK-362:** add disabled for button ([#870](https://github.com/Secure-T-Team/edu-frontend/issues/870)) ([45a138d](https://github.com/Secure-T-Team/edu-frontend/commit/45a138d43fa74d6a2548fd911049ae70efcb87d4))
- **TSK-363:** add new error text for course creating ([#871](https://github.com/Secure-T-Team/edu-frontend/issues/871)) ([7926df7](https://github.com/Secure-T-Team/edu-frontend/commit/7926df72f87d28fb890de168ed67a9c35dad9ee4))
- **TSK-476:** add ad sync time ([#862](https://github.com/Secure-T-Team/edu-frontend/issues/862)) ([1f698ab](https://github.com/Secure-T-Team/edu-frontend/commit/1f698ab3140fad4e132336d814be5c41854c0c19))
- **TSK-549:** update old request to rtk and delete newrequest ([#861](https://github.com/Secure-T-Team/edu-frontend/issues/861)) ([be1cbcc](https://github.com/Secure-T-Team/edu-frontend/commit/be1cbccf1ae2b53ab9eef81fe193d8829a6e263d))
- **TSK-549:** update old requests to rtk query (newrequest, requestFormData) ([#856](https://github.com/Secure-T-Team/edu-frontend/issues/856)) ([0a5b0bb](https://github.com/Secure-T-Team/edu-frontend/commit/0a5b0bba5b3c2f64a5aee4a5a54a57f648d636cb))
- **TSK-722:** fixed sending the same life days in newbie tag ([#857](https://github.com/Secure-T-Team/edu-frontend/issues/857)) ([82292f8](https://github.com/Secure-T-Team/edu-frontend/commit/82292f885b6a76bfb88e67b2afb3eac25ff0799a))
- **TSK-809:** update tip text ([#854](https://github.com/Secure-T-Team/edu-frontend/issues/854)) ([1bec040](https://github.com/Secure-T-Team/edu-frontend/commit/1bec04064c1fb58a8e3ba6404e6210498a7a39d5))
- **TSK-851:** excluded custom tag from phish ([#876](https://github.com/Secure-T-Team/edu-frontend/issues/876)) ([da1be4e](https://github.com/Secure-T-Team/edu-frontend/commit/da1be4ecea5d38a19acbcdde49eb19700a2a770b)), closes [#877](https://github.com/Secure-T-Team/edu-frontend/issues/877) [#878](https://github.com/Secure-T-Team/edu-frontend/issues/878)
- **TSK-855:** update error handling ([#855](https://github.com/Secure-T-Team/edu-frontend/issues/855)) ([8f61d75](https://github.com/Secure-T-Team/edu-frontend/commit/8f61d751c547825ed9103906b4f4550761f2d9c5))
- **TSK-864:** Added show all answers switch to create/edit modules ([#869](https://github.com/Secure-T-Team/edu-frontend/issues/869)) ([635693f](https://github.com/Secure-T-Team/edu-frontend/commit/635693ffc1ed16fd9bd1a60a56e60b31a27c143b))
- **TSK-866:** Added use-event and use-hotkeys hooks ([#849](https://github.com/Secure-T-Team/edu-frontend/issues/849)) ([a0d927e](https://github.com/Secure-T-Team/edu-frontend/commit/a0d927eb46e8c70c430410b69decdeed03993d3a))
- **TSK-892:** implement selected users count ([#874](https://github.com/Secure-T-Team/edu-frontend/issues/874)) ([4a61cdc](https://github.com/Secure-T-Team/edu-frontend/commit/4a61cdc52e663aa028c9dbdf44c3207f87c6f817))
- **TSK-904:** update config for editor ([#873](https://github.com/Secure-T-Team/edu-frontend/issues/873)) ([0bdcd35](https://github.com/Secure-T-Team/edu-frontend/commit/0bdcd352f3c3f7084548277b51acc99bea666946))
- **TSK-907:** add param to request ([#877](https://github.com/Secure-T-Team/edu-frontend/issues/877)) ([927acf4](https://github.com/Secure-T-Team/edu-frontend/commit/927acf4eb6a95dc777094d96241f7f42e851cdf2))
- update button color ([#853](https://github.com/Secure-T-Team/edu-frontend/issues/853)) ([f31f217](https://github.com/Secure-T-Team/edu-frontend/commit/f31f2179460210864c318cc58f0ece66b62c4e76))

### Bug Fixes

- fix build errrors ([7a0e524](https://github.com/Secure-T-Team/edu-frontend/commit/7a0e524ba5ab0724388c76d0ad4f65491c41c626))
- **TSK-308:** add filename to the reports page ([#863](https://github.com/Secure-T-Team/edu-frontend/issues/863)) ([7ed7913](https://github.com/Secure-T-Team/edu-frontend/commit/7ed79136da49268aedc63d178f85f507a1a0a496))
- **TSK-325:** Added date comparison to DatePicker ([#858](https://github.com/Secure-T-Team/edu-frontend/issues/858)) ([8d5f6a4](https://github.com/Secure-T-Team/edu-frontend/commit/8d5f6a4a260df5a43af490534aaf49e5bd6f7f70))
- **TSK-325:** Bugfix with delay date off ([#867](https://github.com/Secure-T-Team/edu-frontend/issues/867)) ([77ccd05](https://github.com/Secure-T-Team/edu-frontend/commit/77ccd053918877aecca33b9a87c897ae42822166))
- **TSK-336:** add not active course label ([#875](https://github.com/Secure-T-Team/edu-frontend/issues/875)) ([9af4503](https://github.com/Secure-T-Team/edu-frontend/commit/9af45032b7759e5b56f4f3aaf01a82b6260df713))
- **TSK-401:** Change phishing statistics date timezone ([#881](https://github.com/Secure-T-Team/edu-frontend/issues/881)) ([119fd29](https://github.com/Secure-T-Team/edu-frontend/commit/119fd2941e718464c694f0a480c80f681946572f))
- **TSK-549:** fix requests ([#864](https://github.com/Secure-T-Team/edu-frontend/issues/864)) ([0a5d464](https://github.com/Secure-T-Team/edu-frontend/commit/0a5d4647c5b5d710602faed99ada1e0728b777e6))
- **TSK-549:** fix scorm courses saving ([#866](https://github.com/Secure-T-Team/edu-frontend/issues/866)) ([57b005e](https://github.com/Secure-T-Team/edu-frontend/commit/57b005ec44f349af0bb35913833dd2efb95f31ea))
- **TSK-864:** Changed showAll answers logic ([#880](https://github.com/Secure-T-Team/edu-frontend/issues/880)) ([3403534](https://github.com/Secure-T-Team/edu-frontend/commit/3403534bc6b8fd1aad1c987fbbb12df0e13974f6))
- **TSK-864:** Fix create module modal overflow ([#882](https://github.com/Secure-T-Team/edu-frontend/issues/882)) ([627f67f](https://github.com/Secure-T-Team/edu-frontend/commit/627f67f76e8ee1e912b24ced74c1d69a4e347d68))
- **TSK-891:** don't check empty departments ([#865](https://github.com/Secure-T-Team/edu-frontend/issues/865)) ([6f4d522](https://github.com/Secure-T-Team/edu-frontend/commit/6f4d52228817f2ab1adec211be83f011e7648564))
- поменян placeholder текст на странице login ([0fecf4a](https://github.com/Secure-T-Team/edu-frontend/commit/0fecf4ad41914080705e1fdf77071dad4dddb7b1))

### [0.0.19](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.18...v0.0.19) (2024-02-26)

### [0.0.18](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.17...v0.0.18) (2024-02-22)

### Bug Fixes

- add hints to twofa ([bd6a7c3](https://github.com/Secure-T-Team/edu-frontend/commit/bd6a7c3087ea86cda9e9ae22a98e206639bd41f9))

### [0.0.17](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.16...v0.0.17) (2024-02-22)

### [0.0.16](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.15...v0.0.16) (2024-02-22)

### Features

- **TSK-496:** add sort ([#835](https://github.com/Secure-T-Team/edu-frontend/issues/835)) ([eeaf010](https://github.com/Secure-T-Team/edu-frontend/commit/eeaf0108e37bf1aa1e9b7a146b427b79a2c44e29))
- **TSK-794:** add delete confirm modal ([#836](https://github.com/Secure-T-Team/edu-frontend/issues/836)) ([43ab846](https://github.com/Secure-T-Team/edu-frontend/commit/43ab8468fb896c009553660b9369f4a7a9af6a2a))
- **TSK-806:** adding a arbitrary tag ([#838](https://github.com/Secure-T-Team/edu-frontend/issues/838)) ([0747d85](https://github.com/Secure-T-Team/edu-frontend/commit/0747d854e0cc1e85c79b7120d2b1e7aa6711b8eb))
- **TSK-806:** fix onboarding when assign a new user to the tag ([#848](https://github.com/Secure-T-Team/edu-frontend/issues/848)) ([3820b69](https://github.com/Secure-T-Team/edu-frontend/commit/3820b69d1979f8242032d45c0506589b8773fb3e))
- **TSK-806:** update with fixes ([#846](https://github.com/Secure-T-Team/edu-frontend/issues/846)) ([bc0c5a9](https://github.com/Secure-T-Team/edu-frontend/commit/bc0c5a9fdb588e076cbea346840326181018a9ea))

### Bug Fixes

- add params to twofa deactivate in user and organization ([c1d2beb](https://github.com/Secure-T-Team/edu-frontend/commit/c1d2beb370d73bb1709ee9c4b6de1d7885580e7b))
- change req to testing ([2569fd2](https://github.com/Secure-T-Team/edu-frontend/commit/2569fd219d943d28a2508e795b2f78f2d9585589))
- correct path to field ([8395bdc](https://github.com/Secure-T-Team/edu-frontend/commit/8395bdca84c8a2492b06f7239d1c78d44e8f8ddb))
- fix login-form when password isn't correct tho show ([1cb940a](https://github.com/Secure-T-Team/edu-frontend/commit/1cb940ab86c704fbaa4758c110072b77b208d39c))
- fix login-form when password isn't correct tho show ([8948d9e](https://github.com/Secure-T-Team/edu-frontend/commit/8948d9e5befa441ddac40da916fccd1de6f32ef5))
- password change token desctr ([27ed43a](https://github.com/Secure-T-Team/edu-frontend/commit/27ed43a4576e2c9037be664318e8849bc2dcc396))
- **TSK-629:** PostScormCourse request moved to RTK Query ([#847](https://github.com/Secure-T-Team/edu-frontend/issues/847)) ([12a6728](https://github.com/Secure-T-Team/edu-frontend/commit/12a6728207644ef24dcca1064dfadb89d182bb63))
- **TSK-672:** don't show not found when searching while fetching ([#840](https://github.com/Secure-T-Team/edu-frontend/issues/840)) ([ee47454](https://github.com/Secure-T-Team/edu-frontend/commit/ee47454fe2c9549d916d95d08c434ec9963b2652))
- **TSK-797:** Assign scorm course mozilla overflow bugfix ([#841](https://github.com/Secure-T-Team/edu-frontend/issues/841)) ([381c983](https://github.com/Secure-T-Team/edu-frontend/commit/381c9839b5ec2bfaf79f2635b768e9a2bca70271))
- **TSK-818:** phishing campaign card incident risk change ([#837](https://github.com/Secure-T-Team/edu-frontend/issues/837)) ([3849ab3](https://github.com/Secure-T-Team/edu-frontend/commit/3849ab3fd0d24749b5189c4ba4479452d4b07f39))
- **TSK-837:** clear api cache after logout ([#842](https://github.com/Secure-T-Team/edu-frontend/issues/842)) ([7799177](https://github.com/Secure-T-Team/edu-frontend/commit/7799177797d17981dc6555940001d09bd600bca8))
- **TSK-837:** Remove local storage org id item after logout ([#845](https://github.com/Secure-T-Team/edu-frontend/issues/845)) ([c41594f](https://github.com/Secure-T-Team/edu-frontend/commit/c41594f67805ab84a1a83a23dc4fd2631b6ae160))
- **TSK-863:** fix twofa lk profile switch add cursor pointer to ([2732ce2](https://github.com/Secure-T-Team/edu-frontend/commit/2732ce2cde19db5f0631a29a5b8bcd63eba9af31))
- **TSK-863:** fix twofa lk profile switch width to fit content ([4dc1e0b](https://github.com/Secure-T-Team/edu-frontend/commit/4dc1e0b1f399ea05cc5ebe02c5c4ea99f758c772))
- **TSK-868:** Added by_tag parameter to GetPhishingCampaignsTableDepartments request ([#844](https://github.com/Secure-T-Team/edu-frontend/issues/844)) ([e7013bb](https://github.com/Secure-T-Team/edu-frontend/commit/e7013bb600f8a0080b328dcc86133aa46c6deb2b))
- twofa settings clicable width ([9973e9f](https://github.com/Secure-T-Team/edu-frontend/commit/9973e9f4c54b919f8bee88b3c62339513a32fa25))

### [0.0.15](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.14...v0.0.15) (2024-02-13)

### Features

- add global auth steps handle ([9042512](https://github.com/Secure-T-Team/edu-frontend/commit/904251225d8346b2c0313ee8097ecfd5dc0c5b9c))

### [0.0.14](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.13...v0.0.14) (2024-02-12)

### Bug Fixes

- correct phishing tag message ([5895e7d](https://github.com/Secure-T-Team/edu-frontend/commit/5895e7d5d0363de54d92d584763fd856a5c50130))
- empty plug position ([8f9860e](https://github.com/Secure-T-Team/edu-frontend/commit/8f9860e9469f41e1a886260aebbe938f96561896))
- fix mail validation field ([623442f](https://github.com/Secure-T-Team/edu-frontend/commit/623442f31ded29874de94d9a4514498efb27017e))
- **TSK-847:** fix message in phishing create momment, ([b5e231d](https://github.com/Secure-T-Team/edu-frontend/commit/b5e231d066263b80edb0656ea82e336c27806586))
- **TSK-848:** fix to percent ([a48c532](https://github.com/Secure-T-Team/edu-frontend/commit/a48c53290de76622532a9a6e64d3f0924f2d4a8d))

### [0.0.13](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.12...v0.0.13) (2024-02-09)

### Features

- add param to users req ([b17767e](https://github.com/Secure-T-Team/edu-frontend/commit/b17767e32ad95e2632c231bd18aa4094e9b04e20))
- **TSK-831:** change request method ([0005d4a](https://github.com/Secure-T-Team/edu-frontend/commit/0005d4acd76eca98f05fb9724f72b891243647b3))
- **TSK-831:** connect status checker ([6f43999](https://github.com/Secure-T-Team/edu-frontend/commit/6f43999f0930c46194f4b4224116a8de38e2b55e))
- **TSK-831:** create ad sinc modal ([5faa436](https://github.com/Secure-T-Team/edu-frontend/commit/5faa436ba2b655611ace281761d9c2c7342a9e43))
- **TSK-831:** end ad sync modal ([7305302](https://github.com/Secure-T-Team/edu-frontend/commit/73053023d8125677ceef6e0fbc9e414c92ae1f1f))

### Bug Fixes

- add empty line ([f1b6764](https://github.com/Secure-T-Team/edu-frontend/commit/f1b6764cad3858e89119a7b06b3162cf8ad4065d))
- add ext ([845c420](https://github.com/Secure-T-Team/edu-frontend/commit/845c420b1fab50bfcb01f3b51c1e6e5e94366598))
- add separator ([0ed8722](https://github.com/Secure-T-Team/edu-frontend/commit/0ed87221f0973f0ee07cde5e6302c367fdf750b8))
- correct path ([2d6f749](https://github.com/Secure-T-Team/edu-frontend/commit/2d6f74943aeeeecf113ab8871d893ae7b2b99235))
- make normal login page ([7f443f3](https://github.com/Secure-T-Team/edu-frontend/commit/7f443f3005256faa23e9ae349aa4d65cfc94f5f2))
- try to delete sso login method to check behavior ([dea55f2](https://github.com/Secure-T-Team/edu-frontend/commit/dea55f2d79f609532be121aba182f7da80a3c03c))
- **TSK-831:** fix status checker ([9280385](https://github.com/Secure-T-Team/edu-frontend/commit/928038579aecfaa9f6d82d0c1775992fba14c0a2))
- **TSK-831:** fix status checker ([7ec09a8](https://github.com/Secure-T-Team/edu-frontend/commit/7ec09a85a0b64a8e5a6d2fb127d317aa388aa59b))
- **TSK-831:** fix status checker ([58f5f02](https://github.com/Secure-T-Team/edu-frontend/commit/58f5f02d07c087f4f8707cd9a503e434a11b1ec2))
- **TSK-831:** fix status checker ([a36c6f7](https://github.com/Secure-T-Team/edu-frontend/commit/a36c6f75c18615c0629033698057efee5b47c46c))
- **TSK-831:** fix status checker ([e8fa748](https://github.com/Secure-T-Team/edu-frontend/commit/e8fa74872fd3593a81c84d42238aaa48b735ea75))
- **TSK-831:** fix status checker ([9da9694](https://github.com/Secure-T-Team/edu-frontend/commit/9da9694e25047c14e23e5759d1181457b875c3ea))
- **TSK-831:** remove doubles usestate ([e7f2ac4](https://github.com/Secure-T-Team/edu-frontend/commit/e7f2ac46bfe0fef16e235cf17e57228f2ae95b98))
- unused var ([c6d0726](https://github.com/Secure-T-Team/edu-frontend/commit/c6d0726365750e4dfe6dc500ee0ef057ca6e44fa))
- url ([f9458ca](https://github.com/Secure-T-Team/edu-frontend/commit/f9458ca8a67f212bbfd53aef352c398101c63165))

### Refactoring

- **TSK-831:** refactor code ([0219b2a](https://github.com/Secure-T-Team/edu-frontend/commit/0219b2a4845fb6647fe9931fe97045cde7a7aacc))
- **TSK-831:** update default state value ([be11f50](https://github.com/Secure-T-Team/edu-frontend/commit/be11f50b44e42381674626154f197194066d03fa))

### [0.0.12](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.11...v0.0.12) (2024-02-05)

### Bug Fixes

- **TSK-721:** only the updated message should be sent ([#818](https://github.com/Secure-T-Team/edu-frontend/issues/818)) ([9592d70](https://github.com/Secure-T-Team/edu-frontend/commit/9592d70dac00d30e0d2513fef35915c7eba858d1))

### [0.0.11](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.10...v0.0.11) (2024-02-02)

### Features

- **TSK-389:** update texts ([#819](https://github.com/Secure-T-Team/edu-frontend/issues/819)) ([3d64c38](https://github.com/Secure-T-Team/edu-frontend/commit/3d64c38f29fe384177ff43d646892f41922707d3))

### [0.0.10](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.9...v0.0.10) (2024-02-01)

### [0.0.9](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.8...v0.0.9) (2024-01-31)

### Bug Fixes

- stretch button and delete char in statistics page ([4de3a72](https://github.com/Secure-T-Team/edu-frontend/commit/4de3a72e0e56ca22db12a9ea55da4417d2415663))
- **TSK-824:** fix superuser ([#816](https://github.com/Secure-T-Team/edu-frontend/issues/816)) ([9567052](https://github.com/Secure-T-Team/edu-frontend/commit/95670527268ad0c5c0b69d12ba3b0c4bfbd107af))

### [0.0.8](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.7...v0.0.8) (2024-01-30)

### [0.0.7](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.6...v0.0.7) (2024-01-30)

### [0.0.6](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.5...v0.0.6) (2024-01-29)

### Bug Fixes

- fix infinity scroll admin statistics ([1a3b523](https://github.com/Secure-T-Team/edu-frontend/commit/1a3b523738991735e2ad5e198420f33de9cac3ad))
- fix slide modals to flex column ([e1c1053](https://github.com/Secure-T-Team/edu-frontend/commit/e1c1053070b1c74ae5fbc4bfb5553ec516ad04cd))

### [0.0.5](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.4...v0.0.5) (2024-01-29)

### [0.0.4](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.3...v0.0.4) (2024-01-29)

### [0.0.3](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.2...v0.0.3) (2024-01-28)

### [0.0.2](https://github.com/Secure-T-Team/edu-frontend/compare/v0.0.1...v0.0.2) (2024-01-28)

### [0.0.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.51.0...v0.0.1) (2024-01-28)

### Features

- connect my courses page to new routing ([052b5fe](https://github.com/Secure-T-Team/edu-frontend/commit/052b5fe42992d116d91da026a92a4461248ef423))
- install base packeges and create store ([6a01dc6](https://github.com/Secure-T-Team/edu-frontend/commit/6a01dc69aa98dddb593a052ef79ee881a56c1ea5))
- moved favicons to new folder ([f3caf92](https://github.com/Secure-T-Team/edu-frontend/commit/f3caf927135adcd06517db82096e56d490b2176d))
- moved fonts to new folders ([a3dff0b](https://github.com/Secure-T-Team/edu-frontend/commit/a3dff0b6810b923683b6aea2515702fff64f7152))
- **react-new-delete-audio-button:** add delete audio btn to new react app ([#801](https://github.com/Secure-T-Team/edu-frontend/issues/801)) ([b4e7d99](https://github.com/Secure-T-Team/edu-frontend/commit/b4e7d9930034377813a4bedd5893ac65e85130e1))
- stash ([7906c9b](https://github.com/Secure-T-Team/edu-frontend/commit/7906c9bc922231e03b62cc106a700e91960d6e6f))
- **tsk 501:** add scorm progress to employee stats and сut data to 90-day ([#719](https://github.com/Secure-T-Team/edu-frontend/issues/719)) ([21b5a1b](https://github.com/Secure-T-Team/edu-frontend/commit/21b5a1b94100152f14908353c89d6d421ee88e65))
- **tsk 667:** add scorm statistic to graphs ([#742](https://github.com/Secure-T-Team/edu-frontend/issues/742)) ([9b01364](https://github.com/Secure-T-Team/edu-frontend/commit/9b013646b9e8f7c0741895afd40fabe6b77fb096))
- **TSK-300:** global error handler and notification improvements ([#758](https://github.com/Secure-T-Team/edu-frontend/issues/758)) ([a845e1f](https://github.com/Secure-T-Team/edu-frontend/commit/a845e1fd71210b037bbbae50bb88e8f0a6667755))
- **TSK-300:** updated condition for calling error notitifcation ([2f73c13](https://github.com/Secure-T-Team/edu-frontend/commit/2f73c13552dba81b6d80c3d72cacc922e195ec33))
- **TSK-361:** added error if no modules are selected ([eff18c3](https://github.com/Secure-T-Team/edu-frontend/commit/eff18c371cc00b040a3fd4bd2565e72d682a9729))
- **TSK-361:** added error if no modules are selected ([0c8326f](https://github.com/Secure-T-Team/edu-frontend/commit/0c8326fd6f68a3d2a5cc90cc9b94e20511c2ca52))
- **TSK-465:** added ru language to calendar ([a81c231](https://github.com/Secure-T-Team/edu-frontend/commit/a81c2318b75b4677faf5a920ff64f9ba9d37ccb9))
- **TSK-488:** added lower case for employee search ([d47aa9c](https://github.com/Secure-T-Team/edu-frontend/commit/d47aa9c7053ecb9954b1a65607d75dad64828cc8))
- **TSK-489:** add select all button for employees page ([#724](https://github.com/Secure-T-Team/edu-frontend/issues/724)) ([1d06cb8](https://github.com/Secure-T-Team/edu-frontend/commit/1d06cb88fb0f001a7ef39ce136a94a0a5578411c))
- **TSK-489:** updated display conditions for selecting all ([283af56](https://github.com/Secure-T-Team/edu-frontend/commit/283af56b9958ec9848ad556dbe5a708dccde6a6a))
- **TSK-498:** added padding for the panel and changed colors ([a53d0a1](https://github.com/Secure-T-Team/edu-frontend/commit/a53d0a1d81d110fc38b668c5dabbaaa3bf87cf5b))
- **TSK-608:** added badge for new tobs and connected it to phishing campaigns page ([4ffeaef](https://github.com/Secure-T-Team/edu-frontend/commit/4ffeaef53bd6556d2ba87c3ecf1bd6b99749f4bc))
- **TSK-615:** added tags and delete extra code ([34e3496](https://github.com/Secure-T-Team/edu-frontend/commit/34e34964e9c4c225891d5589419811d80c020f4a))
- **TSK-618:** add new format icon ([851a030](https://github.com/Secure-T-Team/edu-frontend/commit/851a030e52200559e18ff970b8f06e1e70bac9b8))
- **TSK-620:** add error message for sender fields ([abe74b1](https://github.com/Secure-T-Team/edu-frontend/commit/abe74b1a3d7c433d8a3cb555e08f1c606d7aa715))
- **TSK-621:** add closing modal ([a48e342](https://github.com/Secure-T-Team/edu-frontend/commit/a48e342d8e63ccf81327c2a13ab518b3866f7c67))
- **TSK-636:** connected reinvite letter on the employees page ([#722](https://github.com/Secure-T-Team/edu-frontend/issues/722)) ([a8a1d90](https://github.com/Secure-T-Team/edu-frontend/commit/a8a1d90129a03647054c52f81e1ba5ac4ef9091c))
- **TSK-636:** connected reinvite to the select all button ([#725](https://github.com/Secure-T-Team/edu-frontend/issues/725)) ([155e439](https://github.com/Secure-T-Team/edu-frontend/commit/155e43926b44b0cdcdb106e53855b0b0f3248d13))
- **TSK-638:** added hints for turn off tag ([20ec166](https://github.com/Secure-T-Team/edu-frontend/commit/20ec1665ab5286cef25d224eabb4414fb83cb6b2))
- **TSK-652:** added tags for adfs service ([11e01ce](https://github.com/Secure-T-Team/edu-frontend/commit/11e01ce88dd50e16b14856cf7de7d44a02531da3))
- **TSK-653:** added disabled for completed courses ([b791b68](https://github.com/Secure-T-Team/edu-frontend/commit/b791b68ec32b5beb68811c45ab03c77bdb2dacf8))
- **TSK-656:** added hover for regular action buttons ([1af2768](https://github.com/Secure-T-Team/edu-frontend/commit/1af2768b6cd28c04ad2a330ceb12fdfa052ba496))
- **TSK-656:** update employees panel ([1753ccd](https://github.com/Secure-T-Team/edu-frontend/commit/1753ccdaf4911b95a1540eb213debacf6769ca9d))
- **TSK-660:** added icons for auto phish and testing phishing campaigns ([88dda57](https://github.com/Secure-T-Team/edu-frontend/commit/88dda57ca2a3e845cddeff1853cd2b804edde000))
- **TSK-663:** connected bulk delete to select all button ([#726](https://github.com/Secure-T-Team/edu-frontend/issues/726)) ([c998317](https://github.com/Secure-T-Team/edu-frontend/commit/c998317adfaf2c53aee181103d565d3728f6623e))
- **TSK-666:** add router confing for future ([61ea3ba](https://github.com/Secure-T-Team/edu-frontend/commit/61ea3bac4b422f2f113a4062607a1ab6d0fb06d4))
- **TSK-667:** add scorm graph calculate to sub-organizations ([#748](https://github.com/Secure-T-Team/edu-frontend/issues/748)) ([b479a36](https://github.com/Secure-T-Team/edu-frontend/commit/b479a366c0ea129413cf8bd93b347a1f77b0fb50))
- **TSK-670:** added export statistics for scorm courses ([9f9e771](https://github.com/Secure-T-Team/edu-frontend/commit/9f9e7713865bfd0f5750b3b68319b5fb41bb78f2))
- **TSK-671:** added employee scorm courses to courses list ([bf4ac3d](https://github.com/Secure-T-Team/edu-frontend/commit/bf4ac3d4e55ae50bcd78d96a884286c0afc1e807))
- **TSK-686:** added auto-selection first filtred tab on settings page ([a3b978f](https://github.com/Secure-T-Team/edu-frontend/commit/a3b978f468d27d5ff74211ef331e62cbf15c53fb))
- **TSK-701:** action log tabs ([5b9c953](https://github.com/Secure-T-Team/edu-frontend/commit/5b9c9533d4ca88afd68bcd3b871a0e686731a9a3))
- **TSK-701:** add admin learning base pages(assigned, tags, assigned-course) ([b35cd1d](https://github.com/Secure-T-Team/edu-frontend/commit/b35cd1dcadf607c57b057438dad54825bf5d65b9))
- **TSK-701:** add admin learning pages ([6c7da37](https://github.com/Secure-T-Team/edu-frontend/commit/6c7da3761be58be02b555526759f7b0ebf70ba95))
- **TSK-701:** add all base pages ([7f828fa](https://github.com/Secure-T-Team/edu-frontend/commit/7f828faed2811a4e0ab333daa96f433d25dc473b))
- **TSK-701:** add all reports to page ([395c134](https://github.com/Secure-T-Team/edu-frontend/commit/395c1343fae80f514b96e1b33804bbad2134837b))
- **TSK-701:** add animation to expanded and collapsed lists ([521feea](https://github.com/Secure-T-Team/edu-frontend/commit/521feeab04a18908a9627feaed3ed5771c1441a6))
- **TSK-701:** add any ([2603dcc](https://github.com/Secure-T-Team/edu-frontend/commit/2603dcc0a6c71561f798306f661e21071d73658b))
- **TSK-701:** add app interface checker ([534237c](https://github.com/Secure-T-Team/edu-frontend/commit/534237c87fa84dc04035e79abba2fc081fc3b3bd))
- **TSK-701:** add back buttons routes ([cc83dba](https://github.com/Secure-T-Team/edu-frontend/commit/cc83dbad82b3cef9e037f42bc0ad13748b5fa7fe))
- **TSK-701:** add branding logs ([d82db3c](https://github.com/Secure-T-Team/edu-frontend/commit/d82db3c815fedcf2be7a5326e47b90762d11dbea))
- **TSK-701:** add branding toggle ([8eb6cd6](https://github.com/Secure-T-Team/edu-frontend/commit/8eb6cd60d9a0fadb01c83c0f43e74a288ccd1d9a))
- **TSK-701:** add calendar min date ([e6d72d3](https://github.com/Secure-T-Team/edu-frontend/commit/e6d72d3ca9f65e7f783ce4bedb3bee2fd57d30a9))
- **TSK-701:** add checking about render ([68420c7](https://github.com/Secure-T-Team/edu-frontend/commit/68420c788a9aadd912e44111b2e01bc2a17c643c))
- **TSK-701:** add condition to helper ([e3cd48d](https://github.com/Secure-T-Team/edu-frontend/commit/e3cd48d2015188f1d0f46a74da16f705bc0317fc))
- **TSK-701:** add conditional creating root ([5ac719b](https://github.com/Secure-T-Team/edu-frontend/commit/5ac719b2be2c1b0a219823ce046c387f7a44b11e))
- **TSK-701:** add config to helper ([8076ad3](https://github.com/Secure-T-Team/edu-frontend/commit/8076ad3ee301e77d0df5e120ebbd611bd8d808d4))
- **TSK-701:** add course_id var to link ([abf70c9](https://github.com/Secure-T-Team/edu-frontend/commit/abf70c9047e2bf1cdaad5d28cb77d3948d76ca02))
- **TSK-701:** add default tab to phishing by tags ([b48786c](https://github.com/Secure-T-Team/edu-frontend/commit/b48786c1e73883e379518a98336e0b5a31c5d6be))
- **TSK-701:** add edit course togglers ([fbbf92b](https://github.com/Secure-T-Team/edu-frontend/commit/fbbf92b167fb19c02be9141bbce5beb82050fba7))
- **TSK-701:** add editor config ru lang and delete test phishing action in tags ([85835cd](https://github.com/Secure-T-Team/edu-frontend/commit/85835cd166e7813b64922ab283deb3f310b98a85))
- **TSK-701:** add email template editor ([3a221c4](https://github.com/Secure-T-Team/edu-frontend/commit/3a221c48ede914f19d68d31dff8447581abda331))
- **TSK-701:** add employees tag page step 2 sps ([1d59750](https://github.com/Secure-T-Team/edu-frontend/commit/1d59750aa8b6f73815049270c437643cf9e48b54))
- **TSK-701:** add error-boundary to app ([678ce0d](https://github.com/Secure-T-Team/edu-frontend/commit/678ce0d6e41f2d9acf6bf3bc81240419e75f93a4))
- **TSK-701:** add features to toggle permissions between auth and unauth ([b3f351a](https://github.com/Secure-T-Team/edu-frontend/commit/b3f351a5f467193041952d21bdde521261cd067e))
- **TSK-701:** add few pages ([3aca7ca](https://github.com/Secure-T-Team/edu-frontend/commit/3aca7ca582b41a765d4c3b1e55216bd243794fc6))
- **TSK-701:** add fix modals styles and fix server branding host ([bc652ea](https://github.com/Secure-T-Team/edu-frontend/commit/bc652ea6b1ed51e747e0969b125c9c6c1e27a564))
- **TSK-701:** add forgot password link ([305e1bd](https://github.com/Secure-T-Team/edu-frontend/commit/305e1bd9035a534d32e5d6819e1d744baaccde2c))
- **TSK-701:** add global editor scroll y auto ([8e49b96](https://github.com/Secure-T-Team/edu-frontend/commit/8e49b9642accd4c88eb520102f487300756b3ad0))
- **TSK-701:** add global window render checking ([dd1e799](https://github.com/Secure-T-Team/edu-frontend/commit/dd1e7997e823ed04a98426d209b963e67d491b81))
- **TSK-701:** add gotcha page and fix bugs ([e400cdd](https://github.com/Secure-T-Team/edu-frontend/commit/e400cdd622261dd8deef6f0e0791b7e2fba0a67f))
- **TSK-701:** add help text to check editor vars ([a518db9](https://github.com/Secure-T-Team/edu-frontend/commit/a518db960912f8accdbdb577cea2182b847e0e99))
- **TSK-701:** add hostURL server logs ([a2c30ad](https://github.com/Secure-T-Team/edu-frontend/commit/a2c30adba61b8278ed3dfb0fe3b249c8f73f69ba))
- **TSK-701:** add https to links ([f1de482](https://github.com/Secure-T-Team/edu-frontend/commit/f1de482dfd05c1ec4c162dc3b4a0348b5247f463))
- **TSK-701:** add isolation styles and scripts in editor ([3834d2d](https://github.com/Secure-T-Team/edu-frontend/commit/3834d2dd4ba6389fbf6f3e87e2a28141cf3ad8e8))
- **TSK-701:** add loaders to protected route and marked query trick ([ed6bcd5](https://github.com/Secure-T-Team/edu-frontend/commit/ed6bcd5645c33c031b9fa6370b64631da8ca6d66))
- **TSK-701:** add logic to sidebar and role switch user, ([555ee6d](https://github.com/Secure-T-Team/edu-frontend/commit/555ee6d7b6665832ebe6168ef51f0968df5bc4f7))
- **TSK-701:** add login pages and make navigation between auth and ([c859e8e](https://github.com/Secure-T-Team/edu-frontend/commit/c859e8e8a26bf84d4063dfd63f61eb4caba8c164))
- **TSK-701:** add logs to debug ([077cb70](https://github.com/Secure-T-Team/edu-frontend/commit/077cb70068579dcb1257b0182029098bdc2d1e80))
- **TSK-701:** add loki, hygen, storybook files ([d799151](https://github.com/Secure-T-Team/edu-frontend/commit/d7991511f8e5db4c6f097d382111645dcec7ef34))
- **TSK-701:** add navigate to Breadcrumbs and add 2 phishing pages to router ([91e15f9](https://github.com/Secure-T-Team/edu-frontend/commit/91e15f900d75b5f46801460e4f71e806566b9f67))
- **TSK-701:** add nesseccary favicon and title ([e210f7c](https://github.com/Secure-T-Team/edu-frontend/commit/e210f7c38c5c5e4b2af1a24d730dd0efe69286bc))
- **TSK-701:** add organizations pages to sidebar ([7a20a92](https://github.com/Secure-T-Team/edu-frontend/commit/7a20a927a7e395d5a65fb046cc5a481977964891))
- **TSK-701:** add package-jsop to building in dockerfile ([504b4b2](https://github.com/Secure-T-Team/edu-frontend/commit/504b4b2e512421ba9e8c6c482b82ff13bcc5eebf))
- **TSK-701:** add pages to routes ([2682825](https://github.com/Secure-T-Team/edu-frontend/commit/26828251aa4e5539ed945524ea600631f57eeae6))
- **TSK-701:** add param for invalid permission in 5 minutes ([d7e0ff0](https://github.com/Secure-T-Team/edu-frontend/commit/d7e0ff0c5ea0f04b4d1d9241a0ec9c9436dfa134))
- **TSK-701:** add pen to the create slide modal ([dd58065](https://github.com/Secure-T-Team/edu-frontend/commit/dd580658c35967dba6a23347881b476a6eb99ad2))
- **TSK-701:** add permission refetch interval ([4e0fb6a](https://github.com/Secure-T-Team/edu-frontend/commit/4e0fb6a46069229d270d53c49b439efbdba8c9cb))
- **TSK-701:** add permissions, redirect to default page ([f25e4ec](https://github.com/Secure-T-Team/edu-frontend/commit/f25e4ec0f62826033283006d8986d3e604a139e6))
- **TSK-701:** add phishing create page and ([2424637](https://github.com/Secure-T-Team/edu-frontend/commit/2424637b5f2e8b2baa9582317a32bc8bcb0faf45))
- **TSK-701:** add phishing pages ([9341b06](https://github.com/Secure-T-Team/edu-frontend/commit/9341b0641ede0e9a2eacae1ce31f09ac0aa99e6a))
- **TSK-701:** add phishing pages and organization page ([59b8934](https://github.com/Secure-T-Team/edu-frontend/commit/59b89342678a0e9bf4a3e01ec17de45696f1c6cd))
- **TSK-701:** add phishing pages to routes ([973f987](https://github.com/Secure-T-Team/edu-frontend/commit/973f987f0fa9666f44a6c08b875b3e5d920069a4))
- **TSK-701:** add plain ad sync modal ([a833cb2](https://github.com/Secure-T-Team/edu-frontend/commit/a833cb26cab0fe74692e245a7a87e0215bcd1631))
- **TSK-701:** add quiz page to routing, create require auth and auth slice ([90c1389](https://github.com/Secure-T-Team/edu-frontend/commit/90c1389a0158817df99a9b296a51b716812b487e))
- **TSK-701:** add react 16.8 in peerDeps ([2b4ace6](https://github.com/Secure-T-Team/edu-frontend/commit/2b4ace6038b261870f96d9fbfe2c045b5e1ab3e8))
- **TSK-701:** add react tranlsation to all pages ([9aeab1d](https://github.com/Secure-T-Team/edu-frontend/commit/9aeab1d23e3178a0a37bbe50987e7f604fa7a004))
- **TSK-701:** add recovery token ([efdc7bb](https://github.com/Secure-T-Team/edu-frontend/commit/efdc7bb5ccec5022c5a749bc50892b6555a2ca0d))
- **TSK-701:** add root wrappers ([d4b4072](https://github.com/Secure-T-Team/edu-frontend/commit/d4b407294ca2e29b25277ea527d6ee879a4cbf21))
- **TSK-701:** add routing to default /lk/user/learning page ([81b72ed](https://github.com/Secure-T-Team/edu-frontend/commit/81b72ed0cf9f98a2a48bccfc8f3bfa0af8d62d2c))
- **TSK-701:** add rtk query error logger ([5f0c7e1](https://github.com/Secure-T-Team/edu-frontend/commit/5f0c7e17d7e3e264ed5f34e866b73f4d244b5a06))
- **TSK-701:** add search by description in modules ([278a126](https://github.com/Secure-T-Team/edu-frontend/commit/278a126fbd270ba11550a0fb94cca2c5d7a1bf5d))
- **TSK-701:** add sidebar ([6a9d9e4](https://github.com/Secure-T-Team/edu-frontend/commit/6a9d9e4bb00b6225a378324f5f550598a839119c))
- **TSK-701:** add sidebar arrows and auth trick ([fbaa4d7](https://github.com/Secure-T-Team/edu-frontend/commit/fbaa4d7e0f7bc2e89f8151a48bfa0181709d1289))
- **TSK-701:** add slide styles ([f4e5f6e](https://github.com/Secure-T-Team/edu-frontend/commit/f4e5f6e79d5b29605d71c806d5d792e00678eb6b))
- **TSK-701:** add some bug fixes ([d6acf16](https://github.com/Secure-T-Team/edu-frontend/commit/d6acf1642012921fc03eef084bdfaa6810985bbb))
- **TSK-701:** add state to gotcha ([45c5fff](https://github.com/Secure-T-Team/edu-frontend/commit/45c5fffb1630df3a7a286d058819f1af55ba19fe))
- **TSK-701:** add static i18n ([20a76f3](https://github.com/Secure-T-Team/edu-frontend/commit/20a76f3c7fbdd3bcd78b5bca13245f14aaad5ac4))
- **TSK-701:** add tag to query params each time when change pagination ([6463c7f](https://github.com/Secure-T-Team/edu-frontend/commit/6463c7f2ba5d11e3b63ce5698f12b02e55185f4b))
- **TSK-701:** add templates and organization pages ([2f64f0e](https://github.com/Secure-T-Team/edu-frontend/commit/2f64f0e4c43377f826544f2073a04008c66f68bd))
- **TSK-701:** add templates, change module type in config ([d4e0048](https://github.com/Secure-T-Team/edu-frontend/commit/d4e00489e965f77d6d7fbd39edb465703b14c11a))
- **TSK-701:** add togglers to forms ([d10ecf0](https://github.com/Secure-T-Team/edu-frontend/commit/d10ecf09d6b28c55908eb7d89ef11580b90cd251))
- **TSK-701:** add useZone to sidebar links ([1db640d](https://github.com/Secure-T-Team/edu-frontend/commit/1db640d23d751917bfca1deb88ece09e48692291))
- **TSK-701:** add width to module input ([95708c1](https://github.com/Secure-T-Team/edu-frontend/commit/95708c1c6fa706b1f65682cbd82f663c0597cf2b))
- **TSK-701:** admin employee-statistics back button ([b133fbb](https://github.com/Secure-T-Team/edu-frontend/commit/b133fbbb3260791a8af6fa6d3bc186927b4c5f3d))
- **TSK-701:** assigned scrom back button route ([75b8fdb](https://github.com/Secure-T-Team/edu-frontend/commit/75b8fdb3464f2d65edf1b54779e12147f255416c))
- **TSK-701:** change api host drone for dev ([a008d82](https://github.com/Secure-T-Team/edu-frontend/commit/a008d822ccf946b7669553d4abe5a8986364e3cc))
- **TSK-701:** change back button routes ([ca99b2a](https://github.com/Secure-T-Team/edu-frontend/commit/ca99b2a1babc1be478527021eea8e7b2eb37ed91))
- **TSK-701:** change breadcrumbs path ([da3d816](https://github.com/Secure-T-Team/edu-frontend/commit/da3d8169a77542508cf532aa83a9edbb4508e084))
- **TSK-701:** change brone branch name again ([8422971](https://github.com/Secure-T-Team/edu-frontend/commit/84229718928a307ff2dd331695e5561c6229f423))
- **TSK-701:** change content checking ([120f496](https://github.com/Secure-T-Team/edu-frontend/commit/120f4961976780a651f4d2b70f6768d1a87d4bf4))
- **TSK-701:** change dev api host ([6a979d8](https://github.com/Secure-T-Team/edu-frontend/commit/6a979d84b4c755cfea3d9dff7bac9b990edd3ede))
- **TSK-701:** change dev api host again( ([bedf4c2](https://github.com/Secure-T-Team/edu-frontend/commit/bedf4c2253289dd934be06cfde318c390069098a))
- **TSK-701:** change drone and server ([bc63b7e](https://github.com/Secure-T-Team/edu-frontend/commit/bc63b7e291952153835a5f2a48d6988ce7e6145d))
- **TSK-701:** change drone commit branch to feat-tsk-701 ([980fbc4](https://github.com/Secure-T-Team/edu-frontend/commit/980fbc49ca7c2f69d51c1e8902f81208ca455c90))
- **TSK-701:** change mui dialogs to modal component ([305c978](https://github.com/Secure-T-Team/edu-frontend/commit/305c978ef75920029d0839a15a40b4121954b874))
- **TSK-701:** change package json to new config extension, ([a3691da](https://github.com/Secure-T-Team/edu-frontend/commit/a3691dad9c92646aba7eb1d2ffb051d158c3d5a8))
- **TSK-701:** change phishing tabs behavior ([e13c3e3](https://github.com/Secure-T-Team/edu-frontend/commit/e13c3e3a9434d293cefac2a05ebccc60e1140f1b))
- **TSK-701:** change publicUrl ([bea4f8e](https://github.com/Secure-T-Team/edu-frontend/commit/bea4f8e27c7ebd809c06fe89fc926ace0258eed7))
- **TSK-701:** change regExp ([585efbd](https://github.com/Secure-T-Team/edu-frontend/commit/585efbd2d18a4a3d2b36564dacd55166f4bf92d1))
- **TSK-701:** change regExp ([887a43a](https://github.com/Secure-T-Team/edu-frontend/commit/887a43a10ffbf0e12ffc8391c93223b124d12725))
- **TSK-701:** change server and add logs ([167025e](https://github.com/Secure-T-Team/edu-frontend/commit/167025e8bb703dfea432948de0327386dd403d1a))
- **TSK-701:** change tab select phishing by tag ([6ca8ead](https://github.com/Secure-T-Team/edu-frontend/commit/6ca8ead1e3831f68f31413ac282b704cfeee6236))
- **TSK-701:** check title ([03474be](https://github.com/Secure-T-Team/edu-frontend/commit/03474be6c50c7b00cabbdef7000de8a439073498))
- **TSK-701:** check url ([4f439f2](https://github.com/Secure-T-Team/edu-frontend/commit/4f439f2a33844c79c074fbe64be9008e205eac29))
- **TSK-701:** check url ([9dfdac0](https://github.com/Secure-T-Team/edu-frontend/commit/9dfdac0336d49347a68c4f63609ac031a5daaa42))
- **TSK-701:** comment unused vars ([919ac2c](https://github.com/Secure-T-Team/edu-frontend/commit/919ac2c8df30a43e5f2df6257776887012ed8bd0))
- **TSK-701:** commit husky ([9328666](https://github.com/Secure-T-Team/edu-frontend/commit/93286662a31c02cf19b775a968e22a79682f6d1c))
- **TSK-701:** correcting register token ([35a1126](https://github.com/Secure-T-Team/edu-frontend/commit/35a112677e55e0c40571d92c41fb3391443958e3))
- **TSK-701:** create components for sidebar ([6d07e6c](https://github.com/Secure-T-Team/edu-frontend/commit/6d07e6c8f4eef13bed8676981a8db5a6b078a5a6))
- **TSK-701:** create course modal ([5788969](https://github.com/Secure-T-Team/edu-frontend/commit/578896946a7f62e6e3c76ab061996418cd959666))
- **TSK-701:** create loading plug ([f15d78f](https://github.com/Secure-T-Team/edu-frontend/commit/f15d78fb91ac17f1b0431839e1310498639c2cf2))
- **TSK-701:** create new vite config ([4bc0f96](https://github.com/Secure-T-Team/edu-frontend/commit/4bc0f9642bb594215fb8e424f88270d20772928e))
- **TSK-701:** create plain build with vite, ([29d4fc8](https://github.com/Secure-T-Team/edu-frontend/commit/29d4fc843b33eb1af23c510304aa51119e5ec4ac))
- **TSK-701:** delete default port in server ([02c6992](https://github.com/Secure-T-Team/edu-frontend/commit/02c6992f76c4f0704eb5c7eb0b02be9767fb46da))
- **TSK-701:** delete handlebars index-config return template ([2918fc6](https://github.com/Secure-T-Team/edu-frontend/commit/2918fc627c6ed1da6bc5f83581c0ca5ed7e144a5))
- **TSK-701:** delete icon padding, delete logs, add incident-card colors ([059ddde](https://github.com/Secure-T-Team/edu-frontend/commit/059ddde850d67617e096b64e9fd6db502a4425a2))
- **TSK-701:** delete interface checker ([fa82144](https://github.com/Secure-T-Team/edu-frontend/commit/fa82144305de4aeabf5ca4dc6a2d6fb47214616a))
- **TSK-701:** delete log link ([9a9bf67](https://github.com/Secure-T-Team/edu-frontend/commit/9a9bf6737439bcf535b8a220c077b9c407758b63))
- **TSK-701:** delete logs and change helper ([44392a3](https://github.com/Secure-T-Team/edu-frontend/commit/44392a32bc415c1234c7679b61d1c2a570cc15db))
- **TSK-701:** delete old files, fix error handling when tag isn't provide ([b27c489](https://github.com/Secure-T-Team/edu-frontend/commit/b27c489844f0f0bc5ba783d9b34425925db95ae7))
- **TSK-701:** delete server logs, fix some bugs, add circle checkbox type ([8f77cca](https://github.com/Secure-T-Team/edu-frontend/commit/8f77ccad9e024dba17eb4ca5efd01e73e20e0456))
- **TSK-701:** delete strict mode ([1bca569](https://github.com/Secure-T-Team/edu-frontend/commit/1bca56984d2c0b9365e3710a9fee0a2ba43a939e))
- **TSK-701:** delete unused var ([2398139](https://github.com/Secure-T-Team/edu-frontend/commit/2398139450df0e2b06abbd4c6aa01efcaefa5c4f))
- **TSK-701:** extends tabs component rerender type and ([02a2aae](https://github.com/Secure-T-Team/edu-frontend/commit/02a2aae50b521030bf2e039f096b146f97cc1f9b))
- **TSK-701:** fix breadcrumbs item in create campaign page ([7871cf3](https://github.com/Secure-T-Team/edu-frontend/commit/7871cf36ddbdb0034fc5ecf98d1daf4829df5167))
- **TSK-701:** fix bugs ([0e56c57](https://github.com/Secure-T-Team/edu-frontend/commit/0e56c57118651e6fb62ed02b57a8b90f0acdc8d9))
- **TSK-701:** fix bugs ([b7f3f7a](https://github.com/Secure-T-Team/edu-frontend/commit/b7f3f7ad53fee134d3f5351184c5b7a738697129))
- **TSK-701:** fix campaign plugs ([de49900](https://github.com/Secure-T-Team/edu-frontend/commit/de499005bc7c82b6cb1df902a2e7524394fa4c6e))
- **TSK-701:** fix campaigns bugs ([9951b23](https://github.com/Secure-T-Team/edu-frontend/commit/9951b23ab2c05983bea562b9281683aebec5595f))
- **TSK-701:** fix create department req ([76e4d0f](https://github.com/Secure-T-Team/edu-frontend/commit/76e4d0fd7087a0c6247f23dd387921030fe9b110))
- **TSK-701:** fix dowload link ([b7b705a](https://github.com/Secure-T-Team/edu-frontend/commit/b7b705ac1b9f6773a0f2c20aece2fef1c06ee7de))
- **TSK-701:** fix event floating date picker ([f3fd146](https://github.com/Secure-T-Team/edu-frontend/commit/f3fd146d6e1983853138b930f6d246b5d172c07b))
- **TSK-701:** fix export excel in phishing campaigns statistics page ([5bef864](https://github.com/Secure-T-Team/edu-frontend/commit/5bef864f298e8e4bfe93eeb67d03ae755051e4bb))
- **TSK-701:** fix fullscreen button ([59c8a36](https://github.com/Secure-T-Team/edu-frontend/commit/59c8a36fc2d6bc4602a3db90f1003dfb08893165))
- **TSK-701:** fix local storage error, add refetch after delete ([686ab4a](https://github.com/Secure-T-Team/edu-frontend/commit/686ab4ab01d156e32be48dd72ff39576798af898))
- **TSK-701:** fix modals and add keeper url ([b0d1df3](https://github.com/Secure-T-Team/edu-frontend/commit/b0d1df33b5e2e3fa7d02ec57812dd565ac6cc79b))
- **TSK-701:** fix phishing by tags pages breadcrumbs name ([4cdc42d](https://github.com/Secure-T-Team/edu-frontend/commit/4cdc42db77bd998a789ff7beb2de27a9d80936d7))
- **TSK-701:** fix sidebar when list is active but not blink ([5b244b6](https://github.com/Secure-T-Team/edu-frontend/commit/5b244b678933bd591124f30fdbfb3e20a8526d2b))
- **TSK-701:** fix slides arrow, fix backbutton navigations ([6076a9a](https://github.com/Secure-T-Team/edu-frontend/commit/6076a9ae584818b2beabce6718f86aa2023cd22f))
- **TSK-701:** fix slides height ([ea03cca](https://github.com/Secure-T-Team/edu-frontend/commit/ea03ccaaf0e1487108e76ea797da7f2e4f5712a8))
- **TSK-701:** fix styles in statistic page and remake newrequest ([3099af4](https://github.com/Secure-T-Team/edu-frontend/commit/3099af4ed2f86ce58456843a9a4d56fb0992a206))
- **TSK-701:** fix tags type ([9cf880a](https://github.com/Secure-T-Team/edu-frontend/commit/9cf880acfa4b1e4da92f3cfbf02b66f36abe6bb4))
- **TSK-701:** fix text areas width and fix requests ([d54701d](https://github.com/Secure-T-Team/edu-frontend/commit/d54701dfbfb870faaaaaee7b455606835e88550c))
- **TSK-701:** fix the trick in useEmployees ([f1f7675](https://github.com/Secure-T-Team/edu-frontend/commit/f1f767561f297054a581b5d9cc6d47e1ab887f45))
- **TSK-701:** fix ts erros and add commitlint ([eb151e1](https://github.com/Secure-T-Team/edu-frontend/commit/eb151e19007aec626f61a6310412e0f68252a1e6))
- **TSK-701:** fix variant when change template again ([60e2cd2](https://github.com/Secure-T-Team/edu-frontend/commit/60e2cd2f80499c0f9f0c971a09bb267d900cd226))
- **TSK-701:** git add slider styles ([a8d950c](https://github.com/Secure-T-Team/edu-frontend/commit/a8d950cd85d9792affa8b557cde986e78a0af1ad))
- **TSK-701:** git add template editor change html value ([f1a119a](https://github.com/Secure-T-Team/edu-frontend/commit/f1a119a2fe5c646979de548b8028a2ce9352ab58))
- **TSK-701:** heh( ([ad2f46f](https://github.com/Secure-T-Team/edu-frontend/commit/ad2f46fe71ab2d34add91a003c454c10f8ed836d))
- **TSK-701:** log link ([1f017f9](https://github.com/Secure-T-Team/edu-frontend/commit/1f017f93da5d7ecdde27cb2fa4c26195aa641555))
- **TSK-701:** log link ([1b70875](https://github.com/Secure-T-Team/edu-frontend/commit/1b70875e4d1e22521112ff45fb6a33a0f6614c41))
- **TSK-701:** override scorm description height ([dd50750](https://github.com/Secure-T-Team/edu-frontend/commit/dd50750fb33cd98a2d465c9871a6d891fc1e5ed7))
- **TSK-701:** phishing by tags add,patch,delete ([3abb796](https://github.com/Secure-T-Team/edu-frontend/commit/3abb7960741484fa62579625f67a092821cba7a3))
- **TSK-701:** phishing by tags page tab ([bd5fd8d](https://github.com/Secure-T-Team/edu-frontend/commit/bd5fd8d7675c0f12b23ddafd38b4228882a3e275))
- **TSK-701:** prepare all urls for pages in config ([0df9f6c](https://github.com/Secure-T-Team/edu-frontend/commit/0df9f6c2be335392448e2788bcf5dc445ec88f61))
- **TSK-701:** prepare some general styles ([43b4b9c](https://github.com/Secure-T-Team/edu-frontend/commit/43b4b9cdcd4bd5ea52df0204b8289965dee3ce0f))
- **TSK-701:** refactor all admin pages and add page wrapper to react ([079d839](https://github.com/Secure-T-Team/edu-frontend/commit/079d839c0b7f1ea64c413b8d327e184dd2a44dbf))
- **TSK-701:** refactor the learning pages routers ([8b0a6ba](https://github.com/Secure-T-Team/edu-frontend/commit/8b0a6ba3ee83a14673686b6521e18e3996941e2f))
- **TSK-701:** remake types to repair build ([6d14da3](https://github.com/Secure-T-Team/edu-frontend/commit/6d14da3c5f4bdb382641cb4666400b8d721fbc61))
- **TSK-701:** remake useConfig, useAnalytics, useSSO hooks, ([9147e22](https://github.com/Secure-T-Team/edu-frontend/commit/9147e223b2274599c3d3c6940a7087736a0c2e4b))
- **TSK-701:** remove end date in phishing by tag ([b8eeae4](https://github.com/Secure-T-Team/edu-frontend/commit/b8eeae44ac0d3c12728ba7373abf7e6e6084d173))
- **TSK-701:** rename files to kebab-case ([95656ce](https://github.com/Secure-T-Team/edu-frontend/commit/95656ce0e55783ebeabe50f37b3cff098517f4c2))
- **TSK-701:** restyle module page ([30f8f15](https://github.com/Secure-T-Team/edu-frontend/commit/30f8f155021364b7ccc58edaf318cab2ea9649b9))
- **TSK-701:** return drone api host to old ([ca928bb](https://github.com/Secure-T-Team/edu-frontend/commit/ca928bb44c0733739f40086c2effbe0aa53ce30b))
- **TSK-701:** return old regExp ([7750ee8](https://github.com/Secure-T-Team/edu-frontend/commit/7750ee81a73b5a6764042029e89aefd59f5d602f))
- **TSK-701:** return regExp ([90d3842](https://github.com/Secure-T-Team/edu-frontend/commit/90d3842145e937fa01595917a4f35e0f21a48934))
- **TSK-701:** return server to old ([2f6f762](https://github.com/Secure-T-Team/edu-frontend/commit/2f6f7622e8cea16f87bdee2e047e2a6085302f91))
- **TSK-701:** return styles ([71983e1](https://github.com/Secure-T-Team/edu-frontend/commit/71983e15b22c81ff307c89e9c444e87831f95ed0))
- **TSK-701:** return to default creating root ([50b2559](https://github.com/Secure-T-Team/edu-frontend/commit/50b25599d583ad5a6c487e3dc07932f6711db575))
- **TSK-701:** return toggle functionality ([2777548](https://github.com/Secure-T-Team/edu-frontend/commit/27775486970018162dab5e7c87bf7f824335ebf1))
- **TSK-701:** set isdev false ([b3b301a](https://github.com/Secure-T-Team/edu-frontend/commit/b3b301aedae14088cf8682f88ff14407061ba0ec))
- **TSK-701:** slides modal scrolls ([0835c67](https://github.com/Secure-T-Team/edu-frontend/commit/0835c6752cc4e25a1129cfb7c9032d76322a68d7))
- **TSK-701:** styles some modals ([b28d9c6](https://github.com/Secure-T-Team/edu-frontend/commit/b28d9c6efd38f6fd31c8471d1285098c54ba9b6c))
- **TSK-701:** translate pages to new i18n instance ([e6ff7dd](https://github.com/Secure-T-Team/edu-frontend/commit/e6ff7dde4981c4e61189882726876a9354ff2c2d))
- **TSK-701:** try change modal download link ([f0b578d](https://github.com/Secure-T-Team/edu-frontend/commit/f0b578d81bcc765e910fdf5cfaa06b4cd4145f64))
- **TSK-701:** try to change regExp ([5da77c1](https://github.com/Secure-T-Team/edu-frontend/commit/5da77c143317b7d54186b3132a5ce3e06c766c1a))
- **TSK-701:** try to clean old deps ([5581f2b](https://github.com/Secure-T-Team/edu-frontend/commit/5581f2b4ac27bee9e681b4e7e497633c700512ff))
- **TSK-701:** try to collect dev from feat(TSK-701) branch ([452734d](https://github.com/Secure-T-Team/edu-frontend/commit/452734d9ffb89570e859f6458087406d4cb4e4bf))
- **TSK-701:** try to delete injectors ([0227225](https://github.com/Secure-T-Team/edu-frontend/commit/02272256e29d83e7982112eef83ce70766f4b349))
- **TSK-701:** try to fix phishing templates edit template by id ([4297efc](https://github.com/Secure-T-Team/edu-frontend/commit/4297efc3cc55a43408634226d00494542b7a9ce0))
- **TSK-701:** try to refactor server config ([4de662b](https://github.com/Secure-T-Team/edu-frontend/commit/4de662b45d5732552852bbb9c8d002a560999b06))
- **TSK-701:** update editor packages ([c259f71](https://github.com/Secure-T-Team/edu-frontend/commit/c259f713109b6f1657f885e219ff532fa07fd10a))
- **TSK-701:** update tags loading status ([c542a4b](https://github.com/Secure-T-Team/edu-frontend/commit/c542a4b9983c3016e817dcc3ea1227ad498b04ef))
- **TSK-707:** added crutch check for employees page for adfs login ([5e0316f](https://github.com/Secure-T-Team/edu-frontend/commit/5e0316f8ced9a10570f036ebe6769be382f40b46))
- **TSK-711:** changed adfs to saml ([d72e388](https://github.com/Secure-T-Team/edu-frontend/commit/d72e388e7d9b03d7e28db18d35b8f1a61e24ecb7))
- **TSK-717:** add hint to load modal ([#764](https://github.com/Secure-T-Team/edu-frontend/issues/764)) ([481feb8](https://github.com/Secure-T-Team/edu-frontend/commit/481feb86506a9387885a0197eecd106ea9cb3657))
- **TSK-722:** add toggle wasChanged to false when change tag ([1c6c631](https://github.com/Secure-T-Team/edu-frontend/commit/1c6c6310afac4431d3b9d026cbe1b720ead7dc99))
- **TSK-722:** add wasChanged to tags and some changes to protected route ([#805](https://github.com/Secure-T-Team/edu-frontend/issues/805)) ([b7e2fef](https://github.com/Secure-T-Team/edu-frontend/commit/b7e2fefea27b7df401b20699074bea15a1e0f996))
- **TSK-737:** fixed password recovery on profile page and refactoring ([1a97547](https://github.com/Secure-T-Team/edu-frontend/commit/1a975471ba1e04e9cf82a2ffb2fb1071536c039f))
- update jodit ([e980ef2](https://github.com/Secure-T-Team/edu-frontend/commit/e980ef2aeb08cb226fd3b7721c7ea878d4e74805))
- update package version ([9b1c182](https://github.com/Secure-T-Team/edu-frontend/commit/9b1c182ece8ff9f5ddf247a40ca8035926c0bfda))
- update packages again aoaoaoaooaoaoa ([8330a5a](https://github.com/Secure-T-Team/edu-frontend/commit/8330a5a70e7f09b68982595f0d16441a6a4eceb5))

### Bug Fixes

- fix event keeper requests ([fd3a649](https://github.com/Secure-T-Team/edu-frontend/commit/fd3a649277f5d088b1d86f2310722fd0bd9e3701))
- **tsk 387:** add scorm course icon download with logic and other fixes ([#740](https://github.com/Secure-T-Team/edu-frontend/issues/740)) ([2d372c7](https://github.com/Secure-T-Team/edu-frontend/commit/2d372c75f979ee831106cee6bfe58d3674ec7f23))
- **tsk 643:** add reset attachments form ([#752](https://github.com/Secure-T-Team/edu-frontend/issues/752)) ([dd1f5e0](https://github.com/Secure-T-Team/edu-frontend/commit/dd1f5e0ad2fcd92538f6b476ffa7418cdeb29c1a))
- **TSK-366:** change btn text to edit ([#737](https://github.com/Secure-T-Team/edu-frontend/issues/737)) ([65a6d1d](https://github.com/Secure-T-Team/edu-frontend/commit/65a6d1d1f59b3685a2c4bbf1e359ce7a2cc623a8))
- **TSK-372:** change variant when notify employeess dialog textarea is empty to notify about it and cancel request ([#738](https://github.com/Secure-T-Team/edu-frontend/issues/738)) ([fe2d172](https://github.com/Secure-T-Team/edu-frontend/commit/fe2d172fc64c411f886e928d3c3bf89c41d52c9f))
- **TSK-387:** show the download icon when there is also a link to the file ([#746](https://github.com/Secure-T-Team/edu-frontend/issues/746)) ([5963295](https://github.com/Secure-T-Team/edu-frontend/commit/59632955b73ec33aca1e0d74e5d5bfaabe5a77dc))
- **TSK-489:** fixed bugs ([69fe7b7](https://github.com/Secure-T-Team/edu-frontend/commit/69fe7b739d945b1ecd0b6c417dc3459307ea6992))
- **TSK-490:** removed unnecessary subgroups only in the SCROM course ([#741](https://github.com/Secure-T-Team/edu-frontend/issues/741)) ([4a140f2](https://github.com/Secure-T-Team/edu-frontend/commit/4a140f276357cb8d4e5f990c76ae7601d3b9dafa))
- **TSK-493:** add scorm accounting to active courses for organization and sub-organization ([#743](https://github.com/Secure-T-Team/edu-frontend/issues/743)) ([f9ee270](https://github.com/Secure-T-Team/edu-frontend/commit/f9ee2700c625b1444eb42ee167ccfd27f411adc9))
- **TSK-506:** change the display when scorm course is completed but the days are in minus ([#739](https://github.com/Secure-T-Team/edu-frontend/issues/739)) ([b2d3da1](https://github.com/Secure-T-Team/edu-frontend/commit/b2d3da1102926023bb3767204e40e59ea0109af6))
- **tsk-604:** refetch templates when copy template and restore cache when delete ([#718](https://github.com/Secure-T-Team/edu-frontend/issues/718)) ([98fa2e5](https://github.com/Secure-T-Team/edu-frontend/commit/98fa2e5c81d1403e31dd4d024bd70a83cb4fe320))
- **TSK-611:** added Y-axis centering to tables ([#765](https://github.com/Secure-T-Team/edu-frontend/issues/765)) ([632dff9](https://github.com/Secure-T-Team/edu-frontend/commit/632dff90bead61643fb99f451fe750da80fb97e6))
- **TSK-612:** fix bugs ([#763](https://github.com/Secure-T-Team/edu-frontend/issues/763)) ([3b92b2c](https://github.com/Secure-T-Team/edu-frontend/commit/3b92b2c0c44f59fcc3a59fbf045576930d39d415))
- **TSK-619:** fixed breadcrumbs text ([a942be1](https://github.com/Secure-T-Team/edu-frontend/commit/a942be1f78c5d5bf7d5bfef09842d20a6ae12662))
- **TSK-619:** fixed button text ([05039f2](https://github.com/Secure-T-Team/edu-frontend/commit/05039f2ced89a97a4e0ce1c4bc84fba3bb35302f))
- **TSK-619:** fixed button text ([867926c](https://github.com/Secure-T-Team/edu-frontend/commit/867926c5d262060d51f697d98d19c5704f0cd20f))
- **TSK-621:** add new text for attachmets card ([7340778](https://github.com/Secure-T-Team/edu-frontend/commit/7340778ad63f629809d75e615c16c65ed88fd380))
- **tsk-621:** fixed bugs on template pages ([9ea6998](https://github.com/Secure-T-Team/edu-frontend/commit/9ea699821deafd17a44069b91a3b9226da395136))
- **TSK-621:** fixed bugs on the email editing page ([#730](https://github.com/Secure-T-Team/edu-frontend/issues/730)) ([68fff4e](https://github.com/Secure-T-Team/edu-frontend/commit/68fff4e9dfa0d3034eb655cb6520d26a3b32e4ef))
- **TSK-628:** changed colors of links to match theme ([1d122db](https://github.com/Secure-T-Team/edu-frontend/commit/1d122dbfb927cc6857576ac587944f2c7b5d3c45))
- **TSK-639:** add angular zone for router and delete extra tag ([f75134a](https://github.com/Secure-T-Team/edu-frontend/commit/f75134ab08232d4ab4ee67db72e0fe1c14d61886))
- **TSK-643:** add error message when html is empty in template email ([#734](https://github.com/Secure-T-Team/edu-frontend/issues/734)) ([75bca44](https://github.com/Secure-T-Team/edu-frontend/commit/75bca44398208dcf2e0924077d7793c893def226))
- **TSK-643:** add reaction to file deletion and addition ([#751](https://github.com/Secure-T-Team/edu-frontend/issues/751)) ([60469ab](https://github.com/Secure-T-Team/edu-frontend/commit/60469ab0b7c3d412ec4cd3c2d4a19b596b33a4d6))
- **TSK-645:** fixed creation campaigns after selecting end date ([98dee9e](https://github.com/Secure-T-Team/edu-frontend/commit/98dee9ed3351ead364a05334ea2205a4a6e8c7a1))
- **TSK-647:** removed extra characters from link in adfs settings ([3425c27](https://github.com/Secure-T-Team/edu-frontend/commit/3425c2765c9102e9cf09262c9ae08093625c7a0d))
- **TSK-651:** fixed loading on phishing companies page ([5f55a2f](https://github.com/Secure-T-Team/edu-frontend/commit/5f55a2f87bf7064eaf6f11f37023746d2c61431b))
- **TSK-667:** swapped progress of testing and learnin in places ([381c1ec](https://github.com/Secure-T-Team/edu-frontend/commit/381c1ec55d5cc6b6c7948d0444f89f9308331176))
- **TSK-677:** corrected deleting employees via cross ([1a9eed4](https://github.com/Secure-T-Team/edu-frontend/commit/1a9eed4caba118b3a7827725541f2639d47085b8))
- **TSK-681:** removed extra scrollbars and fixed infinity scrolling ([42e7c34](https://github.com/Secure-T-Team/edu-frontend/commit/42e7c345028d3737315dc730348fab958702d809))
- **TSK-699:** fix error when user id is undefined in notify modal ([#749](https://github.com/Secure-T-Team/edu-frontend/issues/749)) ([8e12cb6](https://github.com/Secure-T-Team/edu-frontend/commit/8e12cb6e5856a167f22413f9b2e87c0603232e36))
- **TSK-699:** hide check for text so you can delete note ([b72025f](https://github.com/Secure-T-Team/edu-frontend/commit/b72025f31ea9162a24218b11ee2c4f8281396ddc))
- **TSK-699:** update button name ([1382bef](https://github.com/Secure-T-Team/edu-frontend/commit/1382bef3fc9189aec7f5dc7e956836f4a03e4f93))
- **TSK-701:** add preventDefault to edit slide in edit module modal ([8076149](https://github.com/Secure-T-Team/edu-frontend/commit/8076149db463254e574adca23d801b93faafb67d))
- **TSK-701:** change campaigns choose variant ([16dfe92](https://github.com/Secure-T-Team/edu-frontend/commit/16dfe922df323ce1e2c8469c31279a5d406df342))
- **TSK-701:** change user completed card link opportunity ([b70cd14](https://github.com/Secure-T-Team/edu-frontend/commit/b70cd14eedaee047c982bd943873db92b01fa36b))
- **tsk-712:** sorted the display of items in templates ([#761](https://github.com/Secure-T-Team/edu-frontend/issues/761)) ([d639b5f](https://github.com/Secure-T-Team/edu-frontend/commit/d639b5f2775a70948d15cf1d85eb56c13fe5e456))
- **tsk-717:** fix height and hint ([#766](https://github.com/Secure-T-Team/edu-frontend/issues/766)) ([9e37417](https://github.com/Secure-T-Team/edu-frontend/commit/9e3741735cff1f516f9d31a94c88ad17a65be3d0))
- **TSK-730:** fix auth form ([#760](https://github.com/Secure-T-Team/edu-frontend/issues/760)) ([e9b5f5d](https://github.com/Secure-T-Team/edu-frontend/commit/e9b5f5d3f2bef738e4097246529b96268d8b8436))
- **TSK-797:** fix modals overflow-y ([9ba2358](https://github.com/Secure-T-Team/edu-frontend/commit/9ba23584ed91dd1d2d8ee16770a65eebb8e39b54))
- **TSK-800:** increase departments phishing column width ([93a2818](https://github.com/Secure-T-Team/edu-frontend/commit/93a2818b8f73ef8fbce750b4f406b93cf062a5a9))

### Refactoring

- delete extra angular page and routing ([d04202e](https://github.com/Secure-T-Team/edu-frontend/commit/d04202eec9952dc262041f0ecfcc4a0b1c3db231))
- delete extra angular page and routing ([2b6971c](https://github.com/Secure-T-Team/edu-frontend/commit/2b6971cd0eff8678eb8e8189861e817b5ed2c92c))
- hide angualr sidebar ([1fd3a4a](https://github.com/Secure-T-Team/edu-frontend/commit/1fd3a4a24e22f6ee751653bd0d446dde0dbbadeb))
- moved api folders to new folder ([16fad08](https://github.com/Secure-T-Team/edu-frontend/commit/16fad087093ac0de8ff04e80dbb9953bb0876ba8))
- moved context folders to new place ([7a4017d](https://github.com/Secure-T-Team/edu-frontend/commit/7a4017d0e33badee6187110a24b4f718f9499ad6))
- **tsk 620-621:** template emails edit and create page to react ([#728](https://github.com/Secure-T-Team/edu-frontend/issues/728)) ([8790c5c](https://github.com/Secure-T-Team/edu-frontend/commit/8790c5c8824226232d2eda3367f0cb8ad8f40f88))
- **TSK-300:** delete extra code ([#759](https://github.com/Secure-T-Team/edu-frontend/issues/759)) ([8ca1754](https://github.com/Secure-T-Team/edu-frontend/commit/8ca1754fe7443e308a3590cead2d5dbde245353e))
- **TSK-300:** delete extra error handlers ([0ef40b9](https://github.com/Secure-T-Team/edu-frontend/commit/0ef40b98bf282cfa979f76c7dbd0d99511e58a19))
- **TSK-506:** change the display of days in the scorm course when it is completed ([#753](https://github.com/Secure-T-Team/edu-frontend/issues/753)) ([a8f0d3e](https://github.com/Secure-T-Team/edu-frontend/commit/a8f0d3eb903efffa547fffe6f6cf766f3918fe26))
- **TSK-608:** updated new tabs styles ([be51f24](https://github.com/Secure-T-Team/edu-frontend/commit/be51f24cd767ca250237a8b13024e67052849658))
- **tsk-618,-619:** fixed bugs on the landing editing and creating page ([#729](https://github.com/Secure-T-Team/edu-frontend/issues/729)) ([17ad7b0](https://github.com/Secure-T-Team/edu-frontend/commit/17ad7b023e67f61c838be75c74ad2de3c1c07de8))
- **TSK-618:** added title check to breadcrumbs ([6489d84](https://github.com/Secure-T-Team/edu-frontend/commit/6489d84f7197705e0e242f978585f36c03d8bb16))
- **tsk-639:** detail template page to react ([#727](https://github.com/Secure-T-Team/edu-frontend/issues/727)) ([9476613](https://github.com/Secure-T-Team/edu-frontend/commit/947661370aaa217de58ee6049e1380cfbf3fdd4a))
- **TSK-650:** removed extra load variable ([#720](https://github.com/Secure-T-Team/edu-frontend/issues/720)) ([bde263e](https://github.com/Secure-T-Team/edu-frontend/commit/bde263e7cce2b7ab81a7c061b60451f66f6ff7a1))
- **TSK-666:** add eslint for react files ([c39c78a](https://github.com/Secure-T-Team/edu-frontend/commit/c39c78aa3143edfcee2ff1a6402c0cfeb4a09e64))
- **TSK-666:** add eslint for react files ([72e83c1](https://github.com/Secure-T-Team/edu-frontend/commit/72e83c1303c140cb6b07d10328ead2e2f43228a8))
- **TSK-666:** delete old profile page and update new ([733d48f](https://github.com/Secure-T-Team/edu-frontend/commit/733d48fc5e1e962f20c0446d3817e708ab113881))
- **TSK-666:** deleted graphql code and temporarily transferred necessary old types ([7f54e40](https://github.com/Secure-T-Team/edu-frontend/commit/7f54e406160a57beea8be070fef425924a7ce741))
- **TSK-666:** deleted old extra icons ([157e949](https://github.com/Secure-T-Team/edu-frontend/commit/157e949e6a1cf33f4e60612bc73b0fa1c031a4a9))
- **TSK-666:** deleted old storybook and old script folders ([2545549](https://github.com/Secure-T-Team/edu-frontend/commit/25455493f67b7ca64f483025cd21963baa265ea4))
- **TSK-666:** moved auth pages to new folders and refactor ([faada38](https://github.com/Secure-T-Team/edu-frontend/commit/faada38c0727d97c8ca8cc2029ea02ca77a5d400))
- **TSK-666:** moved general settings to new folders and ignore eslint for angular ([3a0f4ed](https://github.com/Secure-T-Team/edu-frontend/commit/3a0f4ed2afaeac1274ceb86ed98c915cc2a100a7))
- **TSK-666:** moved old hooks to new ones and changed imports ([dc82932](https://github.com/Secure-T-Team/edu-frontend/commit/dc8293222f7beb4ec054fd75959545f92056959e))
- **TSK-666:** refactoring my courses page and certificate modal ([2fdc606](https://github.com/Secure-T-Team/edu-frontend/commit/2fdc606c01f447adaee36bf7dbc55dca66562ce1))
- **TSK-666:** removed graphql and apollo code and dependencies ([12a707f](https://github.com/Secure-T-Team/edu-frontend/commit/12a707fd7b17893e3712fdcb871c427abadb3670))
- **TSK-666:** stash refactoring login form ([deead31](https://github.com/Secure-T-Team/edu-frontend/commit/deead3139a69dc93ef9c12f7d307849f98759583))
- **TSK-667:** delete extra imports and [] || ([2623811](https://github.com/Secure-T-Team/edu-frontend/commit/2623811b5c3d482a31d027d4509bd98dbcfc80db))
- **TSK-667:** delete old edit note ([904aa55](https://github.com/Secure-T-Team/edu-frontend/commit/904aa55230e7f96908ad1f6be4b7c437333afa92))
- **TSK-667:** fixed bugs with merging ([872e71b](https://github.com/Secure-T-Team/edu-frontend/commit/872e71b0635d326999f19b10c9d3cf166c986e30))
- **TSK-681:** change sidebar margin trouble ([#733](https://github.com/Secure-T-Team/edu-frontend/issues/733)) ([614f373](https://github.com/Secure-T-Team/edu-frontend/commit/614f373cf5ec48011d59972f8a0ec5fa05ec3e49))
- **TSK-688:** refactoring license page ([8ef29b4](https://github.com/Secure-T-Team/edu-frontend/commit/8ef29b46085cee856e706f664a46576803aa586b))
- **TSK-699:** change note edit modal to react component ([#736](https://github.com/Secure-T-Team/edu-frontend/issues/736)) ([db1a583](https://github.com/Secure-T-Team/edu-frontend/commit/db1a583e46ea6ee2c6506d99e6ccf18880f02733))
- **TSK-699:** update notification ([7acc3c1](https://github.com/Secure-T-Team/edu-frontend/commit/7acc3c19ae6eb661efc17f2ef44f588f897299b3))
- **TSK-701:** phishing pages to new routing ([ade563e](https://github.com/Secure-T-Team/edu-frontend/commit/ade563e5680d1fa6b5b9d1f1cd4cb7734feabf31))
- **TSK-739:** refactor profile page ([9da03ab](https://github.com/Secure-T-Team/edu-frontend/commit/9da03abb08f9e1a2c33c40c7f39854a9b6a93992))
- **TSK-742:** moved action log page file to new folder ([3d2c372](https://github.com/Secure-T-Team/edu-frontend/commit/3d2c3723f7c9444ee03b1ae82d93786d99eff5a4))
- **TSK-745:** refactor organizations page and organizations component ([6f728db](https://github.com/Secure-T-Team/edu-frontend/commit/6f728dbc96ce0380c9c4537b90b3066d6588743c))
- **TSK-746:** refactor create organization page ([0575875](https://github.com/Secure-T-Team/edu-frontend/commit/0575875e2184cb9a167b0a7d483b9e84731f1802))
- **TSK-747:** refactor edit organization page ([d105842](https://github.com/Secure-T-Team/edu-frontend/commit/d105842734beb4ec677486cb3dc4c798a43828a8))
- **TSK-748:** moved phishing campaings page to new folders ([9ffd3fe](https://github.com/Secure-T-Team/edu-frontend/commit/9ffd3febf13fe95833cc1eb3d8e43023114435bc))

## [2.51.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.50.0...v2.51.0) (2023-09-22)

### Features

- **TSK-488:** added lower case for employee search ([d07d238](https://github.com/Secure-T-Team/edu-frontend/commit/d07d238d572a42c6039260b5630b183426fd3465))
- **TSK-608:** added badge for new tobs and connected it to phishing campaigns page ([ad29216](https://github.com/Secure-T-Team/edu-frontend/commit/ad29216603a404df8ec550237feb6ee9ba57a4cf))
- **TSK-653:** added disabled for completed courses ([d42a454](https://github.com/Secure-T-Team/edu-frontend/commit/d42a454d75564dc1dd3b1fadbd500e7a794d8c46))

### Bug Fixes

- **TSK-651:** fixed loading on phishing companies page ([fb913fb](https://github.com/Secure-T-Team/edu-frontend/commit/fb913fb17940c656f65b039640a8f96454f864d7))

### Refactoring

- **TSK-608:** updated new tabs styles ([53ac92c](https://github.com/Secure-T-Team/edu-frontend/commit/53ac92ca8bed8b9c1a652cb178427857e690f436))

## [2.50.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.49.1...v2.50.0) (2023-09-21)

### Features

- **TSK-638:** added hints for turn off tag ([b148544](https://github.com/Secure-T-Team/edu-frontend/commit/b1485440033280fe55a520d9afeeb39731eceef9))

### Bug Fixes

- **TSK-647:** removed extra characters from link in adfs settings ([cc1505a](https://github.com/Secure-T-Team/edu-frontend/commit/cc1505aabc0c20edca1b5fea5a249ed08e2d6bf5))

### Refactoring

- **TSK-650:** removed extra load variable ([#720](https://github.com/Secure-T-Team/edu-frontend/issues/720)) ([042c2b1](https://github.com/Secure-T-Team/edu-frontend/commit/042c2b1e4a50d3477980e8c169cf070eacd7b965))

### [2.49.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.49.0...v2.49.1) (2023-09-21)

### Bug Fixes

- **TSK-604:** fix refetch when copy template and fix cachne in getTemplates(for delete) ([#717](https://github.com/Secure-T-Team/edu-frontend/issues/717)) ([93b1ba3](https://github.com/Secure-T-Team/edu-frontend/commit/93b1ba3059bc5ffd174cd4956537265be646a2de))

## [2.49.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.48.0...v2.49.0) (2023-09-20)

### Features

- add error handler for create phishing ([#676](https://github.com/Secure-T-Team/edu-frontend/issues/676)) ([4a01a26](https://github.com/Secure-T-Team/edu-frontend/commit/4a01a26aa7db60ddf358d014d270e999891db1ed))
- add helpers ([b3abfd3](https://github.com/Secure-T-Team/edu-frontend/commit/b3abfd34bd34c2d83a0b5321aa6d7258940c36f8))
- add helpers ([#665](https://github.com/Secure-T-Team/edu-frontend/issues/665)) ([9599917](https://github.com/Secure-T-Team/edu-frontend/commit/9599917cbb41b2466f8b47b4876a722db21bd8b2))
- add length validation ([f6ab418](https://github.com/Secure-T-Team/edu-frontend/commit/f6ab418251ca1d5d0b797ae1ba80467b4f707736))
- add page reset ([#666](https://github.com/Secure-T-Team/edu-frontend/issues/666)) ([e26964c](https://github.com/Secure-T-Team/edu-frontend/commit/e26964ca26c64cf02f49f1416404af6b76007e52))
- add sorting for my courses ([#664](https://github.com/Secure-T-Team/edu-frontend/issues/664)) ([2fafd51](https://github.com/Secure-T-Team/edu-frontend/commit/2fafd51d78c053772c42c687673ee4f73082f8cd))
- add validation for registration ([#663](https://github.com/Secure-T-Team/edu-frontend/issues/663)) ([c745953](https://github.com/Secure-T-Team/edu-frontend/commit/c745953c88c4da38a5972949bfd3002fac483017))
- change text ([7cee8af](https://github.com/Secure-T-Team/edu-frontend/commit/7cee8af207dc6030859c9f392437e4c842859360))
- changed color of adfs button on hover and correct forget button ([#713](https://github.com/Secure-T-Team/edu-frontend/issues/713)) ([580e542](https://github.com/Secure-T-Team/edu-frontend/commit/580e542eb8cdbfe5401f26010dccd284507f0ee5))
- connect new request for scorm course ([939a693](https://github.com/Secure-T-Team/edu-frontend/commit/939a6931c6a866d08b7ecb1ac336fa1b91185dc7))
- correct button name ([e2e9189](https://github.com/Secure-T-Team/edu-frontend/commit/e2e9189687747787a48c8e1534efd4691e51d5a0))
- correct button name ([5a8f6e3](https://github.com/Secure-T-Team/edu-frontend/commit/5a8f6e370576cc08c465491da32791478b52e2ee))
- create new services for scorm ([f226b0d](https://github.com/Secure-T-Team/edu-frontend/commit/f226b0dd2fe2185ccf53e702b98bf0e3cc8e35da))
- delete extra file ([a0bde26](https://github.com/Secure-T-Team/edu-frontend/commit/a0bde2675e422fd07c237d1127eb4a528b7b80d1))
- merged courses on my courses page and corrected certificates ([ef90d4c](https://github.com/Secure-T-Team/edu-frontend/commit/ef90d4ce9cd347d60ef71828988f4570a5ddb7e9))
- **my courses:** update tabs style ([#690](https://github.com/Secure-T-Team/edu-frontend/issues/690)) ([0ef0654](https://github.com/Secure-T-Team/edu-frontend/commit/0ef0654b438f028d2098dc641832beb944508b04))
- rever reverta ([aa6697f](https://github.com/Secure-T-Team/edu-frontend/commit/aa6697f7b47e577974cb11efedc9203eedde0b34))
- **tsk 604:** refactor phishing templates page ([7c71a5b](https://github.com/Secure-T-Team/edu-frontend/commit/7c71a5b0e70b53f81969a47574aa8ede01207339))
- **TSK-367:** added saving filters ([#674](https://github.com/Secure-T-Team/edu-frontend/issues/674)) ([17c7ee4](https://github.com/Secure-T-Team/edu-frontend/commit/17c7ee4f440b745f3d0ee514d12f93948088c656))
- **TSK-368:** update adfs settings, again ([c2119da](https://github.com/Secure-T-Team/edu-frontend/commit/c2119da6b159c368a5c8096fcd27abc25bbe639b))
- **TSK-374:** add close icon for angular modal ([#672](https://github.com/Secure-T-Team/edu-frontend/issues/672)) ([fb8c24a](https://github.com/Secure-T-Team/edu-frontend/commit/fb8c24a4f516ecd24aa95908d8e3a8a135a7ab7b))
- **TSK-488:** new target modal ([#675](https://github.com/Secure-T-Team/edu-frontend/issues/675)) ([5e7321b](https://github.com/Secure-T-Team/edu-frontend/commit/5e7321b0eca7c30f3afeb63c805c6ed3e0500b9d))
- **tsk-510:** added translation for errors ([#678](https://github.com/Secure-T-Team/edu-frontend/issues/678)) ([f0dc993](https://github.com/Secure-T-Team/edu-frontend/commit/f0dc9937b8db905fae0273762b7d6e17e10396eb))
- **TSK-541:** adding adfs functionality ([#701](https://github.com/Secure-T-Team/edu-frontend/issues/701)) ([123ea14](https://github.com/Secure-T-Team/edu-frontend/commit/123ea14be3b2ee5ad4712ae7b203d5507af0311c))
- **TSK-542:** add permissions for adfs settings page and update old permissiongs ([86b02c4](https://github.com/Secure-T-Team/edu-frontend/commit/86b02c4e4568991eb1eb10ec7b28941b86b8a2cf))
- **TSK-542:** added a crutch cookie check at login ([107ac8a](https://github.com/Secure-T-Team/edu-frontend/commit/107ac8af79d631d30a9050858f8588050e33e729))
- **TSK-542:** added a login button for adfs and getBaseQuery function for servies ([#697](https://github.com/Secure-T-Team/edu-frontend/issues/697)) ([16685e7](https://github.com/Secure-T-Team/edu-frontend/commit/16685e78d0be61bf5c2d2fbddbea752806f9a6c8))
- **TSK-542:** added a new error handler ([e44482b](https://github.com/Secure-T-Team/edu-frontend/commit/e44482bf6af449c96aac54ae1d8026367a3a5dcf))
- **TSK-542:** added encoded for domain url ([675ca75](https://github.com/Secure-T-Team/edu-frontend/commit/675ca75ce0eb4b28bbc4c96162db38cdbd28e6ac))
- **TSK-542:** added logout with cleaning cookies ([d531fe0](https://github.com/Secure-T-Team/edu-frontend/commit/d531fe0d5919162b3175b69737a27191e80fdc8c))
- **TSK-542:** changed hostname to origin for redirect ([34d36fd](https://github.com/Secure-T-Team/edu-frontend/commit/34d36fdfe76492193e66a8c8702ae75ccc5ccb3e))
- **TSK-542:** corrected adfs permissions ([454b43d](https://github.com/Secure-T-Team/edu-frontend/commit/454b43dd4d088f15611e5b3e4957b8eb9c9bfe6a))
- **TSK-604:** phishing templates page ([7ce1d13](https://github.com/Secure-T-Team/edu-frontend/commit/7ce1d13b4c416a60938f5b3f6f7ea833c6c4ed8c))
- **TSK-644:** added hints for adfs settings ([#714](https://github.com/Secure-T-Team/edu-frontend/issues/714)) ([ead2702](https://github.com/Secure-T-Team/edu-frontend/commit/ead270281c99f8cf118e6a2a640ab9b899fa4bbc))
- update delete and add employees request ([c434d93](https://github.com/Secure-T-Team/edu-frontend/commit/c434d939228b89a15b6307c9cc995e75da76d83f))
- update delete and add employees request ([0f15944](https://github.com/Secure-T-Team/edu-frontend/commit/0f15944aed756571385b884f3feec8b1a525c02e))
- update employees statistics ([44df4d1](https://github.com/Secure-T-Team/edu-frontend/commit/44df4d1860333d149782d62b4c2a5b6f9e0a8577))
- update old requests ([c033722](https://github.com/Secure-T-Team/edu-frontend/commit/c0337220b420d4ce342fde3634781bd09ac39e68))
- update organization pages ([d95fce9](https://github.com/Secure-T-Team/edu-frontend/commit/d95fce9719407c3d936273a7e12e768a60d21aea))
- update statistic charts ([2982504](https://github.com/Secure-T-Team/edu-frontend/commit/2982504f954ac9fd293ba961a0ce26ed97021210))
- исправлено восстановление прогресса курса ([19c2251](https://github.com/Secure-T-Team/edu-frontend/commit/19c2251dea6dd3f1cce6f3d47711844b3c2a04b5))

### Bug Fixes

- fix button name ([#669](https://github.com/Secure-T-Team/edu-frontend/issues/669)) ([0aca63d](https://github.com/Secure-T-Team/edu-frontend/commit/0aca63d7c356ac9566020d4537ec95ebf0fa475a))
- tsk 473 ([#662](https://github.com/Secure-T-Team/edu-frontend/issues/662)) ([c680e89](https://github.com/Secure-T-Team/edu-frontend/commit/c680e895dc3406ff977f965a559e687dbb882248))
- **TSK-368):** corrected crutch link for adfs settings ([e2307fe](https://github.com/Secure-T-Team/edu-frontend/commit/e2307febaec4c6c053f8b5f60c344019157f33b5))
- **TSK-368):** corrected crutch link for adfs settings ([43d652a](https://github.com/Secure-T-Team/edu-frontend/commit/43d652a2e2b1ce2f9c7a044f8c1270e4f29f277a))
- **TSK-368:** fixed request body in adfs settings ([a41ecc6](https://github.com/Secure-T-Team/edu-frontend/commit/a41ecc6a769a1b43304ed9c82735b135665e6d82))
- **TSK-368:** update hints ([6c523ef](https://github.com/Secure-T-Team/edu-frontend/commit/6c523ef9bc2b16c4ec61d436c7dc6d9e537c77fc))
- **TSK-488:** fix opening with search([#677](https://github.com/Secure-T-Team/edu-frontend/issues/677)) ([e8c48e4](https://github.com/Secure-T-Team/edu-frontend/commit/e8c48e44fb54795cdcb746c03b240249dc6b5b03))
- **TSK-542:** changed host to origin in the redirect request ([054cfa1](https://github.com/Secure-T-Team/edu-frontend/commit/054cfa176553961b635da17afb98c644366e2dc3))
- **TSK-544:** deleted min height for table ([#712](https://github.com/Secure-T-Team/edu-frontend/issues/712)) ([1efde97](https://github.com/Secure-T-Team/edu-frontend/commit/1efde97d2489a75de4f0ac2029ac61d94d91de8b))
- **TSK-544:** fix table height on audit log page ([#691](https://github.com/Secure-T-Team/edu-frontend/issues/691)) ([c2fc890](https://github.com/Secure-T-Team/edu-frontend/commit/c2fc8908b2d70251eab7860f25f3a91144e31475))
- **TSK-544:** fix table height on audit log page ([#704](https://github.com/Secure-T-Team/edu-frontend/issues/704)) ([f04d1c9](https://github.com/Secure-T-Team/edu-frontend/commit/f04d1c9c7af7f04e2e4a3c57e03fcf30cbbad9e0))
- **TSK-544:** fixing bugs on audit page ([48787c8](https://github.com/Secure-T-Team/edu-frontend/commit/48787c86db317ed52e2db867584d90dc804c8bb1))
- **TSK-604:** change redirect to edit page in phishing templates page, ([#688](https://github.com/Secure-T-Team/edu-frontend/issues/688)) ([6adf5d0](https://github.com/Secure-T-Team/edu-frontend/commit/6adf5d0788cee9162e28219862e11e5fe04f2e1e))
- **TSK-632:** fixed opening and closing of modals ([6b3f135](https://github.com/Secure-T-Team/edu-frontend/commit/6b3f135db7dd96f6247a1577fb183766bd49ec11))
- **TSK-644:** removed extra quotes ([8a65424](https://github.com/Secure-T-Team/edu-frontend/commit/8a654241327c946c97c9c7765b823d69106e8153))
- **TSK-644:** removed extra quotes ([b8154d5](https://github.com/Secure-T-Team/edu-frontend/commit/b8154d5ddf26c03392e1a167fd2eb2286ccf6773))

### Refactoring

- delete extra code ([bfc5d94](https://github.com/Secure-T-Team/edu-frontend/commit/bfc5d9496dceef73ac3969538d390350761156cd))
- delete extra code ([94e124d](https://github.com/Secure-T-Team/edu-frontend/commit/94e124d155aee8c3a648026fe8cb62c7a2242533))
- delete extra code ([eb1abb0](https://github.com/Secure-T-Team/edu-frontend/commit/eb1abb00e011e4770a8945a1bf315c069a1932c9))
- delete extra code ([150162a](https://github.com/Secure-T-Team/edu-frontend/commit/150162ae764c282ab1d3ea9195eac30737fc7212))
- delete extra code ([b71ca7f](https://github.com/Secure-T-Team/edu-frontend/commit/b71ca7f9162525c82565e4f3fba68dd9eeb8f8c1))
- **TSK-540:** settings pages on react ([#702](https://github.com/Secure-T-Team/edu-frontend/issues/702)) ([fd35f07](https://github.com/Secure-T-Team/edu-frontend/commit/fd35f07d60a1c95bbf4cba423bfb7f6956184627))
- **TSK-544:** audit page on react ([1db852c](https://github.com/Secure-T-Team/edu-frontend/commit/1db852c33fd49dcffd0a3932d9820f832a8c082d))
- **tsk-608:** refactoring phishing campaign page to react and redisign, and pagination (tsk-482) ([#705](https://github.com/Secure-T-Team/edu-frontend/issues/705)) ([d4d71db](https://github.com/Secure-T-Team/edu-frontend/commit/d4d71db383732644b0235be0a9a49efaceb204c2))
- **tsk-616:** edit template page and create template page to react ([#703](https://github.com/Secure-T-Team/edu-frontend/issues/703)) ([903e5cc](https://github.com/Secure-T-Team/edu-frontend/commit/903e5cc3974e281e3b1be1c377d1169fc1b45950))
- **tsk-616:** edit template page and create template page to react ([#706](https://github.com/Secure-T-Team/edu-frontend/issues/706)) ([8c20c7b](https://github.com/Secure-T-Team/edu-frontend/commit/8c20c7b5c4c701738b052a6a108a454ca4fe53a9))

## [2.48.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.47.0...v2.48.0) (2023-09-06)

## [2.48.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.47.0...v2.48.0) (2023-09-06)

### Features

- add error handler for create phishing ([#676](https://github.com/Secure-T-Team/edu-frontend/issues/676)) ([21ffad6](https://github.com/Secure-T-Team/edu-frontend/commit/21ffad6e62bc5fa86994c3386110b8ce1ad64e4e))
- add page reset ([#666](https://github.com/Secure-T-Team/edu-frontend/issues/666)) ([4b2f50d](https://github.com/Secure-T-Team/edu-frontend/commit/4b2f50d722cb86999fa12b5a8a1026eb077922d5))
- add sorting for my courses ([#664](https://github.com/Secure-T-Team/edu-frontend/issues/664)) ([c56127b](https://github.com/Secure-T-Team/edu-frontend/commit/c56127b9188e403de053c0096816b2176ee69cd9))
- create new services for scorm ([6432776](https://github.com/Secure-T-Team/edu-frontend/commit/6432776bfd5bd93268ddeb771f87a3f243299541))
- merged courses on my courses page and corrected certificates ([1a6e12b](https://github.com/Secure-T-Team/edu-frontend/commit/1a6e12bfa997f7da8b86b5cd2623e2ff6087b082))
- **tsk-510:** added translation for errors ([#678](https://github.com/Secure-T-Team/edu-frontend/issues/678)) ([2b41726](https://github.com/Secure-T-Team/edu-frontend/commit/2b41726f6628cb21592f0b467c843cd38f502083))

### Bug Fixes

- tsk 473 ([#662](https://github.com/Secure-T-Team/edu-frontend/issues/662)) ([4b4b1b3](https://github.com/Secure-T-Team/edu-frontend/commit/4b4b1b30946606170a97e8a171234a3c16d46aa3))

### Refactoring

- delete extra code ([d5b2404](https://github.com/Secure-T-Team/edu-frontend/commit/d5b2404bf8a3a1f65e0962b8a497de11147f6e66))
- delete extra code ([0319209](https://github.com/Secure-T-Team/edu-frontend/commit/031920937cc586772bdfd9eac4d2b29b1f8ba787))

## [2.47.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.46.0...v2.47.0) (2023-08-31)

### Features

- **TSK-367:** added saving filters ([#674](https://github.com/Secure-T-Team/edu-frontend/issues/674)) ([58108a3](https://github.com/Secure-T-Team/edu-frontend/commit/58108a35a6dfa259eb2344db6b1c0a188741d6e5))
- **TSK-374:** add close icon for angular modal ([#672](https://github.com/Secure-T-Team/edu-frontend/issues/672)) ([16b0908](https://github.com/Secure-T-Team/edu-frontend/commit/16b09081a9ae7b6cc631d8d42b7fcc02e286d022))

## [2.46.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.45.0...v2.46.0) (2023-08-28)

### Features

- add helpers ([20b100e](https://github.com/Secure-T-Team/edu-frontend/commit/20b100e941ed1294953ca004f3098675f729e0b6))
- correct button name ([2a565bf](https://github.com/Secure-T-Team/edu-frontend/commit/2a565bf9afa59487561b1e84ea2e4033397f55f7))

### Bug Fixes

- fix button name ([#669](https://github.com/Secure-T-Team/edu-frontend/issues/669)) ([15931b0](https://github.com/Secure-T-Team/edu-frontend/commit/15931b0be0d7696e8ab82bc8ac775972d3c6fa32))

### Refactoring

- delete extra code ([e06d5e3](https://github.com/Secure-T-Team/edu-frontend/commit/e06d5e3c56a740417fcab913bad638bc0226ef17))

## [2.45.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.44.0...v2.45.0) (2023-08-24)

### Features

- add length validation ([9231f66](https://github.com/Secure-T-Team/edu-frontend/commit/9231f667d1c68afcee830d4cc69d56f0d6dea910))
- add validation for registration ([#663](https://github.com/Secure-T-Team/edu-frontend/issues/663)) ([6ae4021](https://github.com/Secure-T-Team/edu-frontend/commit/6ae40217ba4ecd3196b883cff39beb2466fb1694))

## [2.44.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.43.0...v2.44.0) (2023-08-21)

### Features

- change text ([b26f214](https://github.com/Secure-T-Team/edu-frontend/commit/b26f214081d12b3bbe4fabe9efbba04127173c87))
- update delete and add employees request ([b806813](https://github.com/Secure-T-Team/edu-frontend/commit/b80681370f5f8df7f1c9ca0216ce023b66b0107c))

## [2.43.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.42.1...v2.43.0) (2023-08-17)

### Features

- add add base dep stat ([c512a2f](https://github.com/Secure-T-Team/edu-frontend/commit/c512a2f7e0e43efaa203313c8d7a083d61e80c66))
- add and connect message service ([5cc1997](https://github.com/Secure-T-Team/edu-frontend/commit/5cc1997537c15c3962b37b43ff73b2b28d2f6a92))
- add base selecting for modal ([e67ab3b](https://github.com/Secure-T-Team/edu-frontend/commit/e67ab3bc3f200a9c76eec06c9ea8b35f1839692a))
- add base target modal ([1be0555](https://github.com/Secure-T-Team/edu-frontend/commit/1be05550b8750ee29fff0cd7c5ff1ffae644e01d))
- add callback for search ([2c578b9](https://github.com/Secure-T-Team/edu-frontend/commit/2c578b9fe70287da830c21b3bedeebe40e9475b8))
- add course card selector ([f0bbf02](https://github.com/Secure-T-Team/edu-frontend/commit/f0bbf0258506d1e042d476f309a87b4fa4a102f6))
- add courses for tags ([5c29bb1](https://github.com/Secure-T-Team/edu-frontend/commit/5c29bb105ca3f8d041ec10087673774d8078b183))
- add current date as default for create scorm ([789b78f](https://github.com/Secure-T-Team/edu-frontend/commit/789b78f94e5c8d4856cf28eb77cc0e2d761924b3))
- add department list ([22bb233](https://github.com/Secure-T-Team/edu-frontend/commit/22bb233aa9b0e13928cbaf663e1b3a0cb42af4ca))
- add disabled for course and template modals ([5f07996](https://github.com/Secure-T-Team/edu-frontend/commit/5f07996bdedab93fb9801c863f845e701078af1a))
- add disabled for org button ([badfd6a](https://github.com/Secure-T-Team/edu-frontend/commit/badfd6a26876f94a54158ebb09fcfcf411aa5b35))
- add disabled for target modal button ([380db56](https://github.com/Secure-T-Team/edu-frontend/commit/380db56e9a23e0b83e408a8e3f1b403f8a68d353))
- add edit and go button for employee card ([2a2a01f](https://github.com/Secure-T-Team/edu-frontend/commit/2a2a01f16f0680aee65b230c1ccec8dd95fe0009))
- add email for target modal ([1159d82](https://github.com/Secure-T-Team/edu-frontend/commit/1159d82927d92f957878f1250d386f8b05f3352a))
- add employees filter ([63f754a](https://github.com/Secure-T-Team/edu-frontend/commit/63f754aff1874fc112468138f5bc6f5e3ff1087a))
- add empty text for target modal ([59ac60b](https://github.com/Secure-T-Team/edu-frontend/commit/59ac60b282533612530f52c0dca33e8f4d145e50))
- add icon with data component and connect ([fc541e1](https://github.com/Secure-T-Team/edu-frontend/commit/fc541e18e20707f2eeda0f61d91a51861115d4ef))
- add import and hide story ([3627622](https://github.com/Secure-T-Team/edu-frontend/commit/3627622f4d7aad8865ed92fbcd56cb05c2b509bd))
- add import employees modal ([7dbf41c](https://github.com/Secure-T-Team/edu-frontend/commit/7dbf41c696ef38e897fef129c7f24f1cb2e4a157))
- add input screenshot tests and add test args generator ([64c93ad](https://github.com/Secure-T-Team/edu-frontend/commit/64c93ad7b69841bd67cdbfbac21ad10c540f602a))
- add keys ([6637ed8](https://github.com/Secure-T-Team/edu-frontend/commit/6637ed8281b97a6a3b6e54151bceded48faa0357))
- add loader for filter ([acf5d15](https://github.com/Secure-T-Team/edu-frontend/commit/acf5d15725bcbd685009f2cd11bfff0ea696b586))
- add memo and delete extra var ([25212b7](https://github.com/Secure-T-Team/edu-frontend/commit/25212b741c2552d3a674a9d613340433fda14ba2))
- add memo and delete extra var ([3a3cff7](https://github.com/Secure-T-Team/edu-frontend/commit/3a3cff7404a17e5466df0e4d42bd88313cd15d60))
- add memo for search ([c30448d](https://github.com/Secure-T-Team/edu-frontend/commit/c30448d6eb07cc31a94e9c028ea839234b42828d))
- add menu for rounded button ([3215782](https://github.com/Secure-T-Team/edu-frontend/commit/3215782fe7914ac744fc9b03934956e730a5b606))
- add new fields for employees filter ([821e8b7](https://github.com/Secure-T-Team/edu-frontend/commit/821e8b7152b67dcfffe9c1f785bd7b8a667281f1))
- add new icons for employees and correct styles ([6e28794](https://github.com/Secure-T-Team/edu-frontend/commit/6e28794563cd0cb1cfa1cc576d70c0708a22ef02))
- add new phishing request ([b4a538e](https://github.com/Secure-T-Team/edu-frontend/commit/b4a538e064f12802de9839854c07f6d1174010b6))
- add no active course text ([58eea38](https://github.com/Secure-T-Team/edu-frontend/commit/58eea38700e421360339275bfddea176b435b5d6))
- add notification for tags page ([dbc7367](https://github.com/Secure-T-Team/edu-frontend/commit/dbc736782e39b5b920aa7fe52a2d3f2373c6c8c4))
- add page reseter ([238c4f2](https://github.com/Secure-T-Team/edu-frontend/commit/238c4f2439f676f3df64aa734dd86dbb8e28dcc0))
- add pagination for depertmanets list ([7348253](https://github.com/Secure-T-Team/edu-frontend/commit/7348253bf2b6faf8a5372c92600d0c537c04402e))
- add pagination space and card click ([9f97fe9](https://github.com/Secure-T-Team/edu-frontend/commit/9f97fe93981c16b737d7a6546cdc4d3f9bd38fb1))
- add question icon for phishing ([238bfb6](https://github.com/Secure-T-Team/edu-frontend/commit/238bfb692ad7ce38c7dfd3ef1248fc06f1868fd6))
- add role in employees filter ([803912c](https://github.com/Secure-T-Team/edu-frontend/commit/803912cd74d0a9ee17876cf010ff93463a8ba494))
- add saving for tags and RL ([283e8b3](https://github.com/Secure-T-Team/edu-frontend/commit/283e8b358d49f67d395b3b5eb3104850d943d76f))
- add scorm course into filter ([9d163a5](https://github.com/Secure-T-Team/edu-frontend/commit/9d163a5db05e7bb97f3248041253284753a33892))
- add scrollbar ([c22c36d](https://github.com/Secure-T-Team/edu-frontend/commit/c22c36d767aabb9efd5969906c2b1ffb3503f81b))
- add search ([dd7b4dd](https://github.com/Secure-T-Team/edu-frontend/commit/dd7b4dd65c7a0f6e2fb1d419e49958c8a25d33ed))
- add select filter and selecting active department ([5dfbc4c](https://github.com/Secure-T-Team/edu-frontend/commit/5dfbc4c71fd4b4276d4a62109ded91557dfc66e2))
- add selecting for modal and add loader ([2f6e236](https://github.com/Secure-T-Team/edu-frontend/commit/2f6e236b5d2302f368f550e5afef89058ee94f4d))
- add tabs and create files foe department list ([dd4e53c](https://github.com/Secure-T-Team/edu-frontend/commit/dd4e53cb33e461d963d6e5bb93254130e63e31ab))
- add title for employees page and correct stories ([99e7778](https://github.com/Secure-T-Team/edu-frontend/commit/99e77784601688ac14abdcf1b89666e5ecfbb3fa))
- add toggle for tags ([bd32d4c](https://github.com/Secure-T-Team/edu-frontend/commit/bd32d4cd26ab4b840f5159dca826487eca164ff7))
- add updating data ([f47248d](https://github.com/Secure-T-Team/edu-frontend/commit/f47248d9ced12ea9f6ad2738f1845bd5809fe983))
- add updating risklevel and lifeday ([15d8638](https://github.com/Secure-T-Team/edu-frontend/commit/15d8638e2944b999d8a9a1252ca1e4b4783d934c))
- added select all button ([a705a04](https://github.com/Secure-T-Team/edu-frontend/commit/a705a04594e2047c9c3f5bfd1dae71f4c1637415))
- change component name ([caee454](https://github.com/Secure-T-Team/edu-frontend/commit/caee454bf240df6afa30806cb2b9bd9a0068a06d))
- change dialog to modal component ([4e59f31](https://github.com/Secure-T-Team/edu-frontend/commit/4e59f315fe882ed2089a52f35fc131bcd3b6e81d))
- change dialog to modal component ([fc0c9b3](https://github.com/Secure-T-Team/edu-frontend/commit/fc0c9b3b341d1c6d7a43f9c83c10f2677c394ba6))
- change traget choose modal ([6d71668](https://github.com/Secure-T-Team/edu-frontend/commit/6d7166865760c29245a70b3a2603a9f0419d9e17))
- connect breadacrumb and change active dep ([48ba725](https://github.com/Secure-T-Team/edu-frontend/commit/48ba72594e2d9aed36e4a2d6f9bace8ea7adc87d))
- connect delete modal ([a0d0880](https://github.com/Secure-T-Team/edu-frontend/commit/a0d0880e64540e1285fe8a0d4e6c0374fdcbe23a))
- connect dep delete and refetch ([29a0715](https://github.com/Secure-T-Team/edu-frontend/commit/29a07155ec7d88e08ac22e8d10881b0de6a21c44))
- connect edit employee modal ([7e2b7d7](https://github.com/Secure-T-Team/edu-frontend/commit/7e2b7d70a84302a6d2dfc801f2a1adf727b5bb06))
- connect help icon for progress block ([88fd22f](https://github.com/Secure-T-Team/edu-frontend/commit/88fd22fc8a81e24dab5e186b4345eaff42673345))
- connect new tags for user list ([0c9e86d](https://github.com/Secure-T-Team/edu-frontend/commit/0c9e86d3514b8e896fb6984e2631e673b84335c7))
- connect old ad modal ([2014268](https://github.com/Secure-T-Team/edu-frontend/commit/2014268837a96778b62952f22de2beb3408df0ee))
- connect pagination for employees page ([1da7f39](https://github.com/Secure-T-Team/edu-frontend/commit/1da7f39afa3da5ab1f8549469832cf782e720115))
- connect tab selector for page ([994e88f](https://github.com/Secure-T-Team/edu-frontend/commit/994e88f9b023082c435b278fa923f18ff21121b9))
- correct breadcrumb ([92496d5](https://github.com/Secure-T-Team/edu-frontend/commit/92496d5f5968f1e826e22d740a990ef1690533aa))
- correct button styles ([2b9de57](https://github.com/Secure-T-Team/edu-frontend/commit/2b9de5769d0810c1638d1cc95c8886f41baaeb8e))
- correct checkbox and icon ([d0772f0](https://github.com/Secure-T-Team/edu-frontend/commit/d0772f015c4bb57acd0475d7e609239f42f984d6))
- correct creating ([afac1a2](https://github.com/Secure-T-Team/edu-frontend/commit/afac1a2be814c5b1a4f56eb4919db1c0daf109e5))
- correct fields ([821bb77](https://github.com/Secure-T-Team/edu-frontend/commit/821bb775a6288bee2407900dfdc68d98d2838dc1))
- correct husky ([749a439](https://github.com/Secure-T-Team/edu-frontend/commit/749a439693082f406cb6a7a1174a16bfb27097e9))
- correct input styles ([7fe7373](https://github.com/Secure-T-Team/edu-frontend/commit/7fe7373fb7a35811768c3860eb8962d525030be2))
- correct pagetitle styles ([00ae88a](https://github.com/Secure-T-Team/edu-frontend/commit/00ae88a7295fa827054d2e8362eae788e4b5dabf))
- correct pagination ([63e45c2](https://github.com/Secure-T-Team/edu-frontend/commit/63e45c215aa7d6cece875c2de02cd58855db048b))
- correct rounded button menu ([08359ea](https://github.com/Secure-T-Team/edu-frontend/commit/08359ea6dac17a8a839708186e0b51e287a17ac3))
- correct select and end employees filter ([2f062db](https://github.com/Secure-T-Team/edu-frontend/commit/2f062dbdee0e628c875afa88c034b9727e542aa9))
- correct styles and update filter ([9146317](https://github.com/Secure-T-Team/edu-frontend/commit/91463178dccfb4d02ed9b37f705fb5a93b2833b3))
- corrected turnoff on tags page ([e5db96a](https://github.com/Secure-T-Team/edu-frontend/commit/e5db96ab949bed537c9697dd1ac2f6bb8b776826))
- create add dep modal ([9b55f4c](https://github.com/Secure-T-Team/edu-frontend/commit/9b55f4c16f6cdca32bbb4f0ce4478daf23b30311))
- create add dep modal ([8c85da9](https://github.com/Secure-T-Team/edu-frontend/commit/8c85da9df9d27fd998b9e727e46f7c450dc45a45))
- create and connect context for employees ([068901a](https://github.com/Secure-T-Team/edu-frontend/commit/068901a3c1333b0e8b7fdf3a4136e93c99dc0964))
- create auto resize textarea ([73bcf8e](https://github.com/Secure-T-Team/edu-frontend/commit/73bcf8e53bb0d1d0fa5127f4804bbca1ce8e344a))
- create breadcrumb component ([f4b22e5](https://github.com/Secure-T-Team/edu-frontend/commit/f4b22e57bccab7282f22d527d9aa9b4fafa885b2))
- create course service ([25465b8](https://github.com/Secure-T-Team/edu-frontend/commit/25465b80af3e475692d2ad93e3cdacbd51022c95))
- create date picker ([43b35dc](https://github.com/Secure-T-Team/edu-frontend/commit/43b35dcf05b8cd06bd99c423ee70a8f6bc558756))
- create dep panel and list ([5f34df0](https://github.com/Secure-T-Team/edu-frontend/commit/5f34df062a4a5b760816983aa65467b8578f0d6c))
- create employees list ([487e900](https://github.com/Secure-T-Team/edu-frontend/commit/487e9008be51aaad880254a5df43a0fb3d963148))
- create employees panel component ([b5a4be5](https://github.com/Secure-T-Team/edu-frontend/commit/b5a4be511c2ffbb33c48ae37866b7cd39ad45837))
- create employees sort and connect ([2f9ec95](https://github.com/Secure-T-Team/edu-frontend/commit/2f9ec95703be60cd67292aa1c9ae9471fa036f8a))
- create files for employees list ([b433363](https://github.com/Secure-T-Team/edu-frontend/commit/b433363db58597d88872fb4edb9075775dc6c734))
- create files for import modal ([91ee7af](https://github.com/Secure-T-Team/edu-frontend/commit/91ee7afe5696efeba42549b5e619a07accfad2e4))
- create files for new page ([51175a5](https://github.com/Secure-T-Team/edu-frontend/commit/51175a5c3471acb72fcbd2121acb45fc89c83328))
- create files for switch component ([fee38d8](https://github.com/Secure-T-Team/edu-frontend/commit/fee38d89fb85273ac5620b6594ee2c4e4865695b))
- create help icon component ([0b8b187](https://github.com/Secure-T-Team/edu-frontend/commit/0b8b187bac0c80907560eedf13a2018e56d7add6))
- create import cofirm modal ([055f053](https://github.com/Secure-T-Team/edu-frontend/commit/055f0534d131a6d9eb963bf2339fbe0f78d368ac))
- create multi select component ([db6b9a2](https://github.com/Secure-T-Team/edu-frontend/commit/db6b9a21c1db94c1bb9a41368e6228b3606e0d99))
- create new context and update old and end employees page ([4da288e](https://github.com/Secure-T-Team/edu-frontend/commit/4da288ec9b88cefb708ddee7d3c005a60b87baf4))
- create new context for tags ([9cddd0a](https://github.com/Secure-T-Team/edu-frontend/commit/9cddd0addea559acb8cd62463330db477707d321))
- create new page and new service ([875c7fd](https://github.com/Secure-T-Team/edu-frontend/commit/875c7fd43dd61fdf580577c2fe6020da800f82d8))
- create page component ([e0d5bae](https://github.com/Secure-T-Team/edu-frontend/commit/e0d5baeb9845dd1e8ae6ca323f2aeca253be2d24))
- create page files and connect ([0f21d08](https://github.com/Secure-T-Team/edu-frontend/commit/0f21d08a10aa43e060caba8f19cc4a35352b1ecd))
- create pagination component ([7b93677](https://github.com/Secure-T-Team/edu-frontend/commit/7b93677e8b54045a05fb5c3051f542d1549d4d55))
- create phishing service ([97a99a3](https://github.com/Secure-T-Team/edu-frontend/commit/97a99a348188630f377543eb053d2c1222f6bd3e))
- create radiobox component ([8a14d95](https://github.com/Secure-T-Team/edu-frontend/commit/8a14d9577649d4eca63bf30100d757ef48e7b760))
- create search input ([8221867](https://github.com/Secure-T-Team/edu-frontend/commit/82218670bb164b5b17eb85d68727e8386dec2546))
- create select date card ([781daf3](https://github.com/Secure-T-Team/edu-frontend/commit/781daf334a8d5c90189c1741b75ae4748eebe533))
- create select target card ([8e4710a](https://github.com/Secure-T-Team/edu-frontend/commit/8e4710a68589f39a23a6b1c1d7046641c283a680))
- create tab selector ([19717c0](https://github.com/Secure-T-Team/edu-frontend/commit/19717c09b7043313ae2011f1ea6d159abae1f764))
- create tag component ([96f2989](https://github.com/Secure-T-Team/edu-frontend/commit/96f298949ad5da47527dacb1a10b01fc16a0dbf7))
- create tags list module ([37fcf2a](https://github.com/Secure-T-Team/edu-frontend/commit/37fcf2a7aa4d68815adfef33bfc459a35b46b883))
- create template choose modal and template choose card ([3c0c613](https://github.com/Secure-T-Team/edu-frontend/commit/3c0c6137243e1781bb44f3950d12a61c06149c0c))
- create template logo component ([9a3a432](https://github.com/Secure-T-Team/edu-frontend/commit/9a3a4320026ca672cd28784df5c7ae8c4f3b75ee))
- delete extra code ([aae414f](https://github.com/Secure-T-Team/edu-frontend/commit/aae414f0f6cf024f994fef87f0f401c671757588))
- delete extra tests and install jest ([52b4504](https://github.com/Secure-T-Team/edu-frontend/commit/52b4504bbb4619bab65095a9db081f3f8e812b0e))
- delete extra var ([46edd6a](https://github.com/Secure-T-Team/edu-frontend/commit/46edd6a164bb44314289ceb6a553a6461ee2b6a8))
- delete old license page and add new ([8f03c59](https://github.com/Secure-T-Team/edu-frontend/commit/8f03c5902a1fd3fd8bd158ea826abad3959a846c))
- end employee card ([b10d9b2](https://github.com/Secure-T-Team/edu-frontend/commit/b10d9b2b8172302f4f933b7e63e9326939939246))
- end modal ([ce1f302](https://github.com/Secure-T-Team/edu-frontend/commit/ce1f3025cbf216c4f4dcdb55a6ea2ec24a9da717))
- end page and delete old code ([2b46437](https://github.com/Secure-T-Team/edu-frontend/commit/2b464373334956691a6bdebe680f4ff499e2acfa))
- fetch users with search ([9707bb4](https://github.com/Secure-T-Team/edu-frontend/commit/9707bb4073ab6a179bd85c8d2cf979eb379f95e1))
- hide button if has search ([78b6244](https://github.com/Secure-T-Team/edu-frontend/commit/78b6244124e800b370b0e3544c8384a358966fe2))
- hide inputs with 0 questions ([d2dc3bb](https://github.com/Secure-T-Team/edu-frontend/commit/d2dc3bb5331746efb1c3b747555b024f54afafb5))
- install jest and update package json ([e308aad](https://github.com/Secure-T-Team/edu-frontend/commit/e308aad4a6a8bd783d269a795f0c2e5026fec103))
- moved folders ([a0ca879](https://github.com/Secure-T-Team/edu-frontend/commit/a0ca879c8704de79cdc5df5e2cfca538b8d28300))
- rebuild filter modal and connect phishing events ([419534f](https://github.com/Secure-T-Team/edu-frontend/commit/419534f9ebb0e1995e14e6e9f9b2fd18a40fa109))
- reset filter with closing ([15dae6a](https://github.com/Secure-T-Team/edu-frontend/commit/15dae6a8a715df15c2cb6d014febaeebe68c69c6))
- save ([cbd72f4](https://github.com/Secure-T-Team/edu-frontend/commit/cbd72f4f5d7c1e248b989eadcc85d5fdd36bf2f6))
- set default value for course period ([6ee1f2b](https://github.com/Secure-T-Team/edu-frontend/commit/6ee1f2bc2b39e0c964ea79de14659ff92afbf5cf))
- start add actions ([b2815f6](https://github.com/Secure-T-Team/edu-frontend/commit/b2815f6937874a7339e7399d9b63953d29b51dc5))
- start assign course modal ([9f5d368](https://github.com/Secure-T-Team/edu-frontend/commit/9f5d3682f184b56d6ccbac67161cc4b98fe14b42))
- start creating template select modal ([23dcc6f](https://github.com/Secure-T-Team/edu-frontend/commit/23dcc6f3e8f4f7f55e411ebf2d38ccd59c5ea9f3))
- start fix assign course ([5ae37b6](https://github.com/Secure-T-Team/edu-frontend/commit/5ae37b606563e7b8831026fc88cbecf86f59701b))
- start target modal ([da47575](https://github.com/Secure-T-Team/edu-frontend/commit/da47575eda955c98b6a19fd8b2490e2f5cf2a134))
- update assign course modal ([9cc1efd](https://github.com/Secure-T-Team/edu-frontend/commit/9cc1efdecbd811c45299453118395940cdd713a6))
- update button name ([3cccd19](https://github.com/Secure-T-Team/edu-frontend/commit/3cccd19e94d53840d2b3e7a1034524c085b3d6fd))
- update button styles ([ee7fb98](https://github.com/Secure-T-Team/edu-frontend/commit/ee7fb987693a2d51483d137f440d560954ad49f5))
- update counter title and delete extra code ([1f1aa99](https://github.com/Secure-T-Team/edu-frontend/commit/1f1aa998068e86fc38c75730b2bee33c4d150a28))
- update date helpers ([2be4e99](https://github.com/Secure-T-Team/edu-frontend/commit/2be4e99f9e5329dcf283d0e3f24e9429eff8d93c))
- update date picker ([b956527](https://github.com/Secure-T-Team/edu-frontend/commit/b9565271db1ad7caff7d25f33be60de329f285ea))
- update date picker locale ([bb66762](https://github.com/Secure-T-Team/edu-frontend/commit/bb667627486b11961047a400fcaf3122905ef33c))
- update date picker locale ([23114c2](https://github.com/Secure-T-Team/edu-frontend/commit/23114c2b6b604f8ec12f21b3bbd34a81fae5e0ff))
- update default department ([42a5bc7](https://github.com/Secure-T-Team/edu-frontend/commit/42a5bc7f7a8da1e4b6ba639b3e25a7d182e322d2))
- update department cards ([f149509](https://github.com/Secure-T-Team/edu-frontend/commit/f149509aeb45dcc1df17ea35684e0d35977bda15))
- update employee card ([9774a14](https://github.com/Secure-T-Team/edu-frontend/commit/9774a14e14fec22c16e3a6870851f9793ba2989a))
- update employees filter ([0e8f838](https://github.com/Secure-T-Team/edu-frontend/commit/0e8f8381128058db5bda61af3d57f93114519197))
- update employees filter and connect multi departments ([ac35518](https://github.com/Secure-T-Team/edu-frontend/commit/ac35518f78dc9c8244613ab731af2a9f8974a1d7))
- update filter ([2abfa88](https://github.com/Secure-T-Team/edu-frontend/commit/2abfa882201f81ddee7277734c1726633b9b4f1b))
- update filter ([037d887](https://github.com/Secure-T-Team/edu-frontend/commit/037d887715f79ca0e10b5fa6a59e88842b21e536))
- update filter ([739a8b1](https://github.com/Secure-T-Team/edu-frontend/commit/739a8b10465d588bdacf03772c7d5988a893ef31))
- update filter ([ea17c0d](https://github.com/Secure-T-Team/edu-frontend/commit/ea17c0d2fb5707c74ddc0951d4f40817c81bc8af))
- update filter ([55aea2c](https://github.com/Secure-T-Team/edu-frontend/commit/55aea2c8dd90873d482d24236b02a73c14eee13b))
- update get target function ([456c24a](https://github.com/Secure-T-Team/edu-frontend/commit/456c24a1fcd0d404b7e66c64c37eb911539e1d2b))
- update helpers ([71ef84d](https://github.com/Secure-T-Team/edu-frontend/commit/71ef84d679e6b8b62bf48c1974375525e1552396))
- update hygen template ([123f6cf](https://github.com/Secure-T-Team/edu-frontend/commit/123f6cfa63bac0b3e27d094596719225dc497343))
- update hygen template ([6b63567](https://github.com/Secure-T-Team/edu-frontend/commit/6b635675a984701b1d830840acb807a4817687d0))
- update import employees modal ([4bc2607](https://github.com/Secure-T-Team/edu-frontend/commit/4bc2607db2881c3be0ff9da3217f25c965f5980e))
- update import employees modal ([fa8a439](https://github.com/Secure-T-Team/edu-frontend/commit/fa8a439f01582b60b8cc8ea05cb6c7aa1a0225f2))
- update import employees modal ([1946e85](https://github.com/Secure-T-Team/edu-frontend/commit/1946e857a5fb72502e219e352b382870256d2f10))
- update isDisabled checker and delete mocs ([cdc6175](https://github.com/Secure-T-Team/edu-frontend/commit/cdc6175ef1657b814c8980a8ddab1e4c6cfeb111))
- update localstorage ([3d69e73](https://github.com/Secure-T-Team/edu-frontend/commit/3d69e7377088599a098a174b4cafcd1fe8619390))
- update loki ([49d97d2](https://github.com/Secure-T-Team/edu-frontend/commit/49d97d29c977080c293140c47ee480283cd7043c))
- update modal ([1f92f75](https://github.com/Secure-T-Team/edu-frontend/commit/1f92f75ad72bb810b2ab2c0a5c78124941ee8b5e))
- update multi select ([21d2f22](https://github.com/Secure-T-Team/edu-frontend/commit/21d2f22a8238bfae3a66eaeaafd00b2bdbc20104))
- update page ([f6d0c1a](https://github.com/Secure-T-Team/edu-frontend/commit/f6d0c1a090b4ca27e55ecf1c47e150c6e6a8faf3))
- update phish value ([3ec825b](https://github.com/Secure-T-Team/edu-frontend/commit/3ec825bf04ac0151354d4b4dae365d21e827e5d3))
- update phishing styles ([eef1518](https://github.com/Secure-T-Team/edu-frontend/commit/eef1518cc389a7fdad281ee55a10a37dc01f4679))
- update query params ([68b6231](https://github.com/Secure-T-Team/edu-frontend/commit/68b6231d13145f06843cb5c29383beb03b7a213b))
- update query params ([7745e81](https://github.com/Secure-T-Team/edu-frontend/commit/7745e81c04ec4b8c97a31222e8dac723ad128cc2))
- update rounded button and connect add employees ([fb0f019](https://github.com/Secure-T-Team/edu-frontend/commit/fb0f019ef0219464578fb9977353000dd5d9a3bc))
- update sorting for filter ([77284a4](https://github.com/Secure-T-Team/edu-frontend/commit/77284a4d0366f878c9a97a5de54ec92193f047e9))
- update styles ([3663fdb](https://github.com/Secure-T-Team/edu-frontend/commit/3663fdb8d3b3a849978aadaab3a3e73d7d7521af))
- update styles ([0e0c18a](https://github.com/Secure-T-Team/edu-frontend/commit/0e0c18a8d734132837da03c58aedb5752890c82c))
- update styles ([3677d0c](https://github.com/Secure-T-Team/edu-frontend/commit/3677d0c565f418a2fa9a96e6241a8569687a1424))
- update styles ([11c4008](https://github.com/Secure-T-Team/edu-frontend/commit/11c40086d36a44d2a79b7ec5ee1937d22318862f))
- update styles ([a0c7aef](https://github.com/Secure-T-Team/edu-frontend/commit/a0c7aef38ef259978fb9430d3aa8737f3c24aae4))
- update styles for employee and department cards ([e5a3ee6](https://github.com/Secure-T-Team/edu-frontend/commit/e5a3ee620aae02b9eb2eadca30c9017cdd200dd1))
- update styles for employees page ([f5a6000](https://github.com/Secure-T-Team/edu-frontend/commit/f5a600021d3df8abf86db9481e18721e28b31ab2))
- update styles for employees page ([ecbac89](https://github.com/Secure-T-Team/edu-frontend/commit/ecbac896818b6630e120b89caccd56a5bab9fb5c))
- update styles for employees page ([09e9f33](https://github.com/Secure-T-Team/edu-frontend/commit/09e9f33a0709a21247adfda89e16a6bf5d6feec7))
- update styles for employees page icons ([4fc9672](https://github.com/Secure-T-Team/edu-frontend/commit/4fc9672b65f1da685558aacd27b030576dedfd1d))
- update styles for tags page ([11b1ced](https://github.com/Secure-T-Team/edu-frontend/commit/11b1ced4766161f76ea9ee90f9ce03ad0bc00fba))
- update target choose modal ([69796f2](https://github.com/Secure-T-Team/edu-frontend/commit/69796f2dac4370be14acd38aa4a49577ab1b220f))
- update target counter ([1b546d5](https://github.com/Secure-T-Team/edu-frontend/commit/1b546d588dc3d7022019e9f43e5a80b72c8e9d9b))
- update target course modal ([6a09cde](https://github.com/Secure-T-Team/edu-frontend/commit/6a09cded8b13d1d93b8e44e611af11d12ca76262))
- update target modal ([82732c0](https://github.com/Secure-T-Team/edu-frontend/commit/82732c09736ba1040e521b71ec70c1be05503d9e))
- update target modal ([22c80a2](https://github.com/Secure-T-Team/edu-frontend/commit/22c80a23f8231867c9ee3c83c4c80b64030115d9))
- update template file ([a8feb54](https://github.com/Secure-T-Team/edu-frontend/commit/a8feb540efd2a8ede384df5c96eb9c977693f8c9))
- update theme styles ([78bad0c](https://github.com/Secure-T-Team/edu-frontend/commit/78bad0c4ad14a4b8cb3315290ab4b26fee5e5519))
- update validation for create phishing campaigns ([3d833e3](https://github.com/Secure-T-Team/edu-frontend/commit/3d833e3407daf33d160be399cd4a84819f579282))
- Добавлен timestamp ([#632](https://github.com/Secure-T-Team/edu-frontend/issues/632)) ([eeaf8ae](https://github.com/Secure-T-Team/edu-frontend/commit/eeaf8ae18ac27719cfbfda08ff2d3eb35a6bfc6b))
- Добавлено тестирование курса ([a976299](https://github.com/Secure-T-Team/edu-frontend/commit/a976299d7b83d071c2c1b51ac48b5162f5dd4841))
- Добавлены SCORM курсы ([b8c9a3b](https://github.com/Secure-T-Team/edu-frontend/commit/b8c9a3b22c8019d97cfdee86936104a2e4e41f3e))
- Доработано назначение курса ([ac72394](https://github.com/Secure-T-Team/edu-frontend/commit/ac723941ec9d483343e98cacb36cde6867ef98ca))
- Доработано назначение курса ([aff9ecb](https://github.com/Secure-T-Team/edu-frontend/commit/aff9ecb2c8acd65d03e7821e312b3da73230376f))
- Доработано прохождение ([fff9100](https://github.com/Secure-T-Team/edu-frontend/commit/fff9100a5577358c0c8934a1817bf9a6bbd15dcb))
- Доработано прохождение ([4608f69](https://github.com/Secure-T-Team/edu-frontend/commit/4608f694d70fc4747652a47b95c3f564629116be))
- Доработано прохождение ([a9f228e](https://github.com/Secure-T-Team/edu-frontend/commit/a9f228e29bc071fe232e1b8fa0a4728ce3b2edc9))
- Доработано тестирование курса ([aec68c8](https://github.com/Secure-T-Team/edu-frontend/commit/aec68c8b2639026e5d7fd4c115f89e048cd016f1))

### Bug Fixes

- chagne page component ([b0fdfda](https://github.com/Secure-T-Team/edu-frontend/commit/b0fdfdafd03d3f150bdbfddcf0112fc6726dfed0))
- delete extra memo ([e297d70](https://github.com/Secure-T-Team/edu-frontend/commit/e297d705df5f5aa039aa3c71532d17cd81db43d5))
- delete set zero page after employee edit ([dcca219](https://github.com/Secure-T-Team/edu-frontend/commit/dcca2192b9fe99664f3e29e39193741849b8c888))
- delete set zero page after employee edit ([b8c6124](https://github.com/Secure-T-Team/edu-frontend/commit/b8c61243e637a8896ec798ee199d8a232cb8b290))
- fix all targets ([5c31969](https://github.com/Secure-T-Team/edu-frontend/commit/5c3196916075c6c54497fbd9e819b927261fa240))
- fix button name ([3b8e592](https://github.com/Secure-T-Team/edu-frontend/commit/3b8e592933ff5db10b5bdd9e02030a6143d5eacc))
- fix confirm import modal ([3eae13a](https://github.com/Secure-T-Team/edu-frontend/commit/3eae13a655a32373d4741896c817f836479a3caf))
- fix confirm import modal ([ed47faa](https://github.com/Secure-T-Team/edu-frontend/commit/ed47faaa6a9a25c5d3caf1573e8cccabb63a6c8c))
- fix confirm import modal ([843d9b4](https://github.com/Secure-T-Team/edu-frontend/commit/843d9b49ca20284a91e5b690623195757d48f876))
- fix confirm import modal ([b2e958e](https://github.com/Secure-T-Team/edu-frontend/commit/b2e958e9130f759c12e6f34372617a81be5315fb))
- fix create quiz ([b006760](https://github.com/Secure-T-Team/edu-frontend/commit/b00676069496930e4d149b4fe006101c119e4e21))
- fix creating with course ([d510d90](https://github.com/Secure-T-Team/edu-frontend/commit/d510d9089b13dea9fdcbf361517a22f1427c87b1))
- fix department list ([34973c8](https://github.com/Secure-T-Team/edu-frontend/commit/34973c839618cc9195ae3adcaee3800e87446e7b))
- fix employees cards ([bb6ff43](https://github.com/Secure-T-Team/edu-frontend/commit/bb6ff43254d845a5e9593fabedc6a38abb94be5e))
- fix employees course page again ([03a70c5](https://github.com/Secure-T-Team/edu-frontend/commit/03a70c52204346918c0d464ca25a8891c2b2fb87))
- fix employees filter ([4d67f99](https://github.com/Secure-T-Team/edu-frontend/commit/4d67f99deab0462721516c5ff11c02a55a02fcf3))
- fix empty targets ([781c1bf](https://github.com/Secure-T-Team/edu-frontend/commit/781c1bf051b80a858f909a9674839c98c691238e))
- fix empty text for target modal ([d188cbb](https://github.com/Secure-T-Team/edu-frontend/commit/d188cbb6fbcd2e7ca486a588f5b74c1601547e82))
- fix extra selecting ([5983fa1](https://github.com/Secure-T-Team/edu-frontend/commit/5983fa1cf331b30b6301a40ae129c74575b8baf7))
- fix import modal ([b6c830e](https://github.com/Secure-T-Team/edu-frontend/commit/b6c830efa4dda0bceea96829c4db5f6e25cd0e2e))
- fix modal styles ([04fa6be](https://github.com/Secure-T-Team/edu-frontend/commit/04fa6be863a3f0922e7954394f12a2a81c72e2fd))
- fix phishing value ([6618993](https://github.com/Secure-T-Team/edu-frontend/commit/66189934a81317937c24b7e17a5472ed71a5a000))
- fix request ([0c4e73f](https://github.com/Secure-T-Team/edu-frontend/commit/0c4e73fe5c8a20f2466017f6730bcc388ad44e85))
- fix request ([fc5df5f](https://github.com/Secure-T-Team/edu-frontend/commit/fc5df5f415cf5458fee1e87fcd6301a7831be8ae))
- fix Scorm ([#641](https://github.com/Secure-T-Team/edu-frontend/issues/641)) ([da84051](https://github.com/Secure-T-Team/edu-frontend/commit/da8405109db3cb01b01aedd1a58253abeed33671))
- fix Scorm ([#642](https://github.com/Secure-T-Team/edu-frontend/issues/642)) ([302d74e](https://github.com/Secure-T-Team/edu-frontend/commit/302d74eb596adb4ff60c07470c7958884e86121a))
- fix scorm course patch body ([e7bde82](https://github.com/Secure-T-Team/edu-frontend/commit/e7bde8282020a2f17f3476f414a048afcbd9c86c))
- fix scorm course update url ([17a4f2c](https://github.com/Secure-T-Team/edu-frontend/commit/17a4f2c1d38d8206797e29821e30d218c2c0fc5e))
- fix selected course card ([dd30342](https://github.com/Secure-T-Team/edu-frontend/commit/dd3034220f83bb97cd34426a4f4ce89d6719d741))
- fix styles ([5cf8135](https://github.com/Secure-T-Team/edu-frontend/commit/5cf8135fda019ce3d58b840dfd340f9f680dafc3))
- fix styles ([edc3bbf](https://github.com/Secure-T-Team/edu-frontend/commit/edc3bbf89ca4b5ab921071fa71b4c4c5c78e3628))
- fix title ([862e121](https://github.com/Secure-T-Team/edu-frontend/commit/862e121ac9f139b2a903f574cbb57143ca64df63))
- **scorm:** fix bugs ([#650](https://github.com/Secure-T-Team/edu-frontend/issues/650)) ([05b1866](https://github.com/Secure-T-Team/edu-frontend/commit/05b18663b979140fe3dfffa8343e94bb665d7782))
- **scorm:** fix scorm ([bc67d88](https://github.com/Secure-T-Team/edu-frontend/commit/bc67d884748d034aed19611307c265196eca171b))

### Refactoring

- correct fields ([f813cc8](https://github.com/Secure-T-Team/edu-frontend/commit/f813cc89332e05ff984296c5457daf010d8a5e69))
- correct import ([fac725e](https://github.com/Secure-T-Team/edu-frontend/commit/fac725ed73fcccb20ade831a00af18bd8a08710e))
- correct styles ([cd57455](https://github.com/Secure-T-Team/edu-frontend/commit/cd57455186c07394c79e08624d81cc7630c8d4c2))
- correct value ([763c0fd](https://github.com/Secure-T-Team/edu-frontend/commit/763c0fdda4df79fab07fbd87239064f2d655e869))
- corrected checked ([6df1300](https://github.com/Secure-T-Team/edu-frontend/commit/6df1300e05230c38c3d37d7f437cd8cad7d428f6))
- corrected checked ([323b8de](https://github.com/Secure-T-Team/edu-frontend/commit/323b8dec23be465f71f07c55947ba49e7e1cd520))
- delete counter title ([030e1ae](https://github.com/Secure-T-Team/edu-frontend/commit/030e1ae6d24a144b8785a8c4859e77b39282e9da))
- delete extra slices ([0dcb687](https://github.com/Secure-T-Team/edu-frontend/commit/0dcb687f73b3118636ad241f27442adb03dcb37e))
- delete mocs ([bc5d0e3](https://github.com/Secure-T-Team/edu-frontend/commit/bc5d0e3a6d6b21eb5b203fc4f7b06eaf614715a7))
- delete mocs ([040572e](https://github.com/Secure-T-Team/edu-frontend/commit/040572e341bba35337673f7dbbddf8338c48765c))
- delete old employees components ([4f740a4](https://github.com/Secure-T-Team/edu-frontend/commit/4f740a43aad6bedd837751b8bf453003f43ff2f4))
- delete screenshot test into pre commit ([e7ea085](https://github.com/Secure-T-Team/edu-frontend/commit/e7ea085ae88714e0324e3399d60b8bd59053a3a9))
- hide extra code ([3e99bae](https://github.com/Secure-T-Team/edu-frontend/commit/3e99baed62ffd3b045091f932217086820709acf))
- refactor styles ([5fcd550](https://github.com/Secure-T-Team/edu-frontend/commit/5fcd550cb95a484fd9755109987509c6827df83d))

### [2.42.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.42.0...v2.42.1) (2023-08-03)

### Bug Fixes

- fix tips ([09a2bb6](https://github.com/Secure-T-Team/edu-frontend/commit/09a2bb6145bea3d580b871b47b55779cdb611d0e))

### Refactoring

- delete extra tips ([b308b41](https://github.com/Secure-T-Team/edu-frontend/commit/b308b416660c2be87617f24752384d0c3178db83))

## [2.42.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.41.0...v2.42.0) (2023-07-12)

### Features

- update passing and total checker ([cbfcfdf](https://github.com/Secure-T-Team/edu-frontend/commit/cbfcfdfa24bdd2f41ea77ec43e18be929b5f14f2))

## [2.41.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.40.0...v2.41.0) (2023-07-10)

### Features

- add message text for pptx event ([38ffc49](https://github.com/Secure-T-Team/edu-frontend/commit/38ffc4933b80c85a52c8f889e392f447af55ee26))
- add min and max for input ([924b79e](https://github.com/Secure-T-Team/edu-frontend/commit/924b79e4cd7152b8c3c853df01ef4a2a06723e58))
- add passing checker ([99a07b8](https://github.com/Secure-T-Team/edu-frontend/commit/99a07b83a6b428f22ebb71ba8eab25c3514ecca2))
- update name checker and disable sb checker ([36551fb](https://github.com/Secure-T-Team/edu-frontend/commit/36551fbf5bf2e64ee1b2ec53fc43cee1b15a5264))
- update passing checker ([d04cb9b](https://github.com/Secure-T-Team/edu-frontend/commit/d04cb9b8a92f55f4198ee00059f84b18efec41dc))

## [2.40.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.39.0...v2.40.0) (2023-07-05)

### Features

- update styles for template cards ([dfb957b](https://github.com/Secure-T-Team/edu-frontend/commit/dfb957b0378472556a95e35fd30124dbc40c9ef7))

## [2.39.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.38.1...v2.39.0) (2023-07-03)

### Features

- add date checker ([a7ef81b](https://github.com/Secure-T-Team/edu-frontend/commit/a7ef81b6daac058dcfcd5326d8fa75e0523a055a))
- add loki references ([e249b00](https://github.com/Secure-T-Team/edu-frontend/commit/e249b00e9e0c6028c1dadb9575b2075ecac36a5f))
- add tests into husky ([07e1569](https://github.com/Secure-T-Team/edu-frontend/commit/07e1569229214328bc955e5e7fec6a7d6ef5a2f7))
- install loki ([e365dd3](https://github.com/Secure-T-Team/edu-frontend/commit/e365dd332943cff3438502a4d89596767430b5c3))
- update checker ([411fbe8](https://github.com/Secure-T-Team/edu-frontend/commit/411fbe832eaad2c8fb733533d1fdb22d0658500a))
- update package json ([83ddde1](https://github.com/Secure-T-Team/edu-frontend/commit/83ddde14f9f54c7ba4987e5a137d0d2f1200e002))

### Bug Fixes

- fix styles ([6218f9a](https://github.com/Secure-T-Team/edu-frontend/commit/6218f9a5d097b8bfc42aba5c3626999559f499e3))

### [2.38.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.38.0...v2.38.1) (2023-07-03)

### Bug Fixes

- fix styles ([88575fb](https://github.com/Secure-T-Team/edu-frontend/commit/88575fbb824eea43d0a2ea16e21efbca4c77b156))

## [2.38.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.37.0...v2.38.0) (2023-07-03)

### Features

- add adaptive ([73d4b37](https://github.com/Secure-T-Team/edu-frontend/commit/73d4b37c642599f6729844b34528c43d18d9b542))
- add close button for modal ([a03f7d9](https://github.com/Secure-T-Team/edu-frontend/commit/a03f7d9ee9fd95586a1211f5b4c6e95a57fe337c))
- add new var in drone env ([a228574](https://github.com/Secure-T-Team/edu-frontend/commit/a228574e9a49e12ba5b257d6e0c5a961d3b5b91b))
- add no completed course modal ([702cfc8](https://github.com/Secure-T-Team/edu-frontend/commit/702cfc8cb179a7a01b326ee4cf64903e6e8fa99a))
- add query for backbutton and add query for my course page ([e210713](https://github.com/Secure-T-Team/edu-frontend/commit/e210713dfd22f00e3405bfa305e9866f21d61e5e))
- add refetch ([040c297](https://github.com/Secure-T-Team/edu-frontend/commit/040c297fbfe8602c7fb4179a341b62b566c174d0))
- add refetch ([6ddbe70](https://github.com/Secure-T-Team/edu-frontend/commit/6ddbe703ca192cd512688b26aee52ac1db4a78d1))
- add report modal for certificate ([ea13643](https://github.com/Secure-T-Team/edu-frontend/commit/ea13643f59fa5144de321c76f984f4ec8782cff7))
- add url checker ([1097ef9](https://github.com/Secure-T-Team/edu-frontend/commit/1097ef961a38e825bc37354750a07be3eed18c60))
- add wrong answer for results ([ae17c8f](https://github.com/Secure-T-Team/edu-frontend/commit/ae17c8f916d9421a0007b1afe3ca01d08d9bcfd3))
- centered button ([06bc2fe](https://github.com/Secure-T-Team/edu-frontend/commit/06bc2fee1f8aee653af91b1e31a3bc93ec37222e))
- change button card color ([98aec61](https://github.com/Secure-T-Team/edu-frontend/commit/98aec616d906da5e6033f0f789603e20d036ac8a))
- change color for loader ([68f2fa0](https://github.com/Secure-T-Team/edu-frontend/commit/68f2fa0ada6066cd55740da72471e3133eeb7da8))
- change color for textarea ([ede3917](https://github.com/Secure-T-Team/edu-frontend/commit/ede391760692714cd0fc22c7a56b9b24670e12ba))
- change domains request ([7f57fe1](https://github.com/Secure-T-Team/edu-frontend/commit/7f57fe1723316be27a09b47a33af995e19d5d37f))
- change font styles for page title ([1e20e63](https://github.com/Secure-T-Team/edu-frontend/commit/1e20e633618a4cb016f5d5ab9326790addcb686a))
- change icon color ([7d75bda](https://github.com/Secure-T-Team/edu-frontend/commit/7d75bda5b8ce7a8aff78c28bfafa2f4bfb6650b6))
- change license color ([95dd2e8](https://github.com/Secure-T-Team/edu-frontend/commit/95dd2e85a6f560855860105949639fbc14a0523a))
- change org button color ([35e4b9d](https://github.com/Secure-T-Team/edu-frontend/commit/35e4b9da4c75a0bdeb0582cb8afee2fc047a5142))
- connect request ([55f725f](https://github.com/Secure-T-Team/edu-frontend/commit/55f725fa6813d7e2f340b650ebdc599a79870fdd))
- create certificate model ([2bf661d](https://github.com/Secure-T-Team/edu-frontend/commit/2bf661dac4fe313c5549037445360ff5cc07b49a))
- create course card ([ea0deef](https://github.com/Secure-T-Team/edu-frontend/commit/ea0deef7fb0751a0eb562ec16ed3be3096bad4d4))
- create event modal component and connect ([68ef759](https://github.com/Secure-T-Team/edu-frontend/commit/68ef759c7f024468947e453d283b09f450d5a0c8))
- create new page and ddelte old ([3ca7233](https://github.com/Secure-T-Team/edu-frontend/commit/3ca723360167f7c027cd05b8f40a016dda686f8f))
- create notification module ([270caac](https://github.com/Secure-T-Team/edu-frontend/commit/270caacc2af972390317ef97e18c6889f8f9af31))
- create tabs component ([ede6d3f](https://github.com/Secure-T-Team/edu-frontend/commit/ede6d3f3d59682e97b7fcdcde5076703f0f15937))
- delete button in certificate modal ([a9b18f1](https://github.com/Secure-T-Team/edu-frontend/commit/a9b18f146bac7da56b280d87af045404caedbb04))
- hardcode link ([3d16e57](https://github.com/Secure-T-Team/edu-frontend/commit/3d16e579bc558ac29c9c1b69ee3d9e4507e25821))
- hide extra element ([5ec4072](https://github.com/Secure-T-Team/edu-frontend/commit/5ec40724ad363e776e2d10f68146657bfb36424a))
- update active tab function ([f098e74](https://github.com/Secure-T-Team/edu-frontend/commit/f098e7451e4887f5e2a2b6908d82abf51980e43d))
- update loader icon and rounded icon ([845821e](https://github.com/Secure-T-Team/edu-frontend/commit/845821ed3efd1a1c87b85d8906974df34587bb50))
- update loader styles ([0cc8581](https://github.com/Secure-T-Team/edu-frontend/commit/0cc85813efe0b4730abc97a054a93b36e5639dad))
- update styles ([1f46e63](https://github.com/Secure-T-Team/edu-frontend/commit/1f46e6365d860ea9e39d44abf54d91f3f6fb2ac1))
- update styles ([6fb9d83](https://github.com/Secure-T-Team/edu-frontend/commit/6fb9d83b7de61b268736cae5a35b4d6ad46bf86a))
- update text color ([3ce5005](https://github.com/Secure-T-Team/edu-frontend/commit/3ce5005b5e0e89f758b81e160dcf3e094dc79f8f))
- update text for report modal ([75860eb](https://github.com/Secure-T-Team/edu-frontend/commit/75860eb055654fc7b9228cccf55bd26cb6593f9e))

### Bug Fixes

- delete extra code ([71a5867](https://github.com/Secure-T-Team/edu-frontend/commit/71a5867a2365e04f369f64f3f0c979b1011e0199))
- fix certificate title ([e5ea004](https://github.com/Secure-T-Team/edu-frontend/commit/e5ea0046aa3019c6eaf86dea772c52e26107b225))
- fix certificates modal ([cd246dd](https://github.com/Secure-T-Team/edu-frontend/commit/cd246dd1f4acf7c8833b15cec5899363a223c536))
- fix course card image ([15b279b](https://github.com/Secure-T-Team/edu-frontend/commit/15b279b6c75cd3962606079ec90ae2c1e9deca36))
- fix letter ([5937d1d](https://github.com/Secure-T-Team/edu-frontend/commit/5937d1db7ef549f487caccb9e7c9374a248720da))
- fix list ([4554f2d](https://github.com/Secure-T-Team/edu-frontend/commit/4554f2d491f5c7d420db15b561b9de7ec2bbad7c))
- fix logo for ELDAR ([0f45a35](https://github.com/Secure-T-Team/edu-frontend/commit/0f45a350588db5235b011b0e1a32126798859c5c))
- fix progress on course card ([4699caf](https://github.com/Secure-T-Team/edu-frontend/commit/4699caf1530120d42b766c845dced8a21d33289a))
- fix report modal for cert ([e18e980](https://github.com/Secure-T-Team/edu-frontend/commit/e18e980bbd54065dea8505bdadb7f6793545931e))
- fix report modal for cert ([6a051a0](https://github.com/Secure-T-Team/edu-frontend/commit/6a051a0d1cf0e5c34118a9c06fd2804a74223838))
- fix styles ([1b10f11](https://github.com/Secure-T-Team/edu-frontend/commit/1b10f11f7c3c3420c78c8e7406898d8f50d847d5))
- fix styles ([1917da3](https://github.com/Secure-T-Team/edu-frontend/commit/1917da3ee6880711de73de00aa3c90b28cfac068))
- fix url ([6464c72](https://github.com/Secure-T-Team/edu-frontend/commit/6464c72f73925839a21ccf016746c0b41f298764))
- fix url path ([84fdb6c](https://github.com/Secure-T-Team/edu-frontend/commit/84fdb6c3d2e3bddc2ffc4386c9c2a798837067de))

### Refactoring

- change class ([4a22db7](https://github.com/Secure-T-Team/edu-frontend/commit/4a22db7085413c852511e60a63677f60b9015e78))
- change var name ([de0f178](https://github.com/Secure-T-Team/edu-frontend/commit/de0f178bde86a19cac81f71f738d26d79a1160e1))
- correct url ([d21e5a8](https://github.com/Secure-T-Team/edu-frontend/commit/d21e5a8cc28b2478fe4640e6bc4acd1c1527eea1))
- delete extra code ([6b35519](https://github.com/Secure-T-Team/edu-frontend/commit/6b35519cfb4b01f3acd24cebe001ad5ad196515a))
- delete extra code n2 ([fb659a0](https://github.com/Secure-T-Team/edu-frontend/commit/fb659a057090a19823d9f6de5f42a67a2b65fd05))
- delete mocs and delete border ([6b69c93](https://github.com/Secure-T-Team/edu-frontend/commit/6b69c931260ceef33244be4572a2891e91b39705))

## [2.37.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.36.0...v2.37.0) (2023-06-15)

### Features

- add back button from ([a2a1969](https://github.com/Secure-T-Team/edu-frontend/commit/a2a1969eca7b80fa9f76974b547b2b0c09e31e27))
- add sso checker for login forms ([b54afe2](https://github.com/Secure-T-Team/edu-frontend/commit/b54afe2c6a43bf90aa08ba4fa5c6237f0f06a1f9))
- add timeout error and corrected icon ([d81c847](https://github.com/Secure-T-Team/edu-frontend/commit/d81c84763871a2b059ee195db7f6d89559708e54))
- change icon ([f55f5a5](https://github.com/Secure-T-Team/edu-frontend/commit/f55f5a5a6ef018403027b51a26ad480815458600))
- change icon ([a2b607c](https://github.com/Secure-T-Team/edu-frontend/commit/a2b607cf0b3a6fdc84c22627315a9346ddbc9c6a))
- connect new request and delete old code ([bbf1b97](https://github.com/Secure-T-Team/edu-frontend/commit/bbf1b97a306a8cb2f1893fa6712e44f9f2e44743))
- corrected auth inputs ([41efd25](https://github.com/Secure-T-Team/edu-frontend/commit/41efd25ef00437e582936b0d7ef8088c3729bb15))
- create sso login module ([5c6f17d](https://github.com/Secure-T-Team/edu-frontend/commit/5c6f17dfc3da18ca7b1f65ef34116de5d35c87d8))
- delete login ad modal ([be201b1](https://github.com/Secure-T-Team/edu-frontend/commit/be201b116c1866525e7c54a2d73d20ac4c0720a8))

### Bug Fixes

- add cache for themes ([b02d011](https://github.com/Secure-T-Team/edu-frontend/commit/b02d01125ecfac5dd5edb2c9942b1e9e92c47ad6))
- corrected registration select ([e70d4f3](https://github.com/Secure-T-Team/edu-frontend/commit/e70d4f36baa8e082f537d028dcfccf00abdd820a))
- fix modal closing ([a0a8c02](https://github.com/Secure-T-Team/edu-frontend/commit/a0a8c02de0dc990858338de426251e12bb67b5b7))
- fix report modal ([711943b](https://github.com/Secure-T-Team/edu-frontend/commit/711943b9a5331979dd9d10d0ea931c339d4a0318))
- fix report modal again ([88e9156](https://github.com/Secure-T-Team/edu-frontend/commit/88e915688d83cb3ccbd4c4d120b44cd872a662f6))
- fix report modal status ([e182b1e](https://github.com/Secure-T-Team/edu-frontend/commit/e182b1e34d21e84c5d11a51aa737b93fe6654bbe))

### Refactoring

- change field name ([b5cb820](https://github.com/Secure-T-Team/edu-frontend/commit/b5cb820e77cf5fd4fbed19fe430239d71130af7e))

## [2.36.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.35.3...v2.36.0) (2023-06-07)

### Features

- add attempt result title ([a12c16a](https://github.com/Secure-T-Team/edu-frontend/commit/a12c16a6c971aae071db20232b6707f21cb03f50))
- add create button ([1f82e99](https://github.com/Secure-T-Team/edu-frontend/commit/1f82e9974beb41611f886c17f51c4cb1310cd745))
- add custom error ([6816c41](https://github.com/Secure-T-Team/edu-frontend/commit/6816c416e6712c9282391c86cdc8c9806b10569b))
- add custom for pdf ([c766f8e](https://github.com/Secure-T-Team/edu-frontend/commit/c766f8e20b0d07b66dabe417c9e40485d8892a2b))
- add disabled for notify button ([efa3efd](https://github.com/Secure-T-Team/edu-frontend/commit/efa3efd1620e52f01e584d5f5c241c3ea1436eec))
- add email if no name ([a498dae](https://github.com/Secure-T-Team/edu-frontend/commit/a498dae8b79d0494cc81fafc11c65ee0fccf1f44))
- add get organization id helpers ([6406118](https://github.com/Secure-T-Team/edu-frontend/commit/6406118b64b684d4a57a175661d1273a88915bce))
- add new helpers ([2785aab](https://github.com/Secure-T-Team/edu-frontend/commit/2785aabf550032e6ee8c9d731fd8248f827d8ae0))
- add onblur for input ([d558989](https://github.com/Secure-T-Team/edu-frontend/commit/d558989683ca241d3286568ff3da9ffdedaeb0db))
- add report modal for assigned course ([e3ecd04](https://github.com/Secure-T-Team/edu-frontend/commit/e3ecd04cf41a19f8fbb692657c5c751249522447))
- add report modal for child org ([0e3331a](https://github.com/Secure-T-Team/edu-frontend/commit/0e3331a5c346765547b427fba8a8ecd3a9967b3d))
- add report modal for employee ([884d2a8](https://github.com/Secure-T-Team/edu-frontend/commit/884d2a8b07469660a2b0dbb7e3cdfa6875f66152))
- add skeleton for quiz ([33d3d91](https://github.com/Secure-T-Team/edu-frontend/commit/33d3d91a249fdb43f486417d05409f0f5e7b1255))
- add timeout error for report modal ([b8aca47](https://github.com/Secure-T-Team/edu-frontend/commit/b8aca47e3e239246d6bd84b4d9d13e2917546e30))
- add tole checker for link ([a3f6750](https://github.com/Secure-T-Team/edu-frontend/commit/a3f675019065eba479453c69859d5f4d9ca76844))
- add trim value for edit and add employee modal ([d7da718](https://github.com/Secure-T-Team/edu-frontend/commit/d7da718e14e38919b691dec0db5b613fa9685164))
- added disabled for button for notification ([10b2d48](https://github.com/Secure-T-Team/edu-frontend/commit/10b2d48e9a418fae7c92174b115c93c4c76fc02b))
- added page with message about registration email ([3901358](https://github.com/Secure-T-Team/edu-frontend/commit/39013588fd23b78793c91e41d8dc0da2a6859650))
- changed folder location and paths ([d5a49aa](https://github.com/Secure-T-Team/edu-frontend/commit/d5a49aa8e05f02ac8a247b084e1804336690783f))
- connect report modal for main statistic ([aa6d0f0](https://github.com/Secure-T-Team/edu-frontend/commit/aa6d0f03eaed16411edbfc2a4ada16e7eb62be3c))
- corrected risk level checker ([0849d5f](https://github.com/Secure-T-Team/edu-frontend/commit/0849d5f7eeae0f95e66047cd4cbfc4a1502ca2f0))
- create back button ([fe265d5](https://github.com/Secure-T-Team/edu-frontend/commit/fe265d52c5bb8d06acd61da31b8a36eb55b58ce6))
- create delete modal form ([7915992](https://github.com/Secure-T-Team/edu-frontend/commit/7915992de8ae6864097aee877c65da8296e3d317))
- create edit organization page and delete old ([d411051](https://github.com/Secure-T-Team/edu-frontend/commit/d4110519293a9c689e6b88153268806a760e895c))
- create license block for organization ([e267956](https://github.com/Secure-T-Team/edu-frontend/commit/e2679569399dcac10a6c9db43f63b49b73992488))
- create modal layout ([a202788](https://github.com/Secure-T-Team/edu-frontend/commit/a202788bb7425c6d226b01c179e724a406287f68))
- create new organization page ([bdcec40](https://github.com/Secure-T-Team/edu-frontend/commit/bdcec40dce8a3e3a5334f24e42f9a8401720e57c))
- create new page and connect ([8b83a20](https://github.com/Secure-T-Team/edu-frontend/commit/8b83a20e950b0c800e35ce9d952a07e7b8611183))
- create page title component ([358889d](https://github.com/Secure-T-Team/edu-frontend/commit/358889d70f26752edd2c528ca8a22f189f219d6c))
- create report module and connect for button ([2464105](https://github.com/Secure-T-Team/edu-frontend/commit/2464105f138c97fc44804bf33d892db120194397))
- end organization create page ([1d2bae5](https://github.com/Secure-T-Team/edu-frontend/commit/1d2bae5d7bce9aee5b020ba564565bd12a2b5fb9))
- end organizations list ([8e7b4d4](https://github.com/Secure-T-Team/edu-frontend/commit/8e7b4d44688475651dd7bf3fe0885c8f49bfce33))
- report modal ([24449ea](https://github.com/Secure-T-Team/edu-frontend/commit/24449ea4720f1330114ce723d6f77174094e8d06))
- report modal for main statistic ([53468b1](https://github.com/Secure-T-Team/edu-frontend/commit/53468b18c7d92a1e37b9054b3cb98220f6643f0e))
- update employee page ([5c8a207](https://github.com/Secure-T-Team/edu-frontend/commit/5c8a2070b2ebd8ef19c563a8792dab78d49c1209))
- update type ([21c5ed1](https://github.com/Secure-T-Team/edu-frontend/commit/21c5ed1b34cc3400a0fc1f7c266ab48cfd1ddb8c))

### Bug Fixes

- add refetch ([067f532](https://github.com/Secure-T-Team/edu-frontend/commit/067f532167b4ea5f7fda1159748c24cd281cee24))
- correct attempt title ([c26af03](https://github.com/Secure-T-Team/edu-frontend/commit/c26af03b91a6b358575bf1226f2f008301d3f6ed))
- correct attempt title ([6737137](https://github.com/Secure-T-Team/edu-frontend/commit/6737137b370f609082f2a8f6834e3b7a4cb86200))
- correct department list styles ([88ae6d1](https://github.com/Secure-T-Team/edu-frontend/commit/88ae6d1a755fb79e1942e228091429e1b0f93550))
- correct erro label ([119a419](https://github.com/Secure-T-Team/edu-frontend/commit/119a419161bab1774e0a161e340d36acc4ee9fec))
- correct error icon ([11e0557](https://github.com/Secure-T-Team/edu-frontend/commit/11e0557c2b881ed8e2be1f2a0d9afa0853b15ecf))
- correct icon ([cd8bd00](https://github.com/Secure-T-Team/edu-frontend/commit/cd8bd00ec19cc90ce2d13fe22e91a66d7b8d1241))
- correct icon ([00eceed](https://github.com/Secure-T-Team/edu-frontend/commit/00eceed47ed46b93106e6f9c7bf5d72b983f99b2))
- correcte edit and create enployee modal ([1d23972](https://github.com/Secure-T-Team/edu-frontend/commit/1d23972c08472e54b8cbf0d3e451b26e667aef95))
- corrected attempt title ([b89bb4b](https://github.com/Secure-T-Team/edu-frontend/commit/b89bb4b13294d17241b6a18e803c2893d0e9e95b))
- corrected date formatter ([79fc11f](https://github.com/Secure-T-Team/edu-frontend/commit/79fc11f20c0eaa2e14bab543f692ace9a77ac99f))
- corrected delete modal text ([f54867c](https://github.com/Secure-T-Team/edu-frontend/commit/f54867cb19ff5a3df25e7db08090be424d3b7364))
- corrected styles and case ([5716e26](https://github.com/Secure-T-Team/edu-frontend/commit/5716e266898b85964c67650052fb5a429e06c9c7))
- corrected styles for emplyee note ([5de9b24](https://github.com/Secure-T-Team/edu-frontend/commit/5de9b24aa4b218a381019c50ec241b33807a84f1))
- delete extra file and correct style ([e22f963](https://github.com/Secure-T-Team/edu-frontend/commit/e22f96335cd20dcc7c50e35e4e5b7b8650fc672d))
- fix attemp report modal ([7cbe192](https://github.com/Secure-T-Team/edu-frontend/commit/7cbe1929f5de7f73ef1cc1f5c4878e026b00fbd6))
- fix registration text ([92cc7e1](https://github.com/Secure-T-Team/edu-frontend/commit/92cc7e131593c782e14f95e7e888968636295890))
- target user modal ([bb36027](https://github.com/Secure-T-Team/edu-frontend/commit/bb36027e7b3678831c278df73dedcaed9a6acea9))
- target user modal ([83919f5](https://github.com/Secure-T-Team/edu-frontend/commit/83919f5b94042c2588c825a6dd6bea893f0ba952))

### Refactoring

- add break ([d26a8d5](https://github.com/Secure-T-Team/edu-frontend/commit/d26a8d52128fa8c47ea1ed281511be77f54d472a))
- correct counter name ([74841b0](https://github.com/Secure-T-Team/edu-frontend/commit/74841b0aee3dfcc911dc816c17e96d978a39acc4))
- correct icons ([b25bdc1](https://github.com/Secure-T-Team/edu-frontend/commit/b25bdc163fe50f71c8887af61a4a9192d3ee4d5b))
- correct link ([7288df7](https://github.com/Secure-T-Team/edu-frontend/commit/7288df76644e70b42113efa2c386bc1ce8dd2339))
- correct modal title ([542b38f](https://github.com/Secure-T-Team/edu-frontend/commit/542b38fe33258a6fa6c8cc5fc72f2e14c1e704ae))
- correct value ([15aa707](https://github.com/Secure-T-Team/edu-frontend/commit/15aa707b01ff8a66caa2b1e98f91c960f3342b62))
- corrected icon ([8d3293f](https://github.com/Secure-T-Team/edu-frontend/commit/8d3293f5fac1d163613bc7252b9f6f8423cfdd46))
- corrected report modal ([cdec09a](https://github.com/Secure-T-Team/edu-frontend/commit/cdec09ac01ecad54c05d15d5aca105fdb65185d8))
- corrected variable name ([2d1de63](https://github.com/Secure-T-Team/edu-frontend/commit/2d1de6388682ba2aa70f5d0cf1239219618c726d))
- delete extra code ([0b97fc6](https://github.com/Secure-T-Team/edu-frontend/commit/0b97fc611c4b345b91d8278cfe70df879a42bf53))
- delete old create page ([60837a2](https://github.com/Secure-T-Team/edu-frontend/commit/60837a298fd7d8132b3ec9fdb9ad928f2dab069f))
- delete old organization page ([c312d4c](https://github.com/Secure-T-Team/edu-frontend/commit/c312d4c36f06756186e924195da6e1e2f89f7f20))
- report modal ([dc0cac3](https://github.com/Secure-T-Team/edu-frontend/commit/dc0cac34ba3d43080d3c7950ab7e4c83aa3c35e2))

### [2.35.3](https://github.com/Secure-T-Team/edu-frontend/compare/v2.35.2...v2.35.3) (2023-05-31)

### Bug Fixes

- update email regex ([508c568](https://github.com/Secure-T-Team/edu-frontend/commit/508c568bdc8134e31941b64eb5034b5c6ec38e87))

### [2.35.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.35.1...v2.35.2) (2023-05-29)

### [2.35.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.35.0...v2.35.1) (2023-05-27)

### Bug Fixes

- correct module slide styles ([2bd8b6f](https://github.com/Secure-T-Team/edu-frontend/commit/2bd8b6f6c4459805b660614c1f91f905ba693424))

## [2.35.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.34.0...v2.35.0) (2023-05-26)

### Features

- add components generator ([4074dfe](https://github.com/Secure-T-Team/edu-frontend/commit/4074dfeb4d9ca4c00b5f4416b17db325ff5475e7))
- add default value for selected user ([c094fc3](https://github.com/Secure-T-Team/edu-frontend/commit/c094fc3fbceb1d32110d60900d189ade529bacbc))
- add service generator ([ab54865](https://github.com/Secure-T-Team/edu-frontend/commit/ab54865a06ce89eafbc335011e8589bf8ecfb541))
- connect new request for org pages ([640b75e](https://github.com/Secure-T-Team/edu-frontend/commit/640b75e8b202801e9c41d74dd27197270dad3908))
- correct icon and add storybook ([dcb8376](https://github.com/Secure-T-Team/edu-frontend/commit/dcb837624a062bdf1e921b841a06d188be538b9a))
- correct input date ([b56193e](https://github.com/Secure-T-Team/edu-frontend/commit/b56193ee3c566eccebdab733cf4106e3bddadb2d))
- create button icon component ([cb942ba](https://github.com/Secure-T-Team/edu-frontend/commit/cb942ba3a1ea721222f9bf023df3331e38fbb21b))
- create file icon component ([89fdd66](https://github.com/Secure-T-Team/edu-frontend/commit/89fdd66343fa0cd37199cd19b2ece65747a4cf3c))
- create menu icons and create storybook ([10cd3bc](https://github.com/Secure-T-Team/edu-frontend/commit/10cd3bc8e8f3a143cab2e4169ff12c7bec3c1cc5))
- create rounded button and correct icon ([a26f8c7](https://github.com/Secure-T-Team/edu-frontend/commit/a26f8c7e70898464099fe9f315bc24ce1244f17b))
- modal for user selection ([5125dc8](https://github.com/Secure-T-Team/edu-frontend/commit/5125dc8b8169fbae0ff9dbabf45430d7f9e3da00))
- statistics page and new components ([5c82423](https://github.com/Secure-T-Team/edu-frontend/commit/5c82423b3b573fb7fa2edf6361cea64dcc981edd))
- update stitistics group block ([a9b8ec1](https://github.com/Secure-T-Team/edu-frontend/commit/a9b8ec1cdce7d72a1a98d3800a74dda39d3bd954))

### Bug Fixes

- correct hygen settings ([642e0ad](https://github.com/Secure-T-Team/edu-frontend/commit/642e0ad7999de8fadb9abfc1b5cb0f4c2c5c264d))
- correct hygen template ([d2f6a6b](https://github.com/Secure-T-Team/edu-frontend/commit/d2f6a6b3359239d4d8df0ee686e9e43e14bae9e2))
- correct hygtn settings ([547730a](https://github.com/Secure-T-Team/edu-frontend/commit/547730a9aa4740cfa22fb643b687b67b8517d600))
- correct logo size ([9359de8](https://github.com/Secure-T-Team/edu-frontend/commit/9359de831abbb8add062db31139d0fd2adf983d8))
- correct slide image height ([ca064c8](https://github.com/Secure-T-Team/edu-frontend/commit/ca064c82c10fab07e316cdbc4b9b3fd79192e422))
- corrected choice of department in registration ([931e606](https://github.com/Secure-T-Team/edu-frontend/commit/931e606d635b1e7ec2d8dda80f76e0756ea319e3))
- corrected image height ([3b593a7](https://github.com/Secure-T-Team/edu-frontend/commit/3b593a7ab935a2ec6a6918caab8a75792a588548))
- corrected image height ([3a56c9b](https://github.com/Secure-T-Team/edu-frontend/commit/3a56c9b2dff81be3b142b1b599488d5840987d28))

### Refactoring

- correct hygen template ([019d1ee](https://github.com/Secure-T-Team/edu-frontend/commit/019d1ee6e20a5e46545539cb5763ebef11a0bd7a))
- correct icon component ([6931894](https://github.com/Secure-T-Team/edu-frontend/commit/69318941a080ccc4f0d6a6b415a1d929c7040d04))
- correct icon component ([fdfe944](https://github.com/Secure-T-Team/edu-frontend/commit/fdfe944712d612b539ee68c03515259925295165))
- correct icon component ([195f3d3](https://github.com/Secure-T-Team/edu-frontend/commit/195f3d3683674d920003654d1b0ebec9f4159303))
- correct icon components ([dee8a64](https://github.com/Secure-T-Team/edu-frontend/commit/dee8a642311789539fc3e8d0970f3c98cefacf8e))
- correct menu icon sb ([139042e](https://github.com/Secure-T-Team/edu-frontend/commit/139042e7c3d870170f1e8820b9eb3a5383815672))
- delete extra code ([47d4abf](https://github.com/Secure-T-Team/edu-frontend/commit/47d4abf1439ab54545c537c5d212dc9f5781fef9))
- update organization statistic service ([f4efbc2](https://github.com/Secure-T-Team/edu-frontend/commit/f4efbc2bad81bdc33ef5a2cadd511827e71b8eb1))
- update package lock ([8810898](https://github.com/Secure-T-Team/edu-frontend/commit/881089812ab7370901947cd986bc88ddd1596f4e))

## [2.34.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.33.0...v2.34.0) (2023-05-23)

### Features

- add error handler for logo ([23bace8](https://github.com/Secure-T-Team/edu-frontend/commit/23bace8a6123805ba8e8db5fb942fdab695b7ca9))
- added new create employee modal ([9221012](https://github.com/Secure-T-Team/edu-frontend/commit/9221012420482d946d134dbf9b7aaeab7e0974e9))
- change modal closing ([f0c1f05](https://github.com/Secure-T-Team/edu-frontend/commit/f0c1f05e7bc4d6e54a092ad0fbdc63bdd4e6e197))
- create create modal and connect ([7415cf3](https://github.com/Secure-T-Team/edu-frontend/commit/7415cf3e753a710eb16617e301e41e048ed749d3))
- update select closing ([40b10ea](https://github.com/Secure-T-Team/edu-frontend/commit/40b10ea5e4761a448096e5688f15bc3d906a8f80))

### Bug Fixes

- correct line charts ([6b4ea1e](https://github.com/Secure-T-Team/edu-frontend/commit/6b4ea1e515f0b802d11be2f85ae6a89883584e74))
- correct loader styles ([5455f1e](https://github.com/Secure-T-Team/edu-frontend/commit/5455f1e3223aeda6aeb8b5b17342439ece265459))
- correct types for edit employee modal ([3d4b846](https://github.com/Secure-T-Team/edu-frontend/commit/3d4b8468d129321ee4437fda23726ab512fe816c))
- corrected image height ([e9bbb3c](https://github.com/Secure-T-Team/edu-frontend/commit/e9bbb3c0ba2a1038daca4231cca1fad39da442f7))
- corrected styles for image height ([31ebbf4](https://github.com/Secure-T-Team/edu-frontend/commit/31ebbf46176c1ae731400cc877f272caf8b2d1a8))
- fix logo size ([8fc2726](https://github.com/Secure-T-Team/edu-frontend/commit/8fc272674e762a6ea35324682fd4963382885f11))

### Refactoring

- delete error handler for logo ([c558145](https://github.com/Secure-T-Team/edu-frontend/commit/c5581456effff99d519301a609c45d4667431c6d))

## [2.33.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.32.0...v2.33.0) (2023-05-17)

### Features

- add avatar button ([791bb2f](https://github.com/Secure-T-Team/edu-frontend/commit/791bb2f0de14ad45d11d3c926d30c5ef03e2090f))
- add requited for inputs ([fc587bd](https://github.com/Secure-T-Team/edu-frontend/commit/fc587bd85946888a844985fb36e3e1c9fd1f37a9))

### Bug Fixes

- correct avatar size ([87fae80](https://github.com/Secure-T-Team/edu-frontend/commit/87fae80bd9e7193b8c23c41e5f113ef66fc20e62))

## [2.32.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.31.0...v2.32.0) (2023-05-16)

### Features

- add base url ([8be58af](https://github.com/Secure-T-Team/edu-frontend/commit/8be58af545f1baadaf6a601f9a6230be2748fe48))
- add label for input and correct reexport ([d3600c2](https://github.com/Secure-T-Team/edu-frontend/commit/d3600c2b3c78b767e66a95f274f7b6f905a04595))
- add label for select ([7151776](https://github.com/Secure-T-Team/edu-frontend/commit/7151776e425a2f611bd6b75b7dec2434d0afa686))
- add loader for page and correct styles ([38652bf](https://github.com/Secure-T-Team/edu-frontend/commit/38652bf03c267368bd4d1ddb9d8c6bab385587be))
- add new edit slide ([3c7f28e](https://github.com/Secure-T-Team/edu-frontend/commit/3c7f28e21b33b14e51fe47971cc4f6bda10b03c0))
- add new file for service and delte extra code ([5165e4b](https://github.com/Secure-T-Team/edu-frontend/commit/5165e4bcf390551d8ba4ad75c564bd8f2000d2b6))
- add new icons and correct old ([e59d8bc](https://github.com/Secure-T-Team/edu-frontend/commit/e59d8bc34124be77f9263ff691730b7260312033))
- add new icons and correct old ([4e1e0cb](https://github.com/Secure-T-Team/edu-frontend/commit/4e1e0cbab51a6bc0e6f9b69052e5deb0e4acaeab))
- add new login page and connect ([db924e4](https://github.com/Secure-T-Team/edu-frontend/commit/db924e4dd7351d6648997491ddbe82c07e01f175))
- add registration ([16bc9c6](https://github.com/Secure-T-Team/edu-frontend/commit/16bc9c6dd766c23cc020efc269e18c20c264c3fe))
- add service for create questions ([e90180b](https://github.com/Secure-T-Team/edu-frontend/commit/e90180bde1dc8051606a2da688068d99905768a5))
- add service for my courses ([1d790fd](https://github.com/Secure-T-Team/edu-frontend/commit/1d790fd8eae14747186f18e45e102460dac73d30))
- add service for my statistics and delete extra code ([2f87779](https://github.com/Secure-T-Team/edu-frontend/commit/2f87779d165351a67762cacf1bf07ada7c1a37d6))
- connect loader for registration page ([438b014](https://github.com/Secure-T-Team/edu-frontend/commit/438b014ff16a60b303d5317047a66102f5d2c5f2))
- connect new create slide ([4421dcb](https://github.com/Secure-T-Team/edu-frontend/commit/4421dcbfbab154e676fe2f18a285a6ab0b083675))
- connect new delete, copy and get modules ([7770a18](https://github.com/Secure-T-Team/edu-frontend/commit/7770a18ebfad177a699d52fb60720c34247812c4))
- connect new get info module and edit module ([6c3eee6](https://github.com/Secure-T-Team/edu-frontend/commit/6c3eee6b6a8babea1ca4c76ad4e43485b9a2eaf2))
- connect new login and create types for user ([7f60528](https://github.com/Secure-T-Team/edu-frontend/commit/7f605284c8a000ae977134df54051a62131efd8a))
- connect question excel ([112f886](https://github.com/Secure-T-Team/edu-frontend/commit/112f8869e1fbdf09253a47dd977630118c9d8a42))
- connect service for creating module and delete extra code ([242988b](https://github.com/Secure-T-Team/edu-frontend/commit/242988b12dffe162c7e8a6bb126f599e4d359bbf))
- connect service for edit module ([2fccf32](https://github.com/Secure-T-Team/edu-frontend/commit/2fccf3287dfc018d5248f57f73a9daffc7e643f0))
- connect service for edit questions ([bcf1026](https://github.com/Secure-T-Team/edu-frontend/commit/bcf10261b219671423aac10dd7eb194a433cf59f))
- correct css import and path for storybook ([7e7d3ca](https://github.com/Secure-T-Team/edu-frontend/commit/7e7d3cae6e4a9f511b8bd48edea4017e292443db))
- correct import question from excel ([c480e01](https://github.com/Secure-T-Team/edu-frontend/commit/c480e015983789666b7628cb5e6ec00aaeee6763))
- correct login page and create stories for pages ([b802255](https://github.com/Secure-T-Team/edu-frontend/commit/b8022559bf76892f3f1778debb748f989e95e75c))
- correct select and add sort ([3a8de72](https://github.com/Secure-T-Team/edu-frontend/commit/3a8de7285f21af631324df4fc7f7d09bce13d968))
- correct validation settings ([f9678f3](https://github.com/Secure-T-Team/edu-frontend/commit/f9678f3f665a26ada1442d79a2b52af5c3f995ea))
- corrected icon and checkbox ([1de494c](https://github.com/Secure-T-Team/edu-frontend/commit/1de494c4f952e337aa1d71fda3af5a841e3f5274))
- corrected icons ([96c27ec](https://github.com/Secure-T-Team/edu-frontend/commit/96c27ec21120377bb7f4144fa20184fdf076313c))
- corrected input and select component ([66b01f5](https://github.com/Secure-T-Team/edu-frontend/commit/66b01f5db525f15d823f561f51e3d44c602b501c))
- corrected service and add new types ([ef1db9f](https://github.com/Secure-T-Team/edu-frontend/commit/ef1db9f4e87b787bd6fb04815fe7d069ad5b19a1))
- creat sso login form and hide ([6de079d](https://github.com/Secure-T-Team/edu-frontend/commit/6de079dab6389960913efed09fabcbf7744ed05f))
- create all icons ([f658e31](https://github.com/Secure-T-Team/edu-frontend/commit/f658e3117f5780165fcbec9f5f8712d1c3667ad4))
- create auth service ([40ffad4](https://github.com/Secure-T-Team/edu-frontend/commit/40ffad49f50d9dd945d44ed146e1cce08320453a))
- create base checkbox ([75c4b31](https://github.com/Secure-T-Team/edu-frontend/commit/75c4b31dbecada5c6b55cd438ecf3c50a00051fa))
- create base loader component ([42a2f1a](https://github.com/Secure-T-Team/edu-frontend/commit/42a2f1a04ff092614e388380b0a97b0e7e7d3cf6))
- create base template for button stories ([a179403](https://github.com/Secure-T-Team/edu-frontend/commit/a1794038ca7fe00dc96384f55e150f78f59ee22c))
- create button component without icons ([4ee941f](https://github.com/Secure-T-Team/edu-frontend/commit/4ee941f01280ffaf138e4196ca58d11afc546e19))
- create button stories and correct styles for auth page ([bef230d](https://github.com/Secure-T-Team/edu-frontend/commit/bef230d734c9d0371dcc699e6ab85877a73e0586))
- create folders and files for auth form modules ([aeb98e9](https://github.com/Secure-T-Team/edu-frontend/commit/aeb98e9c2acf86be403a954409b78f533a4b97a7))
- create folders and files for auth pages ([32581f0](https://github.com/Secure-T-Team/edu-frontend/commit/32581f07f557525605d708fd727e8745ea630c97))
- create folders for redisign ([44b933d](https://github.com/Secure-T-Team/edu-frontend/commit/44b933df3c28c6d27a4d4c81a0c6e4ebb6f94b65))
- create form title component and correct input ([45b2bee](https://github.com/Secure-T-Team/edu-frontend/commit/45b2beedea02f7fc8a509cbcc9e1398498324e9f))
- create hooks for redux ([8442ef9](https://github.com/Secure-T-Team/edu-frontend/commit/8442ef985589fb6642abf6731e4ba9a65c3f7119))
- create layout for auth pages ([82f3d33](https://github.com/Secure-T-Team/edu-frontend/commit/82f3d33fb7811c3065a17b50f2d289f69310fdb5))
- create logo component ([8ff70f8](https://github.com/Secure-T-Team/edu-frontend/commit/8ff70f851f68abcb44afbec183ac677151116412))
- create modal component ([6a792ee](https://github.com/Secure-T-Team/edu-frontend/commit/6a792eee8c3e84512c8d8b106889695eb6861e53))
- create new edit employee modal ([d557879](https://github.com/Secure-T-Team/edu-frontend/commit/d5578793a1026b7f3c02fc19767c6dcdb11e6525))
- create new icon component ([43283b8](https://github.com/Secure-T-Team/edu-frontend/commit/43283b82ef4b4de9a382a899ae86ff20465d87e3))
- create new input component ([5c06a5b](https://github.com/Secure-T-Team/edu-frontend/commit/5c06a5b49a16c1717df887581e8bf95f55ecd961))
- create new password recovery request ([b540f7f](https://github.com/Secure-T-Team/edu-frontend/commit/b540f7fca891efb232961b739b49641eee331eac))
- create new restore request and delete old ([efaeae9](https://github.com/Secure-T-Team/edu-frontend/commit/efaeae9c4d47bef5a7a45e885559fcd64c59fcfe))
- create new service for user and end edit form ([a1bdba6](https://github.com/Secure-T-Team/edu-frontend/commit/a1bdba6100cbdd909e9d90f4fae7d09e39382bac))
- create new theme css files ([d2bc4e7](https://github.com/Secure-T-Team/edu-frontend/commit/d2bc4e770c7c67804af03b831c4cccc95c439b8a))
- create password change modal and connect logout ([5ed812b](https://github.com/Secure-T-Team/edu-frontend/commit/5ed812bb58583fc6499d3aa3ca167b6ddcd72a2e))
- create select component ([52f875d](https://github.com/Secure-T-Team/edu-frontend/commit/52f875dc61397587a04c71c7144fe896f0622a8d))
- create service for module page and new types ([6b9b45e](https://github.com/Secure-T-Team/edu-frontend/commit/6b9b45ebe6591a412bbe3795105cc5b9f95b7671))
- create service for statistics page and delete extra code ([ca1523e](https://github.com/Secure-T-Team/edu-frontend/commit/ca1523ea763b80fda8706388c217a2d401f2db11))
- crete departments service and use ([680cf5c](https://github.com/Secure-T-Team/edu-frontend/commit/680cf5c79884055b8d3d3838701d3aeab8ebe669))
- end login page ([8f6a6ce](https://github.com/Secure-T-Team/edu-frontend/commit/8f6a6cedfbf1093417e5e62379152d908f9bcde9))
- end password change page ([d7593a5](https://github.com/Secure-T-Team/edu-frontend/commit/d7593a5340c3541259af832945a30a8c5b60f0ea))
- end password recovery form ([de96c85](https://github.com/Secure-T-Team/edu-frontend/commit/de96c8559b33701e10e177db398345fa109d49de))
- end profile page and create base button card ([5c76e08](https://github.com/Secure-T-Team/edu-frontend/commit/5c76e081519a1e00ccb242e170b97dfe637952f8))
- ended registration page ([e82f7ba](https://github.com/Secure-T-Team/edu-frontend/commit/e82f7ba08a190a589d739458b264db8b173d5497))
- install new packages ([1314520](https://github.com/Secure-T-Team/edu-frontend/commit/13145205ce5b6100f381da79cf916ae49d7851a6))
- install react toolkit ([297ca66](https://github.com/Secure-T-Team/edu-frontend/commit/297ca6649d188b87030856a11de5a2669eda9aef))
- realize base login page ([477eb67](https://github.com/Secure-T-Team/edu-frontend/commit/477eb677ede89a1e246cb2f45f587ffc6fecb453))
- rename and safe old storybook and create new settings ([7bc7804](https://github.com/Secure-T-Team/edu-frontend/commit/7bc78041cffe8d55172b561346c2d564b8a6ca6b))
- save new service ([3640ecb](https://github.com/Secure-T-Team/edu-frontend/commit/3640ecbdd74488c0e5e715aad4c314de49a8eb40))
- start profile page ([94a757a](https://github.com/Secure-T-Team/edu-frontend/commit/94a757a22aff2cea8437019b1dbae148c1f9e080))
- starting registration page ([8c3a5f7](https://github.com/Secure-T-Team/edu-frontend/commit/8c3a5f7d788c260c347295236687de25f62ad23e))

### Bug Fixes

- change limit ([c990dce](https://github.com/Secure-T-Team/edu-frontend/commit/c990dce2d6f3ce0c04b07f72cc4bc4088bf3b023))
- correct button and input color ([4b91abc](https://github.com/Secure-T-Team/edu-frontend/commit/4b91abc18c9d106726acd68dd36bfbd2a2c8769a))
- correct button styles ([2f58f64](https://github.com/Secure-T-Team/edu-frontend/commit/2f58f6415a01e29e6e01c914007a362ea6eb2aae))
- correct buttonn and icon ([357cfd7](https://github.com/Secure-T-Team/edu-frontend/commit/357cfd72247fcdd43d4aa1e7ba1f3e59271938e9))
- correct error validator ([346d8b1](https://github.com/Secure-T-Team/edu-frontend/commit/346d8b1532208375aec87f3373fea7e7201bb685))
- correct icons ([318339c](https://github.com/Secure-T-Team/edu-frontend/commit/318339caaccd1b74fb1d22af8bdb9f0d0a3e9202))
- correct styles ([c0220ec](https://github.com/Secure-T-Team/edu-frontend/commit/c0220ec92535afaa0418bb17642026af136dd4a5))
- correct title styles and comment extra code ([e0de0d2](https://github.com/Secure-T-Team/edu-frontend/commit/e0de0d249d3519c2d4ce6b870689eda2b1ac102d))
- corrected modal styles ([cdbe1f7](https://github.com/Secure-T-Team/edu-frontend/commit/cdbe1f7f5f70df9cee63f42acb9891bf76f6f702))
- corrected regex ([72c785b](https://github.com/Secure-T-Team/edu-frontend/commit/72c785b745e0eb6a7191f280514621491ea12ed6))

### Refactoring

- delete extra code ([a5476f9](https://github.com/Secure-T-Team/edu-frontend/commit/a5476f92bd192e37b1d40bde45b2bbfdfdba66b4))
- delete extra file ([6dbc727](https://github.com/Secure-T-Team/edu-frontend/commit/6dbc727cc341bd43b333dda034fee36c3e4cf09b))
- delete extra pages ([3db25bb](https://github.com/Secure-T-Team/edu-frontend/commit/3db25bb5c7d3a4299d66daea4525cf66818c4d3e))
- delete extra styles ([395b2a3](https://github.com/Secure-T-Team/edu-frontend/commit/395b2a33f6e67c74cc0397e54b7d91b17f3991ed))
- delete old login ([2386226](https://github.com/Secure-T-Team/edu-frontend/commit/2386226bfc4844a024d8ca5d0476e85f5c7ca8e3))
- delete old login page and correct styles ([6dac327](https://github.com/Secure-T-Team/edu-frontend/commit/6dac32731eff5e103a29753309724947a8b14ec4))
- delete old registration ([57b8a6b](https://github.com/Secure-T-Team/edu-frontend/commit/57b8a6b282bce24d29dff11a86cd165cdd1e2de6))

## [2.31.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.30.2...v2.31.0) (2023-05-15)

### Features

- add default date for datepicker ([cc74925](https://github.com/Secure-T-Team/edu-frontend/commit/cc749252eb14d72b9cf293976f0afd9daa6bc073))
- added color check for answer ([71abe5c](https://github.com/Secure-T-Team/edu-frontend/commit/71abe5c2c9ec91ed2b598ed8b6106e2b72598dca))
- added undo delete ([a480af8](https://github.com/Secure-T-Team/edu-frontend/commit/a480af87a44b7c747f27d407c31f8630ce4325b9))

### Bug Fixes

- correct placeholder ([ca04206](https://github.com/Secure-T-Team/edu-frontend/commit/ca04206e92e85e18251e5242c6b632d664eb9e3b))

### [2.30.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.30.1...v2.30.2) (2023-05-12)

### Bug Fixes

- correct line chart ([58de522](https://github.com/Secure-T-Team/edu-frontend/commit/58de52218ff5577057d626cd2d55be624da1eed5))
- correct url parser ([722575c](https://github.com/Secure-T-Team/edu-frontend/commit/722575cc3a88afa9e0fc125a8453904c7c0e162a))
- fix risk level color ([4f4f7c8](https://github.com/Secure-T-Team/edu-frontend/commit/4f4f7c843cc08483358b590a29281f775f3d76d6))

### Refactoring

- delete extra code ([58f1e84](https://github.com/Secure-T-Team/edu-frontend/commit/58f1e84ba8b37a322bf4d65254dd8789de1e4b4d))

### [2.30.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.30.0...v2.30.1) (2023-05-10)

### Bug Fixes

- correct base url checker ([cda2f5b](https://github.com/Secure-T-Team/edu-frontend/commit/cda2f5b26c3fa2df54c30f8303e9b6a1e4f4a36f))
- correct base url checker again ([6332387](https://github.com/Secure-T-Team/edu-frontend/commit/6332387bd541d51aa663a2750d99e5df12c4f465))
- correct base url checker again ([71658f3](https://github.com/Secure-T-Team/edu-frontend/commit/71658f393412bd06e4896848c013b959a3914619))
- correct base url checker again ([8f5740b](https://github.com/Secure-T-Team/edu-frontend/commit/8f5740b59eb599455c0950877208720827f7fea2))
- corrected base url ([a546c5c](https://github.com/Secure-T-Team/edu-frontend/commit/a546c5c03b95ffaf301e6894eaf601ca6a5ba591))

## [2.30.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.29.0...v2.30.0) (2023-05-10)

### Features

- add x-referer for header ([0bacb06](https://github.com/Secure-T-Team/edu-frontend/commit/0bacb061914fa2f6883faf98d14bbe1d04aec370))

## [2.29.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.28.0...v2.29.0) (2023-05-09)

### Features

- add autoresize for slides in my courses ([042aaa0](https://github.com/Secure-T-Team/edu-frontend/commit/042aaa01a3f40b29940390d31df64dc2386fa0a0))
- add checker for line charts ([369f1da](https://github.com/Secure-T-Team/edu-frontend/commit/369f1da101f2b3b2ee809d5001ea6e87110267d6))
- add default department for setValue ([5208991](https://github.com/Secure-T-Team/edu-frontend/commit/52089919e93881393e89d5c5c0bc30b82ba4a791))
- add default value for select ([2ed8076](https://github.com/Secure-T-Team/edu-frontend/commit/2ed807614298d293e96892ede9b398ab9d6d21fc))
- add default values for create quiz modal ([189df9c](https://github.com/Secure-T-Team/edu-frontend/commit/189df9c6db5ea7816e3ae96913cb313d57d971da))
- add delay for copy module ([ea20a20](https://github.com/Secure-T-Team/edu-frontend/commit/ea20a20ba72c60793a59c9ec287c6b6b3038bd21))
- add email instead empty name ([6b97936](https://github.com/Secure-T-Team/edu-frontend/commit/6b97936250f63e8d71e7d943a166a525bd204efb))
- add encode for employees search ([865422f](https://github.com/Secure-T-Team/edu-frontend/commit/865422f14b403f51d8c47078be42e21962f969d0))
- add error cleaner ([8d0df75](https://github.com/Secure-T-Team/edu-frontend/commit/8d0df75c1c44f2638aa9c08ff4f6923b2e106fb0))
- add error for edit and create question ([abbb7ae](https://github.com/Secure-T-Team/edu-frontend/commit/abbb7ae4eb0bba47e6c633606d5694a249c57f30))
- add file name for csv upload ([eac6621](https://github.com/Secure-T-Team/edu-frontend/commit/eac6621608a1176a7dce6b7528b1fa8f41bb2811))
- add https for create template ([c11d124](https://github.com/Secure-T-Team/edu-frontend/commit/c11d124ff61cb2430b9d3d88ffac85176a82cac0))
- add https for edit template ([025a581](https://github.com/Secure-T-Team/edu-frontend/commit/025a5813595fc5412bf9a1763b2583e55ed3eaea))
- add input validation for quiz fields ([14a862f](https://github.com/Secure-T-Team/edu-frontend/commit/14a862f662074e7dd5c3a4f0d3070292122b4647))
- add limit for quiz total and passing ([244103f](https://github.com/Secure-T-Team/edu-frontend/commit/244103f128edb34c698e870b214b56774cd5869b))
- add max width for domain select ([3a154e6](https://github.com/Secure-T-Team/edu-frontend/commit/3a154e6cff5e4e1e98b7c534c4f79f80e0111532))
- add min date for date picker ([dbab155](https://github.com/Secure-T-Team/edu-frontend/commit/dbab155f86773cf190daa38cee321559e2de9459))
- add min value for employees limit fields ([7bf6035](https://github.com/Secure-T-Team/edu-frontend/commit/7bf6035aef5599a28b5b4208c9b82965e9fb41d4))
- add min value for input ([e4b8d5e](https://github.com/Secure-T-Team/edu-frontend/commit/e4b8d5e41ceb0ea1e40d9ec0677ad4fcf1b59f34))
- add new command for windows ([7c72e3b](https://github.com/Secure-T-Team/edu-frontend/commit/7c72e3b67463b3c60c13ae7bde59a81f5826a7ed))
- add new components and registration page ([1ba8564](https://github.com/Secure-T-Team/edu-frontend/commit/1ba8564a6b3e3afb1443e655fa92ef669b938e33))
- add new domain slicer for edit email ([8b29ff3](https://github.com/Secure-T-Team/edu-frontend/commit/8b29ff38420d018fcdc0469585b0221f96e40117))
- add new edit form for employees and connect new services ([c0cc9b7](https://github.com/Secure-T-Team/edu-frontend/commit/c0cc9b79f0f6b705aaab51067123b2d785c47d89))
- add new rule for disable registration button ([2aad23e](https://github.com/Secure-T-Team/edu-frontend/commit/2aad23e143254b78f8d13e6fb813a4598a5dbf84))
- add new sender fields for create email ([dfd47dd](https://github.com/Secure-T-Team/edu-frontend/commit/dfd47dd4832c5ed3315510880ce777ef47beb8cc))
- add new sender fields for edit email ([95a202a](https://github.com/Secure-T-Team/edu-frontend/commit/95a202a7b8ced7b5291803fa09c31660b1dfaae1))
- add progress bar for module in my courses ([156bfc9](https://github.com/Secure-T-Team/edu-frontend/commit/156bfc9d1c3236dcb6c44dbf611f588cc8b6bfe8))
- add redirect ([8d89800](https://github.com/Secure-T-Team/edu-frontend/commit/8d89800dc13ae6aa1bb0dffbd9a2e454f3883ee0))
- add refetch for questions file upload ([9167d6f](https://github.com/Secure-T-Team/edu-frontend/commit/9167d6fff4c5cf3f4912bbfdac027ca612551ddd))
- add required for field ([7c970b4](https://github.com/Secure-T-Team/edu-frontend/commit/7c970b45e184990163fab84a7f7a8299ef01dc95))
- add required for fields ([65f649e](https://github.com/Secure-T-Team/edu-frontend/commit/65f649ea42cb3be7701a6e6e6005586ca87d0d2f))
- add role handler for select ([c73d917](https://github.com/Secure-T-Team/edu-frontend/commit/c73d917946c6f187b92ddbb6ecd3a527f01a43f6))
- add select all button ([1b24bc0](https://github.com/Secure-T-Team/edu-frontend/commit/1b24bc040c530bd4a05f35872bbb8f684f1490c4))
- add service for user statistics page ([2e2d02b](https://github.com/Secure-T-Team/edu-frontend/commit/2e2d02bc3588ec6f4322f1e1e74875ee4559bde0))
- add sorting slides in module ([e4008e7](https://github.com/Secure-T-Team/edu-frontend/commit/e4008e7cef002d8de61076351832e39bdaa238a3))
- add sorting slides in module preview ([62fa8e4](https://github.com/Secure-T-Team/edu-frontend/commit/62fa8e45ea9fcaca29280d1479e1efabe930a7cd))
- add timer for quiz ([810c748](https://github.com/Secure-T-Team/edu-frontend/commit/810c748f5efe2214aa8b3fd7f0e487bde9bc0c1b))
- add tracker for create template ([1dddd1d](https://github.com/Secure-T-Team/edu-frontend/commit/1dddd1d2882842341d0e45a79ced0309a48a4f8c))
- add url checker for template url ([099db93](https://github.com/Secure-T-Team/edu-frontend/commit/099db936acd5640b13a4cce397b663a939fe2e0d))
- add uuid counter for employees selector ([c7d67ab](https://github.com/Secure-T-Team/edu-frontend/commit/c7d67abc86245d44de21c5c15e9e3aa450ff76fa))
- add validation for sender in phishing email ([c8e82f8](https://github.com/Secure-T-Team/edu-frontend/commit/c8e82f8aa5643dabc8771208cd047904d540d0b9))
- add validation for tag messages ([85f649c](https://github.com/Secure-T-Team/edu-frontend/commit/85f649c546a3c6cd0699783602ef86326021ecc9))
- add word in slider counter ([c07bf8f](https://github.com/Secure-T-Team/edu-frontend/commit/c07bf8fbe4d1bc8a9db049a548c8c930e8594347))
- added a new temporary type for courses ([2c3839c](https://github.com/Secure-T-Team/edu-frontend/commit/2c3839c5a2055dc6ee51642e7eafd08974f0f02a))
- added a query for list of modules and made a stub ([893e032](https://github.com/Secure-T-Team/edu-frontend/commit/893e032af25843bad16f0c3ec1dc15b32a5ec944))
- added course edit request ([e4d168e](https://github.com/Secure-T-Team/edu-frontend/commit/e4d168eca184a8f93690e7f6c4c07bfc38c3a6f0))
- added creation of subsidiaries ([3b72500](https://github.com/Secure-T-Team/edu-frontend/commit/3b725001f3de1700a8bb4dc3882eb3c245579c3f))
- added deleting org and started updating ([1c4bb43](https://github.com/Secure-T-Team/edu-frontend/commit/1c4bb437649bf1234be7f47af885a421636b2584))
- added error deleting ([e1f1b09](https://github.com/Secure-T-Team/edu-frontend/commit/e1f1b09ad763437cf391944bea3b5957abf53c5a))
- added error handler for uploading employees with excel ([2c538c9](https://github.com/Secure-T-Team/edu-frontend/commit/2c538c98f58a283b984a6a81c7e27026659cb5e5))
- added getting risk level ([4c368b6](https://github.com/Secure-T-Team/edu-frontend/commit/4c368b6468f4b387e349644a00665a358273a934))
- added new field to creating ([63e5f2c](https://github.com/Secure-T-Team/edu-frontend/commit/63e5f2cfedeb70a9c06a480dc35e81e1195a4485))
- added new types ([c368f41](https://github.com/Secure-T-Team/edu-frontend/commit/c368f4166609a38d4cd2657b35977ee0e388b6ad))
- added organization editing ([2ea0c1a](https://github.com/Secure-T-Team/edu-frontend/commit/2ea0c1a086c8f5a157a266d0f586764b2847cf29))
- added receiving data to send ([2aeac82](https://github.com/Secure-T-Team/edu-frontend/commit/2aeac82deae1c019a4292749015a0ba6a83d3490))
- added reload after policy confirmation ([afa2406](https://github.com/Secure-T-Team/edu-frontend/commit/afa2406dfdb55e62a70b1bf75e22681a2f5a2cfb))
- added request for registration user info ([1e8ed26](https://github.com/Secure-T-Team/edu-frontend/commit/1e8ed2675db80e9dbf0156d5b7a8e1502ca7cf71))
- added request to update course ([03cd96a](https://github.com/Secure-T-Team/edu-frontend/commit/03cd96aac23e7784176acf4fe3b794c0961ec9cc))
- added separate loaders and changed order of requests ([2a33f0a](https://github.com/Secure-T-Team/edu-frontend/commit/2a33f0a57559a757112db2b9571d0bb1358ca962))
- added uuid to the list of employees ([0b9698f](https://github.com/Secure-T-Team/edu-frontend/commit/0b9698f068e163bd25c16c198c19fcd3e658c4c7))
- changed course link to old id ([5fefef1](https://github.com/Secure-T-Team/edu-frontend/commit/5fefef18a3e4afc64086979758e8746a1c309ee5))
- changed data for course editing form ([eef7f75](https://github.com/Secure-T-Team/edu-frontend/commit/eef7f75fc8043ae64870c8db8f2109766f6ca75a))
- changed get data request ([cadd084](https://github.com/Secure-T-Team/edu-frontend/commit/cadd0840a88e79094ce91e83df7c0f938c7ac826))
- changed links ([7775a1b](https://github.com/Secure-T-Team/edu-frontend/commit/7775a1b5e511f4b95991995c4e155ae52e5a2e52))
- changed password recovery request and made template for verification ([7015917](https://github.com/Secure-T-Team/edu-frontend/commit/701591795866d7895d6e5754df04f07997aa28d0))
- changed password validation request ([f641f2e](https://github.com/Secure-T-Team/edu-frontend/commit/f641f2edf725b3f4663d4efb7f14be6171b77f15))
- changed query when creating the course ([030a459](https://github.com/Secure-T-Team/edu-frontend/commit/030a4596c4ea96f086e2bf32ea60aba383926e77))
- changed request for geting department statistics ([79bebd8](https://github.com/Secure-T-Team/edu-frontend/commit/79bebd893720989197679f6727f93ca3aea634c3))
- changed request for getting information about course for editing ([e9d23f9](https://github.com/Secure-T-Team/edu-frontend/commit/e9d23f9e0e88694efb2d4d48dd1712572631d50d))
- changed request for receiving and corrected deletion ([dcff64d](https://github.com/Secure-T-Team/edu-frontend/commit/dcff64d36900805d81b4b8f21f80a511cd3c1df0))
- changed request for receiving courses and cleaned page ([d8e914e](https://github.com/Secure-T-Team/edu-frontend/commit/d8e914e244dd9b2026d6f5e448ef5e86e33d5cb5))
- changed request to delete courses ([2d372d0](https://github.com/Secure-T-Team/edu-frontend/commit/2d372d0408045af7490ecaf5844b677a4eb2682c))
- changed request to update password and made a blank for checking ([696702d](https://github.com/Secure-T-Team/edu-frontend/commit/696702d628df2b079ead28b86396540d8cd7a41c))
- changed requests for obtaining statistics data ([34bb41b](https://github.com/Secure-T-Team/edu-frontend/commit/34bb41b1f866b7cdf5eeb04872d69b7333ba88f7))
- changed requests on page of employees in the course ([b276b39](https://github.com/Secure-T-Team/edu-frontend/commit/b276b39f72563e4ed823f410a56043e4450d3e81))
- changed the course request ([ebea604](https://github.com/Secure-T-Team/edu-frontend/commit/ebea6046e09bd13e68f472a3775464aee9675319))
- changed the request queue in the statistics ([02c972e](https://github.com/Secure-T-Team/edu-frontend/commit/02c972e7fc293ad85252364a3a7c797c6a5ca0c5))
- changed to rest first step creating from ready-made courses ([501c64c](https://github.com/Secure-T-Team/edu-frontend/commit/501c64c508e5c2b8c4c4217d83d0e880545859d4))
- connect new domain request and corrected templates ([75b6033](https://github.com/Secure-T-Team/edu-frontend/commit/75b6033909bedf03e0643e96784e27ce9bb12fbc))
- connected active courses ([eb67fe4](https://github.com/Secure-T-Team/edu-frontend/commit/eb67fe441404db4dc88be4f9a812a02072f2a019))
- connected completing course ([a6be126](https://github.com/Secure-T-Team/edu-frontend/commit/a6be12666bd9c4b5e3014162bda794dd31d35eba))
- connected excel reports and group form ([d591dc9](https://github.com/Secure-T-Team/edu-frontend/commit/d591dc9c21f3b53a529d377a53a335fdb6983bd7))
- connected notify employess for assigned courses ([993682f](https://github.com/Secure-T-Team/edu-frontend/commit/993682f1c7faa6fc8c90328facc8e770162f440b))
- connected password checker ([c8491b0](https://github.com/Secure-T-Team/edu-frontend/commit/c8491b010fd87a6dcdb1cb672e41cb9ec5823d23))
- connected progress change for employee ([4d84633](https://github.com/Secure-T-Team/edu-frontend/commit/4d846339a494c1c1fe4db78fc1e28b010c389031))
- connected registration with rest queries ([ee45c9c](https://github.com/Secure-T-Team/edu-frontend/commit/ee45c9c3d297caa65ff2fdd5a5b0529abdd4d5bb))
- connected reports for children org and corrected table request ([6673e7f](https://github.com/Secure-T-Team/edu-frontend/commit/6673e7fbaa06ad1c5158eb05dafb7fa657b84306))
- connected reports requests ([da14bb4](https://github.com/Secure-T-Team/edu-frontend/commit/da14bb4c89dfe8826beda5d9aa442e8e91436f1c))
- connected request about organization ([10d8eb7](https://github.com/Secure-T-Team/edu-frontend/commit/10d8eb7d696c609dd5f625f662e8dc14e50aac28))
- connected request to generate pdf ([481e048](https://github.com/Secure-T-Team/edu-frontend/commit/481e04826ba546cd997e75f360a850ae391db88b))
- connected requests for obtaining statistics ([fa5ede4](https://github.com/Secure-T-Team/edu-frontend/commit/fa5ede414af5758527102ff65f94e6dc82b78c60))
- connected requests for user statistics ([deb929d](https://github.com/Secure-T-Team/edu-frontend/commit/deb929d192dfc7884a5764185dfa82b15063b651))
- copied modal ([45d4642](https://github.com/Secure-T-Team/edu-frontend/commit/45d4642a0bad5b2e7d20b8159a99185dd2d6c1e0))
- correct button loading ([06dcf16](https://github.com/Secure-T-Team/edu-frontend/commit/06dcf163de1ea0395a42b379b6f55dda1ee1a927))
- correct create template select ([b80cc69](https://github.com/Secure-T-Team/edu-frontend/commit/b80cc695fc14df00cc19e05eaac6b4fd310dd594))
- correct prefix slicer ([680aac7](https://github.com/Secure-T-Team/edu-frontend/commit/680aac72215fb07f16e42ab076351a5b443b8dbf))
- correct registration select ([0d8d183](https://github.com/Secure-T-Team/edu-frontend/commit/0d8d1832b2676c257b92a8fff75c022c6afaaf52))
- correct select arrow ([c615f67](https://github.com/Secure-T-Team/edu-frontend/commit/c615f67de442e66f83ccf0a4f5b9276de0cdb865))
- correct select arrow ([febfabe](https://github.com/Secure-T-Team/edu-frontend/commit/febfabebe2fe2ccb621701fd867b314384ebc60c))
- correct select for edit template ([88fc4f3](https://github.com/Secure-T-Team/edu-frontend/commit/88fc4f3fa11f5903be45732aa5eb7737f38b8a33))
- correct setvalue for registration ([b863c54](https://github.com/Secure-T-Team/edu-frontend/commit/b863c549bc7a5b3cde7a54c3cdc382a63d9f9c8d))
- correct tracker position ([5ca5d32](https://github.com/Secure-T-Team/edu-frontend/commit/5ca5d3226f7a762f367942c011ecc5a5c90d195c))
- correct validation settings ([7ca40b8](https://github.com/Secure-T-Team/edu-frontend/commit/7ca40b8174f73e42f33c940f418cdf22765bd870))
- correct yaxis values in chart ([815136a](https://github.com/Secure-T-Team/edu-frontend/commit/815136abc466990073b278797647a2ba5fdaf6ff))
- corrected context and add slider counter ([d9ebfea](https://github.com/Secure-T-Team/edu-frontend/commit/d9ebfea87dadb55d90adb2354615459c54b3e49a))
- corrected input ([387ed12](https://github.com/Secure-T-Team/edu-frontend/commit/387ed12670c1343d8d2f9c4103602dcb13493964))
- corrected request for creating courses~ ([280da3e](https://github.com/Secure-T-Team/edu-frontend/commit/280da3ee912e6ea126f52c310adf1fdef074b04e))
- corrected request for receiving courses ([0343f6b](https://github.com/Secure-T-Team/edu-frontend/commit/0343f6ba5e9f23906dff7e5e2e59e08b5da53ae0))
- corrected request function and connected new login ([1d492f5](https://github.com/Secure-T-Team/edu-frontend/commit/1d492f5bd8fe85f661ff230a482f0e3c6d90f5ff))
- corrected required fileds in registration ([7b589ab](https://github.com/Secure-T-Team/edu-frontend/commit/7b589ab5801e0b705d76be28f075ef66a4840455))
- corrected sender validator ([41fab23](https://github.com/Secure-T-Team/edu-frontend/commit/41fab239ffd6e2d8d19098155b0d944c2e63d39c))
- corrected statistics requests ([8892984](https://github.com/Secure-T-Team/edu-frontend/commit/8892984cf715099a0a364491d8207e306251083e))
- corrected url slicer for template ([5a376d9](https://github.com/Secure-T-Team/edu-frontend/commit/5a376d92d2970c71c554e4e4b69fbdb5dbecf1ea))
- corrected user types ([4cbe8f1](https://github.com/Secure-T-Team/edu-frontend/commit/4cbe8f19ab282b2f1e2ef439f0cfeaf50ea9fea4))
- correcter styles ([6cb1196](https://github.com/Secure-T-Team/edu-frontend/commit/6cb119688b15b05301d6a566c1a319731cccf55b))
- create custom YTick for chart and connect ([6b6a2c3](https://github.com/Secure-T-Team/edu-frontend/commit/6b6a2c3e046e8893abb7865730e0526b65dc557f))
- create new components and connect rtk query ([5bdd5a3](https://github.com/Secure-T-Team/edu-frontend/commit/5bdd5a318fc616b89a4bc044a2f836b9b53878e0))
- create services for modules page ([412a5d5](https://github.com/Secure-T-Team/edu-frontend/commit/412a5d51942db9c7eca0f41e72740c2c3f23c0a5))
- delete cursor pointer ([a70f7b1](https://github.com/Secure-T-Team/edu-frontend/commit/a70f7b10c89be9eef12878187faedb622636900b))
- delete cursor pointer ([019ed6a](https://github.com/Secure-T-Team/edu-frontend/commit/019ed6a3d24c24616e5c36607ad15118d083bd71))
- delete prefix for original url ([ee696a2](https://github.com/Secure-T-Team/edu-frontend/commit/ee696a217b5b6e166a09ebfd7cee35906439dede))
- delete required from create and edit slide ([f56552c](https://github.com/Secure-T-Team/edu-frontend/commit/f56552ca6d76a794ed40330be21cc513fddd3644))
- delete slide count ([6f8009b](https://github.com/Secure-T-Team/edu-frontend/commit/6f8009b901e97814a71ecd79749fe9c15c80c367))
- deleted the old data request ([795ad43](https://github.com/Secure-T-Team/edu-frontend/commit/795ad438ff453971448fbac3935fe6348eec0157))
- employees page and deleting extra code ([4f2803a](https://github.com/Secure-T-Team/edu-frontend/commit/4f2803a80733fc6a1308e0f94e86f5eecd808383))
- enabled getting color by risk level ([9a4c4c3](https://github.com/Secure-T-Team/edu-frontend/commit/9a4c4c3028c08b983db7c3cabe4452a99c25983a))
- enabled getting information about the organization ([ca466fb](https://github.com/Secure-T-Team/edu-frontend/commit/ca466fb989171069797cdb9aa9ac3f2142997d56))
- limited the number of concurrent requests ([b36701d](https://github.com/Secure-T-Team/edu-frontend/commit/b36701dba66d4bd197ef812667b1701471d5211e))
- made a blank for deleting courses ([a411655](https://github.com/Secure-T-Team/edu-frontend/commit/a4116556ca6d11a25b9d4bad5df644ee4bd103ac))
- made a request to delete course ([dcebf1e](https://github.com/Secure-T-Team/edu-frontend/commit/dcebf1e759ed41fbd1993ec39b19238f51884bf8))
- made completion of course and preparation for excel reports ([aa289a4](https://github.com/Secure-T-Team/edu-frontend/commit/aa289a449f0d458855cc326cb08b4638c3e67299))
- made request to assign course from modules ([ec09fc2](https://github.com/Secure-T-Team/edu-frontend/commit/ec09fc258bc28c402b529b6bb03dc6b83e03e4bc))
- made request to assign course from ready-made ([6c71e44](https://github.com/Secure-T-Team/edu-frontend/commit/6c71e4425e6b3951e57844b21887e1448edef8fb))
- made rest requests for auth, course, assigned course and personal ([865ab70](https://github.com/Secure-T-Team/edu-frontend/commit/865ab70bc19aca6076d144453f872971dbe76dac))
- made saving organization UUID in localstorage ([87162e8](https://github.com/Secure-T-Team/edu-frontend/commit/87162e8f7b1b68108db1b754915de1d83d1abf99))
- realize auto resize image for slider ([fdbcb85](https://github.com/Secure-T-Team/edu-frontend/commit/fdbcb85a68499dae8c3b2d4f370194847e4b65c3))
- realize employees and quiz page and delete extra code ([a9eb983](https://github.com/Secure-T-Team/edu-frontend/commit/a9eb9835a8aad3f0f714c67cf13ebe0dfef37774))
- realize module without slides ([e8acc2a](https://github.com/Secure-T-Team/edu-frontend/commit/e8acc2a34cf024c693ddc32b5b308b00db9c13bd))
- realize new login ([baa23d2](https://github.com/Secure-T-Team/edu-frontend/commit/baa23d290709b9e2de4a2a39172e269c9b4ad295))
- realized emplayee table ([cbfee56](https://github.com/Secure-T-Team/edu-frontend/commit/cbfee56d644fbec41502d6fa5b0c30fdbe895013))
- removed delete button from default department ([4d7c13d](https://github.com/Secure-T-Team/edu-frontend/commit/4d7c13d94dd3a3734c382f4716b9cdd105926d5a))
- rest queries for statistics pages ([734308d](https://github.com/Secure-T-Team/edu-frontend/commit/734308d60e1a3fe1254b64c0b6e158831fdda7ee))
- save changing ([3026ed7](https://github.com/Secure-T-Team/edu-frontend/commit/3026ed7c08474077e82c45359b00844fcc79f35d))
- saved base request ([54cb0ac](https://github.com/Secure-T-Team/edu-frontend/commit/54cb0aca6f1888b15c7a4c60e1cdd68ead42b8de))
- saved change ([82dbafb](https://github.com/Secure-T-Team/edu-frontend/commit/82dbafbf258fd9d41e2df645d667bf2b2b732914))
- saved change ([ac751f8](https://github.com/Secure-T-Team/edu-frontend/commit/ac751f80690d391e8cd0c212d8f0599717745c9d))
- super_user check in phishing templates ([#484](https://github.com/Secure-T-Team/edu-frontend/issues/484)) ([d3e6c70](https://github.com/Secure-T-Team/edu-frontend/commit/d3e6c70d9568f01e5dac2a3373de8f208e5d1c6d))
- update audio checker ([c9daf20](https://github.com/Secure-T-Team/edu-frontend/commit/c9daf20d0a49ae78042ad38d7c0c38dca6f1e6a1))
- update package lock ([be553ad](https://github.com/Secure-T-Team/edu-frontend/commit/be553ad885baaa370cceb9d43e572d8acc607df5))
- update url checker for templter ([641914c](https://github.com/Secure-T-Team/edu-frontend/commit/641914cecf0657d056d0f33d3cd57a09bda8c1b5))

### Bug Fixes

- add ? for id department ([6192850](https://github.com/Secure-T-Team/edu-frontend/commit/61928508a04111294b65722ce6eac1c5b1511348))
- added height limit for image in fullscreen ([20a3f55](https://github.com/Secure-T-Team/edu-frontend/commit/20a3f556c8bc54a0260cd48bb048576142802a9e))
- change old_id to uuid in employees page ([a89a7f5](https://github.com/Secure-T-Team/edu-frontend/commit/a89a7f51e50b7a5e315cd57e7bf12104ace130a8))
- correct body service ([004d8d7](https://github.com/Secure-T-Team/edu-frontend/commit/004d8d77783762ce30023145b10add6644f71721))
- correct button and input color ([978d1f4](https://github.com/Secure-T-Team/edu-frontend/commit/978d1f41ead74f5f9908314f152f6855665a043c))
- correct quiz ([d1de2c3](https://github.com/Secure-T-Team/edu-frontend/commit/d1de2c3fd77a1fb23e67204117d31c2c8426a1c3))
- correct quiz page ([5d5e011](https://github.com/Secure-T-Team/edu-frontend/commit/5d5e0112a04c1ff8316a25480f6a001c66a62ed2))
- correct quiz request ([2bcdaa0](https://github.com/Secure-T-Team/edu-frontend/commit/2bcdaa0d01b12dbec4181b51f38abeb7b4ff95d9))
- correct required fields ([c75d2e7](https://github.com/Secure-T-Team/edu-frontend/commit/c75d2e769c59fcba6514f4b9949a8135a7805c57))
- correct template request ([6b52ea5](https://github.com/Secure-T-Team/edu-frontend/commit/6b52ea5743261b194c5a9a315fd2096469f74fb7))
- correct url ([308485d](https://github.com/Secure-T-Team/edu-frontend/commit/308485d74c2c8e60635c7efbe545b061ccb2521d))
- correct url ([a081674](https://github.com/Secure-T-Team/edu-frontend/commit/a081674d7e0133819efa611cd2cadf2dcfd97d00))
- correct url request ([c802233](https://github.com/Secure-T-Team/edu-frontend/commit/c8022333059b97bc77a18fd4317ddb778350f03d))
- corrected departments request ([a648f4a](https://github.com/Secure-T-Team/edu-frontend/commit/a648f4a74e68f6ed3c0c5f4c221068f08f261fd1))
- corrected departments request ([e3bd813](https://github.com/Secure-T-Team/edu-frontend/commit/e3bd813b13b3f383e7f51f43b4b24b445bc1df1e))
- corrected display of graph ([5b671b6](https://github.com/Secure-T-Team/edu-frontend/commit/5b671b68f8fcf21aa5b276899894151c69edc86b))
- corrected employee edit request ([21299b1](https://github.com/Secure-T-Team/edu-frontend/commit/21299b173f9422ba6341b70ec8966226a5d1b80f))
- corrected employees request ([8518adf](https://github.com/Secure-T-Team/edu-frontend/commit/8518adfda77e128ef701f26b05256bd5053acfb1))
- corrected errors ([7d0d14e](https://github.com/Secure-T-Team/edu-frontend/commit/7d0d14e44c71f0c3edf92ce9f01e0b849ab5d0d6))
- corrected graph text ([12209af](https://github.com/Secure-T-Team/edu-frontend/commit/12209af46e859145e10b82c7692390ce8bdff850))
- corrected regex ([fa858ab](https://github.com/Secure-T-Team/edu-frontend/commit/fa858aba13c0429c9791486f97aa32a4f4e4929a))
- corrected request ([b27b2bf](https://github.com/Secure-T-Team/edu-frontend/commit/b27b2bf105ac8de08719da506101b8c2153ed26b))
- corrected request path ([ab22bd4](https://github.com/Secure-T-Team/edu-frontend/commit/ab22bd41d7c6e7712b97e49cb9f8faa1077f5957))
- corrected requests link ([346009f](https://github.com/Secure-T-Team/edu-frontend/commit/346009f9ae036cc67e338146a5429c54310e48e6))
- corrected table styles ([f1a15ae](https://github.com/Secure-T-Team/edu-frontend/commit/f1a15aeb76dc23d7cdb91bee4894899b72b8d6d3))
- corrected the request condition ([d3c8066](https://github.com/Secure-T-Team/edu-frontend/commit/d3c80664cead3c2c43c594a71972fecc08110a15))
- corrected word ([f7b293e](https://github.com/Secure-T-Team/edu-frontend/commit/f7b293e1347983481997e88ab10e121ef9f66a98))
- deleted extra code ([2d907fe](https://github.com/Secure-T-Team/edu-frontend/commit/2d907fefc2237caec800707e233167453329602c))
- fix bugs and delete extra code ([51286c6](https://github.com/Secure-T-Team/edu-frontend/commit/51286c6188973d22d0c94e019ebec769cbd6e881))
- fixed merge conflicts ([55863f3](https://github.com/Secure-T-Team/edu-frontend/commit/55863f3dca2fa0d15e6dd98ed7517b8f1494785b))
- fixed refactoring bugs ([5400760](https://github.com/Secure-T-Team/edu-frontend/commit/5400760e897eb0c1fd91ac9c5087d22c03cd7be0))
- fixed sync with ad ([3897c43](https://github.com/Secure-T-Team/edu-frontend/commit/3897c432dcbad3df048c30357908d32b64b13daf))
- **tip:** change tip message ([cc0bc1f](https://github.com/Secure-T-Team/edu-frontend/commit/cc0bc1f0cdc6466ac8541015918072507d76ab7e))

### Refactoring

- comment mobile alert ([18cdabf](https://github.com/Secure-T-Team/edu-frontend/commit/18cdabfaecb887bd8f27b04e9350121326ad39d6))
- correted report function ([e1c1b91](https://github.com/Secure-T-Team/edu-frontend/commit/e1c1b9146d1d38b38d1b1ebd7f0075e2d2f4d5cd))
- delete default value for password ([c7cd367](https://github.com/Secure-T-Team/edu-frontend/commit/c7cd36763414c7e201f98c073b3e6213d08267e6))
- delete extra code ([253581b](https://github.com/Secure-T-Team/edu-frontend/commit/253581b7d1fad86c828cfc8f9eb6ec6424ccb49b))
- deleted extra line ([64ff1c9](https://github.com/Secure-T-Team/edu-frontend/commit/64ff1c90a0885d4c07c6d6d32dfa43303b7685d3))
- deleted extra line ([a552ba7](https://github.com/Secure-T-Team/edu-frontend/commit/a552ba7503aef91bcae1090bda68e2af7495f8c5))
- deleted extra lines ([e17587b](https://github.com/Secure-T-Team/edu-frontend/commit/e17587ba33a2c86446a86a2fb61ec0b095f18008))
- fix bugs and delete extra code ([3612d0c](https://github.com/Secure-T-Team/edu-frontend/commit/3612d0c8bc4976c59333a127681df6fc0be330a0))

## [2.28.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.27.0...v2.28.0) (2023-04-03)

### Features

- added lowercase email for request ([#542](https://github.com/Secure-T-Team/edu-frontend/issues/542)) ([d8c0f78](https://github.com/Secure-T-Team/edu-frontend/commit/d8c0f78633b02ecb4fcd5905f5e6b8beaa90c395))

## [2.27.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.26.0...v2.27.0) (2023-04-03)

### Features

- added reload after policy confirmation ([f5b6625](https://github.com/Secure-T-Team/edu-frontend/commit/f5b6625219eaa597752fc865aef458253da93f5e))

## [2.26.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.25.0...v2.26.0) (2023-03-16)

## [2.27.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.26.0...v2.27.0) (2023-03-20)

### Features

- added reload after policy confirmation ([f5b6625](https://github.com/Secure-T-Team/edu-frontend/commit/f5b6625219eaa597752fc865aef458253da93f5e))

## [2.26.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.25.0...v2.26.0) (2023-03-16)

### Features

- add custom logo sizes for different domains ([140c679](https://github.com/Secure-T-Team/edu-frontend/commit/140c6795851ed9f8ec42ebab874c39d62cc0a710))

### Refactoring

- delete extra code ([d8fc983](https://github.com/Secure-T-Team/edu-frontend/commit/d8fc983e0f052a4c83c0ad406e1b9bb749170c95))
- delete moc data ([8611b59](https://github.com/Secure-T-Team/edu-frontend/commit/8611b59b99309b7d243834feb8bb511c7dabcd24))

## [2.25.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.24.2...v2.25.0) (2023-02-21)

### Features

- removed percentages in statistics tables ([5303669](https://github.com/Secure-T-Team/edu-frontend/commit/53036690e08860052c3c9cb561b0bc0248715fe1))

### [2.24.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.24.1...v2.24.2) (2023-02-07)

### Bug Fixes

- added height limit for image in fullscreen ([be4ccb8](https://github.com/Secure-T-Team/edu-frontend/commit/be4ccb8be15e8c00219ea239f8487896844386e5))

### [2.24.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.24.0...v2.24.1) (2023-02-06)

### Bug Fixes

- added slide height limit and styles for lists ([9c8e769](https://github.com/Secure-T-Team/edu-frontend/commit/9c8e769b06986a30c228336c9eefaecfdaf5a743))

## [2.24.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.23.0...v2.24.0) (2023-01-19)

### Features

- added new svg icons ([84ea121](https://github.com/Secure-T-Team/edu-frontend/commit/84ea121b1fbf3380e97143a51b1e2d586cc7989d))
- creating default tags ([f45b390](https://github.com/Secure-T-Team/edu-frontend/commit/f45b3901c42a2c449d7bdab90c4a2706254212d7))
- redesign personnel selection form ([ffb3787](https://github.com/Secure-T-Team/edu-frontend/commit/ffb3787aec45ff3e64a116d49eef1edae7267b9b))

## [2.23.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.22.0...v2.23.0) (2023-01-16)

### Features

- added link styles ([f9ad20d](https://github.com/Secure-T-Team/edu-frontend/commit/f9ad20da2f095357fbe578bf2aea85961a3ff818))

## [2.22.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.21.0...v2.22.0) (2022-12-16)

### Features

- added hint for tracker switcher ([ef5a011](https://github.com/Secure-T-Team/edu-frontend/commit/ef5a0118ea4adda305bfe1781c2a0fd6f07be20b))
- added redirect upon successful testing ([015fff7](https://github.com/Secure-T-Team/edu-frontend/commit/015fff7eedd46bfb8fe9a0c30f283665cfec921b))
- processing data ([11ee753](https://github.com/Secure-T-Team/edu-frontend/commit/11ee753f791666301e15982364d48d9ad36494f0))
- styles for inputs and textarea on tags page ([4f2773c](https://github.com/Secure-T-Team/edu-frontend/commit/4f2773c5c46c2d7212e1e64cd6128160d0756ed5))
- tracker on edit email page ([dc7c9e4](https://github.com/Secure-T-Team/edu-frontend/commit/dc7c9e459f93dfc95812a9e98e66d4230865d60f))

### Bug Fixes

- changed percentage rounding ([9f6f0e0](https://github.com/Secure-T-Team/edu-frontend/commit/9f6f0e076198fe3f7dfd6845bbc88fe0ce76a223))

## [2.21.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.20.2...v2.21.0) (2022-11-29)

### Features

- added selector with domains to editing phishing templates ([48a7058](https://github.com/Secure-T-Team/edu-frontend/commit/48a705857f2fec80a681d70c7eb826e1e26da8a8))

### Bug Fixes

- corrected words ([321bdff](https://github.com/Secure-T-Team/edu-frontend/commit/321bdff393032d36a6bb7df0614572b6b58374d7))

### [2.20.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.20.1...v2.20.2) (2022-11-28)

### Bug Fixes

- changed request to get user tags ([80f1e74](https://github.com/Secure-T-Team/edu-frontend/commit/80f1e744398c9893c50ea0dc581daed363990ade))

### [2.20.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.20.0...v2.20.1) (2022-11-25)

### Bug Fixes

- changed date checker ([5d2f5de](https://github.com/Secure-T-Team/edu-frontend/commit/5d2f5de87e2b03eb61674a5b5a2bdead6a0a97e0))

## [2.20.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.19.0...v2.20.0) (2022-11-25)

### Features

- tags for staff page and some fixes ([bd579cf](https://github.com/Secure-T-Team/edu-frontend/commit/bd579cf2eaa3523c87e8e1384e3fa7f8ab873b1c))
- tags page ([b34efa7](https://github.com/Secure-T-Team/edu-frontend/commit/b34efa77f29bee79fa3f33d772ff3794a942b350))
- user registration with new requests ([0204028](https://github.com/Secure-T-Team/edu-frontend/commit/0204028a689bc6051e270494596ff22c03c360f4))

### Bug Fixes

- added stoping loader after error ([a1fb630](https://github.com/Secure-T-Team/edu-frontend/commit/a1fb630fe24b8f68a8ec5abf06a9522e2b5af6b4))
- fixed action creating ([1fec441](https://github.com/Secure-T-Team/edu-frontend/commit/1fec441cfdc5abc04d5ba720a2f561fcb32a6450))

## [2.19.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.18.0...v2.19.0) (2022-10-16)

### Features

- add local run commands ([5b3f0e8](https://github.com/Secure-T-Team/edu-frontend/commit/5b3f0e8a656e05ab8621767fceadba062fc81f41))
- ssl downloader ([7bc2db7](https://github.com/Secure-T-Team/edu-frontend/commit/7bc2db707c439efa3a28763059f4e7c01a653ebf))
- unknown mail status ([61eb2a2](https://github.com/Secure-T-Team/edu-frontend/commit/61eb2a2d0f3008de04d2d122e39149a0dcca599b))

### Bug Fixes

- fixed bug request ([958f578](https://github.com/Secure-T-Team/edu-frontend/commit/958f5780ebcb69d3556a6982a18cad24c2317a59))

## [2.18.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.17.0...v2.18.0) (2022-10-05)

### Features

- add custom value in header for test ([f082fa1](https://github.com/Secure-T-Team/edu-frontend/commit/f082fa1ca44d8b19f8c5ea00605672cec971991b))
- add date formater without timezone ([4b005c2](https://github.com/Secure-T-Team/edu-frontend/commit/4b005c2ddcad803e181b5c25273c6f5ca817519b))
- add filter for org list ([7e37207](https://github.com/Secure-T-Team/edu-frontend/commit/7e37207e10522e6999d2edda3f2c8ad8591484a5))
- add selected title for course selector ([1e15c3a](https://github.com/Secure-T-Team/edu-frontend/commit/1e15c3ad8af400b03f491436c2f3d65128000f9d))
- add selected view for course selector ([930b6fc](https://github.com/Secure-T-Team/edu-frontend/commit/930b6fc84910a1e812226777e850287fd872026d))
- add word changing ([d598442](https://github.com/Secure-T-Team/edu-frontend/commit/d5984422e3ae383f0871609cc533f32d9d591393))
- added slide download functionality ([fbd1f71](https://github.com/Secure-T-Team/edu-frontend/commit/fbd1f71b033a7ffbff20681c3c1599cb6f38e3a5))
- correct header request ([66f9f1f](https://github.com/Secure-T-Team/edu-frontend/commit/66f9f1f15b5a87feee226c22563d622e66dc0b63))
- delete header for request another ([79832c3](https://github.com/Secure-T-Team/edu-frontend/commit/79832c380ec9da6110f8c7eb02c0a48efe5be926))
- realize new slide download ([e7cbe55](https://github.com/Secure-T-Team/edu-frontend/commit/e7cbe5542afaf2fd8d1ae76cb3433ca6707ee5f8))

### Bug Fixes

- correct avatar drawing with () ([6c37158](https://github.com/Secure-T-Team/edu-frontend/commit/6c37158971e20b47577a7117970bfea04ec5055e))
- correct header ([7c13995](https://github.com/Secure-T-Team/edu-frontend/commit/7c13995203c5cd8964515198cf49d73a65b123e9))
- correct testing header ([0c0cefa](https://github.com/Secure-T-Team/edu-frontend/commit/0c0cefa2a43dae06657fa2473c1857d3013c8657))
- rename placeholder ([7d11d14](https://github.com/Secure-T-Team/edu-frontend/commit/7d11d147f13f241da27183f8fc1d00bb2683d86d))
- rename titles ([80f0f3f](https://github.com/Secure-T-Team/edu-frontend/commit/80f0f3fcc929c96a48ab6e793420c6d1517bccdb))

## [2.17.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.16.0...v2.17.0) (2022-09-29)

### Features

- add filter for org list ([5802ea2](https://github.com/Secure-T-Team/edu-frontend/commit/5802ea241d7440a44183044db86e9d43305d394c))

### Bug Fixes

- correct avatar drawing with () ([907e8d9](https://github.com/Secure-T-Team/edu-frontend/commit/907e8d907f1236ab3e824fcf11941f114a04459e))

## [2.16.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.15.0...v2.16.0) (2022-09-28)

### Features

- added autoreplacement avatar ([177a602](https://github.com/Secure-T-Team/edu-frontend/commit/177a602dd5a4944a32be3a221db9dce005899839))

### Bug Fixes

- fix upload avatar ([e5671bc](https://github.com/Secure-T-Team/edu-frontend/commit/e5671bc1e63a98fa390a2c9872f72df12ad8b139))

## [2.15.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.14.1...v2.15.0) (2022-09-09)

### Features

- add license page ([df94030](https://github.com/Secure-T-Team/edu-frontend/commit/df94030d4c1e7e0ce928cc839c2e4d0d3f1fd3a5))
- create auto_assign config and fix drone ([b83bf5a](https://github.com/Secure-T-Team/edu-frontend/commit/b83bf5a7ff4e3839221f48dd0181064360dc0df1))
- delete extra margin in license card ([2fcb3d2](https://github.com/Secure-T-Team/edu-frontend/commit/2fcb3d2cac15bd9f4b5d7c5499cbc42684d07157))
- **organization:** add current license page ([b8be93f](https://github.com/Secure-T-Team/edu-frontend/commit/b8be93f3e9c539ceb69ea5b3c1a060cbddfc3786))
- **organization:** add current license page ([a9b48ad](https://github.com/Secure-T-Team/edu-frontend/commit/a9b48add417d852691ae3ddf1bff437e1aad55dd))

### Bug Fixes

- correct card width ([d9795c6](https://github.com/Secure-T-Team/edu-frontend/commit/d9795c63a94546fbb16100d8c31f053eea9bba0a))
- fixed adding 3 hours to course time ([c23e303](https://github.com/Secure-T-Team/edu-frontend/commit/c23e303cbdb2c0e5fcaaca55428daee865a91854))

### [2.14.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.14.0...v2.14.1) (2022-09-07)

### Bug Fixes

- fixed adding 3 hours to course time ([4190ff6](https://github.com/Secure-T-Team/edu-frontend/commit/4190ff6c32e2f72bde3c9d0aa111a6fd6e0516f5))

## [2.14.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.13.3...v2.14.0) (2022-09-06)

### Features

- update drone config ([c723442](https://github.com/Secure-T-Team/edu-frontend/commit/c7234424743e44e72cd95b863bba37db458bc4d8))
- update drone config ([6432b03](https://github.com/Secure-T-Team/edu-frontend/commit/6432b038ca9eb13c3a0780ccfa5938511118f472))

### [2.13.3](https://github.com/Secure-T-Team/edu-frontend/compare/v2.13.2...v2.13.3) (2022-07-15)

### Refactoring

- remove default domain ([f960a6e](https://github.com/Secure-T-Team/edu-frontend/commit/f960a6ece60e7bc85612bdf1275899956466ee89))

### [2.13.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.13.1...v2.13.2) (2022-07-11)

### Bug Fixes

- pass organization_id when query progress groups users for subsidiary organization ([4dac8b3](https://github.com/Secure-T-Team/edu-frontend/commit/4dac8b39d64585cedc681f9ddaeb8aa542ee4385))

### [2.13.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.13.0...v2.13.1) (2022-06-17)

### Bug Fixes

- Исправлена валидация отправки письма по окончании рассылки ([0f82cc2](https://github.com/Secure-T-Team/edu-frontend/commit/0f82cc2cc96ae88e26ae37f53b848cd530d8a93e))

## [2.13.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.12.3...v2.13.0) (2022-06-15)

### Features

- Добавлены настройки отправки писем по окончании рассылки ([99a1484](https://github.com/Secure-T-Team/edu-frontend/commit/99a1484094176ed344a7f4a3b0beeaea340dfc40))

### [2.12.3](https://github.com/Secure-T-Team/edu-frontend/compare/v2.12.2...v2.12.3) (2022-06-14)

### [2.12.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.12.1...v2.12.2) (2022-06-14)

### Refactoring

- test xss mitigation ([f3ccfcc](https://github.com/Secure-T-Team/edu-frontend/commit/f3ccfcc751037044395e614b3018075ca5156b97))

### [2.12.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.12.0...v2.12.1) (2022-06-07)

### Refactoring

- Изменен текст кнопки ([821d668](https://github.com/Secure-T-Team/edu-frontend/commit/821d668648fb3bdc10e07a1b6d24f88c66820156))
- Изменил текст карточек лицензий в оргструктуре ([22234a3](https://github.com/Secure-T-Team/edu-frontend/commit/22234a388983309c3fd4e9b5675d0d8c78b3c8ac))

## [2.12.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.11.0...v2.12.0) (2022-05-23)

### Features

- Добавлена загрузка на страницу сотрудников ([f91209f](https://github.com/Secure-T-Team/edu-frontend/commit/f91209f732e8f854e22b2703381f7163290754d3))
- Изменена иконка добавления курса ([3ccd85e](https://github.com/Secure-T-Team/edu-frontend/commit/3ccd85ed504608d73b4f688eae099d18abe29567))
- Изменены отступы в шаблонах в рассылке ([d8e59eb](https://github.com/Secure-T-Team/edu-frontend/commit/d8e59eb086930c1d8d3334b5096a4bbbcf7a2ac7))
- Обновленена иконка календарика ([8f337bb](https://github.com/Secure-T-Team/edu-frontend/commit/8f337bbd04b274d4d440ca6c5202a089ef615e4b))
- Обновлены отступы у вкладок ([9e4770c](https://github.com/Secure-T-Team/edu-frontend/commit/9e4770cf04d04d63ad43907699cc9ffa92fb1390))
- Обновлены цвета статистики ([b5a42a7](https://github.com/Secure-T-Team/edu-frontend/commit/b5a42a7c2c79db704da1ee67794c86b77877e77a))

### Refactoring

- **phishing:** optional position ([7d6bff4](https://github.com/Secure-T-Team/edu-frontend/commit/7d6bff442f21e6862a4a420ffa6cd5de6c0acca8))

## [2.11.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.10.0...v2.11.0) (2022-04-21)

### Features

- Добавлен поиск по юзерам ([f5b07e8](https://github.com/Secure-T-Team/edu-frontend/commit/f5b07e8e2c63fed3a74581cc7ab7372863e49400))
- Добавлено поле default_domain ([c9dd83f](https://github.com/Secure-T-Team/edu-frontend/commit/c9dd83f1a83b82d39eb4fef2249b589bc4c31050))

### Refactoring

- update README ([e5a063d](https://github.com/Secure-T-Team/edu-frontend/commit/e5a063d60e1a6abecbbdd03bf381a48898091b68))
- Добавлены переводы ([0bd1826](https://github.com/Secure-T-Team/edu-frontend/commit/0bd1826759b8f539eb21bffdc2f291cc4263ab20))

## [2.10.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.9.0...v2.10.0) (2022-04-21)

### Features

- Добавлено поле default_domain ([9367f1d](https://github.com/Secure-T-Team/edu-frontend/commit/9367f1d777500f7ea9e20d3d7238cbc6706e3a80))

## [2.9.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.8.3...v2.9.0) (2022-04-19)

### Features

- Добавлен параметр send_invites в синхронизации ad ([7dbad28](https://github.com/Secure-T-Team/edu-frontend/commit/7dbad28a0de6e0a7deb78235175219ea6fd990a9))

### [2.8.3](https://github.com/Secure-T-Team/edu-frontend/compare/v2.8.2...v2.8.3) (2022-04-18)

### Bug Fixes

- remove another mock ([7630d8f](https://github.com/Secure-T-Team/edu-frontend/commit/7630d8f8e5c5cdcf0884469645a52f72612d1f82))

### [2.8.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.8.1...v2.8.2) (2022-04-18)

### Bug Fixes

- remove ad data mock ([fc09e6f](https://github.com/Secure-T-Team/edu-frontend/commit/fc09e6f12a7ff69aa9b19b862d9c649025340d42))

### [2.8.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.8.0...v2.8.1) (2022-04-17)

### Bug Fixes

- Исправлено сохранение настроек ldap ([34f83b1](https://github.com/Secure-T-Team/edu-frontend/commit/34f83b159fc000b79eecadd60489656859bc1f9e))

## [2.8.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.7.3...v2.8.0) (2022-04-17)

### Features

- Добавлены настройки LDAP и SMTP ([0008713](https://github.com/Secure-T-Team/edu-frontend/commit/00087133c055284674c4601373a72130ae3ea1a1))
- Добавлены общие настройки и загрузка кейтаба ([a0971ad](https://github.com/Secure-T-Team/edu-frontend/commit/a0971ad80d7d23ec7092aaba5a4d9c673fe68a8c))

### [2.7.3](https://github.com/Secure-T-Team/edu-frontend/compare/v2.7.2...v2.7.3) (2022-04-16)

### Bug Fixes

- Исправлены таблицы в статистике организации ([e18f7fc](https://github.com/Secure-T-Team/edu-frontend/commit/e18f7fcc368c2085f152a6e5e25ff7c5406584e8))

### [2.7.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.7.1...v2.7.2) (2022-04-14)

### Bug Fixes

- fix list perfomance ([4ce016c](https://github.com/Secure-T-Team/edu-frontend/commit/4ce016c8a561eb06985102568b296dddb03e3701))

### [2.7.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.7.0...v2.7.1) (2022-04-14)

### Bug Fixes

- Исправлено удаление сотрудников в назначенном курсе ([3e80403](https://github.com/Secure-T-Team/edu-frontend/commit/3e804031760aaac20392bd2776bec1239a20afe4))

## [2.7.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.6.0...v2.7.0) (2022-04-13)

### Features

- add sso check ([58ca074](https://github.com/Secure-T-Team/edu-frontend/commit/58ca074f3f5db72a5b2654439dc69a4164f287c2))
- Добавлена кнопка "Вход через AD" ([463ef9d](https://github.com/Secure-T-Team/edu-frontend/commit/463ef9d03db2ed5678ad85385b10c3cc05912546))
- Добавлена обработка ошибок логина через ldap ([25ea974](https://github.com/Secure-T-Team/edu-frontend/commit/25ea9744f22029cbf10a739847f3e55772770afe))
- Добавлена синхронизация с AD ([d7b38db](https://github.com/Secure-T-Team/edu-frontend/commit/d7b38db18ce6377dfc9030feb459289f39a55f18))
- Добавлена сквозная аутентификация ([b66b15c](https://github.com/Secure-T-Team/edu-frontend/commit/b66b15c38fbe5846a93a457f86c53f2102f88641))
- Правки офлайна ([f1eaa39](https://github.com/Secure-T-Team/edu-frontend/commit/f1eaa3986679a916b987d3053207b2a8ca6deb4f))
- Удаление настроек kerberos ([32a7e30](https://github.com/Secure-T-Team/edu-frontend/commit/32a7e307e29f05abbdf1ef31735b5079c35c1a43))

### Bug Fixes

- add headers print ([f29c64a](https://github.com/Secure-T-Team/edu-frontend/commit/f29c64aefbfabf1d8b531c3c2141fd395c8089f6))
- fix ad-login schema ([13cc0c2](https://github.com/Secure-T-Team/edu-frontend/commit/13cc0c24b353306941ae5871d4f7291da85778c4))
- fix header name ([fc2d73f](https://github.com/Secure-T-Team/edu-frontend/commit/fc2d73fd29812cf2e31410f7e1f124da9897d73a))
- fix server build ([fd9bc1a](https://github.com/Secure-T-Team/edu-frontend/commit/fd9bc1ac77b359d32d4372b3b2bbc74c76f4652e))

### Refactoring

- auth refactoring ([c0f733b](https://github.com/Secure-T-Team/edu-frontend/commit/c0f733bfc716056742f59ecd5a80ef1789f99835))

## [2.6.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.5.1...v2.6.0) (2022-03-16)

### Features

- add favicon ([9813903](https://github.com/Secure-T-Team/edu-frontend/commit/9813903e224b1527813f8176e553a1cead62d1c6))
- start config ([6caeea6](https://github.com/Secure-T-Team/edu-frontend/commit/6caeea62098fa44035870489c00acae6235b48b7))

### Bug Fixes

- fix favicons paths ([264c0f7](https://github.com/Secure-T-Team/edu-frontend/commit/264c0f7e65159d66d6fbd75a1da6943e100d0131))
- fix ie ([f3d0932](https://github.com/Secure-T-Team/edu-frontend/commit/f3d0932625f3a7aa47ff8311e45e21b5da78e168))
- Исправлено прыгание графиков ([2d936a6](https://github.com/Secure-T-Team/edu-frontend/commit/2d936a6aacaf7af8cca3aa6be04d168b34b0b5e9))
- Правки переводов ([07a1325](https://github.com/Secure-T-Team/edu-frontend/commit/07a1325ccbaf20485ec234d6328086607cfa732e))

### Refactoring

- refactor settings permissions ([80fc88d](https://github.com/Secure-T-Team/edu-frontend/commit/80fc88de409129ecde05764ff8c940d6db11f4dc))
- Рефакторинг конфига ([b00c54d](https://github.com/Secure-T-Team/edu-frontend/commit/b00c54d311b6b6f9be7ead265e7ebd08ed9b4754))

### [2.5.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.5.0...v2.5.1) (2022-03-10)

### Bug Fixes

- fix favicon ([60650bc](https://github.com/Secure-T-Team/edu-frontend/commit/60650bc9bc5f2fb94a740c9f65559955a2bc0330))

## [2.5.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.4.1...v2.5.0) (2022-03-10)

### Features

- add favicon ([4b87b92](https://github.com/Secure-T-Team/edu-frontend/commit/4b87b929d7248c48c7eb31f62fefe7214495cc8b))

### Bug Fixes

- fix favicons paths ([420bd65](https://github.com/Secure-T-Team/edu-frontend/commit/420bd65c97a763dd61510d4a9c9fc2e13b8ea9d8))

### [2.4.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.4.0...v2.4.1) (2022-03-03)

### Bug Fixes

- Улучшение производительности картинок в курсах ([fb7fe49](https://github.com/Secure-T-Team/edu-frontend/commit/fb7fe490c511dad5a5449510a007975a7868faa4))

## [2.4.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.3.0...v2.4.0) (2022-03-03)

### Features

- Возвращена вкладка отделы в статистике по рассылке ([ef09e5d](https://github.com/Secure-T-Team/edu-frontend/commit/ef09e5d16b48faae553a683a6443508ef9840f39))
- Добавлена аналитика ([f18a0c2](https://github.com/Secure-T-Team/edu-frontend/commit/f18a0c2394d8fa7e7ea34fe66e1d6a99555c54b0))
- добавлена аналитика роли пользователя ([fa783e5](https://github.com/Secure-T-Team/edu-frontend/commit/fa783e56c20d1169a69e2c77c4bf878ce8e19c55))
- Добавлены больше ивентов аналитики ([a8f1ef7](https://github.com/Secure-T-Team/edu-frontend/commit/a8f1ef7544561dddcda3ed9c6ebffa36ac237f66))

### Bug Fixes

- fix back button ([e16cb28](https://github.com/Secure-T-Team/edu-frontend/commit/e16cb2839994c0d0f9419d7869ba000bc1e41f37))
- fix metrica id ([33ff474](https://github.com/Secure-T-Team/edu-frontend/commit/33ff47442e184bbc706f3c694f6799c166a1f18a))
- Исправлена проблема отображения результатов тестирования ([4b9fe48](https://github.com/Secure-T-Team/edu-frontend/commit/4b9fe48ae6c5485df24b91c0f47cbf8483a8bf7b))
- Исправлено отображение "нет данных" ([5f12df9](https://github.com/Secure-T-Team/edu-frontend/commit/5f12df941249ec75f4d1d73b1c1d33f08eb45518))

## [2.3.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.2.2...v2.3.0) (2022-02-24)

### Features

- Редизайн карточек фишинга ([ee50ba3](https://github.com/Secure-T-Team/edu-frontend/commit/ee50ba35d84ed43650b287f19d7fe90eb722f2d4))

### [2.2.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.2.1...v2.2.2) (2022-02-24)

### Bug Fixes

- Исправлен компонент Switch ([ab3adb9](https://github.com/Secure-T-Team/edu-frontend/commit/ab3adb97781b24d7e8f1b199a746e53c1c03fca3))

### [2.2.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.2.0...v2.2.1) (2022-02-23)

### Bug Fixes

- Исправлена ссылка на шрифт ([dc93010](https://github.com/Secure-T-Team/edu-frontend/commit/dc930103193ff2a53df937bc0fbfd11f7fca951c))

## [2.2.0](https://github.com/Secure-T-Team/edu-frontend/compare/v2.1.4...v2.2.0) (2022-02-23)

### Features

- Добавлена аналитика ([5f2542a](https://github.com/Secure-T-Team/edu-frontend/commit/5f2542a442680745a805045d241214d6fdfdea4a))
- Добавлено апи для получения сотрудников внутри рассылки ([f37aa30](https://github.com/Secure-T-Team/edu-frontend/commit/f37aa301388b203f1c25c949df66615950aad301))

### [2.1.4](https://github.com/Secure-T-Team/edu-frontend/compare/v2.1.3...v2.1.4) (2022-02-23)

### Bug Fixes

- Исправлен перевод в диалоге просмотра сотрудников в группе ([288b1e1](https://github.com/Secure-T-Team/edu-frontend/commit/288b1e1a9a2f1c66929422bcdb8e72ab99c9823d))

### [2.1.3](https://github.com/Secure-T-Team/edu-frontend/compare/v2.1.2...v2.1.3) (2022-02-22)

### [2.1.2](https://github.com/Secure-T-Team/edu-frontend/compare/v2.1.1...v2.1.2) (2022-02-22)

### [2.1.1](https://github.com/Secure-T-Team/edu-frontend/compare/v2.1.0...v2.1.1) (2022-02-22)

## [2.1.0](https://github.com/Secure-T-Team/edu-frontend/compare/v1.4.2...v2.1.0) (2022-02-22)

### Features

- add audit request ([881dbb0](https://github.com/Secure-T-Team/edu-frontend/commit/881dbb0578ddca68806c3aeea985e0bcd1032fc0))
- add base ssr and handlebars ([a16840e](https://github.com/Secure-T-Team/edu-frontend/commit/a16840e7313b6c6fc25f19ebb4fa61d451985bb3))
- add color palette ([ec6736d](https://github.com/Secure-T-Team/edu-frontend/commit/ec6736db991758482d6b62b3c9f1cf974d7721e8))
- add css vars ([024ca2e](https://github.com/Secure-T-Team/edu-frontend/commit/024ca2e950c2c181bd6d4fab15d30dad47101b8f))
- add deployUrl ([e56a7b4](https://github.com/Secure-T-Team/edu-frontend/commit/e56a7b4d8a00c63c0104a9d25c274f8a722a27fb))
- add employees pages ([d9fa52c](https://github.com/Secure-T-Team/edu-frontend/commit/d9fa52c9b7fb0f9b680a0a65092b3ecd47fa229d))
- add IE support ([ed062c1](https://github.com/Secure-T-Team/edu-frontend/commit/ed062c1c3c175485cda58796d9f15a19f03cfc52))
- add netrowks ([27dbae1](https://github.com/Secure-T-Team/edu-frontend/commit/27dbae154cebbaa29ed9afd1481be1e79141938a))
- add permission service ([0348997](https://github.com/Secure-T-Team/edu-frontend/commit/0348997d2981edc1bd9a832e74bcf071bb2c5601))
- add scroll restoration ([976a915](https://github.com/Secure-T-Team/edu-frontend/commit/976a915da3bf1cc473b28a6db13a44f705faf818))
- add storybook ([a1029b2](https://github.com/Secure-T-Team/edu-frontend/commit/a1029b25d7b23e8963622f488417ad356821f959))
- auth ([828ac31](https://github.com/Secure-T-Team/edu-frontend/commit/828ac3162d4bf5c369064e461c6fcfb0ff6c3773))
- desktopRequiredGuard и улучшения мобилки ([34647f6](https://github.com/Secure-T-Team/edu-frontend/commit/34647f619821eed81000ee302b941f5cc677521a))
- drop IE10 support ([4500eb3](https://github.com/Secure-T-Team/edu-frontend/commit/4500eb3e180359633b4347973c8b71d09120468d))
- fix not-found page ([b38f299](https://github.com/Secure-T-Team/edu-frontend/commit/b38f299dc0f72c02a377dbd85e3d764cd25ea22f))
- permissions request ([adfa04f](https://github.com/Secure-T-Team/edu-frontend/commit/adfa04f6f44b3b5363df68882cf6a8ebe792d577))
- remove categories mock ([30c5ff1](https://github.com/Secure-T-Team/edu-frontend/commit/30c5ff1226741b60644c1eb3a75b312cb74dc5e1))
- set baseHref=/lk ([38eb952](https://github.com/Secure-T-Team/edu-frontend/commit/38eb9527227ebf079e442beb3102bf3e0345b178))
- sidebar ([38e8f07](https://github.com/Secure-T-Team/edu-frontend/commit/38e8f074ff060beec3d0c6e99215fa63895ee571))
- sidebar fixes ([5e03518](https://github.com/Secure-T-Team/edu-frontend/commit/5e03518f5423df6887fc6909b9db426669183d7d))
- update server ([8baa2d3](https://github.com/Secure-T-Team/edu-frontend/commit/8baa2d3dd32fcc4cb11668e6423205e10101f587))
- Базовая реализация таблицы логов ([9873d17](https://github.com/Secure-T-Team/edu-frontend/commit/9873d175346bc1a60ad879207a411e4a06716027))
- В аудит добавлена пагинация ([f937803](https://github.com/Secure-T-Team/edu-frontend/commit/f9378039b64958dc3dcfbd8c92b0af13d6c2e8d6))
- В рассылку добавлен просмотр курса ([a1c9d88](https://github.com/Secure-T-Team/edu-frontend/commit/a1c9d883941d1997b4dcf5fd47c0d5b3de87d437))
- всякий там фишинг ([ff6e189](https://github.com/Secure-T-Team/edu-frontend/commit/ff6e1893e90679e83342f4035d7ef5743a9e7519))
- всякий фишинг ([875b17f](https://github.com/Secure-T-Team/edu-frontend/commit/875b17fef622c16f9c41bbbacfa3f6246123132e))
- добавлен api фишингового редактора ([50f7551](https://github.com/Secure-T-Team/edu-frontend/commit/50f755100c58bccff1a790e9e0ecdb01d2d107fe))
- Добавлен query-параметр lang ([fb2a6ab](https://github.com/Secure-T-Team/edu-frontend/commit/fb2a6ab5c7ce526a3ab826bf22312eec8a1f7978))
- Добавлен useTranslate хук в реакт ([a8c0b2c](https://github.com/Secure-T-Team/edu-frontend/commit/a8c0b2c4abc6c1954acf6ae076364cf2cc07ec5a))
- Добавлен выбор времени в фишинге ([d1eb631](https://github.com/Secure-T-Team/edu-frontend/commit/d1eb631d1ca36e92a7709918ff2e94cc0ebe1df2))
- Добавлен выбор доменов в создании шаблона ([caa40b3](https://github.com/Secure-T-Team/edu-frontend/commit/caa40b38805fa569f221ddf36d9022ac864a912b))
- Добавлен диалог создания вложения ([e588476](https://github.com/Secure-T-Team/edu-frontend/commit/e5884766f00fc1f42a52988d76782bd0b9655aec))
- Добавлен неразрывный пробел ([9c2d57c](https://github.com/Secure-T-Team/edu-frontend/commit/9c2d57c64ea2c3fb00ef1cad8c471c85f791e44b)), closes [#283](https://github.com/Secure-T-Team/edu-frontend/issues/283)
- Добавлен поиск по шаблонам ([2fa187f](https://github.com/Secure-T-Team/edu-frontend/commit/2fa187fd8de8e733059833144371e092892e9377))
- Добавлен прогресс обучения в курс ([f7c536e](https://github.com/Secure-T-Team/edu-frontend/commit/f7c536ed9f8c95ed14142bf38b51e78b96b56fd4))
- Добавлен экспорт аудит отчета ([afe9341](https://github.com/Secure-T-Team/edu-frontend/commit/afe934103e999082c2ef4e266885bb67ba5ffc8a))
- Добавлен эндпоинт на конфиги ([fa5e638](https://github.com/Secure-T-Team/edu-frontend/commit/fa5e6380cc7810f78994dacf665f8f4abc4af194))
- Добавлена заглушка "шаблоны не найдены" ([ae5792a](https://github.com/Secure-T-Team/edu-frontend/commit/ae5792a44c585b618f671f09650c12190ad9598f))
- Добавлена заглушка для локального апи [skip ci] ([04c92ed](https://github.com/Secure-T-Team/edu-frontend/commit/04c92ed7538ecaf1022c00ad8a2c74b2d4f0c480))
- Добавлена заглушка, если нет рассылок ([eb8ed47](https://github.com/Secure-T-Team/edu-frontend/commit/eb8ed474a3f1086b04632495f417695b0db19a8f))
- Добавлена загрузка презентаций ([3d2ebe3](https://github.com/Secure-T-Team/edu-frontend/commit/3d2ebe3cb201628028cad6769adbf4e3001d595c))
- Добавлена заметка сотрудника ([c00fe89](https://github.com/Secure-T-Team/edu-frontend/commit/c00fe891943d083d1b94bb22c21954a77f0f1866))
- Добавлена минимальная обработка ошибок ([8797732](https://github.com/Secure-T-Team/edu-frontend/commit/879773206fa7376ddb36e22039514ee6ecb2916f))
- Добавлена мобильная верстка ([f7d8154](https://github.com/Secure-T-Team/edu-frontend/commit/f7d81541ebd0dc615e7c739cfda372c742fa13f2))
- Добавлена обработка ошибок генерации отчета ([834ce95](https://github.com/Secure-T-Team/edu-frontend/commit/834ce9518035936151bba0e490d56d7d50ae4237))
- Добавлена поддержка Markdown ([bf12b62](https://github.com/Secure-T-Team/edu-frontend/commit/bf12b620786672b669c0b0ee36443c5bac7e528d))
- Добавлена статистика шаблона в фишинге ([da39f08](https://github.com/Secure-T-Team/edu-frontend/commit/da39f0831ff9449b02f7582f69d49f6ac0bc152c))
- Добавлена страница подробной статистики по рассылке ([0d33d5e](https://github.com/Secure-T-Team/edu-frontend/commit/0d33d5ec733b11d8c87c4ce55a428df26db9de09))
- Добавлено "нет данных" в таблицу ([1d2f8a4](https://github.com/Secure-T-Team/edu-frontend/commit/1d2f8a473660333bf14ee1ef3c41ca5dbb7a14cf))
- Добавлено api к аудиту ([b26dc51](https://github.com/Secure-T-Team/edu-frontend/commit/b26dc510473765777a93fdb993947e6ce615c910))
- Добавлено больше информативности в сообщения об ошибках SSR ([7591480](https://github.com/Secure-T-Team/edu-frontend/commit/7591480f7ffb8f28639dd315986a41ef477d21b9))
- Добавлено выделение активного дня в каледаре ([6cf3e0e](https://github.com/Secure-T-Team/edu-frontend/commit/6cf3e0ec3e60b3fbb83f20b45e584c72e1c01a9d))
- Добавлено дублирование шаблонов ([3164cf3](https://github.com/Secure-T-Team/edu-frontend/commit/3164cf38f73beca1cb65667e2cf699c62f1cd272))
- Добавлено отображение email у пользователя ([#278](https://github.com/Secure-T-Team/edu-frontend/issues/278)) ([cabaf2f](https://github.com/Secure-T-Team/edu-frontend/commit/cabaf2f017d36a66ed84095b2614938a58d8df43))
- Добавлено перенаправление старых ссылок на новые ([00081bf](https://github.com/Secure-T-Team/edu-frontend/commit/00081bf3007a5c96784d70b890e968d54c9e3781))
- Добавлено подтверждение удаления и генерация отчета ([0dd46b5](https://github.com/Secure-T-Team/edu-frontend/commit/0dd46b5c62ee09805392df7e2ea9949bf1086493))
- Добавлено редактирование лендоса ([14b3880](https://github.com/Secure-T-Team/edu-frontend/commit/14b388082a1f529ef378bdae5bb916c983a83587))
- Добавлено скругление логотипов ([f384a06](https://github.com/Secure-T-Team/edu-frontend/commit/f384a064264d5b0efaefb9befece514a54be548f))
- Добавлено создание, редактирование и удаление шаблонов ([13f2c6a](https://github.com/Secure-T-Team/edu-frontend/commit/13f2c6a8382b4135d1d9ade85e2643779b4c9c84))
- Добавлены автокурсы ([ab08e0c](https://github.com/Secure-T-Team/edu-frontend/commit/ab08e0cbaf4c72dc14ef3e768184d457d8976dff))
- Добавлены заглушки на страницу шаблона ([ba89b44](https://github.com/Secure-T-Team/edu-frontend/commit/ba89b44d5e3352e72a187b10bad745ba215b8c12))
- Добавлены проверки на пермишены в фишинговых шаблонах ([0827d1b](https://github.com/Secure-T-Team/edu-frontend/commit/0827d1ba900a8b915c33d92bdabc60a29d5612a6))
- Добавлены снэки ([146c255](https://github.com/Secure-T-Team/edu-frontend/commit/146c255e7ba6d34186a3057ccddefefb37fd6a2f))
- Добавлены тултипы с описанием ([752ad2e](https://github.com/Secure-T-Team/edu-frontend/commit/752ad2e3f7b92b79187caecdfce5f78f858d7e73))
- Доступность шаблонов в демо ([e938fa9](https://github.com/Secure-T-Team/edu-frontend/commit/e938fa979f8b85f1cfb1dcb83bf38b0edb541789))
- закончена страница шаблона ([76858b2](https://github.com/Secure-T-Team/edu-frontend/commit/76858b2da9b35075902d49e48a1a07fcc74f8506))
- Изменен шрифт и исправлены иконки в редакторе ([8baa346](https://github.com/Secure-T-Team/edu-frontend/commit/8baa346bf4a8987c4a3e213cf4d1dba569cc26eb))
- Измененение цветов в legacy части платформы ([04c48ad](https://github.com/Secure-T-Team/edu-frontend/commit/04c48ad93bb33e4ee2b46274bc459727f16715c2))
- Исправлена загрузка ([dadbb86](https://github.com/Secure-T-Team/edu-frontend/commit/dadbb863a3d26f0baeb65f747320c42f4e00f290))
- Локализация всей старой части платформы ([69b59ff](https://github.com/Secure-T-Team/edu-frontend/commit/69b59ffd80c692821211e730f71981842e09ad91))
- Локализация новой части платформы ([5161fb2](https://github.com/Secure-T-Team/edu-frontend/commit/5161fb2bc826a90f1446b993a7ba5b95e399c603))
- Начало страницы сотрудников ([bcf2a66](https://github.com/Secure-T-Team/edu-frontend/commit/bcf2a661c2257e58c3428a26725ea952e6a5ac57))
- Новый дизайн статистики в шаблоне ([c35357b](https://github.com/Secure-T-Team/edu-frontend/commit/c35357bcf5b885f689532590f576b0146c30a237))
- Отключено выделение unicodeHighlight ([c2ceaba](https://github.com/Secure-T-Team/edu-frontend/commit/c2ceaba49b112ab2acffe59ef4f7c6b3e0c66bb1))
- Переделана страница для рассылок с автоназначением ([221ee19](https://github.com/Secure-T-Team/edu-frontend/commit/221ee195e4c21b055dd57a8b9ef07f4534c0c682))
- Правки SSR ([24d8421](https://github.com/Secure-T-Team/edu-frontend/commit/24d8421e51901c7d84b6d5b3abab1750ae98d92e))
- прикручены настоящие отчеты ([ef2e97a](https://github.com/Secure-T-Team/edu-frontend/commit/ef2e97ab3ce95ea426a81e77d045ca9b8eac7570))
- редактирование писем ([b98237f](https://github.com/Secure-T-Team/edu-frontend/commit/b98237f66f8b44216c445e84886d5844a9ea1b0d))
- редизайн campaignTemplate ([d4f9a6f](https://github.com/Secure-T-Team/edu-frontend/commit/d4f9a6fc419bf955656ba765566a782d051ea278))
- Редизайн icon-button ([bfe154d](https://github.com/Secure-T-Team/edu-frontend/commit/bfe154dd58e9de520c21964195eb6bb0c3600186))
- редизайн карточки рассылки ([77e447c](https://github.com/Secure-T-Team/edu-frontend/commit/77e447cb22aa9eb6fee7f64ee22732fdfb7c3465))
- Редизайн пагинатора ([851b96d](https://github.com/Secure-T-Team/edu-frontend/commit/851b96d755b88b05c38d445677f898e92dd64793))
- Русский язык по умолчанию ([cf2225d](https://github.com/Secure-T-Team/edu-frontend/commit/cf2225d6fc30ffcf86c2b7e01ff625f1ff33efba))
- создание фишинговой рассылки ([1369191](https://github.com/Secure-T-Team/edu-frontend/commit/136919189a5fa5e8f3b68be43b5d7cfe496a8d6b))
- статистика из api ([8b50ca5](https://github.com/Secure-T-Team/edu-frontend/commit/8b50ca5e1317d0613ef81e054cb854030688e1d5))
- Страница рассылок и рассылки ([83755c9](https://github.com/Secure-T-Team/edu-frontend/commit/83755c95c1b0f223da9b9af99867d592f580ae7b))
- странички там и все такое ([f8c16e9](https://github.com/Secure-T-Team/edu-frontend/commit/f8c16e9ce9bfe7adc6bbddef437a5eb9ffe04bee))
- Улучшен скролл в диалогах ([3fe553e](https://github.com/Secure-T-Team/edu-frontend/commit/3fe553e41afff8ee2fc9a72626515a8c92033e08))
- Фичи для офлайна ([6953c64](https://github.com/Secure-T-Team/edu-frontend/commit/6953c642d39e3c1f3877c2de21f67fe7902e9737))

### Bug Fixes

- :bug: Исправлен редирект фишинга ([e6b4dba](https://github.com/Secure-T-Team/edu-frontend/commit/e6b4dba0cea702f0a80f43c239ded2dcb1de7ca6))
- disable view cache ([737c998](https://github.com/Secure-T-Team/edu-frontend/commit/737c99839ee676419cf04a322656b9b1b4bec80d))
- fix button component ([e9acb5d](https://github.com/Secure-T-Team/edu-frontend/commit/e9acb5dffe7ef25b4401229f4e530ac089aa4fb9))
- fix endless loop on error ([c8522e1](https://github.com/Secure-T-Team/edu-frontend/commit/c8522e1e3e3264de97929c635a1bb1b934cf3619))
- fix http service ([de31d08](https://github.com/Secure-T-Team/edu-frontend/commit/de31d08a1873751e41c37c5ee11382493534f4c4))
- fix ie colors ([a42895e](https://github.com/Secure-T-Team/edu-frontend/commit/a42895e6e78e47feb444aa01ddc18a509b12ea0c))
- fix permissions guards ([3d931c6](https://github.com/Secure-T-Team/edu-frontend/commit/3d931c6a7410756e4ad5a0182c9eee41a295d28f))
- fix registration path ([dfe65e5](https://github.com/Secure-T-Team/edu-frontend/commit/dfe65e5634dbabb82d11eb0c686132d8140cfdaa))
- fix report svg ([da1fed8](https://github.com/Secure-T-Team/edu-frontend/commit/da1fed867ac6dec12a70328fb3137181850427e5))
- fix requests params filtering ([d27dfb2](https://github.com/Secure-T-Team/edu-frontend/commit/d27dfb215873d47954dbb15bcb138e13fa987c8d))
- fix role-switch ([fd42c4a](https://github.com/Secure-T-Team/edu-frontend/commit/fd42c4ab2eea8fce1970fe34467ec4a6d7a551ad))
- fix sidebar ([9809c32](https://github.com/Secure-T-Team/edu-frontend/commit/9809c3209051a2bb226fd5d0d10b2106fabf24e8))
- fix sidebar ([226edee](https://github.com/Secure-T-Team/edu-frontend/commit/226edee359e5841d667fb0791919724fac263e98))
- fix table border ([32501d8](https://github.com/Secure-T-Team/edu-frontend/commit/32501d84ee7c82a7531f0ea202d75aeff61c997a))
- fix tables cells ([d512842](https://github.com/Secure-T-Team/edu-frontend/commit/d512842f2cca5d620bc316c0c35f2693aa4cb756))
- fix templates grid ([fbe0caa](https://github.com/Secure-T-Team/edu-frontend/commit/fbe0caa0c17d70d475eef60f8b051b696d17211f))
- fix textarea ([7dbb738](https://github.com/Secure-T-Team/edu-frontend/commit/7dbb738bfd6dce4d6ee094d8b5a4231866058370))
- fix typo ([88ee1ff](https://github.com/Secure-T-Team/edu-frontend/commit/88ee1ffad6e67578719188bfdd733fe21668eb99))
- временный фикс редактирования шаблона ([853c6a2](https://github.com/Secure-T-Team/edu-frontend/commit/853c6a21f0d11d51a90bb80c340535a3fc0ffd8e))
- всякие там фиксы ([876200e](https://github.com/Secure-T-Team/edu-frontend/commit/876200e27ad6084bd780278d051e2ee713b1eed2))
- всякие там фиксы ([8cda727](https://github.com/Secure-T-Team/edu-frontend/commit/8cda727261bfb026ea1000251f406856af3d517f))
- всякие там фиксы ([50707a0](https://github.com/Secure-T-Team/edu-frontend/commit/50707a0d49f6cdf64df58bb109a5361129979366))
- Исправлен input ([f8f6516](https://github.com/Secure-T-Team/edu-frontend/commit/f8f651642ee8b9ccec551944c92cf100cfb2edaa))
- исправлен select ([#259](https://github.com/Secure-T-Team/edu-frontend/issues/259)) ([5d46efe](https://github.com/Secure-T-Team/edu-frontend/commit/5d46efe12db769d5706ad30fb30dcdc6bbd3b0bb))
- Исправлен алгоритм расчета размера превью ([216d139](https://github.com/Secure-T-Team/edu-frontend/commit/216d139bd2032d05e4c66d381a875f59d1f81343))
- Исправлен бесконечный цикл запросов permissions ([cad6024](https://github.com/Secure-T-Team/edu-frontend/commit/cad6024fb3b3fb102ec39229e233c803a8123179)), closes [#276](https://github.com/Secure-T-Team/edu-frontend/issues/276)
- Исправлен редирект фишинга ([d9c489e](https://github.com/Secure-T-Team/edu-frontend/commit/d9c489e37fa949e5b6252c0a5e41924e4314e70c))
- Исправлен фон курсов ([7bbab26](https://github.com/Secure-T-Team/edu-frontend/commit/7bbab263d0984a471a0656a5bc47dd36d78a98bc))
- Исправлен ховер в сотрудниках ([#265](https://github.com/Secure-T-Team/edu-frontend/issues/265)) ([8ea435e](https://github.com/Secure-T-Team/edu-frontend/commit/8ea435ef24f4af4377b878bcc5fd4c724e9e503e))
- Исправлен цвет иконки в text-button ([121ce9a](https://github.com/Secure-T-Team/edu-frontend/commit/121ce9a347a2da5bea09a4395c22648ffd4d311d))
- Исправлена верстка курсов в базе ([030bfb7](https://github.com/Secure-T-Team/edu-frontend/commit/030bfb7e69f32dfc61684d091d83629cde4e9f00))
- Исправлена верстка попапа шаблонов ([#268](https://github.com/Secure-T-Team/edu-frontend/issues/268) [#263](https://github.com/Secure-T-Team/edu-frontend/issues/263) [#261](https://github.com/Secure-T-Team/edu-frontend/issues/261)) ([ae22992](https://github.com/Secure-T-Team/edu-frontend/commit/ae22992869b3994c1036b030028ef98347b54fe7))
- Исправлена высота сотрудника ([ebb54c6](https://github.com/Secure-T-Team/edu-frontend/commit/ebb54c6e97732d5a805e0816b7d0d60ce3d884c8))
- Исправлена генерация отчета по организации ([339c264](https://github.com/Secure-T-Team/edu-frontend/commit/339c264abace08ecd55c0950d7e91d1a4d6d3b45))
- Исправлена прыгающая кнопка "создать курс" ([52277f1](https://github.com/Secure-T-Team/edu-frontend/commit/52277f178c797a4b22b50a55b23182ad7aa648bf))
- Исправлена стата по фишингу ([82a6bbd](https://github.com/Secure-T-Team/edu-frontend/commit/82a6bbd1ccc4b7d9754d745cbbc2dcba20d13247))
- Исправлено минимальное время окончания ([55bfb24](https://github.com/Secure-T-Team/edu-frontend/commit/55bfb246716ee6b754b18339012b0ecac6f001fe)), closes [#285](https://github.com/Secure-T-Team/edu-frontend/issues/285)
- Исправлено скачивание отчетов ([a12a8cc](https://github.com/Secure-T-Team/edu-frontend/commit/a12a8ccbcaedab5bf2e6ca952ef0fbc086e997d5))
- исправлено создание слайдов ([90221c9](https://github.com/Secure-T-Team/edu-frontend/commit/90221c959682c26c62b373cf15a7f3d546d1cc1c))
- Исправлено сообщение ([2afb5aa](https://github.com/Secure-T-Team/edu-frontend/commit/2afb5aa8a9ffc633c49310da9a5bcab016685edc))
- Исправлены HostBinding ([dabcc30](https://github.com/Secure-T-Team/edu-frontend/commit/dabcc30fa678dc5aad48ae5749e2f1a9a67d5b7c))
- Исправлены крошки в создании письма ([937a6bc](https://github.com/Secure-T-Team/edu-frontend/commit/937a6bc562792017d1510849c1c447e5b274f74b))
- Исправлены поля на страничке профиля ([8348e59](https://github.com/Secure-T-Team/edu-frontend/commit/8348e59600efd43590c99682f43c4296b576fad1))
- Испрвлены типы) ([d7ef36b](https://github.com/Secure-T-Team/edu-frontend/commit/d7ef36b3bf0fb8c20de52ca488f2cc23d5f2a385))
- Снова исправлен алгоритм расчета высоты фрейма превью ([5fb3711](https://github.com/Secure-T-Team/edu-frontend/commit/5fb37115f81e4ac53b33436180ffebb9e95103ef))
- Убран раздел шаблоны ([982a1b9](https://github.com/Secure-T-Team/edu-frontend/commit/982a1b99ef845f2378bd7bed0086b132b1eab15a))
- Фишинг ([dd725b1](https://github.com/Secure-T-Team/edu-frontend/commit/dd725b1c8ef8f64146a7b3907e0a1113057aac48))

### Refactoring

- add log ([0e4bde2](https://github.com/Secure-T-Team/edu-frontend/commit/0e4bde2706b1f755b75d974c232eef1a84ec830c))
- add stylelint ([a38cfc7](https://github.com/Secure-T-Team/edu-frontend/commit/a38cfc706de78800c2691a69ebfdca523485a386))
- auth refactor ([eba25af](https://github.com/Secure-T-Team/edu-frontend/commit/eba25afd076585fb85921b354a5852bdc8c8925a))
- change mergeMap to switchMap ([5b32620](https://github.com/Secure-T-Team/edu-frontend/commit/5b32620f9e0f9f7224ce69b527c6f20c898af39c))
- fix eslint ignore ([1d76f4f](https://github.com/Secure-T-Team/edu-frontend/commit/1d76f4f88de1a5bc96efcf50a38b0b59d8d7a2f3))
- formatting ([659f2b1](https://github.com/Secure-T-Team/edu-frontend/commit/659f2b1943ce67c032196d4b45d60f5d8071def1))
- lint fix ([e18e789](https://github.com/Secure-T-Team/edu-frontend/commit/e18e789d7d6fd60fa8b9b428ab629917d350e242))
- move react folder ([d2267dd](https://github.com/Secure-T-Team/edu-frontend/commit/d2267ddbe70b4f78e93ec0e223bf115bf4716325))
- move webpack configs ([2d1f0db](https://github.com/Secure-T-Team/edu-frontend/commit/2d1f0db115c7e70943c23a2397c90644e97cb453))
- phishing refactoring ([312e45c](https://github.com/Secure-T-Team/edu-frontend/commit/312e45c14d7b4a756eefb49bd299ea85bb1dce49))
- refactor login page ([053d088](https://github.com/Secure-T-Team/edu-frontend/commit/053d08881e0a0730058da97596463e152462a019))
- refactor spinner usage ([dbd97c2](https://github.com/Secure-T-Team/edu-frontend/commit/dbd97c296e5de7b3b3f69cc0ffb8a8261376535f))
- remove logs ([0690078](https://github.com/Secure-T-Team/edu-frontend/commit/069007883f7dcea1cecb8dce6ac57f1cc0524eb3))
- remove unused fields ([bd51ea8](https://github.com/Secure-T-Team/edu-frontend/commit/bd51ea8e3584b93d6fb616aba0e590c298218589))
- remove unused prop ([b306f9a](https://github.com/Secure-T-Team/edu-frontend/commit/b306f9a09f56bf6b0035318ed112da8f2fb48167))
- remove unused stores ([a7a7f60](https://github.com/Secure-T-Team/edu-frontend/commit/a7a7f60b40e85062e5e6cbd4e2564f3a3583921f))
- rename personal to stuff ([9c9c448](https://github.com/Secure-T-Team/edu-frontend/commit/9c9c448492063b8ac75ec69efeba0660954bdf4f))
- use relative api path ([41e4fbd](https://github.com/Secure-T-Team/edu-frontend/commit/41e4fbd913f91f7b0ac586d30d07e3189d6878c6))
- Всякие правки по фишингу ([54c6d9a](https://github.com/Secure-T-Team/edu-frontend/commit/54c6d9a55e126fff73295cd22eb80ec80faf58cc))
- всякие фиксы ([81a26b7](https://github.com/Secure-T-Team/edu-frontend/commit/81a26b797d066ffaca3b97f668cfbe14d1556c9d))
- Добавлен базовый DataSource ([0d800d2](https://github.com/Secure-T-Team/edu-frontend/commit/0d800d28bc6b9fca254afe4e3732990600d4d12f))
- Изменена папка сервера ([58af462](https://github.com/Secure-T-Team/edu-frontend/commit/58af4624bbff786f57b4f6ddacb83eaf1af97b6a))
- Небольшой рефакторинг legacy части ([dd25fb8](https://github.com/Secure-T-Team/edu-frontend/commit/dd25fb8c5f34a5389fa0005c4e25df17c14b1732))
- небольшой рефакторинг картинки ([cdf7a39](https://github.com/Secure-T-Team/edu-frontend/commit/cdf7a390ef022d85b7012a595b117f6c323ff6ea))
- рефакторинг ([4918f5e](https://github.com/Secure-T-Team/edu-frontend/commit/4918f5e6238d400eaa72d7241401c0e6b3d153ae))
- Рефакторинг ([a3e5f63](https://github.com/Secure-T-Team/edu-frontend/commit/a3e5f637804fb97e2d8654d362b29cdc7796da94))
- Рефакторинг logsDataSource и table component ([fd75db8](https://github.com/Secure-T-Team/edu-frontend/commit/fd75db8beb1d27c7ddabdc2d9796daacec3905dc))
- рефакторинг конфигов ([2640eb9](https://github.com/Secure-T-Team/edu-frontend/commit/2640eb9aa594f0ea0c3724d76ef173c70d7cd0f8))
- рефакторинг сервиса апи и правки attack_vector ([aa42267](https://github.com/Secure-T-Team/edu-frontend/commit/aa42267f1f329a24ecc6351483f267f030191aab))
- Рефакторинг стилей ([0a01093](https://github.com/Secure-T-Team/edu-frontend/commit/0a0109357a082ceaa5ce2962064b5d674a5fb9e4))

### [1.4.2](https://github.com/Secure-T-Team/edu-frontend/compare/v1.4.1...v1.4.2) (2022-02-01)

### [1.4.1](https://github.com/Secure-T-Team/edu-frontend/compare/v1.4.0...v1.4.1) (2022-02-01)

### Refactoring

- изменение README ([8ea7238](https://github.com/Secure-T-Team/edu-frontend/commit/8ea72382dc3924aeeaf298a847202af079d2ef86))

## [1.4.0](https://github.com/Secure-T-Team/edu-frontend/compare/v1.3.4...v1.4.0) (2021-10-29)

### Features

- В фишинг добавлено предупреждение о сохранении данных ([#248](https://github.com/Secure-T-Team/edu-frontend/issues/248)) ([4ffea50](https://github.com/Secure-T-Team/edu-frontend/commit/4ffea502ef42a4219d0fb11b0050840adaa5c4e6))

### [1.3.4](https://github.com/Secure-T-Team/edu-frontend/compare/v1.3.2...v1.3.4) (2021-10-25)

### Bug Fixes

- Исправлена верстка таблиц ([#239](https://github.com/Secure-T-Team/edu-frontend/issues/239)) ([8d0d2dd](https://github.com/Secure-T-Team/edu-frontend/commit/8d0d2dda6e52e6d19d01ff0fb28e81d586aaa051))

### [1.3.3](https://github.com/Secure-T-Team/edu-frontend/compare/v1.3.2...v1.3.3) (2021-09-13)

### Bug Fixes

- Исправлена верстка таблиц ([#239](https://github.com/Secure-T-Team/edu-frontend/issues/239)) ([8d0d2dd](https://github.com/Secure-T-Team/edu-frontend/commit/8d0d2dda6e52e6d19d01ff0fb28e81d586aaa051))

### [1.3.2](https://github.com/Secure-T-Team/edu-frontend/compare/v1.3.1...v1.3.2) (2021-09-08)

### Refactoring

- remove unused fields ([3aaf2b0](https://github.com/Secure-T-Team/edu-frontend/commit/3aaf2b06d985ce5252be1043517c03bc92a28309))

### [1.3.1](https://github.com/Secure-T-Team/edu-frontend/compare/v1.3.0...v1.3.1) (2021-09-03)

### Refactoring

- remove unused import ([015742d](https://github.com/Secure-T-Team/edu-frontend/commit/015742d89edee7d15701b082612fd8cafb68a0a7))

## 1.3.0 (2021-09-03)

### Features

- :beers: Для рассылок с автоназначением показывается другая страничка "Вы попались" ([#232](https://github.com/Secure-T-Team/edu-frontend/issues/232)) ([c404772](https://github.com/Secure-T-Team/edu-frontend/commit/c4047720a319931d4efedcf07ff7fe7807e921ec))

## 1.2.0 (2021-09-03)

### Features

- Добавлена поддержка markdown в слайдах ([#230](https://github.com/Secure-T-Team/edu-frontend/issues/230)) ([5befd08](https://github.com/Secure-T-Team/edu-frontend/commit/5befd0859f32aa7a4469a678dd2381f9aee5daec))

## 1.1.0 (2021-09-03)

### Features

- Добавлено автоназначение курсов после фишинговой рассылки ([d8b0a89](https://github.com/Secure-T-Team/edu-frontend/commit/d8b0a892765f110b356dffb66db5468bfd830683))

## 1.0.0 (2021-09-03)

### Features

- Улучшен скрол в диалогах ([24debf4](https://github.com/Secure-T-Team/edu-frontend/commit/24debf466588fd4c79e15efc265310436dd1c871))

### Bug Fixes

- correct errors in texts ([03bae29](https://github.com/Secure-T-Team/edu-frontend/commit/03bae292b5cba668a30ecb5285e7c14071c6449e))
- Исправлена проблема с проваливанием в незагруженные курсы ([#220](https://github.com/Secure-T-Team/edu-frontend/issues/220)) ([79d68ff](https://github.com/Secure-T-Team/edu-frontend/commit/79d68fffb562997b535185ff83ec70736ab34241))
- Исправлено сложение фишинговой статистики ([8009ce8](https://github.com/Secure-T-Team/edu-frontend/commit/8009ce80c851c1950a4c75ab145b31b000b6224a))
