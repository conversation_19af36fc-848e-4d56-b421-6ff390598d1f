// @ts-nocheck
const { CleanWebpackPlugin } = require('clean-webpack-plugin')
const path = require('path')
const NodemonPlugin = require('nodemon-webpack-plugin')

const { NODE_ENV = 'production' } = process.env

const isDev = NODE_ENV !== 'production'

const OUTPUT_PATH = path.join(__dirname, 'server')

module.exports = {
  target: 'node',
  entry: path.join(__dirname, 'src', 'server', 'index.ts'),
  devtool: isDev && 'inline-source-map',
  mode: NODE_ENV,
  watch: isDev,
  optimization: {
    nodeEnv: false,
  },
  output: {
    path: OUTPUT_PATH,
    filename: 'index.js',
  },
  resolve: {
    extensions: ['.ts', '.js'],
  },
  node: {
    // Need this when working with express, otherwise the build fails
    __dirname: false, // if you don't put this is, __dirname
    __filename: false, // and __filename return blank or /
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        use: [
          {
            loader: 'ts-loader',
            options: {
              compilerOptions: {
                noEmit: false,
              },
              configFile: path.resolve('tsconfig.server.json'),
            },
          },
        ],
        exclude: /node_modules/,
      },
    ],
    exprContextCritical: false,
  },
  plugins: [
    !isDev && new CleanWebpackPlugin(),
    isDev &&
      new NodemonPlugin({
        env: process.env,
      }),
  ].filter(v => !!v),
  externalsPresets: {
    node: true,
  },
}
