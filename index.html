<!doctype html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    {{#unless fav_icon }}
    <link rel="apple-touch-icon" sizes="57x57" href="/favicons/apple-icon-57x57.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/favicons/apple-icon-60x60.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/favicons/apple-icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/favicons/apple-icon-76x76.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/favicons/apple-icon-114x114.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/favicons/apple-icon-120x120.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/favicons/apple-icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/favicons/apple-icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/favicons/apple-icon-180x180.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicons/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="96x96" href="/favicons/favicon-96x96.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicons/favicon-16x16.png" />
    {{/unless }} {{#if fav_icon }}
    <link rel="icon" href="{{{fav_icon}}}" />
    {{/if}}
    <meta name="msapplication-TileColor" content="{{theme.colors.primary}}" />
    <meta name="theme-color" content="{{theme.colors.primary}}" />

    <style id="theme"></style>

    <script id="config" type="application/json">
      {{{config_json}}}
    </script>

    <!-- Yandex.Metrika counter -->
    <script type="text/javascript">
      (function(m,e,t,r,i,k,a){m[i]=m[i]||function(){(m[i].a=m[i].a||[]).push(arguments)};
      m[i].l=1*new Date();
      for (var j = 0; j < document.scripts.length; j++) {if (document.scripts[j].src === r) { return; }}
      k=e.createElement(t),a=e.getElementsByTagName(t)[0],k.async=1,k.src=r,a.parentNode.insertBefore(k,a)})
      (window, document, "script", "https://mc.yandex.ru/metrika/tag.js", "ym");

      ym({{{yandex_metrika_id}}}, "init", {
          clickmap:true,
          trackLinks:true,
          accurateTrackBounce:true
      });
    </script>
    <!-- /Yandex.Metrika counter -->

    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  </head>
  <body>
    <div id="root"></div>
    <noscript
      ><div>
        <img
          src="https://mc.yandex.ru/watch/100185348"
          style="position: absolute; left: -9999px"
          alt=""
        /></div
    ></noscript>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
