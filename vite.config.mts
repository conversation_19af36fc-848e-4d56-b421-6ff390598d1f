import { PluginOption, defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import tsconfigPaths from 'vite-tsconfig-paths'
import handlebars from 'vite-plugin-handlebars'
import postcssNesting from 'postcss-nesting'
import { viteStaticCopy } from 'vite-plugin-static-copy'
import fixReactVirtualized from 'esbuild-plugin-react-virtualized'
import { viteCommonjs, esbuildCommonjs } from '@originjs/vite-plugin-commonjs'
import { visualizer } from 'rollup-plugin-visualizer'
import { getDefaultBrandingConfig } from './src/shared/configs/app-config/get-default-branding-config'

function i18nHotReload(): PluginOption {
  return {
    name: 'i18n-hot-reload',
    handleHotUpdate({ file, server }) {
      if (file.includes('locales') && file.endsWith('.json')) {
        server.ws.send({
          type: 'custom',
          event: 'locales-update',
        })
      }
    },
  }
}

function getPlugins(mode: string) {
  const plugins: PluginOption[] = [
    react(),
    viteCommonjs(),
    tsconfigPaths(),
    viteStaticCopy({
      targets: [
        {
          src: 'src/shared/assets/legacy_files/*',
          dest: 'assets/legacy_files',
        },
      ],
    }),
    i18nHotReload(),
  ]

  if (mode === 'report') {
    plugins.push(
      visualizer({
        emitFile: true,
        gzipSize: true,
        filename: 'reports/bundle.html',
      }),
    )
  }

  if (mode === 'development') {
    const brandingConfig = getDefaultBrandingConfig(mode)

    plugins.push(
      handlebars({
        context: {
          ...brandingConfig,
          config_json: JSON.stringify(brandingConfig, null, 4),
        },
      }),
    )
  }

  return plugins
}

// TODO: error is because of '@originjs/vite-plugin-commonjs' lib
// IT doesn't crush app, just show error. Fix it. Added temporary to fix theme create page for testing
export default defineConfig(({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd(), '') }

  return {
    define: {
      process: {
        env: {
          ...process.env,
          VITE_MANUAL_DEV: !!process.env.VITE_MANUAL_DEV,
        },
      },
      VITE_MANUAL_DEV: !!process.env.VITE_MANUAL_DEV,
    },
    optimizeDeps: {
      esbuildOptions: {
        plugins: [
          fixReactVirtualized,
          esbuildCommonjs([
            'react-editor-js',
            '@react-editor-js/client',
            '@react-editor-js/server',
          ]),
        ],
      },
    },
    build: {
      minify: 'esbuild',
    },
    mode,
    server: {
      port: 4200,
      proxy: {
        '/media': {
          target: 'https://static.sec-t.ru/',
          changeOrigin: true,
          secure: false,
        },
      },
    },
    plugins: getPlugins(mode),
    css: {
      postcss: {
        plugins: [postcssNesting],
      },
    },
  }
})
