import type { Preview } from '@storybook/react'
import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from 'react-router-dom'
import { withThemeByClassName } from '@storybook/addon-themes'

import i18n from './../src/shared/configs/i18n/'

import 'react-loading-skeleton/dist/skeleton.css';
import './../src/index.css'
import './themes/themes.css'

const preview: Preview = {
  tags: ['autodocs'],
  argTypes: {
    className: {
      description: 'Дополнительный CSS-класс для стилизации',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'undefined' },
        readonly: true
      },
    },
    'data-testid': {
      description: 'Тестовый идентификатор для автоматизированного тестирования',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        readonly: true
      },
    },
  },
  globals: {
    locale: 'ru',
  },
  parameters: {
    i18n,
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
    docs: {
      extractComponentDescription: () => null, // Отключает JSDoc, но оставляет prop-types
    },
    backgrounds: {
      default: 'background',
      clearable: false,
      values: [
        {
          name: 'background',
          value: 'var(--color-background)',
        },
        {
          name: 'surface',
          value: 'var(--color-surface)',
        },
      ],
    },
  },
  decorators: [
    Story => (
      <div style={{ padding: '20px' }}>
        <BrowserRouter>
          <Story />
        </BrowserRouter>
      </div>
    ),
    withThemeByClassName({
      themes: {
        'secure-t': 'theme-secure-t',
        bastion: 'theme-bastion',
        megafon: 'theme-megafon',
      },
      defaultTheme: 'secure-t',
    }),
  ],
}

export default preview
