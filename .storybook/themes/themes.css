.theme-bastion {
	--injected-theme-color-primary: rgb(14, 114, 181);
	--injected-theme-color-primary-90: rgb(41, 130, 189);
	--injected-theme-color-primary-80: rgb(67, 145, 197);
	--injected-theme-color-primary-70: rgb(94, 161, 206);
	--injected-theme-color-primary-60: rgb(120, 176, 214);
	--injected-theme-color-primary-50: rgb(148, 192, 222);
	--injected-theme-color-primary-40: rgb(174, 208, 230);
	--injected-theme-color-primary-30: rgb(201, 223, 238);
	--injected-theme-color-primary-20: rgb(228, 239, 247);

	overflow: auto !important;
}

.theme-megafon {
	--injected-theme-color-primary: rgb(58, 170, 53);
	--injected-theme-color-primary-90: rgb(80, 179, 75);
	--injected-theme-color-primary-80: rgb(102, 189, 98);
	--injected-theme-color-primary-70: rgb(124, 198, 120);
	--injected-theme-color-primary-60: rgb(145, 207, 142);
	--injected-theme-color-primary-50: rgb(167, 217, 165);
	--injected-theme-color-primary-40: rgb(189, 227, 187);
	--injected-theme-color-primary-30: rgb(211, 236, 210);
	--injected-theme-color-primary-20: rgb(233, 246, 233);

	overflow: auto !important;
}

.theme-secure-t {
	--injected-theme-color-primary: rgb(61, 188, 135);
	--injected-theme-color-primary-90: rgb(83, 195, 148);
	--injected-theme-color-primary-80: rgb(104, 203, 162);
	--injected-theme-color-primary-70: rgb(126, 210, 175);
	--injected-theme-color-primary-60: rgb(147, 218, 188);
	--injected-theme-color-primary-50: rgb(169, 225, 202);
	--injected-theme-color-primary-40: rgb(190, 233, 215);
	--injected-theme-color-primary-30: rgb(212, 240, 228);
	--injected-theme-color-primary-20: rgb(233, 248, 242);

	overflow: auto !important;
}
