import { configureStore } from '@reduxjs/toolkit'
import { TypedUseSelectorHook, useDispatch } from 'react-redux'
import { useSelector } from 'react-redux'
import { eventKeeperAPI } from './services/event-keeper-service'
import { coursesAPI } from './services/course-service'
import { moduleAPI } from './services/module-service'
import { assignedCoursesAPI } from './services/assigned-courses-service'
import { assignedCourseAPI } from './services/assigned-course-service'
import { rtkQueryErrorLogger } from './error-handling'
import { tagsEmployeesApi } from './services/tags-employees-service'
import { departmentsTreeApi } from './services/endpoints/departments/api'
import { rootReducer } from './reducer'
import { globalBaseApi } from './services/endpoints/base'
import { myCoursesAPI } from './services/user-courses-service'
import { NavigateFunction } from 'react-router-dom'

export type ExtraArgs = {
  navigate: NavigateFunction
}

export type SetupStoreParams = {
  extraArgs: ExtraArgs
} | void

export const setupStore = (params: SetupStoreParams) => {
  return configureStore({
    reducer: rootReducer,
    middleware: getDefaultMiddleware => {
      return getDefaultMiddleware({
        serializableCheck: false,
        thunk: { extraArgument: params?.extraArgs },
      }).concat([
        eventKeeperAPI.middleware,
        // --- education old start
        coursesAPI.middleware,
        myCoursesAPI.middleware,
        assignedCourseAPI.middleware,
        departmentsTreeApi.middleware,
        moduleAPI.middleware,
        // --- education old end
        tagsEmployeesApi.middleware,
        assignedCoursesAPI.middleware,
        globalBaseApi.middleware,
        rtkQueryErrorLogger,
      ])
    },
  })
}

let store: AppStore

export const setStore = (newStore: AppStore) => {
  store = newStore
}

export const getStore = () => store

export type RootState = ReturnType<typeof rootReducer>
export type AppStore = ReturnType<typeof setupStore>
export type AppDispatch = AppStore['dispatch']

export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector
