/* eslint-disable no-empty */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { BaseQueryApi, fetchBaseQuery, FetchBaseQueryArgs } from '@reduxjs/toolkit/query'
import { BaseConfig, Config } from '@/shared/configs/app-config'
import { FetchArgs } from '@reduxjs/toolkit/query'

class LocalConfig extends BaseConfig implements Config {
  constructor(config = document.getElementById('config')) {
    let resultConfig
    if (!config) {
      resultConfig = {}
    } else {
      try {
        resultConfig = JSON.parse(config.textContent || '{}')
      } catch (error) {
        resultConfig = {}
      }
    }

    super(resultConfig)
  }
}

const temp = new LocalConfig()
export const getBaseUrl = () => {
  if (import.meta.env.MODE !== 'production') return temp?.apiHost ?? 'https://edu.sec-t.ru'

  return window.location.origin
}
export const baseUrl = getBaseUrl()

const baseQuery = (overrideBaseUrl?: string) =>
  fetchBaseQuery({
    baseUrl: overrideBaseUrl ?? getBaseUrl() + '/lk/api/v2',
    credentials: 'include',
  })

export const getBaseQuery = (
  _ = true,
  additionalOptions?: FetchBaseQueryArgs,
  isEventKeeper = false,
): FetchBaseQueryArgs => {
  const base = temp?.apiUrl
  let token = ''
  try {
    token = `Token ${JSON.parse(localStorage.getItem('token') || '')}`
  } catch (error) {}

  const options: FetchBaseQueryArgs = {
    baseUrl: base,
    credentials: 'include',
    headers: {
      // возможно вернуть к window.location.href
      'X-Referer': window.location.href.split('/lk/')[0] + '/lk/',
      ['Authorization']: token,
    },
    ...additionalOptions,
  }

  if (isEventKeeper) {
    // options.baseUrl = "https://dev-services.sec-t.ru";
    options.baseUrl = temp.eventKeeperUrl
  }

  return options
}

export const baseQueryWithReauth = async (
  args: FetchArgs & {
    baseUrl?: string
  },
  api: BaseQueryApi,
  extraOptions: any,
) => {
  const newArgs = {
    ...args,
    headers: {
      ...args.headers,
    },
  }

  const result = await baseQuery(args.baseUrl)(newArgs, api, extraOptions)

  if (result?.error?.status === 401) api.dispatch({ type: 'auth/logOut' })

  return result
}
