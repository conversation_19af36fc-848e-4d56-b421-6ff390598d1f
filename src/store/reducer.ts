import { combineSlices } from '@reduxjs/toolkit'
import authSlice from './slices/auth/auth-slice'
import { eventKeeperAPI } from './services/event-keeper-service'
import notificationSlice from './slices/notification-slice'
import { coursesAPI } from './services/course-service'
import { moduleAPI } from './services/module-service'
import { assignedCoursesAPI } from './services/assigned-courses-service'
import { assignedCourseAPI } from './services/assigned-course-service'
import tagsSlice from './slices/tags/tags-slice'
import { tagsEmployeesApi } from './services/tags-employees-service'
import phishingSlice from './slices/phishing/phishing-slice'
import languageSlice from './slices/language/language-slice'
import { departmentsTreeApi } from './services/endpoints/departments/api'
import { departmentsTreeSlice } from '../shared/modals/organization-tree'
import { globalBaseApi } from './services/endpoints/base'
import { sidebar } from './slices/sidebar-slice'
import { myCoursesAPI } from './services/user-courses-service'
import { employeesSlice } from '@/entities/employee'
import { departmentsSlice } from '@/entities/department'

export interface LazyLoadedSlices {}

export const rootReducer = combineSlices(
  eventKeeperAPI,
  coursesAPI,
  tagsEmployeesApi,
  moduleAPI,
  assignedCourseAPI,
  assignedCoursesAPI,
  departmentsTreeApi,
  globalBaseApi,
  myCoursesAPI,
  {
    auth: authSlice,
    tags: tagsSlice.reducer,
    notification: notificationSlice,
    phishing: phishingSlice.reducer,
    language: languageSlice.reducer,
    departmentsTree: departmentsTreeSlice.reducer,
    sidebar: sidebar.reducer,
    employees: employeesSlice.reducer,
    departments: departmentsSlice.reducer,
  },
).withLazyLoadedSlices<LazyLoadedSlices>()
