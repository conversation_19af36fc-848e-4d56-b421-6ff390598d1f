import { createAsyncThunk, createSlice, PayloadAction, WithSlice } from '@reduxjs/toolkit'
import { rootReducer } from '@/store/reducer'
import { TTheme as TThemeData } from '@/entities/themeCourse/model/types'
import { CourseStructure, ThemeProgress, TTheme } from '@/pages/user/learning/types'
import { RootState } from '@/store/store'
import { testApi } from '@/entities/courses/model/api/endpoints'
import { Step } from '@/entities/themeCourse/ui/theme-preview/components/steps-toolbar-preview'
import { loadNamespaces } from 'i18next'
import { DataType } from '../../../entities/themeCourse/ui/theme-preview'
import { userStaticticsApi } from '@/pages/user/learning/endpoints'

type UserCourseModal = {
  title: string
  description?: string
  agreeeBtnText?: string
  disagreeBtnText?: string
  onAgree: () => void
  onDisagree?: () => void
}

type UserCourseState = {
  data?: DataType
  sectionId?: UUID
  theme?: TTheme
  themeData?: TThemeData
  activeStep?: Step
  structure?: CourseStructure
  courseId?: UUID
  themeProgress?: ThemeProgress
  isLast?: boolean
  disableNext?: boolean
  disableFetch?: boolean
  activeStepHandler?: (step: Step) => Promise<void>
  modal?: UserCourseModal
}

const initialState: UserCourseState = {
  sectionId: undefined,
  theme: undefined,
  themeData: undefined,
  activeStep: undefined,
  structure: undefined,
  courseId: undefined,
  themeProgress: undefined,
  isLast: false,
  activeStepHandler: undefined,
  disableFetch: undefined,
  disableNext: false,
  modal: undefined,
}

export const checkCurrentStep = createAsyncThunk(
  'userCourse/handleNextStepStatus',
  async (_, { getState, dispatch }) => {
    const { userCourseSlice } = getState() as RootState

    if (!userCourseSlice) return

    const { sectionId, theme, themeData, activeStep, structure } = userCourseSlice
    const currentSection = structure?.sections.find(section => section.id === sectionId)
    const stepIndex = themeData?.steps.findIndex(step => step.id === activeStep?.id)
    const isLastStep = themeData && stepIndex === themeData.steps.length - 1
    const themeIndex = currentSection?.themes.findIndex(t => t.id === theme?.theme_id)
    const isLastTheme = currentSection && themeIndex === currentSection.themes.length - 1
    const sectionIndex = structure?.sections.findIndex(section => section.id === sectionId)
    const isLastSection = structure && sectionIndex === structure?.sections.length - 1

    if (isLastTheme && isLastSection && isLastStep) {
      await dispatch(setUserCourseIsLast(true))
    } else {
      await dispatch(setUserCourseIsLast(false))
    }
  },
)

export const goToTheory = createAsyncThunk(
  'userCourse/handleGoToTheory',
  async (_, { getState, dispatch }) => {
    const { userCourseSlice } = getState() as RootState

    if (!userCourseSlice) return

    const { sectionId, structure, theme, activeStepHandler } = userCourseSlice

    const currentSection = structure?.sections.find(section => section.id === sectionId)
    const themeIndex = currentSection?.themes.findIndex(t => t.id === theme?.theme_id)
    const sectionIndex = structure?.sections.findIndex(section => section.id === sectionId)
    if (sectionIndex !== undefined && themeIndex !== undefined) {
      const theme = structure?.sections[sectionIndex].themes[themeIndex]
      dispatch(setUserCourseThemeData(theme))
      dispatch(
        setUserCourseTheme({
          theme_id: theme?.id ?? '',
          title: theme?.title ?? '',
        }),
      )
      dispatch(setUserCourseActiveStep(theme?.steps[0]))
      if (activeStepHandler) {
        activeStepHandler({
          id: theme?.steps[0].id ?? '',
          type: theme?.steps[0].type ?? 'article',
        })
      }
    }
  },
)

const TRANSLATIONS = 'modals__education-editor'

loadNamespaces([TRANSLATIONS])

export const handleNextStep = createAsyncThunk(
  'userCourse/handleNextStep',
  async (_, { getState, dispatch }) => {
    const state = getState() as RootState
    const { userCourseSlice } = state

    if (!userCourseSlice) return

    await dispatch(setDisableNext(true))

    const {
      sectionId: currentSectionId,
      theme: currentTheme,
      themeData,
      activeStep,
      structure,
      courseId,
      themeProgress,
      activeStepHandler,
    } = userCourseSlice

    // Сохраняем текущие значения для сравнения
    const prevThemeId = currentTheme?.theme_id
    const prevSectionId = currentSectionId

    // Получаем индексы и статусы
    const currentSection = structure?.sections.find(section => section.id === currentSectionId)
    const stepIndex = themeData?.steps.findIndex(step => step.id === activeStep?.id)
    const stepProgress = themeProgress?.steps.find(step => step.step_id === activeStep?.id)
    const isLastStep = themeData && stepIndex === themeData.steps.length - 1
    const themeIndex = currentSection?.themes.findIndex(t => t.id === currentTheme?.theme_id)
    const isLastTheme = currentSection && themeIndex === currentSection.themes.length - 1
    const sectionIndex = structure?.sections.findIndex(section => section.id === currentSectionId)
    const isLastSection = structure && sectionIndex === structure?.sections.length - 1

    // Переменные для новой темы
    let newThemeId = prevThemeId
    let newSectionId = prevSectionId

    const handleNext = async () => {
      if (isLastTheme && isLastStep && sectionIndex !== undefined && !isLastSection) {
        // Переход на следующую секцию
        dispatch(setDisableFetch(true))
        newSectionId = structure?.sections[sectionIndex + 1].id
        const newTheme = structure?.sections[sectionIndex + 1].themes[0]
        newThemeId = newTheme?.id ?? ''

        if (newSectionId) dispatch(setUserCourseSection(newSectionId))
        dispatch(setUserCourseThemeData(newTheme))
        dispatch(
          setUserCourseTheme({
            theme_id: newThemeId,
            title: newTheme?.title ?? '',
          }),
        )

        dispatch(setDisableFetch(false))
        dispatch(setUserCourseActiveStep(newTheme?.steps[0]))
      } else if (
        isLastStep &&
        sectionIndex !== undefined &&
        themeIndex !== undefined &&
        !isLastTheme
      ) {
        // Переход на следующую тему в текущей секции
        dispatch(setDisableFetch(true))
        const newTheme = structure?.sections[sectionIndex].themes[themeIndex + 1]
        newThemeId = newTheme?.id ?? ''

        dispatch(setUserCourseThemeData(newTheme))
        dispatch(
          setUserCourseTheme({
            theme_id: newThemeId,
            title: newTheme?.title ?? '',
          }),
        )

        dispatch(setDisableFetch(false))
        dispatch(setUserCourseActiveStep(newTheme?.steps[0]))
      } else if (
        sectionIndex !== undefined &&
        themeIndex !== undefined &&
        stepIndex !== undefined
      ) {
        // Переход на следующий шаг в текущей теме
        const theme = structure?.sections[sectionIndex].themes[themeIndex]
        if (activeStepHandler) {
          dispatch(setDisableNext(true))
          activeStepHandler({
            id: theme?.steps[stepIndex + 1].id ?? '',
            type: theme?.steps[stepIndex + 1].type ?? 'article',
          })
        }
      }
    }

    // Обработка завершения теории
    if (
      activeStep &&
      activeStep.type !== 'quiz' &&
      activeStep.type !== 'scorm' &&
      courseId &&
      currentSectionId &&
      currentTheme?.theme_id &&
      activeStep.id &&
      stepProgress &&
      !stepProgress.passed &&
      stepProgress?.step_progress_id
    ) {
      await dispatch(
        testApi.endpoints.theoryComplete.initiate({
          assigned_course_id: courseId,
          body: {
            section_id: currentSectionId,
            theme_id: currentTheme?.theme_id,
            step_id: activeStep.id,
            step_progress_id: stepProgress?.step_progress_id,
          },
        }),
      )
    }

    await handleNext()
    dispatch(setDisableNext(false))

    // Если тема изменилась - загружаем прогресс новой темы
    if (newThemeId !== prevThemeId && courseId && newSectionId && newThemeId) {
      try {
        const themeProgress = await dispatch(
          userStaticticsApi.endpoints.getThemeProgress.initiate({
            course_id: courseId,
            theme_id: newThemeId,
            section_id: newSectionId,
          }),
        ).unwrap()

        dispatch(setUserCourseThemeProgress(themeProgress))

        // Дополнительные действия при смене темы
        return {
          themeChanged: true,
          newThemeId,
          newSectionId,
          themeProgress,
        }
      } catch (error) {
        console.error('Failed to fetch new theme progress:', error)
      }
    }

    return {
      themeChanged: false,
      newThemeId,
      newSectionId,
    }
  },
)

const userCourseSlice = createSlice({
  name: 'userCourseSlice',
  initialState,
  reducers: {
    setData: (state, { payload }: PayloadAction<DataType | undefined>) => {
      state.data = payload
    },
    setModal: (state, { payload }: PayloadAction<UserCourseModal | undefined>) => {
      state.modal = payload
    },
    setUserCourseSection: (state, { payload }: PayloadAction<UUID>) => {
      state.sectionId = payload
    },
    setUserCourseTheme: (state, { payload }: PayloadAction<TTheme | undefined>) => {
      state.theme = payload
    },
    setUserCourseThemeData: (state, { payload }: PayloadAction<TThemeData | undefined>) => {
      state.themeData = payload
    },
    setUserCourseActiveStep: (state, { payload }: PayloadAction<Step | undefined>) => {
      state.activeStep = payload
    },
    setUserCourseStructure: (state, { payload }: PayloadAction<CourseStructure | undefined>) => {
      state.structure = payload
    },
    setUserCourseId: (state, { payload }: PayloadAction<UUID | undefined>) => {
      state.courseId = payload
    },
    setUserCourseThemeProgress: (state, { payload }: PayloadAction<ThemeProgress | undefined>) => {
      state.themeProgress = payload
    },
    setUserCourseIsLast: (state, { payload }: PayloadAction<boolean | undefined>) => {
      state.isLast = payload
    },
    setActiveStepHandler: (state, { payload }: PayloadAction<(step: Step) => Promise<void>>) => {
      state.activeStepHandler = payload
    },
    resetUserCourseState: () => initialState,
    setDisableNext: (state, { payload }: PayloadAction<boolean | undefined>) => {
      state.disableNext = payload
    },
    setDisableFetch: (state, { payload }: PayloadAction<boolean | undefined>) => {
      state.disableFetch = payload
    },
  },
  selectors: {
    selectSectionId: (state: UserCourseState) => state.sectionId,
    selectTheme: (state: UserCourseState) => state.theme,
    selectThemeData: (state: UserCourseState) => state.themeData,
    selectActiveStep: (state: UserCourseState) => state.activeStep,
    selectCourseStructure: (state: UserCourseState) => state.structure,
    selectCourseId: (state: UserCourseState) => state.courseId,
    selectThemeProgress: (state: UserCourseState) => state.themeProgress,
    selectLastStep: (state: UserCourseState) => state.isLast,
    selectActiveStepHandler: (state: UserCourseState) => state.activeStepHandler,
    selectDisableNext: (state: UserCourseState) => state.disableNext,
    selectDisableFetch: (state: UserCourseState) => state.disableFetch,
    selectModal: (state: UserCourseState) => state.modal,
    selectData: (state: UserCourseState) => state.data,
  },
})

const injectedThemePageSlice = userCourseSlice.injectInto(rootReducer)

export const {
  setUserCourseTheme,
  setUserCourseSection,
  setUserCourseThemeData,
  setUserCourseActiveStep,
  setUserCourseStructure,
  setUserCourseId,
  setUserCourseThemeProgress,
  setUserCourseIsLast,
  setActiveStepHandler,
  resetUserCourseState,
  setDisableNext,
  setDisableFetch,
  setModal,
  setData,
} = injectedThemePageSlice.actions

export const {
  selectSectionId,
  selectTheme,
  selectThemeData,
  selectActiveStep,
  selectCourseStructure,
  selectCourseId,
  selectThemeProgress,
  selectLastStep,
  selectActiveStepHandler,
  selectDisableNext,
  selectDisableFetch,
  selectModal,
  selectData,
} = injectedThemePageSlice.selectors

// Type declaration merging for LazyLoadedSlices in src/store/reducer
declare module '@/store/reducer' {
  export interface LazyLoadedSlices extends WithSlice<typeof userCourseSlice> {}
}
