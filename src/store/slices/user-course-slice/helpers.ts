import { CourseProgress, TSection, TTheme } from '../../../pages/user/learning/types'

export const isCoursePassed = ({
  exclude_themes = [],
  progress,
}: {
  progress: CourseProgress | undefined
  exclude_themes?: string[]
}):
  | boolean
  | {
      theme: TTheme
      section: TSection
    } => {
  if (progress?.passed) return progress?.passed

  let resultTheme: TTheme | undefined = undefined
  let resultSection: TSection | undefined = undefined

  const passedThemes = progress?.sections?.every(
    section =>
      !!section.themes.every(theme => {
        if (exclude_themes.includes(theme.theme_id)) return true
        if (!theme.passed) {
          resultTheme = theme
          resultSection = section
          return false
        }
        return !!theme.passed
      }),
  )

  if (resultTheme && resultSection) return { theme: resultTheme, section: resultSection }

  return !!passedThemes
}

export const findFirstNotPassedStep = (
  sections: CourseProgress['sections'] | undefined,
):
  | {
      section: TSection
      theme: TTheme
    }
  | undefined => {
  if (!sections) return undefined

  for (const section of sections) {
    for (const theme of section.themes) {
      if (!theme.passed)
        return {
          section,
          theme,
        }
    }
  }

  return undefined
}
