import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { ReactNode } from 'react'

type InitialState = {
  createContent?: () => ReactNode
}

const initialState: InitialState = {}

export const sidebar = createSlice({
  name: 'sidebar',
  initialState,
  reducers: {
    setCreateContent: (state, action: PayloadAction<() => ReactNode>) => {
      state.createContent = action.payload
    },
    setInitialState: () => initialState,
  },
  selectors: {
    selectCreateContent: state => state.createContent,
  },
})

export const { setCreateContent } = sidebar.actions
export const { selectCreateContent } = sidebar.selectors
