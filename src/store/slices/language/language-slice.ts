import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { SupportedLanguage } from './types'

type InitialState = {
  currentLanguage: SupportedLanguage | null
}

const initialState: InitialState = {
  currentLanguage: null,
}

const languageSlice = createSlice({
  name: 'languageSlice',
  initialState,
  reducers: {
    setCurrentLanguage: (state, { payload }: PayloadAction<SupportedLanguage>) => {
      state.currentLanguage = payload
    },
  },
})

export const { setCurrentLanguage } = languageSlice.actions

export default languageSlice
