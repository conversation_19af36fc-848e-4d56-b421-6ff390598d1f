import { createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { INotification } from '@/shared/types/store/notification'

interface IInitialState {
  notifications: INotification[]
}

const initialState = {
  notifications: [],
} as IInitialState

const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    addNotification: (state, action: PayloadAction<INotification>) => {
      state.notifications.push(action.payload)
    },
    deleteNotification: (state, action: PayloadAction<UUID>) => {
      state.notifications = state.notifications.filter(i => i.id !== action.payload)
    },
  },
})

export const { addNotification, deleteNotification } = notificationSlice.actions
export default notificationSlice.reducer
