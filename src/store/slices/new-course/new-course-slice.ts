import { v4 as uuid } from 'uuid'
import {
  createEntityAdapter,
  createSlice,
  EntityState,
  PayloadAction,
  WithSlice,
} from '@reduxjs/toolkit'
import { ECourseVisibility } from '@/shared/types/store/course'
import { rootReducer } from '@/store/reducer'
import { RootState } from '@/store'
import { arrayMove } from '@dnd-kit/sortable'
import { CourseByIdResponse } from '@/entities/courses'
import { t } from 'i18next'

export type TThemeCourse = {
  id: UUID
  title: string
  order: number
  index: number
  hasStep?: boolean
}

export type TSectionCourse = {
  id: UUID
  title: string
  order: number
  index: number
  themes: TThemeCourse[]

  isTitleValidationError?: boolean
  isThemesValidationError?: boolean
}

const sectionsCourseAdapter = createEntityAdapter<TSectionCourse, UUID>({
  selectId: section => section.id,
  sortComparer: (a, b) => a.order - b.order,
})

const initialCourseSectionsState = sectionsCourseAdapter.setAll(
  sectionsCourseAdapter.getInitialState(),
  [
    {
      id: uuid(),
      title: `${t('commons:section')} 1`,
      order: 1,
      index: 0,
      themes: [],
    },
  ],
)

export const newCourseSectionsSelectors = sectionsCourseAdapter.getSelectors<RootState>(
  state => state.newCourse?.sections || initialCourseSectionsState,
)

type NewCourseState = {
  title: string
  description: string
  lockSequence: boolean
  visibility: ECourseVisibility
  isHaveTags: boolean
  isHaveSections: boolean
  sections: EntityState<TSectionCourse, UUID>
  isTitleValidationError: boolean
  image?: File
  tags?: string[]
  initialNewCourse: Pick<
    NewCourseState,
    'title' | 'description' | 'lockSequence' | 'image' | 'tags'
  > & { sections: { [key: string]: string }; imagePreview?: string }
}

const initialState: NewCourseState = {
  title: '',
  description: '',
  lockSequence: true,
  isHaveTags: false,
  isHaveSections: false,
  isTitleValidationError: false,
  visibility: ECourseVisibility.public,
  sections: initialCourseSectionsState,
  initialNewCourse: {
    title: '',
    description: '',
    lockSequence: true,
    tags: [],
    sections: {},
  },
}

const newCourseSlice = createSlice({
  name: 'newCourse',
  initialState,
  reducers: {
    resetNewCourseState: () => initialState,
    setNewCourseByCourseResponse: (state, { payload }: PayloadAction<CourseByIdResponse>) => {
      state.title = payload.title
      state.initialNewCourse.title = payload.title
      state.description = payload.description || ''
      state.initialNewCourse.description = payload.description || ''
      state.lockSequence = payload.lock_sequence
      state.initialNewCourse.lockSequence = payload.lock_sequence
      state.initialNewCourse.imagePreview = payload.image_path
      if (payload.tags && payload.tags.length > 0) {
        state.isHaveTags = true
        state.tags = [...payload.tags]
        state.initialNewCourse.tags = [...payload.tags]
      }

      if (payload.sections.length > 1) {
        state.isHaveSections = true
      }

      const newSections = Array.from(payload.sections)
        .sort((a, b) => a.order_id - b.order_id)
        .map((newSection, index) => ({
          id: newSection.id,
          index: index,
          order: newSection.order_id,
          title: newSection.title,
          themes: newSection.themes
            ? Array.from(newSection.themes)
                .sort((a, b) => a.order_id - b.order_id)
                .map((newTheme, themeIndex) => ({
                  id: newTheme.id,
                  title: newTheme.title,
                  order: newTheme.order_id,
                  index: themeIndex,
                }))
            : [],
        }))

      sectionsCourseAdapter.setAll(state.sections, newSections)

      payload.sections.forEach(
        section => (state.initialNewCourse.sections[section.id] = section.title),
      )
    },
    setNewCourseTitle: (state, { payload }: PayloadAction<string>) => {
      state.title = payload

      if (payload.length === 0) {
        state.isTitleValidationError = true
      } else {
        state.isTitleValidationError = false
      }
    },
    setNewCourseTitleValidationError: (state, { payload }: PayloadAction<boolean>) => {
      state.isTitleValidationError = payload
    },
    setNewCourseDescription: (state, { payload }: PayloadAction<string>) => {
      state.description = payload
    },
    setNewCourseTags: (state, { payload }: PayloadAction<string[]>) => {
      state.tags = payload
    },
    setIsNewCourseHaveTags: (state, { payload }: PayloadAction<boolean>) => {
      state.isHaveTags = payload
    },
    setIsNewCourseHaveSections: (state, { payload }: PayloadAction<boolean>) => {
      state.isHaveSections = payload
    },
    setNewCourseVisibility: (state, { payload }: PayloadAction<ECourseVisibility>) => {
      state.visibility = payload
    },
    setNewCourseSequence: (state, { payload }: PayloadAction<boolean>) => {
      state.lockSequence = payload
    },
    setNewCourseImage: (state, { payload }: PayloadAction<File | undefined>) => {
      state.image = payload
    },
    addNewCourseSection: (state, { payload }: PayloadAction<TSectionCourse>) => {
      sectionsCourseAdapter.addOne(state.sections, payload)
    },
    deleteNewCourseSection: (state, { payload }: PayloadAction<{ sectionDeleteId: UUID }>) => {
      const { sectionDeleteId } = payload
      const result = Object.values(state.sections.entities)
        .filter(prevSection => prevSection.id !== sectionDeleteId)
        .map((section, index) => ({ ...section, order: index + 1, index }))
      sectionsCourseAdapter.setAll(state.sections, result)
    },
    deleteAllSectionsExceptFirst: state => {
      const firstSection = { ...state.sections.entities[state.sections.ids[0]] }
      sectionsCourseAdapter.setAll(state.sections, [firstSection])
    },
    upCourseSectionOrder: (
      state,
      { payload }: PayloadAction<{ activeSectionId: UUID; passiveSectionId: UUID }>,
    ) => {
      const { activeSectionId, passiveSectionId } = payload
      const prevActiveOrder = state.sections.entities[activeSectionId].order
      const prevPassiveOrder = state.sections.entities[passiveSectionId].order
      const prevActiveIndex = state.sections.entities[activeSectionId].index
      const prevPassiveIndex = state.sections.entities[passiveSectionId].index

      sectionsCourseAdapter.updateMany(state.sections, [
        { id: activeSectionId, changes: { order: prevActiveOrder + 1, index: prevPassiveIndex } },
        { id: passiveSectionId, changes: { order: prevPassiveOrder - 1, index: prevActiveIndex } },
      ])
    },
    downCourseSectionOrder: (
      state,
      { payload }: PayloadAction<{ activeSectionId: UUID; passiveSectionId: UUID }>,
    ) => {
      const { activeSectionId, passiveSectionId } = payload
      const prevActiveOrder = state.sections.entities[activeSectionId].order
      const prevPassiveOrder = state.sections.entities[passiveSectionId].order
      const prevActiveIndex = state.sections.entities[activeSectionId].index
      const prevPassiveIndex = state.sections.entities[passiveSectionId].index

      sectionsCourseAdapter.updateMany(state.sections, [
        { id: activeSectionId, changes: { order: prevActiveOrder - 1, index: prevPassiveIndex } },
        { id: passiveSectionId, changes: { order: prevPassiveOrder + 1, index: prevActiveIndex } },
      ])
    },
    setCourseSectionTitle: (
      state,
      { payload }: PayloadAction<{ sectionId: UUID; title: string }>,
    ) => {
      const { sectionId, title } = payload
      sectionsCourseAdapter.updateOne(state.sections, {
        id: sectionId,
        changes: { title, isTitleValidationError: title.length === 0 },
      })
    },
    replaceAllThemeFromSection: (
      state,
      { payload }: PayloadAction<{ fromSectionId: UUID; toSectionId: UUID }>,
    ) => {
      const { fromSectionId, toSectionId } = payload

      const toThemes = [...state.sections.entities[toSectionId].themes]
      const fromThemes = [...state.sections.entities[fromSectionId].themes]
      const resultThemes = [...toThemes, ...fromThemes].map((theme, index) => ({
        ...theme,
        order: index + 1,
      }))

      sectionsCourseAdapter.updateMany(state.sections, [
        { id: fromSectionId, changes: { themes: [], isThemesValidationError: true } },
        { id: toSectionId, changes: { themes: resultThemes, isThemesValidationError: false } },
      ])
    },
    replaceThemeFromSection: (
      state,
      {
        payload,
      }: PayloadAction<{ fromSectionId: UUID; toSectionId: UUID; movingTheme: TThemeCourse }>,
    ) => {
      const { fromSectionId, toSectionId, movingTheme } = payload

      const fromThemes = [...state.sections.entities[fromSectionId].themes]
      const toThemes = [...state.sections.entities[toSectionId].themes]

      sectionsCourseAdapter.updateMany(state.sections, [
        {
          id: fromSectionId,
          changes: {
            themes: fromThemes
              .filter(fromTheme => fromTheme.id !== movingTheme.id)
              .map((newTheme, index) => ({ ...newTheme, order: index + 1 })),
            isThemesValidationError: fromThemes.length === 1,
          },
        },
        {
          id: toSectionId,
          changes: {
            themes: [...toThemes, movingTheme].map((newTheme, index) => ({
              ...newTheme,
              order: index + 1,
            })),
            isThemesValidationError: false,
          },
        },
      ])
    },
    addThemeCourseInSection: (
      state,
      { payload }: PayloadAction<{ sectionId: UUID; courseTheme: TThemeCourse }>,
    ) => {
      const { sectionId, courseTheme } = payload
      const sectionEntry = state.sections.entities[sectionId]
      const prevThemes = sectionEntry.themes

      sectionsCourseAdapter.updateOne(state.sections, {
        id: sectionId,
        changes: {
          themes: [
            ...prevThemes,
            {
              id: courseTheme.id,
              title: courseTheme.title,
              order: prevThemes.length + 1,
              index: prevThemes.length,
            },
          ],
          isThemesValidationError: false,
        },
      })
    },
    updateThemeTitleInSection: (
      state,
      { payload }: PayloadAction<{ sectionId: UUID; themeId: UUID; title: string }>,
    ) => {
      const { sectionId, themeId, title } = payload
      const sectionEntry = state.sections.entities[sectionId]
      const prevThemes = [...sectionEntry.themes]
      const resultThemes = prevThemes.map(sectionTheme =>
        sectionTheme.id === themeId ? { ...sectionTheme, title } : sectionTheme,
      )

      sectionsCourseAdapter.updateOne(state.sections, {
        id: sectionId,
        changes: {
          themes: resultThemes,
          isThemesValidationError: false,
        },
      })
    },
    addThemesCourseInSection: (
      state,
      { payload }: PayloadAction<{ sectionId: UUID; newThemesCourse: TThemeCourse[] }>,
    ) => {
      const { sectionId, newThemesCourse } = payload
      const sectionEntry = state.sections.entities[sectionId]
      const prevThemes = sectionEntry.themes
      const resultThemes = [...prevThemes, ...newThemesCourse].map((theme, index) => ({
        ...theme,
        order: index + 1,
        index,
      }))

      sectionsCourseAdapter.updateOne(state.sections, {
        id: sectionId,
        changes: {
          themes: resultThemes,
          isThemesValidationError: false,
        },
      })
    },
    deleteAllThemeInSection: (state, { payload }: PayloadAction<{ sectionId: UUID }>) => {
      const { sectionId } = payload
      sectionsCourseAdapter.updateOne(state.sections, {
        id: sectionId,
        changes: {
          themes: [],
          isThemesValidationError: true,
        },
      })
    },
    updateNewCourseSection: (
      state,
      { payload }: PayloadAction<{ sectionId: UUID; newValue: Partial<TSectionCourse> }>,
    ) => {
      const { sectionId, newValue } = payload
      sectionsCourseAdapter.updateOne(state.sections, {
        id: sectionId,
        changes: { ...newValue },
      })
    },
    deleteThemeCourseFromSection: (
      state,
      { payload }: PayloadAction<{ sectionId: UUID; themeIdForDelete: UUID }>,
    ) => {
      const { sectionId, themeIdForDelete } = payload
      const sectionEntry = state.sections.entities[sectionId]
      const prevThemes = sectionEntry.themes

      sectionsCourseAdapter.updateOne(state.sections, {
        id: sectionId,
        changes: {
          themes: prevThemes
            .filter(prevTheme => prevTheme.id !== themeIdForDelete)
            .map((newTheme, index) => ({
              ...newTheme,
              order: index + 1,
            })),
          isThemesValidationError: state.sections.ids.length === 1,
        },
      })
    },
    changeThemeCourseOrderInSection: (
      state,
      { payload }: PayloadAction<{ sectionId: UUID; oldThemeIndex: number; newThemeIndex: number }>,
    ) => {
      const { sectionId, oldThemeIndex, newThemeIndex } = payload
      const sectionEntry = state.sections.entities[sectionId]
      const themesWithNewOrder = arrayMove(
        [...sectionEntry.themes],
        oldThemeIndex,
        newThemeIndex,
      ).map((newTheme, index) => ({ ...newTheme, order: index + 1, index }))

      sectionsCourseAdapter.updateOne(state.sections, {
        id: sectionId,
        changes: { themes: themesWithNewOrder },
      })
    },
    setImagePreview: (state, { payload }: PayloadAction<string>) => {
      state.initialNewCourse.imagePreview = payload
    },
    resetNewCourseStateToInitial: state => {
      state.title = state.initialNewCourse.title
      state.description = state.initialNewCourse.description
      state.isHaveTags =
        state.initialNewCourse.tags && state.initialNewCourse.tags.length > 0 ? true : false
      state.tags = state.initialNewCourse.tags ? state.initialNewCourse.tags : undefined
      state.lockSequence = state.initialNewCourse.lockSequence

      state.sections.ids.forEach(sectionId => {
        state.sections.entities[sectionId].isTitleValidationError = false
        state.sections.entities[sectionId].isThemesValidationError = false
      })
    },
  },

  selectors: {
    selectNewCourse: (newCourseState: NewCourseState) => newCourseState,
    selectTitleNewCourse: (newCourseState: NewCourseState) => newCourseState.title,
    selectVisibilityCourse: (newCourseState: NewCourseState) => newCourseState.visibility,
    selectLockSequenceCourse: (newCourseState: NewCourseState) => newCourseState.lockSequence,
    selectDescriptionNewCourse: (newCourseState: NewCourseState) => newCourseState.description,
    selectIsNewCourseHaveTags: (newCourseState: NewCourseState) => newCourseState.isHaveTags,
    selectIsNewCourseHaveSections: (newCourseState: NewCourseState) =>
      newCourseState.isHaveSections,
    selectNewCourseTags: (newCourseState: NewCourseState) => newCourseState.tags,
    selectThemesByCourseId: (newCourseState: NewCourseState, sectionId: UUID) =>
      newCourseState.sections.entities[sectionId]?.themes,
    selectImagePreview: (newCourseState: NewCourseState) =>
      newCourseState.initialNewCourse.imagePreview,
    selectCourseImage: (newCourseState: NewCourseState) => newCourseState.image,
    selectInitialNewCourse: (newCourseState: NewCourseState) => newCourseState.initialNewCourse,
    selectSection: (newCourseState: NewCourseState, sectionId: UUID) =>
      newCourseState.sections.entities[sectionId],
    selectSectionTitle: (newCourseState: NewCourseState, sectionId: UUID) =>
      newCourseState.sections.entities[sectionId].title,
    selectIsTitleValidationError: (newCourseState: NewCourseState) =>
      newCourseState.isTitleValidationError,
  },
})

const injectedNewCourseSlice = newCourseSlice.injectInto(rootReducer)

export const {
  setNewCourseByCourseResponse,
  setIsNewCourseHaveSections,
  setIsNewCourseHaveTags,
  setNewCourseTitle,
  setNewCourseDescription,
  setNewCourseTags,
  setCourseSectionTitle,
  setNewCourseVisibility,
  setNewCourseSequence,
  setNewCourseImage,
  addNewCourseSection,
  upCourseSectionOrder,
  replaceAllThemeFromSection,
  addThemeCourseInSection,
  updateThemeTitleInSection,
  addThemesCourseInSection,
  deleteAllThemeInSection,
  deleteNewCourseSection,
  deleteAllSectionsExceptFirst,
  downCourseSectionOrder,
  changeThemeCourseOrderInSection,
  replaceThemeFromSection,
  deleteThemeCourseFromSection,
  setImagePreview,
  resetNewCourseState,
  resetNewCourseStateToInitial,
  updateNewCourseSection,
  setNewCourseTitleValidationError,
} = newCourseSlice.actions

export const {
  selectNewCourse,
  selectTitleNewCourse,
  selectVisibilityCourse,
  selectLockSequenceCourse,
  selectDescriptionNewCourse,
  selectNewCourseTags,
  selectIsNewCourseHaveSections,
  selectIsNewCourseHaveTags,
  selectThemesByCourseId,
  selectImagePreview,
  selectCourseImage,
  selectInitialNewCourse,
  selectSectionTitle,
  selectSection,
  selectIsTitleValidationError,
} = injectedNewCourseSlice.selectors

// Type declaration merging for LazyLoadedSlices in src/store/reducer
declare module '@/store/reducer' {
  export interface LazyLoadedSlices extends WithSlice<typeof newCourseSlice> {}
}
