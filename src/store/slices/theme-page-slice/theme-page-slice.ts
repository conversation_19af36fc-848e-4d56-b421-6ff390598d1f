import { createSlice, PayloadAction, WithSlice } from '@reduxjs/toolkit'
import { BreadcrumbItem } from '@/shared/ui'
import { URLS } from '@/shared/configs/urls'
import { rootReducer } from '@/store/reducer'
import { To } from 'react-router-dom'

type ThemePageState = {
  breadcrumbs: BreadcrumbItem[]
  navigateTo?: To
}

export const DEFAULT_BREADCRUMBS: BreadcrumbItem[] = [
  { id: URLS.ADMIN_LEARNING_THEMES_PAGE, text: 'commons:themes', clickable: true },
  { id: '', text: 'commons:theme_creation', clickable: false },
]

const initialState: ThemePageState = {
  breadcrumbs: DEFAULT_BREADCRUMBS,
}

const themePageSlice = createSlice({
  name: 'themePageSlice',
  initialState,
  reducers: {
    setThemePageBreadcrumbs: (state, { payload }: PayloadAction<BreadcrumbItem[]>) => {
      state.breadcrumbs = payload
    },
    resetThemePageState: () => initialState,
    setNavigateTo: (state, { payload }: PayloadAction<To>) => {
      state.navigateTo = payload
    },
  },

  selectors: {
    selectThemePageBreadcrumbs: (newCourseState: ThemePageState) => newCourseState.breadcrumbs,
    selectNavigateTo: (newCourseState: ThemePageState) => newCourseState.navigateTo,
  },
})

const injectedThemePageSlice = themePageSlice.injectInto(rootReducer)

export const { setThemePageBreadcrumbs, resetThemePageState, setNavigateTo } =
  injectedThemePageSlice.actions

export const { selectThemePageBreadcrumbs, selectNavigateTo } = injectedThemePageSlice.selectors

// Type declaration merging for LazyLoadedSlices in src/store/reducer
declare module '@/store/reducer' {
  export interface LazyLoadedSlices extends WithSlice<typeof themePageSlice> {}
}
