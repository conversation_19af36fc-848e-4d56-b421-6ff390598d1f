import { RootState } from '@/store'
import { createSelector } from '@reduxjs/toolkit'
import { selectCurrentSelectedUsersCount, selectSelectAll } from '@/shared/modals/organization-tree'

const selectFilterValues = createSelector(
  (state: RootState) => state,
  state => state.phishing.filterValues,
)

const selectFilterStatuses = createSelector(
  (state: RootState) => state,
  state => state.phishing.filterValues.statuses,
)

const selectFilterOses = createSelector(
  (state: RootState) => state,
  state => state.phishing.filterValues.oses,
)

const selectFilterInputStatuses = createSelector(
  (state: RootState) => state,
  state => state.phishing.filterInputValues.statuses,
)

const selectFilterInputOses = createSelector(
  (state: RootState) => state,
  state => state.phishing.filterInputValues.oses,
)

const selectCreateCampaign = createSelector(
  (state: RootState) => state,
  state => state.phishing.createCampaign,
)

const selectCreateCampaignByEnding = createSelector(
  (state: RootState) => state,
  state => state.phishing.createCampaignByEnding,
)

const selectCreateCampaignForm = createSelector(
  (state: RootState) => state,
  state => state.phishing.createCampaignForm,
)

const selectCreateCampaignByEndingForm = createSelector(
  (state: RootState) => state,
  state => state.phishing.createCampaignForm?.post_campaign,
)

const selectCampaignIsAllSelected = createSelector(
  [(state: RootState) => selectSelectAll(state)],
  isSelectAllFromTree => {
    return isSelectAllFromTree
  },
)

const selectActualCampaignSelectedUsersCount = createSelector(
  [
    selectCreateCampaignForm,
    selectCampaignIsAllSelected,
    (state: RootState) => selectCurrentSelectedUsersCount(state),
  ],
  (_, isAllSelected, dynamicCount) => {
    if (isAllSelected) {
      return dynamicCount
    }

    return undefined
  },
)

export {
  selectFilterValues,
  selectFilterStatuses,
  selectFilterInputStatuses,
  selectFilterOses,
  selectFilterInputOses,
  selectCreateCampaign,
  selectCreateCampaignForm,
  selectCreateCampaignByEndingForm,
  selectCreateCampaignByEnding,
  selectCampaignIsAllSelected,
  selectActualCampaignSelectedUsersCount,
}
