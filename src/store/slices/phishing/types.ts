import { ITargetData } from '@/entities/target'
import { ITagWithNew } from '@/shared/types/store/tag'

export type TTargetAutoCourseType = 'opened' | 'clicked' | 'entered_data' | 'opened_attachment'
export type TTargetType = 'custom' | 'all' | 'filtered' | 'by_selected_campaigns'
export type TDateType = 'auto' | 'choose'
export type TRedirectType = 'original' | 'choose'
export type TEmailSendEndType = 'to_end' | 'choose'

export type CreateCampaign = {
  dateType: TDateType
  emailSendType: TEmailSendEndType
  enableRedirectPage: boolean
  targetType: TTargetType
  enableCreateCampaignByEnding: boolean
  enableCreateCampaignByEndingSubmit?: boolean
}

export type CreateCampaignForm = {
  name: string
  enableRedirect: boolean
  redirect_page?: string
  isTesting: boolean
  isDelay: boolean
  hasAutoCourse: boolean
  period?: number
  targets?: ITargetData
  excludedTags?: ITagWithNew[]
  templates?: UUID[]
  targetsAutoCourse?: TTargetAutoCourseType[]
  course?: UUID | null
  hasMessage: boolean
  targetsMessage?: TTargetAutoCourseType[]
  theme?: string
  text?: string
  dateEnd?: Date | null
  dateDelay?: Date | null
  email_send_end_date?: Date | null
  post_campaign?: CreateCampaignByEndingForm
  post_campaign_targets?: TTargetAutoCourseType[]
}

export type CreateCampaignByEndingForm = Omit<CreateCampaignForm, 'targets' | 'post_campaign'>
