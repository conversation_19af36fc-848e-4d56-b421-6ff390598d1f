import { createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { IListItem } from '@/shared/ui'
import { CreateCampaign, CreateCampaignForm } from './types'

export const DEFAULT_CAMPAIGN: CreateCampaignForm = {
  name: '',
  enableRedirect: false,
  isTesting: false,
  hasAutoCourse: false,
  redirect_page: undefined,
  isDelay: false,
  course: null,
  templates: [],
  targets: {
    target_users: [],
    target_departments: [],
  },
  excludedTags: [],
  targetsAutoCourse: ['opened'],
  period: 30,
  hasMessage: false,
  targetsMessage: ['opened'],
  theme: '',
  text: '',
  dateEnd: null,
  dateDelay: null,
  email_send_end_date: undefined,
  post_campaign: {
    name: '',
    enableRedirect: false,
    isTesting: false,
    hasAutoCourse: false,
    redirect_page: undefined,
    isDelay: false,
    course: null,
    templates: [],
    excludedTags: [],
    targetsAutoCourse: ['opened'],
    period: 30,
    hasMessage: false,
    targetsMessage: ['opened'],
    theme: '',
    text: '',
    dateEnd: null,
    dateDelay: null,
    email_send_end_date: undefined,
  },
  post_campaign_targets: ['opened'],
}

const DEFAULT_CREATE_CAMPAIGN: CreateCampaign = {
  dateType: 'auto',
  emailSendType: 'to_end',
  enableRedirectPage: false,
  targetType: 'custom',
  enableCreateCampaignByEnding: false,
}
const DEFAULT_CREATE_CAMPAIGN_BY_ENDING: Omit<CreateCampaign, 'enableCreateCampaignByEnding'> = {
  dateType: 'auto',
  emailSendType: 'to_end',
  enableRedirectPage: false,
  targetType: 'custom',
}

interface IInitialState {
  filterInputValues: {
    statuses?: IListItem[]
    oses?: IListItem[]
  }
  filterValues: {
    statuses?: IListItem[]
    oses?: IListItem[]
  }
  createCampaign: CreateCampaign
  createCampaignByEnding: Omit<CreateCampaign, 'enableCreateCampaignByEnding'>
  createCampaignForm: CreateCampaignForm
}

const initialState = {
  filterInputValues: {
    statuses: undefined,
    oses: undefined,
  },
  filterValues: {
    statuses: undefined,
    oses: undefined,
  },
  createCampaign: DEFAULT_CREATE_CAMPAIGN,
  createCampaignByEnding: DEFAULT_CREATE_CAMPAIGN_BY_ENDING,
  createCampaignForm: DEFAULT_CAMPAIGN,
} as IInitialState

const phishingSlice = createSlice({
  name: 'phishingSlice',
  initialState,
  reducers: {
    setFilterInputStatus: (state, { payload }: PayloadAction<IListItem>) => {
      if (!state.filterInputValues.statuses) {
        state.filterInputValues.statuses = [payload]
      } else {
        const { id: selectedId } = payload
        const selectedIds = state.filterInputValues.statuses.map(filterValue => filterValue.id)
        const oldStatuses = [...state.filterInputValues.statuses]

        if (selectedIds?.includes(selectedId)) {
          state.filterInputValues.statuses = [
            ...oldStatuses.filter(filterValue => filterValue.id !== selectedId),
          ]
        } else {
          state.filterInputValues.statuses = [...oldStatuses, payload]
        }
      }
    },
    setFilterInputOs: (state, { payload }: PayloadAction<IListItem>) => {
      if (!state.filterInputValues.oses) {
        state.filterInputValues.oses = [payload]
      } else {
        const { id: selectedId } = payload
        const selectedIds = state.filterInputValues.oses.map(filterValue => filterValue.id)
        const oldOses = [...state.filterInputValues.oses]

        if (selectedIds?.includes(selectedId)) {
          state.filterInputValues.oses = [
            ...oldOses.filter(filterValue => filterValue.id !== selectedId),
          ]
        } else {
          state.filterInputValues.oses = [...oldOses, payload]
        }
      }
    },
    setFilterValues: state => {
      if (state.filterInputValues.statuses) {
        state.filterValues.statuses = state.filterInputValues.statuses
      }
      if (state.filterInputValues.oses) {
        state.filterValues.oses = state.filterInputValues.oses
      }
    },
    clearFilterValues: state => {
      state.filterValues.statuses = undefined
      state.filterInputValues.statuses = undefined
      state.filterValues.oses = undefined
      state.filterInputValues.oses = undefined
    },
    setCreateCampaign: (
      state,
      { payload }: PayloadAction<Partial<IInitialState['createCampaign']>>,
    ) => {
      const campaign = state?.createCampaign
      state.createCampaign = { ...campaign, ...payload }
    },
    setCreateCampaignByEnding: (
      state,
      { payload }: PayloadAction<Partial<IInitialState['createCampaignByEnding']>>,
    ) => {
      const campaign = state?.createCampaignByEnding
      state.createCampaignByEnding = { ...campaign, ...payload }
    },
    setCreateCampaignForm: (
      state,
      { payload }: PayloadAction<Partial<IInitialState['createCampaignForm']>>,
    ) => {
      const campaignForm = state?.createCampaignForm
      state.createCampaignForm = { ...campaignForm, ...payload }
    },
    setPostCampaignForm: (
      state,
      { payload }: PayloadAction<Partial<IInitialState['createCampaignForm']['post_campaign']>>,
    ) => {
      const postCampaignForm = state?.createCampaignForm?.post_campaign ?? {
        name: '',
        enableRedirect: false,
        isTesting: false,
        hasAutoCourse: false,
        redirect_page: undefined,
        isDelay: false,
        course: null,
        templates: [],
        excludedTags: [],
        targetsAutoCourse: ['opened'],
        period: 30,
        hasMessage: false,
        targetsMessage: ['opened'],
        theme: '',
        text: '',
        dateEnd: null,
        dateDelay: null,
        email_send_end_date: undefined,
        post_campaign: undefined,
        targets: ['opened'],
      }

      state.createCampaignForm.post_campaign = {
        ...postCampaignForm,
        ...payload,
      }
    },
    setInitialForms: state => {
      state.createCampaignForm = { ...DEFAULT_CAMPAIGN }
      state.createCampaignByEnding = {
        ...DEFAULT_CREATE_CAMPAIGN_BY_ENDING,
      }
      state.createCampaign = { ...DEFAULT_CREATE_CAMPAIGN }
    },
  },
})

export const {
  setFilterValues,
  clearFilterValues,
  setFilterInputStatus,
  setFilterInputOs,
  setCreateCampaign,
  setCreateCampaignForm,
  setPostCampaignForm,
  setCreateCampaignByEnding,
  setInitialForms,
} = phishingSlice.actions

export default phishingSlice
