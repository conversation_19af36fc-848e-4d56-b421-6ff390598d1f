import { createEntityAdapter, createSlice } from '@reduxjs/toolkit'
import type { EntityState, PayloadAction } from '@reduxjs/toolkit'
import {
  ITagAction,
  ITagCourse,
  ITagTarget,
  ITagMessage,
  IUpdateActionBody,
} from '@/shared/types/store/tag'
import { ITag } from '@/shared/types/store/tag'
import { phishingQueries } from '@/entities/phishing'
import { type IPhishingCampaignTemplate } from '@/entities/phishing'
import { ETagType } from '../../../shared/types/enums'
import { TAG_LIFE_DAYS_LIMIT } from '@/shared/constants'
import { tagsEmployeesApi } from '@/store/services/tags-employees-service'

type ModalVariants =
  | 'courseSelector'
  | 'scormCourseSelector'
  | 'templateSelector'
  | 'reassignCourses'
  | 'turnOff'
  | 'phishingWarning'

interface IInitialState {
  tags?: ITag[]
  page: number
  courses?: ITagCourse[]
  messages?: ITagMessage[]
  actions?: ITagAction[]
  tagLifeDays?: number
  initialTagLifeDays?: number
  riskLevelMin?: number
  riskLevelMax?: number
  activeTag?: ITag | null
  initialMessages: EntityState<ITagMessage, UUID>
  tagTargets: {
    users?: ITagTarget[]
    departments?: ITagTarget[]
    initialUsers: EntityState<string, UUID>
    initialDepartments: EntityState<string, UUID>
    excludeUsersIds: UUID[]
  }
  modals: Record<ModalVariants, boolean>
  selectedCourses?: UUID[]
  selectedScormCourses?: UUID[]
  phishingTemplates?: UUID[]
  turnOff: boolean
  authophishingSettingsByIdData?: IPhishingCampaignTemplate[]
}

export const initialMessagesAdapter = createEntityAdapter<ITagMessage, UUID>({
  selectId: message => message.id,
})

export const initialUsersTargetAdapter = createEntityAdapter<UUID, UUID>({
  selectId: userId => userId,
})

export const initialDepartmentsTargetAdapter = createEntityAdapter<UUID, UUID>({
  selectId: departmentId => departmentId,
})

const initialState = {
  activeTag: undefined,
  actions: undefined,
  tagLifeDays: undefined,
  tags: undefined,
  page: 1,
  riskLevelMin: 0,
  riskLevelMax: 10,
  modals: {
    courseSelector: false,
    scormCourseSelector: false,
    reassignCourses: false,
    templateSelector: false,
    turnOff: false,
    phishingWarning: false,
  },
  tagTargets: {
    initialUsers: initialUsersTargetAdapter.getInitialState(),
    initialDepartments: initialDepartmentsTargetAdapter.getInitialState(),
    excludeUsersIds: [],
  },
  turnOff: false,
  initialMessages: initialMessagesAdapter.getInitialState(),
} as IInitialState

const tagsSlice = createSlice({
  name: 'tagsSlice',
  initialState,
  reducers: {
    setPage: (state, { payload }: PayloadAction<number>) => {
      state.page = payload
    },
    setTurnOff: (state, { payload }: PayloadAction<boolean>) => {
      state.turnOff = payload
    },
    setActions: (state, { payload }: PayloadAction<ITagAction[]>) => {
      state.actions = payload
    },
    setInitialMessages: (state, { payload }: PayloadAction<ITagMessage[]>) => {
      initialMessagesAdapter.setAll(state.initialMessages, payload)
    },
    setInitialMessage: (state, { payload }: PayloadAction<ITagMessage>) => {
      initialMessagesAdapter.setOne(state.initialMessages, payload)
    },
    updateInitialMessage: (state, { payload }: PayloadAction<ITagMessage>) => {
      initialMessagesAdapter.upsertOne(state.initialMessages, payload)
    },
    removeInitialMessage: (state, { payload }: PayloadAction<UUID>) => {
      initialMessagesAdapter.removeOne(state.initialMessages, payload)
    },
    setUsersTagTarget: (state, { payload }: PayloadAction<ITagTarget[]>) => {
      state.tagTargets.users = payload
    },
    setInitialUsersTagTarget: (state, { payload }: PayloadAction<UUID[]>) => {
      initialUsersTargetAdapter.setAll(state.tagTargets.initialUsers, payload)
    },
    removeInitialUserTagTarget: (state, { payload }: PayloadAction<UUID>) => {
      initialUsersTargetAdapter.removeOne(state.tagTargets.initialUsers, payload)
    },
    setDepartmentsTagTarget: (state, { payload }: PayloadAction<ITagTarget[]>) => {
      state.tagTargets.departments = payload
    },
    setInitialDepartmentsTagTarget: (state, { payload }: PayloadAction<UUID[]>) => {
      initialDepartmentsTargetAdapter.setAll(state.tagTargets.initialDepartments, payload)
    },
    setExcludeUsersIds: (state, { payload }: PayloadAction<UUID[]>) => {
      state.tagTargets.excludeUsersIds = payload ?? []
    },
    addAction: (state, { payload }: PayloadAction<ITagAction>) => {
      state.actions = state?.actions ? [...state.actions, payload] : [payload]
    },
    addActions: (state, { payload }: PayloadAction<ITagAction[]>) => {
      state.actions = state?.actions ? [...state.actions, ...payload] : [...payload]
    },
    updateAction: (state, { payload }: PayloadAction<{ id: UUID; body: IUpdateActionBody }>) => {
      const { id, body } = payload
      const {
        theme,
        isNew,
        text,
        courses,
        type,
        phishing_templates,
        defer_by,
        registration_link_days,
        registration_link_time,
      } = body || {}

      state.actions = state.actions?.map(action => {
        if (action.id !== id) return action

        const newItem = JSON.parse(JSON.stringify(action)) as ITagAction

        if (isNew !== undefined) newItem.isNew = isNew
        if (phishing_templates) newItem.phishing_templates = phishing_templates
        if (defer_by !== undefined) {
          const MIN_DATE = 0
          const MAX_DATE = state.tagLifeDays || 0
          const validateDate = Math.max(
            MIN_DATE,
            Math.min(MAX_DATE > 0 ? MAX_DATE - 1 : 0, defer_by),
          )

          newItem.defer_by = validateDate <= 99 ? validateDate : 99
        }
        if (theme !== undefined) newItem.theme = theme
        if (text !== undefined) newItem.text = text
        if (courses) newItem.courses = courses
        if (type !== undefined) newItem.type = type
        if (registration_link_days !== undefined)
          newItem.registration_link_days = registration_link_days
        if (registration_link_time !== undefined)
          newItem.registration_link_time = registration_link_time

        return { ...action, ...newItem }
      })
    },
    deleteAction: (state, { payload }: PayloadAction<UUID>) => {
      state.actions = state.actions?.filter(i => i.id !== payload)
    },
    setTagsModalValue: (
      state,
      {
        payload,
      }: PayloadAction<{
        name: ModalVariants
        value: boolean
      }>,
    ) => {
      state.modals[payload.name] = payload.value || false
    },
    setSelectedCourses: (state, { payload }: PayloadAction<UUID[] | undefined>) => {
      state.selectedCourses = payload
    },
    setSelectedScormCourses: (state, { payload }: PayloadAction<UUID[] | undefined>) => {
      state.selectedScormCourses = payload
    },
    setActiveTag: (state, { payload }: PayloadAction<ITag | null>) => {
      state.activeTag = payload

      state.turnOff = Boolean(payload?.is_active)

      if (payload?.type === 'newbie') {
        state.tagLifeDays = payload?.settings.life_days ?? TAG_LIFE_DAYS_LIMIT
      } else {
        state.tagLifeDays = 100
      }
      state.initialTagLifeDays = payload?.settings.life_days ?? TAG_LIFE_DAYS_LIMIT

      state.riskLevelMax = payload?.settings.max_risk_level || payload?.settings.min_risk_level || 0

      state.riskLevelMin = payload?.settings.min_risk_level || 0
    },
    setTagLifeDays: (state, { payload }: PayloadAction<number | undefined>) => {
      let validateDate: number
      if (!payload) {
        validateDate = 0
      } else {
        const MIN_DATE = 0
        const MAX_DATE = 999
        validateDate = Math.max(MIN_DATE, Math.min(MAX_DATE, payload))
      }

      state.actions = state.actions?.map(action => {
        if (!action.defer_by) return action

        const newItem = JSON.parse(JSON.stringify(action)) as ITagAction

        if (
          validateDate !== undefined &&
          action.defer_by !== undefined &&
          action.defer_by >= validateDate
        ) {
          newItem.defer_by = 0
        }

        return { ...action, ...newItem }
      })

      state.tagLifeDays = validateDate
    },
    setRiskLevel: (
      state,
      {
        payload,
      }: PayloadAction<
        | {
            value: number
            name: string
          }
        | undefined
      >,
    ) => {
      if (!payload) return

      const { value, name } = payload

      const MIN_DATE = 0
      const MAX_DATE = 10

      switch (name) {
        case 'min': {
          const validatedValue = Math.max(MIN_DATE, Math.min(MAX_DATE, value))
          state.riskLevelMin = validatedValue

          if (!state?.riskLevelMax || validatedValue > state?.riskLevelMax) {
            state.riskLevelMax = validatedValue
          }
          break
        }
        case 'max': {
          const validatedValue = Math.max(MIN_DATE, Math.min(MAX_DATE, value))
          state.riskLevelMax = validatedValue

          if (state.riskLevelMin && validatedValue < state.riskLevelMin) {
            state.riskLevelMin = validatedValue
          }
          break
        }
        default: {
          break
        }
      }
    },
  },
  extraReducers: builder => {
    builder
      .addMatcher(
        tagsEmployeesApi.endpoints.getOrganizationTags.matchFulfilled,
        (state, { payload }) => {
          state.tags = payload?.data

          if (state.activeTag) return

          const tag = payload?.data?.find(tag => tag.type === ETagType.newbie) || payload?.data?.[0]

          state.activeTag = tag ?? null
          state.turnOff = tag?.is_active ?? false
          state.tagLifeDays = tag?.settings.life_days ?? TAG_LIFE_DAYS_LIMIT
          state.initialTagLifeDays = tag?.settings.life_days ?? TAG_LIFE_DAYS_LIMIT

          state.riskLevelMax = tag?.settings.max_risk_level || tag?.settings.min_risk_level || 0
          state.riskLevelMin = tag?.settings.min_risk_level || 0

          return state
        },
      )
      .addMatcher(
        tagsEmployeesApi.endpoints.getActionsByTag.matchFulfilled,
        (state, { payload }) => {
          state.phishingTemplates = payload ? payload.phishing?.email_templates : undefined
        },
      )
      .addMatcher(
        phishingQueries.endpoints.getAutophishingSettingsById.matchFulfilled,
        (state, { payload }) => {
          state.authophishingSettingsByIdData = payload
        },
      )
      .addMatcher(
        tagsEmployeesApi.endpoints.updateLifeDays.matchFulfilled,
        (state, { payload }) => {
          state.initialTagLifeDays = payload.settings.life_days ?? TAG_LIFE_DAYS_LIMIT
        },
      )
  },
})

export const {
  setSelectedCourses,
  setSelectedScormCourses,
  setActiveTag,
  setRiskLevel,
  setTagLifeDays,
  setTagsModalValue,
  setActions,
  setUsersTagTarget,
  setExcludeUsersIds,
  setDepartmentsTagTarget,
  setInitialUsersTagTarget,
  setInitialDepartmentsTagTarget,
  removeInitialUserTagTarget,
  setInitialMessages,
  setInitialMessage,
  updateInitialMessage,
  removeInitialMessage,
  addAction,
  deleteAction,
  updateAction,
  addActions,
  setTurnOff,
  setPage,
} = tagsSlice.actions

export default tagsSlice
