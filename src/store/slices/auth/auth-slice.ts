import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { userAPI } from 'entities/employee'

type AuthSteps = 'permissions' | 'twofa' | 'twofa__assign'

interface IInitialState {
  _inited?: boolean
  authorized: boolean
  steps: Partial<Record<AuthSteps, boolean>>
  twofa__assign?: string
}

const initialState = {
  _inited: false,
  authorized: false,
  steps: {},
} as IInitialState

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    initAuthData: state => {
      state._inited = true
    },
    setTwofaAssignCode: (state, { payload }: PayloadAction<string>) => {
      state.twofa__assign = payload
    },
    setAuthSteps: (state, { payload }: PayloadAction<Partial<Record<AuthSteps, boolean>>>) => {
      state.steps = { ...state.steps, ...payload }
    },
    setLoginToken: (state, { payload }: PayloadAction<string>) => {
      localStorage.setItem('token', JSON.stringify(payload))
      state.authorized = true
    },
    logIn: state => {
      state.authorized = true
    },
    logOut: () => {
      localStorage.removeItem('token')
      return { ...initialState, _inited: true }
    },
  },
  extraReducers: builder => {
    builder
      .addMatcher(userAPI.endpoints.getUserInfo.matchFulfilled, state => {
        state.authorized = true
        state._inited = true
      })
      .addMatcher(userAPI.endpoints.getUserInfo.matchRejected, state => {
        state.authorized = false
        state._inited = true
      })
  },
})

export const { setLoginToken, logIn, logOut, setTwofaAssignCode, initAuthData, setAuthSteps } =
  authSlice.actions

export default authSlice.reducer
