import { createApi } from '@reduxjs/toolkit/query/react'
import {
  IMyCourseWithPagination,
  IMyScormCourseWithPagination,
  IMyScormCourse,
  IMyAssignedModule,
  IMyCourseWithModule,
} from '@/shared/types/store/user-courses'

import { baseQueryWithReauth } from '../helper'

export const myCoursesAPI = createApi({
  reducerPath: 'myCoursesAPI',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'my-courses',
    'my-course',
    'my-scorm-courses',
    'my-scorm-course',
    'my-scorm-course-statement',
    'my-modules',
    'my-quiz',
  ],
  endpoints: build => ({
    //! course
    getMyCourses: build.query<IMyCourseWithPagination, void>({
      query: () => ({
        url: `/learning/assigned-courses/my-courses`,
      }),
      providesTags: ['my-courses'],
    }),
    getMyCourse: build.query<IMyCourseWithModule, UUID>({
      query: id => ({
        url: `/learning/assigned-courses/my-courses/${id}`,
      }),
      providesTags: ['my-course'],
    }),
    //! scorm course
    getMyScormCourses: build.query<IMyScormCourseWithPagination, void>({
      query: () => ({
        url: '/scorm/assigned-courses/my-courses',
      }),
      providesTags: ['my-scorm-courses'],
    }),
    getMyScormCourse: build.query<IMyScormCourse, UUID>({
      query: id => ({
        url: `/scorm/assigned-courses/my-courses/${id}`,
      }),
      providesTags: ['my-scorm-course'],
    }),
    getMyScormCourseStatement: build.query<
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      Nullable<Record<string, any>>,
      { courseId: UUID; userId: UUID }
    >({
      query: ({ courseId, userId }) => ({
        url: `/scorm/statement/${courseId}/${userId}`,
      }),
      providesTags: ['my-scorm-course-statement'],
    }),
    postMyScormCourseStatement: build.mutation<
      IMyScormCourse,
      { statement: unknown; courseId: UUID; userId: UUID }
    >({
      query: ({ courseId, userId, statement }) => ({
        url: `/scorm/statement`,
        body: {
          statement: statement,
          user_id: userId,
          course_id: courseId,
        },
        method: 'POST',
      }),
      invalidatesTags: [],
    }),
    getMyAssignedModules: build.query<IMyAssignedModule, UUID>({
      query: id => ({
        url: `/learning/assigned-modules/${id}?with_slides=true&with_questions=true&`,
      }),
      providesTags: ['my-modules'],
    }),
    createQuiz: build.mutation<{ quiz_id: UUID }, UUID>({
      query: id => ({
        url: `/learning/assigned-modules/${id}/quiz`,
        method: 'POST',
      }),
    }),
  }),
})

export const {
  useGetMyCoursesQuery,
  useGetMyCourseQuery,
  useGetMyScormCoursesQuery,
  useGetMyScormCourseQuery,
  useGetMyScormCourseStatementQuery,
  useLazyGetMyScormCourseStatementQuery,
  usePostMyScormCourseStatementMutation,
  useGetMyAssignedModulesQuery,
  useCreateQuizMutation,
} = myCoursesAPI
