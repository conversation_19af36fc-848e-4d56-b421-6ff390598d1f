import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import { getBaseQuery } from '../helper'
import { IPPTXEvent } from '@/shared/types/store/notification'

export const eventKeeperAPI = createApi({
  reducerPath: 'eventKeeperAPI',
  baseQuery: fetchBaseQuery(getBaseQuery(true, {}, true)),
  tagTypes: ['event'],
  endpoints: build => ({
    getEvent: build.query<IPPTXEvent, UUID>({
      query: id => ({
        url: `/${id}`,
      }),
      providesTags: ['event'],
    }),
  }),
})

export const { useLazyGetEventQuery } = eventKeeperAPI
