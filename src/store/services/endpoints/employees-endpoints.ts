import {
  IDeleteEmployeesRequest,
  IEditEmployeeResponse,
  IEmployeesImportWithFileInfo,
  IEmployeesReinviteRequest,
  IEmployeesExportRequest,
  IEmployeesStatWithData,
  IGetEmployee,
  IUser,
  IUsers,
} from 'entities/employee'
import { TagsEmployeesApiEndpointBuilder } from '../tags-employees-service'
import { EmployeeEdit } from '@/shared/modals/edit-employee-modal'
import { EmployeeCreate } from '@/shared/modals/create-employees-modal'
import { EntityId } from '@reduxjs/toolkit'
import { getFilterQuery, getHighRole } from '@/shared/helpers/employees'
import { EMPLOYEES_LIMIT } from '@/shared/constants'
import { Report } from 'shared/types/store/settings'
import { BACKEND_URLS } from '@/shared/constants/urls'
import { IUsersResponse } from '@/entities/employee/model/types'

export const getEmployeesEndpoints = () => {
  return (build: TagsEmployeesApiEndpointBuilder) =>
    ({
      getEmployees: build.query<IUsers, IGetEmployee>({
        query: ({ page = 0, filter, limit = EMPLOYEES_LIMIT, sort, sortDirection }) => {
          let query = getFilterQuery(filter)

          query += `&with_risk_level=true`
          query += `&limit=${limit}`
          query += `&offset=${page * limit}`

          if (sort) query += `&sort_by=${sort}`
          if (sortDirection) query += `&sort_order=${sortDirection}`

          return {
            url: `${BACKEND_URLS.USER_SERVICE}/users${query}&`,
          }
        },
        transformResponse: (response: IUsersResponse) => {
          const newUsers = [
            ...response.data.map(({ roles, ...user }) => ({
              ...user,
              role: getHighRole(roles),
            })),
          ]
          return {
            ...response,
            data: newUsers,
          }
        },
        providesTags: ['employees'],
        keepUnusedDataFor: 30,
      }),
      getModalEmployees: build.query<IUsers, IGetEmployee>({
        query: ({ page = 0, filter, limit = EMPLOYEES_LIMIT, sort, sortDirection }) => {
          let query = `?search=${filter.search}`

          query += `&limit=${limit}`
          query += `&offset=${page * limit}`

          if (sort) query += `&sort_by=${sort}`
          if (sortDirection) query += `&sort_order=${sortDirection}`

          return {
            url: `${BACKEND_URLS.USER_SERVICE}/users/modal${query}&`,
          }
        },
        transformResponse: (response: IUsersResponse) => {
          const newUsers = [
            ...response.data.map(({ roles, ...user }) => ({
              ...user,
              role: getHighRole(roles),
            })),
          ]
          return {
            ...response,
            data: newUsers,
          }
        },
        providesTags: ['employees'],
        keepUnusedDataFor: 30,
      }),
      editEmployee: build.mutation<
        ResponseWithNotification<IEditEmployeeResponse>,
        EmployeeEdit & { id: UUID }
      >({
        query: employee => {
          const { id, last_name, email, first_name, middle_name, position, role, department } =
            employee

          const body = department && {
            department_id: department.id,
            role: role,
            last_name,
            first_name,
            middle_name,
            position,
            email,
          }

          return {
            url: `${BACKEND_URLS.USER_SERVICE}/users/${id}`,
            method: 'PATCH',
            body,
          }
        },
        invalidatesTags: ['employees'],
        transformResponse: (response: IEditEmployeeResponse) => ({
          ...response,
          notificationTip: 'tips:employess.update',
        }),
      }),
      createEmployee: build.mutation<ResponseWithNotification<void>, EmployeeCreate>({
        query: employee => {
          const {
            last_name,
            first_name,
            middle_name,
            position,
            role,
            department,
            email,
            need_message = true,
          } = employee

          const body = department && {
            department: { id: department.id, title: department.title },
            role: role,
            last_name,
            first_name,
            middle_name,
            position,
            email,
          }

          return {
            url: `${BACKEND_URLS.USER_SERVICE}/users/employee/add`,
            method: 'POST',
            params: {
              need_message,
            },
            body,
          }
        },
        invalidatesTags: ['employees'],
        transformResponse: () => ({
          notificationTip: 'tips:employess.create',
        }),
      }),
      deleteEmployee: build.mutation<ResponseWithNotification<void>, UUID>({
        query: id => {
          return {
            url: `${BACKEND_URLS.USER_SERVICE}/users/${id}`,
            method: 'DELETE',
          }
        },
        invalidatesTags: ['employees'],
        transformResponse: () => ({
          notificationTip: 'tips:employess.delete',
        }),
      }),
      deleteEmployees: build.mutation<ResponseWithNotification<void>, IDeleteEmployeesRequest>({
        query: ({ ids: body, all, filter }) => {
          let query = `${getFilterQuery(filter)}`

          if (all) {
            query += '&need_all=true'
          }

          return {
            url: `${BACKEND_URLS.USER_SERVICE}/users/bulk-delete${query}`,
            method: 'POST',
            body: body,
          }
        },
        invalidatesTags: ['employees'],
        transformResponse: () => ({
          notificationTip: 'tips:employess.delete_many',
        }),
      }),
      reinviteEmployees: build.mutation<ResponseWithNotification<void>, IEmployeesReinviteRequest>({
        query: ({ ids: body, all, filter }) => {
          let query = `${getFilterQuery(filter)}`

          if (all) {
            query += '&need_all=true'
          }

          return {
            url: `${BACKEND_URLS.USER_SERVICE}/users/reinvite${query}`,
            method: 'POST',
            body: body,
          }
        },
        transformResponse: () => ({
          notificationTip: 'tips:employess.reinvite',
        }),
      }),
      exportEmployees: build.mutation<Report, IEmployeesExportRequest>({
        query: ({ ids: body, all, filter }) => {
          let query = `${getFilterQuery(filter)}`

          if (all) {
            query += '&need_all=true'
          }

          return {
            url: `${BACKEND_URLS.USER_SERVICE}/users/filtered-users-report${query}`,
            method: 'POST',
            body: body,
          }
        },
      }),
      getEmployessReport: build.query<ResponseWithNotification<Report>, UUID>({
        query: id => ({
          url: `v2/reports/${id}`,
        }),
        providesTags: ['employeesStat'],
        transformResponse: (response: Report) => {
          if (response.status === 'complete' && response.url) {
            return {
              ...response,
              notificationTip: 'tips:report.success',
            }
          }

          return response
        },
      }),
      getEmployeesStat: build.query<
        IEmployeesStatWithData,
        { parentID?: UUID | null; search?: string }
      >({
        query: ({ search, parentID: parent_id }) => {
          const params: {
            parent_id?: UUID | null
            search?: string
            registered_users_only?: boolean
          } = {}

          if (search) params.search = search
          if (parent_id) params.parent_id = parent_id
          params.registered_users_only = false

          return {
            url: `v2/departments/hierarchy/users`,
            params,
          }
        },
        providesTags: ['employeesStat'],
        transformResponse: (response: IEmployeesStatWithData, _, arg) => {
          return { ...response, parentID: arg.parentID }
        },
      }),
      getEmployeesByTag: build.query<{ data: IUser[] } & ResponseWithPagination, UUID>({
        query: tagId => ({
          url: `${BACKEND_URLS.USER_SERVICE}/tags/${tagId}/users`,
        }),
        transformResponse: (response: IUsersResponse) => {
          const newUsers = response.data.map(({ roles, ...user }) => ({
            ...user,
            role: getHighRole(roles),
          }))
          return {
            ...response,
            data: newUsers,
          }
        },
        keepUnusedDataFor: 60000 * 10,
        providesTags: ['employeesTags'],
      }),
      assignTagToEntities: build.mutation<
        void,
        {
          tagID: UUID
          usersIds: UUID[] | EntityId[]
          departmentsIds: UUID[] | EntityId[]
          needOnBoarding?: boolean
          exclude_users_ids?: UUID[]
        }
      >({
        query: ({ tagID, usersIds, departmentsIds, needOnBoarding, exclude_users_ids }) => ({
          url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/assign`,
          method: 'POST',
          body: {
            tag_id: tagID,
            users_ids: usersIds,
            departments_ids: departmentsIds,
            need_onboarding: Boolean(needOnBoarding),
            exclude_users_ids,
          },
        }),
        invalidatesTags: ['employeesTags', 'employees'],
      }),
      removeTagFromEmployee: build.mutation<void, { tagIds: UUID[]; userID: UUID | EntityId }>({
        query: ({ tagIds, userID }) => ({
          url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/users/${userID}/remove`,
          method: 'PUT',
          body: {
            tag_ids: tagIds,
          },
        }),
        invalidatesTags: ['employeesTags', 'employees'],
      }),
      removeTagFromEntities: build.mutation<
        void,
        {
          tagID: UUID
          usersIds: UUID[] | EntityId[]
          departmentsIds: UUID[] | EntityId[]
        }
      >({
        query: ({ tagID, usersIds, departmentsIds }) => ({
          url: `${BACKEND_URLS.USER_SERVICE}/tags/custom/${tagID}/users/bulk-delete`,
          method: 'POST',
          body: { departments_ids: departmentsIds, users_ids: usersIds },
        }),
        invalidatesTags: ['employeesTags', 'employees'],
      }),
      importEmployeesWithFileInfo: build.mutation<IEmployeesImportWithFileInfo, FormData>({
        query: body => ({
          url: `${BACKEND_URLS.USER_SERVICE}/users/employee/csv/info`,
          body,
          method: 'POST',
        }),
      }),
      importEmployeesWithFile: build.mutation<
        IEmployeesImportWithFileInfo,
        {
          type: 'basic' | 'update' | 'delete' | 'full'
          need_message?: boolean
          event_id: UUID
        }
      >({
        query: ({ event_id, type, need_message = true }) => ({
          url: `${BACKEND_URLS.USER_SERVICE}/users/employee/csv/process`,
          method: 'POST',
          params: {
            add_type: type,
            need_message,
            event_id,
          },
        }),
      }),
      getEmployeeEventInfo: build.query<IEmployeesImportWithFileInfo, UUID>({
        query: eventId => ({
          url: `${BACKEND_URLS.USER_SERVICE}/events/${eventId}`,
        }),
      }),
      deleteEventId: build.mutation<ResponseWithNotification<void>, UUID>({
        query: event_id => {
          return {
            url: `${BACKEND_URLS.USER_SERVICE}/events/${event_id}`,
            method: 'DELETE',
          }
        },
      }),
    }) as const
}
