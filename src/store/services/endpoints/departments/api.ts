import { createApi } from '@reduxjs/toolkit/query/react'
import { baseQueryWithReauth } from '../../../helper'

export const reducerPath = 'departments-tree'

export type TagTypes = ''

const tagTypes: TagTypes[] = []

const createDepartmentsTreeApi = () =>
  createApi({
    reducerPath,
    baseQuery: baseQueryWithReauth,
    tagTypes,
    endpoints: () => ({}),
  })

export const departmentsTreeApi = createDepartmentsTreeApi()
