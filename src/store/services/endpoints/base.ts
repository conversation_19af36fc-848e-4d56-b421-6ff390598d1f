import { createApi, EndpointBuilder } from '@reduxjs/toolkit/query/react'
import { baseQueryWithReauth, baseUrl } from '@/store/helper'

const tagTypes = [
  'themes',
  'assigned-course',
  'assigned-courses',
  'assigned-courses-employees',
  'courses',
  'theme',
  'quiz',
  'slide',
  'video',
  'presentation',
  'article',
  'scorm',
  'steps',
  'gallery',
  'courses-by-tag',
  'email-template',
  'email-templates',
  'default-emails-templates',
  'course',
  'pdf-reports',
  'all-reports',
  'me',
  'organization',
  'LDAP',
  'SMTP',
  'adfs',
  'superusers',
  'instructions',
  'templates',
  'page-by-id',
  'emails',
  'email',
  'autophish',
  'statuses',
  'campaign',
  'campaigns',
  'campaign-template',
  'template-by-id',
  'campaign-templates',
  'phishing-tag',
  'redirect-pages',
  'changelogs',
  'employee-statistic-note',
  'employee-statistic-info',
  'department',
  'department-stat',
  'department-tree',
  'user',
  'organization',
  'employees-group',
  'invite-reminder-config',
  'employees',
  'password-policies',
  'shared-courses',
] as const

export type GlobalTagTypes = (typeof tagTypes)[number]

export type GlobalEndpointBuilder = EndpointBuilder<
  typeof baseQueryWithReauth,
  GlobalTagTypes,
  typeof globalBaseApi.reducerPath
>

export const globalBaseApi = createApi({
  baseQuery: (args, api, extraOptions) =>
    baseQueryWithReauth({ ...args, baseUrl }, api, extraOptions),
  tagTypes,
  endpoints: () => ({}),
  reducerPath: 'globalBaseApi',
})

export type GlobalBaseApi = typeof globalBaseApi
