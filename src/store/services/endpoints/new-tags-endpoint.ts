import { globalBaseApi } from './base'

type AssignCoursesByTag = {
  tag_id: string
  courses_ids: string[]
}

type UpdateCoursesByTag = {
  tag_id: string
  courses_ids: string[]
}

type CourseByTag = {
  id: string
  title: string
  description: string
  organization_id: string
  visibility: string
  archived: boolean
  available_in_demo: boolean
  image_path: string
  created_at: string
  lock_sequence: boolean
  tags: string | undefined[]
}

type CoursesResponse = CourseByTag[]

export const coursesByTagApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getCoursesByTag: builder.query<CoursesResponse, { tag_id: string }>({
      query: ({ tag_id }) => ({
        url: `/learning/api/courses-by-tag/${tag_id}`,
        method: 'GET',
      }),
      providesTags: (_, __, arg) => [{ type: 'courses-by-tag', id: arg.tag_id }],
    }),
    deleteCoursesByTag: builder.mutation<void, { tag_id: string }>({
      query: ({ tag_id }) => ({
        url: `/learning/api/courses-by-tag/${tag_id}`,
        method: 'DELETE',
      }),

      // invalidatesTags: (_, __, arg) => [{ type: 'courses-by-tag', id: arg.tag_id }],
    }),
    updateCoursesByTag: builder.mutation<void, UpdateCoursesByTag>({
      query: body => ({
        url: `/learning/api/courses-by-tag`,
        method: 'PATCH',
        body,
      }),
      // invalidatesTags: (_, __, arg) => [{ type: 'courses-by-tag', id: arg.tag_id }],
    }),
    assignCoursesByTag: builder.mutation<void, AssignCoursesByTag>({
      query: body => ({
        url: `/learning/api/courses-by-tag`,
        method: 'POST',
        body,
      }),
      // invalidatesTags: (_, __, arg) => [{ type: 'courses-by-tag', id: arg.tag_id }],
    }),
  }),
})
