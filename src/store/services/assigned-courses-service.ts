import { createApi } from '@reduxjs/toolkit/query/react'

import { baseQueryWithReauth } from '../helper'
import {
  TAssignedCourse,
  IAssignedCoursesWithData,
  IAssgnedScromCourse,
  IAssgnedScromCourseWithData,
  IAssignScormCourseRequest,
} from '@/shared/types/store/course'
import { IToggleAssignedCourseRequest } from '@/shared/types/store/assigned-course'

export const assignedCoursesAPI = createApi({
  reducerPath: 'assignedCoursesAPI',
  baseQuery: baseQueryWithReauth,
  tagTypes: ['assigned-courses', 'assigned-scorm-courses'],
  endpoints: build => ({
    // Модульные
    getAssignedCourses: build.query<TAssignedCourse[], void>({
      query: () => ({
        url: `/learning/assigned-courses/organization`,
      }),
      transformResponse: (response: IAssignedCoursesWithData) => response.data,
      providesTags: ['assigned-courses'],
    }),
    deleteAssignedCourse: build.mutation<ResponseWithNotification<void>, UUID>({
      query: id => ({
        url: `/learning/assigned-courses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['assigned-courses'],
      transformResponse: () => ({
        notificationTip: 'tips:course.delete',
      }),
    }),
    assignCourse: build.mutation<ResponseWithNotification<{ assigned_course_id: UUID }>, FormData>({
      query: body => ({
        url: `/learning/assigned-courses`,
        method: 'POST',
        body,
        formData: true,
      }),
      invalidatesTags: ['assigned-courses'],
      transformResponse: (response: { assigned_course_id: UUID }) => ({
        ...response,
        notificationTip: 'tips:course.assign',
      }),
    }),
    // Скорм
    getAssignedScormCourses: build.query<IAssgnedScromCourse[], void>({
      query: () => ({
        url: `/scorm/assigned-courses`,
      }),
      transformResponse: (response: IAssgnedScromCourseWithData) => response.data,
      providesTags: ['assigned-scorm-courses'],
    }),
    assignScormCourse: build.mutation<{ assigned_course_id: UUID }, IAssignScormCourseRequest>({
      query: body => ({
        url: `/scorm/assigned-courses`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['assigned-scorm-courses'],
      transformResponse: (response: { assigned_course_id: UUID }) => ({
        ...response,
        notificationTip: 'tips:course.assign',
      }),
    }),
    deleteScormAssignedCourse: build.mutation<ResponseWithNotification<void>, UUID>({
      query: id => ({
        url: `/scorm/assigned-courses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['assigned-scorm-courses'],
      transformResponse: () => ({
        notificationTip: 'tips:course.delete',
      }),
    }),
    updatePictureForScormAssignedCourse: build.mutation<
      ResponseWithNotification<void>,
      { id: UUID; body: FormData }
    >({
      query: ({ id, body }) => ({
        url: `/scorm/assigned-courses/${id}/picture`,
        method: 'PATCH',
        body,
        formData: true,
      }),
      invalidatesTags: ['assigned-scorm-courses'],
    }),
    // Модульные и скорм
    toggleAssignedCourse: build.mutation<
      ResponseWithNotification<void>,
      IToggleAssignedCourseRequest
    >({
      query: ({ params, id, isScorm }) => ({
        url: `/${isScorm ? 'scorm' : 'learning'}/assigned-courses/${id}/toggle`,
        method: 'POST',
        params,
      }),
      invalidatesTags: (_, __, arg) => {
        return arg.isScorm ? ['assigned-scorm-courses'] : ['assigned-courses']
      },
    }),
  }),
})

export const {
  // Модульные
  useGetAssignedCoursesQuery,
  useDeleteAssignedCourseMutation,
  useAssignCourseMutation,
  // Скорм
  useGetAssignedScormCoursesQuery,
  useDeleteScormAssignedCourseMutation,
  useAssignScormCourseMutation,
  useUpdatePictureForScormAssignedCourseMutation,
  // Модульные и скорм
  useToggleAssignedCourseMutation,
} = assignedCoursesAPI
