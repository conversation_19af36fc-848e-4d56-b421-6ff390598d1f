import { createApi } from '@reduxjs/toolkit/query/react'

import { baseQueryWithReauth } from '../helper'
import { IGroups } from '@/shared/types/store/groups'
import { IProgress, IProgressChange } from '@/shared/types/store/course'
import { INewCourse } from '@/shared/types/deprecated'
import {
  IToggleAssignedCourseRequest,
  IUpdateAssignedCourseRequest,
} from '@/shared/types/store/assigned-course'
import { ICourseEmployees, IScormCourseEmployees } from '@/shared/types/store/assigned-courses'
import { ITargetData } from '@/entities/target'

export const assignedCourseAPI = createApi({
  reducerPath: 'assignedCourseAPI',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'groups',
    'progress',
    'progress-change',
    'assigned-course-data',
    'employees',
    'scorm-groups',
    'scorm-progress',
    'scorm-progress-change',
    'scorm-assigned-course-data',
    'scorm-employees',
  ],
  endpoints: build => ({
    getGroups: build.query<IGroups, UUID>({
      query: id => ({
        url: `/analytics/operator/assigned_course/${id}/groups`,
      }),
      providesTags: ['groups'],
    }),
    getProgress: build.query<IProgress, UUID>({
      query: id => ({
        url: `/analytics/operator/assigned_course/${id}/progress`,
      }),
      providesTags: ['progress'],
    }),
    getProgressChange: build.query<IProgressChange, UUID>({
      query: id => ({
        url: `/analytics/operator/assigned_course/${id}/progress_change`,
      }),
      providesTags: ['progress-change'],
    }),
    getAssignedCourseData: build.query<INewCourse, UUID>({
      query: id => ({
        url: `/learning/assigned-courses/${id}`,
      }),
      providesTags: ['assigned-course-data'],
    }),
    completeAssignedCourse: build.mutation<ResponseWithNotification<void>, UUID>({
      query: id => ({
        url: `/learning/assigned-courses/${id}/complete`,
        method: 'POST',
      }),
      invalidatesTags: ['assigned-course-data'],
      transformResponse: () => ({
        notificationTip: 'tips:course.complete',
      }),
    }),
    // SCORM
    getScormGroups: build.query<IGroups, UUID>({
      query: id => ({
        url: `/scorm/analytics/assigned_course/${id}/groups`,
      }),
      providesTags: ['scorm-groups'],
    }),
    getScormProgress: build.query<IProgress, UUID>({
      query: id => ({
        url: `/scorm/analytics/assigned_course/${id}/progress`,
      }),
      providesTags: ['scorm-progress'],
    }),
    getScormProgressChange: build.query<IProgressChange, UUID>({
      query: id => ({
        url: `/scorm/analytics/assigned_course/${id}/progress_change`,
      }),
      providesTags: ['scorm-progress-change'],
    }),
    getScormAssignedCourseData: build.query<INewCourse, UUID>({
      query: id => ({
        url: `/scorm/assigned-courses/${id}`,
      }),
      providesTags: ['scorm-assigned-course-data'],
    }),
    completeScormAssignedCourse: build.mutation<ResponseWithNotification<void>, UUID>({
      query: id => ({
        url: `/scorm/assigned-courses/${id}/complete`,
        method: 'POST',
      }),
      invalidatesTags: ['scorm-assigned-course-data'],
      transformResponse: () => ({
        notificationTip: 'tips:course.complete',
      }),
    }),
    // Модульные и scorm
    updateAssignedCourse: build.mutation<
      ResponseWithNotification<void>,
      IUpdateAssignedCourseRequest
    >({
      query: ({ body, id, isScorm }) => ({
        url: `/${isScorm ? 'scorm' : 'learning'}/assigned-courses/${id}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: (_, __, { isScorm }) =>
        isScorm ? ['scorm-assigned-course-data'] : ['assigned-course-data'],
      transformResponse: () => ({
        notificationTip: 'tips:course.update',
      }),
    }),
    toggleAssignedCourse: build.mutation<
      ResponseWithNotification<void>,
      IToggleAssignedCourseRequest
    >({
      query: ({ params, id, isScorm }) => ({
        url: `/${isScorm ? 'scorm' : 'learning'}/assigned-courses/${id}/toggle`,
        method: 'POST',
        params,
      }),
      invalidatesTags: (_, __, { isScorm }) =>
        isScorm ? ['scorm-assigned-course-data'] : ['assigned-course-data'],
    }),
    // Сотрудники
    getEmployees: build.query<ICourseEmployees | IScormCourseEmployees, UUID | void>({
      query: id => ({
        url: id?.startsWith('scorm-')
          ? `/scorm/assigned-courses/${id?.replace('scorm-', '')}/employees`
          : `/learning/assigned-courses/${id}/employees`,
      }),
      providesTags: (_, __, arg) =>
        arg?.startsWith('scorm-') ? ['scorm-employees'] : ['employees'],
      transformResponse(response: ICourseEmployees | IScormCourseEmployees) {
        if ('data' in response) {
          return response as IScormCourseEmployees
        }

        return response as ICourseEmployees
      },
    }),
    deleteEmployee: build.mutation<
      ResponseWithNotification<void>,
      { targets: ITargetData; courseID: UUID }
    >({
      query: ({ targets, courseID }) => ({
        url: courseID?.startsWith('scorm-')
          ? `/scorm/assigned-courses/${courseID?.replace('scorm-', '')}/employees/delete/`
          : `/learning/assigned-courses/${courseID}/employees/delete/`,
        method: 'POST',
        body: targets,
      }),
      invalidatesTags: (_, __, arg) =>
        arg.courseID?.startsWith('scorm-') ? ['scorm-employees'] : ['employees'],
      transformResponse: () => ({
        notificationTip: 'tips:employess.delete',
      }),
    }),
    addEmployees: build.mutation<
      ResponseWithNotification<void>,
      { targets: ITargetData; courseID: UUID }
    >({
      query: ({ targets, courseID }) => ({
        url: courseID?.startsWith('scorm-')
          ? `/scorm/assigned-courses/${courseID?.replace('scorm-', '')}/employees/create`
          : `/learning/assigned-courses/${courseID}/employees`,
        method: 'POST',
        body: targets,
      }),
      invalidatesTags: (_, __, arg) =>
        arg.courseID?.startsWith('scorm-') ? ['scorm-employees'] : ['employees'],
      transformResponse: () => ({
        notificationTip: 'tips:employess.add',
      }),
    }),
    deleteEmployees: build.mutation<
      ResponseWithNotification<void>,
      { targets: ITargetData; courseID: UUID }
    >({
      query: ({ targets, courseID }) => {
        return {
          url: courseID?.startsWith('scorm-')
            ? `/scorm/assigned-courses/${courseID?.replace('scorm-', '')}/employees/delete`
            : `/learning/assigned-courses/${courseID}/employees/delete`,
          method: 'POST',
          body: targets,
        }
      },
      invalidatesTags: (_, __, arg) =>
        arg.courseID?.startsWith('scorm-') ? ['scorm-employees'] : ['employees'],
      transformResponse: () => ({
        notificationTip: 'tips:employess.delete_many',
      }),
    }),
  }),
})

export const {
  // Модульные
  useGetGroupsQuery,
  useGetProgressQuery,
  useGetProgressChangeQuery,
  useGetAssignedCourseDataQuery,
  useCompleteAssignedCourseMutation,
  // SCORM
  useGetScormGroupsQuery,
  useGetScormProgressQuery,
  useGetScormProgressChangeQuery,
  useGetScormAssignedCourseDataQuery,
  useCompleteScormAssignedCourseMutation,
  // Модульные и scorm
  useUpdateAssignedCourseMutation,
  useToggleAssignedCourseMutation,
  // Сотрудники
  useGetEmployeesQuery,
  useDeleteEmployeeMutation,
  useDeleteEmployeesMutation,
  useAddEmployeesMutation,
} = assignedCourseAPI
