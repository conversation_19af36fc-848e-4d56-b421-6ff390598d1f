import { createApi, EndpointBuilder } from '@reduxjs/toolkit/query/react'
import { baseQueryWithReauth, baseUrl } from '../helper'
import { getTagsEndpoints } from './endpoints/tags-endpoints'
import { getEmployeesEndpoints } from './endpoints/employees-endpoints'

const reducerPath = 'tagsEmployeesAPI'
type TagTypes =
  | 'tags'
  | 'courses'
  | 'scormCourses'
  | 'messages'
  | 'employees'
  | 'employeesStat'
  | 'employeesTags'

const tagTypes: TagTypes[] = [
  'tags',
  'courses',
  'scormCourses',
  'messages',
  'employees',
  'employeesStat',
  'employeesTags',
]

const createTagsEmployeesBaseApi = () => {
  return createApi({
    reducerPath,
    baseQuery: (args, api, extraOptions) =>
      baseQueryWithReauth({ ...args, baseUrl }, api, extraOptions),
    tagTypes,
    endpoints: () => ({}),
  })
}

export type TagsEmployeesApiEndpointBuilder = EndpointBuilder<
  typeof baseQueryWithReauth,
  TagTypes,
  typeof reducerPath
>

const baseApi = createTagsEmployeesBaseApi()

export const tagsEmployeesApi = baseApi
  .injectEndpoints({
    endpoints: getTagsEndpoints(baseApi),
  })
  .injectEndpoints({ endpoints: getEmployeesEndpoints() })

export const {
  // tags hooks
  useGetOrganizationTagsQuery,
  useGetCoursesByTagQuery,
  useGetScormCoursesByTagQuery,
  useGetActionsByTagQuery,
  useLazyGetActionsByTagQuery,
  useUpdateMessageMutation,
  useCreateMessageMutation,
  useDeleteMessageMutation,
  useUpdateLifeDaysMutation,
  useUpdateRiskLevelMutation,
  useCreateCoursesMutation,
  useUpdateCoursesMutation,
  useDeleteCoursesMutation,
  useCreateScormCoursesMutation,
  useUpdateScormCoursesMutation,
  useDeleteScormCoursesMutation,
  useToggleTagMutation,
  useCreateCustomTagMutation,
  useEditCustomTagMutation,
  useDeleteCustomTagMutation,
  // employees hooks
  useGetEmployeesQuery,
  useLazyGetModalEmployeesQuery,
  useDeleteEmployeeMutation,
  useDeleteEmployeesMutation,
  useGetEmployeesStatQuery,
  useLazyGetEmployeesStatQuery,
  useImportEmployeesWithFileInfoMutation,
  useImportEmployeesWithFileMutation,
  useGetEmployeeEventInfoQuery,
  useDeleteEventIdMutation,
  useImportTagEmployeesWithFileInfoMutation,
  useImportTagEmployeesWithFileMutation,
  useGetEmployeesByTagQuery,
  useLazyGetEmployeesByTagQuery,
  useAssignTagToEntitiesMutation,
  useRemoveTagFromEmployeeMutation,
  useRemoveTagFromEntitiesMutation,
  useReinviteEmployeesMutation,
  useExportEmployeesMutation,
  useGetEmployessReportQuery,
} = tagsEmployeesApi
