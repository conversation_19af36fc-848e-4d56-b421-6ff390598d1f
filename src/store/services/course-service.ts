/* eslint-disable @typescript-eslint/no-explicit-any */
import { createApi } from '@reduxjs/toolkit/query/react'
import { ICourse, IScormCourse } from '@/shared/types/store/course'
import { baseQueryWithReauth } from '../helper'

const REFETCH_DELAY = 1200

export const coursesAPI = createApi({
  reducerPath: 'coursesAPI',
  baseQuery: baseQueryWithReauth,
  tagTypes: [
    'courses',
    'scrom-course-by-id',
    'courses-archived-scorm',
    'course-by-id',
    'scorm-courses',
    'courses-archived',
  ],
  endpoints: build => ({
    // Модульные
    getCourses: build.query<ICourse[], void>({
      query: () => ({
        url: `/library/courses`,
      }),
      transformResponse: (response: { data: ICourse[] }) => response.data,
      providesTags: ['courses'],
    }),
    getArchivedCourses: build.query<
      ICourse[],
      {
        by_tag?: boolean
        limit?: number
        offset?: number
      } | void
    >({
      query: params => ({
        url: `/admin/content/library/courses/archived`,
        params: params ?? undefined,
      }),
      transformResponse: (response: { data: ICourse[] }) => response.data,
      providesTags: ['courses-archived'],
    }),
    getCourse: build.query<ICourse, UUID>({
      query: id => ({
        url: `/library/courses/${id}`,
      }),
      providesTags: ['course-by-id'],
    }),
    putCourse: build.mutation<ResponseWithNotification<ICourse>, { body: FormData; id: UUID }>({
      query: ({ body, id }) => ({
        url: `/library/courses/${id}`,
        method: 'PUT',
        body,
        formData: true,
      }),
      //! Задержка, чтобы на беке усели обновиться данные
      // Нужно добавить обновиление курса по id
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        await queryFulfilled
        setTimeout(() => {
          dispatch(coursesAPI.util.invalidateTags(['courses', 'course-by-id']))
        }, REFETCH_DELAY)
      },
      transformResponse: (response: ICourse) => ({
        ...response,
        notificationTip: 'tips:course.update',
      }),
    }),
    postCourse: build.mutation<ResponseWithNotification<ICourse>, FormData>({
      query: body => ({
        url: `/library/courses`,
        method: 'POST',
        body,
        formData: true,
      }),
      invalidatesTags: ['courses'],
      transformResponse: (response: ICourse) => ({
        ...response,
        notificationTip: 'tips:course.create',
      }),
    }),
    deleteCourse: build.mutation<ResponseWithNotification<void>, UUID>({
      query: id => ({
        url: `/library/courses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['courses'],
      transformResponse: () => ({
        notificationTip: 'tips:course.delete',
      }),
    }),
    // Скорм
    getScormCourses: build.query<ICourse[], boolean>({
      query: tested => ({
        url: `/scorm/courses/organization`,
        params: { tested },
      }),
      transformResponse: (response: { data: ICourse[] }) => response.data,
      providesTags: ['scorm-courses'],
    }),
    getArchivedScormCourses: build.query<
      ICourse[],
      {
        by_tag?: boolean
        limit?: number
        offset?: number
      } | void
    >({
      query: params => ({
        url: `/admin/content/scorm/courses/archived`,
        params: params ?? undefined,
      }),
      transformResponse: (response: { data: ICourse[] }) => response.data,
      providesTags: ['courses-archived-scorm'],
    }),
    getScormCourse: build.query<IScormCourse, UUID>({
      query: id => ({
        url: `/scorm/courses/${id}`,
      }),
      providesTags: ['scrom-course-by-id'],
    }),
    postScormCourse: build.mutation<ResponseWithNotification<any>, FormData>({
      query: body => ({
        url: `/scorm/courses`,
        method: 'POST',
        body,
        formData: true,
      }),
      invalidatesTags: ['scorm-courses'],
    }),
    patchScormCourse: build.mutation<ResponseWithNotification<any>, { body: FormData; id: UUID }>({
      query: ({ body, id }) => ({
        url: `/scorm/courses/${id}`,
        method: 'PATCH',
        body,
        formData: true,
      }),
      //! Задержка, чтобы на беке усели обновиться данные
      async onQueryStarted(_, { dispatch, queryFulfilled }) {
        await queryFulfilled
        setTimeout(() => {
          dispatch(coursesAPI.util.invalidateTags(['scrom-course-by-id', 'scorm-courses']))
        }, REFETCH_DELAY)
      },
      transformResponse: (response: any) => ({
        ...response,
        notificationTip: 'tips:course.update',
      }),
    }),
    deleteScormCourse: build.mutation<ResponseWithNotification<void>, UUID>({
      query: id => ({
        url: `/scorm/courses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['scorm-courses'],
      transformResponse: () => ({
        notificationTip: 'tips:course.delete',
      }),
    }),
    // TEMPTODO-889 - перенесте в api сотрудника
    // Назначенные курсы
  }),
})

export const {
  // Модульные
  useGetArchivedCoursesQuery,
  useGetCoursesQuery,
  usePutCourseMutation,
  usePostCourseMutation,
  useGetCourseQuery,
  // Скорм
  useGetArchivedScormCoursesQuery,
  useGetScormCoursesQuery,
  useGetScormCourseQuery,
  usePostScormCourseMutation,
  usePatchScormCourseMutation,
  useDeleteCourseMutation,
  useDeleteScormCourseMutation,
} = coursesAPI
