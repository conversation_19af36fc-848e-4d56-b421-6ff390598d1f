/* eslint-disable react-hooks/exhaustive-deps */
import { FC, PropsWithChildren, useLayoutEffect } from 'react'
import { useThemeInjector } from './shared/hooks'
import { getDefaultBrandingConfig } from './shared/configs/app-config/get-default-branding-config'

export const IndexConfig: FC<PropsWithChildren> = props => {
  const env = process.env.API_ENV || process.env.NODE_ENV || 'development'

  const data = getDefaultBrandingConfig(env)

  useLayoutEffect(() => {
    const configScript = document.getElementById('config')

    if (!configScript || configScript?.textContent?.trim() !== '{{{config_json}}}') return

    configScript.innerText = JSON.stringify({
      ...data,
      config_json: JSON.stringify(data, null, 4),
    })
  }, [data])

  useThemeInjector()

  return props.children
}
