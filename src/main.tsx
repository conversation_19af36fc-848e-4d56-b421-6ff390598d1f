/* eslint-disable @typescript-eslint/no-explicit-any */
import ReactDOM from 'react-dom/client'
import App from './App'
import './index.css'
import { IndexConfig } from './index-html.transform'

const rootElement = document.getElementById('root')

if (!rootElement) {
  throw new Error('No root element!')
}

const root = ReactDOM.createRoot(rootElement)

root.render(
  <>
    <IndexConfig />
    <App />
  </>,
)
