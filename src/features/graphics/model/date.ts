import * as d3 from 'd3'

type ValueItem = {
  value: number
  date: Date | string
}

export const getGraphicDates = (values: ValueItem[], locale = 'default') => {
  // Сортируем массив по дате
  values.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

  const minDate = new Date(values[0].date)
  const maxDate = new Date(values[values.length - 1].date)
  const diffMonths =
    (maxDate.getFullYear() - minDate.getFullYear()) * 12 + maxDate.getMonth() - minDate.getMonth()
  const currentYear = new Date().getFullYear()

  const labels: string[] = []
  let formatter: Intl.DateTimeFormat

  if (diffMonths >= 12) {
    // Если разница больше или равна 12 месяцам
    const step = diffMonths / 12
    for (let i = 0; i <= 12; i++) {
      const date = new Date(minDate.getFullYear(), minDate.getMonth() + i * step, 1)
      formatter = new Intl.DateTimeFormat(locale, {
        month: 'short',
        year: 'numeric',
        day: 'numeric',
      })
      if (date.getFullYear() === currentYear) {
        labels.push(date.toLocaleString(locale, { month: 'long' }))
      } else {
        labels.push(formatter.format(date))
      }
    }

    return labels
  }

  // Если разница меньше 12 месяцев
  for (let i = 0; i <= diffMonths; i++) {
    const date = new Date(minDate.getFullYear(), minDate.getMonth() + i, 1)
    formatter = new Intl.DateTimeFormat(locale, {
      month: 'short',
      year: 'numeric',
      day: 'numeric',
    })
    if (date.getFullYear() === currentYear) {
      labels.push(date.toLocaleString(locale, { month: 'long', day: 'numeric' }))
    } else {
      labels.push(formatter.format(date))
    }
  }

  return labels
}

/* eslint-disable i18n/no-russian-character */
/* TODO: does anyone use this? */

const russianLocale = d3.timeFormatLocale({
  dateTime: '%A, %e %B %Y г. %X',
  date: '%-d %b %Y',
  time: '%H:%M:%S',
  periods: ['AM', 'PM'],
  days: ['воскресенье', 'понедельник', 'вторник', 'среда', 'четверг', 'пятница', 'суббота'],
  shortDays: ['вс', 'пн', 'вт', 'ср', 'чт', 'пт', 'сб'],
  months: [
    'января',
    'февраля',
    'марта',
    'апреля',
    'мая',
    'июня',
    'июля',
    'августа',
    'сентября',
    'октября',
    'ноября',
    'декабря',
  ],
  shortMonths: ['янв', 'фев', 'мар', 'апр', 'май', 'июн', 'июл', 'авг', 'сен', 'окт', 'ноя', 'дек'],
})
/* eslint-enable i18n/no-russian-character */

export const formatDate = russianLocale.format('%-d %b %Y')
