import { RangeItem } from './types'

export const DEFAULT_TIME_RANGE: RangeItem[] = [
  { text: '1_mon', value: 1 },
  { text: '3_mon', value: 3 },
  { text: '6_mon', value: 6 },
  { text: 'year', value: 12 },
]

export interface IChartColor {
  offset: number
  stopColor: { r: number; g: number; b: number; a: number }
  stopOpacity?: string
}

export const redToGreenAreaColor: IChartColor[] = [
  {
    offset: 0,
    stopColor: { r: 255, g: 227, b: 224, a: 1 },
  },
  {
    offset: 25,
    stopColor: { r: 255, g: 239, b: 226, a: 1 },
  },
  {
    offset: 50,
    stopColor: { r: 249, g: 247, b: 237, a: 1 },
  },
  {
    offset: 75,
    stopColor: { r: 247, g: 250, b: 244, a: 1 },
  },
  {
    offset: 100,
    stopColor: { r: 255, g: 255, b: 255, a: 1 },
  },
]

export const redToGreenLineColor: IChartColor[] = [
  {
    offset: 0,
    stopColor: { r: 255, g: 88, b: 107, a: 1 },
  },
  {
    offset: 50,
    stopColor: { r: 255, g: 178, b: 87, a: 1 },
  },
  {
    offset: 100,
    stopColor: { r: 61, g: 188, b: 135, a: 1 },
  },
]

export const greenToRedAreaColor: IChartColor[] = [
  {
    offset: 0,
    stopColor: { r: 225, g: 242, b: 229, a: 1 },
  },
  {
    offset: 25,
    stopColor: { r: 241, g: 241, b: 225, a: 1 },
  },
  {
    offset: 50,
    stopColor: { r: 255, g: 243, b: 232, a: 1 },
  },
  {
    offset: 75,
    stopColor: { r: 255, g: 244, b: 239, a: 1 },
  },
  {
    offset: 100,
    stopColor: { r: 255, g: 255, b: 255, a: 1 },
  },
]

export const greenToRedLineColor: IChartColor[] = [
  {
    offset: 0,
    stopColor: { r: 61, g: 188, b: 135, a: 1 },
  },
  {
    offset: 50,
    stopColor: { r: 255, g: 178, b: 87, a: 1 },
  },
  {
    offset: 100,
    stopColor: { r: 255, g: 88, b: 107, a: 1 },
  },
]

export const purpleAreaColor: IChartColor[] = [
  {
    offset: 0,
    stopColor: { r: 207, g: 200, b: 255, a: 1 },
  },
  {
    offset: 100,
    stopColor: { r: 255, g: 255, b: 255, a: 1 },
  },
]

export const purpleLineColor: IChartColor[] = [
  {
    offset: 0,
    stopColor: { r: 97, g: 53, b: 153, a: 1 },
  },
]

export const blueAreaColor: IChartColor[] = [
  {
    offset: 0,
    stopColor: { r: 179, g: 205, b: 255, a: 1 },
  },
  {
    offset: 100,
    stopColor: { r: 255, g: 255, b: 255, a: 1 },
  },
]

export const blueLineColor: IChartColor[] = [
  {
    offset: 0,
    stopColor: { r: 118, g: 165, b: 255, a: 1 },
  },
]

export type TChartType = 'redToGreen' | 'greenToRed' | 'purple' | 'blue'

export const colorMap: Record<TChartType, Record<'area' | 'line', IChartColor[]>> = {
  redToGreen: {
    area: redToGreenAreaColor,
    line: redToGreenLineColor,
  },
  greenToRed: {
    area: greenToRedAreaColor,
    line: greenToRedLineColor,
  },
  purple: {
    area: purpleAreaColor,
    line: purpleLineColor,
  },
  blue: {
    area: blueAreaColor,
    line: blueLineColor,
  },
}
