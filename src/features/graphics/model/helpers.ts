import { getCopy } from '@/shared/helpers/'
import { IChartColor, TChartType, colorMap } from './constants'

const getColor = ({
  color,
  nextColor,
  currentPercent,
}: {
  color: IChartColor
  nextColor: IChartColor
  currentPercent: number
}) => {
  const differenceBetweenMaxAndMin = nextColor.offset - color.offset
  const differenceBetweenMinAndCurrent = currentPercent - color.offset

  const coef = differenceBetweenMinAndCurrent / differenceBetweenMaxAndMin

  const rDiff = (nextColor.stopColor.r - color.stopColor.r) * coef
  const gDiff = (nextColor.stopColor.g - color.stopColor.g) * coef
  const bDiff = (nextColor.stopColor.b - color.stopColor.b) * coef
  const aDiff = (nextColor.stopColor.a - color.stopColor.a) * coef

  const newColor = {
    r: Number((color.stopColor.r + rDiff).toFixed()),
    g: Number((color.stopColor.g + gDiff).toFixed()),
    b: Number((color.stopColor.b + bDiff).toFixed()),
    a: Number((color.stopColor.a + aDiff).toFixed()),
  }

  return {
    offset: currentPercent,
    stopColor: newColor,
  }
}

export const getGradientColors = ({
  type,
  max,
  min,
  customMax,
  chartType,
}: {
  type: TChartType
  max: number
  min: number
  customMax: number
  chartType: 'line' | 'area'
}) => {
  const colors = getCopy(colorMap[type][chartType])

  if (type === 'blue' || type === 'purple') return colors

  const reversedColors = colors.map(color => ({
    ...color,
    offset: 100 - color.offset,
  }))

  reversedColors.sort((a, b) => a.offset - b.offset)

  const safeMax = Math.max(customMax, max)

  const minPercent = Number(((Math.max(0, min) / safeMax) * 100).toFixed(3))
  const maxPercent = Number(((Math.max(0, max) / safeMax) * 100).toFixed(3))

  let newMax: Nullable<IChartColor> = null
  let newMin: Nullable<IChartColor> = null

  for (let i = 0; i < reversedColors.length - 1; i++) {
    const color = reversedColors[i]
    const nextColor = reversedColors[i + 1]

    if (color.offset < maxPercent && nextColor.offset > maxPercent) {
      newMax = getColor({ color, currentPercent: maxPercent, nextColor })
    }

    if (color.offset < minPercent && nextColor.offset > minPercent) {
      newMin = getColor({ color, currentPercent: minPercent, nextColor })
    }
  }

  const filtredColors = reversedColors.filter(
    color => (chartType === 'area' || color.offset >= minPercent) && color.offset <= maxPercent,
  )

  if (filtredColors.length === 0 && minPercent === 0 && maxPercent === 100) {
    filtredColors.push(reversedColors[0], reversedColors[reversedColors.length - 1])
  }

  newMax && filtredColors.push(newMax)
  newMin && filtredColors.push(newMin)

  const reversedFiltredColors = filtredColors.map(color => ({
    ...color,
    offset: Math.min(100, Math.max(0, Math.abs(color.offset - 100))),
  }))

  reversedFiltredColors.sort((a, b) => a.offset - b.offset)

  return reversedFiltredColors
}
