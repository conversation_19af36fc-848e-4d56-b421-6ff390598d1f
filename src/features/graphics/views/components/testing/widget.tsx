import { DEFAULT_TIME_RANGE } from '../../../model/constants'
import { AreaChartProps } from '../area-chart'
import { GraphicWrapper } from '../graphic-wrapper'
import classNamesBind from 'classnames/bind'
import styles from './testing.module.scss'
import { RangeItem } from '../../../model/types'
import { TimeRangeProps } from '../time-range/time-range'
import { useTranslation } from 'react-i18next'
import { lazy, Suspense } from 'react'

const LazyAreaChart = lazy(() =>
  import('../area-chart').then(module => ({ default: module.AreaChart })),
)

const cx = classNamesBind.bind(styles)

type EducationChartWidgetProps = Partial<TimeRangeProps> & {
  chartProps: Omit<AreaChartProps.Props, 'type'>
  descriptionProps: {
    precent: number | string
  }
  timeRange?: RangeItem[]
  isLoading?: boolean
  isFetching?: boolean
  className?: string
}

export const TestingChartWidget: React.FC<EducationChartWidgetProps> = ({
  chartProps,
  descriptionProps,
  timeRange = DEFAULT_TIME_RANGE,
  isLoading,
  isFetching,
  defaultItem,
  onTimeRangeChange,
  className,
}) => {
  const { data } = chartProps
  const { t } = useTranslation()

  const content =
    data && data.length >= 2 ? (
      <Suspense>
        <LazyAreaChart {...chartProps} type='blue' />
      </Suspense>
    ) : (
      <div className={cx('placeholder')}>{t('commons:no_data')}</div>
    )

  return (
    <GraphicWrapper
      isLoading={isLoading}
      isFetching={isFetching}
      content={content}
      leftDescription={
        <>
          <span className={cx('description__precent')}>
            {Number(descriptionProps?.precent)?.toFixed()}%
          </span>
          <span className={cx('description__text')}>{t('commons:test_progress')}</span>
        </>
      }
      defaultItem={defaultItem}
      onTimeRangeChange={onTimeRangeChange}
      timeRange={timeRange}
      className={className}
    />
  )
}
