.description {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;

  &__precent {
    font: var(--font-title-1-medium);
    color: var(--color-gray-90);
    margin-right: 12px;
  }

  &__text {
    color: var(--color-gray-90);
    font: var(--font-caption-1-demibold);
    max-width: 52px;
  }
  transform: translateX();
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
  height: -webkit-fill-available;
}
