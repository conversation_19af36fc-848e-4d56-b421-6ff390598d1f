import classNamesBind from 'classnames/bind'
import styles from './area-chart.module.scss'
import { AreaChartProps, LineColorProps, AreaColorProps, TooltipProps } from './area-chart.d'
import { ResponsiveContainer, ComposedChart, Area, YAxis, XAxis, Tooltip } from 'recharts'
import { useCallback, useId, useMemo } from 'react'
import { format, fromUnixTime, getUnixTime, parseISO } from '@/shared/helpers/date'
import { getGradientColors } from '@/features/graphics/model/helpers'
import { adjustValue } from './helpers'
import { isNumber } from '@/shared/helpers'
import { useLocale } from '@/shared/hooks/use-locale'

const cx = classNamesBind.bind(styles)

const LineColor: React.FC<LineColorProps> = props => {
  const { className, type, id, max, min, customMax } = props

  const colors = useMemo(
    () => getGradientColors({ type, max, min, customMax, chartType: 'line' }),
    [type, max, min, customMax],
  )

  return (
    <>
      <linearGradient
        className={cx(className)}
        id={`colorLine-${id}`}
        x1='0%'
        y1='0%'
        x2='0%'
        y2='100%'
      >
        {colors.map(props => {
          const { offset, stopColor, stopOpacity } = props
          const { r, g, b, a } = stopColor

          return (
            <stop
              {...props}
              offset={offset + '%'}
              stopColor={`rgba(${r}, ${g}, ${b}, ${a})`}
              key={`${offset}-${r}-${g}-${b}-${a}-${stopOpacity || 0}`}
            />
          )
        })}
      </linearGradient>
    </>
  )
}

const AreaColor: React.FC<AreaColorProps> = props => {
  const { className, type, id, max, min, customMax } = props

  const colors = useMemo(
    () => getGradientColors({ type, max, min, customMax, chartType: 'area' }),
    [type, max, min, customMax],
  )

  return (
    <>
      <linearGradient
        className={cx(className)}
        id={`colorArea-${id}`}
        x1='0%'
        y1='0%'
        x2='0%'
        y2='100%'
      >
        {colors.map(props => {
          const { offset, stopColor, stopOpacity } = props
          const { r, g, b, a } = stopColor

          return (
            <stop
              {...props}
              offset={offset + '%'}
              stopColor={`rgba(${r}, ${g}, ${b}, ${a})`}
              key={`${offset}-${r}-${g}-${b}-${a}-${stopOpacity || 0}`}
            />
          )
        })}
      </linearGradient>
    </>
  )
}

const CustomTooltip: React.FC<TooltipProps> = props => {
  const { active, className, dimension } = props
  const { payload } = props

  const usedPayload = payload[0]

  if (active && payload && payload.length) {
    const value = usedPayload?.value

    if (value === undefined) return null

    return (
      <div className={cx(className, 'custom-tooltip')}>
        {Number(value).toFixed(0) + (dimension || '')}
      </div>
    )
  }

  return null
}

export const AreaChart: React.FC<AreaChartProps.Props> = props => {
  const {
    className,
    data: propsedData,
    type = 'redToGreen',
    dimension = '%',
    customMin,
    customMax,
  } = props

  const chartId = useId()
  const locale = useLocale()

  const data = propsedData

  const min = useMemo(() => Math.min(...data.map(i => i.value)), [data])
  const max = useMemo(() => Math.max(...data.map(i => i.value)), [data])

  const formattedData = useMemo(() => {
    const newData = data?.map(i => ({
      date: getUnixTime(parseISO(i.date)),
      value: i.value,
    }))

    const isAllValuesEquals = new Set(data?.map(v => v?.value))?.size === 1

    if (isAllValuesEquals && isNumber(newData?.at(0)?.value)) {
      const firstElement = newData?.[0]

      firstElement.value += 0.01
    }

    newData.sort((a, b) => a.date - b.date)

    return newData
  }, [data])

  const XtickFormatter = useCallback(
    (tick: number) => format(fromUnixTime(tick), 'd MMM', locale),
    [locale],
  )

  const YtickFormatter = useCallback(
    (value: number) => +Number(value).toFixed(0) + dimension,
    [dimension],
  )

  const getCustomMax = () => {
    if (max < 2) return 10

    return customMax !== undefined ? Number(customMax) : adjustValue(max)
  }

  return (
    <div className={cx('wrapper', className)}>
      <ResponsiveContainer debounce={300} width='100%' height='100%'>
        <ComposedChart data={formattedData} height={400}>
          <XAxis
            dataKey={'date'}
            domain={['dataMin', 'dataMax']}
            interval={'preserveStartEnd'}
            tickMargin={10}
            tickFormatter={XtickFormatter}
            tick={{
              fontSize: 11,
              fontWeight: 400,
              fill: 'var(--color-gray-70)',
              width: 'fit-content',
              height: '4px',
            }}
            axisLine={false}
            tickLine={false}
            width={0}
            offset={0}
          />

          <YAxis
            dataKey='value'
            type='number'
            domain={[customMin !== undefined ? customMin : -5, getCustomMax()]}
            interval='preserveStartEnd'
            tickFormatter={YtickFormatter}
            tickMargin={25}
            tick={{
              fontSize: 11,
              fontWeight: 400,
              fill: 'var(--color-gray-70)',
            }}
            axisLine={false}
            tickLine={false}
            offset={0}
          />

          <Tooltip
            cursor={{ stroke: 'var(--color-gray-50)', strokeWidth: 1 }}
            isAnimationActive={false}
            content={<CustomTooltip active={false} payload={[]} dimension={dimension} />}
          />

          <defs>
            <AreaColor
              id={chartId}
              type={type}
              min={min}
              max={max ?? getCustomMax()}
              customMax={typeof customMax === 'number' ? customMax : getCustomMax()}
            />
          </defs>
          <Area
            strokeWidth={2}
            type={'monotone'}
            dataKey='value'
            fillOpacity={1}
            style={{ marginLeft: '-40px', zIndex: 1000 }}
            fill={`url(#colorArea-${chartId})`}
            stroke={`url(#colorLine-${chartId})`}
            dot={false}
          />

          <defs>
            <LineColor
              id={chartId}
              type={type}
              min={min}
              max={max ?? getCustomMax()}
              customMax={typeof customMax === 'number' ? customMax : getCustomMax()}
            />
          </defs>
          {/* <Line
            strokeWidth={2}
            type='monotone'
            dataKey='value'
            stroke={`url(#colorLine-${chartId})`}
            strokeOpacity={1}
            dot={false}
            activeDot={{
              stroke: `url(#colorLine-${chartId})`,
              strokeWidth: 2,
              fill: '#fff',
              r: 4,
              height: 5,
            }}
          /> */}
        </ComposedChart>
      </ResponsiveContainer>
    </div>
  )
}
