export type TChartType = 'redToGreen' | 'greenToRed' | 'purple' | 'blue'

export type DataPoint = {
  date: string
  value: number
}

export declare namespace AreaChartProps {
  interface Own {
    className?: string
    data: DataPoint[]
    type: TChartType
    dimension?: string
    customMin?: number
    customMax?: number
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch
}

export interface LineColorProps {
  className?: string
  type: TChartType
  id: string
  min: number
  max: number
  customMax: number
}

export interface AreaColorProps extends LineColorProps {}

interface TooltipPayload {
  strokeWidth: number
  fillOpacity: number
  fill: string
  stroke: string
  dataKey: string
  name: string
  color: string
  value: number
  payload: {
    date: number
    value: number
  }
  hide: boolean
}

export interface TooltipProps {
  className?: string
  active: boolean
  payload: TooltipPayload[]
  dimension?: string
}

export {}
