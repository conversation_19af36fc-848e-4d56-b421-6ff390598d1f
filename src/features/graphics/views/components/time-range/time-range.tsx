import { useState } from 'react'
import { RangeItem } from '../../../model/types'
import classNamesBind from 'classnames/bind'
import styles from './time-range.module.scss'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export type TimeRangeProps = {
  timeRange?: RangeItem[]
  defaultItem?: RangeItem | null
  onTimeRangeChange?: (item?: RangeItem) => void
  isLoading?: boolean
}

export const GraphicTimeRange: React.FC<TimeRangeProps> = ({
  timeRange,
  onTimeRangeChange,
  defaultItem,
  isLoading,
}) => {
  const [selectedItem, setSelectedItem] = useState<RangeItem | undefined | null>(() =>
    defaultItem !== null ? defaultItem : timeRange?.[0],
  )
  const { t } = useTranslation()

  if (timeRange?.length === 0) return null

  return (
    <ul className={cx('range__list')}>
      {timeRange?.map(v => (
        <li
          key={v.value}
          onClick={() => {
            if (selectedItem && selectedItem?.value === v?.value) {
              setSelectedItem(undefined)
              onTimeRangeChange?.(undefined)
              return
            }

            setSelectedItem(v)
            onTimeRangeChange?.(v)
          }}
          className={cx('range__item', selectedItem === v && 'active', isLoading && 'disabled')}
        >
          {t(v.text)}
        </li>
      ))}
    </ul>
  )
}
