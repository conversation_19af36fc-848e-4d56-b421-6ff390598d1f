.range {
  &__list {
    display: flex;
    flex-direction: row;
    column-gap: 8px;
    height: fit-content;
  }
  &__item {
    padding: 4px 8px;
    color: var(--color-gray-70);
    cursor: pointer;
    border-radius: 8px;
    font: var(--font-caption-1-normal);

    &.active:not(.disabled) {
      background-color: var(--color-gray-40);
      color: var(--color-gray-90);
    }
    transition: var(--transition);

    &:hover:not(.disabled) {
      background-color: var(--color-gray-40);
      color: var(--color-gray-90);
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }
  }
}
