import { HelpIcon } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './risk-level.module.scss'
import { Trans, useTranslation } from 'react-i18next'
import { useState } from 'react'
import LazyRiskLevelModal from '@/shared/modals/risk-level-modal/lazy-risk-level-modal'

export type RiskLevelDescriptionProps = {
  risk: number
  fullRisk?: number
  showHelpIcon?: boolean
}

const getRiskLevelClass = (risk: number) => {
  if (risk <= 4) return 'good'
  if (risk <= 7) return 'warning'

  return 'bad'
}

const cx = classNamesBind.bind(styles)
export const RiskLevelDescription: React.FC<RiskLevelDescriptionProps> = ({
  risk,
  fullRisk = 10,
  showHelpIcon = true,
}) => {
  const { t } = useTranslation()
  const [openModal, setOpenModal] = useState<boolean>(false)

  return (
    <div className={cx('description')}>
      <div className={cx('risk')}>
        <span className={cx('risk__main', `risk__main_${getRiskLevelClass(risk)}`)}>
          {Number(risk) || 0}
        </span>
        <span className={cx('risk__main', `risk__main_neutral`)}>/</span>
        <span className={cx('risk__secondary')}>{fullRisk}</span>
      </div>
      <div className={cx('text')}>{t('commons:risk_level')}</div>
      {showHelpIcon && (
        <HelpIcon
          tooltipClassname={cx('tooltip')}
          text={
            <Trans
              i18nKey={'commons:risk_level_hint'}
              t={t}
              components={{
                button: <div className={cx('open')} onClick={() => setOpenModal(true)} />,
              }}
            />
          }
        />
      )}
      {openModal && <LazyRiskLevelModal open={openModal} setOpen={setOpenModal} />}
    </div>
  )
}
