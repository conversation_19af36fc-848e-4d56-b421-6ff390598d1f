.description {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;

  .text {
    color: var(--color-gray-90);
    font: var(--font-caption-1-demibold);
    max-width: 52px;
  }

  .risk {
    &__main {
      font: var(--font-title-1-medium);
      &_good {
        color: var(--snackbars-success);
      }
      &_bad {
        color: var(--snackbars-error);
      }
      &_warning {
        color: var(--snackbars-warning);
      }
      &_neutral {
        color: var(--snackbars-neutral);
      }
    }
    &__secondary {
      color: var(--snackbars-neutral);
      font: var(--font-title-3-medium);
    }
  }
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 150px;
  height: -webkit-fill-available;
}

.tooltip {
  transform: translateX(20px) translateY(-50%);
}

.open {
  text-decoration: underline;
  cursor: pointer;
}
