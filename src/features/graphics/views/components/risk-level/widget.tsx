import { RiskLevelDescription, RiskLevelDescriptionProps } from './risk-level'
import { AreaChartProps } from '../area-chart'
import { DEFAULT_TIME_RANGE } from '../../../model/constants'
import { RangeItem } from '../../../model/types'
import { GraphicWrapper } from '../graphic-wrapper'
import { lazy, ReactNode, Suspense } from 'react'
import { TimeRangeProps } from '../time-range/time-range'
import classNamesBind from 'classnames/bind'
import styles from './risk-level.module.scss'
import { useTranslation } from 'react-i18next'

const LazyAreaChart = lazy(() =>
  import('../area-chart').then(module => ({ default: module.AreaChart })),
)

const cx = classNamesBind.bind(styles)

type RiskLevelChartWidgetProps = Partial<TimeRangeProps> & {
  chartProps: AreaChartProps.Props
  descriptionProps: RiskLevelDescriptionProps
  timeRange?: RangeItem[]
  isLoading?: boolean
  isFetching?: boolean
  infoClassName?: string
  bottomContent?: ReactNode
  className?: string
}

export const RiskLevelChartWidget: React.FC<RiskLevelChartWidgetProps> = ({
  chartProps,
  descriptionProps,
  timeRange = DEFAULT_TIME_RANGE,
  isLoading,
  isFetching,
  infoClassName,
  bottomContent,
  onTimeRangeChange,
  defaultItem,
  className,
}) => {
  const { data } = chartProps
  const { t } = useTranslation()

  const content =
    data && data.length >= 2 ? (
      <Suspense>
        <LazyAreaChart {...chartProps} type='redToGreen' />
      </Suspense>
    ) : (
      <div className={cx('placeholder')}>{t('commons:no_data')}</div>
    )

  return (
    <GraphicWrapper
      isLoading={isLoading}
      isFetching={isFetching}
      content={content}
      leftDescription={<RiskLevelDescription {...descriptionProps} />}
      timeRange={timeRange}
      defaultItem={defaultItem}
      infoClassName={infoClassName}
      bottomContent={bottomContent}
      onTimeRangeChange={onTimeRangeChange}
      className={className}
    />
  )
}
