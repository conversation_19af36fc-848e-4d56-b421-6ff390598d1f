import { DEFAULT_TIME_RANGE } from '../../../model/constants'
import { AreaChartProps } from '../area-chart'
import { GraphicWrapper } from '../graphic-wrapper'
import classNamesBind from 'classnames/bind'
import styles from './education.module.scss'
import { TimeRangeProps } from '../time-range/time-range'
import { useTranslation } from 'react-i18next'
import { lazy, ReactNode, Suspense } from 'react'

const cx = classNamesBind.bind(styles)

const LazyAreaChart = lazy(() =>
  import('../area-chart').then(module => ({ default: module.AreaChart })),
)

type EducationChartWidgetProps = Partial<TimeRangeProps> & {
  chartProps: Omit<AreaChartProps.Props, 'type'>
  descriptionProps: {
    percent: number | string
  }
  isLoading?: boolean
  isFetching?: boolean
  className?: string
  graphicClassName?: string
  infoClassName?: string
  bottomContent?: ReactNode
}

export const EducationChartWidget: React.FC<EducationChartWidgetProps> = ({
  chartProps,
  timeRange = DEFAULT_TIME_RANGE,
  descriptionProps,
  isLoading,
  isFetching,
  defaultItem,
  onTimeRangeChange,
  className,
  graphicClassName,
  infoClassName,
  bottomContent,
}) => {
  const { data } = chartProps
  const { t } = useTranslation()

  const content =
    data && data.length >= 2 ? (
      <Suspense>
        <LazyAreaChart className={graphicClassName} {...chartProps} type='purple' />
      </Suspense>
    ) : (
      <div className={cx('placeholder')}>{t('commons:no_data')}</div>
    )

  return (
    <GraphicWrapper
      isLoading={isLoading}
      isFetching={isFetching}
      content={content}
      className={className}
      leftDescription={
        <>
          <p className={cx('description__precent')}>
            {Number(descriptionProps?.percent)?.toFixed()}%
          </p>
          <p className={cx('description__text')}>{t('commons:learning_progress')}</p>
        </>
      }
      contentClassName={graphicClassName}
      defaultItem={defaultItem}
      onTimeRangeChange={onTimeRangeChange}
      timeRange={timeRange}
      infoClassName={infoClassName}
      bottomContent={bottomContent}
    />
  )
}
