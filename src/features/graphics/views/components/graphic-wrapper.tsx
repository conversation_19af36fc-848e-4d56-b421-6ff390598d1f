import { ReactNode } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './graphic-wrapper.module.scss'
import { GraphicTimeRange, TimeRangeProps } from './time-range/time-range'
import Skeleton from 'react-loading-skeleton'
import { DEFAULT_TIME_RANGE } from '../../model/constants'

const cx = classNamesBind.bind(styles)

type Props = Partial<TimeRangeProps> & {
  leftDescription: ReactNode
  content: ReactNode
  isLoading?: boolean
  isFetching?: boolean
  className?: string
  contentClassName?: string
  infoClassName?: string
  bottomContent?: ReactNode
}

export const GraphicWrapper: React.FC<Props> = ({
  leftDescription,
  content,
  isLoading,
  isFetching,
  timeRange = DEFAULT_TIME_RANGE,
  defaultItem,
  onTimeRangeChange,
  className,
  contentClassName,
  infoClassName,
  bottomContent,
}) => {
  return (
    <section className={cx('wrapper', className)}>
      <div className={cx('info__wrapper', infoClassName)}>
        <div className={cx('left')}>
          {isLoading ? <Skeleton height={40} width={100} /> : leftDescription}
        </div>
        {timeRange && (
          <GraphicTimeRange
            defaultItem={defaultItem}
            onTimeRangeChange={onTimeRangeChange}
            timeRange={timeRange}
            isLoading={isLoading || isFetching}
          />
        )}
      </div>
      <div className={contentClassName}>
        {isLoading ? <Skeleton width={'100%'} height={200} /> : content}
      </div>
      {bottomContent && bottomContent}
    </section>
  )
}
