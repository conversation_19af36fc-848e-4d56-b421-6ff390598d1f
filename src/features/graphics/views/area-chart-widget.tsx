import { GraphicWrapper } from './components/graphic-wrapper'
import { DEFAULT_TIME_RANGE } from '../model/constants'

import { RangeItem } from '../model/types'
import { ReactNode } from 'react'

type AreaChartWidgetProps = {
  chart: ReactNode
  leftDescription: ReactNode
  timeRange?: RangeItem[]
}

export const ChartWidget: React.FC<AreaChartWidgetProps> = ({
  chart,
  leftDescription,
  timeRange = DEFAULT_TIME_RANGE,
}) => {
  return <GraphicWrapper content={chart} leftDescription={leftDescription} timeRange={timeRange} />
}
