import { FC, useState } from 'react'
import { ButtonIcon } from '@/shared/ui'
import { EditNoteModal, EditNoteModalProps } from './edit-notice-modal'

type EditNoticeProps = Omit<EditNoteModalProps, 'open' | 'setOpen'>

export const EditNotice: FC<EditNoticeProps> = props => {
  const [open, setOpen] = useState(false)

  return (
    <>
      <ButtonIcon onClick={() => setOpen(true)} icon='editBold' />
      <EditNoteModal {...props} open={open} setOpen={setOpen} />
    </>
  )
}
