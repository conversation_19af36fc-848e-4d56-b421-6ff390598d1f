import { useEffect, useState } from 'react'
import classNamesBind from 'classnames/bind'
import { Modal } from '@/shared/components'
import { Button, Textarea } from '@/shared/ui'
import styles from './edit-notice.module.scss'
import { useNotification } from '@/shared/contexts/notifications'
import { useTranslation } from 'react-i18next'
import { employeeStatisticAPI } from '@/entities/statistic'

const cx = classNamesBind.bind(styles)

export type EditNoteModalProps = {
  note: string | undefined
  userId: string
  open: boolean
  setOpen: (v: boolean) => void
}

export const EditNoteModal = ({ note, userId, open, setOpen }: EditNoteModalProps) => {
  const [text, setText] = useState<string>(note ?? '')
  const { t } = useTranslation()

  const [updateNote, { isLoading }] = employeeStatisticAPI.useUpdateNoteMutation()
  const { handleErrorResponse } = useNotification()

  useEffect(() => {
    setText(note ?? '')
  }, [note])

  const onResetText = () => {
    setText(note || '')
  }

  const onSubmit = async () => {
    if (!userId) {
      handleErrorResponse({
        status: 'error',
        message: t('commons:error_user_id'),
      })
      return
    }

    if (note === text) {
      handleErrorResponse({
        status: 'error',
        message: t('commons:employee_note_matches'),
      })

      return
    }

    await updateNote({ id: userId, text }).unwrap()
    setOpen(false)
  }

  return (
    <Modal
      className={cx('modal')}
      active={open}
      setActive={v => {
        onResetText()
        setOpen(!!v)
      }}
    >
      <h3 className={cx('modal__title')}>{t('commons:employee_note')}</h3>
      <Textarea
        disabled={isLoading}
        className={cx('modal__textarea')}
        value={text}
        onChange={(v: string) => setText(v)}
      />
      <div className={cx('modal__actions')}>
        <Button disabled={isLoading} color='gray' fullWidth onClick={onResetText}>
          {t('commons:cancel_changes')}
        </Button>
        <Button disabled={isLoading} fullWidth onClick={onSubmit}>
          {t('commons:save')}
        </Button>
      </div>
    </Modal>
  )
}
