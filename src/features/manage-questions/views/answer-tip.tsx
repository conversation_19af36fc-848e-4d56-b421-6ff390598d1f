import { Button, ButtonIcon, Textarea } from '@/shared/ui'
import { FC, useState } from 'react'
import { Controller, useFormContext } from 'react-hook-form'
import classNamesBind from 'classnames/bind'
import styles from './manage-questions-form.module.scss'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

type Props = {
  questionIndex: number
  answerIndex: number
  readonly?: boolean
}

export const AnswerTip: FC<Props> = ({ questionIndex, answerIndex, readonly }) => {
  const form = useFormContext()

  const { t } = useTranslation('features__manage-questions-form')

  const tip = form.watch(`questions.${questionIndex}.answers.${answerIndex}.answer_tip`)

  const [isOpen, setIsOpen] = useState<boolean>(!!tip)

  return (
    <div className={cx('tip_wrapper')}>
      {isOpen || readonly ? (
        <div className={cx('tip')}>
          <div className={cx('tip_label')}>{t('explanation__title')}</div>
          <div className={cx('tip_inner')}>
            <Controller
              name={`questions.${questionIndex}.answers.${answerIndex}.answer_tip`}
              control={form.control}
              render={({ field, fieldState }) => (
                <Textarea
                  value={field?.value ?? ''}
                  onChange={v => {
                    field.onChange(v)
                  }}
                  // resize={false}
                  placeholder={t('explanation__title')}
                  view={field?.value ? 'bordered' : undefined}
                  className={cx('content__textarea', 'explanation')}
                  classNameWrapper={cx('content__answers__item__textarea')}
                  fullWidth
                  error={fieldState.error?.message}
                  disabled={readonly}
                />
              )}
            />
            {!readonly && (
              <ButtonIcon
                onClick={() => {
                  setIsOpen(false)
                  form.setValue(`questions.${questionIndex}.answers.${answerIndex}.answer_tip`, '')
                }}
                icon='trashBold'
                size='32'
                iconSize='24'
                color='gray70'
              />
            )}
          </div>
        </div>
      ) : (
        <Button
          onClick={() => setIsOpen(true)}
          type='button'
          leftIconColor={'gray80'}
          leftIcon={'plus'}
          className={cx('button')}
          size='small'
        >
          {t('explanation__title')}
        </Button>
      )}
    </div>
  )
}
