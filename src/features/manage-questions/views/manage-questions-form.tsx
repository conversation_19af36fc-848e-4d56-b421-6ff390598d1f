import React, { use<PERSON><PERSON>back, useState } from 'react'
/* eslint-disable i18next/no-literal-string */
import classNamesBind from 'classnames/bind'
import styles from './manage-questions-form.module.scss'
import { <PERSON>, FormProvider, useFieldArray, useForm } from 'react-hook-form'
import { getAnswer, getQuestion } from '../model/helpers'
import { Button, ButtonIcon, Checkbox, SwitchNew, Textarea } from '@/shared/ui'
import Settings from '@/shared/ui/Icon/icons/components/Settings'
import { zodResolver } from '@hookform/resolvers/zod'
import { QuestionAnswerSchema, QuestionsSchema } from '../model/validation'
import { useTranslation } from 'react-i18next'
import { ThemeTestSettingsModal } from '@/shared/modals/theme-test-settings-modal'
import { TestSettings } from '@/entities/test'
import { themeQuizApi } from '@/entities/themeCourse/model/api'
import { TAns<PERSON>, T<PERSON><PERSON><PERSON>, TSettings } from '@/entities/themeCourse/model/types'
import { AnswerTip } from './answer-tip'
import { useNotification } from '@/shared/contexts/notifications'
import { v4 as uuid } from 'uuid'
import { ThemeContentControlButton } from '@/shared/components/theme-content'
import { DownloadIcon } from '@/shared/ui/Icon/icons/components'
import { useDownload } from '@/shared/hooks'
import { delay } from '@/shared/helpers/delay'
import DeleteConfirmModal from '@/shared/modals/delete-confirm-modal/delete-confirm-modal'
import { isNumber } from '@/shared/helpers'

const cx = classNamesBind.bind(styles)

export type ManageQuestionsFormState = {
  questions: TQuestion[]
  settings?: TSettings
}

type ManageQuestionsFormProps = {
  initialState?: ManageQuestionsFormState
  stepId?: UUID
  themeId: UUID
  isNewStep?: boolean
  createTheme?: () => void
  appendThemeInCourseIfNeeded?: (cb?: () => void) => Promise<void>
  readonly?: boolean
}

export const ManageQuestionsForm = (props: ManageQuestionsFormProps) => {
  const {
    initialState,
    stepId,
    createTheme,
    appendThemeInCourseIfNeeded,
    isNewStep,
    readonly,
    themeId: theme_id,
  } = props

  const { t } = useTranslation('features__manage-questions-form')

  const { add, handleErrorResponse } = useNotification()
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const [isLoadingQuizFile, setIsLoadingQuizFile] = useState<boolean>(false)
  const form = useForm<ManageQuestionsFormState>({
    defaultValues: initialState
      ? { questions: initialState.questions }
      : {
          questions: [getQuestion(1)],
        },
    resolver: zodResolver(QuestionsSchema),
  })

  const [deleteQuestionIndex, setDeleteQuestionIndex] = useState<number | undefined>()
  const [createQuiz] = themeQuizApi.useCreateQuizMutation()
  const [updateQuiz] = themeQuizApi.useUpdateQuizMutation()
  const [updateSettings] = themeQuizApi.useUpdateQuizSettingsMutation()

  const [download] = useDownload()

  const [getQuizFromFile] = themeQuizApi.useLazyGetQuizFromFileQuery()

  const downloadQuiz = useCallback(async () => {
    setIsLoadingQuizFile(true)

    try {
      const { data: quizJSONFile } = await getQuizFromFile({
        themeId: theme_id,
        stepId: stepId ?? '',
      })

      if (!quizJSONFile?.file_path) return

      add({
        message: t('download_json_quiz_notify'),
        status: 'success',
        id: uuid(),
      })

      await delay(2000)

      download(quizJSONFile.file_path, 'quiz-export.json')
    } catch (e) {
      handleErrorResponse(e)
    } finally {
      setIsLoadingQuizFile(false)
    }
  }, [getQuizFromFile, stepId, theme_id, download, handleErrorResponse, add, t])

  const isCanAddQuestion = form.formState.isValid

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'questions',
    keyName: 'id',
  })
  const questions = form.watch('questions')
  const controlledFields = fields.map((field, index) => {
    return {
      ...field,
      ...questions[index],
    }
  })

  const [selectedQuestion, setSelectedQuestion] = useState(controlledFields[0])

  const openSettings = () => setIsOpen(true)

  const onSubmit = () => {
    const data = questions

    if (!theme_id && createTheme) {
      return createTheme()
    }

    try {
      if (!!stepId && !isNewStep) {
        updateQuiz({
          themeId: theme_id,
          stepId,
          questions: data,
        }).then(() => {
          add({
            message: t('notify.update_step'),
            status: 'success',
            id: uuid(),
          })
        })
      } else {
        createQuiz({
          themeId: theme_id,
          questions: data,
        }).then(() => {
          if (appendThemeInCourseIfNeeded) {
            appendThemeInCourseIfNeeded()
            add({
              message: t('notify.create_step'),
              status: 'success',
              id: uuid(),
            })
          } else {
            add({
              message: t('notify.create_step'),
              status: 'success',
              id: uuid(),
            })
          }
        })
      }
    } catch (e) {
      handleErrorResponse(e)
    }
  }

  const onSettingsSubmit = async (settings: TestSettings) => {
    if (!theme_id && createTheme) {
      return createTheme()
    }

    const body = {
      need_explain: settings.explanations,
      correct_answers: settings.right_answers,
      questions_count: settings.questions_amount,
      testing_time_duration: settings.duration,
      learning_block: !settings.allow_continue,
      attempts_limit: settings.limit_attempts ? settings.attempts_amount : null,
    }

    if (stepId) {
      await updateSettings({
        themeId: theme_id,
        stepId,
        settings: body,
      })
    } else {
      const data = questions
      const { data: quiz } = await createQuiz({
        themeId: theme_id,
        questions: data,
      })
      if (quiz?.id) {
        await updateSettings({
          themeId: theme_id,
          stepId: quiz?.id,
          settings: body,
        })
      }
      if (appendThemeInCourseIfNeeded) {
        appendThemeInCourseIfNeeded()
      }
    }

    setIsOpen(false)
  }

  return (
    <FormProvider {...form}>
      <DeleteConfirmModal
        open={isNumber(deleteQuestionIndex)}
        setOpen={() => setDeleteQuestionIndex(undefined)}
        title={t('delete_question_modal.title')}
        description={t('delete_question_modal.description')}
        onConfirm={() => {
          remove(deleteQuestionIndex)
          setDeleteQuestionIndex(undefined)
        }}
      />
      <ThemeContentControlButton
        id='manage-questions-form-download'
        className={cx('download')}
        icon={DownloadIcon}
        size='24'
        color={isNewStep ? 'gray60' : 'gray80'}
        disabled={isLoadingQuizFile}
        onClick={isNewStep ? undefined : downloadQuiz}
        tooltip={{
          content: <>{t('download_json_quiz')}</>,
          tooltipClassname: cx('tooltip_download'),
          position: 'left',
        }}
      />
      {!readonly && (
        <ThemeContentControlButton
          id='manage-questions-form-settings'
          className={cx('settings', { disabled: isNewStep })}
          icon={Settings}
          size='24'
          color={isNewStep ? 'gray60' : 'gray80'}
          onClick={isNewStep ? undefined : openSettings}
        />
      )}
      <section className={cx('root')}>
        <section className={cx('questions__wrapper')}>
          {!readonly && (
            <div>
              <Button
                onClick={() => {
                  append(getQuestion(questions.length + 1))

                  setTimeout(() => {
                    const element = document.getElementById('questions-list')
                    if (element) {
                      element.scrollTop = element.scrollHeight
                    }
                  }, 20)
                }}
                disabled={!isCanAddQuestion || readonly}
                type='button'
                leftIconColor={isCanAddQuestion ? 'white' : 'gray90'}
                leftIcon={'plus'}
                className={cx(!isCanAddQuestion && 'add__question_disabled')}
              >
                {t('add_question')}
              </Button>
            </div>
          )}
          <h4 className={cx('questions__title')}>{t('questions')}</h4>
          <ul id='questions-list' className={cx('list', 'scrollbar')}>
            {controlledFields.map((question, index) => {
              const isEdit = selectedQuestion.id === question.id

              return (
                <li className={cx('list__item', isEdit && 'active')} key={question.id}>
                  <span>{index + 1}. </span>
                  <Controller
                    name={`questions.${index}.text`}
                    control={form.control}
                    render={({ field, fieldState }) => {
                      return (
                        <>
                          <Textarea
                            resize={false}
                            className={cx('textarea', !isEdit && 'disabled', 'readonly')}
                            classNameWrapper={cx('textarea__wrapper')}
                            placeholder={t('questions_text_placeholder')}
                            rows={3}
                            onClick={() => setSelectedQuestion(controlledFields[index])}
                            value={field.value}
                            error={fieldState.error?.message}
                            readOnly
                          />
                          {fieldState.error?.message}
                        </>
                      )
                    }}
                  />
                  {controlledFields.length > 1 && !readonly && (
                    <div className={cx('actions')}>
                      <ButtonIcon
                        onClick={() => {
                          if (isEdit) {
                            setSelectedQuestion(
                              index > 0 ? controlledFields[index - 1] : controlledFields[index + 1],
                            )
                          }

                          setDeleteQuestionIndex(index)
                        }}
                        icon='trashBold'
                        size='32'
                        iconSize='24'
                        color='gray70'
                        className={cx('trash')}
                      />
                    </div>
                  )}
                </li>
              )
            })}
          </ul>
        </section>
        <section className={cx('content', 'content__form')}>
          {controlledFields.map(
            (question, index) =>
              question.id === selectedQuestion?.id && (
                <React.Fragment key={question.id}>
                  <Controller
                    name={`questions.${index}.text`}
                    control={form.control}
                    render={({ field, fieldState }) => (
                      <Textarea
                        value={field.value}
                        onChange={v => field.onChange(v)}
                        view={field.value ? 'bordered' : undefined}
                        resize={false}
                        label={t('question_text')}
                        error={fieldState.error?.message}
                        disabled={readonly}
                      />
                    )}
                  />
                  <div className={cx('content__answers')}>
                    <div className={cx('content__answers__title__wrapper')}>
                      <h4>{t('answers_variants')}</h4>
                      <Controller
                        name={`questions.${index}.multiple_answers`}
                        control={form.control}
                        key={'switch'}
                        render={({ field }) => (
                          <SwitchNew
                            text={t('enable_many_answers')}
                            onChange={v => {
                              field.onChange(v)
                              if (!v) {
                                const answers = form.watch(`questions.${index}.answers`)
                                let wasCorrect = false

                                form.setValue(
                                  `questions.${index}.answers`,
                                  answers.map(a => {
                                    if (a?.is_correct && !wasCorrect) {
                                      wasCorrect = true
                                    } else {
                                      a.is_correct = false
                                    }

                                    return a
                                  }),
                                )
                              }
                            }}
                            value={!!field.value}
                            disabled={readonly}
                          />
                        )}
                      />
                    </div>
                    <ul id='answers-list' className={cx('content__answers', 'scrollbar')}>
                      {question.answers?.map((answer, answerIndex, array) => (
                        <li>
                          <div className={cx('content__answers__item')}>
                            <Controller
                              name={`questions.${index}.answers.${answerIndex}.is_correct`}
                              control={form.control}
                              render={({ field }) => (
                                <Checkbox
                                  disabled={readonly}
                                  type={
                                    form.watch(`questions.${index}.multiple_answers`)
                                      ? 'square'
                                      : 'circle'
                                  }
                                  onChange={v => {
                                    const hasManyAnswers = form.watch(
                                      `questions.${index}.multiple_answers`,
                                    )
                                    const answers = form.watch(`questions.${index}.answers`)
                                    const doesntHaveCorrectAnswer = answers?.every(
                                      answer => !answer?.is_correct,
                                    )

                                    if (hasManyAnswers || doesntHaveCorrectAnswer) {
                                      field.onChange(v)

                                      return
                                    }

                                    const newAnswers: TAnswer[] = answers.map(ans =>
                                      ans.id === answer?.id
                                        ? {
                                            ...answer,
                                            is_correct: true,
                                          }
                                        : { ...ans, is_correct: false },
                                    )

                                    form.setValue(`questions.${index}.answers`, newAnswers)
                                  }}
                                  customChecked={!!field.value}
                                />
                              )}
                            />
                            <Controller
                              name={`questions.${index}.answers.${answerIndex}.text`}
                              control={form.control}
                              render={({ field, fieldState }) => (
                                <Textarea
                                  disabled={readonly}
                                  value={field?.value ?? ''}
                                  onChange={v => {
                                    field.onChange(v)
                                  }}
                                  onInput={event => {
                                    event.currentTarget.style.height = 'auto'
                                    event.currentTarget.style.height =
                                      event.currentTarget.scrollHeight + 'px'
                                  }}
                                  resize={false}
                                  placeholder={t('numbered_answer_variant', {
                                    value: answerIndex + 1,
                                  })}
                                  view={field?.value ? 'bordered' : undefined}
                                  className={cx('content__textarea')}
                                  classNameWrapper={cx('content__answers__item__textarea')}
                                  fullWidth
                                  error={fieldState.error?.message}
                                />
                              )}
                            />
                            {array.length > 1 && (
                              <div className={cx('actions')}>
                                <ButtonIcon
                                  key={answer.id}
                                  onClick={() => {
                                    const answers = form.watch(`questions.${index}.answers`)

                                    form.setValue(
                                      `questions.${index}.answers`,
                                      answers.filter(ans => ans.id !== answer?.id),
                                    )
                                  }}
                                  icon='closeBold'
                                  size='32'
                                  iconSize='24'
                                  color='gray70'
                                  disabled={readonly}
                                />
                              </div>
                            )}
                          </div>
                          <AnswerTip
                            questionIndex={index}
                            answerIndex={answerIndex}
                            readonly={readonly}
                          />
                        </li>
                      ))}
                    </ul>
                    {!readonly && (
                      <Button
                        onClick={() => {
                          const answers = form.watch(`questions.${index}.answers`)

                          form.setValue(`questions.${index}.answers`, [...answers, getAnswer()])

                          setTimeout(() => {
                            const listElement = document.querySelector(`#answers-list`)

                            listElement?.scrollTo(0, listElement.scrollHeight)
                          }, 20)
                        }}
                        disabled={
                          !question.answers?.every(
                            answer => QuestionAnswerSchema.safeParse(answer).success,
                          ) || readonly
                        }
                        type='button'
                        leftIconColor={
                          question.answers?.every(
                            answer => QuestionAnswerSchema.safeParse(answer).success,
                          )
                            ? 'white'
                            : 'gray80'
                        }
                        leftIcon={'plus'}
                        size='small'
                        className={cx(
                          !question.answers?.every(
                            answer => QuestionAnswerSchema.safeParse(answer).success,
                          ) && 'add__question_disabled',
                          'add_button',
                        )}
                      >
                        {t('add_one_answer')}
                      </Button>
                    )}
                  </div>
                </React.Fragment>
              ),
          )}
          {!readonly && (
            <Button
              disabled={
                !QuestionsSchema.safeParse({
                  questions: form.watch().questions,
                })?.success
              }
              onClick={onSubmit}
              className={cx('submit')}
              size='big'
              color='green'
            >
              {t('commons:save')}
            </Button>
          )}
        </section>
        <ThemeTestSettingsModal
          saveSettings={onSettingsSubmit}
          initialState={initialState?.settings}
          active={isOpen}
          setActive={setIsOpen}
          questions={questions}
        />
      </section>
    </FormProvider>
  )
}
