@use '../../../shared/assets/styles/mixins/text';

.root {
  --root-max-height: 900px;
  background: var(--white);
  border: 1px solid var(--color-gray-30);
  border-radius: 12px;
  display: grid;
  grid-template-columns: minmax(175px, 4fr) 7fr;
  max-height: var(--root-max-height);
  position: relative;

  & * {
    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background: #e1e4eb;
      border-radius: 8px;
    }
  }
}

.content {
  padding: 32px 24px 12px 24px;
  overflow-y: auto;
  scrollbar-width: thin;

  &__form {
    display: flex;
    flex-direction: column;
    gap: 24px;
    position: relative;
  }

  &__save {
    margin-left: auto;
  }

  &__textarea {
    min-height: 48px;
    height: 0px;
    max-height: 200px;

    overflow-y: auto; /* Появление полосы прокрутки при необходимости */
  }

  &__title {
    font: var(--font-text-1-medium);
    color: var(--color-gray-90);
  }

  &__answers {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 2px 0;
    width: 100%;
    overflow-y: auto;
    max-height: calc(var(--root-max-height) - 304px);

    &::-webkit-scrollbar {
      width: 4px;
    }
    &::-webkit-scrollbar-track {
      background: transparent;
    }
    &::-webkit-scrollbar-thumb {
      background: #e1e4eb;
      border-radius: 8px;
    }

    &__switch {
      display: flex;
      flex-direction: row-reverse;
    }

    &__item {
      display: flex;
      gap: 8px;
      align-items: center;
      width: 100%;

      .hide {
        opacity: 0;
        pointer-events: none;
      }

      &__textarea {
        width: 100%;
      }

      &__checkbox {
        margin-right: 4px;
      }
    }

    &__wrapper {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    &__title {
      &__wrapper {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        gap: 10px;
      }
    }
  }

  &__explanations {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.explanation {
  height: max-content;
  min-height: 88px;
}

.questions {
  &__wrapper {
    border-right: 2px solid var(--color-gray-30);
    padding: 32px 16px;
    display: flex;
    flex-direction: column;
    gap: 20px;
  }

  &__title {
    font: var(--font-text-2-medium);
    color: var(--color-gray-70);
  }

  &__list {
    display: flex;
    flex-direction: column;

    &__item {
      padding: 12px 16px;
      border-radius: 12px;
      color: var(--color-gray-80);
      display: flex;
      gap: 8px;
      cursor: pointer;

      &:hover {
        background: var(--color-gray-40);
        &__actions {
          display: flex;
        }
        * {
          color: var(--color-gray-90);
        }
      }

      &__actions {
        display: none;
      }
    }
  }
}

.list {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  max-height: calc(var(--root-max-height) - 166px);

  &__item {
    padding: 12px 16px;
    border-radius: 12px;
    color: var(--color-gray-80);
    display: flex;
    align-items: flex-start;
    gap: 8px;
    height: auto;

    &::-webkit-scrollbar {
      display: none;
    }

    & .actions {
      display: none;
      opacity: 0;
      align-self: center;
    }

    .textarea {
      font: var(--font-text-2-medium);
      background: none;
      border: none;
      outline: none;
      box-shadow: none !important;
      width: 100%;
      padding: 0;
      border-radius: 0px;
      overflow-y: auto;
      margin-top: -1px;
      resize: none;
      min-height: 20px !important;
      max-height: 57px !important;
      &::-webkit-scrollbar {
        display: none;
      }

      &.readonly {
        cursor: pointer;
        caret-color: transparent;
      }

      &__wrapper {
        width: 100%;
      }

      @include text.max-lines(3);

      &:focus {
        background: none;
        border: none;
        outline: none;
      }
      &:active {
        background: none;
        border: none;
        outline: none;
      }
    }

    .text {
      @include text.max-lines(3);
    }

    &.active {
      background: var(--color-gray-40);

      * {
        color: var(--color-gray-90);
      }
    }

    &:hover {
      .actions {
        display: flex;
        opacity: 1;
      }
    }
  }
}

.add__question {
  &_disabled {
    color: var(--color-gray-80) !important;
    cursor: not-allowed !important;
  }
}

.settings {
  cursor: pointer;

  &:not(&.disabled) {
    &::after {
      content: '';
      position: absolute;
      top: -6px;
      left: -6px;
      width: calc(100% + 12px);
      height: calc(100% + 12px);
    }

    svg {
      transition: var(--transition);
    }
  }

  &.disabled {
    cursor: not-allowed;
  }

  &:hover:not(&.disabled) svg {
    transform: scale(1.125) rotate(60deg);
  }
}

.button {
  background: var(--button-disabled-color);
  border: 2px solid var(--button-disabled-color);
  color: var(--color-gray-80);

  &:hover {
    svg {
      path {
        fill: var(--white) !important;
      }
    }
  }
}

.tip {
  &_wrapper {
    margin: 16px 0 0 28px;
  }
  &_label {
    font: var(--font-text-2-normal);
    color: var(--color-gray-80);
    margin-bottom: 8px;
  }
  &_inner {
    display: flex;
    gap: 8px;
    align-items: center;
  }
}

.add_button {
  margin-left: 28px;

  width: fit-content;
}

.submit {
  width: fit-content;
  margin-left: auto;
  margin-right: 0;
}

.download {
  &:hover {
    cursor: pointer;
    transform: translate(0, 2px);
  }
}

.tooltip_download {
  width: 150px;
}
