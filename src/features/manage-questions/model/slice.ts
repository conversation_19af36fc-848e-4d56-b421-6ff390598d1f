// import { EntityState, PayloadAction, createEntityAdapter, createSlice } from '@reduxjs/toolkit'
// import { Question, QuestionAnswer } from '@/entities/questions'
// import { createSelector } from 'reselect'
// import { getAnswer, getQuestion } from './helpers'
// import { TQuestion } from '@/entities/themeCourse/model/types'

// export const questionsAdapter = createEntityAdapter({
//   selectId: (q: Question) => q.id,
// })

// const DEFAULT_QUESTION = getQuestion(1)

// export type ManageQuestionsSliceState = {
//   questions: EntityState<Question, UUID>
//   selectedQuestion?: TQuestion['id']
// }

// export type ManageQuestionsState = {
//   manageQuestions: ManageQuestionsSliceState
// }

// const initialState: ManageQuestionsSliceState = {
//   questions: {
//     entities: {
//       [DEFAULT_QUESTION.id]: { ...DEFAULT_QUESTION },
//     },
//     ids: [DEFAULT_QUESTION.id],
//   },
//   selectedQuestion: DEFAULT_QUESTION['id'],
// }

// export const manageQuestionSlice = createSlice({
//   name: 'manageQuestionsSlice',
//   initialState,
//   reducers: {
//     addQuestion: (state, action: PayloadAction<Question>) => {
//       questionsAdapter.addOne(state.questions, action.payload)
//       state.selectedQuestion = action.payload.id
//     },
//     removeAllQuestions: state => {
//       questionsAdapter.removeAll(state.questions)
//       state.selectedQuestion = undefined
//     },
//     changeQuestion: (state, action: PayloadAction<Question>) => {
//       questionsAdapter.upsertOne(state.questions, action.payload)
//     },
//     onUpdateQuestionText: (
//       state,
//       action: PayloadAction<{ question: Question; text: string }>,
//     ) => {
//       questionsAdapter.updateOne(state.questions, {
//         id: action.payload.question.id,
//         changes: {
//           content: {
//             ...action.payload.question.content,
//             text: action.payload.question.content.text
//           }
//         },
//       })
//     },
//     changeQuestionAnswer: (
//       state,
//       action: PayloadAction<{ question: Question; answer: DeepPartial<QuestionAnswer> }>,
//     ) => {
//       const { question, answer } = action.payload
//       questionsAdapter.updateOne(state.questions, {
//         id: question.id,
//         changes: {
//           content: {
//             ...question.content,
//             answers:
//               question.content?.answers?.map((answ: QuestionAnswer) => {
//                 if (answ.id === answer.id) {
//                   return {
//                     ...answ,
//                     ...answer,
//                   }
//                 }
//                 return answ
//               }) ?? [],
//           },
//         },
//       })
//     },
//     toggleHasManyAnswers: (state, action: PayloadAction<Question>) => {
//       questionsAdapter.upsertOne(state.questions, {
//         ...action.payload,
//         content: {
//           ...action.payload.content,
//           hasManyAnswers: !action.payload.content?.hasManyAnswers,
//         },
//       })
//     },
//     onAddAnswer: (
//       state,
//       action: PayloadAction<{ question: Question; answer: DeepPartial<QuestionAnswer> }>,
//     ) => {
//       const question = action.payload.question
//       const answer = action.payload.answer
//       questionsAdapter.upsertOne(state.questions, {
//         ...question,
//         content: {
//           ...question.content,
//           answers: [...(question.content?.answers ?? []), getAnswer(answer)],
//         },
//       })
//     },
//     onRemoveAnswer: (
//       state,
//       action: PayloadAction<{ question: Question; answer: QuestionAnswer }>,
//     ) => {
//       const question = action.payload

//       questionsAdapter.upsertOne(state.questions, {
//         ...question.question,
//         content: {
//           ...question.question.content,
//           answers: question.question.content?.answers?.filter(
//             (answ: QuestionAnswer) => answ.id !== question.answer.id,
//           ),
//         },
//       })
//     },
//   },
// })

// export const selectQuestionsSelector = questionsAdapter.getSelectors(
//   (state: ManageQuestionsState) => state.manageQuestions.questions,
// )

// export const selectSelectedQuestion = createSelector(
//   [
//     (state: ManageQuestionsState) => state,
//     () => selectQuestionsSelector,
//     (state: ManageQuestionsState) => state.manageQuestions.selectedQuestion,
//   ],
//   (state, selector, id) => {
//     if (!id) return undefined

//     return selector.selectById(state, id)
//   },
// )

// export const selectSelectedQuestionText = createSelector(
//   [
//     (state: ManageQuestionsState) => state,
//     () => selectQuestionsSelector,
//     (state: ManageQuestionsState) => state.manageQuestions.selectedQuestion,
//   ],
//   (state, selector, id) => {
//     if (!id) return undefined

//     return selector.selectById(state, id)?.content.text
//   },
// )

// export const selectIsCanAddAnswer = createSelector(
//   [(state: ManageQuestionsState) => selectSelectedQuestion(state)],
//   question =>
//     question?.content?.answers?.every((answ: QuestionAnswer) => answ.text?.length > 0) ||
//     question?.content?.answers.length === 0,
// )

// export const selectIsCanAddQuestion = createSelector(
//   [(state: ManageQuestionsState) => selectQuestionsSelector.selectAll(state)],
//   questions => {
//     const lastElement = questions?.[questions.length - 1]

//     if (!lastElement) return true

//     return lastElement?.content.text?.length > 1
//   },
// )
