import { TAnswer, TQuestion } from '@/entities/themeCourse/model/types'
import { v4 } from 'uuid'

export const getQuestion = (order_id: number): TQuestion => ({
  order_id: order_id,
  text: '',
  multiple_answers: true,
  answers: [getAnswer(), getAnswer()],
  id: v4(),
})

export const getAnswer = (answer?: DeepPartial<TAnswer>): TAnswer => ({
  id: answer?.id ?? v4(),
  text: answer?.text ?? '',
  is_correct: answer?.is_correct ?? false,
  answer_tip: answer?.answer_tip ?? ''
})
