import { z } from 'zod'

export const QuestionExplanationSchema = z.object({
  truth: z.string().optional(),
  falsy: z.string().optional(),
})

export const QuestionAnswerSchema = z.object({
  id: z.string(),
  is_correct: z.boolean(),
  text: z.string().min(1),
})

export const QuestionContentSchema = z.object({
  text: z.string().min(1),
  answers: z.array(z.record(QuestionAnswerSchema)).min(1),
  hasManyAnswers: z.boolean().optional(),
  explanation: QuestionExplanationSchema.optional(),
})

export const QuestionAnswersSchema = z
  .object({
    answers: z.array(QuestionAnswerSchema).min(2),
  })
  .refine(data => {
    if (!data?.answers?.find(answer => answer?.is_correct)) return false

    return true
  })

export const QuestionSchema = z
  .object({
    id: z.string(),
    answers: z
      .array(z.object({ id: z.string(), text: z.string().min(1), is_correct: z.boolean() }))
      .min(2),
    text: z.string().min(1),
    explanation: z.object({ falsy: z.string(), truth: z.string() })?.optional(),
    hasManyAnswers: z.boolean().optional(),
  })
  .refine(data => {
    if (!data?.answers?.find(answer => answer?.is_correct)) return false

    return true
  })

export const QuestionsSchema = z.object({
  questions: z.array(QuestionSchema).min(1),
})

export const TestSettingsSchema = z
  .object({
    explanations: z.boolean(),
    questions_amount: z.number().min(1),
    right_answers: z.number().min(1),
    duration: z.number().min(1),
    allow_continue: z.boolean(),
    limit_attempts: z.boolean(),
    attempts_amount: z.number().optional().nullable().or(z.nan()),
  })
  .refine(data => {
    if (!!data.limit_attempts && !data.attempts_amount) {
      return false
    }
    return true
  })
