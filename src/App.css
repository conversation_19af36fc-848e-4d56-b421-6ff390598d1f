#root {
  margin: 0 auto;
  max-width: 1280px;
  padding: 2rem;

  text-align: center;
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-2-medium);
}

.logo {
  height: 6em;
  padding: 1.5em;

  transition: filter 300ms;

  will-change: filter;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.success {
  color: var(--color-primary);
  font: var(--font-text-1-normal);
}
