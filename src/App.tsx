/* eslint-disable react-hooks/exhaustive-deps */
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { Provider } from 'react-redux'
import { setStore, setupStore, useAppSelector } from '@/store'
import { selectUserInited } from '@/store/slices/auth'
import { LoadingPlug, ProtectedRouter } from '@/shared/components'
import { routes } from '@/shared/configs/routes'
import '@/shared/helpers/polyfills'
import '@/shared/configs/i18n/index'
import { NotificationProvider } from './shared/contexts/notifications'
import 'react-loading-skeleton/dist/skeleton.css'
import ErrorBoundary from '@/shared/components/error-boundary'
import { LanguageWatcher } from '@/shared/components/LanguageWatcher'
import { PropsWithChildren, useMemo } from 'react'
import { userAPI } from 'entities/employee'

const App = () => {
  return (
    <>
      <ErrorBoundary>
        <BrowserRouter>
          <StoreProvider>
            <LanguageWatcher>
              <NotificationProvider>
                <RoutedApp />
              </NotificationProvider>
            </LanguageWatcher>
          </StoreProvider>
        </BrowserRouter>
      </ErrorBoundary>
    </>
  )
}

const StoreProvider = ({ children }: PropsWithChildren) => {
  const navigate = useNavigate()
  const reduxStore = useMemo(() => {
    const store = setupStore({ extraArgs: { navigate } })
    setStore(store)
    return store
  }, [])

  return <Provider store={reduxStore}>{children}</Provider>
}

const RoutedApp = () => {
  const isInited = useAppSelector(selectUserInited)

  userAPI.useGetUserInfoQuery(undefined, {
    skip: isInited,
  })

  if (!isInited) return <LoadingPlug />

  return <ProtectedRouter routes={routes}></ProtectedRouter>
}

export default App
