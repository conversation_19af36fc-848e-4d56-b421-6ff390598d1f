/* eslint-disable @typescript-eslint/no-explicit-any */
// error - части ошибка или полная ошибка
const errorTranslation = [
  {
    error: 'Number of users',
    translate: 'Недостаточно лицензий',
  },
]

export const handleErrorResponseAndTranslate = (e: any) => {
  let error =
    e?.data?.detail ||
    e?.data?.details ||
    e?.data?.message ||
    e.message ||
    (e.error as string) ||
    (e as string)

  if (typeof error !== 'string') {
    try {
      error = JSON.stringify(error)
    } catch {
      error = String(error)
    }
  }

  errorTranslation.forEach(e => {
    if (error.indexOf(e.error) !== -1) {
      error = e.translate
    }
  })

  return error || 'Произошла непредвиденная ошибка'
}
