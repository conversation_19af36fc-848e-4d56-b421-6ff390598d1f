/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { ReactNode, useContext, useMemo } from 'react'
import { NotificationList } from '@/shared/components'
import { v4 as uuid } from 'uuid'
import { handleErrorResponseAndTranslate } from './helper'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  addNotification as addNotificationSlice,
  deleteNotification as deleteNotificationSlice,
} from '@/store/slices/notification-slice'
import { useLazyGetEventQuery } from '@/store/services/event-keeper-service'
import { EPPTXStatus } from '@/shared/types/enums'
import { delay } from '@/shared/helpers/delay'
import { INotification } from '@/shared/types/store/notification'

export interface INotificationContext {
  items: INotification[]
  close: (id: UUID | string) => void
  add: (item: INotification) => void
  fetchPPTXEvent: (
    id: UUID,
    refetch?: () => void,
    message?: { error: string; success: string },
  ) => Promise<void>
  handleErrorResponse: (e: any) => void
  handleResponse: (message: any) => void
}

const notificationContextDefault: INotificationContext = {
  items: [],
  close: async () => {
    throw new Error('Not implemented')
  },
  add: async () => {
    throw new Error('Not implemented')
  },
  fetchPPTXEvent: async () => {
    throw new Error('Not implemented')
  },
  handleErrorResponse: async () => {},
  handleResponse: async () => {},
}

export const notificationContext = React.createContext<INotificationContext>(
  notificationContextDefault,
)

export const NotificationProvider: React.FC<any> = (props: { children: ReactNode }) => {
  const dispatch = useAppDispatch()
  const { notifications } = useAppSelector(state => state.notification)

  const addNotification = async (item: INotification) => {
    dispatch(addNotificationSlice(item))
  }

  const deleteNotification = (id: UUID) => {
    dispatch(deleteNotificationSlice(id))
  }

  const handleResponse = (message: string) => {
    addNotification({
      id: uuid(),
      status: 'success',
      message,
    })
  }
  const handleErrorResponse = (e: any) => {
    const message =
      e.status === 'FETCH_ERROR' ? 'Произошла ошибка сервера' : handleErrorResponseAndTranslate(e)

    addNotification({
      id: uuid(),
      status: 'error',
      message,
    })
  }

  const [getEvent] = useLazyGetEventQuery()

  const fetchPPTXEvent = async (
    id: UUID,
    refetch?: () => void,
    message?: { error: string; success: string },
  ) => {
    const errorItem: INotification = {
      id,
      message: message?.error || 'Не удалось загрузить слайды',
      status: 'error',
    }
    const successItem: INotification = {
      id,
      message: message?.success || 'Слайды успешно загружены',
      status: 'success',
    }

    try {
      const { data } = await getEvent(id)

      const { status } = data || {}

      if (status === EPPTXStatus.success) return addNotification(successItem)
      if (status === EPPTXStatus.error) return addNotification(errorItem)

      let delayCount = 5
      const TICKS = 1_000

      for (let i = 0; i < TICKS; i++) {
        const { data } = await getEvent(id)

        const { status } = data || {}

        if (status === EPPTXStatus.success) {
          addNotification(successItem)
          break
        }
        if (status === EPPTXStatus.error) {
          addNotification(errorItem)
          break
        }

        await delay(delayCount * 1000)

        if (i === 3) {
          delayCount = 4
        } else if (i === 8) {
          delayCount = 3
        } else if (i === 15) {
          delayCount = 2
        }
      }
    } catch (error) {
      addNotification(errorItem)
    } finally {
      refetch && refetch()
    }
  }

  const value = useMemo<INotificationContext>(
    () => ({
      items: notifications,
      close: deleteNotification,
      add: addNotification,
      fetchPPTXEvent,
      handleErrorResponse,
      handleResponse,
    }),
    [notifications, close, addNotification, fetchPPTXEvent, handleErrorResponse, handleResponse],
  )

  return (
    <notificationContext.Provider value={value}>
      <NotificationList />
      {props.children}
    </notificationContext.Provider>
  )
}

export const useNotification = () => useContext<INotificationContext>(notificationContext)
