/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useMemo, useState } from "react";

export interface IStageContext<T extends object = Record<string, any>> {
  next: (meta?: T) => void;
  prev: () => void;
  close: () => void;
  meta?: T;
  setMeta: (meta: T) => void;
}

export const stageContext = React.createContext<IStageContext>({
  next: () => {},
  prev: () => {},
  close: () => {},
  setMeta: () => {},
});

const Provider = stageContext.Provider;

const firstStep = 1;

export const Stages: React.FC<{
  children: Array<React.ReactElement>;
  onClose: () => void;
}> = ({ children, onClose }) => {
  const [stage, setStage] = useState(firstStep);
  const [meta, setMeta] = useState({});

  const stages = React.Children.map<
    React.ReactElement | null,
    React.ReactElement
  >(children, (ch) =>
    React.isValidElement<React.ReactElement>(ch) ? ch : null
  ).filter((e) => !!e);

  const stagesCount = stages.length;

  const value = useMemo<IStageContext>(
    () => ({
      next: (meta) => {
        if (meta) setMeta(meta);
        setStage((st) => (st >= stagesCount ? st : st + 1));
      },
      prev: () => {
        setStage((st) => (st > firstStep ? st - 1 : st));
      },
      close: onClose,
      setMeta,
      meta,
    }),
    [stagesCount, onClose, meta]
  );
  return <Provider value={value}>{stages[stage - 1]}</Provider>;
};
