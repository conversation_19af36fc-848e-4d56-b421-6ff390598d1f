export const DATE_LOCALE = 'ru-RU'
export const MS_PER_HOUR = 1000 * 60 * 60
export const MS_PER_DAY = MS_PER_HOUR * 24
export const DOT_DATE_FORMAT = 'dd.MM.yyyy'
export const DASH_DATE_FORMAT = 'yyyy-MM-dd'
export const SHORT_HUMAN_DATE_FORMAT = 'd MMM'
export const DATE_TIME_FORMAT = 'dd.MM.yyyy HH:mm'
export const TIME_FORMAT = 'HH:mm'
export const DATE_LOCALES = {
  en: 'en-US',
  ru: 'ru-RU',
  // work around https://www.notion.so/1ebb4b2e56df8014824ef5420c7bddbd?source=copy_link
  uz: 'en-US',
}
export const DEFAULT_LOCALE = DATE_LOCALES['ru']

/**
 * Функционал для форматирования даты
 */
const DATE_STRING_TOKENS = [
  'yyyy',
  'yy',
  'LLLL',
  'MMMM',
  'MMM',
  'MM',
  'M',
  'dd',
  'd',
  'HH',
  'H',
  'mm',
  'm',
  'ss',
  's',
] as const

// регулярное выражение для поиска токенов в шаблоне
const TOKEN_REGEX = new RegExp(DATE_STRING_TOKENS.join('|'), 'g')

type Token = (typeof DATE_STRING_TOKENS)[number]

const pad = (n: number, len = 2) => String(n).padStart(len, '0')

const dateHandlers: Record<Token, (d: Date, locale?: string) => string> = {
  yyyy: d => String(d.getFullYear()),
  yy: d => String(d.getFullYear()).slice(-2),
  LLLL: (d, locale) => new Intl.DateTimeFormat(locale, { month: 'long' }).format(d),
  MMMM: (d, locale) => {
    return new Intl.DateTimeFormat(locale, { month: 'long' }).format(d)
  },
  MMM: (d, locale) => {
    return new Intl.DateTimeFormat(locale, { month: 'short' }).format(d).replace('.', '')
  },
  MM: d => pad(d.getMonth() + 1),
  M: d => String(d.getMonth() + 1),
  dd: d => pad(d.getDate()),
  d: d => String(d.getDate()),
  HH: d => pad(d.getHours()),
  H: d => String(d.getHours()),
  mm: d => pad(d.getMinutes()),
  m: d => String(d.getMinutes()),
  ss: d => pad(d.getSeconds()),
  s: d => String(d.getSeconds()),
}

/**
 * Форматирование даты по указанному шаблону
 * @param date - Date, дата для форматирования
 * @param formatStr - string, строка формата (например, 'dd.MM.yyyy')
 * @returns string, отформатированная строка даты
 *
 * @example
 * format(new Date(), 'dd MM yyyy') // '01 01 2023'
 * format(new Date(), 'd MMM y HH:mm') // '1 янв 2023 12:00'
 * format(new Date(), 'dd.MM.yyyy HH:mm:ss') // '01.01.2023 12:00:00'
 */
export const format = (() => {
  return (date: Date, format: string, locale?: string): string => {
    if (!isValidDate(date)) return ''

    const result = format.replace(TOKEN_REGEX, tok =>
      dateHandlers[tok as Token](
        date,
        DATE_LOCALES[locale as keyof typeof DATE_LOCALES] || DEFAULT_LOCALE,
      ),
    )

    return result
  }
})()

/**
 * Преобразует строку ISO формата в объект Date
 * @param dateStr - строка в формате ISO 8601 (например, '2023-01-31T14:30:00.000Z')
 * @returns объект Date
 */
export function parseISO(dateStr: string): Date {
  return new Date(dateStr)
}

export const getTimeLabel = (time: number): string => {
  // time in seconds
  const h = Math.floor(time / (60 * 60)) || null
  const m = Math.floor(time / 60)
  const s = Math.floor(time % 60)

  return [h, m, s]
    .filter(v => v !== null)
    .map(v => (v || 0).toString())
    .map(e => (e.length > 1 ? e : '0' + e))
    .join(':')
}

const getDateWithoutTimezone = (date: string): Date => {
  const parseDate = parseISO(date)
  const timezoneInMilliseconds = parseDate.getTimezoneOffset() * 60 * 1000
  const dateInMilliseconds = parseDate.getTime()

  const dateWithoutTimezoneInMilliseconds = dateInMilliseconds + timezoneInMilliseconds

  return new Date(dateWithoutTimezoneInMilliseconds)
}

export const fromISO = (date?: string, f = 'dd.MM.yyyy', hasTimezone?: boolean) => {
  if (!date) return undefined
  if (!hasTimezone) return format(parseISO(date), f)

  const dateWithoutTimezone = getDateWithoutTimezone(date)

  return format(dateWithoutTimezone, f)
}

export const getDate = () => dateToString()

// Работает для дат формата yyyy-mm-dd и приводит к дате dd.mm.yyyy
export const getDateFromDashed = (date: string) => {
  return date.split('-').reverse().join('.')
}

export const dateToString = (date = new Date()) => {
  return date.toISOString().slice(0, 10)
}

/**
 * получить время в формате -> dd.mm.yyyy
 * @param date - дата, в которую вы хотите получить время в dd.mm.yyyy
 * @returns string
 */
export const getDateThroughDot = (date: Date) => format(date, DOT_DATE_FORMAT)

export const compareDatesWithoutTime = (firstDate: Date, secondDate: Date) => {
  const firstCopy = new Date(firstDate)
  const secondCopy = new Date(secondDate)
  startOfDay(firstCopy)
  startOfDay(secondCopy)
  return firstCopy > secondCopy
}

export function calculateDays(monthsAgo: number): number {
  const today = new Date()
  const targetDate = subMonths(new Date(), monthsAgo)
  return differenceInDays(today, targetDate)
}

const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone

export const getPrettyDate = (date: Date = new Date(), locale?: string) => {
  const currentLocale = locale || DEFAULT_LOCALE
  return new Date(date.getTime()).toLocaleString(currentLocale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: false,
    timeZone,
  })
}

export function subtractOneDay(date: Date): Date {
  const result = new Date(date)
  result.setDate(result.getDate() - 1)
  return result
}

export function formatDateRange(
  startDate: Date,
  endDate: Date,
  locale?: string,
): { startDate: string; endDate: string } {
  const currentLocale = locale || DEFAULT_LOCALE
  const options: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  }

  return {
    startDate: startDate.toLocaleString(currentLocale, options),
    endDate: endDate.toLocaleString(currentLocale, options),
  }
}

export type DateDifference = {
  months: number
  days: number
}

export function getDateDifference(date: Date): DateDifference | undefined {
  const now = new Date()

  if (now >= date) return undefined

  const diffInMilliseconds = date.getTime() - now.getTime()
  const diffInSeconds = diffInMilliseconds / 1000
  const diffInDays = Math.floor(diffInSeconds / (3600 * 24))

  const months = Math.floor(diffInDays / 30)
  const remainingDays = diffInDays % 30

  return { months, days: remainingDays }
}

export function formatDateDifference(date1: Date, date2: Date) {
  date2.setHours(23, 59, 59, 999)
  const diffInMilliseconds = date2.getTime() - date1.getTime()

  if (diffInMilliseconds < 0) return null

  const days = Math.floor(diffInMilliseconds / MS_PER_DAY)
  const hours = Math.floor((diffInMilliseconds % MS_PER_DAY) / MS_PER_HOUR)

  if (days > 0 || hours >= 24) {
    return {
      day: true,
      days,
    }
  } else {
    return {
      hour: true,
      hours,
    }
  }
}

/**
 * Возвращает разницу в днях между двумя датами (dateLeft - dateRight)
 * @param dateLeft - более поздняя дата
 * @param dateRight - более ранняя дата
 * @returns количество дней (целое число)
 */
export function differenceInDays(dateLeft: Date, dateRight: Date): number {
  const start = startOfDay(dateLeft)
  const end = startOfDay(dateRight)
  const timeDifference = start.getTime() - end.getTime()
  const daysDifference = timeDifference / MS_PER_DAY
  return Math.floor(daysDifference)
}

export function fromUnixTime(timestamp: number): Date {
  const date = new Date(timestamp * 1000)
  if (isValidDate(date)) {
    return date
  }
  return new Date(NaN)
}

export function getUnixTime(date: Date): number {
  return Math.floor(date.getTime() / 1000)
}

/**
 * Добавляет указанное количество дней к дате
 * @param date - исходная дата
 * @param amount - количество дней для добавления
 * @returns новая дата
 */
export function addDays(date: Date, amount: number): Date {
  const result = new Date(date)
  result.setDate(result.getDate() + amount)
  return result
}

/**
 * Добавляет указанное количество месяцев к дате
 * @param date - исходная дата
 * @param amount - количество месяцев для добавления
 * @returns новая дата
 */
export function addMonths(date: Date, amount: number): Date {
  const result = new Date(date)
  const currentMonth = result.getMonth()
  const targetMonth = currentMonth + amount

  result.setMonth(targetMonth)

  // Проверка на краевой случай, когда день месяца исходной даты
  // не существует в целевом месяце (например, 31 января -> 31 февраля)
  const expectedMonth = ((targetMonth % 12) + 12) % 12 // Нормализация к диапазону 0-11
  if (result.getMonth() !== expectedMonth) {
    // Устанавливаем последний день предыдущего месяца если месяц "перепрыгнул"
    result.setDate(0)
  }

  return result
}

/**
 * Вычитает указанное количество месяцев из даты
 * @param date - исходная дата
 * @param amount - количество месяцев для вычитания
 * @returns новая дата
 */
export function subMonths(date: Date, amount: number): Date {
  return addMonths(date, -amount)
}

/**
 * Проверяет, является ли первая дата более поздней, чем вторая
 * @param date - дата для сравнения
 * @param dateToCompare - дата, с которой сравниваем
 * @returns true, если date позже dateToCompare
 */
export function isAfter(date: Date, dateToCompare: Date): boolean {
  return date.getTime() > dateToCompare.getTime()
}

/**
 * Проверяет, является ли первая дата более ранней, чем вторая
 * @param date - дата для сравнения
 * @param dateToCompare - дата, с которой сравниваем
 * @returns true, если date раньше dateToCompare
 */
export function isBefore(date: Date, dateToCompare: Date): boolean {
  return date.getTime() < dateToCompare.getTime()
}

/**
 * Проверяет, является ли дата понедельником
 * @param date - дата для проверки
 * @returns true, если это понедельник
 */
export function isMonday(date: Date): boolean {
  return date.getDay() === 1 // 0 - воскресенье, 1 - понедельник, ...
}

/**
 * Проверяет, совпадают ли две даты (день, месяц, год)
 * @param dateLeft - первая дата для сравнения
 * @param dateRight - вторая дата для сравнения
 * @returns true, если даты совпадают (день, месяц, год)
 */
export function isSameDay(dateLeft: Date, dateRight: Date): boolean {
  return (
    dateLeft.getDate() === dateRight.getDate() &&
    dateLeft.getMonth() === dateRight.getMonth() &&
    dateLeft.getFullYear() === dateRight.getFullYear()
  )
}

/**
 * Проверяет, совпадают ли месяц и год у двух дат
 * @param dateLeft - первая дата для сравнения
 * @param dateRight - вторая дата для сравнения
 * @returns true, если месяц и год совпадают
 */
export function isSameMonth(dateLeft: Date, dateRight: Date): boolean {
  return (
    dateLeft.getMonth() === dateRight.getMonth() &&
    dateLeft.getFullYear() === dateRight.getFullYear()
  )
}

/**
 * Проверяет, является ли дата выходным днем (суббота или воскресенье)
 * @param date - дата для проверки
 * @returns true, если это выходной день
 */
export function isWeekend(date: Date): boolean {
  const day = date.getDay()
  return day === 0 || day === 6 // 0 - воскресенье, 6 - суббота
}

/**
 * Возвращает дату предшествующего понедельника
 * @param date - исходная дата
 * @returns дата предшествующего понедельника
 */
export function previousMonday(date: Date): Date {
  const day = date.getDay() // 0 - воскресенье, 1 - понедельник и т.д.
  const result = new Date(date)

  // Если сегодня понедельник, возвращаем предыдущий понедельник
  if (day === 1) {
    result.setDate(date.getDate() - 7)
    return result
  }

  // Для воскресенья (0) вычитаем 6 дней, для вторника (2) - 1 день и т.д.
  const daysToSubtract = day === 0 ? 6 : day - 1
  result.setDate(date.getDate() - daysToSubtract)
  return result
}

/**
 * Возвращает дату начала месяца для указанной даты
 * @param date - исходная дата
 * @returns дата начала месяца
 */
export function startOfMonth(date: Date): Date {
  const result = new Date(date)
  result.setDate(1)
  return startOfDay(result)
}

/**
 * Проверяет, является ли дата сегодняшним днем
 * @param date - дата для проверки
 * @returns true, если дата - сегодня
 */
export function isToday(date: Date): boolean {
  return isSameDay(date, new Date())
}

/**
 * Устанавливает часы для указанной даты, не изменяя другие компоненты даты
 * @param date - исходная дата
 * @param hours - часы для установки (0-23)
 * @returns новая дата с установленными часами
 */
export function setHours(date: Date, hours?: number): Date {
  const result = new Date(date)
  if (hours) result.setHours(hours)
  return result
}

/**
 * Устанавливает минуты для указанной даты, не изменяя другие компоненты даты
 * @param date - исходная дата
 * @param minutes - минуты для установки (0-59)
 * @returns новая дата с установленными минутами
 */
export function setMinutes(date: Date, minutes?: number): Date {
  const result = new Date(date)
  if (minutes) result.setMinutes(minutes)
  return result
}

/**
 * Возвращает часы для указанной даты (0-23)
 * @param date - дата
 * @returns часы (0-23)
 */
export function getHours(date: Date): number {
  return date.getHours()
}

/**
 * Возвращает минуты для указанной даты (0-59)
 * @param date - дата
 * @returns минуты (0-59)
 */
export function getMinutes(date: Date): number {
  return date.getMinutes()
}

/**
 * Возвращает более позднюю из двух или более дат
 * @param dates - массив дат для сравнения
 * @returns наиболее поздняя дата
 */
export function max(dates: Date[]): Date {
  if (dates.length === 0) {
    return new Date(NaN)
  }

  let result = dates[0]
  for (let i = 1; i < dates.length; i++) {
    if (dates[i].getTime() > result.getTime()) {
      result = dates[i]
    }
  }

  return result
}

/**
 * Возвращает дату начала дня (сбрасывает часы, минуты, секунды и миллисекунды на 0)
 * @param date - исходная дата
 * @returns дата начала дня
 */
export function startOfDay(date: Date): Date {
  const result = new Date(date)
  result.setHours(0, 0, 0, 0)
  return result
}

/**
 * Возвращает дату конца дня (устанавливает часы на 23:59:59.999)
 * @param date - исходная дата
 * @returns дата конца дня
 */
export function endOfDay(date: Date): Date {
  const result = new Date(date)
  result.setHours(23, 59, 59, 999)
  return result
}

export function isValidDate(date: Date) {
  if (Object.prototype.toString.call(date) === '[object Date]') {
    return !isNaN(date.getTime())
  }
  return false
}

export const getDateInADay = (date?: Date) => {
  const now = date ?? new Date()
  const tomorrow = new Date(now.getTime() + MS_PER_DAY)
  return tomorrow
}

export const getTomorrow = () => {
  const now = new Date()
  const tomorrow = new Date(now.getTime() + MS_PER_DAY)
  return startOfDay(tomorrow)
}
