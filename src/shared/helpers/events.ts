/* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { MouseEvent } from "react";

export const getOnlyDiffs = <T extends object>(
  obj1: T,
  obj2: T
): Partial<T> => {
  const diffs: Partial<T> = {};

  Object.keys(obj2).forEach((key) => {
    if (obj1[key as keyof T] !== obj2[key as keyof T]) {
      diffs[key as keyof T] = obj2[key as keyof T];
    }
  });

  return diffs;
};

export const plural = (
  number: number,
  one: string,
  two: string,
  five: string
) => {
  let n = Math.abs(number);

  n %= 100;

  if (n >= 5 && n <= 20) {
    return five;
  }
  n %= 10;

  if (n === 1) {
    return one;
  }

  if (n >= 2 && n <= 4) {
    return two;
  }

  return five;
};
export const filterEmpty = <T extends object>(obj: T): Partial<T> => {
  return Object.keys(obj)
    .filter((k) => {
      switch (typeof obj[k as keyof T]) {
        case "string": {
          return (obj[k as keyof T] as unknown as string).length > 0;
        }
        default: {
          return true;
        }
      }
    })
    .reduce(
      (all, key) => ({
        ...all,
        [key]: obj[key as keyof T],
      }),
      {} as Partial<T>
    );
};

export const filterUndefined = <T extends object>(obj: T): T => {
  return Object.keys(obj)
    .filter((k) => k !== undefined)
    .reduce(
      (all, key) => ({
        ...all,
        [key]: obj[key as keyof T],
      }),
      {} as T
    );
};

export const filterNull = <T extends object>(obj: T): T => {
  return Object.keys(obj)
    .filter((k) => k !== null)
    .reduce(
      (all, key) => ({
        ...all,
        [key]: obj[key as keyof T],
      }),
      {} as T
    );
};

export const filterUnique = <T>(array: T[]): T[] => {
  return array.filter((value, index, self) => self.indexOf(value) === index);
};

export const stopPropagation = <T>(e: MouseEvent<T>) => e.stopPropagation();

export const rnd = (min: number, max: number) =>
  Math.random() * (max - min) + min;

export const getUnique = <T, K extends keyof T = keyof T>(
  array: T[],
  key: K
): T[] => {
  return [
    ...array
      .reduce((a, c) => {
        a.set(c[key], c);

        return a;
      }, new Map<T[K], T>())
      .values(),
  ];
};

export const withPreventDefault =
  <T extends Element, F extends (event: MouseEvent<T>, ...args: any[]) => void>(
    f?: F
  ) =>
  (event: MouseEvent<T>, ...args: any[]) => {
    preventDefault(event);
    return f ? f(event, ...args) : undefined;
  };

export const withStopPropagation =
  <T extends any, F extends (event: T) => void>(f?: F) =>
  (event: T) => {
    stopPropagation(event as MouseEvent);
    return f ? f(event) : undefined;
  };

export const preventDefault = (event: any) => {
  event.preventDefault();
};

export const joinFuncs = <A extends any, T extends (...args: A[]) => void>(
  ...funcs: T[]
) => {
  return (...args: A[]) => funcs.map((f) => f(...args));
};

// TODO remove
export function combineURLs(baseURL: string, relativeURL: string) {
  return relativeURL
    ? baseURL.replace(/\/+$/, "") + "/" + relativeURL.replace(/^\/+/, "")
    : baseURL;
}
