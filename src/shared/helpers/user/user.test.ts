import { getFullName } from './user'

describe('[shared] [helpers] getFullName', () => {
  it('Должен вернуть ФИО, если есть что-то из него и нет почты', () => {
    expect(getFullName({ last_name: 'Тестов' })).toEqual('Тестов')
    expect(getFullName({ first_name: 'Тест' })).toEqual('Тест')
    expect(getFullName({ middle_name: 'Те<PERSON>т<PERSON>' })).toEqual('Тестович')
    expect(getFullName({ last_name: 'Тестов', first_name: 'Тест' })).toEqual('Тестов Тест')
    expect(getFullName({ first_name: 'Тест', middle_name: 'Тестович' })).toEqual('Тест Тестович')
    expect(getFullName({ last_name: 'Тестов', middle_name: 'Тестович' })).toEqual('Тестов Тестович')
    expect(
      getFullName({ last_name: 'Тестов', first_name: 'Тест', middle_name: 'Тестович' }),
    ).toEqual('Тестов Тест Тестович')
  })

  it('Должен вернуть ФИО, если есть что-то из него и есть почта', () => {
    expect(getFullName({ email: '<EMAIL>', last_name: 'Тестов' })).toEqual('Тестов')
    expect(getFullName({ email: '<EMAIL>', first_name: 'Тест' })).toEqual('Тест')
    expect(getFullName({ email: '<EMAIL>', middle_name: 'Тестович' })).toEqual('Тестович')
    expect(
      getFullName({ email: '<EMAIL>', last_name: 'Тестов', first_name: 'Тест' }),
    ).toEqual('Тестов Тест')
    expect(
      getFullName({ email: '<EMAIL>', first_name: 'Тест', middle_name: 'Тестович' }),
    ).toEqual('Тест Тестович')
    expect(
      getFullName({ email: '<EMAIL>', last_name: 'Тестов', middle_name: 'Тестович' }),
    ).toEqual('Тестов Тестович')
    expect(
      getFullName({
        email: '<EMAIL>',
        last_name: 'Тестов',
        first_name: 'Тест',
        middle_name: 'Тестович',
      }),
    ).toEqual('Тестов Тест Тестович')
  })

  it('Должен вернуть почту, есть почта и нет ничего из ФИО', () => {
    expect(getFullName({ email: '<EMAIL>' })).toEqual('<EMAIL>')
  })

  it('Должен вернуть пустую строку, если ничего нет', () => {
    expect(getFullName({})).toEqual('')
  })

  it('Должен вернуть пустую строку, если все данные равны null', () => {
    expect(
      getFullName({ last_name: null, first_name: null, middle_name: null, email: null }),
    ).toEqual('')
  })
})
