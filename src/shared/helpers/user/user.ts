/**
 * @kind helper
 * @desc Функция, соединяющая ФИО (из тех данных, что есть) в одну строку, либо отдающая почту, если их нет
 * @returns {string} ФИО, либо почта, либо пустая строка
 */
export const getFullName = ({
  last_name = '',
  first_name = '',
  middle_name = '',
  email = '',
}: {
  last_name?: Nullable<string>
  first_name?: Nullable<string>
  middle_name?: Nullable<string>
  email?: Nullable<string>
}): string => {
  const fullName =
    (last_name ? last_name : '') +
    (first_name ? `${last_name ? ' ' : ''}${first_name}` : '') +
    (middle_name ? `${first_name || last_name ? ' ' : ''}${middle_name}` : '')

  return fullName || email || ''
}
