/* eslint-disable @typescript-eslint/no-explicit-any */
export interface DateSummaryHashItem {
  date: string
  quiz: number
  theory: number
  overall: number
}

export interface DateCompanySummaryHashItem {
  date: string
  theory: number
  quiz: number
  overall: number
}

export interface DateSummaryHash {
  [date: string]: {
    count: number
    quizSum: number
    learningSum: number
  }
}

export interface StaticticsData {
  title: string
  color: string
  data: any
}

interface CalculateDateSummaryResult {
  learningData: any[]
  testingData: any[]
}

export function calculateDateSummary(
  progressStatisticsChange: DateSummaryHashItem[] | null | undefined,
): CalculateDateSummaryResult {
  const dateSummary: DateSummaryHash = {}

  function processItem(item: DateSummaryHashItem) {
    const { date, quiz, theory } = item

    if (!dateSummary[date]) {
      dateSummary[date] = { count: 0, quizSum: 0, learningSum: 0 }
    }

    dateSummary[date].count++
    dateSummary[date].quizSum += quiz
    dateSummary[date].learningSum += theory
  }

  const progress = progressStatisticsChange || []

  // eslint-disable-next-line @typescript-eslint/no-extra-semi
  ;[...progress].forEach(processItem)

  const totalLength = Math.max(progressStatisticsChange?.length || 0)

  const learningData = new Array(totalLength)
  const testingData = new Array(totalLength)

  let index = 0

  for (const date in dateSummary) {
    const { count, quizSum, learningSum } = dateSummary[date]
    learningData[index] = { date, value: learningSum / (count || 1) }
    testingData[index] = { date, value: quizSum / (count || 1) }
    index++
  }

  return {
    learningData: learningData?.filter(Boolean),
    testingData: testingData?.filter(Boolean),
  }
}

export function calculateCompanyDateSummary(
  progressStatisticsChange: DateCompanySummaryHashItem[] | null | undefined,
): CalculateDateSummaryResult {
  const dateSummary: DateSummaryHash = {}
  function processItem(item: DateSummaryHashItem | DateCompanySummaryHashItem) {
    const { date } = item

    if (!dateSummary[date]) {
      dateSummary[date] = { count: 0, quizSum: 0, learningSum: 0 }
    }

    if ('quiz' in item) {
      const { quiz, theory } = item as DateSummaryHashItem
      dateSummary[date].quizSum += quiz
      dateSummary[date].learningSum += theory
    } else {
      const { theory, quiz } = item as DateCompanySummaryHashItem
      dateSummary[date].quizSum += quiz
      dateSummary[date].learningSum += theory
    }

    dateSummary[date].count++
  }

  const progress = progressStatisticsChange || []

  // eslint-disable-next-line @typescript-eslint/no-extra-semi
  ;[...progress].forEach(processItem)

  const totalLength = Math.max(progressStatisticsChange?.length || 0)

  const learningData = new Array(totalLength)
  const testingData = new Array(totalLength)

  let index = 0

  for (const date in dateSummary) {
    const { count, quizSum, learningSum } = dateSummary[date]
    learningData[index] = { date, value: learningSum / (count || 1) }
    testingData[index] = { date, value: quizSum / (count || 1) }
    index++
  }

  return { learningData: learningData?.filter(Boolean), testingData: testingData?.filter(Boolean) }
}
