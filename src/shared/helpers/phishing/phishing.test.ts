import { groupByCategory } from './phishing'
import { phishingTemplates, phishingTemplatesWithoutCategory } from './__mocks__/phishingTemplates'

describe('[shared] [helpers] phishing groupByCategory', () => {
  it('Должен возвращать пустой массив, если передан пустой список шаблонов', () => {
    expect(groupByCategory([])).toEqual([])
  })

  it('Должен корректно группировать шаблоны по категориям и сортировать', () => {
    const result = groupByCategory(phishingTemplates)

    expect(result).toHaveLength(2)

    // Проверяем, что первая категория — "Соцсети" (т. к. сортировка в обратном порядке)
    expect(result[0]?.category?.name).toBe('Соцсети')
    expect(result[0].templates).toHaveLength(1)
    expect(result[0].templates[0].name).toBe('Facebook (ориг)')

    // Проверяем, что вторая категория — "Банки"
    expect(result[1]?.category?.name).toBe('Банки')
    expect(result[1].templates).toHaveLength(1)
    expect(result[1].templates[0].name).toBe('Сбербанк')
  })

  it('Должен корректно обрабатывать случай, когда у шаблонов нет категории', () => {
    const result = groupByCategory(phishingTemplatesWithoutCategory)

    expect(result).toHaveLength(1)
    expect(result[0].category).toBeNull()
    expect(result[0].templates).toHaveLength(1)
    expect(result[0].templates[0].name).toBe('Шаблон без категории')
  })

  it('Должен корректно обрабатывать шаблоны без категории и с категориями', () => {
    const templatesWithAndWithoutCategory = [
      ...phishingTemplates,
      ...phishingTemplatesWithoutCategory,
    ]

    const result = groupByCategory(templatesWithAndWithoutCategory)

    expect(result).toHaveLength(3)
    // Проверяем, что первая категория — "Соцсети"
    expect(result[0]?.category?.name).toBe('Соцсети')
    expect(result[0].templates).toHaveLength(1)
    expect(result[0].templates[0].name).toBe('Facebook (ориг)')

    // Проверяем, что вторая категория — "Банки"
    expect(result[1]?.category?.name).toBe('Банки')
    expect(result[1].templates).toHaveLength(1)
    expect(result[1].templates[0].name).toBe('Сбербанк')

    // Проверяем, что третья группа — это шаблоны без категории
    expect(result[2]?.category).toBeNull()
    expect(result[2].templates).toHaveLength(1)
    expect(result[2].templates[0].name).toBe('Шаблон без категории')
  })

  it('Должен корректно группировать шаблоны по категориям, если несколько одной категории', () => {
    const templatesWithSameCategory = [
      ...phishingTemplates,
      ...phishingTemplatesWithoutCategory,
      ...phishingTemplates.map(i => ({ ...i, id: i.id + '-clone', name: i.name + ' Клон' })),
      ...phishingTemplatesWithoutCategory.map(i => ({
        ...i,
        id: i.id + '-clone',
        name: i.name + ' Клон',
      })),
    ]

    const result = groupByCategory(templatesWithSameCategory)

    // Проверяем, что количество категорий включает категорию "null" (без категории)
    const uniqueCategories = new Set(templatesWithSameCategory.map(t => t.category?.name || null))
    expect(result).toHaveLength(uniqueCategories.size)

    // Проверяем, что все шаблоны без категории попали в одну группу
    const noCategoryGroup = result.find(group => group.category === null)
    const expectedNoCategoryCount = templatesWithSameCategory.filter(t => !t.category).length

    expect(noCategoryGroup).toBeDefined()
    expect(noCategoryGroup?.templates).toHaveLength(expectedNoCategoryCount)

    // Проверяем, что шаблоны одной категории сгруппированы корректно
    uniqueCategories.forEach(categoryName => {
      if (categoryName !== null) {
        const categoryGroup = result.find(group => group.category?.name === categoryName)
        const expectedCount = templatesWithSameCategory.filter(
          t => t.category?.name === categoryName,
        ).length

        expect(categoryGroup).toBeDefined()
        expect(categoryGroup?.templates).toHaveLength(expectedCount)
      }
    })
  })
})
