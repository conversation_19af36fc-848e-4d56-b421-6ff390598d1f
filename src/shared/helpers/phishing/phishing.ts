import { IPhishingTemplate, IPhishingTemplateByCategory } from '@/entities/phishing'

/**
 * Группирует массив фишинговых шаблонов по их категориям.
 *
 * @param {IPhishingTemplate[]} templates - Массив фишинговых шаблонов.
 * @returns {IPhishingTemplateByCategory[]} - Массив сгруппированных шаблонов по категориям.
 *
 * @example
 * const templates = [
 *   { id: '1', name: 'Template A', category: { id: 'cat1', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' } },
 *   { id: '2', name: 'Template B', category: { id: 'cat2', name: 'Соцсети' } },
 *   { id: '3', name: 'Template C', category: { id: 'cat1', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>' } },
 *   { id: '4', name: 'Template D', category: null }
 * ];
 *
 * const grouped = groupByCategory(templates);
 * console.log(grouped);
 * // [
 * //   { id: 'cat1', category: { id: 'cat1', name: 'Банки' }, templates: [...] },
 * //   { id: 'cat2', category: { id: 'cat2', name: 'Соцсети' }, templates: [...] },
 * //   { id: null, category: null, templates: [...] } // Без категории
 * // ]
 */
export const groupByCategory = (templates: IPhishingTemplate[]): IPhishingTemplateByCategory[] => {
  if (templates.length === 0) return []

  const categoryMap = new Map<string | null, IPhishingTemplateByCategory>()

  for (const template of templates) {
    const category = template.category ?? null
    const categoryId = category?.id ?? null

    if (!categoryMap.has(categoryId)) {
      categoryMap.set(categoryId, {
        id: categoryId,
        category,
        templates: [],
      })
    }

    categoryMap.get(categoryId)!.templates.push(template)
  }

  return [...categoryMap.values()].sort((a, b) => {
    if (!a.category) return 1
    if (!b.category) return -1
    return b.category.name.localeCompare(a.category.name)
  })
}
