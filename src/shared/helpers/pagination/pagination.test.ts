import { getPages } from './pagination'

describe('[shared] [helpers] pagination getPages', () => {
  it('Должен возвращать корректные страницы при обычных значениях', () => {
    expect(getPages(5, 10)).toEqual([1, 3, 4, 5, 6, 7, 10])
    expect(getPages(1, 5)).toEqual([1, 2, 3, 5])
    expect(getPages(10, 10)).toEqual([1, 8, 9, 10])
  })

  it('Должен возвращать все страницы, если total страниц мало', () => {
    expect(getPages(1, 3)).toEqual([1, 2, 3])
    expect(getPages(2, 3)).toEqual([1, 2, 3])
    expect(getPages(3, 3)).toEqual([1, 2, 3])
  })

  it('Должен возвращать только одну страницу, если total = 1', () => {
    expect(getPages(1, 1)).toEqual([1])
  })

  it('Должен возвращать корректные страницы при нахождении в начале диапазона', () => {
    expect(getPages(2, 10)).toEqual([1, 2, 3, 4, 10])
    expect(getPages(3, 10)).toEqual([1, 2, 3, 4, 5, 10])
  })

  it('Должен возвращать корректные страницы при нахождении в конце диапазона', () => {
    expect(getPages(9, 10)).toEqual([1, 7, 8, 9, 10])
    expect(getPages(8, 10)).toEqual([1, 6, 7, 8, 9, 10])
  })

  it('Должен корректно обрабатывать случаи, когда total = 0', () => {
    expect(getPages(1, 0)).toEqual([])
  })

  it('Должен корректно обрабатывать отрицательные значения', () => {
    expect(getPages(-1, 10)).toEqual([1, 10]); // Отрицательная страница не имеет смысла, берем только 1 и последнюю
    expect(getPages(-5, 5)).toEqual([1, 5]); // Аналогично, берем первую и последнюю
  })

  it('Должен корректно работать при page > total', () => {
    expect(getPages(12, 10)).toEqual([1, 10]) // Точно также, как и с отрицательными
  })
})
