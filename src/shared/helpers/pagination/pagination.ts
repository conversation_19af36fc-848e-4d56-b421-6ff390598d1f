/**
 * Генерирует массив номеров страниц для пагинации.  
 * Функция гарантирует, что в массиве всегда будут первая и последняя страницы,  
 * а также несколько страниц вокруг текущей для удобной навигации (добавляет по 2 слева и справа, если они есть).
 *
 * @param {number} page - Текущая активная страница.
 * @param {number} total - Общее количество страниц.
 * @returns {number[]} Массив уникальных номеров страниц, отсортированный по возрастанию.
 *
 * @example
 * getPages(5, 10); // [1, 3, 4, 5, 6, 7, 10]
 *
 * @example
 * getPages(1, 5); // [1, 2, 3, 5]
 *
 * @example
 * getPages(10, 10); // [1, 8, 9, 10]
 */
export const getPages = (page: number, total: number): number[] => {
  const pagesSet = new Set<number>()

  // Добавляем диапазон страниц вокруг текущей страницы (-2 до +2)
  for (let i = -2; i <= 2; i++) {
    const p = page + i
    if (p > 0 && p <= total) {
      pagesSet.add(p)
    }
  }

  if (total > 0) {
    pagesSet.add(1); // Первая страница
    pagesSet.add(total); // Последняя страница
  }

  return Array.from(pagesSet).sort((a, b) => a - b)
}
