/* eslint-disable @typescript-eslint/no-explicit-any */
import { differenceInDays } from '@/shared/helpers/date'

import { EMyCourseStatus, IMyCourse, IMyScormCourse } from '@/shared/types/store/course'
import { getCopy } from './'
import { getTimstamp } from './employees'
import { ECourseStatus } from '@/shared/types/enums'

//! Получение активных курсов (как скорм, так и модульных)
export const getActiveCourses = (courses: (IMyCourse | IMyScormCourse)[]) => {
  return courses.filter(c => {
    if ((c as IMyScormCourse).assigned_course) {
      c = c as IMyScormCourse

      return c.assigned_course.status !== ECourseStatus.completed
    } else {
      c = c as IMyCourse

      return c.status !== EMyCourseStatus.complete
    }
  })
}

//! Получение завершенных курсов (как скорм, так и модульных) (не по дате, а по статусу)
export const getEndedCourses = (courses: (IMyCourse | IMyScormCourse)[]) => {
  return courses.filter(c => {
    if ((c as IMyScormCourse).assigned_course) {
      c = c as IMyScormCourse

      return c.assigned_course.status === ECourseStatus.completed
    } else {
      c = c as IMyCourse

      return c.status === EMyCourseStatus.complete
    }
  })
}

//! Сортировка курсов по дате начала (как скорм, так и модульных)
export const getSortedMyCoursesByStartDate = (courses: (IMyCourse | IMyScormCourse)[]) => {
  return getCopy<typeof courses>(courses).sort((a: any, b: any) => {
    if (a.assigned_course) {
      a = a.assigned_course
    }
    if (b.assigned_course) {
      b = b.assigned_course
    }

    return +getTimstamp(b.start_date) - +getTimstamp(a.start_date)
  })
}

//! Получение ID курса (как скорм, так и модульных)
export const getCourseID = (c: IMyCourse | IMyScormCourse) => {
  if ((c as IMyScormCourse).assigned_course) {
    c = c as IMyScormCourse

    return c.assigned_course.id
  } else {
    c = c as IMyCourse

    return c.id
  }
}

//! Получение выполненных курсов (как скорм, так и модульных)
export const getCompletedCourses = (courses: (IMyCourse | IMyScormCourse)[]) => {
  return courses.filter(c => c.statistics.value === 100)
}

export const isEndedCourse = (endDate: string) => {
  return +endDate.split('-')[0] < 2030
}

// format: yyyy-MM-dd
const parse = (dateString: string): Date => {
  const [year, month, day] = dateString.split('-').map(Number)
  return new Date(year, month - 1, day)
}

export const getDaysLeft = (endDate: string) => {
  return endDate ? differenceInDays(parse(endDate), new Date().withoutTime()) : undefined
}
