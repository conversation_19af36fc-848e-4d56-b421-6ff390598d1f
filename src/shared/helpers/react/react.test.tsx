import React, {  } from 'react';
import { useStrictContext, createStrictContext } from './react';
import { renderTestScreen } from '../tests/render';
import '@testing-library/jest-dom';



describe('[shared] [helpers] react', () => {
  describe('useStrictContext', () => {
    it('Должен правильно возвращать значение контекста, если оно передано', () => {
      const TestContext = createStrictContext<string>();
      const TestComponent = () => {
        const contextValue = useStrictContext(TestContext);

        return <div>{contextValue}</div>;
      };

      const { getByText } = renderTestScreen(
        <TestContext.Provider value="Test Value">
          <TestComponent />
        </TestContext.Provider>
      );

      expect(getByText('Test Value')).toBeInTheDocument();
    });

    it('Должен выбрасывать ошибку, если контекст равен null', () => {
      const TestContext = createStrictContext<string>();
      const TestComponent: React.FC<{ value: string | null }> = () => {
        const contextValue = useStrictContext(TestContext);

        return <div>{contextValue}</div>;
      };

      // Мокаем консоль, чтобы подавить ошибку в тестах
      const consoleError = jest.spyOn(console, 'error').mockImplementation(() => { });

      expect(() =>
        renderTestScreen(
          <TestContext.Provider value={null}>
            <TestComponent value={null} />
          </TestContext.Provider>
        )
      ).toThrowError('Strict context not passed');

      // Восстанавливаем консоль
      consoleError.mockRestore(); 
    });
  });

  describe('createStrictContext', () => {
    it('Должен создавать контекст с значением null по умолчанию', () => {
      const NewContext = createStrictContext<number>();
      const TestComponent = () => {
        const contextValue = useStrictContext(NewContext);

        return <div>{contextValue}</div>;
      };

      expect(() =>
        renderTestScreen(
          <NewContext.Provider value={null}>
            <TestComponent />
          </NewContext.Provider>
        )
      ).toThrowError('Strict context not passed');
    });

    it('Должен создавать контекст и передавать значение', () => {
      const NewContext = createStrictContext<number>();
      const TestComponent = () => {
        const contextValue = useStrictContext(NewContext);

        return <div>{contextValue}</div>;
      };

      const { getByText } = renderTestScreen(
        <NewContext.Provider value={42}>
          <TestComponent />
        </NewContext.Provider>
      );

      expect(getByText('42')).toBeInTheDocument();
    });
  });
})

