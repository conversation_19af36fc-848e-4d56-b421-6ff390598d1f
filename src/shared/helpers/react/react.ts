import { Context, createContext, useContext } from 'react'

/**
 * Хук для безопасного доступа к значению контекста. 
 * Бросает ошибку, если контекст не был передан (значение равно `null`).
 * 
 * @template T - Тип значения, которое хранится в контексте.
 * @param {Context<T | null>} context - Контекст, из которого извлекается значение.
 * @returns {T} Возвращает значение контекста, если оно не равно `null`.
 * @throws {Error} Если значение контекста равно `null`, генерирует ошибку с сообщением "Strict context not passed".
 */
export function useStrictContext<T>(context: Context<T | null>) {
  const value = useContext(context)
  if (value === null) throw new Error('Strict context not passed')
  return value as T
}

/**
 * Функция для создания контекста, который всегда должен быть предоставлен. 
 * Контекст инициализируется значением `null`, что указывает на отсутствие значения по умолчанию.
 * 
 * @template T - Тип значения, которое будет храниться в контексте.
 * @returns {Context<T | null>} Возвращает новый контекст с типом `T | null`, где `null` означает отсутствие значения.
 */
export function createStrictContext<T>() {
  return createContext<T | null>(null)
}
