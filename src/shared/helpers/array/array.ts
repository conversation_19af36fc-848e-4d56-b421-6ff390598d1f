/**
 * Преобразует массив строк или чисел в объект (Record), 
 * где ключами являются элементы массива, а значениями — `true`.
 * Используется для фильтраа отделов при генерации вложенного списка сотрудников
 *
 * @template T - Тип элементов массива (строки или числа).
 * @param {T[]} arr - Входной массив строк или чисел.
 * @returns {Record<string | number, true>} Объект с ключами из массива и значением `true` для каждого.
 *
 * @example
 * arrayToRecord(['a', 'b', 'c']); // { a: true, b: true, c: true }
 * arrayToRecord([1, 2, 3]); // { 1: true, 2: true, 3: true }
 */
export function arrayToRecord<T extends string | number>(arr: T[]): Record<string | number, true> {
  return arr.reduce(
    (acc, item) => {
      acc[item] = true
      return acc
    },
    {} as Record<string | number, true>,
  )
}

/**
 * Функция для добавления или удаления значения из массива.
 * Если значение уже есть в массиве, оно удаляется. Если нет — добавляется.
 *
 * @template T - Тип элементов массива.
 * @param {T} value - Значение, которое нужно добавить или удалить.
 * @param {T[]} array - Исходный массив.
 * @param {(value: React.SetStateAction<T[]>) => void} toggle - Функция обновления состояния.
 *
 * @example
 * // Пример использования в React:
 * const [selectedItems, setSelectedItems] = useState<string[]>([]);
 * toggleArrayValue('apple', selectedItems, setSelectedItems);
 */
export const toggleArrayValue = <T>(
  value: T,
  array: T[],
  toggle: (value: React.SetStateAction<T[]>) => void,
) => {
  const position = array.indexOf(value)

  if (position === -1) {
    toggle(prev => [...prev, value])
  } else {
    toggle(prev => [...prev.slice(0, position), ...prev.slice(position + 1)])
  }
}
