import { renderHook } from '@testing-library/react'
import { useState, act } from 'react'
import { arrayToRecord, toggleArrayValue } from './array'

describe('[shared] [helpers] array', () => {
  describe('arrayToRecord', () => {
    it('Должен корректно преобразовывать массив строк в объект', () => {
      expect(arrayToRecord(['a', 'b', 'c'])).toEqual({ a: true, b: true, c: true })
    })

    it('Должен корректно преобразовывать массив чисел в объект', () => {
      expect(arrayToRecord([1, 2, 3])).toEqual({ 1: true, 2: true, 3: true })
    })

    it('Должен корректно работать с пустым массивом', () => {
      expect(arrayToRecord([])).toEqual({})
    })

    it('Должен корректно работать с массивом, содержащим одинаковые значения', () => {
      expect(arrayToRecord(['a', 'a', 'b'])).toEqual({ a: true, b: true })
      expect(arrayToRecord([1, 1, 2])).toEqual({ 1: true, 2: true })
    })

    it('Должен корректно работать со смешанными типами в массиве', () => {
      expect(arrayToRecord(['1', 2, 'three'])).toEqual({ '1': true, 2: true, three: true })
    })

    it('Должен корректно работать с одним элементом в массиве', () => {
      expect(arrayToRecord(['single'])).toEqual({ single: true })
      expect(arrayToRecord([42])).toEqual({ 42: true })
    })

    it('Должен корректно работать с числами с плавающей точкой', () => {
      expect(arrayToRecord([1.1, 2.2, 3.3])).toEqual({ 1.1: true, 2.2: true, 3.3: true })
    })

    it('Должен корректно работать с `0` и пустыми строками', () => {
      expect(arrayToRecord([0, ''])).toEqual({ 0: true, '': true })
    })

    it('Должен корректно работать с `NaN`', () => {
      expect(arrayToRecord([NaN])).toEqual({ NaN: true })
    })

    it('Должен корректно работать с отрицательными числами', () => {
      expect(arrayToRecord([-1, -2, -3])).toEqual({ '-1': true, '-2': true, '-3': true })
    })

    it('Должен корректно работать с числами в виде строк', () => {
      expect(arrayToRecord(['1', '2', '3'])).toEqual({ '1': true, '2': true, '3': true })
    })

    it('Должен корректно работать с длинными строками', () => {
      const input = ['long_string_value_with_special_chars!@#$%^&*()']
      expect(arrayToRecord(input)).toEqual({
        'long_string_value_with_special_chars!@#$%^&*()': true,
      })
    })

    it('Должен корректно работать с многократными пустыми строками', () => {
      expect(arrayToRecord(['', '', 'a'])).toEqual({ '': true, a: true })
    })

    it('Должен корректно работать с разными регистрами', () => {
      expect(arrayToRecord(['a', 'A', 'b', 'B'])).toEqual({ a: true, A: true, b: true, B: true })
    })
  })

  describe('toggleArrayValue', () => {
    it('Должен добавлять значение в пустой массив', () => {
      const { result } = renderHook(() => useState<string[]>([]));
  
      act(() => {
        toggleArrayValue('apple', result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual(['apple']);
    });
  
    it('Должен удалять единственное значение в массиве', () => {
      const { result } = renderHook(() => useState(['apple']));
  
      act(() => {
        toggleArrayValue('apple', result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual([]);
    });
  
    it('Должен добавлять несколько разных значений', () => {
      const { result } = renderHook(() => useState<string[]>([]));
  
      act(() => {
        toggleArrayValue('apple', result.current[0], result.current[1]);
      });
      act(() => {
        toggleArrayValue('banana', result.current[0], result.current[1]);
      });
      act(() => {
        toggleArrayValue('cherry', result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual(['apple', 'banana', 'cherry']);
    });
  
    it('Должен корректно удалять конкретное значение, не трогая остальные', () => {
      const { result } = renderHook(() => useState(['apple', 'banana', 'cherry']));
  
      act(() => {
        toggleArrayValue('banana', result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual(['apple', 'cherry']);
    });
  
    it('Должен корректно работать с числами', () => {
      const { result } = renderHook(() => useState<number[]>([1, 2, 3]));
  
      act(() => {
        toggleArrayValue(2, result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual([1, 3]);
    });
  
    it('Должен корректно работать с булевыми значениями', () => {
      const { result } = renderHook(() => useState<boolean[]>([true, false]));
  
      act(() => {
        toggleArrayValue(false, result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual([true]);
    });
  
    it('Должен работать с null и undefined', () => {
      const { result } = renderHook(() => useState<(string | null | undefined)[]>(['apple', null]));
  
      act(() => {
        toggleArrayValue(null, result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual(['apple']);
  
      act(() => {
        toggleArrayValue(undefined, result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual(['apple', undefined]);
    });
  
    it('Должен корректно работать с объектами', () => {
      const obj1 = { id: 1 };
      const obj2 = { id: 2 };
  
      const { result } = renderHook(() => useState<{ id: number }[]>([obj1]));
  
      act(() => {
        toggleArrayValue(obj2, result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual([obj1, obj2]);
    });
  
    it('Должен удалять объект, если он уже есть (по ссылке)', () => {
      const obj = { id: 1 };
  
      const { result } = renderHook(() => useState<{ id: number }[]>([obj]));
  
      act(() => {
        toggleArrayValue(obj, result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual([]);
    });
  
    it('Должен добавлять объект с таким же значением, но другой ссылкой', () => {
      const obj1 = { id: 1 };
      const obj2 = { id: 1 }; // Другой объект с тем же значением
  
      const { result } = renderHook(() => useState<{ id: number }[]>([obj1]));
  
      act(() => {
        toggleArrayValue(obj2, result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual([obj1, obj2]); // Разные ссылки → оба объекта остаются
    });
  
    it('Должен корректно работать с массивами внутри массива', () => {
      const nestedArray = [1, 2, 3];
      const { result } = renderHook(() => useState<(number | number[])[]>([nestedArray]));
  
      act(() => {
        toggleArrayValue(nestedArray, result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual([]); // Удаление по ссылке
  
      act(() => {
        toggleArrayValue([1, 2, 3], result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual([[1, 2, 3]]); // Новый массив (другая ссылка)
    });
  
    it('Должен корректно работать с пустым массивом', () => {
      const { result } = renderHook(() => useState<string[]>([]));
  
      act(() => {
        toggleArrayValue('orange', result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual(['orange']);
    });
  
    it('Должен не вызывать ошибку при пустом массиве и попытке удалить значение', () => {
      const { result } = renderHook(() => useState<string[]>([]));
  
      act(() => {
        toggleArrayValue('apple', result.current[0], result.current[1]);
      });
  
      act(() => {
        toggleArrayValue('banana', result.current[0], result.current[1]);
      });
  
      act(() => {
        toggleArrayValue('apple', result.current[0], result.current[1]);
      });
  
      expect(result.current[0]).toEqual(['banana']);
    });
  });
})
