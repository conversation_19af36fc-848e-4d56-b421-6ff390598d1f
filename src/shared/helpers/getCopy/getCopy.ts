/**
 * Создает глубокую копию переданного объекта, используя JSON.stringify и JSON.parse.
 * 
 * 🔹 **Ограничения:**
 * - Не сохраняет методы объекта.
 * - Потеряет `undefined`, `Symbol`, `Function`, `Map`, `Set`, `Date`, `RegExp` и другие сложные типы.
 * - Работает только с сериализуемыми объектами.
 * 
 * @template T - Тип объекта, который копируется.
 * @param {T} obj - Объект, который нужно скопировать.
 * @returns {T} - Глубокая копия переданного объекта.
 */
export const getCopy = <T>(obj: T) => JSON.parse(JSON.stringify(obj)) as unknown as T;
