import { getCopy } from './getCopy'
describe('[shared] [helpers] getCopy', () => {
  it('Должен полностью копировать не сохраняя ссылки', async () => {
    const worker = {
      name: 'Джо<PERSON>',
      job: {
        salary: '100',
        position: 'Noob',
        workplace: {
          floor: '1',
          table: '123',
        },
      },
    }

    const worker2 = getCopy(worker)

    expect(worker).toEqual(worker2)
    expect(worker.job.workplace !== worker2.job.workplace).toBeTruthy()

    worker2.job.workplace.table = '111'

    expect(worker.job.workplace.table !== '111').toBeTruthy()
  })
})
