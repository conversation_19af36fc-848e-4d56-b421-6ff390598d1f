import { ReactNode } from 'react'
import { render, RenderResult } from '@testing-library/react'
import { TestScreen } from './test-screen'

/**
 * @kind helper
 * @desc Функция-обертка для рендеринга компонентов.
 * Вместо render()
 */
export const renderTestScreen = (element: ReactNode): RenderResult => {
  const renderResult = render(<TestScreen>{element}</TestScreen>)

  const rawRerender = renderResult.rerender
  renderResult.rerender = (element: ReactNode) => {
    rawRerender(<TestScreen>{element}</TestScreen>)
  }

  return renderResult
}
