import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'

const DEFAULT_LANGUAGE = 'ru'
const DEFAULT_NAMESPACE = ['translation', 'commons']

/**
 * @kind helper
 * @desc Функция для инициализации переводов для тестирования
 * @param translations - объект с переводами
 */
export function initI18n(translations = {}) {
  i18n.use(initReactI18next).init({
    lng: DEFAULT_LANGUAGE,
    fallbackLng: DEFAULT_LANGUAGE,
    ns: DEFAULT_NAMESPACE,
    defaultNS: DEFAULT_NAMESPACE,
    debug: false,
    interpolation: {
      escapeValue: false,
    },
    resources: {
      [DEFAULT_LANGUAGE]: {
        [DEFAULT_NAMESPACE[0]]: translations,
        [DEFAULT_NAMESPACE[1]]: translations,
      },
    },
  })
}

/**
 * @kind helper
 * @desc Функция для добавления мок-переводов для тестирования
 * ---
 * @param resource - объект с переводами
 * @example { "old": { "back_button": { "back": "Назад" }, }
 * ---
 * @param ns - namespace, путь до файла с переводами
 * @default ['translation', 'commons']
 * @example const { t } = useTranslation('pages__translation')
 * ns мока для теста = 'pages__translation'
 *
 * @param lang - язык
 */
export function addI18nResources(
  resource = {},
  { ns = DEFAULT_NAMESPACE, lang = DEFAULT_LANGUAGE } = {},
) {
  ns.forEach(ns => {
    i18n.addResourceBundle(lang, ns, resource, true, true)
  })
}
