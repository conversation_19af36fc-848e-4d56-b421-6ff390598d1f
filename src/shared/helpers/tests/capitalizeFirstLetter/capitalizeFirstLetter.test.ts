import { capitalizeFirstLetter } from "./capitalizeFirstLetter"

describe('[shared] [helpers] tests capitalizeFirstLetter', () => {
  it('Должен делать первую букву большой, оставляя другие в том же размере', () => {
    expect(capitalizeFirstLetter('строка')).toEqual('Строка')
    expect(capitalizeFirstLetter('Строка')).toEqual('Строка')
    expect(capitalizeFirstLetter('сТРОКА')).toEqual('СТРОКА')
    expect(capitalizeFirstLetter('СТРОКА')).toEqual('СТРОКА')
    expect(capitalizeFirstLetter('123')).toEqual('123')
  })
})
