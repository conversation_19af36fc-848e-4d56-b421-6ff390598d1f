import Handlebars from 'handlebars'
import { generatePrimaryPalette } from '@/shared/utils'

function getApiUrl(env: string) {
  if (env === 'offline') {
    return 'https://offline.sec-t.ru/api'
  } else if (env === 'offline-local' || env === 'local') {
    return 'http://localhost:5000'
  }
  return 'https://edu.sec-t.ru/api'
}

const generateConfigJson = (env: string) => {
  const apiData = {
    id: 'secure-t',
    url: 'edu.sec-t.ru',
    title: 'Secure-T',
    description: 'Secure-T',
    logo: {
      light: 'https://storage.yandexcloud.net/secure-t-frontend-develop/secure-t.png',
      dark: 'https://storage.yandexcloud.net/secure-t-frontend-develop/secure-t.png',
    },
    theme: {
      id: 'default',
      colors: {
        ...generatePrimaryPalette('#3dbc87'),
      },
    },
  }

  return {
    layout: false,
    ...apiData,

    analytics_enabled: true,
    yandex_metrika_id: '87651235',
    google_analytics_id: '',
    lang: 'ru',
    useSSO: env === 'offline' || env === 'offline-local',
    eventKeeperUrl: 'https://dev-services.sec-t.ru',
    apiUrl: getApiUrl(env),
    needPrivacyPolicyPage: true,
    needAgreementPage: true,
  }
}

const generateConfigJsonString = (env: string) => JSON.stringify(generateConfigJson(env), null, 4)

const generateHtml = (env?: string) => {
  const envValue = process.env.NODE_ENV || 'development'
  const template = Handlebars.compile(generateConfigJsonString(env || envValue)) // Ваш шаблон Handlebars здесь

  const configData = generateConfigJson(env || envValue)

  return template({
    ...configData,
    config_json: generateConfigJsonString(env || envValue),
  })
}

const ConfigComponent = () => {
  const generatedHtml = generateHtml()

  // Ваш JSX компонента здесь

  return <script async dangerouslySetInnerHTML={{ __html: generatedHtml }} id='config' />
}

export default ConfigComponent
