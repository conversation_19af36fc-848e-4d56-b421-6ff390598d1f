import { TFunction } from 'i18next'
import { FieldValues, Path, PathValue, UseFormSetError, UseFormSetValue } from 'react-hook-form'

/**
 * @kind helper
 * @desc Функция для отрезания лишних пробелов для формы. Обычно использовалось в register => onBlur
 * @example 
 * const handleTrim = useCallback((str: string, name: keyof EditQuizDialogProps.Inputs, isRequired = false) => {
 *   return trimFormInput<EditQuizDialogProps.Inputs>({ str, name, isRequired, setError, setValue, t })
 * }, [setError, setValue, t])
*/

export function trimFormInput<TFieldValues extends FieldValues = FieldValues>({
  str,
  name,
  isRequired = false,
  setError,
  setValue,
  t
}: {
  str: string
  name: Path<TFieldValues>
  isRequired: boolean
  setError: UseFormSetError<TFieldValues>
  setValue: UseFormSetValue<TFieldValues>
  t: TFunction
}) {
  if (!str?.trim().length && isRequired) {
    setError(name, { type: 'custom', message: t('commons:required_field') })
  }
  setValue(name, str.trim() as PathValue<TFieldValues, Path<TFieldValues>>)
}
