/**
 * Обрезает строку до указанной длины и добавляет в конец указанную строку.
 *
 * @param {string} input - Исходная строка, которую нужно обрезать.
 * @param {number} n - Максимальная длина обрезанной строки.
 * @param {string} [additionalString='...'] - Строка, добавляемая в конец (по умолчанию "...").
 * @returns {string} - Обрезанная строка с добавленной строкой в конце.
 *
 * @example
 * truncateString('Hello, world!', 5) // 'Hello...'
 * truncateString('Hello, world!', 5, '!!!') // 'Hello!!!'
 * truncateString('Short', 10) // 'Short'
 */
export function truncateString(input: string, n: number, additionalString = '...'): string {
  if (n <= 0) return additionalString
  if (input.length <= n) return input

  return input.slice(0, n) + additionalString
}

/**
 * Обрезает длинную строку в середине, заменяя вырезанный фрагмент на "...".
 *
 * @param {string} str - Исходная строка.
 * @param {number} [maxLength=21] - Максимальная длина результирующей строки (по умолчанию 21).
 * @returns {string} - Строка, обрезанная в середине, если она превышает maxLength.
 *
 * @example
 * truncateStringInMiddle('Hello, this is a long sentence!', 15)
 * // 'Hello, thi...tence!'
 *
 * truncateStringInMiddle('Short string', 20)
 * // 'Short string' (не обрезается)
 */
export function truncateStringInMiddle(str: string, maxLength = 21): string {
  if (str.length <= maxLength) {
    return str
  }

  const ellipsis = '...'

  if (maxLength <= 0) return ellipsis

  const charsToShow = maxLength - ellipsis.length
  const frontChars = Math.ceil(charsToShow / 2)
  const backChars = Math.floor(charsToShow / 2)

  return str.slice(0, frontChars) + ellipsis + str.slice(str.length - backChars)
}

/**
 * Удаляет последний сегмент строки после разделителя.
 * Если разделитель не найден, возвращает исходную строку.
 *
 * @param {string | undefined | null} input - Исходная строка для обработки.
 * @param {string} [delimiter='_'] - Разделитель для поиска сегментов (по умолчанию '_').
 * @returns {string} - Строка без последнего сегмента или исходная строка.
 */
export function removeLastSegmentAfterDelimiter(
  input: string | undefined | null,
  delimiter = '_',
): string {
  if (!input) return ''

  if (!input.includes(delimiter)) {
    return input
  }

  const segments = input.split(delimiter)
  return segments.slice(0, -1).join(delimiter)
}
