import { truncateString, truncateStringInMiddle } from './text'

describe('[shared] [helpers] text', () => {
  describe('truncateString', () => {
    it('Должен обрезать строку и добавлять "..." по умолчанию', () => {
      expect(truncateString('Hello, world!', 5)).toBe('Hello...')
    })

    it('Должен обрезать строку и добавлять кастомный суффикс', () => {
      expect(truncateString('Hello, world!', 5, '!!!')).toBe('Hello!!!')
    })

    it('Должен возвращать строку без изменений, если её длина меньше или равна указанной', () => {
      expect(truncateString('Short', 10)).toBe('Short')
      expect(truncateString('Exactly14chars', 14)).toBe('Exactly14chars')
    })

    it('Должен корректно работать с пустой строкой', () => {
      expect(truncateString('', 5)).toBe('')
    })

    it('Должен корректно обрабатывать случаи, когда n равно 0', () => {
      expect(truncateString('Hello, world!', 0)).toBe('...')
    })

    it('Должен корректно работать при отрицательном значении n', () => {
      expect(truncateString('Hello, world!', -1)).toBe('...')
    })

    it('Должен корректно работать с длинными строками', () => {
      const longString = 'a'.repeat(100)
      expect(truncateString(longString, 10)).toBe('aaaaaaaaaa...')
    })
  })

  describe('truncateStringInMiddle', () => {
    it('Должен обрезать строку в середине и добавлять "..."', () => {
      expect(truncateStringInMiddle('Hello, this is a long sentence!', 15)).toBe('Hello,...tence!')
    })

    it('Должен возвращать строку без изменений, если её длина меньше или равна maxLength', () => {
      expect(truncateStringInMiddle('Short', 10)).toBe('Short')
      expect(truncateStringInMiddle('Exactly23CharactersLong', 23)).toBe('Exactly23CharactersLong')
    })

    it('Должен корректно работать с пустой строкой', () => {
      expect(truncateStringInMiddle('', 5)).toBe('')
    })

    it('Должен корректно обрабатывать случаи, когда maxLength равно 0', () => {
      expect(truncateStringInMiddle('Hello, world!', 0)).toBe('...')
    })

    it('Должен корректно обрезать длинные строки с разными maxLength', () => {
      expect(truncateStringInMiddle('Supercalifragilisticexpialidocious', 10)).toBe('Supe...ous')
      expect(truncateStringInMiddle('Supercalifragilisticexpialidocious', 5)).toBe('S...s')
    })

    it('Должен корректно работать, если строка содержит пробелы и спецсимволы', () => {
      expect(truncateStringInMiddle('Hello, this is a test!', 10)).toBe('Hell...st!')
      expect(truncateStringInMiddle('   spaced out text   ', 10)).toBe('   s...   ')
    })
  })
})
