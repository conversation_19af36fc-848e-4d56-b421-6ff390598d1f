/* eslint-disable @typescript-eslint/no-explicit-any */
export function paramsClerify<T extends Record<string, any>>(
  obj: T,
  removeValues?: Partial<T>,
): Partial<T> {
  const isEmpty = (val: any): boolean => {
    if (val == null) return true
    if (typeof val === 'string') return val.trim().length === 0
    if (Array.isArray(val)) return val.length === 0
    if (typeof val === 'object') return Object.keys(val).length === 0
    return false
  }

  const shouldRemove = (key: string, value: any): boolean => {
    if (!removeValues) return false

    if (Object.prototype.hasOwnProperty.call(removeValues, key)) {
      const removeValue = removeValues[key]

      if (Array.isArray(removeValue) && Array.isArray(value)) {
        return JSON.stringify(removeValue) === JSON.stringify(value)
      }
      return removeValue === value
    }

    return false
  }

  const result: Record<string, any> = {}

  Object.entries(obj).forEach(([key, value]) => {
    if (shouldRemove(key, value)) return
    if (isEmpty(value)) return

    if (typeof value === 'object' && !Array.isArray(value)) {
      const nested = paramsClerify(value, removeValues)
      if (Object.keys(nested).length > 0) {
        result[key] = nested
      }
    } else {
      result[key] = value
    }
  })

  return result as Partial<T>
}
