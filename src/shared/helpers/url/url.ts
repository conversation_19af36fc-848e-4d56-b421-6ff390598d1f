/**
 * Объединяет базовый URL с относительным путем, корректно убирая/добавляя слэши.
 *
 * @param {string} baseURL - Базовый URL (например, `'https://example.com'`).
 * @param {string} relativeURL - Относительный путь (например, `'/path'` или `'path'`).
 * @returns {string} Полный объединенный URL.
 *
 * @example
 * combineURLs('https://example.com', 'path') // 'https://example.com/path'
 * combineURLs('https://example.com/', '/path') // 'https://example.com/path'
 * combineURLs('https://example.com//', '//path') // 'https://example.com/path'
 * combineURLs('https://example.com', '') // 'https://example.com'
 */
export function combineURLs(baseURL: string, relativeURL: string): string {
  if (!relativeURL) return baseURL

  return baseURL?.replace(/\/+$/, '') + '/' + relativeURL?.replace(/^\/+/, '')
}

/**
 * Извлекает расширение файла из переданного URL или пути.
 * 
 * @param {string} url - URL или путь к файлу.
 * @returns {string | undefined} Расширение файла в нижнем регистре или undefined, если расширения нет.
 *
 * @example
 * getExtensionFromUrl('https://example.com/image.png') // 'png'
 * getExtensionFromUrl('file.tar.gz') // 'gz'
 * getExtensionFromUrl('https://example.com') // com - `исключение`
 * getExtensionFromUrl('/path/.gitignore') // undefined
 * getExtensionFromUrl('https://example.com/archive..tar.gz') // 'gz'
 */
export const getExtensionFromUrl = (url: string): string | undefined => {
  if (!url) return undefined;

  // Убираем query-параметры и hash
  const cleanUrl = url.split(/[?#]/)[0];

  // Если после удаления нет точки (значит, нет расширения)
  if (!cleanUrl.includes('.')) return undefined;

  // Получаем последнюю часть пути после "/"
  const parts = cleanUrl.split('/').filter(Boolean);
  const lastSegment = parts[parts.length - 1];

  // Игнорируем файлы без имени, например ".gitignore"
  if (lastSegment.startsWith('.') && !lastSegment.includes('.', 1)) {
    return undefined;
  }

  // Извлекаем расширение (после последней точки)
  const match = lastSegment.match(/\.([a-zA-Z0-9]+)$/);
  return match ? match[1].toLowerCase() : undefined;
}