import { combineURLs, getExtensionFromUrl } from './url'

describe('[shared] [helpers] url', () => {
  describe('combineURLs', () => {
    it('Должен корректно объединять baseURL и relativeURL', () => {
      expect(combineURLs('https://example.com', 'path')).toBe('https://example.com/path')
      expect(combineURLs('https://example.com/', '/path')).toBe('https://example.com/path')
      expect(combineURLs('https://example.com//', '//path')).toBe('https://example.com/path')
    })

    it('Должен возвращать baseURL, если relativeURL пуст', () => {
      expect(combineURLs('https://example.com', '')).toBe('https://example.com')
      expect(combineURLs('https://example.com/', '')).toBe('https://example.com/')
    })

    it('Должен работать с query-параметрами', () => {
      expect(combineURLs('https://example.com/', '?key=value')).toBe(
        'https://example.com/?key=value',
      )
      expect(combineURLs('https://example.com/api', '/?id=123')).toBe(
        'https://example.com/api/?id=123',
      )
    })

    it('Должен корректно работать с hash-фрагментами', () => {
      expect(combineURLs('https://example.com/', '#section')).toBe('https://example.com/#section')
    })

    it('Должен корректно работать без baseURL', () => {
      expect(combineURLs('', 'path')).toBe('/path')
      expect(combineURLs('', '/path')).toBe('/path')
    })

    it('Должен корректно работать с baseURL без "/" в конце', () => {
      expect(combineURLs('https://example.com', '/page')).toBe('https://example.com/page')
      expect(combineURLs('https://example.com', 'page')).toBe('https://example.com/page')
    })

    it('Должен корректно работать, если relativeURL - только "/"', () => {
      expect(combineURLs('https://example.com', '/')).toBe('https://example.com/')
    })

    it('Должен корректно работать с пустыми строками', () => {
      expect(combineURLs('', '')).toBe('')
      expect(combineURLs('https://example.com', '')).toBe('https://example.com')
      expect(combineURLs('', 'page')).toBe('/page')
    })
  })

  describe('getExtensionFromUrl', () => {
    it('Должен корректно извлекать расширение из обычных URL', () => {
      expect(getExtensionFromUrl('file.txt')).toBe('txt')
      expect(getExtensionFromUrl('image.jpeg')).toBe('jpeg')
      expect(getExtensionFromUrl('document.PDF')).toBe('pdf')
      expect(getExtensionFromUrl('archive.tar.gz')).toBe('gz')

      expect(getExtensionFromUrl('https://example.com/image.png')).toBe('png')
      expect(getExtensionFromUrl('https://example.com/archive.tar.gz')).toBe('gz')
      expect(getExtensionFromUrl('https://example.com/index.html')).toBe('html')

      expect(getExtensionFromUrl('https://static.sec-t.ru/media/reports/Отчет по отделу (Без отдела 03-02-25)_5af9cdb0d2c3__otTYqX5CTQ.xlsx')).toBe('xlsx')
    })

    it('Должен корректно работать с URL, содержащими параметры и хеш', () => {
      expect(getExtensionFromUrl('file.txt?version=2')).toBe('txt')
      expect(getExtensionFromUrl('image.png#hash')).toBe('png')
      expect(getExtensionFromUrl('video.mp4?autoplay=1#section')).toBe('mp4')

      expect(getExtensionFromUrl('https://example.com/style.css?version=1.2')).toBe('css')
      expect(getExtensionFromUrl('https://example.com/script.js#hash')).toBe('js')
    })

    it('Должен корректно работать с URL, содержащими слэши', () => {
      expect(getExtensionFromUrl('https://example.com/path/file.html')).toBe('html')
      expect(getExtensionFromUrl('https://example.com/file.tar.gz')).toBe('gz')
      expect(getExtensionFromUrl('/folder/subfolder/script.js')).toBe('js')
    })

    it('Должен корректно работать с URL без расширения', () => {
      expect(getExtensionFromUrl('https://example.com')).toBe('com')
      expect(getExtensionFromUrl('https://example.com/path/')).toBe(undefined)
      expect(getExtensionFromUrl('https://example.com/page?query=value')).toBe(undefined)
      expect(getExtensionFromUrl('https://example.com/?query=value')).toBe('com')
      expect(getExtensionFromUrl('https://example.com/#section')).toBe('com')
    })

    it('Должен работать с файлами без доменов', () => {
      expect(getExtensionFromUrl('/home/<USER>/file.txt')).toBe('txt')
      expect(getExtensionFromUrl('C:\\path\\to\\file.exe')).toBe('exe')
    })

    it('Должен возвращать undefined для пустых строк', () => {
      expect(getExtensionFromUrl('')).toBe(undefined)
    })

    it('Должен корректно работать с URL, где точка используется в названии, но не как расширение', () => {
      expect(getExtensionFromUrl('https://example.com/my.file')).toBe('file') // Вернет "file", т.к. это считается расширением
      expect(getExtensionFromUrl('https://example.com/path.with.dots/')).toBe('dots')
      expect(getExtensionFromUrl('https://example.com/.hiddenfile')).toBe(undefined) // Нет расширения после точки
    })

    it('Должен корректно обрабатывать URL с многоточием', () => {
      expect(getExtensionFromUrl('file....txt')).toBe('txt')
      expect(getExtensionFromUrl('https://example.com/archive..tar.gz')).toBe('gz')
    })

    it('Должен корректно работать с файлами без имени', () => {
      expect(getExtensionFromUrl('/path/.gitignore')).toBe(undefined)
      expect(getExtensionFromUrl('.gitignore')).toBe(undefined)
      expect(getExtensionFromUrl('.env')).toBe(undefined)
    })

    it('Должен работать с относительными путями', () => {
      expect(getExtensionFromUrl('/path/to/file.mp4')).toBe('mp4')
      expect(getExtensionFromUrl('file.pdf')).toBe('pdf')
    })

    it('Должен корректно работать с URL без "/" перед расширением', () => {
      expect(getExtensionFromUrl('https://example.com.file.txt')).toBe('txt')
    })

    it('Должен возвращать undefined, если расширение не указано (даже если есть "?")', () => {
      expect(getExtensionFromUrl('https://example.com/file?version=2')).toBeUndefined()
    })
  })
})
