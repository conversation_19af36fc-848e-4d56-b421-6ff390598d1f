import { isNumber, formatNumber } from './number'

describe('[shared] [helpers] number', () => {
  describe('isNumber', () => {
    it('Должен возвращать true для чисел', () => {
      expect(isNumber(0)).toBe(true)
      expect(isNumber(42)).toBe(true)
      expect(isNumber(-100)).toBe(true)
      expect(isNumber(Infinity)).toBe(true)
      expect(isNumber(-Infinity)).toBe(true)
    })

    it('Должен возвращать false для NaN', () => {
      expect(isNumber(NaN)).toBe(false)
    })

    it('Должен возвращать false для нечисловых значений', () => {
      expect(isNumber('42')).toBe(false)
      expect(isNumber([])).toBe(false)
      expect(isNumber({})).toBe(false)
      expect(isNumber(null)).toBe(false)
      expect(isNumber(undefined)).toBe(false)
      expect(isNumber('Hello')).toBe(false)
    })
  })

  describe('formatNumber', () => {
    it('Должен форматировать положительные числа с пробелами', () => {
      expect(formatNumber(1000)).toBe('1 000')
      expect(formatNumber(1234567)).toBe('1 234 567')
    })

    it('Должен форматировать отрицательные числа с пробелами', () => {
      expect(formatNumber(-1000)).toBe('-1 000')
      expect(formatNumber(-9876543)).toBe('-9 876 543')
    })

    it('Должен корректно работать с разным размером группировки', () => {
      expect(formatNumber(123456789, 2)).toBe('1 23 45 67 89')
      expect(formatNumber(987654321, 4)).toBe('9 8765 4321')
    })

    it('Должен корректно форматировать короткие числа', () => {
      expect(formatNumber(9)).toBe('9')
      expect(formatNumber(-5)).toBe('-5')
      expect(formatNumber(99)).toBe('99')
    })

    it("Должен корректно форматировать числа с десятичной частью", () => {
      expect(formatNumber(1234567.89)).toBe("1 234 567.89");
      expect(formatNumber(-98765.4321)).toBe("-98 765.4321");
    });

    it("должен корректно обрабатывать NaN и Infinity", () => {
      expect(formatNumber(NaN)).toBe("NaN");
      expect(formatNumber(Infinity)).toBe("Infinity");
      expect(formatNumber(-Infinity)).toBe("-Infinity");
    });
  })
})
