/**
 * Проверяет, является ли переданное значение числом (исключая NaN).
 *
 * @param {unknown} value - Значение для проверки.
 * @returns {value is number} `true`, если `value` - число (включая 0, Infinity и отрицательные значения), иначе `false`.
 */
export function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value)
}

/**
 * Форматирует число, добавляя пробелы в качестве разделителей разрядов.
 *
 * @param {number} num - Число для форматирования.
 * @param {number} [groupSize=3] - Размер группы цифр (по умолчанию 3).
 * @returns {string} Отформатированное число в строковом формате.
 *
 * @example
 * formatNumber(1234567) // "1 234 567"
 * formatNumber(-987654321, 4) // "-9 8765 4321"
 * formatNumber(1234.56789) // "1 234.56789"
 */
export function formatNumber(num: number, groupSize: number = 3): string {
  if (!Number.isFinite(num)) return String(num); // Обработка NaN и Infinity

  const [integerPart, decimalPart] = Math.abs(num).toString().split('.'); // Разделяем целую и дробную часть
  const sign = num < 0 ? '-' : ''

  const formattedInteger = integerPart.replace(
    new RegExp(`\\B(?=(\\d{${groupSize}})+(?!\\d))`, 'g'),
    ' '
  );

  return sign + formattedInteger + (decimalPart ? '.' + decimalPart : '');
}