import { Config } from '../../configs/app-config'

/**
 * Заменяет {{плейсхолдеры}} в переданном HTML-коде на реальные значения (URL в данном случае).
 *
 * @param {string} html - Исходный HTML-код, содержащий плейсхолдеры (например, `{{logo_url}}`).
 * @param {Record<string, string>} logoUrls - Объект с заменами, где ключ — плейсхолдер, значение — URL.
 * @returns {string} Обновленный HTML-код с заменёнными URL.
 */
export function replaceLogoUrls(html: string, logoUrls: Record<string, string>): string {
  if (!html || Object.keys(logoUrls).length === 0) return html;

  // regex всех переменных. Например - /{{logo_url}}|{{dark_logo_url}}/g
  const regex = new RegExp(Object.keys(logoUrls).join("|"), "g");

  return html.replace(regex, (match) => logoUrls[match] || match);
}

/**
 * Создаёт карту замен для URL логотипов, подставляя плейсхолдеры вместо реальных значений.
 *
 * @param {Config} config - Объект с URL логотипов.
 * @returns {Record<string, string>} Объект, где ключ — URL, значение — его плейсхолдер.
 */
export function prepareReplacedMapForUrls(config: Config): Record<string, string> {
  const replacedMap: Record<string, string> = {}

  if (config?.darkLogoUrl) {
    replacedMap[config?.darkLogoUrl] = '{{dark_logo_url}}'
  }
  if (config?.lightLogoUrl) {
    replacedMap[config?.lightLogoUrl] = '{{light_logo_url}}'
  }
  if (config?.logoUrl) {
    replacedMap[config?.logoUrl] = '{{logo_url}}'
  }

  return replacedMap
}

/**
 * Создаёт карту замен для логотипов, подставляя реальные значения URL вместо плейсхолдеров.
 *
 * @param {Config} config - Объект конфигурации с URL логотипов.
 * @returns {Record<string, string>} Карта замен, где ключ — плейсхолдер, значение — реальный URL.
 */
export function prepareReplacedMapForLogos(config: Config): Record<string, string> {
  const replacedMap: Record<string, string> = {
    '{{dark_logo_url}}': config?.darkLogoUrl || '{{dark_logo_url}}',
    '{{light_logo_url}}': config?.lightLogoUrl || '{{light_logo_url}}',
    '{{logo_url}}': config?.logoUrl || '{{logo_url}}',
  }

  return replacedMap
}
