import {
  replaceLogoUrls,
  prepareReplacedMapForUrls,
  prepareReplacedMapForLogos,
} from './logo'
import { Config } from '../../configs/app-config'

describe('[shared] [helpers] logo', () => {
  describe('replaceLogoUrls', () => {
    it('Должен заменить плейсхолдеры в HTML на реальные URL', () => {
      const html = '<img src="{{logo_url}}" /><img src="{{dark_logo_url}}" />'
      const logoUrls = {
        '{{logo_url}}': 'https://example.com/logo.png',
        '{{dark_logo_url}}': 'https://example.com/dark-logo.png',
      }

      const result = replaceLogoUrls(html, logoUrls)
      expect(result).toBe(
        '<img src="https://example.com/logo.png" /><img src="https://example.com/dark-logo.png" />',
      )
    })
    
    it('Должен заменить все плейсхолдеры в HTML на реальные URL', () => {
      const html = '<img src="{{logo_url}}" /><img src="{{logo_url}}" />'
      const logoUrls = {
        '{{logo_url}}': 'https://example.com/logo.png',
      }

      const result = replaceLogoUrls(html, logoUrls)
      expect(result).toBe(
        '<img src="https://example.com/logo.png" /><img src="https://example.com/logo.png" />',
      )
    })

    it('Не должен изменять HTML, если плейсхолдеров нет', () => {
      const html = '<img src="https://example.com/static-logo.png" />'
      const logoUrls = {
        '{{logo_url}}': 'https://example.com/logo.png',
      }

      const result = replaceLogoUrls(html, logoUrls)
      expect(result).toBe(html)
    })

    it('Не должен ничего менять, если нет значений для замену', () => {
      const html = '<img src="{{logo_url}}" />'
      const result = replaceLogoUrls(html, {})
      expect(result).toBe(html)
    })
  })

  describe('prepareReplacedMapForUrls', () => {
    it('Должен создать объект [что нужно заменить]-[placeholder]', () => {
      const config: Partial<Config> = {
        logoUrl: 'https://example.com/logo.png',
        darkLogoUrl: 'https://example.com/dark-logo.png',
        lightLogoUrl: 'https://example.com/light-logo.png',
      }

      const result = prepareReplacedMapForUrls(config as Config)

      expect(result).toEqual({
        'https://example.com/logo.png': '{{logo_url}}',
        'https://example.com/dark-logo.png': '{{dark_logo_url}}',
        'https://example.com/light-logo.png': '{{light_logo_url}}',
      })
    })

    it('Должен возвращать объект, если URL-ов нет', () => {
      const config = {} as Config
      expect(prepareReplacedMapForUrls(config)).toEqual({})
    })
  })

  describe('prepareReplacedMapForLogos', () => {
    it('Должен создать карту замен переменных на реальные значения', () => {
      const config: Partial<Config> = {
        logoUrl: 'https://example.com/logo.png',
        darkLogoUrl: 'https://example.com/dark-logo.png',
        lightLogoUrl: 'https://example.com/light-logo.png',
      }

      const result = prepareReplacedMapForLogos(config as Config)

      expect(result).toEqual({
        '{{logo_url}}': 'https://example.com/logo.png',
        '{{dark_logo_url}}': 'https://example.com/dark-logo.png',
        '{{light_logo_url}}': 'https://example.com/light-logo.png',
      })
    })

    it('Должен использовать плейсхолдеры, если URL-ов нет', () => {
      const config = {} as Config

      expect(prepareReplacedMapForLogos(config)).toEqual({
        '{{logo_url}}': '{{logo_url}}',
        '{{dark_logo_url}}': '{{dark_logo_url}}',
        '{{light_logo_url}}': '{{light_logo_url}}',
      })
    })
  })
})
