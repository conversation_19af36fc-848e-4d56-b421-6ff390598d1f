export { getCopy } from './getCopy/getCopy'
export { useStrictContext, createStrictContext } from './react/react'
export { getFullName } from './user/user'
export { getRickLevelColor, getIncidentRiskLevelColor } from './risk/risk'
export { replaceLogoUrls, prepareReplacedMapForUrls, prepareReplacedMapForLogos } from './logo/logo'
export { isNumber, formatNumber } from './number/number'
export { combineURLs, getExtensionFromUrl } from './url/url'
export { arrayToRecord, toggleArrayValue } from './array/array'
export {
  truncateString,
  truncateStringInMiddle,
  removeLastSegmentAfterDelimiter,
} from './text/text'
export { getPages } from './pagination/pagination'
export { groupByCategory } from './phishing/phishing'
