import axios from "axios";

export const onFileDownload = async (
    e: React.MouseEvent<HTMLElement>,
    path?: string
  ) => {
    try {
      e.preventDefault();

      if (!path) return;

      const fileName = path.slice(path.lastIndexOf("/") + 1, path.length);

      await axios
        .get(`${path}?timestamp=${new Date().getTime()}`, {
          responseType: "blob",
        })
        .then((res) => {
          const blob = res.data;

          const url = window.URL.createObjectURL(new Blob([blob]));
          const link = document.createElement("a");

          link.href = url;
          link.setAttribute("download", fileName);

          document.body.appendChild(link);

          link.click();

          if (link.parentNode) link.parentNode.removeChild(link);
        });
    } catch (error) {
      console.log(error);
    }
  };