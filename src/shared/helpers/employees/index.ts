import { IListItem } from '@/shared/ui'
import { ECourseStatus, EPhishingCampaignsStatus, ERole } from '@/shared/types/enums'
import { IFilters } from '@/shared/types/filters'
import { Course } from '@/pages/admin/assigned-courses/api'
import { getCopy } from '..'
import { IPhishingCapmaign } from '@/entities/phishing'
import { t } from 'i18next'
import { PRIORITIZED_ROLES } from '@/shared/constants/employees'

// date формата yyyy.mm.dd
export const getTimstamp = (initialDate: string) => {
  const date = initialDate.split('-')

  return +new Date(+date[0], +date[1] - 1, +date[2])
}

// date формата 2023-05-05T16:01:53.497446+00:00
const getTimestampWithFullDate = (initialDate: string) => {
  return +new Date(initialDate)
}

export const getSortedCoursesByStartDate = (courses: Course[]) => {
  return getCopy<typeof courses>(courses).sort(
    (a, b) => +getTimstamp(b.start_date) - +getTimstamp(a.start_date),
  )
}

export const getFilteredAssignedCourses = (courses: Course[] | undefined): IListItem[] => {
  if (!courses) return []

  const sortedCourses = getSortedCoursesByStartDate(courses)

  const coursesByStatus = sortedCourses.reduce<Record<ECourseStatus, Course[]>>(
    (acc, course) => {
      if (!course.status) return acc

      acc[course.status as ECourseStatus].push(course)

      return acc
    },
    {
      [ECourseStatus.active]: [],
      [ECourseStatus.planned]: [],
      [ECourseStatus.complete]: [],
      [ECourseStatus.completed]: [],
    },
  )

  const createCourseListItems = (status: ECourseStatus, title: string): IListItem[] => {
    const courses = coursesByStatus[status]
    return courses.length > 0
      ? [
          {
            id: `${status}Title`,
            title,
            isTitle: true,
            children: courses,
          },
        ]
      : []
  }

  const activeCoursesListItem = createCourseListItems(ECourseStatus.active, t('commons:active'))
  const plannedCoursesListItem = createCourseListItems(ECourseStatus.planned, t('commons:planned'))
  const completedCoursesListItem = createCourseListItems(
    ECourseStatus.complete,
    t('commons:completed'),
  ).concat(createCourseListItems(ECourseStatus.completed, t('commons:completed')))

  return [...activeCoursesListItem, ...plannedCoursesListItem, ...completedCoursesListItem]
}

export const getSortedPhishingCampaignByStartDate = (courses: IPhishingCapmaign[]) => {
  return getCopy<typeof courses>(courses).sort(
    (a, b) => +getTimestampWithFullDate(b.start_date) - +getTimestampWithFullDate(a.start_date),
  )
}

export const getFilteredPhishingCampaigns = (
  phishing: IPhishingCapmaign[] | undefined,
): IListItem[] => {
  if (!phishing) return []

  const sortedPhishing = getSortedPhishingCampaignByStartDate(phishing)

  const phishingByStatus = sortedPhishing.reduce<
    Record<EPhishingCampaignsStatus, IPhishingCapmaign[]>
  >(
    (acc, campaign) => {
      if (!campaign.status) return acc

      acc[campaign.status as EPhishingCampaignsStatus].push(campaign)

      return acc
    },
    {
      [EPhishingCampaignsStatus.active]: [],
      [EPhishingCampaignsStatus.planned]: [],
      [EPhishingCampaignsStatus.completed]: [],
      [EPhishingCampaignsStatus.creating]: [],
      [EPhishingCampaignsStatus.failed]: [],
    },
  )

  const createPhishingListItems = (
    status: EPhishingCampaignsStatus,
    title: string,
  ): IListItem[] => {
    const campaigns = phishingByStatus[status]

    if (campaigns.length === 0) return []

    return [
      {
        id: `${title}Title`,
        title,
        isTitle: true,
        children: campaigns.map(c => ({
          ...c,
          title: c.name,
          isTitle: false,
        })),
      },
    ]
  }

  const activePhishingItems = createPhishingListItems(
    EPhishingCampaignsStatus.active,
    t('commons:active'),
  )
  const plannedPhishingItems = createPhishingListItems(
    EPhishingCampaignsStatus.planned,
    t('commons:planned'),
  )
  const completedPhishingItems = createPhishingListItems(
    EPhishingCampaignsStatus.completed,
    t('commons:completed'),
  )

  return [...activePhishingItems, ...plannedPhishingItems, ...completedPhishingItems]
}

export const getFilterQuery = (filter: IFilters) => {
  const {
    search,
    departments,
    phishing_events,
    role,
    courses,
    courseProgress,
    phishing,
    learning,
    tags,
    riskLevelMin = 0,
    riskLevelMax = 10,
  } = filter

  let query = `?search=${encodeURIComponent(search || '')}`

  query += riskLevelMin === 0 ? '' : `&risk_level_from=${riskLevelMin}`
  query += riskLevelMax === 10 ? '' : `&risk_level_to=${riskLevelMax}`
  query += phishing_events?.length
    ? `&phishing_events=${phishing_events.join(`&phishing_events=`)}`
    : ''
  query += departments?.length ? `&departments=${departments.join(`&departments=`)}` : ''
  query += role ? `&roles=${role}` : ''
  query += courses?.length ? `&in_course=${courses.join(`&in_course=`)}` : ''
  query += courseProgress?.length
    ? `&course_progress=${courseProgress.join(`&course_progress=`)}`
    : ''
  query += phishing?.length ? `&in_phishing=${phishing.join(`&in_phishing=`)}` : ''
  query += learning?.length ? `&learning=${learning.join(`&learning=`)}` : ''
  query += tags?.length ? `&tags=${tags.join(`&tags=`)}` : ''

  return query
}

export const getHighRole = (roles: Nullable<ERole[]>) => {
  if (!roles) return null

  const prioritizedRoles = PRIORITIZED_ROLES.filter(role => roles.includes(role.id))
  const highRole = prioritizedRoles.reduce((max, role) =>
    role.priority > max.priority ? role : max,
  )

  return highRole.id
}
