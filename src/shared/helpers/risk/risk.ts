import { ERiskLevelColor } from "@/shared/types/enums";

/**
 * Функция для получения цвета уровня риска на основе переданного числового значения.
 * 
 * Уровни риска интерпретируются следующим образом:
 * - 0 — Зеленый (ERiskLevelColor.Green).
 * - Значения от 1 до 4 `(не включая 4)` — Зеленый (ERiskLevelColor.Green).
 * - Значения от 4 до 6 — Желтый (ERiskLevelColor.Yellow).
 * - Значения больше или равные 7 — Красный (ERiskLevelColor.Red).
 * 
 * @param {number} [riskLevel] Уровень риска. Если не передан, функция вернет красный цвет.
 * @returns {ERiskLevelColor} Цвет, соответствующий уровню риска.
 */
export const getRickLevelColor = (riskLevel?: Nullable<number>): ERiskLevelColor => {
  if (riskLevel == 0) return ERiskLevelColor.Green;
  if (!riskLevel || riskLevel >= 7) return ERiskLevelColor.Red;
  if (riskLevel < 4) return ERiskLevelColor.Green;

  return ERiskLevelColor.Yellow;
};

/**
 * Функция для получения цвета уровня риска инцидента на основе числового значения.
 * 
 * Уровни риска инцидента интерпретируются следующим образом:
 * - 0-15 — Зеленый (ERiskLevelColor.Green).
 * - 15-50`(не включая 15)` — Желтый (ERiskLevelColor.Yellow).
 * - Более 50 — Красный (ERiskLevelColor.Red).
 * 
 * @param {number} riskLevel Уровень риска инцидента.
 * @returns {ERiskLevelColor} Цвет, соответствующий уровню риска инцидента.
 */
export const getIncidentRiskLevelColor = (
  riskLevel: number
): ERiskLevelColor => {
  if (riskLevel <= 15) return ERiskLevelColor.Green;
  if (riskLevel <= 50) return ERiskLevelColor.Yellow;
  return ERiskLevelColor.Red;
};
