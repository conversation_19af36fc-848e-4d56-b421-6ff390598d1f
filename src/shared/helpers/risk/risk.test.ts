import { getRickLevelColor, getIncidentRiskLevelColor } from './risk'
import { ERiskLevelColor } from '@/shared/types/enums'

describe('[shared] [helpers] risk', () => {
  it('Должен вернуть Green, если riskLevel равен 0', () => {
    expect(getRickLevelColor(0)).toBe(ERiskLevelColor.Green)
  })

  it('Должен вернуть Green, если riskLevel меньше 4', () => {
    expect(getRickLevelColor(1)).toBe(ERiskLevelColor.Green)
    expect(getRickLevelColor(2)).toBe(ERiskLevelColor.Green)
    expect(getRickLevelColor(3)).toBe(ERiskLevelColor.Green)
  })

  it('Должен вернуть Yellow, если riskLevel больше или равен 4, но меньше 7', () => {
    expect(getRickLevelColor(4)).toBe(ERiskLevelColor.Yellow)
    expect(getRickLevelColor(5)).toBe(ERiskLevelColor.Yellow)
    expect(getRickLevelColor(6)).toBe(ERiskLevelColor.Yellow)
  })

  it('Должен вернуть Red, если riskLevel больше или равен 7', () => {
    expect(getRickLevelColor(7)).toBe(ERiskLevelColor.Red)
    expect(getRickLevelColor(10)).toBe(ERiskLevelColor.Red)
    expect(getRickLevelColor(15)).toBe(ERiskLevelColor.Red)
  })

  it('Должен вернуть Red, если riskLevel неопределен или null', () => {
    expect(getRickLevelColor(undefined)).toBe(ERiskLevelColor.Red)
    expect(getRickLevelColor(null)).toBe(ERiskLevelColor.Red)
  })

  it('Должен вернуть Red, если riskLevel не передан', () => {
    expect(getRickLevelColor()).toBe(ERiskLevelColor.Red)
  })
})

describe('getIncidentRiskLevelColor', () => {
  it('Должен вернуть Green, если riskLevel меньше или равно 15', () => {
    expect(getIncidentRiskLevelColor(0)).toBe(ERiskLevelColor.Green)
    expect(getIncidentRiskLevelColor(5)).toBe(ERiskLevelColor.Green)
    expect(getIncidentRiskLevelColor(10)).toBe(ERiskLevelColor.Green)
    expect(getIncidentRiskLevelColor(15)).toBe(ERiskLevelColor.Green)
  })

  it('Должен вернуть Yellow, если riskLevel больше 15, но меньше или равно 50', () => {
    expect(getIncidentRiskLevelColor(16)).toBe(ERiskLevelColor.Yellow)
    expect(getIncidentRiskLevelColor(25)).toBe(ERiskLevelColor.Yellow)
    expect(getIncidentRiskLevelColor(30)).toBe(ERiskLevelColor.Yellow)
    expect(getIncidentRiskLevelColor(50)).toBe(ERiskLevelColor.Yellow)
  })

  it('Должен вернуть Red, если riskLevel больше 50', () => {
    expect(getIncidentRiskLevelColor(51)).toBe(ERiskLevelColor.Red)
    expect(getIncidentRiskLevelColor(100)).toBe(ERiskLevelColor.Red)
    expect(getIncidentRiskLevelColor(150)).toBe(ERiskLevelColor.Red)
  })

  it('Должен вернуть Green, если riskLevel отрицательное число', () => {
    expect(getIncidentRiskLevelColor(-1)).toBe(ERiskLevelColor.Green)
    expect(getIncidentRiskLevelColor(-5)).toBe(ERiskLevelColor.Green)
    expect(getIncidentRiskLevelColor(-10)).toBe(ERiskLevelColor.Green)
  })

  it('Должен правильно обрабатывать пограничные значения для Yellow (16 и 50)', () => {
    expect(getIncidentRiskLevelColor(16)).toBe(ERiskLevelColor.Yellow)
    expect(getIncidentRiskLevelColor(50)).toBe(ERiskLevelColor.Yellow)
  })

  it('Должен правильно обрабатывать пограничные значения для Red (51)', () => {
    expect(getIncidentRiskLevelColor(51)).toBe(ERiskLevelColor.Red)
  })
})
