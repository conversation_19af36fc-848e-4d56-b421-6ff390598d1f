import { useMemo } from 'react'
import { ERole } from '../../types/enums'
import {
  adminPermissions,
  contentManagerPermissions,
  emptyPermissions,
  operatorPermissions,
  superUserPermissions,
  userPermissions,
} from '../../constants/permissions'
import { IPermissions } from 'entities/employee'
/**
 * Хук для получения прав доступа в зависимости от роли пользователя.
 * Этот хук использует роль пользователя, переданную в качестве аргумента, и возвращает соответствующие права доступа к страницам.
 * Все права для различных ролей определяются в константах `adminPermissions`, `contentManagerPermissions`, 
 * `operatorPermissions`, `superUserPermissions`, `userPermissions`, и `emptyPermissions`.
 *
 * Роли, которые поддерживаются:
 * - `ERole.employee`: Права для обычного пользователя (userPermissions).
 * - `ERole.content_manager`: Права для контент-менеджера (contentManagerPermissions).
 * - `ERole.operator`: Права для оператора (operatorPermissions).
 * - `ERole.super_user`: Права для суперпользователя (superUserPermissions).
 * - `ERole.system_admin`: Права для системного администратора (adminPermissions).
 * - Если роль не задана (null), возвращаются пустые права (emptyPermissions).
 *
 * @param {ERole | null} role - Роль пользователя, для которой необходимо получить права. Может быть одной из значений:
 *  - `ERole.employee`
 *  - `ERole.content_manager`
 *  - `ERole.operator`
 *  - `ERole.super_user`
 *  - `ERole.system_admin`
 *  - `null` для отсутствия роли.
 *
 * @returns {IPermissions} Объект с правами доступа, соответствующими переданной роли. Если роль не указана, возвращаются пустые права.
 */
export const useUserPermissions = (role: ERole | null): IPermissions => {
  return useMemo(() => {
    switch (role) {
      case ERole.employee:
        return userPermissions
      case ERole.content_manager:
        return contentManagerPermissions
      case ERole.operator:
        return operatorPermissions
      case ERole.super_user:
        return superUserPermissions
      case ERole.system_admin:
        return adminPermissions
      default:
        return emptyPermissions
    }
  }, [role])
}
