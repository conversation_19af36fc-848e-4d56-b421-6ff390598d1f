import { renderHook } from '@testing-library/react-hooks'
import { useUserPermissions } from './use-user-permissions'
import { ERole } from '../../types/enums'
import {
  adminPermissions,
  contentManagerPermissions,
  emptyPermissions,
  operatorPermissions,
  superUserPermissions,
  userPermissions,
} from '../../constants/permissions'

describe('[shared] [hooks] useUserPermissions', () => {
  it('Должен вернуть права для роли employee', () => {
    const { result } = renderHook(() => useUserPermissions(ERole.employee))

    expect(result.current).toEqual(userPermissions)
  })

  it('Должен вернуть права для роли content_manager', () => {
    const { result } = renderHook(() => useUserPermissions(ERole.content_manager))

    expect(result.current).toEqual(contentManagerPermissions)
  })

  it('Должен вернуть права для роли operator', () => {
    const { result } = renderHook(() => useUserPermissions(ERole.operator))

    expect(result.current).toEqual(operatorPermissions)
  })

  it('Должен вернуть права для роли super_user', () => {
    const { result } = renderHook(() => useUserPermissions(ERole.super_user))

    expect(result.current).toEqual(superUserPermissions)
  })

  it('Должен вернуть права для роли system_admin', () => {
    const { result } = renderHook(() => useUserPermissions(ERole.system_admin))

    expect(result.current).toEqual(adminPermissions)
  })

  it('Должен вернуть пустые права при отсутствии роли (null)', () => {
    const { result } = renderHook(() => useUserPermissions(null))

    expect(result.current).toEqual(emptyPermissions)
  })

  it('Должен корректно обновлять права при изменении роли', () => {
    const { result, rerender } = renderHook(({ role }) => useUserPermissions(role), {
      initialProps: { role: ERole.employee },
    })

    expect(result.current).toEqual(userPermissions)

    rerender({ role: ERole.operator })
    expect(result.current).toEqual(operatorPermissions)

    rerender({ role: ERole.super_user })
    expect(result.current).toEqual(superUserPermissions)
  })
})
