/* eslint-disable @typescript-eslint/no-explicit-any */
import { useLayoutEffect, useState } from 'react'
import { Config, BaseConfig } from '@/shared/configs/app-config'

/**
 * Кастомный React-хук `useConfig` для загрузки конфигурации приложения.
 *
 * Загружает конфигурацию из элемента `<script id="config">` в HTML-документе
 * и преобразует ее в объект `Config`. Также учитывает вложенные настройки `config_json`.
 * Достался по наследнию со временен Angular-React. Делается для того, чтобы
 * мини-бек на фронте прокидывал через html переменные с бека, которые можем использовать на фронте
 * Например - цвета темы
 *
 * @returns {Config} Конфигурационный объект с параметрами приложения.
 *
 * @example
 * const config = useConfig();
 * console.log(config.apiUrl); // Выведет URL API из конфигурации
 */
export const useConfig = () => {
  const [config, setConfig] = useState<Config>({
    useSSO: false,
    logoUrl: '',
    apiUrl: '',
    apiHost: '',
    forse_saml: false,
    eventKeeperUrl: '',
    publicUrl: '/lk',
    lang: 'ru',
    analytics_enabled: false,
    needPrivacyPolicyPage: true,
    needAgreementPage: true,
    theme: {},
    featureFlags: {},
    use_admin_ad: false,
    use_sso: false,
    title: '',
  })

  useLayoutEffect(() => {
    let configRaw: any
    let nestedConfig: any

    try {
      configRaw = JSON.parse(document.getElementById('config')?.textContent ?? '{}')
      nestedConfig = JSON.parse(configRaw.config_json ?? '{}')
    } catch (error) {
      console.error('Configuration parsing error:', error)
    }

    const baseConfig = new BaseConfig(configRaw)

    setConfig(prevConfig => ({
      ...prevConfig,
      title: configRaw?.title || '',
      useSSO: !!baseConfig.useSSO,
      use_sso: !!baseConfig?.configRaw?.use_sso,
      logoUrl: baseConfig.logoUrl,
      darkLogoUrl: baseConfig?.darkLogoUrl,
      lightLogoUrl: baseConfig?.lightLogoUrl,
      apiUrl: baseConfig.apiUrl,
      apiHost: baseConfig.apiHost,
      eventKeeperUrl: baseConfig.eventKeeperUrl,
      publicUrl: configRaw?.publicUrl || nestedConfig?.publicUrl,
      url: configRaw?.url || nestedConfig?.url,
      lang: baseConfig.lang,
      analytics_enabled: baseConfig.analytics_enabled,
      yandex_metrika_id: baseConfig.yandex_metrika_id,
      theme: baseConfig.theme,
      featureFlags: baseConfig.featureFlags,
      forse_saml: !!baseConfig?.forse_saml,
      needPrivacyPolicyPage: configRaw?.needPrivacyPolicyPage ?? baseConfig?.needPrivacyPolicyPage,
      needAgreementPage: configRaw?.needAgreementPage ?? baseConfig?.needAgreementPage,
      use_admin_ad: !!baseConfig?.use_admin_ad,
      use_admin_sso: !!baseConfig?.use_admin_sso,
    }))
  }, [])

  return config
}
