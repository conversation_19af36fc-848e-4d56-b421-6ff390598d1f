import { renderHook } from '@testing-library/react'
import { useConfig } from './use-config'

describe('useConfig', () => {
  beforeEach(() => {
    document.body.innerHTML = '' // Чистим DOM перед каждым тестом
  })

  it('Должен инициализировать config с дефолтными значениями', async () => {
    const { result } = renderHook(() => useConfig())

    expect(result.current).toEqual({
      useSSO: false,
      logoUrl: undefined,
      apiUrl: undefined,
      apiHost: undefined,
      forse_saml: false,
      eventKeeperUrl: undefined,
      publicUrl: undefined,
      lang: 'ru',
      analytics_enabled: undefined,
      needPrivacyPolicyPage: undefined,
      needAgreementPage: undefined,
      theme: undefined,
      featureFlags: {},
      use_admin_ad: false,
      use_sso: false,
      title: '',
      darkLogoUrl: undefined,
      lightLogoUrl: undefined,
      url: undefined,
      yandex_metrika_id: undefined,
      use_admin_sso: false,
    })
  })

  it('Должен корректно парсить JSON из #config', () => {
    document.body.innerHTML = `
      <script id="config" type="application/json">
        {
          "title": "Test App",
          "apiUrl": "https://api.example.com",
          "useSSO": true,
          "config_json": "{ \\"publicUrl\\": \\"/dashboard\\" }"
        }
      </script>
    `

    const { result } = renderHook(() => useConfig())

    expect(result.current.title).toBe('Test App')
    expect(result.current.apiUrl).toBe('https://api.example.com')
    expect(result.current.useSSO).toBe(true)
    expect(result.current.publicUrl).toBe('/dashboard')
  })

  it('Должен корректно обработать пустой или некорректный JSON', () => {
    document.body.innerHTML = `
      <script id="config" type="application/json">
        { invalid json }
      </script>
    `

    const { result } = renderHook(() => useConfig())

    // Должен остаться дефолтный `config`, так как JSON сломан
    expect(result.current.publicUrl).toBeUndefined()
    expect(result.current.apiUrl).toBeUndefined()
  })

  it('Должен корректно работать без #config', () => {
    const { result } = renderHook(() => useConfig())

    expect(result.current).toMatchObject({
      apiUrl: undefined,
      publicUrl: undefined,
    })
  })
})
