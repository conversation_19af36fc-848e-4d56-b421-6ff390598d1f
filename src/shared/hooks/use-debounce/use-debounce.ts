/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, EffectCallback, DependencyList, useRef } from 'react'

/**
 * Хук для задержки обновления значения.
 *
 * Этот хук использует `useState` и `useEffect` для того, чтобы обновить значение
 * только после того, как пройдет указанное количество времени с момента его изменения.
 *
 * @template T Тип значения, которое будет подвергаться дебаунсу.
 * @param {T} value Значение, которое будет дебаунситься.
 * @param {number} delay Задержка в миллисекундах, после которой значение обновляется.
 * @returns {T} Значение после дебаунса.
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Хук для дебаунса функции. Функция будет вызываться только после задержки.
 *
 * Этот хук возвращает функцию, которая будет вызываться только после задержки
 * после последнего вызова. Подходит для дебаунса событий.
 *
 * @template T Тип передаваемой функции.
 * @param {T} f Функция, которая будет подвергаться дебаунсу.
 * @param {number} delay Задержка в миллисекундах перед вызовом функции.
 * @returns {T} Функция с дебаунсом.
 */
type FunctionType = (...args: any[]) => void

export function useFunctionDebounce<T extends FunctionType>(f: T, delay: number): T {
  const timer = useRef<NodeJS.Timeout | null>(null)

  const debounceFunc = (...args: Parameters<T>) => {
    if (timer.current) clearTimeout(timer.current)

    timer.current = setTimeout(() => {
      f(...args)
    }, delay)
  }

  return debounceFunc as T
}

/**
 * Хук для дебаунса с useEffect и зависимостями.
 *
 * Этот хук позволяет задать побочный эффект (например, API-запрос) с задержкой.
 * Он будет вызываться после указанной задержки и после того, как изменятся зависимости.
 *
 * @param {EffectCallback} effect Побочный эффект, который будет выполняться после задержки.
 * @param {number} delay Задержка в миллисекундах перед выполнением эффекта.
 * @param {DependencyList} [deps] Список зависимостей, при изменении которых будет вызываться эффект.
 */
export function useDebounceEffect(
  effect: EffectCallback,
  delay: number,
  deps?: DependencyList,
): void {
  const timer = useRef<NodeJS.Timeout | number | null>(null)

  const debounceFunc = () => {
    if (timer.current) clearTimeout(timer.current)

    timer.current = setTimeout(() => {
      effect()
    }, delay)
  }

  useEffect(() => {
    debounceFunc()

    return () => {
      if (timer.current) clearTimeout(timer.current)
    }
  }, deps)
}
