import { renderHook, act } from '@testing-library/react'
import { useDebounce, useDebounceEffect, useFunctionDebounce } from './use-debounce' // Путь к твоему хуку

jest.useFakeTimers()

describe('[shared] [hooks] use-debounce', () => {
  describe('useDebounce', () => {
    it('Должен правильно дебаунсить значение', () => {
      const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
        initialProps: { value: 'test', delay: 500 },
      })

      expect(result.current).toBe('test')

      rerender({ value: 'test2', delay: 500 })
      expect(result.current).toBe('test')

      act(() => {
        jest.advanceTimersByTime(500)
      })

      expect(result.current).toBe('test2')
    })

    it('Должен работать с разными значениями задержки', () => {
      const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
        initialProps: { value: 'test', delay: 1000 },
      })

      expect(result.current).toBe('test')

      rerender({ value: 'test2', delay: 1000 })
      expect(result.current).toBe('test')

      act(() => {
        jest.advanceTimersByTime(1000)
      })

      expect(result.current).toBe('test2')
    })

    it('Должен иметь тоже значение, если оно не изменилось', () => {
      const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
        initialProps: { value: 'test', delay: 500 },
      })

      expect(result.current).toBe('test')

      act(() => {
        jest.advanceTimersByTime(500)
      })

      expect(result.current).toBe('test') // Значение не должно измениться
    })

    it('Должен очищать таймеры при размонтировании', () => {
      // Шпионим за clearTimeout
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout')

      const { unmount } = renderHook(({ value, delay }) => useDebounce(value, delay), {
        initialProps: { value: 'test', delay: 500 },
      })

      unmount()

      // Проверяем, что clearTimeout был вызван
      expect(clearTimeoutSpy).toHaveBeenCalled()

      // Отчистка шпиона
      clearTimeoutSpy.mockRestore()
    })
  })

  describe('useDebounceEffect', () => {
    it('Должен вызывать effect после задержки', () => {
      const effect = jest.fn()
      renderHook(({ delay }) => useDebounceEffect(effect, delay), {
        initialProps: { delay: 500 },
      })

      // Эффект не должен быть вызван сразу
      expect(effect).not.toHaveBeenCalled()

      // Крутим время
      act(() => {
        jest.advanceTimersByTime(500)
      })

      // Эффект должен быть вызван после задержки
      expect(effect).toHaveBeenCalledTimes(1)
    })

    it('Должен вызывать effect только после завершения задержки', () => {
      const effect = jest.fn()
      const { rerender } = renderHook(({ delay }) => useDebounceEffect(effect, delay), {
        initialProps: { delay: 500 },
      })

      // Ререндер и изменение пропсов
      rerender({ delay: 1000 })

      act(() => {
        jest.advanceTimersByTime(500) // Эффект еще не должен быть вызван
      })

      expect(effect).not.toHaveBeenCalled()

      act(() => {
        jest.advanceTimersByTime(500) // Эффект должен быть вызван после новой задержки
      })

      expect(effect).toHaveBeenCalledTimes(1)
    })

    it('Должен очищать таймер при размонтировании', () => {
      // Шпион на clearTimeout
      const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout')

      const effect = jest.fn()
      const { unmount } = renderHook(({ delay }) => useDebounceEffect(effect, delay), {
        initialProps: { delay: 500 },
      })

      // Таймер должен быть установлен, но еще не должен сработать
      act(() => {
        jest.advanceTimersByTime(200) // Таймер не сработал, но он был активирован
      })

      // Размонтируем компонент
      unmount()

      // Теперь проверим, что clearTimeout был вызван
      expect(clearTimeoutSpy).toHaveBeenCalled()

      // Очистка шпиона
      clearTimeoutSpy.mockRestore()
    })

    it('Должен не вызывать effect при изменении зависимостей, если задержка не прошла', () => {
      const effect = jest.fn()
      const { rerender } = renderHook(({ delay, deps }) => useDebounceEffect(effect, delay, deps), {
        initialProps: { delay: 500, deps: [1] },
      })

      // Убедимся, что эффект еще не был вызван
      expect(effect).not.toHaveBeenCalled()

      // Меняем зависимость
      rerender({ delay: 500, deps: [2] })

      // Крутим таймер на 200 мс (эффект еще не должен быть вызван)
      act(() => {
        jest.advanceTimersByTime(200)
      })

      // Проверяем, что эффект не был вызван
      expect(effect).not.toHaveBeenCalled()

      // Крутим таймер еще на 299 мс (за 1 до)
      act(() => {
        jest.advanceTimersByTime(299)
      })

      // Проверяем, что эффект все еще не был вызван
      expect(effect).not.toHaveBeenCalled()

      // Крутим таймер на оставшиеся 1 мс (полная задержка)
      act(() => {
        jest.advanceTimersByTime(1)
      })

      // Теперь эффект должен быть вызван, так как прошла полная задержка
      expect(effect).toHaveBeenCalledTimes(1)
    })
  })

  describe('useFunctionDebounce', () => {
    it('Должен вызывать функцию после задержки', () => {
      const mockFn = jest.fn()
      const { result } = renderHook(() => useFunctionDebounce(mockFn, 500))

      // Вызываем функцию несколько раз
      act(() => {
        result.current('test1')
        result.current('test2')
        result.current('test3')
      })

      // Проверяем, что функция не была вызвана сразу
      expect(mockFn).not.toHaveBeenCalled()

      // Круим таймер на 500 мс
      act(() => {
        jest.advanceTimersByTime(500)
      })

      // Функция должна быть вызвана один раз, и только с последними аргументами
      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('test3')
    })

    it('Должен очищать таймер при новом вызове', () => {
      const mockFn = jest.fn()
      const { result } = renderHook(() => useFunctionDebounce(mockFn, 500))

      // Вызываем функцию с аргументом 'test1'
      act(() => {
        result.current('test1')
      })

      // Крутим таймер на 200 мс
      act(() => {
        jest.advanceTimersByTime(200)
      })

      // Вызываем функцию снова с аргументом 'test2'
      act(() => {
        result.current('test2')
      })

      // Крутим таймер на 499 мс (почти до конца)
      act(() => {
        jest.advanceTimersByTime(499)
      })

      // Проверяем, что функция еще не была вызвана, так как задержка еще не прошла
      expect(mockFn).not.toHaveBeenCalled()

      // Крутим таймер на оставшийся 1 мс (для того, чтобы завершить задержку)
      act(() => {
        jest.advanceTimersByTime(1)
      })

      // Проверяем, что функция была вызвана только один раз и с аргументом 'test2'
      expect(mockFn).toHaveBeenCalledTimes(1)
      expect(mockFn).toHaveBeenCalledWith('test2')
    })
  })
})
