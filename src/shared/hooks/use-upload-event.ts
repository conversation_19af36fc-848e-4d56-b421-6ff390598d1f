import { EventData } from '@/entities/themeCourse/model/types'
import { useNotification } from '../contexts/notifications'
import { eventApi } from '@/entities/themeCourse/model/api/event'
import { useTranslation } from 'react-i18next'
import { v4 as uuid } from 'uuid'
import { SetStateAction } from 'react'
import { EVENT_JSON_UPLOAD_KEY, EVENT_STATUS_LS_KEY } from '../components/theme-content/const'

const TIMEOUT = 5000
const TRANSLATION_KEY = 'hooks__use-upload-event'

let INTERVAL_ID: NodeJS.Timeout

type HandleUploadProps = {
  eventId: UUID
  stepId: UUID
}

type HandleEventProps = {
  data: EventData
  stepId: UUID
}

type Props = {
  setStatus: React.Dispatch<SetStateAction<'initial' | 'created' | 'completed' | 'failed'>>
}

export const useUploadEvent = ({ setStatus }: Props) => {
  const { add, handleErrorResponse } = useNotification()
  const { t } = useTranslation(TRANSLATION_KEY)
  const [getEventData] = eventApi.useLazyGetEventDataQuery()

  const handleEvent = ({ data, stepId }: HandleEventProps) => {
    const statuses = localStorage.getItem(EVENT_STATUS_LS_KEY)
    if (!statuses) {
      localStorage.setItem(
        EVENT_STATUS_LS_KEY,
        JSON.stringify({
          [stepId]: data.status,
        }),
      )
    } else {
      const jsonStatuses = JSON.parse(statuses)
      jsonStatuses[stepId] = data.status
      localStorage.setItem(EVENT_STATUS_LS_KEY, JSON.stringify(jsonStatuses))
    }
    if (data.status === 'created') return
    if (data.status === 'completed') {
      add({
        message: t('file_upload_success'),
        status: 'success',
        id: uuid(),
      })
    } else if (data.status === 'failed') {
      handleErrorResponse(data.error)
    }
    setStatus(data.status)
    clearInterval(INTERVAL_ID)
  }

  const intervalCb = async (eventId: UUID, stepId: UUID) => {
    const data = await getEventData(eventId).unwrap()
    handleEvent({
      data: data,
      stepId: stepId,
    })
  }

  const handleUpload = ({ eventId, stepId }: HandleUploadProps) => {
    const statuses = localStorage.getItem(EVENT_STATUS_LS_KEY)
    if (!statuses) {
      localStorage.setItem(
        EVENT_STATUS_LS_KEY,
        JSON.stringify({
          [stepId]: 'created',
        }),
      )
    } else {
      const jsonStatuses = JSON.parse(statuses)
      jsonStatuses[stepId] = 'created'
      localStorage.setItem(EVENT_STATUS_LS_KEY, JSON.stringify(jsonStatuses))
    }
    setStatus('created')
    INTERVAL_ID = setInterval(() => intervalCb(eventId, stepId), TIMEOUT)
  }

  const handleJsonUpload = () => {
    localStorage.setItem(EVENT_JSON_UPLOAD_KEY, 'uploading')

    window.addEventListener('beforeunload', () => {
      localStorage.removeItem(EVENT_JSON_UPLOAD_KEY)
    })
  }

  return {
    handleUpload,
    handleJsonUpload,
  }
}
