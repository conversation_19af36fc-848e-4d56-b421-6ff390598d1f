/**
 * Кастомный хук `useDownload` для загрузки файлов по URL.
 *
 * @returns {[Function]} - Возвращает массив с одной функцией `download`,
 *                         которая загружает файл по указанному URL.
 *
 * @example
 * const [download] = useDownload();
 * download("https://example.com/file.pdf", "example.pdf");
 */
export const useDownload = () => {
  /**
   * Загружает файл по указанному URL, создавая временный `a`-тег.
   *
   * @param {string} url - Ссылка на загружаемый файл.
   * @param {string} [name=""] - Имя файла (по умолчанию пустая строка).
   *
   * @example
   * download("https://example.com/image.jpg", "photo.jpg");
   */
  const download = (url: string, name: string = '') => {
    const a = document.createElement('a')

    a.style.display = 'none'
    a.href = url
    a.download = name
    a.target = '_blank'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }

  return [download]
}
