import { renderHook } from '@testing-library/react'
import { useDownload } from './use-download'

describe('useDownload хук', () => {
  it('Должен создать ссылку и вызвать клик', () => {
    const { result } = renderHook(() => useDownload())
    const download = result.current[0]

    document.body.innerHTML = ''

    const url = 'https://example.com/file.pdf'
    const fileName = 'test-file.pdf'

    const anchorElement = document.createElement('a') // Создаём реальный элемент
    const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(anchorElement)
    const appendChildSpy = jest.spyOn(document.body, 'appendChild')
    const removeChildSpy = jest.spyOn(document.body, 'removeChild')

    const clickMock = jest.spyOn(anchorElement, 'click')

    download(url, fileName)

    expect(createElementSpy).toHaveBeenCalledWith('a')
    expect(appendChildSpy).toHaveBeenCalledWith(anchorElement)
    expect(clickMock).toHaveBeenCalled()

    // Проверяем, есть ли элемент в body перед удалением
    if (document.body.contains(anchorElement)) {
      expect(removeChildSpy).toHaveBeenCalledWith(anchorElement)
    }

    createElementSpy.mockRestore()
    appendChildSpy.mockRestore()
    removeChildSpy.mockRestore()
  })

  it('Должен корректно устанавливать атрибуты ссылки', () => {
    const { result } = renderHook(() => useDownload())
    const download = result.current[0]

    const url = 'https://example.com/image.png'
    const fileName = 'image.png'

    const anchorElement = document.createElement('a')
    jest.spyOn(document, 'createElement').mockReturnValue(anchorElement)
    const clickMock = jest.spyOn(anchorElement, 'click')

    download(url, fileName)

    expect(anchorElement.href).toBe(url)
    expect(anchorElement.download).toBe(fileName)
    expect(anchorElement.target).toBe('_blank')
    expect(clickMock).toHaveBeenCalled()

    // Проверяем, есть ли элемент в body перед удалением
    if (document.body.contains(anchorElement)) {
      document.body.removeChild(anchorElement)
    }
  })
})
