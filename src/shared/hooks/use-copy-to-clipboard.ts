import { useNotification } from '@/shared/contexts/notifications'
import { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuid } from 'uuid'

type UseCopyToClipboardProps = {
  copyValue: string
  notifyText?: string
}

export const useCopyToClipboard = (props: UseCopyToClipboardProps) => {
  const { add } = useNotification()
  const { t } = useTranslation()
  const { copyValue, notifyText = t('commons:success_copy') } = props

  const copyToClipboard = useCallback(() => {
    const copy = async () => {
      await navigator.clipboard.writeText(copyValue)
    }

    copy()
      .then(() => add({ id: uuid(), message: notifyText, status: 'success' }))
      .catch(() => add({ id: uuid(), message: t('commons:copy_failed'), status: 'error' }))
  }, [add, copyValue, notifyText, t])

  return { copyToClipboard }
}
