import { renderHook, act } from "@testing-library/react";
import { useEvent } from "./use-event";

describe("[shared] [hooks] useEvent", () => {
  it("Должен подписываться и отписываться от события окна", () => {
    const handler = jest.fn();
    const { unmount } = renderHook(() => useEvent("resize", handler));

    act(() => {
      window.dispatchEvent(new Event("resize"));
    });

    expect(handler).toHaveBeenCalledTimes(1);

    // Проверяем отписку
    unmount();
    act(() => {
      window.dispatchEvent(new Event("resize"));
    });
    expect(handler).toHaveBeenCalledTimes(1);
  });

  it("Должен подписываться и отписываться от события элемента", () => {
    const handler = jest.fn();
    const div = document.createElement("div");

    const { unmount } = renderHook(() => useEvent("click", handler, div));

    act(() => {
      div.dispatchEvent(new MouseEvent("click"));
    });

    expect(handler).toHaveBeenCalledTimes(1);

    // Проверяем отписку
    unmount();
    act(() => {
      div.dispatchEvent(new MouseEvent("click"));
    });

    expect(handler).toHaveBeenCalledTimes(1);
  });

  it("Не должен подписываться, если `target` равен `null`", () => {
    const handler = jest.fn();
    renderHook(() => useEvent("keydown", handler, null));

    act(() => {
      window.dispatchEvent(new KeyboardEvent("keydown", { key: "Enter" }));
    });

    expect(handler).not.toHaveBeenCalled();
  });

  it("Должен корректно обновлять обработчик при изменении `handler`", () => {
    const handler1 = jest.fn();
    const handler2 = jest.fn();

    const { rerender } = renderHook(
      ({ handler }) => useEvent("scroll", handler),
      { initialProps: { handler: handler1 } }
    );

    act(() => {
      window.dispatchEvent(new Event("scroll"));
    });

    expect(handler1).toHaveBeenCalledTimes(1);
    expect(handler2).not.toHaveBeenCalled();

    // Обновляем обработчик
    rerender({ handler: handler2 });

    act(() => {
      window.dispatchEvent(new Event("scroll"));
    });

    expect(handler1).toHaveBeenCalledTimes(1);
    expect(handler2).toHaveBeenCalledTimes(1);
  });

  it("Должен корректно обновлять `target` во время работы", () => {
    const handler = jest.fn();
    const div1 = document.createElement("div");
    const div2 = document.createElement("div");

    const { rerender } = renderHook(
      ({ target }) => useEvent("click", handler, target),
      { initialProps: { target: div1 } }
    );

    act(() => {
      div1.dispatchEvent(new MouseEvent("click"));
    });

    expect(handler).toHaveBeenCalledTimes(1);

    // Обновляем target
    rerender({ target: div2 });

    act(() => {
      div1.dispatchEvent(new MouseEvent("click"));
    });

    expect(handler).toHaveBeenCalledTimes(1);

    act(() => {
      div2.dispatchEvent(new MouseEvent("click"));
    });

    expect(handler).toHaveBeenCalledTimes(2);
  });

  it("Должен корректно подписываться на разные события", () => {
    const handlerClick = jest.fn();
    const handlerKeyDown = jest.fn();

    renderHook(() => {
      useEvent("click", handlerClick);
      useEvent("keydown", handlerKeyDown);
    });

    act(() => {
      window.dispatchEvent(new MouseEvent("click"));
    });

    expect(handlerClick).toHaveBeenCalledTimes(1);
    expect(handlerKeyDown).not.toHaveBeenCalled();

    act(() => {
      window.dispatchEvent(new KeyboardEvent("keydown", { key: "A" }));
    });

    expect(handlerClick).toHaveBeenCalledTimes(1);
    expect(handlerKeyDown).toHaveBeenCalledTimes(1);
  });

  it("Не должен пересоздавать подписку, если `handler` и `eventName` не менялись", () => {
    const handler = jest.fn();
    const addListenerSpy = jest.spyOn(window, "addEventListener");
    const removeListenerSpy = jest.spyOn(window, "removeEventListener");

    const { rerender } = renderHook(() => useEvent("focus", handler));

    // Повторный ререндер с теми же параметрами
    rerender();
    rerender();
    rerender();

    expect(addListenerSpy).toHaveBeenCalledTimes(1);
    expect(removeListenerSpy).toHaveBeenCalledTimes(0);

    addListenerSpy.mockRestore();
    removeListenerSpy.mockRestore();
  });

  it("Должен корректно отписываться при изменении `eventName`", () => {
    const handler = jest.fn();
    const addListenerSpy = jest.spyOn(window, "addEventListener");
    const removeListenerSpy = jest.spyOn(window, "removeEventListener");

    const { rerender } = renderHook(
        ({ eventName }: { eventName: keyof WindowEventMap }) => useEvent(eventName, handler),
        {
          initialProps: { eventName: "focus" as keyof WindowEventMap },
        }
      );

    act(() => {
      window.dispatchEvent(new FocusEvent("focus"));
    });

    expect(handler).toHaveBeenCalledTimes(1);

    // Меняем тип события
    rerender({ eventName: "blur" });

    act(() => {
      window.dispatchEvent(new FocusEvent("focus"));
    });

    expect(handler).toHaveBeenCalledTimes(1);

    act(() => {
      window.dispatchEvent(new FocusEvent("blur"));
    });

    expect(handler).toHaveBeenCalledTimes(2);

    expect(addListenerSpy).toHaveBeenCalledTimes(2);
    expect(removeListenerSpy).toHaveBeenCalledTimes(1);

    addListenerSpy.mockRestore();
    removeListenerSpy.mockRestore();
  });
});
