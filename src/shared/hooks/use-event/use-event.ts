import { useEffect, useRef } from "react";

/**
 * Хук `useEvent` привязывает обработчик события к заданному элементу (по умолчанию `window`).
 *
 * @template K - Тип события из `WindowEventMap`.
 * @param {K} eventName - Имя события, например, `"resize"` или `"click"`.
 * @param {(event: WindowEventMap[K]) => void} handler - Функция-обработчик события.
 * @param {Window | Document | HTMLElement | null} [target=window] - Цель события (по умолчанию `window`).
 *
 * @example
 * useEvent("resize", (event) => console.log("Окно изменилось", event));
 */
export const useEvent = <K extends keyof WindowEventMap>(
    eventName: K,
    handler: (event: WindowEventMap[K]) => void,
    target: Window | Document | HTMLElement | null = window,
  ): void => {
    const savedHandler = useRef(handler);
  
    // Обновляем ref при каждом ререндере (но не пересоздаём обработчик)
    useEffect(() => {
      savedHandler.current = handler;
    }, [handler]);

    useEffect(() => {
      if (!target) return;

      const eventListener = (event: WindowEventMap[K]) => savedHandler.current(event);
      target.addEventListener(eventName, eventListener as EventListener);
  
      return () => {
        target.removeEventListener(eventName, eventListener as EventListener);
      };
    }, [eventName, handler, target]);
  };
  