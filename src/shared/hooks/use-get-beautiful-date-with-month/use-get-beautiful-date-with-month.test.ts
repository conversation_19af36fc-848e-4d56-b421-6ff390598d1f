import { renderHook } from '@testing-library/react-hooks'
import { useGetBeautifulDateWithMonth } from './use-get-beautiful-date-with-month'
import { addI18nResources, initI18n } from '@/shared/helpers/tests/i18n'
import i18n from '@/shared/configs/i18n'

beforeAll(() => {
  initI18n()
})

describe('[shared] [hooks] useGetBeautifulDateWithMonth', () => {
  beforeAll(() => {
    addI18nResources(
      {
        january: 'Январь',
        february: 'Февраль',
        march: 'Март',
        april: 'Апрель',
        may: 'Май',
        june: 'Июнь',
        july: 'Июль',
        august: 'Август',
        september: 'Сентябрь',
        october: 'Октябрь',
        november: 'Ноябрь',
        december: 'Декабрь',
      },
      { lang: 'ru' },
    )
    addI18nResources(
      {
        january: 'January',
        february: 'February',
        march: 'March',
        april: 'April',
        may: 'May',
        june: 'June',
        july: 'July',
        august: 'August',
        september: 'September',
        october: 'October',
        november: 'November',
        december: 'December',
      },
      { lang: 'en' },
    )
    
    i18n.changeLanguage('ru')
  })

  it('Должен правильно форматировать дату в формате dd MMM yyyy', () => {
    const { result } = renderHook(() => useGetBeautifulDateWithMonth('2025-02-12'))

    expect(result.current).toBe('12 фев 2025')
  })

  it('Должен правильно обрабатывать даты для всех месяцев', () => {
    const months = [
      { date: '2025-01-15', nameRU: 'Январь', nameEN: 'January' },
      { date: '2025-02-15', nameRU: 'Февраль', nameEN: 'February' },
      { date: '2025-03-15', nameRU: 'Март', nameEN: 'March' },
      { date: '2025-04-15', nameRU: 'Апрель', nameEN: 'April' },
      { date: '2025-05-15', nameRU: 'Май', nameEN: 'May' },
      { date: '2025-06-15', nameRU: 'Июнь', nameEN: 'June' },
      { date: '2025-07-15', nameRU: 'Июль', nameEN: 'July' },
      { date: '2025-08-15', nameRU: 'Август', nameEN: 'August' },
      { date: '2025-09-15', nameRU: 'Сентябрь', nameEN: 'September' },
      { date: '2025-10-15', nameRU: 'Октябрь', nameEN: 'October' },
      { date: '2025-11-15', nameRU: 'Ноябрь', nameEN: 'November' },
      { date: '2025-12-15', nameRU: 'Декабрь', nameEN: 'December' },
    ]

    months.forEach(({ date, nameRU }) => {
      const { result } = renderHook(() => useGetBeautifulDateWithMonth(date))

      const monthAbbr = nameRU.toLowerCase().slice(0, 3)

      const expectedDate = `${new Date(date).getDate()} ${monthAbbr} ${new Date(date).getFullYear()}`
      expect(result.current).toBe(expectedDate)
    })
    
    i18n.changeLanguage('en')

    months.forEach(({ date, nameEN }) => {
      const { result } = renderHook(() => useGetBeautifulDateWithMonth(date))

      const monthAbbr = nameEN.toLowerCase().slice(0, 3)

      const expectedDate = `${new Date(date).getDate()} ${monthAbbr} ${new Date(date).getFullYear()}`
      expect(result.current).toBe(expectedDate)
    })
  })

  it('Должен возвращать undefined, если дата невалидна', () => {
    const { result } = renderHook(() => useGetBeautifulDateWithMonth('invalid-date'))

    expect(result.current).toBeUndefined()
  })

  it('Должен корректно работать с датой в формате yyyy-mm-dd, включая переход между годами', () => {
    i18n.changeLanguage('ru')

    const { result } = renderHook(() => useGetBeautifulDateWithMonth('2025-12-31'))

    expect(result.current).toBe('31 дек 2025')
  })
})
