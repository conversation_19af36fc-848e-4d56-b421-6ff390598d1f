import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

/**
 * Хук для преобразования даты в красивый формат с месяцем на языке пользователя.
 * Дата должна быть передана в формате "yyyy-mm-dd".
 * 
 * @param {string} date - Дата в формате "yyyy-mm-dd".
 * @returns {string | undefined} Возвращает строку в формате "dd MMM yyyy" (где MMM - первые три буквы месяца на языке пользователя),
 * или `undefined`, если дата невалидна.
 * 
 * @example
 * // Если передать '2025-02-12', вернется '12 фев 2025' (для русского языка)
 * const formattedDate = useGetBeautifulDateWithMonth('2025-02-12')
 * 
 * @example
 * // Если передать 'invalid-date', вернется undefined
 * const formattedDate = useGetBeautifulDateWithMonth('invalid-date')
 */
export const useGetBeautifulDateWithMonth = (date: string) => {
  const { t } = useTranslation()

  const months = useMemo(
    () => [
      t('commons:january').toLowerCase(),
      t('commons:february').toLowerCase(),
      t('commons:march').toLowerCase(),
      t('commons:april').toLowerCase(),
      t('commons:may').toLowerCase(),
      t('commons:june').toLowerCase(),
      t('commons:july').toLowerCase(),
      t('commons:august').toLowerCase(),
      t('commons:september').toLowerCase(),
      t('commons:october').toLowerCase(),
      t('commons:november').toLowerCase(),
      t('commons:december').toLowerCase(),
    ],
    [t],
  )

  const isValidDate = (dateString: string) => {
    const regex = /^\d{4}-\d{2}-\d{2}$/
    if (!regex.test(dateString)) return false

    const date = new Date(dateString)
    return date instanceof Date
  }

  if (!isValidDate(date)) return undefined

  const dateArray = date.split('-').reverse()

  return `${dateArray[0]} ${months[+dateArray[1] - 1].slice(0, 3)} ${dateArray[2]}`
}
