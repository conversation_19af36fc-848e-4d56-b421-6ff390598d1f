import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

/**
 * Хук для получения списка статусов по фишингу с переводами на текущий язык.
 * 
 * Этот хук возвращает список объектов, каждый из которых содержит:
 * - `id`: идентификатор статуса фишинга
 * - `title`: переведенное название статуса фишинга на основе текущего языка.
 * 
 * Переводы подгружаются с использованием i18n через библиотеку `react-i18next`.
 * Переводы для списка типов фишинга включают следующие ключи:
 * - `didnt_open_letter`
 * - `letter_opened`
 * - `followed_link`
 * - `entered_data`
 * - `opened_attachment`
 * - `not_sent`
 *  
 * @returns {Array} Массив объектов, каждый из которых представляет тип фишинга:
 *   - `id` {string} Идентификатор типа фишинга (например, 'sent', 'opened')
 *   - `title` {string} Переведенное название типа фишинга (например, 'Не открыл письмо')
 */
export const useGetTranslatePhishingTypeList = () => {
  const { t } = useTranslation()

  const list = useMemo(
    () => [
      { id: 'sent', title: t('commons:didnt_open_letter') },
      { id: 'opened', title: t('commons:letter_opened') },
      { id: 'clicked', title: t('commons:followed_link') },
      { id: 'entered_data', title: t('commons:entered_data') },
      { id: 'opened_attachment', title: t('commons:opened_attachment') },
      { id: 'unknown', title: t('commons:not_sent') },
    ],
    [t],
  )

  return list
}
