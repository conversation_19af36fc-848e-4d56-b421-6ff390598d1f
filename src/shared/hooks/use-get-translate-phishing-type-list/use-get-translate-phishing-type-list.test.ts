import { renderHook } from '@testing-library/react-hooks'
import { useGetTranslatePhishingTypeList } from './use-get-translate-phishing-type-list'
import { addI18nResources, initI18n } from '@/shared/helpers/tests/i18n'
import i18n from '@/shared/configs/i18n'

beforeAll(() => {
  initI18n()
})

describe('[shared] [hooks] useGetTranslatePhishingTypeList', () => {
  it('Должен возвращать ключи перевода, если переводов нет', () => {
    addI18nResources({
      // Не добавляем переводов
    })

    const { result } = renderHook(() => useGetTranslatePhishingTypeList())

    const expectedEmptyList = [
      { id: 'sent', title: 'didnt_open_letter' },
      { id: 'opened', title: 'letter_opened' },
      { id: 'clicked', title: 'followed_link' },
      { id: 'entered_data', title: 'entered_data' },
      { id: 'opened_attachment', title: 'opened_attachment' },
      { id: 'unknown', title: 'not_sent' },
    ]

    expect(result.current).toEqual(expectedEmptyList)
  })

  it('Должен возвращать правильные переводы для списка типов фишинга', () => {
    addI18nResources({
      didnt_open_letter: 'Не открыл письмо',
      letter_opened: 'Открыл письмо',
      followed_link: 'Перешёл по ссылке',
      entered_data: 'Ввёл данные',
      opened_attachment: 'Открыл вложение',
      not_sent: 'Не отправлено',
    })

    const { result } = renderHook(() => useGetTranslatePhishingTypeList())

    const expectedList = [
      { id: 'sent', title: 'Не открыл письмо' },
      { id: 'opened', title: 'Открыл письмо' },
      { id: 'clicked', title: 'Перешёл по ссылке' },
      { id: 'entered_data', title: 'Ввёл данные' },
      { id: 'opened_attachment', title: 'Открыл вложение' },
      { id: 'unknown', title: 'Не отправлено' },
    ]

    expect(result.current).toEqual(expectedList)
  })

  it('Должен корректно работать с переводами, если язык изменен', () => {
    addI18nResources(
      {
        didnt_open_letter: 'Не открыл письмо',
        letter_opened: 'Открыл письмо',
        followed_link: 'Перешёл по ссылке',
        entered_data: 'Ввёл данные',
        opened_attachment: 'Открыл вложение',
        not_sent: 'Не отправлено',
      },
      { lang: 'ru' },
    )
    addI18nResources(
      {
        didnt_open_letter: "Didn't open the email",
        letter_opened: 'Opened the email',
        followed_link: 'Followed the link',
        entered_data: 'Entered Data',
        opened_attachment: 'Opened Attachment',
        not_sent: 'Not Sent',
      },
      { lang: 'en' },
    )

    i18n.changeLanguage('en')

    const { result } = renderHook(() => useGetTranslatePhishingTypeList())

    const expectedList = [
      { id: 'sent', title: "Didn't open the email" },
      { id: 'opened', title: 'Opened the email' },
      { id: 'clicked', title: 'Followed the link' },
      { id: 'entered_data', title: 'Entered Data' },
      { id: 'opened_attachment', title: 'Opened Attachment' },
      { id: 'unknown', title: 'Not Sent' },
    ]

    expect(result.current).toEqual(expectedList)
  })
})
