import { useCallback, useLayoutEffect } from "react";

/**
 * Хук для блокировки прокрутки страницы.
 * 
 * @param {boolean} shouldLock - Флаг, указывающий, нужно ли блокировать прокрутку. По умолчанию `true`.
 *
 * @example
 * useScrollLock(true); // Блокирует прокрутку
 * useScrollLock(false); // Разрешает прокрутку
 */
export const useScrollLock = (shouldLock: boolean = true) => {
  const handleScroll = useCallback((e: Event) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);
  
  useLayoutEffect(() => {
    if (!shouldLock) return;

    document.body.style.overflow = "hidden";
    document.addEventListener("scroll", handleScroll, { passive: false });

    return () => {
      document.body.style.overflow = "";
      document.removeEventListener("scroll", handleScroll);
    };
  }, [shouldLock, handleScroll]);
};
