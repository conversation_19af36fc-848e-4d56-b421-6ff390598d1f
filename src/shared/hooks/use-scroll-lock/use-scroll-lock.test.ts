import { renderHook } from '@testing-library/react';
import { useScrollLock } from './use-scroll-lock';

describe('[shared] [hooks] useScrollLock', () => {
  beforeEach(() => {
    document.body.style.overflow = '';
    jest.spyOn(document, 'addEventListener');
    jest.spyOn(document, 'removeEventListener');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('Должен блокировать прокрутку при shouldLock=true', () => {
    renderHook(() => useScrollLock(true));

    expect(document.body.style.overflow).toBe('hidden');
    expect(document.addEventListener).toHaveBeenCalledWith('scroll', expect.any(Function), { passive: false });
  });

  it('Должен разрешать прокрутку при shouldLock=false', () => {
    renderHook(() => useScrollLock(false));

    expect(document.body.style.overflow).toBe('');
    expect(document.addEventListener).not.toHaveBeenCalled();
  });

  it('Должен снимать блокировку при размонтировании', () => {
    const { unmount } = renderHook(() => useScrollLock(true));

    expect(document.body.style.overflow).toBe('hidden');

    unmount();

    expect(document.body.style.overflow).toBe('');
    expect(document.removeEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));
  });

  it('Должен изменять блокировку при смене shouldLock', () => {
    const { rerender } = renderHook(({ shouldLock }) => useScrollLock(shouldLock), {
      initialProps: { shouldLock: true },
    });

    expect(document.body.style.overflow).toBe('hidden');

    rerender({ shouldLock: false });

    expect(document.body.style.overflow).toBe('');
    expect(document.removeEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));
  });

  it('Должен предотвращать прокрутку при shouldLock=true', () => {
    renderHook(() => useScrollLock(true));

    const event = new Event('scroll', { bubbles: true, cancelable: true });
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(event, 'stopPropagation');

    document.dispatchEvent(event);

    expect(event.preventDefault).toHaveBeenCalled();
    expect(event.stopPropagation).toHaveBeenCalled();
  });

  it('Не должен предотвращать прокрутку при shouldLock=false', () => {
    renderHook(() => useScrollLock(false));

    const event = new Event('scroll', { bubbles: true, cancelable: true });
    jest.spyOn(event, 'preventDefault');
    jest.spyOn(event, 'stopPropagation');

    document.dispatchEvent(event);

    expect(event.preventDefault).not.toHaveBeenCalled();
    expect(event.stopPropagation).not.toHaveBeenCalled();
  });

  it('Должен корректно обновлять обработчик при изменении shouldLock', () => {
    const { rerender } = renderHook(({ shouldLock }) => useScrollLock(shouldLock), {
      initialProps: { shouldLock: true },
    });

    expect(document.addEventListener).toHaveBeenCalledWith('scroll', expect.any(Function), { passive: false });

    rerender({ shouldLock: false });

    expect(document.removeEventListener).toHaveBeenCalledWith('scroll', expect.any(Function));

    rerender({ shouldLock: true });

    expect(document.addEventListener).toHaveBeenCalledTimes(2);
  });
});
