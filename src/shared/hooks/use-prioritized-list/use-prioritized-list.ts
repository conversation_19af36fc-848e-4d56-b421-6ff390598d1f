import { useMemo } from 'react'

interface EntityWithId {
  id: string | null
}

export const usePrioritizedList = <T extends EntityWithId>(
  items: T[],
  priorityEntity: T | null | undefined,
): T[] => {
  return useMemo(() => {
    if (!priorityEntity || !priorityEntity.id) {
      return items
    }

    const existingIndex = items.findIndex(item => item.id === priorityEntity.id)

    if (existingIndex === -1) {
      return [priorityEntity, ...items]
    } else {
      const filteredItems = items.filter(item => item.id !== priorityEntity.id)
      return [priorityEntity, ...filteredItems]
    }
  }, [items, priorityEntity])
}
