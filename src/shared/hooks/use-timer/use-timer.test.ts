import { renderHook, act } from '@testing-library/react'
import { useTimer } from './use-timer'

jest.useFakeTimers()

describe('[shared] [hooks] useTimer', () => {
  it('Должен корректно инициализировать время', () => {
    const futureTime = new Date(Date.now() + 10 * 60 * 1000).toISOString() // Через 10 минут
    const { result } = renderHook(() => useTimer(futureTime))

    expect(result.current.time).toBeGreaterThan(0)
  })

  it('Должен уменьшать время с заданным интервалом (5000 мс)', () => {
    const futureTime = new Date(Date.now() + 10 * 60 * 1000).toISOString()
    const { result } = renderHook(() => useTimer(futureTime, 5000))

    act(() => {
      jest.advanceTimersByTime(5000) // Пропускаем 5 секунд
    })

    expect(result.current.time).toBeGreaterThan(0)
  })

  it('Должен переключиться на 1-секундный интервал при времени < 60000 мс', () => {
    const futureTime = new Date(Date.now() + 59000).toISOString() // Через 59 секунд
    const { result } = renderHook(() => useTimer(futureTime))

    act(() => {
      jest.advanceTimersByTime(5000) // Пропускаем 5 секунд
    })

    expect(result.current.time).toBeGreaterThan(0)

    act(() => {
      jest.advanceTimersByTime(1000) // Пропускаем 1 секунду
    })

    expect(result.current.time).toBeGreaterThan(0)
  })

  it('Должен остановиться, когда время достигает 0', () => {
    const futureTime = new Date(Date.now() + 3000).toISOString() // Через 3 секунды
    const { result } = renderHook(() => useTimer(futureTime, 1000))

    act(() => {
      jest.advanceTimersByTime(3000) // Пропускаем 3 секунды
    })

    expect(result.current.time).toBe(0)

    act(() => {
      jest.advanceTimersByTime(3000) // Пропускаем 3 секунды
    })

    expect(result.current.time).toBe(0)
  })
})
