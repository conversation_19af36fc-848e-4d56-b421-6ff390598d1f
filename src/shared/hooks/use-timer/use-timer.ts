import { useCallback, useEffect, useRef, useState } from 'react'

/**
 * Хук useTimer отсчитывает время до указанного предела (limit).
 * При достижении порога в 60 секунд интервал обновляется с 5000ms (по умолчанию) на 1000ms.
 * При достижении конца - останавливается и вовзращает 0
 *
 * @param {string} limit - Дата и время окончания таймера в формате, поддерживаемом `Date`.
 * @param {number} [interval=5000] - Начальный интервал обновления времени (по умолчанию 5000 мс).
 * @returns {{ time: number }} Объект с оставшимся временем в миллисекундах.
 */
export const useTimer = (limit: string, interval: number = 5000) => {
  const [time, setTime] = useState<number | null>(null)
  const [isSeconds, setIsSeconds] = useState<boolean>(false)
  const intervalId = useRef<NodeJS.Timeout | null>(null)

  const handleInitial = useCallback(() => {
    const now = new Date()
    const limitDate = new Date(limit)
    const diff = limitDate.getTime() - now.getTime()
    setTime(diff > 0 ? diff : 0)
  }, [limit])

  useEffect(() => {
    handleInitial()

    intervalId.current = setInterval(() => {
      setTime(prev => (prev !== null ? Math.max(prev - interval, 0) : 0))
    }, interval)

    return () => {
      if (intervalId.current) clearInterval(intervalId.current)
    }
  }, [handleInitial, interval])

  useEffect(() => {
    if (time !== null && time > 0 && time < 60000 && !isSeconds) {
      setIsSeconds(true)
      if (intervalId.current) clearInterval(intervalId.current)

      intervalId.current = setInterval(() => {
        setTime(prev => (prev !== null ? Math.max(prev - 1000, 0) : 0))
      }, 1000)
    }

    if (time !== null && time <= 0 && intervalId.current) {
      clearInterval(intervalId.current)
      intervalId.current = null
    }
  }, [time, isSeconds])

  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        handleInitial()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [handleInitial])

  return { time: time || 0 }
}
