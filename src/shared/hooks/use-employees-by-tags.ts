import { useLazyGetEmployeesByTagQuery } from '@/store/services/tags-employees-service'
import { useEffect, useState } from 'react'
import { IUser } from 'entities/employee'

export const useEmployeesByTags = (tagIds?: UUID[] | null) => {
  const [getEmployeesByTag] = useLazyGetEmployeesByTagQuery()
  const [usersByTags, setUsersByTags] = useState<IUser[]>([])
  const [isFetching, setIsFetching] = useState<boolean>(false)

  useEffect(() => {
    if (!tagIds) return

    const getUsersByTag = async () => {
      setIsFetching(true)
      const result: IUser[] = []
      try {
        await Promise.all(
          tagIds.map(tagId =>
            getEmployeesByTag(tagId, true)
              .unwrap()
              .then(employesByTag => result.push(...(employesByTag?.data ?? []))),
          ),
        )
        setUsersByTags(result)
      } catch (error) {
        console.error('Failed to fetch employees by tags', error)
      } finally {
        setIsFetching(false)
      }
    }

    getUsersByTag()
  }, [tagIds, getEmployeesByTag])

  return { usersByTags, isFetching }
}
