import { useEffect } from 'react'
import { useLatest } from './'

export function useResizeObserver(
  elementRef: React.RefObject<Element>,
  cb: ResizeObserverCallback,
) {
  const latestCb = useLatest(cb)

  useEffect(() => {
    const element = elementRef.current

    if (!element) {
      return
    }

    const observer = new ResizeObserver((...args) => {
      latestCb.current(...args)
    })

    observer.observe(element)

    return () => observer.disconnect()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [latestCb])
}
