/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from 'react'

interface UseFullscreenResult {
  isFullscreen: boolean
  toggleFullscreen: () => void
  exitFullscreen: () => void
}

export const isNativeFullscreenSupported = () => {
  return (
    document.fullscreenEnabled ||
    (document as any).webkitFullscreenEnabled ||
    (document as any).msFullscreenEnabled
  )
}

const useFullscreen = (ref: React.RefObject<HTMLIFrameElement>): UseFullscreenResult => {
  const [isFullscreen, setIsFullscreen] = useState(false)

  const stopEventPropagation = useCallback((e: Event) => {
    e.preventDefault()
    e.stopPropagation()
    e.stopImmediatePropagation()
  }, [])

  const addEventBlockers = useCallback(
    (element: HTMLElement) => {
      element.addEventListener('click', stopEventPropagation, { capture: true, passive: false })
      element.addEventListener('touchstart', stopEventPropagation, {
        capture: true,
        passive: false,
      })
      element.addEventListener('touchend', stopEventPropagation, { capture: true, passive: false })
      element.addEventListener('touchmove', stopEventPropagation, { capture: true, passive: false })
      element.addEventListener('touchcancel', stopEventPropagation, {
        capture: true,
        passive: false,
      })
    },
    [stopEventPropagation],
  )

  const removeEventBlockers = useCallback(
    (element: HTMLElement) => {
      element.removeEventListener('click', stopEventPropagation, true)
      element.removeEventListener('touchstart', stopEventPropagation, true)
      element.removeEventListener('touchend', stopEventPropagation, true)
      element.removeEventListener('touchmove', stopEventPropagation, true)
      element.removeEventListener('touchcancel', stopEventPropagation, true)
    },
    [stopEventPropagation],
  )

  const handleFullscreenChange = useCallback(() => {
    const isFull =
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).msFullscreenElement

    setIsFullscreen(!!isFull)
  }, [])

  useEffect(() => {
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
    document.addEventListener('MSFullscreenChange', handleFullscreenChange)

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange)
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange)
    }
  }, [handleFullscreenChange])

  const enterFullscreen = useCallback(() => {
    const element = ref.current
    if (!element) return

    setIsFullscreen(true)
    addEventBlockers(element)

    if (isNativeFullscreenSupported()) {
      const openFullscreen =
        element.requestFullscreen ||
        (element as any).webkitRequestFullscreen ||
        (element as any).msRequestFullscreen

      if (openFullscreen) {
        openFullscreen.call(element).catch(() => {
          applyCustomFullscreen(element)
        })
        return
      }
    }

    applyCustomFullscreen(element)
  }, [addEventBlockers, ref])

  const applyCustomFullscreen = (element: HTMLElement) => {
    element.style.position = 'fixed'
    element.style.top = 'env(safe-area-inset-top, 0)'
    element.style.left = 'env(safe-area-inset-left, 0)'
    element.style.right = 'env(safe-area-inset-right, 0)'
    element.style.bottom = 'env(safe-area-inset-bottom, 0)'
    element.style.width =
      'calc(100% - (env(safe-area-inset-left, 0) + env(safe-area-inset-right, 0)))'
    element.style.height =
      'calc(100% - (env(safe-area-inset-top, 0) + env(safe-area-inset-bottom, 0)))'
    element.style.zIndex = '1000'
    document.body.style.overflow = 'hidden'
  }

  const exitFullscreen = useCallback(() => {
    const element = ref.current
    if (!element) return

    setIsFullscreen(false)
    resetCustomFullscreen(element)

    if (isNativeFullscreenSupported()) {
      const closeFullscreen =
        document.exitFullscreen ||
        (document as any).webkitExitFullscreen ||
        (document as any).msExitFullscreen

      if (closeFullscreen) {
        closeFullscreen.call(document).catch(() => {
          resetCustomFullscreen(element)
        })
        return
      }
    }

    removeEventBlockers(element)
  }, [ref, removeEventBlockers])

  const resetCustomFullscreen = (element: HTMLElement) => {
    element.style.position = ''
    element.style.top = ''
    element.style.left = ''
    element.style.width = ''
    element.style.height = ''
    element.style.zIndex = ''
    document.body.style.overflow = ''
  }

  const toggleFullscreen = useCallback(() => {
    if (isFullscreen) {
      exitFullscreen()
    } else {
      enterFullscreen()
    }
  }, [isFullscreen, enterFullscreen, exitFullscreen])

  return { isFullscreen, toggleFullscreen, exitFullscreen }
}

export default useFullscreen
