import { useEffect } from 'react'
import { useNavigate, useLocation, NavigateOptions } from 'react-router-dom'
import { URLS } from '../configs/urls'

type StorageAdapter = {
  get: (key: string) => string | null
  set: (key: string, value: string) => void
  remove: (key: string) => void
}

type UseRedirectStorageOptions = {
  storage?: StorageAdapter
  storageKey?: string
  maxAge?: number
  fallbackPath?: string
  encode?: (data: string) => string
  decode?: (data: string) => string
}

const defaultStorage: StorageAdapter = {
  get: key => localStorage.getItem(key),
  set: (key, value) => localStorage.setItem(key, value),
  remove: key => localStorage.removeItem(key),
}

const ONE_DAY = 24 * 60 * 60 * 1000

export const useRedirectStorage = (options?: UseRedirectStorageOptions) => {
  const {
    storage = defaultStorage,
    storageKey = 'redirectUrl',
    maxAge = ONE_DAY * 7,
    fallbackPath = URLS.USER_MY_COURSES_PAGE,
    encode = (data: string) => btoa(encodeURIComponent(data)),
    decode = (data: string) => decodeURIComponent(atob(data)),
  } = options || {}

  const navigate = useNavigate()
  const location = useLocation()

  const save = (url: string) => {
    const data = {
      url,
      timestamp: Date.now(),
    }
    storage.set(storageKey, encode(JSON.stringify(data)))
  }

  const get = (): string | null => {
    const rawData = storage.get(storageKey)
    if (!rawData) return null

    try {
      const data = JSON.parse(decode(rawData))
      if (Date.now() - data.timestamp > maxAge) {
        storage.remove(storageKey)
        return null
      }
      return data.url
    } catch {
      storage.remove(storageKey)
      return null
    }
  }

  const clear = () => storage.remove(storageKey)

  const redirect = (navigateOptions?: NavigateOptions) => {
    const url = get()
    clear()
    navigate(url || fallbackPath, navigateOptions || { replace: true })
  }

  useEffect(() => {
    if (location.state?.redirect) {
      save(location.state.redirect)
    }
  }, [location.state])

  return { save, get, clear, redirect }
}
