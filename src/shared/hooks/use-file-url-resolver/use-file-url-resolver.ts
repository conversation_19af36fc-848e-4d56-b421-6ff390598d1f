import { useState, useEffect } from "react";

/**
 * Хук `useFileUrlResolver` принимает файл или строку с URL и возвращает URL-адрес.
 * Если передана строка, она возвращается без изменений.
 * Если передан объект `File`, создаётся временный `object URL`, который автоматически
 * очищается при изменении файла или размонтировании компонента.
 *
 * @param {string | File | undefined} file - Файл или строка с URL. Если `undefined`, вернётся `undefined`.
 * @returns {string | undefined} URL файла или `undefined`, если файл отсутствует.
 *
 * @example
 * // Передача строки
 * const url = useFileUrlResolver("https://example.com/image.jpg");
 * console.log(url); // "https://example.com/image.jpg"
 *
 * @example
 * // Передача объекта File
 * const file = new File(["content"], "example.txt", { type: "text/plain" });
 * const url = useFileUrlResolver(file);
 * console.log(url); // "blob:generated-url"
 */
export function useFileUrlResolver(file: string | File | undefined): string | undefined {
  const [url, setUrl] = useState<string | undefined>(typeof file === 'string' ? file : undefined)

  useEffect(() => {
    if (!file) {
      setUrl(undefined);
      return;
    }

    if (typeof file === "string") {
      setUrl(file); 
      return;
    }

    const objectUrl = URL.createObjectURL(file);
    setUrl(objectUrl);

    return () => {
      URL.revokeObjectURL(objectUrl);
    };
  }, [file]);

  return url
}
