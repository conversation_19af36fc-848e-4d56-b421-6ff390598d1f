import { renderHook } from '@testing-library/react'
import { useFileUrlResolver } from './use-file-url-resolver'

describe('[shared] [hooks] useFileUrlResolver', () => {
  beforeAll(() => {
    global.URL.createObjectURL = jest.fn(() => 'blob:mocked-url')
    global.URL.revokeObjectURL = jest.fn()
  })

  afterAll(() => {
    jest.restoreAllMocks()
  })

  it('Должен вернуть undefined, если file === undefined', () => {
    const { result } = renderHook(() => useFileUrlResolver(undefined))
    expect(result.current).toBeUndefined()
  })

  it('Должен вернуть строку, если передан URL в виде строки', () => {
    const testUrl = 'https://example.com/image.jpg'
    const { result } = renderHook(() => useFileUrlResolver(testUrl))
    expect(result.current).toBe(testUrl)
  })

  it('Должен создать object URL, если передан File', () => {
    const file = new File(['test'], 'test.png', { type: 'image/png' })
    const { result } = renderHook(() => useFileUrlResolver(file))

    expect(result.current).toBe('blob:mocked-url')
    expect(URL.createObjectURL).toHaveBeenCalledWith(file)
  })

  it('Должен отзывать object URL при размонтировании', () => {
    const file = new File(['test'], 'test.png', { type: 'image/png' })
    const { result, unmount } = renderHook(() => useFileUrlResolver(file))

    expect(result.current).toBe('blob:mocked-url')
    unmount()
    expect(URL.revokeObjectURL).toHaveBeenCalledWith('blob:mocked-url')
  })

  it('Должен обновить URL при смене файла', () => {
    const file1 = new File(['test1'], 'test1.png', { type: 'image/png' })
    const file2 = new File(['test2'], 'test2.png', { type: 'image/png' })

    const { result, rerender } = renderHook(({ file }) => useFileUrlResolver(file), {
      initialProps: { file: file1 },
    })

    expect(result.current).toBe('blob:mocked-url')
    expect(URL.createObjectURL).toHaveBeenCalledWith(file1)

    ;(URL.createObjectURL as jest.Mock).mockReturnValueOnce('blob:mocked-url-2')
    rerender({ file: file2 })

    expect(result.current).toBe('blob:mocked-url-2')
    expect(URL.createObjectURL).toHaveBeenCalledWith(file2)
    expect(URL.revokeObjectURL).toHaveBeenCalledWith('blob:mocked-url')
  })
})
