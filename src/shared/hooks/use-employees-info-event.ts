import { useAppDispatch } from '@/store'
import {
  tagsEmployeesApi,
  useDeleteEventIdMutation,
  useGetEmployeeEventInfoQuery,
} from '@/store/services/tags-employees-service'
import { useCallback, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

const LOCAL_STORAGE_KEY = 'employeeEventId'

const TRANSLATION_FILE = 'modals__import-employess'

export const useEmployeesInfoEvent = () => {
  const [eventId, setEventId] = useState<string | null>()
  const [errorMessage, setErrorMessage] = useState<string | undefined>()
  const [sectionErrors, setSectionErrors] = useState<string[]>([])
  const [uploading, setUploading] = useState<string | undefined>()
  const { t } = useTranslation(TRANSLATION_FILE)
  const {
    data,
    error,
    isLoading: isEventLoading,
  } = useGetEmployeeEventInfoQuery(eventId || '', {
    skip: !eventId || uploading === 'not_started',
    pollingInterval: 5000,
  })
  const [deleteEventId] = useDeleteEventIdMutation()
  const handleEventId = () => {
    const id = localStorage.getItem(LOCAL_STORAGE_KEY)
    setEventId(id)
  }

  const handleUploading = () => {
    setUploading('in_process')
  }

  const dispatch = useAppDispatch()

  useEffect(() => {
    handleEventId()
  }, [])

  const handleClear = useCallback(
    (shoulClearUploading: boolean = true) => {
      dispatch(
        tagsEmployeesApi.util.upsertQueryData('getEmployeeEventInfo', eventId || '', {
          metadata: undefined,
          id: '',
          type: '',
          status: '',
          error: {},
        }),
      )
      localStorage.removeItem(LOCAL_STORAGE_KEY)
      eventId && deleteEventId(eventId || '').unwrap()
      shoulClearUploading && setUploading('')
      setEventId(null)
    },
    [dispatch, eventId, deleteEventId, setUploading, setEventId],
  )

  useEffect(() => {
    if (data?.error && data?.error?.code) {
      let message = ''
      const newSectionErrors: string[] = []

      if (data.error.code === 'invalid_headers_upload_file') {
        const mainMessage = t(`error.${data.error.code}`, {
          defaultValue: 'unknown_error',
        })

        if (data.error.params?.employees?.section_code === 'invalid_headers_users') {
          const invalidHeaders = data.error.params.employees.info?.invalid?.join(', ') || ''
          newSectionErrors.push(t('error.invalid_headers_users', { invalid: invalidHeaders }))
        }

        if (data.error.params?.employees?.section_code === 'empty_headers') {
          newSectionErrors.push(t('error.empty_headers_employees'))
        }

        if (data.error.params?.departments?.section_code === 'invalid_headers_departments') {
          const invalidHeaders = data.error.params.departments.info?.invalid?.join(', ') || ''
          newSectionErrors.push(t('error.invalid_headers_departments', { invalid: invalidHeaders }))
        }

        if (data.error.params?.departments?.section_code === 'empty_headers') {
          newSectionErrors.push(t('error.empty_headers_departments'))
        }

        message = mainMessage
        setSectionErrors(newSectionErrors)
      } else {
        message = t(`error.${data.error?.code}`, {
          ...data.error?.params,
          defaultValue: 'unknown_error',
        })
        setSectionErrors([])
      }

      setErrorMessage(message)
      dispatch(
        tagsEmployeesApi.util.upsertQueryData('getEmployeeEventInfo', eventId || '', {
          metadata: undefined,
          id: '',
          type: '',
          status: '',
          error: {},
        }),
      )
      handleClear()
    } else {
      setSectionErrors([])
    }
  }, [data?.error, dispatch, eventId, handleClear, t])

  useEffect(() => {
    if (data?.metadata?.content?.uploading) {
      setUploading(data?.metadata?.content?.uploading)
    }

    if (data?.metadata?.content?.uploading === 'success') {
      handleClear(false)
    }
  }, [data?.metadata?.content?.uploading, handleClear])

  return {
    status: data?.status,
    uploading,
    info: data?.metadata?.content?.info,
    errorLoading: error,
    errorMessage: errorMessage,
    sectionErrors,
    eventId,
    handleEventId,
    fileName: data?.metadata?.metadata?.file_name,
    isEventLoading,
    handleClear,
    handleUploading,
    setUploading,
  }
}
