import { useTranslation } from "react-i18next"
import { EDITOR_I18N_EN_CONFIG } from "./configs/en"
import { EDITOR_I18N_RU_CONFIG } from "./configs/ru"
import { useEffect, useState } from "react"

const configs: Record<string, Object> = {
    ru: EDITOR_I18N_RU_CONFIG,
    en: EDITOR_I18N_EN_CONFIG
}

export const useEditorLocale = () => {
  const { i18n } = useTranslation()
  const [currentConfig, setCurrentConfig] = useState<Object | undefined>();

useEffect(() => {
    if (!i18n.isInitialized) return

    const config = configs[i18n.language];
    setCurrentConfig(config);

}, [i18n.isInitialized, i18n.language])

  return {
    currentConfig
  }
}