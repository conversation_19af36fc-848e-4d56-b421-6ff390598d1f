import { useRef, useCallback, useEffect } from 'react'
import { EditorCore } from '@react-editor-js/core'

interface UseStableEditorOptions {
  stepId?: string
  onDestroy?: () => void
}

export const useStableEditor = (options: UseStableEditorOptions = {}) => {
  const { stepId, onDestroy } = options
  const editorCore = useRef<EditorCore | null>(null)
  const isDestroyingRef = useRef(false)

  const handleInitialize = useCallback((instance: EditorCore) => {
    if (isDestroyingRef.current) return
    editorCore.current = instance
  }, [])

  const destroyEditor = useCallback(async () => {
    if (isDestroyingRef.current || !editorCore.current) return

    isDestroyingRef.current = true

    try {
      console.debug('Destroying editor instance via useStableEditor')
    } catch (error) {
      console.warn('Error destroying EditorJS instance:', error)
    } finally {
      editorCore.current = null
      isDestroyingRef.current = false
      onDestroy?.()
    }
  }, [onDestroy])

  const handleSave = useCallback(async () => {
    if (!editorCore.current || isDestroyingRef.current) return undefined

    try {
      return await editorCore.current.save()
    } catch (error) {
      console.error('Error saving EditorJS data:', error)
      return undefined
    }
  }, [])

  useEffect(() => {
    return () => {
      if (editorCore.current) {
        console.debug('Clearing editor reference due to stepId change')
        editorCore.current = null
        isDestroyingRef.current = false
        onDestroy?.()
      }
    }
  }, [stepId, onDestroy])

  return {
    editorCore,
    handleInitialize,
    handleSave,
    destroyEditor,
    isDestroying: isDestroyingRef.current,
  }
}
