import { useLayoutEffect, useRef } from 'react'


/**
 * Хук для хранения актуального значения без триггера ререндеров.
 *
 * @param {T} val - Значение, которое должно быть всегда актуальным.
 * @returns {React.MutableRefObject<T>} Ref с актуальным значением.
 */
export function useLatest<T>(val: T): React.MutableRefObject<T> {
  const valueRef = useRef<T>(val)

  useLayoutEffect(() => {
    valueRef.current = val
  }, [val])

  return valueRef
}
