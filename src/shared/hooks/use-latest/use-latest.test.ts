import { renderHook } from '@testing-library/react'
import { useLatest } from './use-latest'

describe('[shared] [hooks] useLatest', () => {
  it('Должен хранить актуальное значение', () => {
    const { result, rerender } = renderHook(({ val }) => useLatest(val), {
      initialProps: { val: 10 },
    })

    expect(result.current.current).toBe(10)

    rerender({ val: 20 })
    expect(result.current.current).toBe(20)

    rerender({ val: 30 })
    expect(result.current.current).toBe(30)
  })

  it('Нне должен менять ссылку на ref при обновлении значения', () => {
    const { result, rerender } = renderHook(({ val }) => useLatest(val), {
      initialProps: { val: 'initial' },
    })

    const initialRef = result.current

    rerender({ val: 'updated' })

    expect(result.current).toBe(initialRef) // Ссылка остаётся той же
    expect(result.current.current).toBe('updated') // Значение обновилось
  })

  it('Должен хранить начальное значение после первого рендера', () => {
    const { result } = renderHook(() => useLatest('Hello'))

    expect(result.current.current).toBe('Hello')
  })

  it('Должен корректно хранить undefined', () => {
    const { result } = renderHook(({ val }) => useLatest(val), {
      initialProps: { val: undefined },
    })

    expect(result.current.current).toBe(undefined)
  })

  it('Должен корректно хранить null', () => {
    const { result } = renderHook(({ val }) => useLatest(val), {
      initialProps: { val: null },
    })

    expect(result.current.current).toBe(null)
  })

  it('Должен корректно работать с объектами и ссылочными типами', () => {
    const obj = { key: 'value' }
    const { result, rerender } = renderHook(({ val }) => useLatest(val), {
      initialProps: { val: obj },
    })

    expect(result.current.current).toBe(obj)

    const newObj = { key: 'newValue' }
    rerender({ val: newObj })
    expect(result.current.current).toBe(newObj)
  })
})
