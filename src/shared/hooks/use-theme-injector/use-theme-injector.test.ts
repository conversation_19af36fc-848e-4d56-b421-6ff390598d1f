import { renderHook } from '@testing-library/react'
import { useThemeInjector } from './use-theme-injector'
import { useConfig } from '..'

jest.mock('..', () => ({
  useConfig: jest.fn(),
}))

describe('useThemeInjector', () => {

  beforeEach(() => {
    // Очищаем DOM перед каждым тестом
    document.body.innerHTML = '<style id="theme"></style>'
  })

  it('Должен обновлять стили темы при изменении конфигурации', () => {
    const mockConfig = { theme: { colors: { primary: '#ff0000', secondary: '#00ff00' } } }

    ;(useConfig as jest.Mock).mockReturnValue(mockConfig)

    renderHook(() => useThemeInjector())

    const styleElement = document.getElementById('theme')

    expect(styleElement?.textContent).toContain(
      '--injected-theme-color-primary: #ff0000 !important;',
    )
    expect(styleElement?.textContent).toContain(
      '--injected-theme-color-secondary: #00ff00 !important;',
    )
  })

  it('Не должен обновлять стили темы, если colors отсутствуют', () => {
    const mockConfig = { theme: {} }

    ;(useConfig as jest.Mock).mockReturnValue(mockConfig)

    renderHook(() => useThemeInjector())

    const styleElement = document.getElementById('theme')
    expect(styleElement?.textContent).toBe('')
  })

  it('Не должен обновлять стили темы, если элемент <style id="theme"> отсутствует', () => {
    document.body.innerHTML = ''

    const mockConfig = { theme: { colors: { primary: '#ff0000' } } }

    ;(useConfig as jest.Mock).mockReturnValue(mockConfig)

    renderHook(() => useThemeInjector())

    const styleElement = document.getElementById('theme')
    expect(styleElement).toBeNull()
  })

  it('должен обновлять стили темы при изменении конфигурации', () => {
    const initialConfig = { theme: { colors: { primary: '#ff0000' } } }

    ;(useConfig as jest.Mock).mockReturnValue(initialConfig)

    const { rerender } = renderHook(() => useThemeInjector())

    let styleElement = document.getElementById('theme')
    expect(styleElement?.textContent).toBe(
      ':root { --injected-theme-color-primary: #ff0000 !important; }',
    )

    const updatedConfig = { theme: { colors: { primary: '#0000ff', secondary: '#00ff00' } } }

    ;(useConfig as jest.Mock).mockReturnValue(updatedConfig)

    rerender()

    styleElement = document.getElementById('theme')
    expect(styleElement?.textContent).toContain(
      '--injected-theme-color-primary: #0000ff !important;',
    )
    expect(styleElement?.textContent).toContain(
      '--injected-theme-color-secondary: #00ff00 !important;',
    )
  })

  it('не должен обновлять стили темы, если конфигурация не изменилась', () => {
    const mockConfig = { theme: { colors: { primary: '#ff0000' } } }

    ;(useConfig as jest.Mock).mockReturnValue(mockConfig)

    const { rerender } = renderHook(() => useThemeInjector())

    const styleElement = document.getElementById('theme')
    const initialStyles = styleElement?.textContent

    rerender()

    expect(styleElement?.textContent).toBe(initialStyles)
  })
})
