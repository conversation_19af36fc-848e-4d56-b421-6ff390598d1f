import { useLayoutEffect } from 'react'
import { useConfig } from '..'

const generateThemeStyles = (colors: Record<string, string>) => {
  return Object.entries(colors)
    .map(([key, value]) => `--injected-theme-color-${key}: ${value} !important;`)
    .join('\n')
}

/**
 * Хук для инжекта темы. Этот хук генерирует и применяет CSS-стили на основе конфигурации темы,
 * получаемой через хук `useConfig`, и добавляет их в элемент с id="theme".
 *
 * Он обновляет стили на странице каждый раз, когда изменяется конфигурация темы. 
 * В частности, он устанавливает переменные CSS для цветов темы, используя данные, полученные из конфигурации.
 *
 * Применяет стили в глобальном контексте для использования переменных CSS в других частях приложения.
 * 
 * @returns {void} Не возвращает значения.
 */
export const useThemeInjector = () => {
  const config = useConfig()

  useLayoutEffect(() => {
    const colors = config?.theme?.colors
    const styleElement = document.getElementById('theme')

    if (colors && styleElement) {
      const themeStyles = generateThemeStyles(colors)
      styleElement.textContent = `:root { ${themeStyles} }`
    }
  }, [config])
}
