/* eslint-disable @typescript-eslint/ban-types */
import { useConfig } from '@/shared/hooks' // Assuming you have a useConfig hook or similar

type UserParams = {
  name?: string
  email: string
  position?: string
  department?: string
  role?: string
  organization?: string
}

type VisitParams = {
  organization: string
  role: string
  theme: string
  licence_type: string
}

export const useAnalytics = () => {
  const config = useConfig()

  const ymId = Number(config.yandex_metrika_id)

  const event = (name: string, params?: object) => {
    if (!window.ym && ymId) return

    try {
      window.ym(ymId, 'reachGoal', name, params)
    } catch (err) {
      console.warn('Metrica reachGoal error', err)
    }
  }

  const route = (url: string) => {
    if (!window.ym && ymId) return

    try {
      window.ym(ymId, 'hit', url, {})
    } catch (err) {
      console.warn('Metrica hit error', err)
    }
  }

  const updateProfile = (userId: string, userParams: UserParams) => {
    if (!window.ym && ymId) return

    try {
      window.ym(ymId, 'userParams', {
        ...userParams,
        UserID: userId,
      })
    } catch (err) {
      console.warn('Metrica userParams error', err)
    }
  }

  const updateParams = (params: VisitParams) => {
    if (!window.ym && ymId) return

    try {
      window.ym(ymId, 'params', params)
    } catch (err) {
      console.warn('Metrica params error', err)
    }
  }

  return {
    event,
    route,
    updateProfile,
    updateParams,
  }
}
