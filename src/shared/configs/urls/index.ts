// TODO пока такой env вроде нет, можно будет добавить, если нужна будет
export const API_DOMAIN = 'http://localhost:5000'
// process.env.REACT_APP_API_DOMAIN ||

export const URLS = {
  GOTCHA: '/lk/phishing/gotcha',
  PRIVACY_POLICY: '/lk/privacy-policy',
  AGREEMENT: '/lk/agreement',
  //
  LOGIN_PAGE: '/lk/auth/login',
  POLICY: '/lk/auth/policy',
  LOGIN_BY_DEFAULT_PAGE: '/lk/auth/*',
  REGISTRATION_PAGE: '/lk/auth/registration/:registration_token',
  PASSWORD_RECOVERY_PAGE: '/lk/auth/password/recovery',
  ADMIN_CREATE_COURSE_PAGE: '/lk/admin/learning/courses/create',
  ADMIN_EDIT_COURSE_PAGE: '/lk/admin/learning/courses/edit/',
  ADMIN_PREVIEW_COURSE_PAGE: `/lk/admin/learning/courses/preview/:course_id`,
  // Сейчас нет такой отдельной страницы. Когда восстановление незареганного пользователя происходит
  PASSWORD_RECOVERY_NOT_REGISTERED_PAGE: '/lk/auth/password/recovery/error',
  // Сейчас нет такой страницы. Это после успешного ввода почты для восстановлениея пароля
  PASSWORD_RECOVERY_SUCCESS_PAGE: '/lk/auth/password/recovery/success',
  // Страница восставновления пароля
  PASSWORD_RECOVERY_CONFIRM_PAGE: '/lk/auth/password/recovery/confirm/:recovery_token',
  //! Статистика
  ADMIN_STATISTICS_GENERAL_PAGE: '/lk/admin/statistics/general-statictics',
  ADMIN_SETTINGS_REPORTS_PAGE: '/lk/admin/statistics/reports',
  //! Персонал
  ADMIN_STAFF_PAGE: '/lk/admin/staff/employees',
  ADMIN_STAFF_EMPLOYEES_PAGE: '/lk/admin/staff/employees',
  ADMIN_STAFF_EMPLOYEE_PAGE: `/lk/admin/staff/employees/:employee_id`,
  ADMIN_STAFF_EMPLOYEE_ASSIGNED_COURSES_PAGE: `/lk/admin/staff/employees/:employee_id/assigned-courses`,
  ADMIN_STAFF_EMPLOYEE_COURSES_COURSE_PAGE: `/lk/admin/staff/employees/:employee_id/courses/:course_id`,
  ADMIN_STAFF_EMPLOYEE_COURSES_COURSE_ATTEMPT_PAGE: `/lk/admin/staff/employees/:employee_id/courses/:course_id/attempts/:attempt_id`,
  ADMIN_STAFF_TAGS_PAGE: `/lk/admin/staff/tags`,
  //
  //
  //
  //
  // Я понял, для чего это, но срабоват ли тогда <Route? Чтоб потом еще и значение взять внутри страницы
  // Сделал сначала insertValuesIntoUrl и не заматил, что ты так сделал
  //
  //
  //
  //

  // ADMIN_STAFF_EMPLOYEE_COURSES_PAGE: (id: number | string): string =>
  // 	`/lk/admin/staff/employees/${id}/courses`,
  // ADMIN_STAFF_EMPLOYEE_DETAILS_COURSE_PAGE: (
  // 	employeeId: number | string,
  // 	courseId: string | number,
  // ): string => `/lk/admin/staff/employees/${employeeId}/courses/${courseId}`,
  // ADMIN_STAFF_TAGS_PAGE: '/lk/admin/staff/tags',
  // //! Вэб тютор
  // ADMIN_WEBTUTOR_PAGE: '/lk/admin/webtutor',
  // //! Страница обучения
  ADMIN_LEARNING_PAGE: '/lk/admin/learning/assigned',
  ADMIN_LEARNING_THEMES_PAGE: '/lk/admin/learning/themes',
  ADMIN_LEARNING_ASSIGNED_COURSE_CREATE_PAGE: `/lk/admin/learning/assigned/create`,
  ADMIN_LEARNING_ASSIGNED_COURSE_CREATE_PUBLIC_PAGE: `/lk/admin/learning/assigned/create-public`,
  ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PAGE: `/lk/admin/learning/assigned/select`,
  ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PUBLIC_PAGE: `/lk/admin/learning/assigned/select-public`,
  ADMIN_LEARNING_ASSIGNED_COURSE_PAGE: `/lk/admin/learning/assigned/:course_id`,
  ADMIN_LEARNING_ASSIGNED_COURSE_EMPLOYEES_PAGE: `/lk/admin/learning/assigned/:course_id/employees`,
  ADMIN_LEARNING_SHARED_COURSE_EMPLOYEES_PAGE: `/lk/admin/learning/shared/:course_id/employees`,
  ADMIN_CREATE_THEME_PAGE: '/lk/admin/learning/themes/new',
  ADMIN_EDIT_THEME_PAGE: '/lk/admin/learning/themes/:theme_id',
  ADMIN_CREATE_THEME_FOR_SECTION_PAGE: '/lk/admin/learning/themes/new/section/:section_id',
  ADMIN_CREATE_THEME_FOR_COURSE_SECTION_PAGE:
    '/lk/admin/learning/themes/new/course/:course_id/section/:section_id',
  ADMIN_EDIT_THEME_FOR_SECTION_PAGE: '/lk/admin/learning/themes/:theme_id/section/:section_id',
  ADMIN_CREATE_NEW_THEME_FOR_SECTION_PAGE:
    '/lk/admin/learning/themes/new/:theme_id/section/:section_id',
  ADMIN_EDIT_THEME_FOR_COURSE_SECTION_PAGE:
    '/lk/admin/learning/themes/:theme_id/course/:course_id/section/:section_id',
  ADMIN_EDIT_THEME_FOR_NEW_COURSE_SECTION_PAGE:
    '/lk/admin/learning/themes/new/:theme_id/course/:course_id/section/:section_id',
  ADMIN_LEARNING_COURSES_PAGE: '/lk/admin/learning/courses',
  ADMIN_LEARNING_MODULES_PAGE: '/lk/admin/learning/modules',
  ADMIN_LEARNING_MODULES_PREVIEW_PAGE: '/lk/admin/learning/modules/preview/:module_id',
  // //! Фишинг
  ADMIN_PHISHING_REDIRECT_PAGE: '/lk/admin/phishing/redirect',
  ADMIN_PHISHING_REDIRECT_CREATE_PAGE: '/lk/admin/phishing/redirect/create',
  ADMIN_PHISHING_REDIRECT_DETAIL_PAGE: '/lk/admin/phishing/redirect/detail/:id',
  ADMIN_PHISHING_REDIRECT_EDIT_PAGE: '/lk/admin/phishing/redirect/edit/:id',
  ADMIN_PHISHING_CAMPAIGNS_PAGE: '/lk/admin/phishing/campaigns',
  ADMIN_PHISHING_BY_TAGS_PAGE: '/lk/admin/phishing/phishing-by-tags',
  ADMIN_PHISHING_BY_TAGS_CAMPAIGN_PAGE: 'lk/admin/phishing/phishing-by-tags/campaigns/:campaign_id',
  ADMIN_PHISHING_BY_TAGS_STATICTICS_PAGE:
    'lk/admin/phishing/phishing-by-tags/campaigns/:campaign_id/statistics',
  ADMIN_PHISHING_BY_TAGSG_CAMPAIGN_STATISTIC_RESULTS_PAGE: `/lk/admin/phishing/phishing-by-tags/campaigns/:campaign_id/:template_id/statistics`,
  ADMIN_PHISHING_CAMPAIGNS_CREATE_PAGE: '/lk/admin/phishing/campaigns/create',
  ADMIN_PHISHING_CAMPAIGNS_BY_ENDING_CREATE_PAGE: '/lk/admin/phishing/campaigns/by-ending/create',
  ADMIN_PHISHING_CAMPAIGN_PAGE: `/lk/admin/phishing/campaigns/:campaign_id`,
  ADMIN_PHISHING_CAMPAIGN_STATISTIC_PAGE: `/lk/admin/phishing/campaigns/:campaign_id/statistics`,
  ADMIN_PHISHING_CAMPAIGN_STATISTIC_RESULTS_PAGE: `/lk/admin/phishing/campaigns/:campaign_id/:template_id/statistics`,
  // ADMIN_PHISHING_CAMPAIGN_PAGE: (camplaignId: string | number): string =>
  // 	`/lk/admin/phishing/campaigns/${camplaignId}`,
  // ADMIN_PHISHING_CAMPAIGN_STATISTIC_PAGE: (camplaignId: string | number): string =>
  // 	`/lk/admin/phishing/campaigns/${camplaignId}/statistics`,
  // ADMIN_PHISHING_CAMPAIGN_EMAIL_PAGE: (
  // 	camplaignId: string | number,
  // 	emailId: string | number,
  // ): string => `/lk/admin/phishing/campaigns/${camplaignId}/${emailId}/statistics`,
  ADMIN_PHISHING_TEMPLATES: '/lk/admin/phishing/templates',
  ADMIN_PHISHING_TEMPLATE: '/lk/admin/phishing/templates/:template_id',
  ADMIN_PHISHING_TEMPLATE_EDIT: '/lk/admin/phishing/templates/:template_id/edit',
  ADMIN_PHISHING_TEMPLATE_PAGE_EDIT: '/lk/admin/phishing/templates/:template_id/page/:page_id/edit',
  ADMIN_PHISHING_TEMPLATE_PAGE_CREATE: '/lk/admin/phishing/templates/:template_id/page/create',
  ADMIN_PHISHING_TEMPLATES_NEW: '/lk/admin/phishing/templates/new',
  ADMIN_PHISHING_TEMPLATES_EMAIL_CREATE: '/lk/admin/phishing/templates/:template_id/email/create',
  ADMIN_PHISHING_TEMPLATES_EMAIL_EDIT:
    '/lk/admin/phishing/templates/:template_id/email/:email_id/edit',
  // //! Организация
  ADMIN_ORGANIZATION_PAGE: '/lk/admin/organization',
  ADMIN_ORGANIZATION_BY_ID_PAGE: '/lk/admin/organization/:organization_id',
  ADMIN_ORGANIZATION_CREATE_PAGE: '/lk/admin/organization/create',
  ADMIN_ORGANIZATION_CREATE_DAUTHER_PAGE: '/lk/admin/organization/:organization_id/create',
  ADMIN_ORGANIZATION_EDIT_PAGE: '/lk/admin/organization/:organization_id/edit',
  ADMIN_ORGANIZATION_STATISTICS_PAGE: '/lk/admin/organization/:organization_id/statistics',

  // ADMIN_ORGANIZATION_BY_ID_PAGE: (organizationId: string | number): string =>
  // 	`/lk/admin/organization/${organizationId}`,
  // ADMIN_ORGANIZATION_CREATE_PAGE: '/lk/admin/organization/create',
  // ADMIN_ORGANIZATION_CREATE_FROM_ID_PAGE: (motherOrgId: string | number): string =>
  // 	`/lk/admin/organization/${motherOrgId}/create`,
  // ADMIN_ORGANIZATION_EDIT_PAGE: (
  // 	organizationId: string | number,
  // 	motherOrgId: string | number,
  // ): string => `/lk/admin/organization/${organizationId}/edit?from=${motherOrgId}`,
  // ADMIN_ORGANIZATION_DETAIL_PAGE: (organizationId: string | number): string =>
  // 	`/lk/admin/organization/${organizationId}`,
  // ADMIN_ORGANIZATION_STATISTICS_PAGE: (
  // 	organizationId: string | number,
  // 	motherOrgId: string,
  // ): string => `/lk/admin/organization/${organizationId}/statistics?from=${motherOrgId}`,
  //! Настройки
  ADMIN_SETTINGS_PAGE: '/lk/admin/settings/audit', //! Страница по умолчанию при клике на которую перенаправлять
  ADMIN_SETTINGS_ADFS_PAGE: '/lk/admin/settings/adfs',
  ADMIN_SETTINGS_EMAIL_PAGE: '/lk/admin/settings/adfs?type=email',
  ADMIN_SETTINGS_AD_PAGE: '/lk/admin/settings/adfs?type=active_directory',
  ADMIN_SETTINGS_GENERAL_PAGE: '/lk/admin/settings/adfs?type=general',
  ADMIN_SETTINGS_SUPERUSERS_PAGE: '/lk/admin/settings/adfs/:organization_id/superusers',
  ADMIN_SETTINGS_ACTION_LOG_PAGE: '/lk/admin/settings/audit',
  ADMIN_SETTINGS_LICENSE_PAGE: '/lk/admin/settings/license',
  ADMIN_SETTINGS_EMAILS_PAGE: '/lk/admin/settings/emails',
  ADMIN_INSTRUCTIONS_PAGE: '/lk/admin/instructions',
  ADMIN_CHANGE_LOG_PAGE: '/lk/admin/change-log',
  //! Сотрудника
  //! Мои курса
  USER_STATISTICS: '/lk/user/statistics',
  USER_PROFILE: '/lk/user/profile',
  ADMIN_PROFILE: '/lk/admin/profile',
  USER_MY_COURSES_PAGE: '/lk/user/learning',
  USER_MY_COURSES_LEARNING_PAGE: '/lk/user/learning/courses/:course_id',
  USER_MY_COURSE_SCORM_MODULES_PAGE: '/lk/user/learning/courses/scorm/:course_id',
  USER_MY_COURSE_MODULES_BY_ID_PAGE: '/lk/user/learning/courses/:course_id/modules/:module_id',
  USER_MY_COURSE_MODULES_BY_ID_QUIZ_PAGE:
    '/lk/user/learning/courses/:course_id/modules/:module_id/quiz/:quiz_id',
  USER_MY_COURSE_MODULES_BY_ID_QUIZ_RESULT_PAGE:
    '/lk/user/learning/courses/:course_id/modules/:module_id/quiz/:quiz_id/result',
  USER_MY_SCORM_COURSE_PAGE: '/lk/user/learning/courses/scorm/:course_id',

  //! ДОЛЖНО БЫТЬ В КОНЦЕ
  NOT_FOUND_PAGE: '*',
}

export const getPhishingRedirectEditUrl = (id: string | number) =>
  URLS.ADMIN_PHISHING_REDIRECT_EDIT_PAGE.replace(':id', String(id))

export const getPhishingRedirectDetailUrl = (id: string | number) =>
  URLS.ADMIN_PHISHING_REDIRECT_DETAIL_PAGE.replace(':id', String(id))

export const getAssignedCoursesDetailStatisticsByIdUrl = (id: string | number) =>
  URLS.ADMIN_LEARNING_ASSIGNED_COURSE_PAGE.replace(':course_id', String(id))

export const getAssignedCoursesDetailStatisticsEmployyesByIdUrl = (id: string | number) =>
  URLS.ADMIN_LEARNING_ASSIGNED_COURSE_EMPLOYEES_PAGE.replace(':course_id', String(id))

export const getSharedCoursesDetailStatisticsEmployyesByIdUrl = (id: string | number) =>
  URLS.ADMIN_LEARNING_SHARED_COURSE_EMPLOYEES_PAGE.replace(':course_id', String(id))

export const getEmployeeDetailsByIdUrl = (id: string | number) =>
  URLS.ADMIN_STAFF_EMPLOYEE_PAGE.replace(':employee_id', String(id))

export const getThemeDetailById = (id: string | number) =>
  URLS.ADMIN_EDIT_THEME_PAGE.replace(':theme_id', String(id))

export const getMyCoursesLearningByIdUrl = (id: string | number) =>
  URLS.USER_MY_COURSES_LEARNING_PAGE.replace(':course_id', String(id))

export const getCoursePreview = (id: string | number) =>
  URLS.ADMIN_PREVIEW_COURSE_PAGE.replace(':course_id', String(id))

export const getAdminEditThemeForCourseSection = ({
  course_id,
  section_id,
  theme_id,
}: {
  theme_id: string | number
  course_id: string | number
  section_id: string | number
}) =>
  URLS.ADMIN_EDIT_THEME_FOR_COURSE_SECTION_PAGE.replace(':theme_id', String(theme_id))
    .replace(':course_id', String(course_id))
    .replace(':section_id', String(section_id))

export const getAdminEditThemeWithSection = ({
  section_id,
  theme_id,
}: {
  theme_id: string | number
  section_id: string | number
}) =>
  URLS.ADMIN_EDIT_THEME_FOR_SECTION_PAGE.replace(':theme_id', String(theme_id)).replace(
    ':section_id',
    String(section_id),
  )

export type TURLS = keyof typeof URLS
