import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import Backend from 'i18next-http-backend'
import LanguageDetector from 'i18next-browser-languagedetector'
import intervalPlural from 'i18next-intervalplural-postprocessor'
import {
  DEFAULT_SUPPORTED_LANGUAGE,
  SUPPORTED_LANGUAGES,
} from '@/shared/constants/supportedLanguages'

const DEFAULT_NAMESPACES = ['translation', 'commons', 'tips', 'global_errors']

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(intervalPlural)
  .use(initReactI18next)
  .init({
    fallbackLng: DEFAULT_SUPPORTED_LANGUAGE.code,
    supportedLngs: SUPPORTED_LANGUAGES.map(language => language.code),
    ns: DEFAULT_NAMESPACES,
    backend: {
      loadPath: (lng: string, ns: string | string[]) => {
        const namespace = Array.isArray(ns) ? ns[0] : ns

        const path = namespace.split('__').join('/')
        return `/locales/${lng}/${path}.json`
      },
    },
    interpolation: {
      escapeValue: false,
    },
  })

i18n.services.pluralResolver.addRule('uz', {
  numbers: [1, 2],
  plurals: function (n: number) {
    if (n === 1) {
      return 0
    }
    return 1
  },
})

// Listener for trigger HMR when update 'public/locales/**/*' files
if (import.meta.hot) {
  import.meta.hot.on('locales-update', () => {
    i18n.reloadResources().then(() => {
      i18n.changeLanguage(i18n.language)
    })
  })
}

export default i18n
