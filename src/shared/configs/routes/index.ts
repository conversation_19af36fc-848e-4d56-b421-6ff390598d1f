/* eslint-disable @typescript-eslint/no-explicit-any */
import { lazy } from 'react'

import { URLS } from '@/shared/configs/urls/index'

export interface IRoute {
  needAuth: boolean
  path: string
  permission?: string
  element: React.LazyExoticComponent<React.ComponentType<any>>
}

export const routes: IRoute[] = [
  {
    needAuth: false,
    path: URLS.POLICY,
    element: lazy(async () => await import('@/pages/common/policy-error')),
  },
  {
    needAuth: false,
    path: URLS.PRIVACY_POLICY,
    element: lazy(async () => await import('@/pages/common/privacy-policy')),
  },
  {
    needAuth: false,
    path: URLS.AGREEMENT,
    element: lazy(async () => await import('@/pages/common/agreement')),
  },
  {
    needAuth: false,
    path: URLS.LOGIN_PAGE,
    element: lazy(async () => await import('@/pages/auth/login')),
  },
  {
    needAuth: false,
    path: URLS.REGISTRATION_PAGE,
    element: lazy(async () => await import('@/pages/auth/registration')),
  },
  {
    needAuth: false,
    path: URLS.PASSWORD_RECOVERY_PAGE,
    element: lazy(async () => await import('@/pages/auth/password-recovery')),
  },
  {
    needAuth: false,
    path: URLS.PASSWORD_RECOVERY_CONFIRM_PAGE,
    element: lazy(async () => await import('@/pages/auth/password-recovery-confirm')),
  },
  {
    needAuth: false,
    path: URLS.REGISTRATION_PAGE,
    element: lazy(async () => await import('@/pages/auth/registration')),
  },
  {
    needAuth: true,
    permission: 'admin.statistics',
    path: URLS.ADMIN_STATISTICS_GENERAL_PAGE,
    element: lazy(async () => await import('@/pages/admin/statistics')),
  },
  {
    needAuth: true,
    permission: 'admin.staff.tags',

    path: URLS.ADMIN_STAFF_TAGS_PAGE,
    element: lazy(async () => await import('@/pages/admin/employees-tags')),
  },
  {
    needAuth: true,
    permission: 'admin.staff.employees',
    path: URLS.ADMIN_STAFF_EMPLOYEES_PAGE,
    element: lazy(async () => await import('@/pages/admin/employees')),
  },
  {
    needAuth: true,
    permission: 'admin.staff.employees',
    path: URLS.ADMIN_STAFF_EMPLOYEE_PAGE,
    element: lazy(async () => await import('@/pages/admin/employee-statistics')),
  },
  {
    needAuth: true,
    permission: 'admin.staff.employees',
    path: URLS.ADMIN_STAFF_EMPLOYEE_ASSIGNED_COURSES_PAGE,
    element: lazy(async () => await import('@/pages/admin/employee-assigned-courses')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.assigned',
    path: URLS.ADMIN_LEARNING_ASSIGNED_COURSE_EMPLOYEES_PAGE,
    element: lazy(async () => await import('@/pages/admin/employee-course')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.assigned',
    path: URLS.ADMIN_LEARNING_SHARED_COURSE_EMPLOYEES_PAGE,
    element: lazy(async () => await import('@/pages/admin/shared-course-employees')),
  },
  {
    needAuth: true,
    permission: 'admin.staff.employees',
    path: URLS.ADMIN_STAFF_EMPLOYEE_COURSES_COURSE_PAGE,
    element: lazy(async () => await import('@/pages/admin/employee-course')),
  },
  {
    needAuth: true,
    permission: 'admin.learning',
    path: URLS.ADMIN_LEARNING_PAGE,
    element: lazy(async () => await import('@/pages/admin/assigned-courses')),
  },
  {
    needAuth: true,
    permission: 'admin.learning',
    path: URLS.ADMIN_LEARNING_THEMES_PAGE,
    element: lazy(async () => await import('@/pages/admin/learning/themes')),
  },
  {
    permission: 'admin.learning.assigned',
    needAuth: true,
    path: URLS.ADMIN_LEARNING_ASSIGNED_COURSE_PAGE,
    element: lazy(async () => await import('@/pages/admin/assigned-course')),
  },
  {
    permission: 'admin.learning.assigned',
    needAuth: true,
    path: URLS.ADMIN_LEARNING_ASSIGNED_COURSE_CREATE_PAGE,
    element: lazy(async () => await import('@/pages/admin/learning/assign/create/assign-course')),
  },
  {
    permission: 'admin.learning.assigned',
    needAuth: true,
    path: URLS.ADMIN_LEARNING_ASSIGNED_COURSE_CREATE_PUBLIC_PAGE,
    element: lazy(
      async () => await import('@/pages/admin/learning/assign/create/assign-public-course'),
    ),
  },
  {
    permission: 'admin.learning.assigned',
    needAuth: true,
    path: URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PAGE,
    element: lazy(async () => await import('@/pages/admin/learning/assign/create/select-courses')),
  },
  {
    permission: 'admin.learning.assigned',
    needAuth: true,
    path: URLS.ADMIN_LEARNING_ASSIGNED_COURSE_SELECT_PUBLIC_PAGE,
    element: lazy(
      async () => await import('@/pages/admin/learning/assign/create/select-public-courses'),
    ),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_LEARNING_COURSES_PAGE,
    element: lazy(async () => await import('@/pages/admin/learning/courses/courses')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_CREATE_COURSE_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-course')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_EDIT_COURSE_PAGE + ':course_id',
    element: lazy(async () => await import('@/pages/admin/create-course')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_PREVIEW_COURSE_PAGE,
    element: lazy(async () => await import('@/pages/admin/preview-course')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_CREATE_THEME_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-theme')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_EDIT_THEME_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-theme')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_CREATE_THEME_FOR_SECTION_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-theme')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_CREATE_THEME_FOR_COURSE_SECTION_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-theme')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_EDIT_THEME_FOR_SECTION_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-theme')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_CREATE_NEW_THEME_FOR_SECTION_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-theme')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_EDIT_THEME_FOR_COURSE_SECTION_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-theme')),
  },
  {
    needAuth: true,
    permission: 'admin.learning.courses',
    path: URLS.ADMIN_EDIT_THEME_FOR_NEW_COURSE_SECTION_PAGE,
    element: lazy(async () => await import('@/pages/admin/create-theme')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.campaigns',
    path: URLS.ADMIN_PHISHING_CAMPAIGNS_CREATE_PAGE,
    element: lazy(async () => await import('@/pages/admin/CreateCampaign')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.campaigns',
    path: URLS.ADMIN_PHISHING_CAMPAIGNS_BY_ENDING_CREATE_PAGE,
    element: lazy(
      async () => await import('@/pages/admin/CreateCampaign/create-campaign-by-ending'),
    ),
  },
  {
    needAuth: true,
    permission: 'admin.phishing',
    path: URLS.ADMIN_PHISHING_BY_TAGS_PAGE,
    element: lazy(async () => await import('@/pages/admin/phishing-by-tags')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing',
    path: URLS.ADMIN_PHISHING_BY_TAGS_CAMPAIGN_PAGE,
    element: lazy(async () => await import('@/pages/admin/phishing-by-tags-detail-page')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing',
    path: URLS.ADMIN_PHISHING_REDIRECT_PAGE,
    element: lazy(async () => await import('@/pages/admin/redirect-pages/index')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing',
    path: URLS.ADMIN_PHISHING_REDIRECT_CREATE_PAGE,
    element: lazy(async () => await import('@/pages/admin/redirect-pages/create')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing',
    path: URLS.ADMIN_PHISHING_REDIRECT_EDIT_PAGE,
    element: lazy(async () => await import('@/pages/admin/redirect-pages/edit')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing',
    path: URLS.ADMIN_PHISHING_REDIRECT_DETAIL_PAGE,
    element: lazy(async () => await import('@/pages/admin/redirect-pages/detail')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.campaigns',
    path: URLS.ADMIN_PHISHING_CAMPAIGNS_PAGE,
    element: lazy(async () => await import('@/pages/admin/phishing-campaigns')),
  },
  {
    needAuth: true,
    path: URLS.ADMIN_PHISHING_TEMPLATES,
    permission: 'admin.phishing.templates',
    element: lazy(async () => await import('@/pages/admin/phishing-templates')),
  },
  {
    needAuth: true,
    permission: 'admin.organization',
    path: URLS.ADMIN_ORGANIZATION_PAGE,
    element: lazy(async () => await import('@/pages/admin/organization')),
  },
  {
    needAuth: true,
    permission: 'admin.settings.audit',
    path: URLS.ADMIN_SETTINGS_PAGE,
    element: lazy(async () => await import('@/pages/admin/settings/action-log')),
  },
  {
    needAuth: true,
    permission: 'admin.settings.adfs',
    path: URLS.ADMIN_SETTINGS_ADFS_PAGE,
    element: lazy(async () => await import('@/pages/admin/settings/general-settings')),
  },
  {
    needAuth: true,
    permission: 'admin.settings.license',
    path: URLS.ADMIN_SETTINGS_LICENSE_PAGE,
    element: lazy(async () => await import('@/pages/admin/settings/license')),
  },

  {
    needAuth: true,
    permission: 'admin',
    path: URLS.ADMIN_SETTINGS_EMAILS_PAGE,
    element: lazy(async () => await import('@/pages/admin/settings/EmailTemplatesEdit')),
  },
  {
    needAuth: true,
    permission: 'admin',
    path: URLS.ADMIN_SETTINGS_REPORTS_PAGE,
    element: lazy(async () => await import('@/pages/admin/settings/reports')),
  },
  {
    needAuth: true,
    permission: 'admin',
    path: URLS.ADMIN_SETTINGS_SUPERUSERS_PAGE,
    element: lazy(async () => await import('@/pages/admin/settings/superusers')),
  },
  {
    needAuth: true,
    permission: 'admin',
    path: URLS.ADMIN_PROFILE,
    element: lazy(async () => await import('@/pages/lk/profile')),
  },
  {
    permission: 'user',
    needAuth: true,
    path: URLS.USER_PROFILE,
    element: lazy(async () => await import('@/pages/lk/profile')),
  },
  {
    needAuth: true,
    permission: 'user.statistics',
    path: URLS.USER_STATISTICS,
    element: lazy(async () => await import('@/pages/user/user-statistics')),
  },
  {
    needAuth: true,
    permission: 'user.learning',
    path: URLS.USER_MY_COURSES_PAGE,
    element: lazy(async () => await import('@/pages/user/user-courses')),
  },
  {
    needAuth: true,
    permission: 'user.learning',
    path: URLS.USER_MY_COURSES_LEARNING_PAGE,
    element: lazy(async () => await import('@/pages/user/learning')),
  },
  {
    needAuth: false,
    permission: '',
    path: URLS.GOTCHA,
    element: lazy(async () => await import('@/pages/common/phishing-gotcha')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.campaigns',
    path: URLS.ADMIN_PHISHING_CAMPAIGN_PAGE,
    element: lazy(async () => await import('@/pages/admin/phishing-campaign-detail-page')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.campaigns',
    path: URLS.ADMIN_PHISHING_CAMPAIGN_STATISTIC_PAGE,
    element: lazy(async () => await import('@/pages/admin/campaign-statictics')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.campaigns',
    path: URLS.ADMIN_PHISHING_BY_TAGS_STATICTICS_PAGE,
    element: lazy(async () => await import('@/pages/admin/campaign-by-tags-statictics')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.campaigns',
    path: URLS.ADMIN_PHISHING_BY_TAGSG_CAMPAIGN_STATISTIC_RESULTS_PAGE,
    element: lazy(
      async () => await import('@/pages/admin/phishing-by-tags-campaign-result-statistics'),
    ),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.campaigns',
    path: URLS.ADMIN_PHISHING_CAMPAIGN_STATISTIC_RESULTS_PAGE,
    element: lazy(async () => await import('@/pages/admin/phishing-campaign-result-statistics')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.templates',
    path: URLS.ADMIN_PHISHING_TEMPLATE,
    element: lazy(async () => await import('@/pages/admin/phishing-template')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.templates',
    path: URLS.ADMIN_PHISHING_TEMPLATE_EDIT,
    element: lazy(async () => await import('@/pages/admin/phishing-templates-edit')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.templates',
    path: URLS.ADMIN_PHISHING_TEMPLATE_PAGE_EDIT,
    element: lazy(async () => await import('@/pages/admin/template-email-page-landing-form')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.templates',
    path: URLS.ADMIN_PHISHING_TEMPLATE_PAGE_CREATE,
    element: lazy(async () => await import('@/pages/admin/template-email-page-landing-form')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.templates',
    path: URLS.ADMIN_PHISHING_TEMPLATES_EMAIL_CREATE,
    element: lazy(async () => await import('@/pages/admin/phishing-template-email-form')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.templates',
    path: URLS.ADMIN_PHISHING_TEMPLATES_EMAIL_EDIT,
    element: lazy(async () => await import('@/pages/admin/phishing-template-email-form')),
  },
  {
    needAuth: true,
    permission: 'admin.organization',
    path: URLS.ADMIN_ORGANIZATION_CREATE_PAGE,
    element: lazy(async () => await import('@/pages/admin/organization-create')),
  },
  {
    needAuth: true,
    permission: 'admin.organization',
    path: URLS.ADMIN_ORGANIZATION_CREATE_DAUTHER_PAGE,
    element: lazy(async () => await import('@/pages/admin/organization-create')),
  },
  {
    needAuth: true,
    permission: 'admin.organization',
    path: URLS.ADMIN_ORGANIZATION_BY_ID_PAGE,
    element: lazy(async () => await import('@/pages/admin/organization')),
  },
  {
    needAuth: true,
    permission: 'admin.organization',
    path: URLS.ADMIN_ORGANIZATION_EDIT_PAGE,
    element: lazy(async () => await import('@/pages/admin/organization-edit')),
  },
  {
    needAuth: true,
    permission: 'admin.organization',
    path: URLS.ADMIN_ORGANIZATION_STATISTICS_PAGE,
    element: lazy(async () => await import('@/pages/admin/organization-statistics')),
  },
  {
    needAuth: true,
    permission: 'admin.phishing.templates',
    path: URLS.ADMIN_PHISHING_TEMPLATES_NEW,
    element: lazy(async () => await import('@/pages/admin/PhishingTemplatesCreate')),
  },
  {
    needAuth: true,
    permission: 'admin',
    path: URLS.ADMIN_INSTRUCTIONS_PAGE,
    element: lazy(async () => await import('@/pages/admin/instructions')),
  },
  {
    needAuth: true,
    permission: 'admin',
    path: URLS.ADMIN_CHANGE_LOG_PAGE,
    element: lazy(async () => await import('@/pages/admin/change-log')),
  },
]
