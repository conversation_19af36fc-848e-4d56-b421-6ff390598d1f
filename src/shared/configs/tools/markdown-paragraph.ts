import { API, BlockTool, BlockToolConstructorOptions } from '@editorjs/editorjs'
import { marked } from 'marked'

interface MarkdownParagraphData {
  text: string
}

interface MarkdownParagraphConfig {
  placeholder?: string
  preserveBlank?: boolean
}

class MarkdownParagraph implements BlockTool {
  private static DEFAULT_PLACEHOLDER = ''

  private api: API
  private readOnly: boolean
  private placeholder: string
  private preserveBlank: boolean
  private element: HTMLDivElement | null = null
  private data: MarkdownParagraphData

  constructor({
    data,
    config,
    api,
    readOnly,
  }: BlockToolConstructorOptions<MarkdownParagraphData, MarkdownParagraphConfig>) {
    this.api = api
    this.readOnly = readOnly || false
    this.placeholder = config?.placeholder || MarkdownParagraph.DEFAULT_PLACEHOLDER
    this.preserveBlank = config?.preserveBlank || false
    this.data = data || { text: '' }

    if (!this.readOnly) {
      this.onKeyUp = this.onKeyUp.bind(this)
    }
  }

  public static get isReadOnlySupported(): boolean {
    return true
  }

  private drawView(): HTMLDivElement {
    const wrapper = document.createElement('div')

    wrapper.classList.add(this.api.styles.block, 'ce-paragraph')
    wrapper.contentEditable = this.readOnly ? 'false' : 'true'
    wrapper.dataset.placeholderActive = this.api.i18n.t(this.placeholder)

    if (this.data.text) {
      wrapper.innerHTML = this.convertMarkdownToHTML(this.data.text)
    }

    if (!this.readOnly) {
      wrapper.addEventListener('keyup', this.onKeyUp)
    }

    return wrapper
  }

  public render(): HTMLElement {
    this.element = this.drawView()
    return this.element
  }

  public save(blockContent: HTMLElement): MarkdownParagraphData {
    return {
      text: this.convertMarkdownToHTML(blockContent.innerHTML?.trim()) || '',
    }
  }

  public validate(savedData: MarkdownParagraphData): boolean {
    return !(savedData.text.trim() === '' && !this.preserveBlank)
  }

  private onKeyUp(event: KeyboardEvent): void {
    if ((event.code === 'Backspace' || event.code === 'Delete') && this.element) {
      const { textContent } = this.element

      if (textContent?.trim() === '') {
        this.element.innerHTML = ''
      }
    }
  }

  private convertMarkdownToHTML(markdown: string): string {
    const lf = '\u{000A}'
    const regex = /(<\/?br\s*\/?>)-/gim

    // 1. find the string pattern `<br>-` (editorjs convert lf(unicode\U000A) -> <br/>)
    // 2. replace with `lf(\U000A)-` for markdown parse in list (<ul><li></li></ul>)

    const result = markdown.replace(regex, lf + '-')

    let html = marked.parse(result) as string
    html = html.replace(
      /<a\s+href="(.*?)"(.*?)>/gim,
      '<a href="$1" target="_blank" rel="noopener noreferrer"$2>',
    )

    return html
  }
  public merge(data: MarkdownParagraphData): void {
    if (this.element) {
      this.data.text += data.text
      const contentFragment = document
        .createRange()
        .createContextualFragment(this.convertMarkdownToHTML(data.text))
      this.element?.appendChild(contentFragment)
      this.element?.normalize()
    }
  }

  public static get pasteConfig(): { tags: string[] } {
    return {
      tags: ['P'],
    }
  }

  public static get conversionConfig() {
    return {
      export: 'text',
      import: 'text',
    }
  }

  public static get sanitize() {
    return {
      text: {
        br: false,
        strong: false,
        em: false,
        b: false,
        i: false,
        ul: false,
        ol: false,
        li: false,
        a: false,
        h1: false,
        h2: false,
        h3: false,
        h4: false,
        h5: false,
        h6: false,
      },
    }
  }

  public static get toolbox() {
    return {
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M8 9V7.2C8 7.08954 8.08954 7 8.2 7L12 7M16 9V7.2C16 7.08954 15.9105 7 15.8 7L12 7M12 7L12 17M12 17H10M12 17H14"/></svg>',
      title: 'Markdown Text',
    }
  }
}

export default MarkdownParagraph
