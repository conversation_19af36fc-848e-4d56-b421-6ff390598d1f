import Paragraph from '@editorjs/paragraph'
import EditorjsList from '@editorjs/list'
import ColorPlugin from 'editorjs-text-color-plugin'
import Header from '@editorjs/header'
import i18n from 'i18next'
import CustomImageTool from '@/shared/components/editor/tools/custom-image-tool/custom-image-size-limit'
import CustomAudioTool from '@/shared/components/editor/tools/custom-audio-tool/custom-audio-size-limit'
import CustomVideoTool from '@/shared/components/editor/tools/custom-video-tool/custom-video-size-limit'
import CustomGalleryTool from '@/shared/components/editor/tools/custom-gallery-tool/custom-gallery-size-limit'
import MdParagraph from './markdown-paragraph'
import CustomCodeTool from '@/shared/components/editor/tools/custom-code-tool/custom-code-tool'

const paragraphIcon = `<svg width='36' height='36' class='codex-editor__tool-icon' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'>
    <rect width='36' height='36' rx='6' fill='#F0F3F7' />
    <path
      d='M8.84 25L10.16 21.56H16.12L17.42 25H20.08L14.56 11H11.8L6.26 25H8.84ZM13.12 13.74H13.16L15.28 19.36H11L13.12 13.74ZM25.4955 15.16C23.6555 15.16 22.3755 16.14 21.6355 17.02L23.0155 18.34C23.5755 17.74 24.3355 17.12 25.4355 17.12C26.5355 17.12 27.3155 17.86 27.3355 18.74V18.76L24.1155 19.32C22.2155 19.66 21.1155 20.78 21.1155 22.4C21.1155 23.9 22.4355 25.2 24.3555 25.2C25.8355 25.2 26.9755 24.32 27.4155 23.66H27.4355V25H29.5955V18.9C29.5955 16.8 27.9555 15.16 25.4955 15.16ZM24.8755 23.34C23.9355 23.34 23.3955 22.78 23.3955 22.12C23.3955 21.56 23.8755 21.06 24.7955 20.9L27.3155 20.42V20.74C27.3155 22.24 26.2355 23.34 24.8755 23.34Z'
      fill='#8E97AF'
    />
  </svg>`

const headerIcon = `<svg width="36" height="36" class='codex-editor__tool-icon' viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="36" height="36" rx="6" fill="#F0F3F7"/>
    <path d="M11.42 25V19.04H17.98V25H20.48V11H17.98V16.74H11.42V11H8.92V25H11.42ZM28.2697 25V11H26.2697C25.8297 11.86 25.0497 12.78 23.1097 12.88V14.74C24.3897 14.74 25.1697 14.48 25.7697 14.04V25H28.2697Z" fill="#8E97AF"/>
  </svg>
`

const listIcon = `<svg width="36" height="36" class='codex-editor__tool-icon' viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="36" height="36" rx="6" fill="#F0F3F7"/>
    <circle cx="9.5" cy="12.5" r="1.5" fill="#8E97AF"/>
    <rect x="13" y="11" width="16" height="3" rx="1.5" fill="#8E97AF"/>
    <circle cx="9.5" cy="18" r="1.5" fill="#8E97AF"/>
    <rect x="13" y="16.5" width="16" height="3" rx="1.5" fill="#8E97AF"/>
    <circle cx="9.5" cy="23.5" r="1.5" fill="#8E97AF"/>
    <rect x="13" y="22" width="16" height="3" rx="1.5" fill="#8E97AF"/>
  </svg>
`

const imageIcon = `<svg width="36" height="36" class='codex-editor__tool-icon' viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="36" height="36" rx="6" fill="#F0F3F7"/>
    <rect x="7" y="9" width="22" height="18" rx="1" stroke="#8E97AF" stroke-width="2"/>
    <path d="M7 24.9999L12.6528 18.8087C13.0494 18.3744 13.7332 18.3744 14.1298 18.8087L17.2509 22.2271C17.6353 22.6481 18.2934 22.6631 18.6965 22.2599L28.9565 11.9998" stroke="#8E97AF" stroke-width="2"/>
    <circle cx="18.5" cy="13.5" r="0.75" stroke="#8E97AF" stroke-width="1.5"/>
  </svg>
`

const audioIcon = `<svg width="36" height="36" class='codex-editor__tool-icon' viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="36" height="36" rx="6" fill="#F0F3F7"/>
    <path d="M8 18C8 16.6868 8.25866 15.3864 8.7612 14.1732C9.26375 12.9599 10.0003 11.8575 10.9289 10.9289C11.8575 10.0003 12.9599 9.26375 14.1732 8.7612C15.3864 8.25866 16.6868 8 18 8C19.3132 8 20.6136 8.25866 21.8268 8.7612C23.0401 9.26375 24.1425 10.0003 25.0711 10.9289C25.9997 11.8575 26.7362 12.9599 27.2388 14.1732C27.7413 15.3864 28 16.6868 28 18" stroke="#8E97AF" stroke-width="2"/>
    <path d="M7 18H10C10.5523 18 11 18.4477 11 19V27C11 27.5523 10.5523 28 10 28H9C7.89543 28 7 27.1046 7 26V18Z" fill="#8E97AF"/>
    <path d="M25 19C25 18.4477 25.4477 18 26 18H29V26C29 27.1046 28.1046 28 27 28H26C25.4477 28 25 27.5523 25 27V19Z" fill="#8E97AF"/>
    <path d="M14 24.5L15.5 21C16.1667 23.1667 17.5 27.4 17.5 27C17.5 26.6 18.5 21.5 19 19L20.5 24.5L22 22" stroke="#8E97AF" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
`

const videoIcon = `<svg width="36" height="36" class='codex-editor__tool-icon' viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="36" height="36" rx="6" fill="#F0F3F7"/>
    <circle cx="18" cy="18" r="9" stroke="#8E97AF" stroke-width="2"/>
    <path d="M21.6142 17.2407L17.1508 13.415C16.5021 12.859 15.5 13.3199 15.5 14.1742V21.8258C15.5 22.6801 16.5021 23.141 17.1508 22.585L21.6142 18.7593C22.0798 18.3602 22.0798 17.6398 21.6142 17.2407Z" fill="#8E97AF"/>
  </svg>
`

const codeIcon = `<svg width="36" height="36" class='codex-editor__tool-icon' viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="36" height="36" rx="6" fill="#F0F3F7"/>
<path d="M12 23L8.49976 18.6247C8.20758 18.2595 8.20758 17.7405 8.49976 17.3753L12 13" stroke="#8E97AF" stroke-width="2" stroke-linecap="round"/>
<path d="M24 23L27.5002 18.6247C27.7924 18.2595 27.7924 17.7405 27.5002 17.3753L24 13" stroke="#8E97AF" stroke-width="2" stroke-linecap="round"/>
<path d="M19.5859 13L15.9999 23.449" stroke="#8E97AF" stroke-width="2" stroke-linecap="round"/>
</svg>
`

const galleryIcon = `
<svg width="36" height="36" class='codex-editor__tool-icon' viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="36" height="36" rx="6" fill="#F0F3F7"/>
<mask id="path-2-inside-1_12510_23176" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.61719 11.0831C7.69972 11.0727 7.78382 11.0674 7.86916 11.0674H24.6692C25.7737 11.0674 26.6692 11.9628 26.6692 13.0674V26.4007C26.6692 26.4857 26.6639 26.5694 26.6536 26.6516C27.6392 26.5277 28.4016 25.6865 28.4016 24.6673V11.334C28.4016 10.2294 27.5062 9.33398 26.4016 9.33398H9.6016C8.582 9.33398 7.7406 10.097 7.61719 11.0831Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.61719 11.0831C7.69972 11.0727 7.78382 11.0674 7.86916 11.0674H24.6692C25.7737 11.0674 26.6692 11.9628 26.6692 13.0674V26.4007C26.6692 26.4857 26.6639 26.5694 26.6536 26.6516C27.6392 26.5277 28.4016 25.6865 28.4016 24.6673V11.334C28.4016 10.2294 27.5062 9.33398 26.4016 9.33398H9.6016C8.582 9.33398 7.7406 10.097 7.61719 11.0831Z" fill="#F0F3F7"/>
<path d="M7.61719 11.0831L5.63267 10.8347L5.31307 13.3885L7.86664 13.0675L7.61719 11.0831ZM26.6536 26.6516L24.6691 26.4032L24.3495 28.957L26.903 28.636L26.6536 26.6516ZM7.86664 13.0675C7.86667 13.0675 7.86686 13.0675 7.86724 13.0674C7.86763 13.0674 7.86826 13.0674 7.86916 13.0674V9.06738C7.70022 9.06738 7.53281 9.07797 7.36773 9.09872L7.86664 13.0675ZM7.86916 13.0674H24.6692V9.06738H7.86916V13.0674ZM24.6692 13.0674H24.6692H28.6692C28.6692 10.8582 26.8783 9.06738 24.6692 9.06738V13.0674ZM24.6692 13.0674V26.4007H28.6692V13.0674H24.6692ZM24.6692 26.4007C24.6692 26.4024 24.6691 26.4032 24.6691 26.4032L28.6381 26.9C28.6587 26.7356 28.6692 26.5689 28.6692 26.4007H24.6692ZM26.4016 24.6673C26.4016 24.6698 26.4014 24.6704 26.4015 24.6696C26.4017 24.6689 26.4019 24.668 26.4023 24.6671C26.4029 24.6654 26.4032 24.6656 26.4021 24.6668C26.4009 24.6681 26.3999 24.6687 26.4003 24.6685C26.4006 24.6683 26.4012 24.668 26.4022 24.6677C26.4027 24.6675 26.4032 24.6674 26.4035 24.6673C26.4039 24.6672 26.4042 24.6672 26.4041 24.6672L26.903 28.636C28.8763 28.3879 30.4016 26.7073 30.4016 24.6673H26.4016ZM26.4016 11.334V24.6673H30.4016V11.334H26.4016ZM26.4016 11.334H26.4016H30.4016C30.4016 9.12484 28.6107 7.33398 26.4016 7.33398V11.334ZM9.6016 11.334H26.4016V7.33398H9.6016V11.334ZM9.60171 11.3315C9.60171 11.3314 9.60168 11.3316 9.6016 11.332C9.60151 11.3324 9.60139 11.3329 9.60124 11.3334C9.60092 11.3343 9.6006 11.335 9.60044 11.3353C9.60027 11.3357 9.60078 11.3347 9.60213 11.3335C9.60336 11.3324 9.60352 11.3327 9.60185 11.3333C9.60095 11.3337 9.60003 11.3339 9.59933 11.334C9.59856 11.3342 9.59914 11.334 9.6016 11.334V7.33398C7.56089 7.33398 5.87976 8.8604 5.63267 10.8347L9.60171 11.3315Z" fill="#8E97AF" mask="url(#path-2-inside-1_12510_23176)"/>
<circle cx="18.4348" cy="14.1008" r="0.75" stroke="#8E97AF" stroke-width="1.1"/>
<mask id="path-5-inside-2_12510_23176" fill="white">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.2148 8.48252C10.2974 8.47214 10.3815 8.4668 10.4668 8.4668H27.2668C28.3714 8.4668 29.2668 9.36223 29.2668 10.4668V23.8001C29.2668 23.8851 29.2615 23.9688 29.2512 24.051C30.2369 23.9271 30.9993 23.086 30.9993 22.0667V8.7334C30.9993 7.62883 30.1038 6.7334 28.9993 6.7334H12.1993C11.1797 6.7334 10.3383 7.49637 10.2148 8.48252Z"/>
</mask>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.2148 8.48252C10.2974 8.47214 10.3815 8.4668 10.4668 8.4668H27.2668C28.3714 8.4668 29.2668 9.36223 29.2668 10.4668V23.8001C29.2668 23.8851 29.2615 23.9688 29.2512 24.051C30.2369 23.9271 30.9993 23.086 30.9993 22.0667V8.7334C30.9993 7.62883 30.1038 6.7334 28.9993 6.7334H12.1993C11.1797 6.7334 10.3383 7.49637 10.2148 8.48252Z" fill="#F0F3F7"/>
<path d="M10.2148 8.48252L8.23032 8.23416L7.91073 10.7879L10.4643 10.4669L10.2148 8.48252ZM29.2512 24.051L27.2667 23.8027L26.9471 26.3564L29.5007 26.0354L29.2512 24.051ZM10.4643 10.4669C10.4643 10.4669 10.4645 10.4669 10.4649 10.4668C10.4653 10.4668 10.4659 10.4668 10.4668 10.4668V6.4668C10.2979 6.4668 10.1305 6.47738 9.96539 6.49814L10.4643 10.4669ZM10.4668 10.4668H27.2668V6.4668H10.4668V10.4668ZM27.2668 10.4668H27.2668H31.2668C31.2668 8.25766 29.476 6.4668 27.2668 6.4668V10.4668ZM27.2668 10.4668V23.8001H31.2668V10.4668H27.2668ZM27.2668 23.8001C27.2668 23.8018 27.2667 23.8026 27.2667 23.8027L31.2358 24.2994C31.2563 24.135 31.2668 23.9684 31.2668 23.8001H27.2668ZM28.9993 22.0667C28.9993 22.0692 28.9991 22.0698 28.9992 22.069C28.9993 22.0683 28.9996 22.0674 28.9999 22.0665C29.0006 22.0648 29.0008 22.065 28.9998 22.0662C28.9986 22.0676 28.9976 22.0681 28.9979 22.0679C28.9982 22.0677 28.9989 22.0674 28.9999 22.0671C29.0004 22.0669 29.0008 22.0668 29.0012 22.0667C29.0016 22.0667 29.0018 22.0666 29.0018 22.0666L29.5007 26.0354C31.474 25.7873 32.9993 24.1067 32.9993 22.0667H28.9993ZM28.9993 8.7334V22.0667H32.9993V8.7334H28.9993ZM28.9993 8.7334H28.9993H32.9993C32.9993 6.52426 31.2084 4.7334 28.9993 4.7334V8.7334ZM12.1993 8.7334H28.9993V4.7334H12.1993V8.7334ZM12.1994 8.73088C12.1994 8.73085 12.1993 8.73106 12.1993 8.73146C12.1992 8.73185 12.199 8.7323 12.1989 8.73277C12.1986 8.73376 12.1983 8.73444 12.1981 8.73474C12.1979 8.73507 12.1984 8.73411 12.1998 8.7329C12.201 8.73181 12.2012 8.73208 12.1995 8.73273C12.1986 8.73307 12.1977 8.73333 12.197 8.73346C12.1962 8.7336 12.1968 8.7334 12.1993 8.7334V4.7334C10.1585 4.7334 8.47741 6.25981 8.23032 8.23416L12.1994 8.73088Z" fill="#8E97AF" mask="url(#path-5-inside-2_12510_23176)"/>
<rect x="6" y="12.9336" width="18.8" height="15.3333" rx="1" fill="#F0F3F7" stroke="#8E97AF" stroke-width="2"/>
<path d="M5.86719 26.6672L10.6678 21.4094C11.0644 20.975 11.7483 20.975 12.1448 21.4094L14.6572 24.161C15.0416 24.5821 15.6997 24.597 16.1028 24.1939L24.8962 15.4004" stroke="#8E97AF" stroke-width="2"/>
<circle cx="15.8332" cy="16.7001" r="0.75" stroke="#8E97AF" stroke-width="1.1"/>
</svg>`

class CustomList extends EditorjsList {
  constructor({ data, config, api, readOnly, block }) {
    const newData = {
      ...data,
      meta: { ...(data.meta || {}) },
      items: data.items || [
        {
          content: '',
          items: [],
        },
      ],
    }

    if (newData.style === 'ordered') {
      delete newData.meta.counterType
      delete newData.meta.start
    }

    const newConfig = {
      ...config,
      checklist: false,
      counter: false,
      start: false,
    }

    super({
      data: newData,
      config: newConfig,
      api,
      readOnly,
      block,
    })
  }

  renderSettings() {
    const defaultTunes = super.renderSettings() || []

    return defaultTunes.filter(
      item =>
        item.label !== 'Checklist' && item.label !== 'Counter type' && item.label !== 'Start with',
    )
  }
}

export const getEditorJsTools = (isReadOnly = false) => ({
  paragraph: {
    class: isReadOnly ? MdParagraph : Paragraph,
    toolbox: {
      title: i18n.t('commons:tools.text'),
      icon: paragraphIcon,
    },
    inlineToolbar: true,
  },
  Color: {
    class: ColorPlugin,
    config: {
      colorCollections: [
        '#EC7878',
        '#9C27B0',
        '#673AB7',
        '#3F51B5',
        '#0070FF',
        '#03A9F4',
        '#00BCD4',
        '#4CAF50',
        '#8BC34A',
        '#CDDC39',
        '#FFF',
      ],
      defaultColor: '#FF1300',
      type: 'text', // 'text' или 'background'
      customPicker: true,
    },
  },
  header: {
    class: Header,
    toolbox: {
      title: i18n.t('commons:tools.header'),
      icon: headerIcon,
    },
    inlineToolbar: true,
    config: {
      levels: [1, 2, 3, 4],
      defaultLevel: 1,
    },
  },
  list: {
    class: CustomList,
    inlineToolbar: true,
    toolbox: {
      title: i18n.t('commons:tools.list'),
      icon: listIcon,
    },
  },
  image: {
    class: CustomImageTool,
    toolbox: {
      title: i18n.t('commons:tools.image'),
      icon: imageIcon,
    },
  },
  code: {
    class: CustomCodeTool,
    toolbox: {
      title: i18n.t('commons:tools.code'),
      icon: codeIcon,
    },
  },
  audio: {
    class: CustomAudioTool,
    toolbox: {
      title: i18n.t('commons:tools.audio'),
      icon: audioIcon,
    },
  },
  video: {
    class: CustomVideoTool,
    toolbox: {
      title: i18n.t('commons:tools.video'),
      icon: videoIcon,
    },
  },
  gallery: {
    class: CustomGalleryTool,
    toolbox: {
      title: i18n.t('commons:tools.gallery'),
      icon: galleryIcon,
    },
  },
})

export const getEditorPresentationTools = (isReadOnly = false) => ({
  header: {
    class: Header,
    toolbox: {
      title: i18n.t('commons:tools.header'),
      icon: headerIcon,
    },
  },
  image: {
    class: CustomImageTool,
    toolbox: {
      title: i18n.t('commons:tools.image'),
      icon: imageIcon,
    },
  },
  audio: {
    class: CustomAudioTool,
    toolbox: {
      title: i18n.t('commons:tools.audio'),
      icon: audioIcon,
    },
  },
  paragraph: {
    class: isReadOnly ? MdParagraph : Paragraph,
    toolbox: {
      title: i18n.t('commons:tools.text'),
      icon: paragraphIcon,
    },
  },
})

export const getEditorVideoTools = (isReadOnly = false) => ({
  header: {
    class: Header,
    toolbox: {
      title: i18n.t('commons:tools.header'),
      icon: headerIcon,
    },
  },
  video: {
    class: CustomVideoTool,
    toolbox: {
      title: i18n.t('commons:tools.video'),
      icon: videoIcon,
    },
  },
  paragraph: {
    class: isReadOnly ? MdParagraph : Paragraph,
    toolbox: {
      title: i18n.t('commons:tools.text'),
      icon: paragraphIcon,
    },
  },
})
