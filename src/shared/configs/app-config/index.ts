/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
export abstract class Config {
  // устанавливается node js сервером(смотреть index.ts сервера)
  useSSO!: boolean
  // устанавливается при наличии krb_file в настройках Active Directory
  use_sso!: boolean
  forse_saml!: boolean
  needPrivacyPolicyPage!: boolean
  needAgreementPage!: boolean
  logoUrl!: string
  apiUrl!: string
  apiHost!: string
  eventKeeperUrl!: string

  publicUrl!: string
  url?: string
  lang!: string

  analytics_enabled!: boolean
  yandex_metrika_id?: string | number
  google_analytics_id?: string
  theme: any
  darkLogoUrl?: string
  lightLogoUrl?: string
  // устанавливается по свичу в админке
  use_admin_ad?: boolean
  // устанавливается по свичу в админке
  use_admin_sso?: boolean

  featureFlags!: Record<string, boolean>
  title?: string
}

export class BaseConfig {
  configRaw!: any // TODO interface
  logoUrl
  eventKeeperUrl
  lang
  yandex_metrika_id
  apiUrl
  apiHost
  url
  useSSO
  analytics_enabled
  darkLogoUrl
  lightLogoUrl
  needPrivacyPolicyPage
  needAgreementPage
  use_sso = false
  forse_saml = false
  use_admin_ad = false
  use_admin_sso = false

  constructor(config?: any) {
    this.configRaw = config

    this.eventKeeperUrl = this.configRaw?.eventKeeperUrl
    this.logoUrl = this.configRaw?.logo?.dark || this.configRaw?.logo?.light
    this.lang = this.configRaw?.lang || 'ru'
    this.yandex_metrika_id = this.configRaw?.yandex_metrika_id
    this.apiUrl = this.configRaw?.apiUrl
    this.apiHost = this.configRaw?.apiHost
    this.url = this.configRaw?.url
    this.useSSO = !!this.configRaw?.useSSO
    this.use_sso = !!this.configRaw?.use_sso
    this.analytics_enabled = this.configRaw?.analytics_enabled
    this.darkLogoUrl = this?.configRaw?.logo?.dark
    this.lightLogoUrl = this?.configRaw?.logo?.light
    this.needPrivacyPolicyPage = this?.configRaw?.needPrivacyPolicyPage
    this.needAgreementPage = this?.configRaw?.needAgreementPage
    this.forse_saml = this?.configRaw?.forse_saml
    this.use_admin_ad = this?.configRaw?.use_admin_ad
    this.use_admin_sso = this?.configRaw?.use_admin_sso
  }

  publicUrl = '/lk'
  featureFlags = {}

  get theme() {
    return this.configRaw?.theme
  }
}
