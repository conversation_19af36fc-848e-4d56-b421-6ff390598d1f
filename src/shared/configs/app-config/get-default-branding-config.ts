import { generatePrimaryPalette } from '../../utils/colors/colors'

export const getDefaultBrandingConfig = (env: string = 'development') => ({
  id: 'secure-t',
  url: 'edu.sec-t.ru',
  title: 'Secure-T',
  description: 'Secure-T',
  logo: {
    light: 'https://storage.yandexcloud.net/secure-t-frontend-develop/secure-t.png',
    dark: 'https://storage.yandexcloud.net/secure-t-frontend-develop/secure-t.png',
  },
  theme: {
    id: 'default',
    colors: {
      ...generatePrimaryPalette('#3dbc87'),
    },
  },
  layout: false,
  analytics_enabled: true,
  yandex_metrika_id: '87651235',
  google_analytics_id: '',
  lang: 'ru',
  useSSO: env === 'offline' || env === 'offline-local' || false,
  apiUrl: process.env.API_HOST ?? 'https://edu.sec-t.ru',
  needPrivacyPolicyPage: true,
  needAgreementPage: true,
})
