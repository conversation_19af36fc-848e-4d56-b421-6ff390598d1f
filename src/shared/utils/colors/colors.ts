export const Colors = {
  blue: "var(--color-statistics-complementary)",
  green: "var(--color-statistics-good)",
  red: "var(--color-statistics-bad)",
  yellow: "var(--color-statistics-warning)",
  gray: "#dcdcdc",
  light_gray: "#eeeeee",
  dark_gray: "#a3a4a6",
};

class Rgb {
  r: number = 0;
  g: number = 0;
  b: number = 0;
  constructor(r: number, g: number, b: number) {
    this.r = r;
    this.g = g;
    this.b = b;
  }

  toString() {
    return `rgb(${Math.round(this.r)}, ${Math.round(this.g)}, ${Math.round(
      this.b
    )})`;
  }
}

function hexToRgb(hex: string): Rgb {
  hex = hex.trimLeft().trimRight().replace("#", "");

  let r = 0,
    g = 0,
    b = 0;

  if (hex.length == 3) {
    r = Number("0x" + hex[0] + hex[0]);
    g = Number("0x" + hex[1] + hex[1]);
    b = Number("0x" + hex[2] + hex[2]);
  } else if (hex.length == 6) {
    r = Number("0x" + hex[0] + hex[1]);
    g = Number("0x" + hex[2] + hex[3]);
    b = Number("0x" + hex[4] + hex[5]);
  }

  return new Rgb(r, g, b);
}

function colorMix(color1: Rgb, color2: Rgb, amount: number = 50) {
  const p = amount / 100;

  const r = (color2.r - color1.r) * p + color1.r;
  const g = (color2.g - color1.g) * p + color1.g;
  const b = (color2.b - color1.b) * p + color1.b;

  return new Rgb(r, g, b);
}

type PaletteRatios = Record<string, number>;
type Palette = Record<string, string>;

function generatePalette(
  color: string,
  palette: PaletteRatios
): Palette {
  const colorRgb = hexToRgb(color);
  const lightBaseRgb = hexToRgb("#ffffff");

  return Object.entries(palette)
    .map(([key, ratio]) => {
      const p = colorMix(lightBaseRgb, colorRgb, ratio);
      return [key, p.toString()] as [string, string];
    })
    .reduce(
      (all, [key, value]) => ({
        ...all,
        [key]: value,
      }),
      {} as Palette
    );
}

/**
 * @kind helper
 * @desc Функция, создаюшая из одного цвета палитру цветов разной насещенности
 * @param {string} primary - основной цвет, самый насыщенный
 * @returns {boolean} Одинаковые ли элементы внутри двух массивов
 */
export function generatePrimaryPalette(primary: string) {
  const palette = {
    primary: 100,
    "primary-90": 88.9,
    "primary-80": 77.9,
    "primary-70": 66.7,
    "primary-60": 55.9,
    "primary-50": 44.5,
    "primary-40": 33.5,
    "primary-30": 22.4,
    "primary-20": 11.1,
  };

  return generatePalette(primary, palette);
}
