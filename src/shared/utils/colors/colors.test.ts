import { generatePrimaryPalette } from './colors'

describe('[shared] [utils] generatePrimaryPalette', () => {
  it('Должен возвращать одинаковую палитру при одном цвете', () => {
    const pallete = generatePrimaryPalette('#53C394')

    expect(pallete).toEqual({
      primary: 'rgb(83, 195, 148)',
      'primary-90': 'rgb(102, 202, 160)',
      'primary-80': 'rgb(121, 208, 172)',
      'primary-70': 'rgb(140, 215, 184)',
      'primary-60': 'rgb(159, 221, 195)',
      'primary-50': 'rgb(178, 228, 207)',
      'primary-40': 'rgb(197, 235, 219)',
      'primary-30': 'rgb(216, 242, 231)',
      'primary-20': 'rgb(236, 248, 243)',
    })

    const pallete2 = generatePrimaryPalette('#FD0598')

    expect(pallete2).toEqual({
      primary: 'rgb(253, 5, 152)',
      'primary-90': 'rgb(253, 33, 163)',
      'primary-80': 'rgb(253, 60, 175)',
      'primary-70': 'rgb(254, 88, 186)',
      'primary-60': 'rgb(254, 115, 197)',
      'primary-50': 'rgb(254, 144, 209)',
      'primary-40': 'rgb(254, 171, 220)',
      'primary-30': 'rgb(255, 199, 232)',
      'primary-20': 'rgb(255, 227, 244)',
    })

    const pallete3 = generatePrimaryPalette('#333')

    expect(pallete3).toEqual({
      primary: 'rgb(51, 51, 51)',
      'primary-90': 'rgb(74, 74, 74)',
      'primary-80': 'rgb(96, 96, 96)',
      'primary-70': 'rgb(119, 119, 119)',
      'primary-60': 'rgb(141, 141, 141)',
      'primary-50': 'rgb(164, 164, 164)',
      'primary-40': 'rgb(187, 187, 187)',
      'primary-30': 'rgb(209, 209, 209)',
      'primary-20': 'rgb(232, 232, 232)',
    })
  })

  it('Должен возвращать одинаковую палитру при пустой строке', () => {
    const pallete = generatePrimaryPalette('')

    expect(pallete).toEqual({
      primary: 'rgb(0, 0, 0)',
      'primary-90': 'rgb(28, 28, 28)',
      'primary-80': 'rgb(56, 56, 56)',
      'primary-70': 'rgb(85, 85, 85)',
      'primary-60': 'rgb(112, 112, 112)',
      'primary-50': 'rgb(142, 142, 142)',
      'primary-40': 'rgb(170, 170, 170)',
      'primary-30': 'rgb(198, 198, 198)',
      'primary-20': 'rgb(227, 227, 227)',
    })
  })

  describe('Должен возвращать одинаковую палитру при невалидной строке, длина которой не 3 и не 6', () => {
    for (let color of ['#C394', '1231', 'asdasdasd']) {
      it(`color = ${color}`, () => {
        const pallete = generatePrimaryPalette(color)

        expect(pallete).toEqual({
          primary: 'rgb(0, 0, 0)',
          'primary-90': 'rgb(28, 28, 28)',
          'primary-80': 'rgb(56, 56, 56)',
          'primary-70': 'rgb(85, 85, 85)',
          'primary-60': 'rgb(112, 112, 112)',
          'primary-50': 'rgb(142, 142, 142)',
          'primary-40': 'rgb(170, 170, 170)',
          'primary-30': 'rgb(198, 198, 198)',
          'primary-20': 'rgb(227, 227, 227)',
        })
      })
    }
  })
})
