/**
 * @kind helper
 * @desc Функция, проверяющая, одинаковые ли элементу внутри у двух массивов
 * @param {array} array1 - массив строк
 * @param {array} array2 - массив строк
 * @returns {boolean} Одинаковые ли элементы внутри двух массивов
 */
export const hasArraysHaveSameElements = (array1: string[], array2: string[]) => {
  if (array1.length !== array2.length) {
    return false
  }

  // Сортируем оба массива и сравниваем их
  const sortedArray1 = [...array1].sort();
  const sortedArray2 = [...array2].sort();

  return sortedArray1.every((value, index) => value === sortedArray2[index])
}
