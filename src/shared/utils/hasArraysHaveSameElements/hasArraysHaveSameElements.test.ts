import { hasArraysHaveSameElements } from './hasArraysHaveSameElements'

describe('[shared] [utils] hasArraysHaveSameElements', () => {
  it('Должен вернуть true для массивов с одинаковыми элементами в любом порядке', () => {
    const array1 = ['apple', 'banana', 'cherry']
    const array2 = ['banana', 'cherry', 'apple']

    expect(hasArraysHaveSameElements(array1, array2)).toBe(true)
  })

  it('Должен вернуть false, если массивы имеют разную длину', () => {
    const array1 = ['apple', 'banana']
    const array2 = ['apple', 'banana', 'cherry']

    expect(hasArraysHaveSameElements(array1, array2)).toBe(false)
  })

  it('Должен вернуть false, если массивы содержат разные элементы', () => {
    const array1 = ['apple', 'banana', 'cherry']
    const array2 = ['banana', 'cherry', 'date']

    expect(hasArraysHaveSameElements(array1, array2)).toBe(false)
  })

  it('Должен вернуть true для пустых массивов', () => {
    const array1: string[] = []
    const array2: string[] = []

    expect(hasArraysHaveSameElements(array1, array2)).toBe(true)
  })

  it('Должен вернуть true для массивов с одинаковыми элементами и одинаковым порядком', () => {
    const array1 = ['apple', 'banana', 'cherry']
    const array2 = ['apple', 'banana', 'cherry']

    expect(hasArraysHaveSameElements(array1, array2)).toBe(true)
  })

  it('Должен вернуть false, если массивы содержат разные элементы', () => {
    const array1 = ['apple', 'banana', 'cherry']
    const array2 = ['cherry', 'banana', 'grape']

    expect(hasArraysHaveSameElements(array1, array2)).toBe(false)
  })
})
