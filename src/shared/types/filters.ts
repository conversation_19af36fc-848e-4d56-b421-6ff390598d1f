import { ECourseProgress, ELearning, EPhishingEvent, ERole } from '@/shared/types/enums'

export type IFilters = {
  search?: string
  departments?: UUID[] | null
  phishing_events?: EPhishingEvent[]
  role?: ERole | null
  courses?: UUID[]
  courseProgress?: ECourseProgress[]
  phishing?: UUID[]
  learning?: ELearning[]
  tags?: UUID[]
  riskLevelMax?: number
  riskLevelMin?: number
}

export type TFilter = keyof IFilters
