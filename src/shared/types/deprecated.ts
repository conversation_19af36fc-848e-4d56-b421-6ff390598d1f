import { ICourseStatus, INewCourseStatus } from "@/shared/types/enums/course";

export type Maybe<T> = T | null;
export type Exact<T extends { [key: string]: unknown }> = {
  [K in keyof T]: T[K];
};
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]?: Maybe<T[SubKey]>;
};
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & {
  [SubKey in K]: Maybe<T[SubKey]>;
};

/** An enumeration. */

/** An enumeration. */

export interface INewCourse {
  id: UUID;
  old_id: number;
  title: string;
  description?: Maybe<string>;
  status: INewCourseStatus | ICourseStatus;
  picture?: string;
  start_date: string;
  end_date: string;
  created_date: string;
  employees_count: number;
  modules: Array<{ id: UUID | null; title: string; old_id: number }>;
}

type IStatisticsMetricBase_CourseProgress_Fragment = {
  value?: Maybe<number>;
  date: string;
};

type IStatisticsMetricBase_ModuleProgress_Fragment = {
  value?: Maybe<number>;
  date: string;
};

type IStatisticsMetricBase_PhishingStatistics_Fragment = {
  value?: Maybe<number>;
  date: string;
};

type IStatisticsMetricBase_ProgressStatistics_Fragment = {
  value?: Maybe<number>;
  date: string;
};

type IStatisticsMetricBase_QuizAttempts_Fragment = {
  value?: Maybe<number>;
  date: string;
};

type IStatisticsMetricBase_RiskLevelStatistics_Fragment = {
  value?: Maybe<number>;
  date: string;
};

export type IStatisticsMetricBaseFragment =
  | IStatisticsMetricBase_CourseProgress_Fragment
  | IStatisticsMetricBase_ModuleProgress_Fragment
  | IStatisticsMetricBase_PhishingStatistics_Fragment
  | IStatisticsMetricBase_ProgressStatistics_Fragment
  | IStatisticsMetricBase_QuizAttempts_Fragment
  | IStatisticsMetricBase_RiskLevelStatistics_Fragment;
