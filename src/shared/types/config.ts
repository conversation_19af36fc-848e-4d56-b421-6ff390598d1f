/* eslint-disable @typescript-eslint/no-explicit-any */
export abstract class Config {
  useSSO!: boolean
  use_sso!: boolean
  forse_saml!: boolean
  needPrivacyPolicyPage!: boolean
  needAgreementPage!: boolean
  logoUrl!: string
  apiUrl!: string
  apiHost!: string
  eventKeeperUrl!: string

  publicUrl!: string

  lang!: string

  analytics_enabled!: boolean
  yandex_metrika_id?: string | number
  google_analytics_id?: string
  theme: any
  darkLogoUrl?: string
  lightLogoUrl?: string

  featureFlags!: Record<string, boolean>
}
