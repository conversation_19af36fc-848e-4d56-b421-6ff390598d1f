import { EMyCourseStatus, EMyScormCoursesStatus } from '@/shared/types/enums'

//! Обычные курсы
export interface IMyCourseAssignedModule {
  id: UUID
  old_id: number
  title: string
}

export interface IMyModule {
  id: UUID
  old_id: number
  title: string
  has_quiz: boolean
  is_learning_complete: boolean
  is_testing_complete: boolean
}

export interface IMyAssignedModuleSlide {
  audio: string
  background: string
  id: UUID
  text: string
  order_id: number
}

export interface IMyAssignedModule {
  can_start_test: boolean
  description: string
  has_quiz: boolean
  id: UUID
  is_available: boolean
  is_learning_complete: boolean
  is_testing_complete: boolean
  title: string
  assigned_slide: IMyAssignedModuleSlide[]
  max_attempts?: number | null
  attempts?: number
}

export interface IMyCourseWithModule {
  id: UUID
  old_id: number
  title: string
  description: string
  picture: string
  status: EMyCourseStatus
  created_date: string
  start_date: string
  end_date: string
  employees_count: number
  modules: IMyModule[]
}

export interface IMyCourse {
  id: UUID
  old_id: number
  title: string
  description: string
  picture: string
  status: EMyCourseStatus
  created_date: string
  start_date: string
  end_date: string
  employees_count: number
  assigned_modules: IMyCourseAssignedModule[]
  statistics: {
    value: number
    quiz: number
    learning: number
  }
}

export interface IMyCourseWithPagination extends ResponseWithPagination {
  data: IMyCourse[]
}

export interface IMyScormCourse {
  assigned_course: {
    id: UUID
    title: string
    description: string
    picture: string
    status: EMyScormCoursesStatus
    created_date: string
    file_link: string
    start_date: string
    end_date: string
    employees_count: number
  }
  statistics: {
    value: number
    quiz: number
    learning: number
  }
}

export interface IMyScormCourseWithPagination extends ResponseWithPagination {
  data: IMyScormCourse[]
}
