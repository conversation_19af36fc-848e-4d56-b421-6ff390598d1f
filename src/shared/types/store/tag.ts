import { ETagType } from '@/shared/types/enums'

export type ActionType = 'none' | 'letter' | 'course' | 'phishing' | 'registration_link'

export interface ITag {
  id: UUID
  title: string
  type: ETagType
  color: string
  organization_id: UUID
  is_active: boolean
  settings: {
    life_days: number | null
    min_risk_level: number | null
    max_risk_level: number | null
    mail_invite_interval: number | null
    mail_last_invite_date: number | null
    mail_invite_time: string | null
  }
}

export interface ITagsWithData extends ResponseWithPagination {
  data: ITag[]
}

export interface ITagMessageWithData extends ResponseWithPagination {
  data: ITagMessage[]
}
export interface ITagMessage {
  id: UUID
  theme: string
  text: string
}

export interface ITagActionResp {
  messages:
    | {
        theme: string
        text: string
        id: UUID
      }[]
    | null
  phishing: {
    campaign_id: UUID
    defer_by: number
    email_templates: UUID[]
  } | null
}

export interface ITagActionWithData {
  data: ITagActionResp
}

export interface ITagCoursesWithData extends ResponseWithPagination {
  data: ITagCourse[]
}

export interface ITagCourse {
  id: string
  old_id: number
  title: string
  description: string
  modules_count: number
  image: string
  can_edit: boolean
  can_delete: boolean
}

export interface ITagAction {
  type: ActionType
  courses?: UUID[]
  phishing_templates?: UUID[]
  defer_by?: number
  oldCourses?: UUID[]
  theme?: string
  text?: string
  id: UUID
  isNew: boolean
  phishing_templates_campaign_id?: UUID
  registration_link_days?: number
  /**
   * @example "00:00"
   */
  registration_link_time?: string
}

export interface IUpdateActionBody {
  theme?: string
  text?: string
  courses?: UUID[]
  isNew?: boolean
  type?: ActionType
  phishing_templates?: UUID[]
  defer_by?: number
  registration_link_days?: number
  registration_link_time?: string
}

export interface ITagTarget {
  id: UUID
  isNew: boolean
}

export interface ITagWithNew extends ITag {
  isNew: boolean
}
