import { ECourseProgress, ELearning, EPhishingEvent, ERole } from '@/shared/types/enums'
import { IGroupsEmployee } from '@/shared/types/store/groups'

export interface IFilter {
  departments: UUID[]
  phishingEvents: EPhishingEvent[]
  role: ERole | null
  courses: UUID[]
  courseProgress: ECourseProgress[]
  phishing: UUID[]
  tags: UUID[]
  learning: ELearning[]
  riskLevelMin: number
  riskLevelMax: number
}

export interface INote {
  id: UUID
  text: string
  email: string
}

export type GroupItem = {
  title: string
  color: string
  group: string
  value: number
  employees?: IGroupsEmployee[]
}
