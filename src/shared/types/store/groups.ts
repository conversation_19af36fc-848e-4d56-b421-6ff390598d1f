import { ERiskLevelColor } from '@/shared/types/enums'

export type ReportStatus = 'in_progress' | 'complete' | 'error'

export interface IGroupNewData {
  id: UUID
  old_id: number
  full_name: string
  picture: string
  position: string
  department: {
    id: UUID
    title: string
  }
}

export interface IGroupNew extends Omit<ResponseWithPagination, 'limit'> {
  data: IGroupNewData[]
  title: string
  color: string
}

export type TGroup = 'NORMAL' | 'BEHIND'

export type GroupItem = {
  title: string
  color: string
  group: string
  value: number
  employees?: IGroupsEmployee[]
}

export interface IGroupNew {
  data: {
    id: UUID
    old_id: number
    full_name: string
    picture: string
    position: string
    department: {
      id: UUID
      title: string
    }
  }[]
  total_count: number
  limit?: number
  offset: number
}

export interface IGroupsNew {
  NORMAL: IGroupNew
  BEHIND: IGroupNew
}

export interface IGroupsEmployee {
  id: UUID
  old_id: number
  full_name: string
  position: string
  department_id: UUID
  department_title: string
  picture: string
}

export interface IGroup {
  group: string
  color: ERiskLevelColor
  value: number
  employees: IGroupsEmployee[]
}

export interface IGroups {
  behind: IGroup | null
  normal: IGroup | null
}

export interface IProgress {
  value: number
  max: number
}

export interface IProgressChange {
  points: {
    value: number
    quiz: number
    learning: number
    date: string
  }[]
}

export interface IReport {
  id: UUID
  status: ReportStatus
  url: string | null
}
