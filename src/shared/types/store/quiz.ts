export interface IQuizAnswer {
  id: UUID;
  text: string;
  is_correct?: boolean;
  is_selected?: boolean;
  tip?: string;
}

export interface IQuizQuestion {
  id: UUID;
  reference_id: UUID;
  module_id: UUID;
  image: string;
  multiple_answers: boolean;
  text: string;
  created: string;
  last_updated: string;
  answers: IQuizAnswer[];
  is_answered: boolean;
}

export interface IQuizNew {
  id: UUID;
  title: string;
  correct_answers: number;
  created_at: string;
  completed_at: string;
  end_date: string;
  is_complete: boolean;
  passed: boolean;
  module: {
    id: UUID;
    reference_id: UUID;
    n_questions: number;
    n_pass: number;
    title: string;
    course_id: UUID;
    description: string;
    quiz_timer: number;
  };
  questions: IQuizQuestion[];
  progress: {
    value: number;
    max: number;
  };
}
