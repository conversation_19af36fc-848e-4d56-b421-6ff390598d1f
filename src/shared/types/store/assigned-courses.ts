import { IMyScormCourse } from "./course";

export interface IEmployee {
  full_name: string;
  first_name?: string;
  last_name?: string;
  email: string;
  id: UUID;
  old_id: number;
  overall_progress_percent: number;
}

export interface ICourseEmployees {
  id: UUID;
  title: string;
  users: IEmployee[];
}

export interface IScormCourseEmployees extends ResponseWithPagination {
  data: {
    assigned_course: Omit<IMyScormCourse, "statistics">;
    users: IEmployee[];
  };
}
