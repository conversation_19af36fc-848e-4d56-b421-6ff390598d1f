export type UserRole = 'employee' | 'operator'

export interface BasePasswordPolicyFields {
  min_length: number
  require_uppercase: boolean
  require_lowercase: boolean
  require_numbers: boolean
  require_special_chars: boolean
  max_age_days: number
  forbidden_words: boolean
  prevent_sequences: boolean
}

export interface BasePasswordPolicyRequestFields
  extends Omit<BasePasswordPolicyFields, 'max_age_days'> {
  max_age_days: number | null
}

export interface PasswordPolicy extends BasePasswordPolicyFields {
  id: string
  organization_id: string
  role: UserRole
}

export interface PasswordPoliciesResponse {
  employee_policy: PasswordPolicy
  operator_policy: PasswordPolicy
}

export interface PasswordPolicyRequest extends BasePasswordPolicyRequestFields {
  role: UserRole
}

export interface PasswordPolicyValidationErrors {
  min_length?: string
  require_special_chars?: string
  max_age_days?: string
}
