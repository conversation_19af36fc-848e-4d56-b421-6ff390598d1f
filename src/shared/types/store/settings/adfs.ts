export interface IADFSResponse extends Omit<IADFSRequest, 'cert_private_key'> {
  organization_uuid: UUID
}

export interface IADFSSettingsFields extends IADFSRequest {
  sp_entity_id: string
  sp_assertion_consumer_service_url: string
  sp_single_logout_service_url: string
}

export interface IADFSRequest {
  x509_cert: string
  idp_entity_id: string
  idp_sso_service_url: string
  idp_single_logout_service_url: string
}
