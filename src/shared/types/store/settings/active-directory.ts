export interface IActiveDirectoryResponse {
  id: string
  bind_dn: string
  password: string
  url: string
  search_dn: string
  filter_str: string
  use_tls: boolean
  use_sso: boolean
  krb_file_exist: boolean
}

export interface IActiveDirectoryRequest extends Omit<IActiveDirectoryResponse, 'krb_file'> {
  krb_file: File
}

export interface ADAutoSyncSettings {
  auto_sync_enabled: boolean
  auto_sync_interval: number
  auto_sync_option_delete: boolean
  auto_sync_option_update: boolean
}
