export interface IDefaultSettingsResponse {
  title: string;
  url: string;
  logo: string;
  certificate: Certificate | null;
}

export interface Certificate {
  not_after: string;
  not_before: string;
}

//! ARCHIVE
export interface IDefaultSettingsUploadArchiveRequest {
  pkcs12_file: Blob;
  password: string | null;
}

export interface IDefaultSettingsUploadArchiveResponse {
  id: string;
}

//! CERT + KEY
export interface IDefaultSettingsKeyAndCertificateRequest {
  key_file: Blob;
  cert_file: Blob;
}

export interface IDefaultSettingsKeyAndCertificateResponse {
  id: string;
}

//! PATCH
export interface IDefaultSettingsPatchRequest {
  title: string;
  url: string;
  logo: string;
  certificate_id: string;
}

export interface IDefaultSettingsPatchResponse
  extends IDefaultSettingsResponse {
  certificate: Certificate;
}
