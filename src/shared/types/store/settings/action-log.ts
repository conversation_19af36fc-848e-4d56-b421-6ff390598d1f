/* eslint-disable @typescript-eslint/no-explicit-any */
export interface IActionLogsResponse extends ResponseWithPagination {
  data: IActionLogItem[]
}

export type IActionLogRequestTypes = 'admin' | 'auth' | 'learning' | 'phishing'

export interface IActionLogAuditRequest {
  report_type: IActionLogRequestTypes
  date_from?: string
  date_to?: string
}

export interface Report {
  id: string
  url: string
  status: string
}

export type IActionLogsGenerateReportResponse = Report

export type IActionLogsGetReportResponse = Report
export interface IActionLogsGetReportRequest {
  id: string
}

export interface ActionLogSearchParams {
  type: IActionLogRequestTypes
  page: number
}

export interface IActionLogReportRequest {
  type: 'audit'
  details: Record<any, any> & {
    type: IActionLogRequestTypes
  }
}

export interface IActionLogRequest extends RequestWithPagination {
  type: IActionLogRequestTypes
}

export interface IActionLogItem {
  id: string
  created_at: string
  type: string
  action: string
  user_email: string
  user_name: string
  details: Record<string, string>
  ip: string
  user_agent: string
}
