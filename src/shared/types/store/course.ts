import { ECourseStatus, ERiskLevelColor, IModuleAttemptEnum } from '@/shared/types/enums'
import { ReportStatus } from '@/shared/types/store/groups'

//! Обычные курсы
export interface IAssignedCourseProps {
  className?: string
}

export interface IGroupsEmployee {
  id: UUID
  old_id: number
  full_name: string
  position: string
  department_id: UUID
  department_title: string
  picture: string
}

export interface ICourseInfo {
  title?: string
  description?: Nullable<string>
  start_date?: string
  end_date?: string
  need_assigned_messages?: boolean
  need_notify_messages?: boolean
}

export interface IGroup {
  group: string
  color: ERiskLevelColor
  value: number
  employees: IGroupsEmployee[]
}

export interface IGroups {
  behind: IGroup | null
  normal: IGroup | null
}

export interface IProgress {
  value: number
  max: number
}

export interface IProgressChange {
  points: {
    value: number
    quiz: number
    learning: number
    date: string
  }[]
}

export interface IReport {
  id: UUID
  status: ReportStatus
  url: string | null
  filename: string
}
export interface IMyCourseAssignedModule {
  id: UUID
  old_id: number
  title: string
}

export enum EMyCourseStatus {
  active = 'active',
  complete = 'complete',
  planned = 'planned',
}

export interface IMyCourse {
  id: UUID
  old_id: number
  title: string
  description: string
  picture: string
  status: EMyCourseStatus
  created_date: string
  start_date: string
  end_date: string
  employees_count: number
  assigned_modules: IMyCourseAssignedModule[]
  statistics: {
    value: number
    quiz: number
    learning: number
  }
}

export interface IMyCourseWithPagination extends ResponseWithPagination {
  data: IMyCourse[]
}

//! Скорм курсы

export interface IMyScormCourse {
  assigned_course: {
    id: UUID
    title: string
    description: string
    picture: string
    status: ECourseStatus
    created_date: string
    file_link: string
    start_date: string
    end_date: string
    employees_count: number
  }
  statistics: {
    value: number
    quiz: number
    learning: number
  }
}

export interface IMyScormCourseWithPagination extends ResponseWithPagination {
  data: IMyScormCourse[]
}

export interface IMyScormCourseWithData extends ResponseWithPagination {
  data: IMyScormCourse[]
}

export type TAssignedCourse = IMyCourse

export interface IAssignedCoursesWithData extends ResponseWithPagination {
  data: TAssignedCourse[]
}

export interface ICourseWithData extends ResponseWithPagination {
  data: ICourse[]
}

export interface ICourse {
  id: string
  old_id: number
  title: string
  archived?: boolean
  description: string
  modules_count: number
  image: string
  picture?: string
  can_edit: boolean
  tested?: boolean
  can_delete: boolean
}

export enum ECourseVisibility {
  public = 'public',
  private = 'private',
}

export interface IScormCourse {
  id: UUID
  title: string
  description: string
  file_link: Nullable<string>
  organization_id: UUID
  visibility: ECourseVisibility
  picture: Nullable<string>
  can_edit: boolean
  can_delete: boolean
  tested: boolean
  config: {
    slideCount: number
    has_quiz: boolean
    quizCount: number
  }
}

export interface IEmployeeScormCourses extends ResponseWithPagination {
  data: IMyScormCourse[]
}

export interface IAttemptProgress {
  max_value: number
  value: number
  start_date: string
  duration: number
  status: IModuleAttemptEnum
  id: UUID
}

export interface IModuleProgress {
  module_id: UUID
  title: string
  passed_theory_percent: number
  quiz_passed: boolean
  attempts_count: number
  attempts: IAttemptProgress[]
}

export interface IEmployeeCourse {
  course_id: UUID
  title: string
  picture: string
  passed_theory_percent: number
  passed_tests_percent: number
  modules_progress: IModuleProgress[]
}

export interface IAssgnedScromCourse {
  created_date: string
  description: string
  employees_count: 1
  end_date: string
  file_link: string
  id: UUID
  picture: null
  start_date: string
  status: ECourseStatus
  title: string
}

export interface IAssgnedScromCourseWithData extends ResponseWithPagination {
  data: IAssgnedScromCourse[]
}

export interface IAssignScormCourseRequest {
  course_id: UUID
  title: string
  description?: string
  start_date: string
  end_date: string
  target_users?: UUID[]
  target_departments?: UUID[]
  need_assigned_messages?: boolean
  need_notify_messages?: boolean
}
