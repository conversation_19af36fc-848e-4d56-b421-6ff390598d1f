import { EStatus } from '@/shared/types/enums'

export interface IReportStatus {
  id: UUID
  type: string
  url: string | null
  status: EStatus
  withoutUrl?: boolean
}

export interface IReportsResponse {
  data: IReport[]
  total_count: number
  limit: number
  offset: number
}

export interface IReport {
  id: string
  status: EStatus
  url?: string
  filename: string
  created_at: string | null
}

export interface IReportRequestScorm {
  type: 'scorm'
}

export interface IReportRequestPhishing extends IReportIntervalParams {
  organization_id: string
}

export interface IReportIntervalParams {
  date_from?: string
  date_to?: string
}
