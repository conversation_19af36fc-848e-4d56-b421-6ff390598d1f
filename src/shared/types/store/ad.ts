export interface IADSyncInfo {
  content?: {
    details: {
      info:
        | 'not_started'
        | 'presync_started'
        | 'presync_completed'
        | 'sync_started'
        | 'sync_completed'
        | 'error'
        | null
      employees: {
        create_count: number
        delete_count: number
        keep_count: number
        total_count: number
        update_count: number
      }
      departments: {
        total_departments_fetched: number
        add_to_db: number
        keep_in_db: number
        delete_in_db: number
      }
    } | null
    error: string | null
    metadata: {
      sync_id: UUID
    }
  }
  event_id: UUID
  status: string
}

export type ADSettings = {
  search_dn: string
  filter_str: string
}

export interface IADSyncApplyRequest {
  params: { update: boolean; delete: boolean; event_id: UUID }
}
