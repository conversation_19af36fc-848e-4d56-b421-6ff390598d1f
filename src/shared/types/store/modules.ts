export interface IModule {
  id: UUID
  old_id: number
  title: string
  description: string
  can_delete: boolean
  can_edit: boolean
  is_available: boolean
  slide_count: number
  show_all_answers: boolean
  max_attempts: null | number
}

export interface IAnswer {
  id?: UUID
  tip: string
  text: string
  is_correct: boolean
}

export interface IQuestionWithAnswers {
  id: UUID
  multiple_answers: boolean
  text: string
  block_uuid: UUID
  answers: IAnswer[]
}

export type IQuestionWithAnswersEditBody = Omit<IQuestionWithAnswers, 'block_uuid' | 'id'>

export interface IQuestion {
  id: UUID
  multiple_answers: boolean
  module_id: string
  text: string
  image: string
}

export type TModuleWithSlidesAndQuestion = {
  slides: ISlide[]
  questions: IQuestion[]
  questions_count: number
  pptx_file_path?: string
  quiz: {
    total: number
    threshold: number
    timer: number
  }
} & IModule

export interface IEditModuleBody {
  id: UUID
  title: string
  description?: string
  total: string | number
  threshold: string | number
  timer: string | number
  show_all_answers: boolean
  max_attempts: null | number
}

export interface ISlide {
  id: UUID
  order_id: number
  module_id: string
  module_old_id: number
  text: string
  audio: string
  background: string
}

export enum EPPTXStatus {
  in_process = 'in_process',
  success = 'success',
  error = 'error',
}

export interface ISlideFromPPTXResponse {
  event_id: UUID
  type: 'pptx'
  status: EPPTXStatus
}

export interface ICreateQuestionBody {
  multiple_answers: boolean
  text: string
  answers:
    | {
        is_correct: boolean
        text: string
        tip: string
      }[]
    | null
}
