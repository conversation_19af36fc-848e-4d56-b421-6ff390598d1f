export enum ICourseStatus {
  Active = "ACTIVE",
  Complete = "COMPLETE",
  Planned = "PLANNED",
}

export enum EMyCourseStatus {
  active = "active",
  complete = "complete",
  planned = "planned",
}

export enum INewCourseStatus {
  Active = "active",
  Complete = "complete",
  Completed = "completed",
  Planned = "planned",
}

export enum EMyScormCoursesStatus {
  active = "active",
  completed = "completed",
  planned = "planned",
}

export enum IModuleAttemptEnum {
  InProgress = "IN_PROGRESS",
  Passed = "PASSED",
  Failed = "FAILED",
}

export enum ECourseStatus {
  active = "active",
  complete = "complete",
  completed = "completed",
  planned = "planned",
}

export enum EScormCourseStatus {
  active = "active",
  completed = "completed",
  planned = "planned",
}

export enum ECourseProgress {
  NOT_START = "NOT_START",
  COMPLETED = "COMPLETED",
  NOT_COMPLETED = "NOT_COMPLETED",
  WITHOUT_COURSE = "WITHOUT_COURSE",
}
