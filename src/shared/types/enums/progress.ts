export enum EProgressGroup {
  Normal = 'NORMAL',
  Behind = 'BEHIND',
}

export enum EAttackVectorType {
  attachments = 'attachments',
  form = 'form',
}

export interface IAttackVectors {
  type: EAttackVectorType
  users_count: number
}

export enum EPhishingCampaignsStatus {
  creating = 'creating',
  active = 'active',
  completed = 'completed',
  planned = 'planned',
  failed = 'failed',
}

export enum EIncidentRiskLevel {
  bad = 'bad',
  warning = 'warning',
  good = 'good',
}

export enum ETarget {
  opened = 'opened',
  clicked = 'clicked',
  entered_data = 'entered_data',
  opened_attachment = 'opened_attachment',
}
