/* eslint-disable @typescript-eslint/no-explicit-any */
export interface ILine extends ILegend {
  data?: Array<{
    value: number
    date: string
  }>
}

export interface IChartData extends ILegend {
  // TODO ADD ID
  value: number
  group?: string
}

export interface ILegend {
  label: string
  color?: ChartColors | string
  group?: string
}

export enum ChartColors {
  red = 'red',
  green = 'green',
  blue = 'blue',
  yellow = 'yellow',
  gray = 'gray',
  light_gray = 'light_gray',
}

export interface ISlide {
  id: number
  seq_n: number
  audio: string
  text: string
  background: string
}

export interface IEditSlide {
  id?: number
  seq_n?: number
  audio: string | File
  text: string
  background: string | File
}

export interface IPresentationSlide extends Pick<ISlide, 'background'> {}

export interface IAnswer {
  question_id: number
  answers: number[]
}

export interface IQuestion {
  id: number
  text: string
  multiple_answers: boolean
  answers: IQuestionAnswer[]
}

interface IQuestionAnswer {
  id: number
  question_id: number
  text: string
  tip?: string
  is_correct?: boolean
}

export interface IEmployee {
  full_name: any
  overall_progress_percent: any
  id: number
  uuid: UUID
  first_name: string
  last_name: string
  middle_name: string
  email: string
  department: number
  position: string
  picture: string
  stats_summary: {
    overall_progress: number
    phishing_stats: {
      emails_stat: {
        opened: number
        sent: number
      }
      opened_percentage: number
      clicked_percentage: number
      enetered_data_percentage: number
    }
    risk_level: {
      value: number
      color: 'green' | 'yellow' | 'red'
    }
  }
  is_registered: boolean
  department_title: string
  department_id: UUID
  organization_title: string
  role: string
}

export interface IDepartment {
  id: number
  uuid: UUID
  title: string
}
