/* eslint-disable @typescript-eslint/no-explicit-any */
/*
  Глобальные обьявления
*/
type Func<R = any> = (...args: any[]) => R

type UUID = string

type Nullable<T> = T | null

// Когда  нужно сделать ОТДЕЛЬНЫЕ поля необязательными
type PartialFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

type DeepPartial<T> = {
  [K in keyof T]?: T[K] extends (infer U)[]
    ? DeepPartial<U>[]
    : T[K] extends object
      ? DeepPartial<T[K]>
      : T[K]
}

//! Используется для ответа, который включает в себя пагинацию
interface ResponseWithPagination {
  total_count: number
  limit: Nullable<number>
  offset: Nullable<number>
}

//! Используется для запроса, который включает в себя пагинацию
interface RequestWithPagination {
  limit: Nullable<number>
  offset?: Nullable<number>
}

//! Используется для ответа с текстом для успешного уведомления
type ResponseWithNotification<T> = (T extends void ? NonNullable<unknown> : T) & {
  notificationTip?: string
}

//! Для передачи пропсов необходимых для тестирования
interface DataTest {
  'data-testid'?: string
}

interface ClassName {
  className?: string
}
