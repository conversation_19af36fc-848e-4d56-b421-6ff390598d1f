/* eslint-disable @typescript-eslint/no-explicit-any */
import { CampaignEventType } from '@/shared/types/common'

export const TABLE_LIMIT = 30

export const EMPLOYESS_TABLE_COLS = [
  {
    label: 'name',
    key: 'name',
  },
  {
    label: 'work_post',
    key: 'work-post',
  },
  {
    label: 'department',
    key: 'employee',
  },
  {
    label: 'system',
    key: 'system',
  },
  {
    label: 'status',
    key: 'status',
    helper: 'modules.employee_statistics.status_helper',
  },
  {
    label: 'date',
    key: 'date',
  },
]

export const DEPARTMENTS_TABLE_COLS = [
  {
    label: 'commons:department',
    key: 'department',
  },
  {
    label: 'commons:several_employees',
    key: 'employess',
  },
  {
    label: 'commons:popular_template',
    key: 'populat-template',
  },
  {
    label: 'commons:fallen_phishing',
    key: 'met-phishing',
  },
]

export const PHISHING_DEPARTMENTS_TABLE_COLS = [
  {
    label: 'commons:department',
    key: 'department',
  },
  {
    label: 'commons:several_employees',
    key: 'employess',
  },
  {
    label: 'commons:fallen_phishing',
    key: 'met-phishing',
  },
]

export const COLOR_MAP: Record<CampaignEventType, any> = {
  sent: 'neutral' as const,
  unknown: 'neutral' as const,
  opened: 'good' as const,
  clicked: 'warning' as const,
  entered_data: 'bad' as const,
  opened_attachment: 'bad' as const,
}
