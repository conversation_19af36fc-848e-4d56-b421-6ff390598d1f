export const GOALS = {
  // Пользователь зашел на страницу курсов
  'courses-page': {
    name: 'courses-page',
    params: {},
  },
  // Пользователь выбрал курс
  'courses-choose': {
    name: 'courses-choose',
    params: {},
  },
  // Пользователь зашел на страницу курса
  'course-page': {
    name: 'course-page',
    params: {},
  },
  // Пользователь скачал презентацию
  'course-download-pptx': {
    name: 'course-download-pptx',
    params: {},
  },
  // Пользователь перешел в другой элемент темы через паменл
  'course-route-theme-item': {
    name: 'course-route-theme-item',
    params: {},
  },
  // Пользователь перешел в другой элемент темы через кнопку "Далее"
  'course-next-theme-item': {
    name: 'course-next-theme-item',
    params: {},
  },
  // Пользователь перешел в другой элемент темы через кнопку "Следующий шаг" после закончившихся попыток
  'course-next-theme-item-after-quiz': {
    name: 'course-next-theme-item-after-quiz',
    params: {},
  },
  // Пользователь перешел назад к теории после теста
  'course-back-to-theory-after-quiz': {
    name: 'course-back-to-theory-after-quiz',
    params: {},
  },
  // Пользователь наэал "Вернуться к курсам" после тестирования
  'course-move-to-courses-after-quiz': {
    name: 'course-move-to-courses-after-quiz',
    params: {},
  },
  // Пользователь перешел в другой элемент темы через кнопку "Далее" после теста
  'course-next-theme-item-after-repeat-end': {
    name: 'course-next-theme-item-after-repeat-end',
    params: {},
  },
  // Пользователь заново начал тест
  'course-repeat-quiz': {
    name: 'course-repeat-quiz',
    params: {},
  },
  // Пользователь завершил тест
  'course-finish-quiz': {
    name: 'course-finish-quiz',
    params: {},
  },
  // Пользователь начао тест
  'course-start-quiz': {
    name: 'course-start-quiz',
    params: {},
  },
  // Пользователь вышел из тестирования
  'course-cancel-quiz': {
    name: 'course-cancel-quiz',
    params: {},
  },
  // Пользователь выбрал ответ
  'course-answer-quiz': {
    name: 'course-answer-quiz',
    params: {},
  },

  // Пользователь перешел в другую тему
  'course-route-theme': {
    name: 'course-route-theme',
    params: {},
  },
  // Пользователь
  //   courses: {
  //     name: 'courses',
  //     params: {},
  //   },
}
