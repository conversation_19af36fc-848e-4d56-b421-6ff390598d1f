import { ERole } from '../types/enums'

export const emptyPermissions = {
  user: [],
}

export const userPermissions = {
  user: [
    { name: 'learning', pages: [] },
    { name: 'statistics', pages: [] },
  ],
}

export const contentManagerPermissions = {
  ...userPermissions,
  admin: [
    {
      name: 'statistics',
      pages: [{ name: 'general-statictics' }, { name: 'reports' }],
    },
    {
      name: 'learning',
      pages: [{ name: 'assigned' }, { name: 'courses' }, { name: 'themes' }],
    },
  ],
}

export const adminPermissions = {
  ...userPermissions,
  admin: [
    {
      name: 'statistics',
      pages: [{ name: 'general-statictics' }, { name: 'reports' }],
    },
    {
      name: 'staff',
      pages: [{ name: 'employees' }, { name: 'departments' }, { name: 'tags' }],
    },
    {
      name: 'learning',
      pages: [{ name: 'assigned' }, { name: 'courses' }, { name: 'themes' }],
    },
    {
      name: 'phishing',
      pages: [
        { name: 'campaigns' },
        { name: 'phishing-by-tags' },
        { name: 'templates' },
        { name: 'redirect' },
      ],
    },
    { name: 'organization', pages: [] },
    {
      name: 'settings',
      pages: [{ name: 'audit' }, { name: 'license' }, { name: 'adfs' }],
    },
  ],
}

export const hiddenAdminPermissions = {
  ...userPermissions,
  admin: [
    {
      name: 'statistics',
      pages: [{ name: 'general-statictics' }, { name: 'reports' }],
    },
    {
      name: 'staff',
      pages: [{ name: 'employees' }, { name: 'departments' }, { name: 'tags' }],
    },
    {
      name: 'learning',
      pages: [{ name: 'assigned' }, { name: 'courses' }, { name: 'themes' }],
    },
    {
      name: 'phishing',
      pages: [
        { name: 'campaigns' },
        { name: 'phishing-by-tags' },
        { name: 'templates' },
        { name: 'redirect' },
      ],
    },
    { name: 'organization', pages: [] },
    {
      name: 'settings',
      pages: [{ name: 'audit' }, { name: 'license' }, { name: 'adfs' }],
    },
  ],
}

export const superUserPermissions = {
  ...userPermissions,
  admin: [
    {
      name: 'statistics',
      pages: [{ name: 'general-statictics' }, { name: 'reports' }],
    },
    {
      name: 'staff',
      pages: [{ name: 'employees' }, { name: 'departments' }, { name: 'tags' }],
    },
    {
      name: 'learning',
      pages: [{ name: 'assigned' }, { name: 'courses' }, { name: 'themes' }],
    },
    {
      name: 'phishing',
      pages: [
        { name: 'campaigns' },
        { name: 'phishing-by-tags' },
        { name: 'templates' },
        { name: 'redirect' },
      ],
    },
    { name: 'organization', pages: [] },
    {
      name: 'settings',
      pages: [{ name: 'audit' }, { name: 'adfs' }, { name: 'settings' }, { name: 'license' }],
    },
  ],
}

export const operatorPermissions = {
  ...userPermissions,
  admin: [
    {
      name: 'statistics',
      pages: [{ name: 'general-statictics' }, { name: 'reports' }],
    },
    {
      name: 'staff',
      pages: [{ name: 'employees' }, { name: 'departments' }, { name: 'tags' }],
    },
    {
      name: 'learning',
      pages: [{ name: 'assigned' }, { name: 'courses' }, { name: 'themes' }],
    },
    {
      name: 'phishing',
      pages: [
        { name: 'campaigns' },
        { name: 'phishing-by-tags' },
        { name: 'templates' },
        { name: 'redirect' },
      ],
    },
    { name: 'organization', pages: [] },
    {
      name: 'settings',
      pages: [{ name: 'audit' }, { name: 'adfs' }, { name: 'license' }],
    },
  ],
}

export const adminPhishingLicensePermissions = {
  ...userPermissions,
  admin: [
    {
      name: 'statistics',
      pages: [{ name: 'general-statictics' }, { name: 'reports' }],
    },
    {
      name: 'staff',
      pages: [{ name: 'employees' }, { name: 'departments' }],
    },
    {
      name: 'phishing',
      pages: [{ name: 'campaigns' }, { name: 'templates' }, { name: 'redirect' }],
    },
    { name: 'organization', pages: [] },
    { name: 'settings', pages: [{ name: 'audit' }] },
  ],
}

interface UserWithRole {
  role: ERole
}

export function getHighestRole(roles: (ERole | null)[]): ERole {
  const rolesMap: Record<ERole, number> = {
    [ERole.pdf_service]: -1,
    [ERole.employee]: 0,
    [ERole.content_manager]: 1,
    [ERole.operator]: 2,
    [ERole.super_user]: 3,
    [ERole.system_admin]: 4,
  }

  if (!roles.length) {
    return ERole.employee
  }

  return roles.reduce((highestRole: ERole, role) => {
    if (role && rolesMap[role] > rolesMap[highestRole]) {
      return role
    }
    return highestRole
  }, ERole.employee)
}

export function isSystemAdmin(user: UserWithRole): boolean {
  return user.role === ERole.system_admin
}
