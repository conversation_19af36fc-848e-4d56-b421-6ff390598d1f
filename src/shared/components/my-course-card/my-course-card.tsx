import { FC, useMemo } from 'react'
import styles from './my-course-card.module.scss'
import classNamesBind from 'classnames/bind'
import { MyCourseCardProps } from './my-course-card.d'
import { DefaultImage } from './default-image'
import SuccessIcon from '@/shared/ui/Icon/icons/components/SuccessIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

import { IMyCourse, IMyCourseAssignedModule } from '@/shared/types/store/user-courses'
import { useNavigate } from 'react-router-dom'
import { getDaysLeft, isEndedCourse } from '@/shared/helpers/courses'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const MyCourseCard: FC<MyCourseCardProps.Props> = props => {
  const { className, isScorm = false, disabled = false } = props
  let { info } = props

  const { end_date, id, title, picture } = info.assigned_course || info
  let assigned_modules: IMyCourseAssignedModule[] = []

  if (!info.assigned_course) {
    info = info as IMyCourse

    assigned_modules = info.assigned_modules
  }

  const isCompleted = useMemo(() => {
    return info.statistics.value == 100
  }, [info])

  const { t } = useTranslation()

  const moduleCount = assigned_modules?.length || 0
  const moduleCountTitle = info.assigned_course
    ? ''
    : moduleCount === 0
      ? t('commons:modules_not_found')
      : t('commons:modules_count', { count: moduleCount })

  const daysLeft = getDaysLeft(end_date)

  const isEndingCourse = isEndedCourse(end_date)

  const linkPath = isScorm
    ? `/lk/user/learning/courses/scorm/${id}?from=active`
    : `/lk/user/learning/courses/${id}/modules`
  const navigate = useNavigate()

  return (
    <div
      className={cx('wrapper', className, {
        'wrapper--completed': isCompleted || disabled,
      })}
      onClick={() => {
        if (!disabled) navigate(linkPath)
      }}
    >
      <div className={cx('imageWrapper')}>
        {picture ? <img src={picture} alt='' /> : <DefaultImage />}
      </div>
      <div className={cx('textWrapper')}>
        <div className={cx('title')}>{title}</div>
        {isCompleted ? (
          <>
            <div className={cx('info')}>
              <div className={cx('infoItem')}>{moduleCountTitle}</div>
              <div className={cx('infoItem', 'green')}>
                {t('commons:passed')}{' '}
                <IconWrapper color='primary'>
                  <SuccessIcon />
                </IconWrapper>
              </div>
            </div>
          </>
        ) : (
          <>
            <div className={cx('info')}>
              <div className={cx('infoItem')}>{moduleCountTitle}</div>
              <div
                className={cx('infoItem')}
              >{`${t('commons:theory')} ${info.statistics.learning.toFixed(0)}%`}</div>
            </div>
            <div className={cx('info')}>
              <div className={cx('infoItem')}>
                {isEndingCourse && daysLeft && daysLeft > 0
                  ? t('modules.employee_courses.days_left', { count: daysLeft })
                  : ''}
              </div>
              <div
                className={cx('infoItem')}
              >{`${t('commons:testing')} ${info.statistics.quiz.toFixed(0)}%`}</div>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
