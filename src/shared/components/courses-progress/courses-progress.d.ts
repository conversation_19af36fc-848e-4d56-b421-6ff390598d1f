import { ReactNode } from 'react'

export declare namespace CoursesProgressProps {
  interface Own {
    active?: number
    finished?: number
    behind?: number
    normal?: number
    onClick?: Dispatch<SetStateAction<string | null>>
    showArrow?: boolean
    isLoading: boolean
    topSlot?: ReactNode
    bottomSlot?: ReactNode
    sendText?: ReactNode
  }

  type Props = Own
}

export declare namespace CoursesProgressMiniProps {
  interface Own {
    active?: number
    finished?: number
    showArrow?: boolean
    isLoading?: boolean
  }

  type Props = Own
}
