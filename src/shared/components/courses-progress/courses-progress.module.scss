.wrapper {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  width: 100%;
}

.block {
  display: flex;
  color: var(--color-gray-90);
  align-items: center;
  gap: 8px;
  font: var(--font-caption-1-demibold);

  &.green {
    color: var(--color-primary);
  }
}

.amount {
  font: var(--font-title-1-normal);
}

.text {
  max-width: 75px;
}

.arrow {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  &__link {
    margin-left: auto;
  }
}

.subtitle {
  color: var(--color-gray-90);
  font: var(--font-caption-1-demibold);
  margin-bottom: 16px;
}

.graph {
  display: flex;
  width: 100%;
  gap: 2px;
  border-radius: 5px;
  overflow: hidden;
  height: 24px;
  margin-bottom: 16px;
  font: var(--font-caption-1-normal);
  color: var(--color-surface);

  & .normal,
  & .behind {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 5%;

    &.clickable {
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
  }

  & .normal {
    background-color: #68cba2;
  }

  & .behind {
    background-color: var(--color-statistics-bad);
  }
}

.list {
  display: inline-grid;
  gap: 8px;
  margin-bottom: 16px;
}

.line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 16px;
  position: relative;
  font: var(--font-caption-1-normal);
  color: var(--color-gray-80);

  span {
    color: var(--color-gray-90);
  }

  &::before {
    content: '';
    display: block;
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    left: 0;
  }

  &.green {
    &::before {
      background-color: #68cba2;
    }
  }

  &.yellow {
    &::before {
      background-color: var(--color-statistics-warning);
    }
  }

  &.red {
    &::before {
      background-color: var(--color-statistics-bad);
    }
  }
}

.send {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--color-primary);
  margin-top: auto;

  &__text {
    text: var(--font-text-2-normal);
    line-height: 18px;
  }
}
