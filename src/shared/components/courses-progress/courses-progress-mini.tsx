import { FC } from 'react'
import styles from './courses-progress.module.scss'
import classNamesBind from 'classnames/bind'
import { CoursesProgressMiniProps } from './courses-progress.d'
import { NavLink } from 'react-router-dom'
import { Card } from '@/shared/ui'
import ArrowIcon from '@/shared/ui/Icon/icons/components/ArrowIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import Skeleton from 'react-loading-skeleton'

const cx = classNamesBind.bind(styles)

interface ProgressBlockProps {
  amount?: number
  translationKey: string
  className?: string
  isLoading?: boolean
}

const ProgressBlock: FC<ProgressBlockProps> = ({
  amount,
  translationKey,
  className,
  isLoading,
}) => {
  const { t } = useTranslation()

  return (
    <div className={cx('block', className)}>
      <div className={cx('amount')}>
        {isLoading || amount === undefined ? <Skeleton height={'100%'} width={'40px'} /> : amount}
      </div>
      <span className={cx('text')}>{t(translationKey)}</span>
    </div>
  )
}

export const CoursesProgressTopline: FC<CoursesProgressMiniProps.Props> = props => {
  const { active, finished, showArrow, isLoading } = props

  return (
    <div className={cx('wrapper')}>
      <ProgressBlock
        amount={active}
        /* eslint-disable-next-line i18next/no-literal-string */
        translationKey='commons:active_courses'
        className='green'
        isLoading={isLoading}
      />
      {finished !== undefined && (
        <ProgressBlock
          amount={finished}
          /* eslint-disable-next-line i18next/no-literal-string */
          translationKey='commons:completed_courses'
          isLoading={isLoading}
        />
      )}
      {showArrow && (
        <NavLink className={cx('arrow__link')} to={'/lk/admin/learning/assigned'}>
          <IconWrapper className={cx('arrow')}>
            <ArrowIcon />
          </IconWrapper>
        </NavLink>
      )}
    </div>
  )
}

export const CoursesProgressMini: FC<CoursesProgressMiniProps.Props> = props => {
  return (
    <Card width='half' padding='normal'>
      <CoursesProgressTopline {...props} />
    </Card>
  )
}
