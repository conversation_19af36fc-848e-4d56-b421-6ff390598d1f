import { FC, ReactNode } from 'react'
import styles from './courses-progress.module.scss'
import classNamesBind from 'classnames/bind'
import { CoursesProgressProps } from './courses-progress.d'
import { Card } from '@/shared/ui'
import EmailPosted2Icon from '@/shared/ui/Icon/icons/components/EmailPosted2Icon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import Skeleton from 'react-loading-skeleton'

const cx = classNamesBind.bind(styles)

interface ProgressCardProps {
  topSlot?: ReactNode
  bottomSlot?: ReactNode
  children: ReactNode
}

const ProgressCard: FC<ProgressCardProps> = ({ topSlot, children }) => {
  const { t } = useTranslation()

  return (
    <Card padding='normal'>
      {topSlot}

      <div className={cx('subtitle')}>{t('commons:compliance_training_plan')}</div>
      {children}
    </Card>
  )
}

interface ProgressLineItemProps {
  type: 'normal' | 'behind'
  value: number
  translationKey: string
}

const ProgressLineItem: FC<ProgressLineItemProps> = ({ type, value, translationKey }) => {
  const { t } = useTranslation()
  const colorClass = type === 'normal' ? 'green' : 'red'

  return (
    <div className={cx('line', colorClass)}>
      {t(translationKey)}
      <span>{value}</span>
    </div>
  )
}

export const CoursesProgress: FC<CoursesProgressProps.Props> = props => {
  const { behind, normal, onClick, sendText, topSlot, bottomSlot, isLoading } = props
  const { t } = useTranslation()

  if (isLoading) {
    return (
      <ProgressCard topSlot={topSlot} bottomSlot={bottomSlot}>
        <div className={cx('placeholder')}>
          <Skeleton height={24} width={'100%'} />
        </div>
      </ProgressCard>
    )
  }

  if (!behind && !normal) {
    return (
      <ProgressCard topSlot={topSlot} bottomSlot={bottomSlot}>
        <div className={cx('placeholder')}>{t('commons:no_data')}</div>
      </ProgressCard>
    )
  }

  const total = (behind ?? 0) + (normal ?? 0)
  const accordingPercent = normal !== undefined ? Math.round((normal / total) * 100) : undefined
  const behindPercent = behind !== undefined ? Math.round((behind / total) * 100) : undefined

  const progressItems = [
    {
      type: 'normal' as const,
      value: normal,
      percent: accordingPercent,
      translationKey: 'commons:normal_plan',
    },
    {
      type: 'behind' as const,
      value: behind,
      percent: behindPercent,
      translationKey: 'commons:behind_plan',
    },
  ].filter(item => !!item.value && item.percent !== undefined)

  return (
    <ProgressCard topSlot={topSlot} bottomSlot={bottomSlot}>
      <div className={cx('graph')}>
        {!!normal && (
          <div
            onClick={() => onClick?.('normal')}
            className={cx('normal', 'cell', onClick && 'clickable')}
            style={{ width: `${accordingPercent}%` }}
          >
            {accordingPercent}%
          </div>
        )}
        {!!behind && (
          <div
            onClick={() => onClick?.('behind')}
            className={cx('behind', 'cell', onClick && 'clickable')}
            style={{ width: `${behindPercent}%` }}
          >
            {behindPercent}%
          </div>
        )}
      </div>
      <div className={cx('list')}>
        {progressItems.map(item => (
          <ProgressLineItem
            key={item.type}
            type={item.type}
            value={item.value!}
            translationKey={item.translationKey}
          />
        ))}
      </div>

      {bottomSlot
        ? bottomSlot
        : !!behind &&
          !!onClick && (
            <button className={cx('send')} onClick={() => onClick?.('behind')}>
              <IconWrapper color='primary'>
                <EmailPosted2Icon />
              </IconWrapper>
              <span className={cx('send__text')}>
                {sendText ?? t('commons:send_message_to_behind')}
              </span>
            </button>
          )}
    </ProgressCard>
  )
}
