/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useCallback, Suspense } from "react";
import styles from "./audio-player.module.scss";
import classNamesBind from "classnames/bind";
import { AudioPlayerProps } from "./audio-player.d";

import { useFileUrlResolver } from "@/shared/hooks";
import { LazySlider as Slider, Sound } from "@/shared/components";
import { joinFuncs } from "@/shared/helpers/events";
import { getTimeLabel } from "@/shared/helpers/date";

const cx = classNamesBind.bind(styles);

const _AudioPlayer: React.FC<AudioPlayerProps.Props> = (props) => {
  const { className, audio, active = true, mobileAdaptive, onClick } = props;

  const [loaded, setLoaded] = useState(false);

  const [time, setTime] = useState({ time: 0, duration: 0 });
  const [progress, setProgress] = useState(0);

  const src = useFileUrlResolver(
    loaded ? (audio ? audio : undefined) : undefined
  ); // TODO FIX

  useEffect(() => {
    if (active && !loaded) setLoaded(true);
    // eslint-disable-next-line
  }, [active]);

  useEffect(() => {
    setTime({ time: 0, duration: 0 });
  }, [audio]);

  const onTimeUpdate = useCallback((time: number, duration: number) => {
    setTime({ time, duration });
    setProgress(Math.round((time * 100) / duration));
  }, []);

  const onEnd = useCallback(() => {
    setTime({ time: 0, duration: 0 });
  }, []);

  return (
    <div className={cx("wrapper", className)} onClick={onClick}>
      <Sound
        src={src}
        active={active}
        position={progress}
        onUpdate={onTimeUpdate}
        onEnd={onEnd}
      >
        <Sound.Consumer>
          {({ setCurrentTime }) => (
            <Suspense>
              <Slider
                value={progress}
                onChange={joinFuncs(setCurrentTime as any, setProgress)}
                className={cx("slider") + " audio-player__slider"}
              />
            </Suspense>
          )}
        </Sound.Consumer>
      </Sound>

      <div
        className={cx("audioTime", { mobileAdaptive }) + " audio-player__time"}
      >
        {getTimeLabel(time.time)}
      </div>
    </div>
  );
};

export const AudioPlayer = _AudioPlayer;
