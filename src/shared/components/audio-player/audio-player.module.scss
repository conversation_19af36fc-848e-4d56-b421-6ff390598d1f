// @use 'media';
@use '../../../shared/assets/styles/mixins/text';

.wrapper {
  align-items: center;
  box-sizing: border-box;
  display: flex;
  width: 100%;
}
.seekbar {
  background: var(--white);
  border-radius: 5px;
  box-shadow:
    inset 0 1px 1px var(--swan),
    0 1px 0 var(--swan);
  box-sizing: border-box;

  flex: 1 0;
  height: 5px;
  margin-left: 15px;
  position: relative;
  &.mobileAdaptive {
    // @include media.mobile {
    // 	display: none;
    // }
  }
}

.progress {
  background: var(--color-primary);
  border-radius: 5px;

  box-sizing: border-box;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;

  /* TODO USE transition: 10ms linear; */
}

.progress::after {
  background: var(--color-primary);
  border-radius: 50%;
  box-shadow: 0 0 2px var(--black);

  content: '';
  height: 13px;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);

  width: 13px;
}

.audioTime {
  @include text.title(16px);
  box-sizing: border-box;

  color: var(--color-primary);
  font-weight: normal;
  margin-left: 20px;
  width: 3.5rem;
  flex-shrink: 0;

  &.mobileAdaptive {
    // @include media.mobile {
    // 	display: none;
    // }
  }
}

.slider {
  box-sizing: border-box;
  margin-left: 20px;
  width: 100%;
}
