.actions {
  background: white;
  padding: 16px 12px;
  bottom: 4px;
  border-radius: 12px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  position: fixed;
  max-width: min(100vw - 255px - 64px, var(--page-container) - 64px);
  bottom: 40px;
  box-shadow:
    0 0 88px -4px rgba(24, 39, 75, 0.12),
    0 0 28px -6px rgba(24, 39, 75, 0.12);
  z-index: 100;

  @media (max-width: 1024px) {
    max-width: min(100vw - 255px - 32px, var(--page-container) - 32px);
  }


  &__counter {
    margin-right: auto;
    color: var(--color-primary) !important;
    font: var(--font-text-2-normal);
  }

  &__elements {
    display: flex;
    gap: 24px;

    &__element {
      display: flex;
      gap: 8px;
      width: fit-content;
      color: var(--color-gray-80);
      font: var(--font-text-2-normal);
      &:hover {
        color: var(--color-primary);
        cursor: pointer;

        * {
          color: var(--color-primary);
        }
      }
    }
  }
  &__delete {
    &__text {
      color: var(--color-error) !important;

      &:hover {
        svg,
        path {
          fill: var(--color-error) !important;
        }
      }
      &__wrapper {
        padding: 24px !important;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
    }
    &__footer {
      margin-top: 8px;
    }
  }

  &__cancel {
    &__wrapper {
      .description {
        white-space: pre-wrap;
      }
    }
    &__text {
      color: var(--color-warn) !important;
    }
    * {
      fill: var(--color-warn) !important;
    }

    &:hover {
      color: var(--color-component-warn-hover) !important;

      * {
        fill: var(--color-component-warn-hover) !important;
      }
    }
  }
  &__padding {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

.select {
  padding: 0;
  border: none;
}

.select__list {
  margin-bottom: 34px;
  width: auto;
}

.no-wrap {
  white-space: nowrap;
}

.more {
  &__text{
    color: var(--color-gray-80) !important;
    font: var(--font-text-2-normal) !important;
  }
  &:hover {
    * {
      color: var(--color-primary) !important;
      fill: var(--color-primary) !important;
    }
  }
}