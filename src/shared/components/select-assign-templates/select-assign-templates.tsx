import { useCallback, useState } from 'react'
import { Button } from '@/shared/ui'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import styles from './select-assign-templates.module.scss'
import classNamesBind from 'classnames/bind'
import { AssignTemplateModal } from '../../modals/assign-template-modal'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

type Props = {
  onChangeSelect?: (ids: UUID[]) => void
  selected?: UUID[]
}

export const SelectAssignLetter: React.FC<Props> = ({
  onChangeSelect,
  selected: defaultSelected,
}) => {
  const { t } = useTranslation()
  const [active, setActive] = useState(false)
  const [selected, setSelected] = useState<UUID[]>(defaultSelected || [])

  const onHandleSelected = useCallback(
    (ids: UUID[]) => {
      setSelected(ids)
      onChangeSelect?.(ids)
    },
    [onChangeSelect],
  )

  return (
    <div>
      <Button
        onClick={() => {
          setActive(true)
        }}
        className={cx('button')}
        color='gray'
      >
        <span className={cx(selected?.length && 'active')}>
          {t('commons:select_letters_count', { count: selected.length })}
        </span>
        <IconWrapper color='primary'>
          <PlusIcon />
        </IconWrapper>
      </Button>
      {active && (
        <AssignTemplateModal
          handleSelect={onHandleSelected}
          selected={selected}
          active={active}
          setActive={setActive}
        />
      )}
    </div>
  )
}
