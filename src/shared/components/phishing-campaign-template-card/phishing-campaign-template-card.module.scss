.wrapper {
  align-items: center;

  background: var(--color-surface);

  cursor: pointer;

  display: flex;
  gap: 20px;
  padding: 16px;

  transition: var(--transition);

  &:hover {
    background-color: var(--color-gray-20);

    transition: var(--transition);
  }
}

.image {
  height: 32px;
  width: 32px;
  > div {
    height: 32px;
    width: 32px;
  }
}

.percent {
  &__wrapper {
    align-items: center;

    color: var(--color-gray-70);
    display: flex;
    font: var(--font-title-4-normal);
    margin-left: auto;
  }
  &__icon {
    path {
      stroke: var(--color-gray-70) !important;
    }
  }
}

.inner {
  &__wrapper {
    align-items: center;
    display: flex;
    justify-content: space-between;
  }
  .title {
    color: var(--gray-gray-90);
    font: var(--font-text-1-normal);

    margin-bottom: 6px;

    display: flex;
    align-items: center;
    gap: 4px;
  }
  .icons {
    align-items: center;
    display: flex;
    gap: 16px;
  }
}

.info {
  align-items: center;
  display: flex;
  gap: 12px;
  &__date {
    color: var(--gray-gray-80);
    font: var(--font-caption-1-normal);
  }
  &__people {
    color: var(--gray-gray-80);
    font: var(--font-caption-1-normal);
  }
  &__risk-level {
    color: var(--gray-gray-80, #5c6585);
    font: var(--font-caption-1-normal);
    &.color {
      &--RED {
        color: #ff5b7a;
      }
      &--GREEN {
        color: #29ab74;
      }
      &--YELLOW {
        color: #ffab49;
      }
    }
  }
}

.redirect {
  color: var(--gray-gray-90);
  font: var(--font-text-2-normal);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  max-width: 450px;
}
