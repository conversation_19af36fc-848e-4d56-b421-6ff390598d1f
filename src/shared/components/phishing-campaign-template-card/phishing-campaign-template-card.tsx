import { FC } from 'react'
import styles from './phishing-campaign-template-card.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingCampaignTemplateCardProps } from './phishing-campaign-template-card.d'
import { TemplateLogo, IconWithData } from '@/shared/components'
import { Tooltip } from '@/shared/ui'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import ArrowRightIcon from '@/shared/ui/Icon/icons/components/ArrowRightIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignTemplateCard: FC<PhishingCampaignTemplateCardProps.Props> = props => {
  const { className, data, onClick, content, showRedirect } = props
  const { t } = useTranslation()

  const { opened, sent, clicked, opened_attachment, entered_data, incident_risk } = data.statistics

  const percent = incident_risk ?? 0

  const hasAttachments = opened_attachment > 0

  return (
    <div className={cx('wrapper', className)} onClick={() => onClick(data.id)}>
      <div className={cx('image')}>
        <TemplateLogo src={data?.logo} alt={data.name} />
      </div>
      {content}
      <div className={cx('inner')}>
        <div className={cx('title')}>
          {`${data?.name} - ${data?.email?.name}`}
          {data?.email?.attachments?.length > 0 && (
            <IconWrapper size='20' color='gray80'>
              <ClipBoldIcon />
            </IconWrapper>
          )}
        </div>
        <div className={cx('icons')}>
          <Tooltip content={t('commons:sent')}>
            <IconWithData icon='emailCloseSmall' value={sent} className={cx('icon-wrapper')} />
          </Tooltip>
          <Tooltip content={t('commons:opened')}>
            <IconWithData icon='emailOpenSmall' value={opened} className={cx('icon-wrapper')} />
          </Tooltip>
          <Tooltip content={t('commons:transition_on_link_nbsp')}>
            <IconWithData icon='web2Small' value={clicked} className={cx('icon-wrapper')} />
          </Tooltip>
          <Tooltip content={t('commons:entered_all_data_nbsp')}>
            <IconWithData icon='lock3' value={entered_data} className={cx('icon-wrapper')} />
          </Tooltip>
          {hasAttachments && (
            <Tooltip content={t('commons:open_attachments_nbsp')}>
              <IconWithData
                icon='clipSmall'
                value={opened_attachment}
                className={cx('icon-wrapper')}
              />
            </Tooltip>
          )}
          {showRedirect && data.original_url && (
            <Tooltip content={data.original_url}>
              <div className={cx('redirect')}>
                {t('commons:redirect_enabled')}: {data.original_url}
              </div>
            </Tooltip>
          )}
        </div>
      </div>
      <Tooltip
        className={cx('percent__wrapper')}
        content={<>{t('commons:probability_of_incident_nbsp')}</>}
      >
        <div className={cx('percent__wrapper')}>
          <span>{percent}%</span>
          <IconWrapper className={cx('percent__icon')} size='20'>
            <ArrowRightIcon />
          </IconWrapper>
        </div>
      </Tooltip>
    </div>
  )
}
