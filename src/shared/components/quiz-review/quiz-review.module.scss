@use "../../assets/styles/mixins/media";
@use "../../assets/styles/mixins/text";

.wrapper {
  position: relative;
}

.header {
  align-items: center;
  box-sizing: border-box;

  cursor: pointer;
  display: flex;
  justify-content: space-between;
  padding: 25px 50px;
  width: 100%;

  @include media.mobile {
    padding: 20px;
  }
}
.legend {
  display: flex;
  margin: 10px -30px 40px;
}

.legendItem {
  @include text.main(18px);

  border-radius: 4px;
  box-sizing: border-box;

  font-weight: normal;
  line-height: 120%;
  margin: 0 30px;
  padding: 20px 15px;
  // max-width: 300px;
  width: 30%;

  &.color {
    &-green {
      background: rgba(97, 163, 32, 0.2);
    }
    &-yellow {
      background: rgb(255, 183, 44, 0.3);
    }
    &-red {
      background: rgba(234, 59, 74, 0.2);
    }
  }
}

.content {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  padding: 0 50px 25px;
  width: 100%;

  @include media.mobile {
    padding: 0 20px 25px;
  }
}

.question {
  margin: 20px 0;
}

.questionText {
  @include text.main(20px);

  font-weight: normal;
  max-width: 60pc;
}

.questionHeader {
  display: flex;
  max-width: 100%;
  min-width: 0;
  @include media.mobile {
    flex-direction: column;
    .questionNumber {
      margin-bottom: 5px;
      margin-right: 0;
    }
  }
  .questionNumber {
    @include text.title(20px);

    color: var(--color-statistics-good);
    flex-shrink: 0;
    margin-right: 20px;
    &.wrong {
      color: var(--color-statistics-bad);
    }
  }
}

.answers {
  align-items: center;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  width: 100%;
}
.answerContainer {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin: 30px 20px 0;
  max-width: 500px;
  width: 100%;

  @include media.mobile {
    max-width: unset;
  }

  &.color {
    &-green {
      .answerText {
        border: 1px solid var(--color-statistics-good);
      }
      .answerTip {
        background: rgba(97, 163, 32, 0.2);
      }
    }
    &-yellow {
      .answerText {
        border: 1px solid var(--color-statistics-warning);
      }
      .answerTip {
        background: rgb(255, 183, 44, 0.3);
      }
    }
    &-red {
      .answerText {
        border: 1px solid var(--color-statistics-bad);
      }
      .answerTip {
        background: rgba(234, 59, 74, 0.2);
      }
    }
  }
}

.answerText {
  @include text.main(18px);

  border-radius: 4px;
  box-shadow: 0 2px 9px rgba(0, 0, 0, 0.11);
  box-sizing: border-box;

  font-weight: normal;
  line-height: 120%;
  max-width: 100%;
  min-width: 0;
  padding: 20px 15px;
}

.answerTip {
  @include text.main(18px);

  background: var(--gray);
  border-radius: 4px;
  box-sizing: border-box;

  font-weight: normal;
  line-height: 120%;
  margin-top: 10px;
  max-width: 100%;
  min-width: 0;
  padding: 15px;
}
