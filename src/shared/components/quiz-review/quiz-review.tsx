/* eslint-disable @typescript-eslint/no-explicit-any */
import classNamesBind from 'classnames/bind'
import React from 'react'
import { QuizReviewProps } from './quiz-review.d'
import styles from './quiz-review.module.scss'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const QuizReview: React.FC<QuizReviewProps.Props> = props => {
  const { className, loading, newQuestions, showAll = true } = props

  const { t } = useTranslation()

  if (loading) return <div>{t('commons:loading_with_dots')}</div>

  const getQuestionColor = (id: UUID) => {
    const currectAnswers = newQuestions?.filter(q => q.id === id)[0]?.answers

    const wrongAnswers =
      currectAnswers?.filter(
        (a: any) => (!a.is_correct && a.is_selected) || (a.is_correct && !a.is_selected),
      ) || []

    return wrongAnswers?.length > 0
  }

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('legend')}>
        <div className={cx('legendItem', 'color-green')}>{t('components.quiz_review.correct')}</div>
        <div className={cx('legendItem', 'color-red')}>{t('components.quiz_review.incorrect')}</div>
        <div className={cx('legendItem', 'color-yellow')}>
          {t('components.quiz_review.unselected')}
        </div>
      </div>
      <div className={cx('content')}>
        {newQuestions?.map((q, i) => (
          <div className={cx('question')} key={q.id}>
            <div className={cx('questionHeader')}>
              <div
                className={cx('questionNumber', {
                  wrong: getQuestionColor(q.id),
                })}
              >
                {t('components.quiz_review.question')} {i + 1}.
              </div>
              <div className={cx('questionText')}>{q.text}</div>
            </div>
            <div className={cx('answers')}>
              {q.answers.map((a: any, index: number) => {
                const is_selected = 'tip' in a && a.is_selected
                if (!showAll && !is_selected) {
                  return
                }
                const is_correct = 'tip' in a && a.is_correct
                const tip = 'tip' in a && a.tip
                let color = ''

                if (is_correct && is_selected) {
                  // eslint-disable-next-line i18next/no-literal-string
                  color = 'green'
                } else if (is_correct && !is_selected) {
                  // eslint-disable-next-line i18next/no-literal-string
                  color = 'yellow'
                } else if (!is_correct && is_selected) {
                  // eslint-disable-next-line i18next/no-literal-string
                  color = 'red'
                }

                return (
                  <div key={index} className={cx('answerContainer', `color-${color}`)}>
                    <div className={cx('answerText')}>{a.text}</div>
                    <div className={cx('answerTip')}>{tip}</div>
                  </div>
                )
              })}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export { QuizReview }
