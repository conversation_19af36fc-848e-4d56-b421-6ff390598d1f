import { useState, useEffect, useRef, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import { ITargetData } from '@/entities/target'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  selectActiveTag,
  selectDepartmentsTagTarget,
  selectInitialDepartmentsTagTarget,
  selectInitialUsersTagTarget,
  selectUsersTagTarget,
  selectActualTagSelectedUsersCount,
  selectTagsIsAllSelected,
  setDepartmentsTagTarget,
  setExcludeUsersIds,
  setInitialUsersTagTarget,
  setUsersTagTarget,
} from '@/store/slices/tags'
import styles from './assign-tag-employees.module.scss'
import { Button } from '@/shared/ui'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { ImportTagEmployeesModal } from '@/shared/modals/import-tag-employess-modal'
import { useGetEmployeesByTagQuery } from '@/store/services/tags-employees-service'
import { ETagType } from '@/shared/types/enums'
import {
  OrganizationTree,
  OrganizationTreeProvider,
  useRestoreTreeByTargetData,
  useUnmountOrganizationTree,
} from '@/shared/modals/organization-tree'
import { isNumber } from '@/shared/helpers'

const cx = classNamesBind.bind(styles)

export const AssignTagEmployees = () => {
  const [targetOpen, setTargetOpen] = useState(false)
  const [importOpen, setImportOpen] = useState(false)
  const dispatch = useAppDispatch()
  const activeTag = useAppSelector(selectActiveTag)
  const initialTagUsersIds = useAppSelector(selectInitialUsersTagTarget.selectIds)
  const initialTagUsersEntities = useAppSelector(selectInitialUsersTagTarget.selectEntities)
  const initialTagDepartmentsEntities = useAppSelector(
    selectInitialDepartmentsTagTarget.selectEntities,
  )
  const selectUsersTarget = useAppSelector(selectUsersTagTarget)
  const selectDepartmentsTarget = useAppSelector(selectDepartmentsTagTarget)
  const onRestoreTree = useRestoreTreeByTargetData()
  const [userCountFromTree, setUserCountFromTree] = useState<undefined | number>(undefined)

  const wasTargetOpen = useRef(false)
  const wasTargetSelect = useRef(false)

  useEffect(() => {
    wasTargetOpen.current = false

    dispatch(setUsersTagTarget([]))
    dispatch(setDepartmentsTagTarget([]))
  }, [activeTag, dispatch])

  const {
    data: usersByTag,
    isFetching: isUsersByTagFetching,
    refetch,
  } = useGetEmployeesByTagQuery(activeTag?.id || '', {
    skip: activeTag?.type !== ETagType?.arbitrary,
  })

  const usersTagTarget = useAppSelector(selectUsersTagTarget)
  const actualUsersCount = useAppSelector(selectActualTagSelectedUsersCount)
  const isAllSelected = useAppSelector(selectTagsIsAllSelected)
  const { t } = useTranslation()

  useUnmountOrganizationTree()

  const onSelect = (data: ITargetData) => {
    dispatch(
      setUsersTagTarget(
        data.target_users.map(userId => ({
          id: userId,
          isNew: !initialTagUsersEntities[userId],
        })),
      ),
    )
    dispatch(setExcludeUsersIds(data.exclude_users_ids ?? []))
    dispatch(
      setDepartmentsTagTarget(
        data.target_departments.map(departmentId => ({
          id: departmentId,
          isNew: !initialTagDepartmentsEntities[departmentId],
        })),
      ),
    )
    wasTargetSelect.current = true
    setTargetOpen(false)
  }

  const selectedTarget = useMemo(() => {
    let target_departments: string[] = []
    let target_users: string[] = []

    if (selectDepartmentsTarget && selectDepartmentsTarget?.length > 0) {
      target_departments = selectDepartmentsTarget.map(d => d.id)
    } else if (initialTagDepartmentsEntities) {
      target_departments = Object.keys(initialTagDepartmentsEntities)
    }

    if (selectUsersTarget && selectUsersTarget?.length > 0) {
      target_users = selectUsersTarget.map(u => u.id)
    } else if (initialTagUsersEntities) {
      target_users = Object.keys(initialTagUsersEntities)
    }
    return {
      target_departments,
      target_users,
    }
  }, [
    initialTagDepartmentsEntities,
    initialTagUsersEntities,
    selectDepartmentsTarget,
    selectUsersTarget,
  ])

  const getUsersForDisplay = () => {
    if (!wasTargetSelect.current) return usersByTag?.total_count

    if (isAllSelected) {
      if (actualUsersCount !== undefined) return actualUsersCount

      return userCountFromTree || 0
    }

    return usersTagTarget ? usersTagTarget : initialTagUsersIds
  }
  const usersForDisplay = getUsersForDisplay()

  const openImportModal = () => setImportOpen(true)

  useEffect(() => {
    if (isUsersByTagFetching || !usersByTag) return

    dispatch(setInitialUsersTagTarget(usersByTag?.data?.map(user => user.id)))
    dispatch(
      setUsersTagTarget(
        usersByTag?.data?.map(tagTarget => ({
          id: tagTarget.id,
          isNew: false,
        })),
      ),
    )
  }, [dispatch, isUsersByTagFetching, usersByTag])

  const refetchUsers = () => setTimeout(() => refetch(), 30000)

  const isShowEmptyUserText = Boolean(
    (isNumber(usersForDisplay) && !usersForDisplay) ||
      (Array.isArray(usersForDisplay) && usersForDisplay?.length === 0),
  )

  return (
    <>
      <h3 className={cx('title')}>{t('dialogs.assign_employees.assign')}</h3>
      <div className={cx('wrapper')}>
        <Button
          onClick={() => {
            if (!wasTargetOpen.current)
              onRestoreTree({
                ...selectedTarget,
                half_checked: [
                  ...new Set(
                    (usersByTag?.data ?? [])?.map(u => u.department?.id ?? '').filter(Boolean),
                  ),
                ],
              })

            wasTargetOpen.current = true

            setTargetOpen(true)
          }}
          className={cx('button')}
          color='darkGray'
        >
          <span
            className={cx(
              ((Array.isArray(usersForDisplay) && usersForDisplay?.length) ||
                Number(usersForDisplay) > 0) &&
                'active',
            )}
          >
            {Array.isArray(usersForDisplay) && (
              <>
                {!!usersForDisplay?.length && (
                  <>
                    {t('dialogs.assign_employees.selected')}:{' '}
                    {t(`dialogs.assign_modules.count.employees`, {
                      count: usersForDisplay.length,
                    })}
                  </>
                )}{' '}
              </>
            )}
            {!!usersForDisplay && (
              <>
                {isNumber(usersForDisplay) && usersForDisplay > 0 && (
                  <>
                    {t('dialogs.assign_employees.selected')}:{' '}
                    {t(`dialogs.assign_modules.count.employees`, {
                      count: usersForDisplay,
                    })}
                  </>
                )}
              </>
            )}
            {isShowEmptyUserText && t('dialogs.assign_employees.assign_tag')}
          </span>
          <IconWrapper color='primary'>
            <PlusIcon />
          </IconWrapper>
        </Button>
      </div>
      <Button
        onClick={openImportModal}
        className={cx('import')}
        type='button'
        color='green'
        fullWidth
      >
        {t('dialogs.assign_employees.import')}
      </Button>
      {importOpen && (
        <ImportTagEmployeesModal
          refetchUsers={refetchUsers}
          active={importOpen}
          setActive={setImportOpen}
        />
      )}
      {targetOpen && (
        <OrganizationTreeProvider
          handleSelect={data => {
            onSelect({
              target_departments: [...(data?.department_ids ?? [])],
              target_users: !data?.selectAll ? data?.users_ids : [],
              exclude_users_ids: data?.selectAll ? data?.users_ids : [],
            })
            setUserCountFromTree(data?.countOfPeople)
          }}
          open={targetOpen}
          setOpen={setTargetOpen}
          isCanEmpty={true}
        >
          <OrganizationTree />
        </OrganizationTreeProvider>
      )}
    </>
  )
}
