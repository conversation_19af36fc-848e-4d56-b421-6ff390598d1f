import { FC, useState } from 'react'
import styles from './select-course-card.module.scss'
import classNamesBind from 'classnames/bind'
import { SelectCourseCardProps } from './select-course-card.d'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { AssignCourseModal } from '@/shared/modals/assign-course-modal'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const SelectCourseCard: FC<SelectCourseCardProps.Props> = props => {
  const { className, text, selected = [], onChange } = props
  const { t } = useTranslation()

  const [active, setActive] = useState(false)

  const handleSelect = (_: UUID, courses: UUID[]) => {
    onChange(courses[0])
  }

  const handleClick = () => {
    setActive(prev => !prev)
  }

  return (
    <>
      <div
        className={cx('wrapper', className, {
          active: !!selected.length,
        })}
        onClick={handleClick}
      >
        <span>{!selected.length ? text : t('commons:selected_course')}</span>
        <IconWrapper color={!selected.length ? 'gray80' : 'primary'}>
          <PlusIcon />
        </IconWrapper>
      </div>
      <AssignCourseModal
        active={active}
        setActive={setActive}
        handleSelect={handleSelect}
        selected={selected}
        multi={false}
      />
    </>
  )
}
