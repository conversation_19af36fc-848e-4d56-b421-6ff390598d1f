import React from "react";
import classNamesBind from "classnames/bind";
import styles from "./slider.module.scss";
// use v9.2.4 because of https://github.com/react-component/slider/issues/656
import RcSlider from "rc-slider/lib/Slider";
import { SliderProps as RcSliderProps } from "rc-slider";

const cx = classNamesBind.bind(styles);

interface SliderProps extends RcSliderProps {
  className?: string;
}

const Slider: React.FC<SliderProps> = (props) => {
  const { className, ...sliderProps } = props;

  return (
    <RcSlider
      className={cx("slider", className)}
      step={sliderProps?.step || 0.0001}
      {...sliderProps}
    />
  );
};

export { Slider };
