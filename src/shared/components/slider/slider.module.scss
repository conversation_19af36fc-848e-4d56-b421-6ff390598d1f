@use 'sass:math';

$height: 8px;
$color: var(--color-primary);
$active-color: var(--color-component-primary-hover);

$handle-size: 24px;

$offset: math.div($handle-size - $height, 2) + 2px;

.slider {
  // padding: 3px 0 !important;
  position: relative;
  :global(.rc-slider-rail) {
    height: $height;
    background-color: var(--color-gray-50);
    border-radius: 3px;
    top: 50%;
    transform: translateY(-50%);
    position: absolute;
    width: 100%;
  }
  :global(.rc-slider-track) {
    background-color: $color;
    height: $height;
    border-radius: 3px;
    top: 50%;
    transform: translateY(-50%);
    position: absolute;

    &::after {
      content: '';
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
      height: 20px;
      width: 20px;
      background-color: #fff;
      border-radius: 50%;
      border: 2px solid var(--color-primary);
      transition: 0.2s ease-in-out;
    }
  }
  :global(.rc-slider-handle) {
    display: none;
  }
}
