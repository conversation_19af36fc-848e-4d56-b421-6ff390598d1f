import { ButtonIcon, Tooltip } from '@/shared/ui'
import { useCopyToClipboard } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'

type Props = {
  copyValue: string
  tooltipText?: string
  notifyText?: string
}

export const CopyButton = (props: Props) => {
  const { t } = useTranslation()
  const {
    copyValue,
    tooltipText = t('commons:copy'),
    notifyText = t('commons:success_copy'),
  } = props

  const { copyToClipboard } = useCopyToClipboard({ copyValue, notifyText })

  return (
    <Tooltip content={tooltipText}>
      <ButtonIcon color='gray70' size='28' icon='copy' onClick={copyToClipboard} />
    </Tooltip>
  )
}
