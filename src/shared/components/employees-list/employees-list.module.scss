.header {
  align-items: center;

  background: var(--color-surface, #fff);
  border-bottom: 1px solid var(--stroke, #ebeff2);
  border-radius: 16px 16px 0 0;

  display: flex;
  justify-content: space-between;
  padding: 10px 24px 10px 16px;

  .selectAll {
    align-items: center;

    color: var(--color-gray-70, #8e97af);

    display: flex;
    font: var(--font-caption-1-medium);
    gap: 12px;
    letter-spacing: 0.13px;

    span {
      cursor: pointer;
    }
  }
}

.items {
  border-top: 1px solid var(--stroke, #ebeff2);
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.emptyText {
  font: var(--font-caption-1-normal);
}
