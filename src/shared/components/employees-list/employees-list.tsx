import { FC, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'

import { Loader, Pagination } from '@/shared/ui'
import { EmployeeCard, EmployeesPanel, EmployeesSort } from '@/shared/components'
import { IUser } from 'entities/employee'
import { EditEmployeeModal } from '@/shared/modals/edit-employee-modal'
import { Checkbox } from '@/shared/ui/checkbox'
import { useEmployees } from '@/entities/employee'
import styles from './employees-list.module.scss'
import { EmployeesListProps } from './employees-list.d'

const cx = classNamesBind.bind(styles)

export const EmployeesList: FC<EmployeesListProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const {
    employees,
    isLoadingEmployees,
    errorEmployees,
    limit,
    page,
    setPage,
    isFetchingEmployees,
    isAllSelected,
    onSelectAllClick,
  } = useEmployees()
  const navigate = useNavigate()

  const handleChangePage = (page: number) => setPage(page)

  const onEmployeeClick = (id: UUID) => navigate(id)

  const [isEditEmployee, setIsEditEmployee] = useState(false)
  const [editEmployeeData, setEditEmployeeData] = useState<{
    employee: IUser
  } | null>(null)

  const onEditEmployee = async (employee: IUser) => {
    setEditEmployeeData({
      employee,
    })
    setIsEditEmployee(true)
  }

  return (
    <>
      {isLoadingEmployees && <Loader size='56' loading className='loader_centered' />}
      {!isLoadingEmployees && errorEmployees && (
        <div className='error-text'>{t('commons:download_failed')} :(</div>
      )}
      {!isLoadingEmployees && !errorEmployees && !!employees && !employees.data.length && (
        <div className={cx('emptyText')}>{t('commons:not_find_such_employees')} :(</div>
      )}
      {!isLoadingEmployees && !errorEmployees && !!employees && !!employees.data.length && (
        <>
          <div className={cx('wrapper', className)}>
            <div className={cx('header')}>
              <div className={cx('selectAll')}>
                <Checkbox
                  onChange={onSelectAllClick}
                  customChecked={isAllSelected}
                  label={<span>{t('commons:select_all')}</span>}
                  variant='center'
                />
                <Loader loading={isFetchingEmployees} />
              </div>
              <EmployeesSort />
            </div>
            <div className={cx('items')}>
              {employees.data.map(e => {
                return (
                  <EmployeeCard
                    key={`employee-${e.id}`}
                    info={e}
                    onEditEmployee={onEditEmployee}
                    onEmployeeClick={onEmployeeClick}
                  />
                )
              })}
            </div>
          </div>
          <Pagination
            limit={limit}
            currentPage={page}
            total={employees.total_count}
            onChange={handleChangePage}
            withEmptySpace
            isLoading={isFetchingEmployees || isLoadingEmployees}
          />

          {editEmployeeData && isEditEmployee && (
            <EditEmployeeModal
              active={isEditEmployee}
              setActive={setIsEditEmployee}
              data={editEmployeeData}
            />
          )}

          <EmployeesPanel />
        </>
      )}
    </>
  )
}
