.wrapper {
  align-items: center;
  display: flex;
  justify-content: center;
  width: 100%;
}

.item {

  border-radius: 8px;

  color: var(--color-gray-80);
  display: flex;
  font: var(--font-text-1-normal);
  margin: 0 2px;
  padding: 10px 12px;

  transition: 100ms ease-out;

  &:hover {
    background-color: var(--color-gray-40);
  }
  &:active {
    background-color: var(--color-gray-50);
  }

  &.active {

    background-color: var(--color-primary-20);
    color: var(--color-primary);
  }
}
