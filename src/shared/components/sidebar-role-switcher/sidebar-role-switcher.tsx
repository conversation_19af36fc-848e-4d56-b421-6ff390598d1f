import { FC } from 'react'
import styles from './sidebar-role-switcher.module.scss'
import classNamesBind from 'classnames/bind'
import { SidebarRoleSwitcherProps } from './sidebar-role-switcher.d'
import { Link, useLocation } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { URLS } from '../../configs/urls'
import { useUserPermissions } from '../../../entities/employee'

const cx = classNamesBind.bind(styles)

const isRouteActive = (route: string, prefix: string) => route.startsWith(prefix)

export const SidebarRoleSwitcher: FC<SidebarRoleSwitcherProps.Props> = props => {
  const { className } = props

  const { pathname } = useLocation()
  const data = useUserPermissions()

  const { t } = useTranslation()

  return (
    <div className={cx('wrapper', className)}>
      {data?.admin && (
        <Link
          className={cx('item', isRouteActive(pathname, '/lk/admin') && 'active')}
          to={URLS.ADMIN_STATISTICS_GENERAL_PAGE}
        >
          {t('commons:admin_role')}
        </Link>
      )}
      {data?.user && (
        <Link
          className={cx('item', isRouteActive(pathname, '/lk/user') && 'active')}
          to='/lk/user/learning'
        >
          {t('commons:employee_role')}
        </Link>
      )}
    </div>
  )
}
