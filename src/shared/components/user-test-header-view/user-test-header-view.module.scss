.wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 16px 0;
}

.title {
    font: var(--font-title-1-medium);
    color: var(--color-primary);
    margin-bottom: 8px;

    &.fail {
        color: var(--color-error);
    }
}

.hint {
    font: var(--font-text-2-normal);
    color: var(--color-gray-80);
    margin-bottom: 16px;
}

.buttonsWrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 24px;
}

.bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.attempt {
    font: var(--font-title-4-normal);
}

.info {
    display: flex;
    gap: 8px;
    color: var(--color-gray-80);
    font: var(--font-caption-1-normal);
}

.button {
    margin-bottom: 24px;
}