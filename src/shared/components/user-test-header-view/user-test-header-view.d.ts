import { Question } from "@/entities/courses/model/api/test-types";

export declare namespace UserTestHeaderViewProps {
  interface Own {
    attempt?: number
    questions?: Question[]
    maxPoints?: number
    started?: string
    finished?: string
    passed?: boolean
    limit?: number
    learningBlock?: boolean
    handleRepeat?: () => void
    openModal: () => void
    isLoading?: boolean
  }

  type Props = Own
}

export {}
