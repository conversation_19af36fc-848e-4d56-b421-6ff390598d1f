import classNamesBind from 'classnames/bind'
import styles from './user-test-header-view.module.scss'
import { UserTestHeaderViewProps } from './user-test-header-view.d'
import React, { useTransition } from 'react'
import { useTranslation } from 'react-i18next'
import { Button } from '@/shared/ui'
import { format } from '@/shared/helpers/date'
import { useAppDispatch, useAppSelector } from '@/store'
import { goToTheory, handleNextStep, selectLastStep } from '@/store/slices/user-course-slice'
import { CompleteModal } from '../../modals/editors/complete'
import { GOALS } from '@/shared/constants'
import { useAnalytics } from '@/shared/hooks/use-analytics'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'components__user-test-header'
const DATE_FORMAT = 'dd.MM.yyyy'

export const UserTestHeaderView: React.FC<UserTestHeaderViewProps.Props> = ({
  attempt,
  questions,
  maxPoints,
  started,
  finished,
  passed,
  limit,
  learningBlock,
  handleRepeat,
  openModal,
  isLoading,
}) => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const dispatch = useAppDispatch()
  const analytics = useAnalytics()

  const points = questions?.reduce((acc, item) => {
    if (item.passed) {
      return acc + 1
    }
    return acc
  }, 0)

  const isLastStep = useAppSelector(selectLastStep)
  const [isPendingTransition, startTransition] = useTransition()

  const handleBack = () => {
    analytics.event(GOALS['course-back-to-theory-after-quiz'].name)
    dispatch(goToTheory())
  }
  const handleNext = () => {
    analytics.event(GOALS['course-next-theme-item-after-quiz'].name)
    dispatch(handleNextStep())
  }

  const startDate = started ? new Date(started) : new Date()
  const finishedDate = finished ? new Date(finished) : new Date()

  const duration = Math.round((finishedDate.getTime() - startDate.getTime()) / 60000)

  const onRepeat = () => {
    startTransition(() => {
      if (!!attempt && !!limit && attempt >= limit) {
        openModal()
      } else {
        handleRepeat && handleRepeat()
      }
    })
  }

  return (
    <div className={cx('wrapper')}>
      <div
        className={cx('title', {
          fail: !passed,
        })}
      >
        {passed
          ? t('test_success', {
              points,
              maxPoints,
            })
          : t('test_fail', {
              points,
              maxPoints,
            })}
      </div>
      <div className={cx('hint')}>
        {passed
          ? t('hint_success', {
              points,
              maxPoints,
            })
          : t('hint_fail', {
              points,
              maxPoints,
            })}
      </div>
      {passed ? (
        isLastStep ? (
          <CompleteModal />
        ) : (
          <Button size='big' color='green' onClick={handleNext} className={cx('button')}>
            {t('commons:next')}
          </Button>
        )
      ) : (
        <div className={cx('buttonsWrapper')}>
          <Button size='big' color='green' onClick={handleBack}>
            {t('back_to_theory')}
          </Button>
          <Button
            size='big'
            disabled={isPendingTransition || isLoading}
            color='darkGray'
            onClick={onRepeat}
          >
            {t('repeat')}
          </Button>
          {!learningBlock && (
            <Button size='big' color='green' onClick={handleNext}>
              {t('commons:next')}
            </Button>
          )}
        </div>
      )}
      <div className={cx('bottom')}>
        <div className={cx('attempt')}>
          {t('attempt', {
            count: attempt,
          })}
        </div>
        <div className={cx('info')}>
          <span>
            {t('point')}: {points}/{maxPoints}
          </span>
          <span>
            {t('date')}: {format(startDate, DATE_FORMAT)}
          </span>
          <span>
            {t('duration')}:{' '}
            {t('minutes', {
              count: duration,
            })}
          </span>
        </div>
      </div>
    </div>
  )
}
