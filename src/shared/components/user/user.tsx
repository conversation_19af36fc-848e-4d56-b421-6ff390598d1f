import React from "react";
import styles from "./user.module.scss";
import classNamesBind from "classnames/bind";
import { UserProps } from "./user.d";
import { OldText } from "@/shared/components";

const cx = classNamesBind.bind(styles);

const OldUser: React.FC<UserProps.Props> = (props) => {
  const {
    className,
    user,
    onClick,
    color = "black",
    mobileAdaptive,
    showMiddleName,
  } = props;

  const isPlaceholder = !user;

  const {
    picture,
    stats_summary,
    first_name,
    last_name,
    position,
    middle_name,
    email,
    department_title,
  } = user || {};

  const needInfo =
    isPlaceholder ||
    first_name ||
    last_name ||
    position ||
    email ||
    department_title;

  const risk_level = stats_summary?.risk_level.value;
  const risk_color = stats_summary?.risk_level.color;

  return (
    <div className={cx("wrapper", className)} onClick={onClick}>
      <div
        className={cx("avatar", `color-${risk_color}`, {
          placeholder: isPlaceholder,
        })}
      >
        <img className={cx("image")} src={picture} alt={""} />
        {risk_level !== null && risk_level !== undefined && (
          <div className={cx("riskLevel")}>{risk_level.toFixed()}</div>
        )}
      </div>
      {needInfo && (
        <div
          className={cx("userInfo", {
            mobileAdaptive,
          })}
        >
          <OldText
            variant="h4"
            placeholder={{ width: "70%" }}
            color={color}
            className={cx("userName")}
          >
            {[last_name, first_name, showMiddleName && middle_name]
              .filter((v) => !!v)
              .join(" ") || email}
          </OldText>
          {(position || department_title || isPlaceholder) && (
            <OldText
              variant="smaller"
              color={color}
              placeholder={{ width: "40%" }}
              className={cx("userPosition")}
            >
              {department_title}
              {position && department_title && " / "}
              {position}
            </OldText>
          )}
        </div>
      )}
    </div>
  );
};

export { OldUser };
