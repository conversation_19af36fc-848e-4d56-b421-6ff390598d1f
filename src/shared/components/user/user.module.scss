// @use "placeholders";
// @use "media";
// @use "mixins/colors";

.wrapper {
  align-items: center;
  box-sizing: border-box;
  display: flex;

  @media (max-width: 1024px) {
    padding: 0;
  }
}
.avatar {
  flex-shrink: 0;
  height: 40px;
  position: relative;
  width: 40px;
  .image {
    background-color: var(--brown-grey);
    border: 3px solid var(--brown-grey);
    border-radius: 50%;
    box-sizing: border-box;
    height: 100%;
    object-fit: cover; // TODO not work in ie
    object-position: center;
    width: 100%;
  }
  .riskLevel {
    align-items: center;

    background-color: var(--brown-grey);
    border-radius: 4px;

    color: var(--white);

    display: flex;
    font-size: 10px;
    font-weight: bold;
    height: 16px;
    justify-content: center;
    position: absolute;
    right: -4px;
    top: -4px;
    width: 20px;
    z-index: 1;
  }
  &.placeholder {
    opacity: 0.5;
    .image {
      // background-color: var(--grey);
      // @include placeholders.rounded;
      border: none;
    }
  }

  // &.color {
  //   // @include colors.color() using($color) {
  //   //   .image {
  //   //     border-color: $color;
  //   //   }
  //   //   .riskLevel {
  //   //     background-color: $color;
  //   //   }
  //   // }
  // }

  @media (max-width: 1024px) {
    display: none;
  }
}

.userInfo {
  display: flex;
  flex: 1 0;
  flex-direction: column;
  justify-content: center;
  margin-left: 15px;
  .userPosition {
    margin-top: 5px;
  }
  // &.mobileAdaptive {
  //   @include media.mobile {
  //     display: none;
  //   }
  // }
}
