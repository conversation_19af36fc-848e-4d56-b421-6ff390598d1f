import { useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Tabs } from "@/shared/components";

export const CourseTypeTabs = () => {
  const [currentQueryParameters, setSearchParams] = useSearchParams();
  const newQueryParameters: URLSearchParams = new URLSearchParams();
  const type = currentQueryParameters.get("type") || "old";

  const handleClick = (value: string) => {
    if (!value) return;

    newQueryParameters.set("type", value);
    setSearchParams(newQueryParameters);
  };

  const { t } = useTranslation();

  const tabs = [
    {
      value: t("modules.store_courses.tabs.old"),
      name: "old",
    },
    {
      value: t("modules.store_courses.tabs.scorm"),
      name: "scorm",
    },
  ];

  return <Tabs tabs={tabs} onClick={handleClick} active={type} />;
};

export default CourseTypeTabs;
