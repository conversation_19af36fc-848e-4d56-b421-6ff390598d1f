/* eslint-disable @typescript-eslint/no-explicit-any */
import styles from './tabs.module.scss'
import classNamesBind from 'classnames/bind'
import { TabsProps } from './tabs.d'

const cx = classNamesBind.bind(styles)

export function Tabs<T = string>(props: TabsProps.Props<T>) {
  const { className, tabs, onClick, active, tabClassname, activeTabClassname, backAdornment } =
    props

  const handleClick = (value: string) => {
    if (onClick) onClick(value)
  }

  return (
    <div className={cx('wrapper', className)}>
      {tabs &&
        tabs.map((tab: any) => {
          const isActive = active === tab.name

          return (
            <div
              key={`${tab.name}-${tab.value}`}
              onClick={() => handleClick(tab.name)}
              className={cx(
                `item`,
                tabClassname,
                isActive && 'active',
                isActive && activeTabClassname,
              )}
            >
              {tab.value}
            </div>
          )
        })}
      {backAdornment}
    </div>
  )
}
