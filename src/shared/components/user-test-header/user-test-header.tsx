import classNamesBind from 'classnames/bind'
import styles from './user-test-header.module.scss'
import { UserTestHeaderProps } from './user-test-header.d'
import React, { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import TimerIcon from '@/shared/ui/Icon/icons/components/TimerIcon'
import RepeatIcon from '@/shared/ui/Icon/icons/components/RepeatIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTimer } from '@/shared/hooks'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'components__user-course-header'

export const UserTestHeader: React.FC<UserTestHeaderProps.Props> = ({
  attempts,
  start_date,
  finished,
  isLimit,
  duration,
}) => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const time_limit = useMemo(() => {
    const date = new Date(start_date)
    date.setMinutes(date.getMinutes() + duration)
    return date.toISOString()
  }, [start_date, duration])
  const { time } = useTimer(time_limit)

  return (
    <div className={cx('wrapper')}>
      {!finished && (
        <div className={cx('time')}>
          <IconWrapper size='24' color='gray70'>
            <TimerIcon />
          </IconWrapper>
          {time > 60000
            ? t('minute_limit', {
                count: Math.floor(time / 60000),
              })
            : time <= 0
              ? t('time_is_over')
              : t('second_limit', {
                  count: Math.floor(time / 1000),
                })}
        </div>
      )}
      {isLimit && (
        <div className={cx('attempts')}>
          <IconWrapper size='24' color='gray70'>
            <RepeatIcon />
          </IconWrapper>
          {t('attempts', {
            count: attempts,
          })}
        </div>
      )}
    </div>
  )
}
