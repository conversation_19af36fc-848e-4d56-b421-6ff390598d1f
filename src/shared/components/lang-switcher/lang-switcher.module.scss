.langSwitcherWrapper {
  position: relative;
}

.listWrapper {
  position: absolute;
  bottom: 60px;
  left: 110px;
  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 8px;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
  max-width: 225px;
  min-width: 140px;
  padding: 5px 0;
  transform: translateY(4px);
  width: max-content;
  z-index: 10;
}

.listItem {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 15px;

  &:hover {
    background-color: var(--color-gray-40);
  }
}

.langSwitcher {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 68px;
  padding: 0 20px;
  color: var(--color-gray-80);
  flex-shrink: 1;
  font: var(--font-text-1-normal);
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &:hover {
    background-color: var(--color-gray-40);
  }
}

.active {
  background-color: var(--color-gray-40);
}

.labelLanguageWrapper {
  display: flex;
  flex: 1 1;
  margin-left: 12px;
  margin-right: 4px;
  min-width: 0;
}

.labelLanguage {
  color: var(--color-gray-80);
  flex-shrink: 1;
  font: var(--font-text-1-normal);
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
