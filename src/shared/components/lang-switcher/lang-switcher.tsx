import { useCallback, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import FlagRussia from '@/shared/ui/Icon/icons/components/FlagRussia'
import FlagUsa from '@/shared/ui/Icon/icons/components/FlagUsa'
import FlagUzIcon from '@/shared/ui/Icon/icons/components/FlagUzIcon'
import ArrowDown from '@/shared/ui/Icon/icons/components/ArrowDown'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useEvent } from '@/shared/hooks'
import styles from './lang-switcher.module.scss'
import { SUPPORTED_LANGUAGES } from '@/shared/constants/supportedLanguages'
import { selectCurrentLanguage } from '@/store/slices/language'
import { useAppSelector } from '@/store'

const cx = classNamesBind.bind(styles)

const renderFlagIcon = (iconName: string) => {
  switch (iconName) {
    case 'flagRussia':
      return <FlagRussia />
    case 'flagUsa':
      return <FlagUsa />
    case 'flagUz':
      return <FlagUzIcon />
    default:
      return <FlagRussia />
  }
}

export const LangSwitcher = () => {
  const { t, i18n } = useTranslation()

  const [isActive, setIsActive] = useState(false)
  const wrapper = useRef<HTMLDivElement>(null)
  const currentLanguage = useAppSelector(selectCurrentLanguage)

  const changeLanguage = useCallback(async (name: string) => {
    i18n.changeLanguage(name)
  }, [])

  const handleButtonClick = () => {
    setIsActive(prev => !prev)
  }

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) {
      setIsActive(false)
    }
  }, [])

  const handleLanguageClick = (lng: string) => {
    if (lng !== currentLanguage?.code) {
      changeLanguage(lng)
      setIsActive(false)
    }
  }

  useEvent('click', handleOutsideClick, window)

  return (
    <div ref={wrapper} className={cx('langSwitcherWrapper')}>
      <button onClick={handleButtonClick} className={cx('langSwitcher')}>
        {currentLanguage && (
          <>
            <IconWrapper size='20' color='self'>
              {renderFlagIcon(currentLanguage.icon)}
            </IconWrapper>
            <div className={cx('labelLanguageWrapper')}>
              <p className={cx('labelLanguage')}>{t(`commons:${currentLanguage.name}`)}</p>
            </div>
          </>
        )}
        <IconWrapper direction={isActive ? 'down' : 'up'} size='20'>
          <ArrowDown />
        </IconWrapper>
      </button>
      {isActive && (
        <div className={cx('listWrapper')} onClick={e => e.stopPropagation()}>
          {SUPPORTED_LANGUAGES.map(supportedLanguage => (
            <button
              key={supportedLanguage.code}
              className={cx('listItem', {
                active: currentLanguage && supportedLanguage.code === currentLanguage.code,
              })}
              onClick={() => handleLanguageClick(supportedLanguage.code)}
            >
              <IconWrapper size='20' color='self'>
                {renderFlagIcon(supportedLanguage.icon)}
              </IconWrapper>
              <div className={cx('labelLanguageWrapper')}>
                <p className={cx('labelLanguage')}>{t(`commons:${supportedLanguage.name}`)}</p>
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
