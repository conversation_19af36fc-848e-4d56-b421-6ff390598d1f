import classNamesBind from 'classnames/bind'
import styles from './test-card.module.scss'
import { TestCardProps } from './test-card.d'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Checkbox } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'components__test-card'

export const TestCard: React.FC<TestCardProps.Props> = ({
  question,
  order,
  answers: answersArr,
  onChangeAnswer,
}) => {
  const { text, answers, multiple_answers: hasManyAnswers } = question

  const { t } = useTranslation(TRANSLATION_FILE)

  return (
    <div className={cx('wrapper')}>
      <div className={cx('title')}>
        {order}. {text}
      </div>
      <div className={cx('hint')}>
        {hasManyAnswers ? t('choose_some_answers') : t('choose_one_answer')}
      </div>
      <ul className={cx('answers')}>
        {answers.map(answer => (
          <li className={cx('answer')} key={answer.id}>
            <Checkbox
              type={hasManyAnswers ? 'square' : 'circle'}
              onClick={() => onChangeAnswer(question.id, answer.id, question.multiple_answers)}
              customChecked={answersArr && answersArr.includes(answer.id)}
              checkboxClassName={cx('checkbox')}
              iconClassName={cx('icon')}
              label={<div className={cx('text')}>{answer.text}</div>}
              // customChecked={answer.active}
            />
          </li>
        ))}
      </ul>
    </div>
  )
}
