// @use 'placeholders';
// @use 'media';

.block {
  display: inline-block;
}

.h3 {
  color: var(--dark);
  font-family: var(--font-title), sans-serif;
  font-size: 18px;
  font-weight: bold;
}

.h4 {
  color: var(--dark);
  font-family: var(--font-title), sans-serif;
  font-size: 14px;
  font-weight: bold;
}

.normal,
.block {
  color: var(--dark);
  font-family: var(--font-title), sans-serif;
  font-size: 12px;
  font-weight: normal;
  letter-spacing: 0.3px;
  line-height: 1.17;
}

.small-normal {
  font-family: var(--font-title), sans-serif;
  font-size: 11px;
  line-height: 14px;
}

.middle-normal {
  font-family: var(--font-title), sans-serif;
  font-size: 14px;
  line-height: 17px;
}

.major-normal {
  font-family: var(--font-title), sans-serif;
  font-size: 16px;
  line-height: 19px;
}

.big-normal {
  font-family: var(--font-title), sans-serif;
  font-size: 18px;
  letter-spacing: 0.3px;
  line-height: 22px;
}

.bigger-normal {
  font-family: var(--font-title), sans-serif;
  font-size: 24px;
  line-height: 37px;
}

.big {
  font-family: GothamPro, sans-serif;
  font-size: 60px;
  letter-spacing: -1.5px;
  line-height: 37px;
  .placeholder {
    height: 60px;
  }
}

.brown-grey {
  color: var(--brown-grey);
}

.gray {
  color: var(--blue-grey);
}

.gark-gray {
  color: var(--gark-gray);
}

.leaf {
  color: var(--color-primary);
}
.green {
  color: var(--color-statistics-good);
}

.red {
  color: var(--color-error);
}

.yellow {
  color: var(--color-statistics-warning);
}

.white {
  color: var(--white);
}

.bright-cyan {
  color: var(--color-statistics-complementary);
}

.bold {
  font-weight: bold;
}

.gigant {
  font-family: GothamPro, sans-serif;
  font-size: 180px;
  letter-spacing: -4.5px;
  line-height: 1;
  .placeholder {
    height: 130px;
    margin: 10px 0 40px;
  }
}

.gigant-small {
  font-family: GothamPro, sans-serif;
  font-size: 80px;
  letter-spacing: -1.5px;
}

.underline {

  cursor: pointer;
  text-decoration: underline;
  white-space: nowrap;
}

.h2 {
  font-family: var(--font-title), sans-serif;
  font-size: 48px;
}

.smaller {
  font-size: 10px;
  font-weight: normal;
}

.var18 {
  line-height: 18px;
}

.var24 {
  line-height: 24px;
}

.more {
  letter-spacing: 0.35px;
}

.less {
  letter-spacing: 0.25px;
}

.no {
  letter-spacing: 0;
}

.big-space {
  letter-spacing: 1px;
}

.uppercase {
  text-transform: uppercase;
}

.alignSelf {
  align-self: center;
}

// @include media.mobile {
// 	.big-normal {
// 		font-weight: bold;
// 	}
// 	.gigant {
// 		font-size: 150px;
// 		letter-spacing: -1.5px;
// 	}
// 	.h2 {
// 		font-size: 36px;
// 		line-height: 1.03;
// 	}
// 	.h3 {
// 		flex-wrap: wrap;

// 		font-size: 18px;
// 		line-height: 1.14;
// 	}
// 	.h4 {
// 		font-size: 12px;
// 		font-weight: normal;
// 	}
// }

// .placeholder {
// 	@include placeholders.simple;
// 	display: block;
// 	& + .placeholder {
// 		margin-top: 2%;
// 	}
// }
