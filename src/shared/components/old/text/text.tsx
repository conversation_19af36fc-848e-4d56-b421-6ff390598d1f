/* eslint-disable no-mixed-spaces-and-tabs */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React from "react";
import styles from "./text.module.scss";
import classNamesBind from "classnames/bind";

const cx = classNamesBind.bind(styles);

export type TextVariantsType =
  | "h3"
  | "normal"
  | "block"
  | "gigant"
  | "big-normal"
  | "bigger-normal"
  | "middle-normal"
  | "major-normal"
  | "small-normal"
  | "h2"
  | "h4"
  | "gigant-small"
  | "smaller"
  | "big";

export type TextColor =
  | "black"
  | "gray"
  | "brown-grey"
  | "leaf"
  | "red"
  | "green"
  | "yellow"
  | "white"
  | "bright-cyan"
  | "dark-gray";

interface TextProps {
  variant: TextVariantsType;
  color?: TextColor;
  spacing?: "more" | "less" | "no" | "big-space";
  lineHeight?: "var18" | "var24";
  bold?: boolean;
  underline?: boolean;
  uppercase?: boolean;
  alignSelf?: boolean;
  onClick?: Func;
  className?: string;
  placeholder?: { width: number | string; lines?: number };
  children?: any;
}

const tagMap: Record<TextVariantsType, string> = {
  h2: "h2",
  h3: "h3",
  h4: "h4",
  block: "div",
  normal: "span",
  gigant: "span",
  "gigant-small": "span",
  "bigger-normal": "span",
  "big-normal": "span",
  "middle-normal": "span",
  "major-normal": "span",
  "small-normal": "span",
  smaller: "div",
  big: "span",
};

export const OldText: React.FC<TextProps> = ({
  onClick,
  underline,
  bold,
  uppercase,
  children,
  variant,
  color,
  spacing,
  lineHeight,
  alignSelf,
  className,
  placeholder,
}) => {
  const Tag = tagMap[variant] as React.ElementType;
  const needPlaceholder =
    placeholder && !React.Children.toArray(children).some((c) => !!c);

  return (
    <Tag
      className={cx(styles[variant], className, color, spacing, lineHeight, {
        bold,
        underline,
        uppercase,
        alignSelf,
        block: tagMap[variant] === "span",
      })}
      style={needPlaceholder ? { width: placeholder?.width } : undefined}
      onClick={onClick}
    >
      {needPlaceholder
        ? Array.from({ length: placeholder?.lines || 1 }).map((__, i, self) => (
            <span key={i} className={cx("placeholder")}>
              {"\u00A0"} {i < self.length - 1 && <br />}
            </span>
          ))
        : children}
    </Tag>
  );
};
