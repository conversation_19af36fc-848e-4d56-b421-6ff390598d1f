import React from "react";
import classNamesBind from "classnames/bind";
import styles from "./link-card.module.scss";

import { LinkCardProps } from "./link-card.d";
import { Card, OptionalLink } from "@/shared/ui";

const cx = classNamesBind.bind(styles);

const LinkCard: React.FC<LinkCardProps.Props> = (props) => {
  const { className, cardClass, to, children, ...cardProps } = props;

  return (
    <OptionalLink className={cx("wrapper", className)} to={to}>
      <Card padding="big" className={cardClass} {...cardProps}>
        {children}
      </Card>
    </OptionalLink>
  );
};

export default LinkCard;
