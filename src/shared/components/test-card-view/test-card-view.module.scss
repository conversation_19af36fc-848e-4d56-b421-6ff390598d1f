.wrapper {
  background-color: var(--white);
  border-radius: 16px;
  padding: 16px;
  color: var(--color-gray-90);
  margin-bottom: 16px;

  &.success {
    border: 2px solid var(--color-statistics-good);
  }
  &.fail {
    border: 2px solid var(--color-error);
  }
  &.half {
    border: 2px solid #fdf2d4;
  }
}

.inner {
  padding: 24px 32px;
}

.title {
  font: var(--font-title-4-medium);
  margin-bottom: 8px;
}

.hint {
  font: var(--font-text-2-normal);
  margin-bottom: 16px;
}

.answers {
  display: grid;
  grid-row-gap: 12px;
}

.text {
  font: var(--font-text-1-normal);
  color: var(--color-gray-80);

  &.green {
    color: var(--color-statistics-good);
  }
  &.red {
    color: var(--color-error);
  }
}

.top {
  display: flex;
  align-items: center;
  gap: 12px;
  font: var(--font-caption-1-normal);
  color: var(--color-gray-80);
}

.question {
  font: var(--font-text-2-normal);
  border-radius: 8px;
  padding: 4px 8px;
  text-transform: lowercase;
  color: var(--color-gray-90);

  &.success {
    background-color: #bee9d7;
  }
  &.fail {
    background-color: #ffdfe3;
  }
  &.half {
    background-color: #fdf2d4;
  }
}

.tip {
  margin-top: 16px;
  font: var(--font-text-2-normal);
}

.tipContent {
  margin-top: 8px;

  &.yellow {
    color: #ffa740;
  }
  &.success,
  &.green {
    color: #29a873;
  }
  &.red,
  &.fail {
    color: #ff4b60;
  }
}

.checkbox {
  &:checked {
    background-color: var(--color-statistics-good) !important;
    border: 2px solid var(--color-statistics-good) !important;

    &.red {
      background-color: #ff4b60 !important;
      border: 2px solid #ff4b60 !important;
    }
  }
}
