import { FC, ReactElement, memo } from 'react'
import styles from './checkbox.module.scss'
import classNamesBind from 'classnames/bind'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import SuccessIcon from '@/shared/ui/Icon/icons/components/SuccessIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

type Props = {
  label?: string | ReactElement
  className?: string
  checkboxClassName?: string
  isChecked: boolean
  isCorrect: boolean
  disabled: boolean
  type?: 'circle' | 'square'
}

const cx = classNamesBind.bind(styles)

// Функция для рендеринга иконок
const renderIcon = (isCorrect: boolean, className?: string) => {
  return (
    <IconWrapper color='white' className={className}>
      {isCorrect ? <SuccessIcon /> : <PlusIcon />}
    </IconWrapper>
  )
}

export const Checkbox: FC<Props> = memo(
  ({
    label,
    className,
    checkboxClassName,
    isChecked,
    isCorrect,
    type = 'square',
    disabled = false,
  }) => {
    return (
      <label className={cx('checkboxWrapper', className)} onClick={e => e.stopPropagation()}>
        <div className={cx('checkboxBase')}>
          <input
            type='checkbox'
            className={cx('checkboxInput', checkboxClassName, type, {
              checked: isChecked,
              correct: isCorrect,
              disabled: disabled,
              red: !isCorrect,
            })}
            checked={isChecked}
            readOnly
          />
          {isChecked &&
            renderIcon(
              isCorrect,
              cx('icon', {
                incorrect: !isCorrect,
              }),
            )}
        </div>
        {label}
      </label>
    )
  },
)

Checkbox.displayName = 'Checkbox'
