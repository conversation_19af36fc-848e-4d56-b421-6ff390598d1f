.checkboxWrapper {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;

  .checkboxInput {
    -webkit-appearance: none;
    appearance: none;

    width: 20px;
    height: 20px;
    flex-shrink: 0;

    background: transparent;
    border: 2px solid #c9cedc;
    border-radius: 6px;

    transition: var(--transition);

    &.checked {
      background: var(--color-primary);
      border: 2px solid var(--color-primary);

      transition: var(--transition);

      &.red {
        background: var(--color-error);
        border: 2px solid var(--color-error);
      }
    }

    &.correct {
      border: 2px solid var(--color-primary);

      transition: var(--transition);
    }
  }

  .circle {
    position: relative;
    border-radius: 50px;
    width: 20px;
    height: 20px;
  }
}

.checkboxBase {
  display: flex;
  position: relative;

  .icon {
    position: absolute;
    top: 0;
    left: 0;
  }
}


.icon {
  &.incorrect {
    transform: rotate(45deg);
  }
}