import { QuestionContent } from '../../../entities/questions/model'

export type Answer = {
  id: UUID,
  text: string,
  is_correct: boolean,
  is_selected: boolean,
  tip?: string
}

interface IQuestion extends QuestionContent {
  answers: Answer[];
  passed: boolean;
}

export declare namespace TestCardViewProps {
  interface Own {
    question: IQuestion
    order: number
    needExplain?: boolean
  }

  type Props = Own
}
