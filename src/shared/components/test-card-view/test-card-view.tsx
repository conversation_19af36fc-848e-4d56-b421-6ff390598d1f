import classNamesBind from 'classnames/bind'
import styles from './test-card-view.module.scss'
import { TestCardViewProps, Answer } from './test-card-view.d'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { Checkbox } from './components/checkbox'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'components__test-card'

export const TestCardView: React.FC<TestCardViewProps.Props> = ({
  question,
  order,
  needExplain,
}) => {
  const { text, answers, multiple_answers, passed } = question

  const { t } = useTranslation(TRANSLATION_FILE)

  const result = passed
    ? 'success'
    : multiple_answers && !!answers.find((a: Answer) => !!a.is_correct && !!a.is_selected)
      ? 'half'
      : 'fail'

  const resultHint = {
    success: t('correct_answer'),
    fail: t('incorrect_answer'),
    half: t('incorrect_answer'),
  }

  return (
    <div className={cx('wrapper', result)}>
      <div className={cx('top')}>
        <div className={cx('question', result)}>
          {order} {t('question')}
        </div>
        ({resultHint[result]})
      </div>
      <div className={cx('inner')}>
        <div className={cx('title')}>{text}</div>
        {needExplain && (
          <>
            <div className={cx('hint')}>
              {multiple_answers ? t('choose_some_answers') : t('choose_one_answer')}
            </div>
            <ul className={cx('answers')}>
              {answers.map(answer => (
                <li className={cx('answer')} key={answer.id}>
                  <Checkbox
                    type={multiple_answers ? 'square' : 'circle'}
                    checkboxClassName={cx('checkbox', {
                      red: answer.is_selected && !answer.is_correct,
                    })}
                    label={
                      <div
                        className={cx('text', {
                          green: answer.is_correct,
                          red: answer.is_selected && !answer.is_correct,
                        })}
                      >
                        {answer.text}
                      </div>
                    }
                    isChecked={answer.is_selected}
                    isCorrect={answer.is_correct}
                    disabled
                  />
                  {answer.tip && (
                    <div className={cx('tip')}>
                      <span>{t('answer_tip')}:</span>
                      <div
                        className={cx('tipContent', result, {
                          green: answer.is_correct && answer.is_selected,
                          red: answer.is_selected && !answer.is_correct,
                          yellow: !answer.is_selected && answer.is_correct,
                        })}
                      >
                        {answer.tip}
                      </div>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          </>
        )}
      </div>
    </div>
  )
}
