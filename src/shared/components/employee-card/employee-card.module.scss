// @use "mixins/icons";

.wrapper {
  align-items: center;

  background: var(--color-surface, #fff);

  border-bottom: 1px solid var(--stroke, #ebeff2);

  cursor: pointer;
  display: grid;
  grid-gap: 12px;
  grid-template-columns: auto 200px 1fr auto;
  padding: 12px 16px;

  &:last-child {
    border-bottom: none;
  }
  &:hover,
  &.active {
    background: var(--color-gray-20, #f9fafc);

    transition: var(--transition);
  }

  &.notRegistred {
    cursor: default;
    .info {
      opacity: 0.5;
    }
  }

  .nameWrapper {
    margin-left: 12px;
    overflow: hidden;
  }

  .name {
    color: var(--color-gray-90, #343b54);
    font: var(--font-text-2-medium);
    margin-bottom: 4px;
  }
  .email {
    color: var(--color-gray-80, #5c6585);
    font: var(--font-caption-1-medium);
    margin-bottom: 4px;
    word-break: break-all;
  }

  .department {
    box-orient: vertical;

    color: var(--color-gray-90, #343b54);
    display: box;
    font: var(--font-text-2-normal);
    -webkit-line-clamp: 2;
    margin-bottom: 4px;
    max-height: 40px;

    overflow: hidden;
  }
  .position {
    color: var(--color-gray-80, #5c6585);
    font: var(--font-caption-1-normal);
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;

    white-space: nowrap;
  }

  .info:last-child {
    margin-left: 20px;
  }

  .info {
    > *:last-child {
      margin-bottom: 0;
    }
  }

  .icons {
    align-items: center;
    display: flex;

    display: grid;
    gap: 16px;
    grid-gap: 12px;
    grid-template-columns: 57px 38px 57px;
    .icon {
      align-items: center;

      color: var(--color-gray-70, #8e97af);
      display: flex;
      font: var(--font-caption-2-normal);
      gap: 4px;

      &.color {
        &--GREEN {
          color: var(--color-statistics-good, #3dbc87);
          // @include icons.color(var(--color-statistics-good, #3dbc87));
        }
        &--RED {
          color: var(--color-statistics-bad-text, #ff4b60);
          // @include icons.color(var(--color-statistics-bad-text, #ff4b60));
        }
        &--YELLOW {
          color: var(--color-statistics-warning, #ffc700);
          // @include icons.color(var(--color-statistics-warning, #ffc700));
        }
      }
    }
  }

  .tags {
    position: relative;
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    max-width: 770px;
    margin-bottom: -4px !important;

    margin-right: -4px;

    .tag {
      max-width: 170px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      background: var(--stat-neutral-light, #e1e4eb);

      border-radius: 8px;

      color: var(--stat-neutral, #8e97af);
      color: var(--color-surface, #fff);
      font: var(--font-caption-2-medium);
      margin-bottom: 4px;

      margin-right: 4px;
      padding: 2px 6px;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .employeeIcons {
    align-items: center;
    display: none;
    gap: 12px;
  }

  &:hover {
    .employeeIcons {
      display: flex;
    }
  }
}
