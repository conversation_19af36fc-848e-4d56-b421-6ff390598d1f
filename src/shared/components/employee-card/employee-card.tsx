import React, { FC } from 'react'
import classNamesBind from 'classnames/bind'

import { useEmployees } from '@/entities/employee'
import { ButtonIcon } from '@/shared/ui'
import { getFullName, getRickLevelColor } from '@/shared/helpers'
import { IconWithData } from '@/shared/components/icon-with-data'
import { Checkbox } from '@/shared/ui/checkbox'

import styles from './employee-card.module.scss'
import { EmployeeCardProps } from './employee-card.d'
import { IUser } from 'entities/employee'

const cx = classNamesBind.bind(styles)

export const EmployeeCard: FC<EmployeeCardProps.Props> = props => {
  const { className, info, isActive, onEditEmployee, onEmployeeClick, withActions = true } = props

  const { checkedEmployees, handleEmployeeCheck, isAllSelected, excludeEmployees } = useEmployees()

  const {
    id,
    last_name,
    middle_name,
    first_name,
    email,
    position,
    department,
    statistic,
    is_registered,
    tags,
  } = info || {}
  const { title } = department || {}
  const { risk_level, phishing, progress } = statistic || {}

  const fullname = getFullName({ last_name, middle_name, first_name })

  const handleCheck = (checked: boolean) => {
    handleEmployeeCheck(id, checked)
  }

  const isChecked =
    checkedEmployees.indexOf(id) !== -1 || (isAllSelected && excludeEmployees.indexOf(id) === -1)

  const handleClickCard = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    if (is_registered) onEmployeeClick(id)
  }

  const handleEdit = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, info: IUser) => {
    onEditEmployee(info)
    e.stopPropagation()
  }

  const handleClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    onEmployeeClick(id)
    e.stopPropagation()
  }

  return (
    <div
      className={cx('wrapper', className, { notRegistred: !is_registered && !isActive })}
      onClick={e => handleClickCard(e, id)}
    >
      <Checkbox onChange={handleCheck} customChecked={isChecked} />
      <div className={cx('info')}>
        {fullname && <div className={cx('name')}>{fullname}</div>}
        <div className={cx('email')}>{email}</div>
        <div className={cx('icons')}>
          <IconWithData
            icon='hatGraduation'
            color={progress ? getRickLevelColor((100 - progress) / 10) : null}
            value={progress}
            withPercent
          />
          <IconWithData
            icon='riskLevel'
            color={risk_level ? getRickLevelColor(risk_level) : null}
            value={risk_level}
          />
          <IconWithData
            icon='fish'
            color={phishing ? getRickLevelColor(phishing / 10) : null}
            value={phishing}
            withPercent
          />
        </div>
      </div>
      <div className={cx('info', 'nameWrapper')}>
        {title && (
          <div className={cx('department')} title={title}>
            {title}
          </div>
        )}
        {position && (
          <div className={cx('position')} title={position}>
            {position}
          </div>
        )}

        {!!tags && !!tags.length ? (
          <div className={cx('tags')}>
            {tags.map(t => {
              return (
                <div key={`${id}-${t.id}`} className={cx('tag')} style={{ background: t.color }}>
                  {t.title}
                </div>
              )
            })}
          </div>
        ) : null}
      </div>
      {withActions && (
        <div className={cx('employeeIcons')}>
          <ButtonIcon
            icon='editBold'
            onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => handleEdit(e, info)}
          />
          <ButtonIcon
            icon='chevroneMedium'
            onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => handleClick(e, id)}
            disabled={!is_registered}
          />
        </div>
      )}
    </div>
  )
}
