import { FC, memo, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { IListItem, Loader } from '@/shared/ui'
import { TagsSelect } from '../tags-select'
import { useGetOrganizationTagsQuery } from '@/store/services/tags-employees-service'
import { isTagCustom } from '@/shared/types/enums/tag'
import { ITag, ITagWithNew } from '@/shared/types/store/tag'
import { useTranslation } from 'react-i18next'
import InfiniteScroll from 'react-infinite-scroll-component'

type Props = {
  label?: string
  initialSelectedTags?: ITagWithNew[]
  handleChangeTags: (tags: ITagWithNew[]) => void
  className?: string
  inputBaseClassName?: string
  isListTop?: boolean
}

const TAGS_LIMIT = 20

export const CustomTagsSelect: FC<Props> = memo(
  ({
    label,
    initialSelectedTags,
    handleChangeTags,
    className,
    inputBaseClassName,
    isListTop = false,
  }) => {
    const { t } = useTranslation()
    const [selectedTags, setSelectedTags] = useState<ITagWithNew[]>(() => initialSelectedTags || [])
    const initialCustomTagIds = useRef<UUID[] | null>(
      initialSelectedTags ? initialSelectedTags.map(tag => tag.id) : null,
    )

    const [tagsPage, setTagsPage] = useState(1)
    const [tagsList, setTagsList] = useState<ITag[]>([])
    const [tagsTotalCount, setTagsTotalCount] = useState(0)
    const [systemTags, setSystemTags] = useState<ITag[] | null>(null)

    const { data: tags } = useGetOrganizationTagsQuery({
      limit: TAGS_LIMIT,
      offset: (tagsPage - 1) * TAGS_LIMIT,
    })

    useEffect(() => {
      handleChangeTags(selectedTags)
    }, [selectedTags])

    useEffect(() => {
      if (!systemTags && tags) {
        setSystemTags(tags.data.filter(tag => !isTagCustom(tag)))
      }

      if (tags) {
        const filteredTags = tags.data.filter(tag => isTagCustom(tag))
        setTagsList(prev => [...prev, ...filteredTags])
        setTagsTotalCount(tags.total_count)
      }
    }, [tags])

    const handleChangeTag = useCallback(
      (selectedTag: IListItem) => {
        const { id: selectedTagId } = selectedTag

        const isNeedRemovedFromSelectedTags =
          selectedTags.findIndex(selectedTag => selectedTag.id === selectedTagId) > -1

        if (isNeedRemovedFromSelectedTags) {
          return setSelectedTags(oldSelectedTags => [
            ...oldSelectedTags.filter(selectedTag => selectedTag.id !== selectedTagId),
          ])
        }

        const selectedTagIndex = tagsList.findIndex(tag => tag.id === selectedTagId)

        setSelectedTags(oldSelectedTags => [
          ...oldSelectedTags,
          {
            ...tagsList[selectedTagIndex],
            isNew: Boolean(!initialCustomTagIds.current?.includes(selectedTagId)),
          },
        ])
      },
      [selectedTags, tagsList],
    )

    let tagsHasMore = tagsPage * TAGS_LIMIT < tagsTotalCount

    const selectedIds = useMemo(() => new Set<UUID>(selectedTags.map(t => t.id)), [selectedTags])

    const displayedTagsList = useMemo<ITag[]>(
      () => [...selectedTags, ...tagsList.filter(tag => !selectedIds.has(tag.id))],
      [tagsList, selectedTags, selectedIds],
    )

    let lastTagsLength = 0
    const rawLength = tagsList.length + selectedIds.size
    if (rawLength > lastTagsLength) {
      lastTagsLength = rawLength
    }

    if (displayedTagsList.length === tagsTotalCount - (systemTags?.length || 0)) {
      tagsHasMore = false
    }

    const lengthForInfinite = lastTagsLength

    const renderListInnerWrapper = useCallback(
      (children: React.ReactNode) => (
        <InfiniteScroll
          scrollThreshold={0.4}
          dataLength={lengthForInfinite}
          next={() => setTagsPage(prev => prev + 1)}
          hasMore={tagsHasMore}
          loader={<Loader size='28' loading />}
          scrollableTarget='custom-tags-list'
        >
          {children}
        </InfiniteScroll>
      ),
      [tagsHasMore, lengthForInfinite],
    )

    return (
      <TagsSelect
        label={label ? label : t('commons:assign_tag')}
        className={className}
        inputBaseClassName={inputBaseClassName}
        handleChange={handleChangeTag}
        tagsList={displayedTagsList}
        selectedTags={selectedTags}
        isListTop={isListTop}
        listWrapperId={'custom-tags-list'}
        renderListInnerWrapper={renderListInnerWrapper}
      />
    )
  },
)

CustomTagsSelect.displayName = 'CustomTagsSelect'
