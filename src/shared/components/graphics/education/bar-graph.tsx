import { FC, ReactNode } from 'react'
import styles from './bar-graph.module.scss'
import classNamesBind from 'classnames/bind'
import { formatNumber, isNumber } from '@/shared/helpers'

const cx = classNamesBind.bind(styles)

type BarItem = {
  text?: ReactNode
  count?: number
  className?: string
}

type ExtendedBar = BarItem & {
  onClick?: (v: BarItem) => void
}

type Props = {
  bars: ExtendedBar[]
}

export const BarGraph: FC<Props> = ({ bars }) => {
  if (!bars.length) return null

  return (
    <ul className={cx('rows')}>
      {bars.map((bar, index) => (
        <li
          onClick={() => bar.onClick?.(bar)}
          key={index}
          className={cx('rows__element', bar.className)}
        >
          {isNumber(bar.count) && (
            <span className={cx('rows__element__count')}>{formatNumber(bar.count)}</span>
          )}
          {bar.text && <span>{bar.text}</span>}
        </li>
      ))}
    </ul>
  )
}
