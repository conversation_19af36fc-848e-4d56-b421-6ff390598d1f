// @use "mixins/icons";

.wrapper {
  align-items: center;

  color: var(--color-gray-80, #5c6585);
  display: flex;
  font: var(--font-caption-1-normal);
  gap: 4px;
  // @include icons.color(var(--color-gray-80, #5c6585));

  &.color {
    &--GREEN {
      color: var(--color-statistics-good, #3dbc87);
      // @include icons.color(var(--color-statistics-good, #3dbc87));
    }
    &--RED {
      color: var(--color-statistics-bad-text, #ff4b60);
      // @include icons.color(var(--color-statistics-bad-text, #ff4b60));
    }
    &--YELLOW {
      color: var(--color-statistics-warning, #ffc700);
      // @include icons.color(var(--color-statistics-warning, #ffc700));
    }
  }
  &.noValue {
    opacity: 0.5;
  }
  span {
    overflow: hidden;
    text-overflow: ellipsis;

    white-space: nowrap;
  }
}
