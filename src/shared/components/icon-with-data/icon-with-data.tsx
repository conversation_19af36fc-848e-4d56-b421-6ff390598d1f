import { FC } from 'react'
import styles from './icon-with-data.module.scss'
import classNamesBind from 'classnames/bind'
import { IconWithDataProps } from './icon-with-data.d'
import EmailCloseSmallIcon from '@/shared/ui/Icon/icons/components/EmailCloseSmallIcon'
import EmailOpenSmallIcon from '@/shared/ui/Icon/icons/components/EmailOpenSmallIcon'
import Web2SmallIcon from '@/shared/ui/Icon/icons/components/Web2SmallIcon'
import Lock3Icon from '@/shared/ui/Icon/icons/components/Lock3Icon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { TIcons } from '@/shared/ui/Icon/icons/icons.d'
import {
  ClipSmallIcon,
  FishIcon,
  HatGraduationIcon,
  PeopleSmallIcon,
  RiskLevelIcon,
} from '@/shared/ui/Icon/icons/components'

const cx = classNamesBind.bind(styles)

const renderIcon = (iconType: TIcons) => {
  switch (iconType) {
    case 'emailCloseSmall':
      return <EmailCloseSmallIcon />
    case 'emailOpenSmall':
      return <EmailOpenSmallIcon />
    case 'web2Small':
      return <Web2SmallIcon />
    case 'lock3':
      return <Lock3Icon />
    case 'hatGraduation':
      return <HatGraduationIcon />
    case 'riskLevel':
      return <RiskLevelIcon />
    case 'fish':
      return <FishIcon />
    case 'peopleSmall':
      return <PeopleSmallIcon />
    case 'clipSmall':
      return <ClipSmallIcon />
    default:
      return null
  }
}

export const IconWithData: FC<IconWithDataProps.Props> = props => {
  const { className, value, icon, color, withPercent = false } = props

  const noValue = value === null || value === undefined
  const valueWithPercent = !noValue ? `${value?.toFixed()}${withPercent ? '%' : ''}` : '—'

  return (
    <div
      className={cx('wrapper', `color--${color}`, className, {
        noValue,
      })}
      title={valueWithPercent}
    >
      <IconWrapper>{renderIcon(icon)}</IconWrapper>
      <span>{valueWithPercent}</span>
    </div>
  )
}
