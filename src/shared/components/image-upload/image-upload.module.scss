.imagePreview {
  position: relative;
  cursor: pointer;

  &__wrapper {
    display: flex;
    justify-content: space-between;
  }

  &__item {
    &:hover {
      color: var(--color-primary);
      p,
      span {
        color: var(--color-primary);
      }
      svg path {
        fill: var(--color-primary);
      }
    }
  }
}

.previewWrapper {
  width: 100%;
  height: 128px;
  border-radius: 12px;

  img {
    max-width: 206px;
    height: 128px;
    border-radius: 12px;
    object-fit: cover;
    opacity: 0.6;
  }
}

.imagePreviewText {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
  margin-bottom: 14px;
}

.imagePreviewStub {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 128px;
  border-radius: 12px;
  background: var(--light-grey);
}

.fileInput {
  width: 0.1px;
  height: 0.1px;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  bottom: 0;
  z-index: -1;
}
