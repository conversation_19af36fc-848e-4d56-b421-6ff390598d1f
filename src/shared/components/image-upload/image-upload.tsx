import { FC, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'

import classNamesBind from 'classnames/bind'
import styles from './image-upload.module.scss'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

const cx = classNamesBind.bind(styles)

const EducationIcon = () => {
  return (
    <svg width='90' height='75' viewBox='0 0 90 75' fill='none' xmlns='http://www.w3.org/2000/svg'>
      <path
        d='M15 61.3926V45.6261L32.4007 56.7322C40.0556 61.6183 49.9444 61.6183 57.5991 56.7322L75 45.6261V61.3926C75 62.0722 74.7745 62.7333 74.3571 63.2767L74.3524 63.2826L74.3473 63.2897L74.334 63.3065L74.2988 63.3513L74.247 63.4153L74.1904 63.4844C74.1017 63.5916 73.9795 63.7352 73.8227 63.9102C73.5094 64.2598 73.0581 64.7354 72.4633 65.2935C71.2735 66.4089 69.504 67.8605 67.1087 69.3004C62.2967 72.1935 55.023 75 45 75C34.9769 75 27.7032 72.1935 22.8913 69.3004C20.496 67.8605 18.7265 66.4089 17.5368 65.2935C16.9418 64.7354 16.4905 64.2598 16.1772 63.9102C16.0205 63.7352 15.8981 63.5916 15.8094 63.4844C15.765 63.4304 15.729 63.386 15.7013 63.3513L15.6659 63.3065L15.6528 63.2897L15.6474 63.2826C15.2302 62.7391 15 62.0722 15 61.3926ZM88.5437 29.4798L54.0831 51.4747C48.5644 54.9972 41.4356 54.9972 35.9168 51.4747L6.42857 32.6534V55.1123C6.42857 56.8465 4.98947 58.2525 3.21428 58.2525C1.4391 58.2525 0 56.8465 0 55.1123V28.9443C0 28.5768 0.0646287 28.2241 0.183343 27.8964C0.0620573 27.5609 -0.00158566 27.2033 4.29153e-05 26.8373C0.0047572 25.7722 0.561771 24.7821 1.47964 24.2073L36.0376 2.56542C41.4994 -0.85514 48.5006 -0.85514 53.9623 2.56542L88.5205 24.2073C89.4381 24.7821 89.9953 25.7722 90 26.8373C90.0043 27.7693 89.5847 28.6474 88.8685 29.2428C88.7661 29.3278 88.6577 29.4069 88.5437 29.4798Z'
        fill='white'
      />
    </svg>
  )
}

type ImageUploadProps = {
  defaultPreview?: string
  defaultFile?: File
  onSelect?: (file?: File) => void
  onSelectPreview?: (preview?: string) => void
  hasDelete?: boolean
  isLoading?: boolean
}

const getPreviewForImageFromFile = (file: File) => URL.createObjectURL(file)

export const ImageUpload: FC<ImageUploadProps> = ({
  defaultFile,
  defaultPreview,
  onSelect,
  onSelectPreview,
  hasDelete = true,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | undefined>(defaultFile)
  const [preview, setPreview] = useState<string | undefined>(defaultPreview)
  const { t } = useTranslation()

  useEffect(() => {
    setPreview(defaultPreview)
  }, [defaultPreview])

  useEffect(() => {
    if (!selectedFile) return

    const objectUrl = getPreviewForImageFromFile(selectedFile)
    setPreview(objectUrl)

    return () => URL.revokeObjectURL(objectUrl)
  }, [selectedFile])

  const onSelectFile = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return

    const file = e.target.files[0]
    setSelectedFile(e.target.files[0])

    const objectUrl = getPreviewForImageFromFile(file)
    setPreview(objectUrl)

    onSelect?.(file)

    onSelectPreview?.(objectUrl)

    e.target.value = ''
  }

  const isEditMode = selectedFile || preview

  return (
    <label className={cx('imagePreview')}>
      <input
        type='file'
        accept='image/*,.png,.jpg,.jpeg'
        multiple={false}
        onChange={onSelectFile}
        className={cx('fileInput')}
      />
      <div className={cx('imagePreview__wrapper')}>
        <div className={cx('imagePreviewText', 'imagePreview__item')}>
          <IconWrapper size='20' color='self'>
            <ClipBoldIcon />
          </IconWrapper>
          <span>{isEditMode ? t('commons:change_image') : t('commons:add_image')}</span>
        </div>
        {isEditMode && hasDelete && (
          <IconWrapper
            className={cx('imagePreview__item')}
            onClick={e => {
              setSelectedFile(undefined)
              if (preview) URL.revokeObjectURL(preview)
              setPreview(undefined)
              onSelect?.(undefined)
              onSelectPreview?.(undefined)
              e.preventDefault()
              e.stopPropagation()
            }}
            size='20'
            color='self'
          >
            <CloseBoldIcon />
          </IconWrapper>
        )}
      </div>
      {preview || selectedFile ? (
        <div className={cx('previewWrapper')}>
          <img width='206px' height='128px' src={preview} />
        </div>
      ) : (
        <div className={cx('imagePreviewStub')}>
          <EducationIcon />
        </div>
      )}
    </label>
  )
}
