import { FC, useEffect, useRef } from 'react'
import styles from './phishing-campaign-progress.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingCampaignProgressProps } from './phishing-campaign-progress.d'
import SettingsBoldIcon from '@/shared/ui/Icon/icons/components/SettingsBoldIcon'
import LightningIcon from '@/shared/ui/Icon/icons/components/LightningIcon'
import EmailBoldIcon from '@/shared/ui/Icon/icons/components/EmailBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { v4 as uuid } from 'uuid'

interface Point {
  x: number
  y: number
}

const cx = classNamesBind.bind(styles)

// Функция для рендеринга иконок кампании
const renderCampaignIcon = (isTesting: boolean, isAutoPhishing: boolean, className?: string) => {
  return (
    <IconWrapper size='28' color='white' className={className}>
      {isTesting ? <SettingsBoldIcon /> : isAutoPhishing ? <LightningIcon /> : <EmailBoldIcon />}
    </IconWrapper>
  )
}

export const PhishingCampaignProgress: FC<PhishingCampaignProgressProps.Props> = props => {
  const { className, progress, isAutoPhishing = false, isTesting = false } = props

  const ref = useRef<null | SVGPathElement>(null)
  const wrapper = useRef<null | SVGSVGElement>(null)
  const id = uuid()

  let width = 0
  let height = 0

  let _progress = 0

  const totalTime = 0

  let waveWidth = 0 // Wave SVG width (usually container width)
  let waveHeight = 0 // Position from the top of container
  const waveDelta = 3 // Wave amplitude
  const speed = 0.3 // Wave animation speed
  const wavePoints = 2 // How many point will be used to compute our wave

  useEffect(() => {
    _progress = progress
    waveHeight = height * (1 - _progress)
  }, [])

  useEffect(() => {
    if (!ref.current || !wrapper.current) return

    width = wrapper.current.getBoundingClientRect().width
    height = wrapper.current.getBoundingClientRect().height

    waveWidth = width
    waveHeight = height * (1 - _progress)

    draw()
  }, [ref, wrapper])

  const draw = () => {
    const factor = totalTime * Math.PI
    const points = calculateWavePoints(factor)

    const dValue = buildPathFromPoints(points)
    ref.current?.setAttribute('d', dValue)
  }

  const buildPathFromPoints = (points: Point[]) => {
    let SVGString = `M ${points[0].x} ${points[0].y}`

    const cp0 = {
      x: (points[1].x - points[0].x) / 2,
      y: points[1].y - points[0].y + points[0].y + (points[1].y - points[0].y),
    }

    SVGString += ' ' + ['C', cp0.x, cp0.y, cp0.x, cp0.y, points[1].x, points[1].y].join(' ')

    let prevCp = cp0
    let inverted = -1

    for (let i = 1; i < points.length - 1; i++) {
      const cp1 = {
        x: points[i].x - prevCp.x + points[i].x,
        y: points[i].y - prevCp.y + points[i].y,
      }

      SVGString +=
        ' ' + ['C', cp1.x, cp1.y, cp1.x, cp1.y, points[i + 1].x, points[i + 1].y].join(' ')

      prevCp = cp1
      inverted = -inverted
    }

    SVGString += ` L ${width} ${height}`
    SVGString += ` L 0 ${height} Z`
    return SVGString
  }

  const calculateWavePoints = (factor: number): Point[] => {
    const points: Point[] = []

    for (let i = 0; i <= wavePoints; i++) {
      const x = (i / wavePoints) * waveWidth
      const sinSeed = (factor + (i + (i % wavePoints))) * speed * 100
      const sinHeight = Math.sin(sinSeed / 100) * waveDelta
      const yPos = Math.sin(sinSeed / 100) * sinHeight + waveHeight
      points.push({ x: x, y: yPos })
    }

    return points
  }

  return (
    <div className={cx('wrapper', className)}>
      <svg
        width='100%'
        height='100%'
        version='1.1'
        xmlns='http://www.w3.org/2000/svg'
        ref={wrapper}
      >
        <defs>
          <linearGradient
            id={`gradient${id}`}
            x1='2.5'
            y1='33.5'
            x2='40'
            y2='10.5'
            gradientUnits='userSpaceOnUse'
          >
            <stop className={cx('gradient-from')} />
            <stop className={cx('gradient-to')} offset='1' />
          </linearGradient>
        </defs>

        <path d='' fill={`url(#gradient${id})`} ref={ref} />
      </svg>

      {renderCampaignIcon(isTesting, isAutoPhishing, cx('icon'))}
      {}
    </div>
  )
}
