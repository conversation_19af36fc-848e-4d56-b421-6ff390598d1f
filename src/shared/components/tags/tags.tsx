import { FC } from 'react'
import styles from './tag.module.scss'
import classNamesBind from 'classnames/bind'

const cx = classNamesBind.bind(styles)

type TagsProps = {
  tags: {
    title: string
    color: string
  }[]
}

export const Tags: FC<TagsProps> = ({ tags }) => {
  return (
    <div className={cx('tags')}>
      {tags?.map(tag => (
        <span key={tag.title} className={cx('tag')} style={{ background: tag.color }}>
          {tag.title}
        </span>
      ))}
    </div>
  )
}
