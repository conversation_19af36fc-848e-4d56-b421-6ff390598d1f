import { ChangeEvent, FC, PropsWithChildren, useRef } from "react";
import { FileUploadProps } from "./file-upload.d";

export const FileUpload: FC<PropsWithChildren<FileUploadProps.Props>> = (
  props
) => {
  const {
    accept = "*",
    onFileSelect,
    children,
    multiple = false,
    disabled,
  } = props;

  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const selectedFile = multiple ? files : files[0];
      onFileSelect(selectedFile);
    } else {
      onFileSelect(null);
    }
    // ! Очищаем значение input, чтобы можно было выбрать один и тот же файл снова.
    // ! т.к событие onChange не срабатывает, если выбран тот же файл
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div
      onClick={() => {
        if (disabled) return;

        if (fileInputRef.current) {
          fileInputRef.current.click();
        }
      }}
    >
      <input
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileSelect}
        ref={fileInputRef}
        style={{ display: "none" }}
      />
      {children}
    </div>
  );
};
