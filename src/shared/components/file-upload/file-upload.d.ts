export declare namespace FileUploadProps {
  type FileChoiseVariants = File | FileList | null;

  interface FileUpload {
    accept: string; // Типы файлов, которые можно выбирать (например, '.pdf, .doc')
    onFileSelect: (file: FileChoiseVariants) => void; // Callback, вызываемый при выборе файла
    multiple?: boolean; // Разрешить выбор нескольких файлов
    disabled?: boolean;
  }

  interface Own extends FileUpload {}

  type Props = Own;
}

export {};
