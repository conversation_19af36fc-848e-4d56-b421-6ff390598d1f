import { I18n<PERSON>rovider } from 'react-aria'
import { DateFieldStateOptions } from 'react-stately'
import { useLocale } from '@/shared/hooks/use-locale'
import styles from './date-field.module.scss'
import classNamesBind from 'classnames/bind'
import { DateInput, DatePicker, DateSegment, Group, Label } from 'react-aria-components'
import { useTranslation } from 'react-i18next'
const cx = classNamesBind.bind(styles)

type DateFieldProps = Partial<DateFieldStateOptions>

export function DateField(props: DateFieldProps) {
  const locale = useLocale()
  const { t } = useTranslation()

  return (
    <I18nProvider locale={locale}>
      <DatePicker {...props} className={cx('wrapper')}>
        <Label className={cx('label')}>{t('commons:date')}</Label>
        <Group className={cx('field')}>
          <DateInput className={cx('input')}>
            {segment => <DateSegment segment={segment} />}
          </DateInput>
        </Group>
      </DatePicker>
    </I18nProvider>
  )
}
