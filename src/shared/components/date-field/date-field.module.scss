.wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font: var(--font-text-2-medium);
}

.label {
  color: var(--color-gray-70);
}

.field {
  padding: 12px 24px;
  border: 2px solid var(--color-gray-60);
  border-radius: 8px;
  color: var(--color-gray-90);

  &:active,&:hover {
    border-color: var(--color-primary);
  }

  transition: 0.3s all;
}

.input {
  & *[data-focused="true"] {
    background: var(--color-primary);
    color: var(--white);
  }
}
