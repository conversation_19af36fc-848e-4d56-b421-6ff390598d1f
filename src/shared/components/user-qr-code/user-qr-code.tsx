/* eslint-disable @typescript-eslint/no-explicit-any */
import QRCode, { QRCodeProps } from 'react-qr-code'
import { DetailedHTMLProps, HTMLAttributes } from 'react'
import { Loader } from '@/shared/ui'
import { useTranslation } from 'react-i18next'
import { authAPI } from 'entities/auth'

type UserQRCodeProps = DetailedHTMLProps<HTMLAttributes<HTMLDivElement>, HTMLDivElement> & {
  qrCodeProps?: React.Component<QRCodeProps, any>
  value?: string
}

export function UserQRCode({ qrCodeProps, value, ...props }: UserQRCodeProps) {
  const {
    data: qrCodeData,
    isLoading,
    isError,
  } = authAPI.useTwofaQRCodeQuery(undefined, { skip: !!value })
  const { t } = useTranslation()

  if (isLoading) return <Loader size='56' />
  if (isError)
    return (
      <div {...props}>
        <Loader error={true} loading={false} success={false} size='56' />
        <p className={'error-text'}>{t('commons:error_get_qr_code')}</p>
      </div>
    )

  return (
    <div {...props}>
      <QRCode
        value={value || qrCodeData?.value || ''}
        viewBox={`0 0 256 256`}
        size={256}
        {...qrCodeProps}
      />
    </div>
  )
}

