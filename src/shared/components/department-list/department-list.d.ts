import { IDepartmentStat } from '@/entities/department/model/types'
import { UUID } from 'react-app/new/types/IUser'

export declare namespace DepartmentListProps {
  interface Own {
    className?: string
  }

  type Props = Own
}

export declare namespace DepartmentListItemProps {
  interface Own {
    className?: string
    item: IDepartmentStat
    isChecked?: boolean
    onClick: (obj: { id: UUID; name: string }) => void
    onCheck: (id: UUID) => void
    onEdit?: () => void
    isEditable?: boolean
  }

  type Props = Own
}

export {}
