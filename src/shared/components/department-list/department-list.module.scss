// @use "mixins/icons";

.header {
  align-items: center;
  min-height: 48.8px;
  background: var(--color-surface, #fff);
  border-radius: 16px 16px 0 0;
  border: 1px solid var(--stroke, #ebeff2);

  display: flex;
  justify-content: space-between;
  padding: 10px 24px 10px 16px;

  .selectAll {
    align-items: center;

    color: var(--color-gray-70, #8e97af);

    display: flex;
    font: var(--font-caption-1-medium);
    gap: 12px;
    letter-spacing: 0.13px;
    text-transform: uppercase;

    span {
      cursor: pointer;
    }
  }
}

.modal-wrapper {
  height: auto;
  max-height: calc(100dvh - 64px);
  min-height: auto;

  width: 100%;
  max-width: 624px;
  width: 100%;

  overflow-y: auto;

  &.grid {
    display: grid;
    grid-template-rows: auto 1fr;
  }

  .wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .button {
    margin-left: auto;
  }
}

.breadcrumb {
  display: flex;
  gap: 6px;

  margin-bottom: 20px;
}

.select {
  padding: 0 24px;
  padding-right: 12px;
  margin-right: -8px;

  background: none;
  border: none;
  display: flex;
  justify-content: start;

  &-list {
    z-index: 100000;
    background: var(--white);
  }

  &-text {
    margin-left: auto;
  }
}

.sort {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.inner {
  background: var(--stroke, #ebeff2);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 16px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  overflow: hidden;
}

.item {
  align-items: center;

  background: var(--color-surface, #fff);

  border-bottom: 1px solid var(--stroke, #ebeff2);

  cursor: pointer;
  display: grid;
  grid-gap: 16px;
  grid-template-columns: auto 1fr auto;
  padding: 12px 16px;

  &:last-child {
    border-bottom: none;
  }
  &:hover,
  &.active {
    background: var(--color-gray-20, #f9fafc);

    transition: var(--transition);
  }

  &.disabled {
    opacity: 0.3;

    transition: var(--transition);
  }

  .actions {
    flex-direction: row;
    gap: 8px;
    align-items: center;
    display: none;
    margin-left: auto;
  }

  &:hover {
    .actions {
      display: flex;
    }
  }

  .info {
    .title {
      box-orient: vertical;

      color: var(--color-gray-90, #343b54);
      display: box;
      font: var(--font-text-2-medium);
      -webkit-line-clamp: 2;
      margin-bottom: 4px;
      max-height: 40px;

      overflow: hidden;
    }
  }

  .icons {
    display: grid;
    grid-gap: 12px;
    grid-template-columns: 64px 57px 38px 57px;
    .icon {
      align-items: center;

      color: var(--color-gray-80, #5c6585);
      display: flex;
      font: var(--font-caption-1-normal);
      gap: 4px;

      &.color {
        &--GREEN {
          color: var(--color-statistics-good, #3dbc87);
          // @include icons.color(var(--color-statistics-good, #3dbc87));
        }
        &--RED {
          color: var(--color-statistics-bad-text, #ff4b60);
          // @include icons.color(var(--color-statistics-bad-text, #ff4b60));
        }
        &--YELLOW {
          color: var(--color-statistics-warning, #ffc700);
          // @include icons.color(var(--color-statistics-warning, #ffc700));
        }
      }
    }
  }
}

.emptyText {
  font: var(--font-caption-1-normal);
}
