/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { FC, useState } from 'react'
import styles from './department-list.module.scss'
import classNamesBind from 'classnames/bind'
import { DepartmentListProps, DepartmentListItemProps } from './department-list.d'
import { Breadcrumbs, ButtonIcon, Loader, Pagination, Select } from '@/shared/ui'
import { getRickLevelColor } from '@/shared/helpers'
import { DepartmentSortItem, useDepartments } from '@/entities/department'
import { IDepartmentStat } from '@/entities/department/model/types'
import { DepartmentsPanel, IconWithData } from '@/shared/components'
import { Checkbox } from '@/shared/ui/checkbox'
import { EditDepartmentModal } from './edit-department-modal'
import { useTranslation } from 'react-i18next'
import { IconSortDirection } from '../icon-sort-direction'

const cx = classNamesBind.bind(styles)

export const DepartmentListItem: FC<DepartmentListItemProps.Props> = props => {
  const { className, item, isChecked, onEdit, onClick, onCheck, isEditable } = props

  const { id, title, statistic, users_count } = item

  const handleClick = (e: React.MouseEvent<HTMLButtonElement | HTMLDivElement, MouseEvent>) => {
    onClick({ id, name: title })
    e.stopPropagation()
  }
  const handleCheck = () => onCheck(id)

  return (
    <div className={cx('item', className, { active: isChecked })} onClick={e => handleClick(e)}>
      <Checkbox onChange={handleCheck} customChecked={isChecked} />
      <div className={cx('info')}>
        <div className={cx('title')} title={title}>
          {title}
        </div>
        <div className={cx('icons')}>
          <IconWithData icon='peopleSmall' value={users_count || 0} />
          <IconWithData icon='hatGraduation' value={statistic.progress} withPercent />
          <IconWithData
            icon='riskLevel'
            value={statistic.risk_level}
            color={statistic.risk_level ? getRickLevelColor(statistic.risk_level) : null}
          />
          <IconWithData
            icon='fish'
            value={statistic.phishing}
            color={statistic.phishing ? getRickLevelColor(statistic.phishing / 10) : null}
            withPercent
          />
        </div>
      </div>
      <div className={cx('actions')}>
        {isEditable && (
          <ButtonIcon
            icon='editBold'
            className={cx('buttonIcon')}
            onClick={e => {
              e.stopPropagation()
              onEdit?.()
            }}
          />
        )}

        <ButtonIcon
          icon='chevroneMedium'
          className={cx('buttonIcon')}
          onClick={e => handleClick(e)}
        />
      </div>
    </div>
  )
}

export const DepartmentList: FC<DepartmentListProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const {
    departmentsStat,
    isLoadingDepartmentsStat: isLoading,
    isFetchingDepartmentsStat: isFetching,
    errorDepartmentsStat: error,
    checkedDeparments,
    handleDeparmentCheck,
    breadcrumbs,
    setActiveDepartment,
    addBreadcrumb,
    deleteBreadcrumbs,
    limit,
    page,
    setPage,
    sortItems,
    sortType,
    setSortType,
    sortDirection,
    setSortDirection,
  } = useDepartments({ isSkipDepartmentsReq: true })

  const [editModalOpen, setEditModalOpen] = useState(false)
  const [editItem, setEditItem] = useState<IDepartmentStat>()

  const handleClickBreadcrumb = (id: string, title?: string) => deleteBreadcrumbs(id, title ?? '')
  const handleClick = ({ id, name }: { id: UUID; name: string }) => {
    addBreadcrumb({
      id,
      text: name,
    })
    setPage(0)
    setActiveDepartment({
      title: name,
      id,
    })
  }
  const handleCheck = (id: UUID) => handleDeparmentCheck(id)
  const handleChangePage = (page: number) => setPage(page)

  return (
    <>
      <div className={cx('wrapper', className)}>
        <Breadcrumbs
          items={breadcrumbs}
          onClick={handleClickBreadcrumb}
          className={cx('breadcrumb')}
        />
        {departmentsStat && departmentsStat?.data?.length > 0 && (
          <div className={cx('header')}>
            <Loader loading={isFetching} />
            <div className={cx('sort')}>
              <Select
                listClassName={cx('select-list')}
                wrapperClassName={cx('select')}
                textClassName={cx('select-text')}
                list={sortItems}
                handleChange={v => {
                  setSortType(v as DepartmentSortItem)
                }}
                value={sortType?.id}
                customValue={sortType?.id}
                placeholder={t('commons:sorting_departments')}
              />
              <IconSortDirection
                direction={sortDirection}
                onChange={(dir, e) => {
                  setSortDirection(dir)
                  e?.stopPropagation()
                }}
              />
            </div>
          </div>
        )}

        {isLoading && <Loader size='56' loading className='loader_centered' />}
        {!isLoading && error && <div className='error-text'>{error as any}</div>}
        {!isLoading && !error && !!departmentsStat && !!departmentsStat.data?.length && (
          <>
            <div className={cx('inner')}>
              {departmentsStat.data.map(item => {
                const isChecked = checkedDeparments.indexOf(item.id) !== -1
                const isEditable =
                  item?.title.trim().toLocaleLowerCase() !==
                  t('commons:without_department', { lng: 'ru' })
                return (
                  <DepartmentListItem
                    key={`department-stat-${item.id}`}
                    item={item}
                    isChecked={isChecked}
                    onClick={handleClick}
                    onCheck={handleCheck}
                    onEdit={() => {
                      setEditItem(item)
                      if (isEditable) setEditModalOpen(true)
                    }}
                    isEditable={isEditable}
                  />
                )
              })}
            </div>

            <Pagination
              limit={limit}
              currentPage={page}
              total={departmentsStat.total_count}
              onChange={handleChangePage}
              withEmptySpace
            />
          </>
        )}
        {editModalOpen && (
          <EditDepartmentModal open={editModalOpen} setOpen={setEditModalOpen} item={editItem} />
        )}

        {!isLoading && !error && !!departmentsStat && !departmentsStat.data?.length && (
          <div className={cx('emptyText')}>{t('commons:no_child_departments')} :(</div>
        )}
      </div>

      <DepartmentsPanel />
    </>
  )
}
