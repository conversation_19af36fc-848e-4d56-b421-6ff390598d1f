import { Dispatch, SetStateAction } from 'react'
import { Modal } from '../modal'
import { Loader } from '@/shared/ui/loader'
import classNamesBind from 'classnames/bind'
import styles from './department-list.module.scss'

import { Button, Input } from '@/shared/ui'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { IDepartmentStat, departmentAPI } from 'entities/department'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  setOpen: Dispatch<SetStateAction<boolean>>
  className?: string
  item?: IDepartmentStat
}

type Inputs = {
  id: string
  title: string
}

export const EditDepartmentModal = ({ setOpen, className, item, open }: Props) => {
  const {
    handleSubmit,
    formState: { errors, isValid },
    control,
    watch,
  } = useForm<Inputs>({
    mode: 'all',
    defaultValues: {
      id: item?.id,
      title: item?.title,
    },
  })

  const { t } = useTranslation()
  const [edit] = departmentAPI.useEditDepartmentMutation()

  const onSubmit: SubmitHandler<Inputs> = async data => {
    if (!data) return

    await edit(data)

    setOpen(false)
  }

  return (
    <Modal setActive={setOpen} active={open} className={cx('modal-wrapper', className)}>
      {!item && <Loader className={cx('loader')} size='56' />}
      {item && (
        <form onSubmit={handleSubmit(onSubmit)} className={cx('wrapper')}>
          <div className={cx('title')}>{t('commons:edit_department')}</div>
          <div className={cx('grid')}>
            <Controller
              control={control}
              name='title'
              render={({ field: { onChange, value } }) => (
                <Input
                  placeholder={t('commons:name_department')}
                  label={t('commons:name_department')}
                  className={cx('input', {
                    errorInput: errors.title?.message,
                  })}
                  value={value}
                  onChange={onChange}
                  error={errors.title?.message}
                  fullWidth
                  required
                />
              )}
            />

            {errors?.title && <div className={cx('error-text')}>{String(errors?.title)}</div>}
          </div>
          <Button
            type='submit'
            color='green'
            size='big'
            className={cx('button')}
            disabled={!isValid && watch('title')?.length === 0}
          >
            {t('commons:apply')}
          </Button>
        </form>
      )}
    </Modal>
  )
}
