import React from "react";
import styles from "./logo.module.scss";
import classNamesBind from "classnames/bind";
import { LogoProps } from "./logo.d";
import { Link } from "react-router-dom";
import { useConfig } from "@/shared/hooks";

const cx = classNamesBind.bind(styles);

export const customLogoSize = [
  {
    domain: "refereeing.ru",
    height: "150px",
    width: "",
    sidebarHeight: "100px",
    sidebarWidth: "",
  },
];

export const getCurrentDomainStyles = (url: string, isSidebar = false) => {
  const styles = {
    height: "",
    width: "",
  };

  customLogoSize.forEach((i) => {
    if (url.includes(i.domain)) {
      styles.width = isSidebar ? i.sidebarWidth : i.width;
      styles.height = isSidebar ? i.sidebarHeight : i.height;
    }
  });

  return styles;
};

export const Logo: React.FC<LogoProps.Props> = (props) => {
  const { className, href, to, height, width } = props;

  const config = useConfig();
  const logoUrl = config.logoUrl;

  const customStyles = getCurrentDomainStyles(window.location.href);

  const L = (
    <img
      src={logoUrl}
      className={cx("logo", "")}
      style={{
        width: customStyles.width ? customStyles.width : width,
        height: customStyles.height ? customStyles.height : height,
      }}
    />
  );

  return to ? (
    <Link to={to} className={cx("wrapper", className)}>
      {L}
    </Link>
  ) : href ? (
    <a href={href} className={cx("wrapper", className)}>
      {L}
    </a>
  ) : (
    <div className={cx("wrapper", className)}>{L}</div>
  );
};
