import { TArticleStep, TBlock, TSlideStep, TVideoStep } from '@/entities/themeCourse/model/types'
import { OutputData, OutputBlockData } from '@editorjs/editorjs'
import { v4 as uuid } from 'uuid'

export const prepareSlideData = (data: OutputData) => {
  const prepared = new FormData()

  data.blocks.forEach(block => {
    if (!prepared.get('title') && block.type === 'header' && block.data.text) {
      prepared.append('title', block.data.text)
    }
    if (!prepared.get('background') && block.type === 'image') {
      const isImageFile = block.data?.image instanceof File
      const isImagePath = block.data?.image
      if (isImageFile) prepared.append('background', block.data?.image)
      if (typeof isImagePath === 'undefined' && !isImageFile)
        prepared.append('clear_background', String(true))
    }
    if (block.type === 'paragraph' && block.data.text) {
      const isFirstParagraph = !prepared.get('description')

      isFirstParagraph
        ? prepared.append('description', block.data.text)
        : prepared.set('description', prepared.get('description') + `<br><br>${block.data.text}`)
    }
    if (!prepared.get('audio') && block.type === 'audio') {
      const isAudioFile = block.data?.audio instanceof File
      const isAudioPath = block.data?.audio
      if (isAudioFile) prepared.append('audio', block.data?.audio)
      if (typeof isAudioPath === 'undefined' && !isAudioFile)
        prepared.append('clear_audio', String(true))
    }
  })

  if (!data.blocks.find((b: OutputBlockData) => b.type === 'audio')) {
    prepared.append('clear_audio', String(true))
  }
  if (!data.blocks.find((b: OutputBlockData) => b.type === 'paragraph')) {
    prepared.append('clear_description', String(true))
  }

  return prepared
}

export const prepareVideoData = (data: OutputData) => {
  const prepared = new FormData()
  data.blocks.forEach(block => {
    if (!prepared.get('title') && block.type === 'header' && block.data.text) {
      prepared.append('title', block.data.text)
    }
    if (!prepared.get('description') && block.type === 'paragraph' && block.data.text) {
      prepared.append('description', block.data.text)
    }
    if (
      !prepared.get('video') &&
      block.type === 'video' &&
      block.data.video &&
      block.data.video instanceof File
    ) {
      const isVideoFile = block.data?.video instanceof File
      const isVideoPath = block.data?.video?.path
      prepared.append('video', block.data.video)
      if (isVideoPath && !isVideoFile) prepared.append('clear_video', String(true))
    }
  })

  if (!data.blocks.find((b: OutputBlockData) => b.type === 'paragraph')) {
    prepared.append('clear_description', String(true))
  }

  return prepared
}

export const prepareArticleData = (data: OutputData) => {
  const prepared = data.blocks.map((i, index) => {
    switch (i.type) {
      case 'header':
        return {
          id: uuid(),
          order_id: index + 1,
          type: 'text',
          content: i.data.text,
          options: {
            level: i.data.level,
            type: 'header',
          },
        }
      case 'list':
        return {
          id: uuid(),
          order_id: index + 1,
          type: 'text',
          content: 'list',
          options: {
            items: i.data.items,
            type: 'list',
            style: i.data.style,
          },
        }
      case 'paragraph':
        return {
          id: uuid(),
          order_id: index + 1,
          type: 'text',
          content: i.data.text,
          options: {
            type: 'paragraph',
          },
        }
      case 'image':
        return {
          id: uuid(),
          order_id: index + 1,
          type: 'image',
          content: typeof i.data.image === 'string' ? i.data.image : i.data.image.path,
          options: {
            ...(typeof i.data?.options?.style === 'object' && { style: i.data.options.style }),
          },
        }
      case 'audio':
        return {
          id: uuid(),
          order_id: index + 1,
          type: 'audio',
          content: typeof i.data.audio === 'string' ? i.data.audio : i.data.audio.path,
          options: {},
        }
      case 'video':
        return {
          id: uuid(),
          order_id: index + 1,
          type: 'video',
          content: typeof i.data.video === 'string' ? i.data.video : i.data.video.path,
          options: {},
        }
      case 'gallery':
        return {
          id: uuid(),
          order_id: index + 1,
          type: 'gallery',
          content: i.data.gallery,
          options: {},
        }
      case 'code':
        return {
          id: uuid(),
          order_id: index + 1,
          type: 'code',
          content: i.data.code,
          options: {
            language: i.data.language,
            diffLines: i.data.diffLines,
          },
        }
    }
  })

  return prepared as TBlock[]
}

export const transformPresentationData = (data: TSlideStep) => {
  const { title, background_path, audio_path, description } = data
  const blocks: OutputBlockData[] = [
    {
      type: 'header',
      id: uuid(),
      data: {
        text: title,
      },
    },
    {
      type: 'image',
      id: uuid(),
      data: {
        image: {
          path: background_path,
        },
      },
    },
  ]

  if (audio_path) {
    blocks.push({
      type: 'audio',
      id: uuid(),
      data: {
        audio: {
          path: audio_path,
        },
      },
    })
  }

  if (description) {
    blocks.push({
      type: 'paragraph',
      id: uuid(),
      data: {
        text: description,
      },
    })
  }

  return {
    blocks,
  }
}

export const transformVideoData = (data: TVideoStep) => {
  const { title, path, description } = data
  const blocks: OutputBlockData[] = [
    {
      type: 'header',
      id: uuid(),
      data: {
        text: title,
      },
    },
  ]

  if (description) {
    blocks.push({
      type: 'paragraph',
      id: uuid(),
      data: {
        text: description,
      },
    })
  }

  if (path) {
    blocks.push({
      type: 'video',
      id: uuid(),
      data: {
        video: {
          path: path,
        },
      },
    })
  }

  return {
    blocks,
  }
}

export type TListItem = {
  content: string
  items: string[]
  meta: object
}

const transformListItems = (items: string[] | TListItem[] | undefined) => {
  const newItems = items?.map(i => {
    if (typeof i === 'string') {
      return {
        content: i,
        items: [],
        meta: {},
      }
    }
    return i
  })

  return newItems
}

export const transformArticleData = (data: TArticleStep) => {
  const { blocks } = data
  const newBlocks = blocks.map(i => {
    let data
    switch (i.type) {
      case 'video':
        data = {
          video: {
            path: i.content,
          },
        }
        break
      case 'audio':
        data = {
          audio: {
            path: i.content,
          },
        }
        break
      case 'image':
        data = {
          image: {
            path: i.content,
          },
          options: {
            ...(i?.options?.style && { style: i.options.style }),
          },
        }
        break
      case 'text':
        data = {
          text: i.content,
        }
        if (i.options.type === 'header') {
          data = {
            ...data,
            level: i.options.level,
          }
        } else if (i.options.type === 'list') {
          data = {
            style: i.options.style,
            meta: {},
            items: transformListItems(i.options.items),
          }
        }
        break
      case 'gallery':
        data = {
          gallery: i.content,
        }
        break
      case 'code':
        data = {
          code: i.content,
          language: i.options.language,
          diffLines: i.options.diffLines,
        }
    }
    return {
      type: (i.type === 'text' ? i.options.type : i.type) || 'paragraph',
      id: i.id,
      data,
    }
  })

  return {
    blocks: newBlocks,
  }
}
