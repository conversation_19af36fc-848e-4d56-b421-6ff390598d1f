import { useContext, createContext, useState, useCallback, useMemo } from 'react'

interface ThemeContentControlButtonsContextType {
  controlButtons: Record<string, React.ReactNode>
  addControlButton: (id: string, button: React.ReactNode) => void
  updateControlButton: (id: string, button: React.ReactNode) => void
  removeControlButton: (id: string) => void
}

export const ThemeContentControlButtonsContext =
  createContext<ThemeContentControlButtonsContextType>({
    controlButtons: {},
    addControlButton: () => {},
    updateControlButton: () => {},
    removeControlButton: () => {},
  })

export const useThemeContentControlButtons = () => {
  const context = useContext(ThemeContentControlButtonsContext)

  if (!context) {
    throw new Error(
      'useThemeContentControlButtons must be used within a ThemeContentControlButtonsContext',
    )
  }

  return context
}

export const ThemeContentControlButtonsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [controlButtons, setControlButtons] = useState<Record<string, React.ReactNode>>({})

  const addControlButton = useCallback((id: string, button: React.ReactNode) => {
    setControlButtons(prev => ({
      ...prev,
      [id]: button,
    }))
  }, [])

  const updateControlButton = useCallback((id: string, button: React.ReactNode) => {
    setControlButtons(prev => ({
      ...prev,
      [id]: button,
    }))
  }, [])

  const removeControlButton = useCallback((id: string) => {
    setControlButtons(prev => {
      const newButtons = { ...prev }
      delete newButtons[id]
      return newButtons
    })
  }, [])

  const value = useMemo(
    () => ({
      controlButtons,
      addControlButton,
      updateControlButton,
      removeControlButton,
    }),
    [controlButtons, addControlButton, updateControlButton, removeControlButton],
  )

  return (
    <ThemeContentControlButtonsContext.Provider value={value}>
      {children}
    </ThemeContentControlButtonsContext.Provider>
  )
}
