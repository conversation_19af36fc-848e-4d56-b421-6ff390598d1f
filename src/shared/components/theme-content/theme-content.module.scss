@use '../../../shared/assets/styles/mixins/icons';

.wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.icon {
  cursor: pointer;

  &::after {
    content: '';
    position: absolute;
    top: -6px;
    left: -6px;
    width: calc(100% + 12px);
    height: calc(100% + 12px);
  }
}

.iframeWrapper {
  width: 100%;
  height: 100%;
  max-height: calc(100vh - 64px);

  iframe {
    min-height: 600px;
  }
}

.trash {
  svg {
    transition: var(--transition);
  }

  &:hover {
    @include icons.color(red);
    svg {
      transform: scale(1.1);
    }
  }
}

.controlButtonsContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-end;

  position: absolute;
  top: 12px;
  right: 12px;
  z-index: 20;
}
