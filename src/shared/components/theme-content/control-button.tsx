import { useEffect, memo } from 'react'
import { useThemeContentControlButtons } from './theme-content-context'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { IIconSize, TIconColor } from '@/shared/ui/Icon/icon.d'
import { Tooltip, TooltipProps } from '@/shared/ui'

interface ThemeContentControlButtonProps {
  id: string
  icon: React.ComponentType
  size?: IIconSize
  color?: TIconColor
  className?: string
  disabled?: boolean
  onClick?: () => void
  tooltip?: TooltipProps.Props
}

export const ThemeContentControlButton: React.FC<ThemeContentControlButtonProps> = memo(
  ({ id, icon: IconComponent, size, color, className, disabled, onClick, tooltip }) => {
    const { addControlButton, updateControlButton, removeControlButton } =
      useThemeContentControlButtons()

    const renderButton = () => {
      const iconElement = (
        <IconWrapper
          size={size}
          color={color}
          className={className}
          onClick={disabled ? undefined : onClick}
          disabled={disabled}
        >
          <IconComponent />
        </IconWrapper>
      )
      return tooltip ? <Tooltip {...tooltip}>{iconElement}</Tooltip> : iconElement
    }

    useEffect(() => {
      addControlButton(id, renderButton())
      return () => removeControlButton(id)
    }, [id])

    useEffect(() => {
      updateControlButton(id, renderButton())
    }, [IconComponent, size, color, className, disabled, onClick, tooltip, id])

    return null
  },
)
