import { TTheme } from '@/entities/themeCourse/model/api/types'
import { SetStateAction } from 'react'
import { TMinifiedStepType } from '../editor/steps-toolbar/steps-toolbar'

export declare namespace ThemeContentProps {
  interface Own {
    data?: DataType
    setData: React.Dispatch<SetStateAction<DataType | undefined>>
    type?: TMinifiedStepType
    stepId?: UUID
    themeName?: string
    createTheme: () => void
    themeData?: TTheme
    themeName?: string
    isNewStep?: boolean
    deleteStep: () => void
  }

  type Props = Own
}

export {}
