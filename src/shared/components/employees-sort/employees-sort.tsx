import { FC, useCallback, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'

import { EmployeesSortProps } from './employees-sort.d'
import ChevroneSmallIcon from '@/shared/ui/Icon/icons/components/ChevroneSmallIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useEvent } from '@/shared/hooks'
import { SORT_BY, useEmployees } from '@/entities/employee'

import styles from './employees-sort.module.scss'
import { IconSortDirection } from '../icon-sort-direction'

const cx = classNamesBind.bind(styles)

export const EmployeesSort: FC<EmployeesSortProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const SORT_LABELS: Record<SORT_BY, string> = useMemo(() => {
    return {
      [SORT_BY.NAME]: t('commons:full_name'),
      [SORT_BY.POSITION]: t('commons:position'),
      [SORT_BY.DEPARTMENT]: t('commons:department'),
      [SORT_BY.RISK_LEVEL]: t('commons:risk_level'),
      [SORT_BY.PROGRESS]: t('commons:learning_progress'),
      [SORT_BY.PHISHING_INDICATOR]: t('commons:probability_of_incident'),
    }
  }, [t])

  const {
    sort: activeSort,
    setSort,
    setPage,
    setIsAllSelected,
    sortDirection,
    setSortDirection,
  } = useEmployees()

  const wrapper = useRef<HTMLDivElement>(null)

  const handleChange = (s: SORT_BY) => {
    setSort(s)
    setIsAllSelected(false)
    setPage(0)
    setIsActive(false)
  }

  const [isActive, setIsActive] = useState(false)

  const handleClick = (isActive: boolean) => {
    setIsActive(!isActive)
  }

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) {
      handleClick(true)
    }
  }, [])

  useEvent('click', handleOutsideClick, window)

  return (
    <div className={cx('wrapper', className)} ref={wrapper} onClick={() => handleClick(isActive)}>
      <div className={cx('text')}>
        {/* TEMPTODO - пока по идее нет такого функционала */}
        {/* <Icon
					color='gray80'
					icon='sort'
					className={cx('icon')}
					direction={!isActive ? 'right' : 'left'}
				/> */}
        {SORT_LABELS[activeSort]}
        <IconWrapper color='gray80' className={cx('icon')} direction={!isActive ? 'right' : 'left'}>
          <ChevroneSmallIcon />
        </IconWrapper>
        <IconSortDirection
          startDirection={'asc'}
          direction={sortDirection}
          onChange={(dir, e) => {
            setSortDirection(dir)
            e?.stopPropagation()
          }}
        />
      </div>
      {isActive && (
        <div
          className={cx('listWrapper', className)}
          style={styles}
          onClick={e => e.stopPropagation()}
        >
          <div className={cx('listInner')}>
            {Object.keys(SORT_LABELS).map(s => {
              const sort = s as SORT_BY
              const isActive = sort === activeSort

              return (
                <span
                  key={`list-item-${s}`}
                  className={cx('listItem', { active: isActive })}
                  onClick={() => handleChange(sort)}
                >
                  {SORT_LABELS[sort]}
                </span>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
