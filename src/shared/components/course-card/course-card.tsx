import { FC } from 'react'
import styles from './course-card.module.scss'
import classNamesBind from 'classnames/bind'
import { CourseCardProps } from './course-card.d'
import { DefaultImage } from './default-image'
import { Radiobox } from '@/shared/ui'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const CourseCard: FC<CourseCardProps.Props> = props => {
  const { className, info, onClick, isSelected, multi = true } = props
  const { modules_count: moduleCount, id, title, image, picture } = info
  const { t } = useTranslation()

  const moduleCountTitle =
    moduleCount === 0 ? t('commons:no_modules') : t('commons:modules_count', { count: moduleCount })

  const handleSelect = () => onClick && onClick(id)

  return (
    <div className={cx('wrapper', className)} onClick={() => onClick && onClick(id)}>
      {!multi && (
        <Radiobox onClick={handleSelect} customChecked={isSelected} className={cx('checkbox')} />
      )}
      <div className={cx('imageWrapper')}>
        {image ? (
          <img src={image} alt='' />
        ) : picture ? (
          <img src={picture} alt='' />
        ) : (
          <DefaultImage />
        )}
      </div>

      <div className={cx('textWrapper')}>
        <div className={cx('title')}>{title}</div>
        {!!moduleCount && (
          <div className={cx('info')}>
            <div className={cx('infoItem')}>{moduleCountTitle}</div>
          </div>
        )}
      </div>
    </div>
  )
}
