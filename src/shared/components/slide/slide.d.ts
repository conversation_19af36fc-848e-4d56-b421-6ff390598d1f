/* eslint-disable @typescript-eslint/no-explicit-any */

export declare namespace SlideProps {
  interface Own {
    className?: string;
    slide?: ISlide;
    active?: boolean;
    fullscreen?: boolean;
    onFullscreenClick?(): void;
    onClick?(e: any): void;
    handleHeight: (height: number) => void;
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch;
}

export {};
