@use '../../assets/styles/mixins/media';

.wrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
  max-width: 900px;
  padding: 5px;
  width: 100%;

  &.fullscreen {
    box-sizing: border-box;
    height: 100%;
    justify-content: center;
    max-width: unset;
    width: unset;
  }
}

.imageWrapper {
  align-items: center;
  box-sizing: border-box;
  display: flex;
  height: 100%;
  justify-content: center;
  width: 100%;
  // flex-grow: 1;
}

.first {
  width: 100dvh;
  height: 100vw;
}

.image {
  max-width: 900px;
  min-height: 400px;
  width: 100%;
  height: clamp(300px, calc(100dvh - 250px - 100px), 700px);

  @include media.mobile {
    min-height: unset;
  }

  &.fullscreen {
    height: 100%;
    max-width: unset;
    min-height: unset;
    width: unset;
  }
}

.audio {
  flex-shrink: 0;
  margin-top: 20px;
}

.fullscreenButton {
  bottom: 15px;
  position: absolute;
  right: 15px;
  cursor: pointer;
  background: var(--white);
  border-radius: 50%;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.247);
}
