import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './slide.module.scss'

import { SlideProps } from './slide.d'
import { Image } from '@/shared/ui'
import FullscreenExitIcon from '@/shared/ui/Icon/icons/components/FullscreenExitIcon'
import FullscreenEnterIcon from '@/shared/ui/Icon/icons/components/FullscreenEnterIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

const cx = classNamesBind.bind(styles)

// Функция для рендеринга иконок полноэкранного режима
const renderFullscreenIcon = (fullscreen: boolean, className?: string, onClick?: () => void) => {
  return (
    <IconWrapper className={className} padding='8' size='44' onClick={onClick}>
      {fullscreen ? <FullscreenExitIcon /> : <FullscreenEnterIcon />}
    </IconWrapper>
  )
}

// TODO use https://github.com/joshwnj/react-visibility-sensor
export const Slide = React.forwardRef((props: SlideProps.Props, _) => {
  const { className, active, slide, fullscreen, onFullscreenClick, onClick, handleHeight } = props

  const { background } = slide || {}

  return (
    <div className={cx('wrapper', fullscreen && 'fullscreen', className)}>
      {/* !! TODO: Проверить валидность слайдера */}
      <div className={cx('imageWrapper')} onClick={onClick}>
        <Image
          image={{ src: background || '' }}
          className={cx('image', fullscreen && 'fullscreen')}
          active={active}
          handleHeight={handleHeight}
        >
          {renderFullscreenIcon(!!fullscreen, cx('fullscreenButton'), onFullscreenClick)}
        </Image>
      </div>
    </div>
  )
})
