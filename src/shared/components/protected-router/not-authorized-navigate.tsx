import { URLS } from '@/shared/configs/urls'
import { useRedirectStorage } from '@/shared/hooks/use-redirect-storage'
import { Navigate, useLocation } from 'react-router-dom'

export const NotAuthorizedNavigate = () => {
  const { save } = useRedirectStorage({
    storageKey: 'redirectUrl',
  })
  const { pathname, search } = useLocation()
  save(`${pathname}${search}`)

  return <Navigate to={URLS.LOGIN_PAGE} />
}
