import { IPermissions } from 'entities/employee'

export const flattenObject = (obj: IPermissions, parentKey = '') => {
  let result: Record<string, true> = {}

  if (Array.isArray(obj)) {
    for (const item of obj) {
      const { name, pages } = item
      const currentKey = parentKey ? `${parentKey}.${name}` : name

      result[currentKey] = true

      if (pages && pages.length > 0) {
        const nestedResult = flattenObject(pages, currentKey)
        result = { ...result, ...nestedResult }
      }
    }
  } else if (typeof obj === 'object') {
    for (const [key, value] of Object.entries(obj)) {
      const currentKey = parentKey ? `${parentKey}.${key}` : key

      result[currentKey] = true

      if (value && typeof value === 'object') {
        const nestedResult = flattenObject(value, currentKey)
        result = { ...result, ...nestedResult }
      }
    }
  }

  return result
}

export const checkPermission = (permissions: Record<string, true>, permission: string): boolean => {
  return permissions?.[permission] || false
}
