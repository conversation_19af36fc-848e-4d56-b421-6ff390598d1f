.page {
  display: grid;
  grid-template-columns: 256px auto;

  &__wrapper {
    height: max-content;
    max-width: 1200px;
    min-height: 100%;
    padding: 40px var(--page-horizontal-padding);
    width: 100%;

    @media (max-width: 1024px) {
      padding: 16px;
      margin-bottom: 56px;
    }
  }

  @media (max-width: 1024px) {
    &.reverse {
      display: flex;
      min-height: 100dvh;
      flex-direction: column-reverse;

      &__wrapper {
        padding: 16px;
      }
    }
  }
}

.centered {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100dvh;
  width: 100%;
}

.content {
  display: flex;
  flex: 1 0;

  justify-content: center;
  max-height: 100dvh;
  overflow-y: auto;
  width: 100%;

  &__loader {
    align-items: center;
    display: flex;
    height: 100dvh;
    justify-content: center;
  }
}

.links {
  overflow-y: auto;
}

.bottom {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}
