@use '../../assets/styles/mixins/icons';

.container {
  display: flex;
  width: 100%;
}

.wrapper {
  align-items: center;

  cursor: pointer;
  display: flex;
  background: var(--white);
  
  height: 68px;
  justify-content: center;
  padding: 0 20px;

  transition: 100ms ease;

  width: 100%;

  &:hover {
    background-color: var(--color-gray-40);
  }
  &.active {
    background-color: var(--color-primary-20);
    .name {
      color: var(--color-primary);
    }
    svg,
    path {
      @include icons.color(var(--color-primary));
    }

    & .icon-fill-light {
      fill: var(--color-primary-50) !important;
    }
  }

  @media (max-width: 1024px) {
    &.userMobile {
      align-items: center;
      color: var(--color-gray-80);
      cursor: pointer;
      display: flex;
      font: var(--font-text-1-normal);
      justify-content: flex-start;
      min-height: 44px;
      padding: 0;
      padding: 4px 8px;
      word-break: break-word;
      transition: 0.1sease-out;
      width: 100%;
      flex-direction: column;

      &.active {
        background-color: #fff;
      }
    }
  }
}

.name-wrapper {
  display: flex;
  flex: 1 1;
  margin-left: 12px;
  margin-right: 4px;
  min-width: 0;
}

.name {
  color: var(--color-gray-80);
  flex-shrink: 1;
  font: var(--font-text-1-normal);
  max-width: 100%;

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.profile-icon,
.arrow-icon {
  flex-shrink: 0;
}
