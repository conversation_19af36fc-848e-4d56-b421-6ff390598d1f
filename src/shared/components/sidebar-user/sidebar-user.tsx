import { FC } from 'react'
import styles from './sidebar-user.module.scss'
import classNamesBind from 'classnames/bind'
import { SidebarUserProps } from './sidebar-user.d'
import ArrowDownIcon from '@/shared/ui/Icon/icons/components/ArrowDown'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { Link, useLocation } from 'react-router-dom'
import { URLS } from '../../configs/urls'
import { useTranslation } from 'react-i18next'
import { userAPI, IUser } from 'entities/employee'
import { useAppSelector } from '@/store'
import { useMediaQuery } from 'usehooks-ts'

const cx = classNamesBind.bind(styles)

const isRouteActive = (route: string, path: string) => route.startsWith(path)

const getUserFullName = (data: IUser | undefined) =>
  data ? `${data?.first_name || ''} ${data?.last_name || ''}` : ''

export const SidebarUser: FC<SidebarUserProps.Props> = () => {
  const { t } = useTranslation()
  const { data } = useAppSelector(userAPI.endpoints.getUserInfo.select())
  const { pathname } = useLocation()
  const isAdmin = pathname.startsWith('/lk/admin')

  const isMobile = useMediaQuery('(max-width: 1024px)')

  const isActive =
    isRouteActive(pathname, URLS.USER_PROFILE) || isRouteActive(pathname, URLS.ADMIN_PROFILE)

  return (
    <div className={cx('container')}>
      <Link
        to={isAdmin ? URLS.ADMIN_PROFILE : URLS.USER_PROFILE}
        className={cx(
          'wrapper',
          isActive && 'active',
          pathname.startsWith('/lk/user') && 'userMobile',
        )}
      >
        {isMobile && pathname.startsWith('/lk/user') ? (
          <>
            <svg
              width='28'
              height='28'
              viewBox='0 0 28 28'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M5.88918 15.75C4.09262 15.75 2.625 17.108 2.625 18.8125C2.625 21.4022 3.97832 23.3546 6.09437 24.626C8.17758 25.8777 10.9861 26.4688 14 26.4688C17.0139 26.4688 19.8224 25.8777 21.9056 24.626C24.0217 23.3546 25.375 21.4022 25.375 18.8125C25.375 17.1213 23.9198 15.75 22.1251 15.75H5.88918Z'
                fill='#8E97AF'
              />
              <path
                d='M8.75 8.3125C8.75 5.17138 11.2963 2.625 14.4375 2.625C17.5787 2.625 20.125 5.17138 20.125 8.3125C20.125 11.4537 17.5787 14 14.4375 14C11.2963 14 8.75 11.4537 8.75 8.3125Z'
                fill='#C9CEDC'
                className={cx('icon-fill-light')}
              />
            </svg>
            <div className={cx('name-wrapper')}>
              <p className={cx('name')}>{t('commons:profile')}</p>
            </div>
          </>
        ) : (
          <>
            <svg
              width='20'
              height='20'
              viewBox='0 0 20 20'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M10 2C7.79086 2 6 3.79086 6 6C6 8.20914 7.79086 10 10 10C12.2091 10 14 8.20914 14 6C14 3.79086 12.2091 2 10 2ZM5.00873 11C3.90315 11 3 11.8869 3 13C3 14.6912 3.83281 15.9663 5.13499 16.7966C6.41697 17.614 8.14526 18 10 18C11.8547 18 13.583 17.614 14.865 16.7966C16.1672 15.9663 17 14.6912 17 13C17 11.8956 16.1045 11 15 11L5.00873 11Z'
                fill='#8E97AF'
              ></path>
            </svg>

            <div className={cx('name-wrapper')}>
              <p className={cx('name')}>
                {getUserFullName(data) || t('commons:loading_with_dots')}
              </p>
            </div>
            <IconWrapper direction='left' size='20' color={isActive ? 'primary' : undefined}>
              <ArrowDownIcon />
            </IconWrapper>
          </>
        )}
      </Link>
    </div>
  )
}
