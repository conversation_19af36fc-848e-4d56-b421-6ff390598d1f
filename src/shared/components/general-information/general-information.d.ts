export declare namespace GeneralInformationProps {
  type DataItem = {
    id: string
    title: string
    value?: string | number | null
    suffix?: string
    loading?: boolean
    color?: string
    background?: string
    change?: number
    hasActiveCourse?: boolean
  }
  interface Own {
    items: DataItem[]
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch
}

export {}
