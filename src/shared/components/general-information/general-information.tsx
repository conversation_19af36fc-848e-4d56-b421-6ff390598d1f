import classNamesBind from 'classnames/bind'

import { Card } from '@/shared/ui'
import { DataItem } from '@/shared/ui/data-item'
import { GeneralInformationProps } from './general-information.d'
import styles from './general-information.module.scss'

const cx = classNamesBind.bind(styles)

export const GeneralInformation = ({ items }: GeneralInformationProps.Props) => {
  return (
    <Card width='wide' className={cx('mainMetricks')}>
      {items.map(({ id, title, value, suffix, color, loading, hasActiveCourse, background }) => (
        <DataItem
          key={id}
          className={cx('mainMetricksDataItem')}
          title={title}
          value={value}
          suffix={suffix}
          color={color}
          loading={loading}
          hasActiveCourse={hasActiveCourse}
          background={background}
        />
      ))}
    </Card>
  )
}
