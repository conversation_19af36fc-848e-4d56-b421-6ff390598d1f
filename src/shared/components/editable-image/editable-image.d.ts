export declare namespace EditableImageProps {
  interface Own
    extends React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> {
    className?: string
    name: string
    height?: number | string
    required?: boolean
    сanDownload?: boolean
    value?: FileList | string
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch
}

export {}
