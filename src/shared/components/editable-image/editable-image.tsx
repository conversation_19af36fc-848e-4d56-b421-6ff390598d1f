import React, { useCallback } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './editable-image.module.scss'

import { EditableImageProps } from './editable-image.d'
import { ButtonIcon, Image } from '@/shared/ui'
import EditBoldIcon from '@/shared/ui/Icon/icons/components/EditBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { FileUploader } from '@/shared/components'
import { useFileUrlResolver } from '@/shared/hooks'
import { onFileDownload } from '@/shared/helpers/download'

const cx = classNamesBind.bind(styles)

const EditableImage: React.FC<EditableImageProps.Props> = props => {
  const { className, height, required, сanDownload = false, value, onChange } = props

  const image = useFileUrlResolver(!value || typeof value === 'string' ? value : value?.[0])

  const picture = image ? { src: image } : undefined

  const onFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      onChange && onChange(e)
    },
    [onChange],
  )

  if (сanDownload && picture?.src) {
    return (
      <div className={cx('wrapper', className)}>
        <Image height={height} image={picture} contentClassName={cx('inner')}>
          <FileUploader accept='image/*' onChange={onFileChange} required={required}>
            <ButtonIcon
              hasBackground
              color='primary'
              iconSize='28'
              size='36'
              className={cx('edit', 'icon')}
              icon='editBold'
              type='button'
              onClick={e => {
                e.preventDefault()
              }}
            />
          </FileUploader>
          <ButtonIcon
            color='primary'
            iconSize='28'
            size='36'
            hasBackground
            className={cx('upload', 'icon')}
            icon='download'
            onClick={e => onFileDownload(e, picture.src)}
            type='button'
          />
        </Image>
      </div>
    )
  }

  return (
    <FileUploader
      accept='image/*'
      className={cx('wrapper', className)}
      onChange={onFileChange}
      required={required}
    >
      <Image height={height} image={picture} key={picture?.src}>
        <IconWrapper color='primary' size='32' className={cx('edit')}>
          <EditBoldIcon />
        </IconWrapper>
      </Image>
    </FileUploader>
  )
}

export default EditableImage
