@use "../../../shared/assets/styles/mixins/text";
// @use "mixins/icons";

.wrapper {
  align-items: center;

  border: 1px solid var(--color-primary);

  border-radius: 10px;
  display: flex;
  padding: 5px 10px;

  text-decoration: none;

  transition: 200ms ease;

  &:hover {
    border-color: var(--color-primary);
    transform: scale(1.02);
    .text {
      color: var(--color-primary);
    }
    .icon {
      // @include icons.color(var(--color-primary));
    }
  }
}

.text {
  @include text.main(18px);
  color: var(--color-primary);
  line-height: 20px;
  text-decoration: none;
}
