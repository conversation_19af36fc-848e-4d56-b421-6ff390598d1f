import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './preview-button.module.scss'

import { PreviewButtonProps } from './preview-button.d'
import { OptionalLink } from '@/shared/ui'
import PlayIcon from '@/shared/ui/Icon/icons/components/PlayIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const PreviewButton: React.FC<PreviewButtonProps.Props> = props => {
  const { className, onClick, to } = props

  const { t } = useTranslation()

  return (
    <OptionalLink to={to} className={cx('wrapper', className)} onClick={onClick}>
      <div className={cx('text')}>{t('components.preview_button.preview')}</div>
      <IconWrapper className={cx('icon')} color='primary' size='24'>
        <PlayIcon />
      </IconWrapper>
    </OptionalLink>
  )
}

export default PreviewButton
