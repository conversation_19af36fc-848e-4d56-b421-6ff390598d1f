import { FC } from 'react'
import classNamesBind from 'classnames/bind'
import { TabBarProps } from './tab-bar.d'
import styles from './tab-bar.module.scss'
import { SidebarLinks } from '../sidebar-links'
import { SidebarUser } from '../sidebar-user'

const cx = classNamesBind.bind(styles)

export const TabBar: FC<TabBarProps.Props> = () => {
  return (
    <div className={cx('wrapper')}>
      <SidebarLinks />
      <SidebarUser />
    </div>
  )
}
