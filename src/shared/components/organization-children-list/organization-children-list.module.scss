.title {
  align-items: center;

  color: var(--color-gray-90, #343b54);
  display: flex;
  font: var(--font-title-4-normal);
  margin-bottom: 12px;
  > * {

    cursor: pointer;
    margin-left: 10px;
  }
}

.notChildren {
  color: var(--gray-sub);
  font-size: 12px;
}

.list {
  border: 1px solid var(--color-gray-30, #ebeff2);
  border-radius: 16px;

  .listItem {
    align-items: center;
    background: #fff;
    border-bottom: 1px solid var(--color-gray-30, #ebeff2);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    padding: 14px 16px 14px 20px;
    transition: var(--transition);

    &:first-child {
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }

    &:last-child {
      border-bottom-left-radius: 16px;
      border-bottom-right-radius: 16px;
    }

    &:hover {
      background: var(--color-gray-20, #f9fafc);
      transition: var(--transition);
    }

    .title {
      color: var(--color-gray-90, #343b54);
      font: var(--font-text-2-normal);
      margin-bottom: 4px;
    }

    .licenses {
      color: var(--color-gray-80, #5c6585);
      font: var(--font-caption-1-normal);
    }

    .icons {
      align-items: center;
      display: flex;
      > * {
        margin-right: 4px;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
