/* eslint-disable no-empty-pattern */
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { useState } from 'react'
import { organizationAPI } from '@/entities/organization'
import { createSearchParams, useNavigate } from 'react-router-dom'
import { coursesApi } from '@/entities/courses'
import { useNotification } from '@/shared/contexts/notifications'
import { useTranslation } from 'react-i18next'
import { v4 as uuid } from 'uuid'

type TThemeCourse = {
  id: UUID
  title: string
  order: number
  index: number
  hasStep?: boolean
}

export const useOrganizationChildrenList = ({ from }: { from?: string }) => {
  const navigate = useNavigate()
  const { add } = useNotification()
  const { t } = useTranslation('commons')
  const onOrganizationClick = (id: UUID) => navigate(`/lk/admin/organization/${id}`)

  const [openCopyCourses, setOpenCopyCourses] = useState(false)
  const [openCopyThemes, setOpenCopyThemes] = useState(false)
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<UUID | null>(null)

  const onStatisticsClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    navigate({
      pathname: `/lk/admin/organization/${id}/statistics`,
      search: createSearchParams({
        from: from || '',
      }).toString(),
    })
  }

  const onEditClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    navigate({
      pathname: `/lk/admin/organization/${id}/edit`,
      search: createSearchParams({
        from: from || '',
      }).toString(),
    })
  }

  const [deletedId, setDeletedId] = useState<UUID | null>(null)
  const [openDeleteModal, setOpenDeleteModal] = useState(false)
  const [deleteOrganization, { isLoading: isDeleteLoading }] =
    organizationAPI.useDeleteOrganizationMutation()
  const [copyCourses] = coursesApi.useCopyOrganizationCoursesMutation()
  const [copyThemes] = coursesApi.useCopyOrganizationThemesMutation()

  const onDeleteClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    setDeletedId(id)
    setOpenDeleteModal(true)
  }

  const onDeleteClose = async (id?: UUID | null) => {
    if (id) {
      await deleteOrganization(id).unwrap()
    }

    setDeletedId(null)
    setOpenDeleteModal(false)
  }

  const onCreateClick = () => {
    navigate('create')
  }

  const onCopyCoursesClick = (organizationId: UUID) => {
    setSelectedOrganizationId(organizationId)
    setOpenCopyCourses(true)
  }

  const onCopyThemesClick = (organizationId: UUID) => {
    setSelectedOrganizationId(organizationId)
    setOpenCopyThemes(true)
  }

  const handleCopyCoursesSelect = async (organizationId: UUID, courses: UUID[]) => {
    try {
      await copyCourses({ organizationId, coursesIds: courses }).unwrap()
      add({
        id: uuid(),
        message: t('courses_will_be_copied'),
        status: 'success',
      })
    } catch (error) {
      add({
        message: t('commons:error_unexpected'),
        status: 'error',
        withoutAdditionalInfo: true,
        id: uuid(),
      })
    }
    setOpenCopyCourses(false)
    setSelectedOrganizationId(null)
  }

  const handleCopyCoursesClose = () => {
    setOpenCopyCourses(false)
    setSelectedOrganizationId(null)
  }

  const handleCopyThemesSelect = async (themes: TThemeCourse[], organizationId?: UUID) => {
    if (!organizationId) return
    const ids = themes.map(theme => theme.id)
    try {
      await copyThemes({ organizationId, themesIds: ids }).unwrap()
      add({
        id: uuid(),
        message: t('themes_will_be_copied'),
        status: 'success',
      })
    } catch (error) {
      add({
        message: t('commons:error_unexpected'),
        status: 'error',
        withoutAdditionalInfo: true,
        id: uuid(),
      })
    }
    setOpenCopyThemes(false)
    setSelectedOrganizationId(null)
  }

  const handleCopyThemesClose = () => {
    setOpenCopyThemes(false)
    setSelectedOrganizationId(null)
  }

  return {
    onOrganizationClick,
    onStatisticsClick,
    onEditClick,
    deletedId,
    openDeleteModal,
    setOpenDeleteModal,
    isDeleteLoading,
    onDeleteClick,
    onDeleteClose,
    onCreateClick,
    openCopyCourses,
    openCopyThemes,
    selectedOrganizationId,
    onCopyCoursesClick,
    onCopyThemesClick,
    handleCopyCoursesSelect,
    handleCopyCoursesClose,
    handleCopyThemesSelect,
    handleCopyThemesClose,
  }
}
