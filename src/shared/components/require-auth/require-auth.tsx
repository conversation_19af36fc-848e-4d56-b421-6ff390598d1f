import { FC } from 'react'
import { RequireAuthProps } from './require-auth.d'
import { Navigate, useLocation } from 'react-router-dom'
import { selectUserAuthorized } from '@/store/slices/auth'
import { useAppSelector } from '@/store'
import { URLS } from '@/shared/configs/urls'

export const RequireAuth: FC<RequireAuthProps.Props> = ({ children }) => {
  const isAuth = useAppSelector(selectUserAuthorized)
  const location = useLocation()

  if (!isAuth) return <Navigate to={URLS.LOGIN_PAGE} state={{ from: location }} replace />

  return children
}
