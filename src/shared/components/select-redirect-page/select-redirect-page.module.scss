.action__wrapper {
  align-items: center;

  background: var(--color-surface);
  border-radius: 12px;
  box-shadow: var(--shadow-border);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  padding: 14px 16px;

  transition: var(---transition);
  span {
    color: var(--color-gray-80);
    font: var(--font-text-2-medium);
  }
  &.active {
    span {
      color: var(--color-primary);
    }
  }

  &:hover {
    background-color: var(--color-gray-20);

    transition: var(---transition);
  }
}

.label {
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
  margin-bottom: 8px;
}

.wrapper {
  background: var(--surface, #fff);
  border-radius: 16px;
  box-shadow:
    0 32px 88px -4px rgba(24, 39, 75, 0.12),
    0 12px 28px -6px rgba(24, 39, 75, 0.12);
  display: grid;
  grid-gap: 16px;
  grid-template-rows: auto 1fr auto;
  height: 100%;
  max-height: 90vh;
  max-width: 1150px;
  width: 100%;
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-4-normal);
}

.counter {
  align-self: center;

  color: var(--color-primary);
  font: var(--font-text-2-normal);
}

.inner {
  display: grid;
  grid-gap: 16px 20px;
  grid-template-columns: minmax(250px, 315px) 1fr;
  grid-template-rows: 1fr;
  overflow: hidden;
  padding: 2px;
}

.searchInput {
  width: 100%;
}

.list {
  height: 100%;
  overflow-y: auto;
}

.listInner {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .list__item {
    display: flex;
    align-items: center;
    gap: 8px;

    .checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  .listName {
    color: var(--color-gray-100);
    font: var(--font-text-1-medium);
    margin-left: 12px;
    margin-top: 12px;
    &:first-child {
      margin-top: 0;
    }
  }
  .listCategoryName {
    align-items: center;
    display: grid;
    grid-gap: 12px;
    grid-template-columns: auto 1fr;
    display: flex;
    margin: 8px 0 4px;
    min-height: 44px;
    padding: 0 12px;
    > span {
      color: var(--color-gray-80);
      font: var(--font-text-2-demibold);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &:hover {
      > span {
        white-space: inherit;
      }
    }
  }
}

.listEmail {
  align-items: center;

  border-radius: 8px;
  cursor: pointer;
  display: grid;
  gap: 12px;
  grid-template-columns: auto 1fr;
  padding: 8px 12px;

  transition: var(--transition);
  &:hover {
    background: var(--color-gray-40);
  }
  &.active {
    background: var(--color-gray-50);
  }
  span {
    color: var(--color-gray-90);
    font: var(--font-text-2-normal);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &:hover {
    span {
      white-space: inherit;
    }
  }
}

.email {
  .emailTitle {
    color: var(--color-gray-90);
    font: var(--font-title-3-medium);
    margin-bottom: 16px;
  }
  iframe {
    height: 100%;
    width: 100%;
  }
}

.buttonWrapper {
  align-self: center;
  display: flex;
  justify-content: end;
}
