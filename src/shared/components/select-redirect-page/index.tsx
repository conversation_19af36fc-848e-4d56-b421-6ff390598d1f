import { FC, useState } from 'react'
import styles from './select-redirect-page.module.scss'
import classNamesBind from 'classnames/bind'
import { Button, Checkbox, Loader } from '@/shared/ui'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { Modal } from '../modal'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '../../../entities/phishing'

const cx = classNamesBind.bind(styles)

interface Props {
  className?: string
  label?: string
  onChange?: (id?: UUID) => void
  onSelect?: (id?: UUID) => void
  selected?: UUID
}

export const SelectRedirectPage: FC<Props> = props => {
  const { className, selected, onChange, onSelect, label } = props
  const { t } = useTranslation()

  const { data, isLoading, error } = phishingQueries.useGetRedirectPagesQuery()
  const [getPageInfo, { data: page, isLoading: isLoadingPage, error: pageError }] =
    phishingQueries.useLazyGetRedirectPageQuery()
  const [active, setActive] = useState<boolean>(false)
  const [selectedPage, setSelectedPage] = useState<UUID | undefined>(selected)

  const pages = data?.data

  if (!pages) return

  const btnText = selectedPage ? t('commons:page_selected') : t('commons:select_page')

  return (
    <>
      {<div className={cx('label')}>{label}</div>}
      <div
        className={cx('action__wrapper', className, {
          active: !!selectedPage,
        })}
        onClick={() => setActive(true)}
      >
        <span>{btnText}</span>
        <IconWrapper color={!selectedPage ? 'gray80' : 'primary'}>
          <PlusIcon />
        </IconWrapper>
      </div>
      <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
        <div className={cx('title')}>{t('commons:select_redirect_page')}</div>
        <div className={cx('inner')}>
          <div className={cx('list')}>
            {isLoading && <Loader size='56' loading />}
            {!isLoading && error && (
              <div className='error-text'>{t('commons:error_unexpected')} :(</div>
            )}
            {!isLoading && !error && pages && !pages.length && (
              <div className='empty-text'>{t('commons:not_find_templates')} :(</div>
            )}
            {!isLoading && !error && pages && !!pages.length && (
              <ul className={cx('listInner')}>
                {pages.map(page => {
                  return (
                    <li className={cx('list__item')} key={page?.id}>
                      <Checkbox
                        className={cx('checkbox')}
                        onChange={v => {
                          if (!page?.id) return
                          if (v) {
                            onChange?.(page?.id)
                            setSelectedPage(page?.id)
                            getPageInfo({ id: page?.id })
                            return
                          }
                          onChange?.(undefined)
                          setSelectedPage(undefined)
                        }}
                        customChecked={selectedPage === page?.id}
                        label={<>{page?.name}</>}
                      />
                    </li>
                  )
                })}
              </ul>
            )}
          </div>
          <div className={cx('email')}>
            {selectedPage ? (
              <>
                {isLoadingPage && <Loader size='56' loading />}
                {!isLoadingPage && pageError && (
                  <div className='error-text'>{t('commons:error_unexpected')} :(</div>
                )}

                {!isLoading && !error && page && (
                  <>
                    <div className={cx('emailTitle')}>{page.name}</div>
                    <iframe allowFullScreen srcDoc={page.html} />
                  </>
                )}
              </>
            ) : null}
          </div>
        </div>

        <div className={cx('buttonWrapper')}>
          <Button
            color='gray'
            onClick={() => {
              setActive(false)
              onSelect?.(undefined)
              setSelectedPage(undefined)
            }}
          >
            {t('commons:cancel')}
          </Button>
          <Button
            onClick={() => {
              setActive(false)
              if (selectedPage) onSelect?.(selectedPage)
            }}
            disabled={!selectedPage}
          >
            {t('commons:select')}
          </Button>
        </div>
      </Modal>
    </>
  )
}
