import { RadialProgressProps } from "../radial-progress";

export declare namespace PhishingCampaignInfoCardProps {
  interface PhishingCampaignInfoCardData {
    level: RadialProgressProps.RadialProgressLevelVariants;
    icon: JSX.Element;
    title: string;
    rangeFrom: number;
    rangeTo: number;
  }
  interface Own extends PhishingCampaignInfoCardData {
    className?: string;
  }

  type Props = Own;
}

export {};
