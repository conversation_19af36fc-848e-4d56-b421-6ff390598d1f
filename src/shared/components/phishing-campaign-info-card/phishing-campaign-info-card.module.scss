@use "../../assets/styles/mixins/icons";

.info-wrapper {
  align-items: center;
  display: flex;
}

.info {
  align-items: center;
  display: flex;
  margin-left: 24px;
}

.info-value {

  color: var(--color-gray-70);
  font: var(--font-title-4-normal);
  margin-left: 8px;
}

.info-icon {
  @include icons.color(var(--color-gray-70));
}

.info-cards-header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
}

.info-cards {
  align-items: center;
  display: flex;
  margin: 12px -10px 0;
}

.info-card-wrapper {

  background-color: var(--color-surface);
  border-radius: 16px;
  box-shadow: var(--shadow-border);
  display: flex;
  flex: 1 0;
  margin: 0 10px;
  overflow: hidden;
}

.info-card {
  align-items: flex-start;
  display: flex;
  flex: 1 0;
  flex-direction: column;
  padding: 24px;
  padding-bottom: 32px;
}

.info-card-header {
  align-items: center;
  display: flex;
}

.info-card-icon-wrapper {
  align-items: center;
  
  background: var(--gradient-primary);
  border-radius: 100%;
  display: flex;
  flex-shrink: 0;
  height: 28px;
  justify-content: center;
  width: 28px;

  @include icons.color(var(--color-surface));
}

.info-card-name {

  color: var(--color-gray-90);
  font: var(--font-text-2-medium);
  margin-left: 12px;
  white-space: nowrap;
}

.info-card-content {
  align-items: center;

  display: flex;
  height: 152px;
  justify-content: center;
  margin: 0 auto;
  margin-top: 28px;
  position: relative;
  width: 152px;
}

.info-card-chart {
  position: absolute;
}

.info-card-info {
  align-items: center;

  display: flex;
  flex-direction: column;
  z-index: 1;
}

.info-card-info-value {
  color: var(--color-gray-90);
  font: var(--font-text-2-medium);
}

.info-card-info-percent {

  color: var(--color-gray-90);
  font: var(--font-title-4-medium);
  margin-top: 2px;
}

.templates-title {

  color: var(--color-gray-90);
  font: var(--font-title-4-medium);
  margin-top: 24px;
}
