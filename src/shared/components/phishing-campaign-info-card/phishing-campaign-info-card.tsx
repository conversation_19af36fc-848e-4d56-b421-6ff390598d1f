import { FC } from 'react'
import styles from './phishing-campaign-info-card.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingCampaignInfoCardProps } from './phishing-campaign-info-card.d'
import { Card } from '@/shared/ui'
import { RadialProgress } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignInfoCard: FC<PhishingCampaignInfoCardProps.Props> = props => {
  const { t } = useTranslation()
  const { className, icon, level, rangeFrom, rangeTo, title } = props

  const clamp = Math.max(0, Math.min(100, (rangeFrom / rangeTo) * 100))
  const percent = rangeTo ? (clamp % 1 === 0 ? clamp : clamp.toFixed(1)) : 0

  return (
    <Card className={cx('info-card', className)}>
      <div className={cx('info-card-header')}>
        <div className={cx('info-card-icon-wrapper')}>{icon}</div>
        <div className={cx('info-card-name')}>{title}</div>
      </div>
      <div className={cx('info-card-content')}>
        <RadialProgress level={level} percent={+percent} className={cx('info-card-chart')} />
        <div className={cx('info-card-info')}>
          <p className={cx('info-card-info-value')}>
            {rangeFrom} {t('commons:of')} {rangeTo}
          </p>
          <p className={cx('info-card-info-percent')}>{percent}%</p>
        </div>
      </div>
    </Card>
  )
}
