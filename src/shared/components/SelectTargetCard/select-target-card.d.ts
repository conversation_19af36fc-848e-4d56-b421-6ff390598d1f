import { ReactNode } from 'react'
import { ITargetData } from 'react-app/new/contexts'

export declare namespace SelectTargetCardProps {
  interface Own {
    className?: string
    label?: string
    onChange?: (target: ITargetData) => void
    selected?: ITargetData
    modalSlot?: ReactNode
    setOpen?: (v: boolean) => void
    text?: ReactNode
    isLoading?: boolean
    active?: boolean
    count?: number
  }

  type Props = Own
}

export {}
