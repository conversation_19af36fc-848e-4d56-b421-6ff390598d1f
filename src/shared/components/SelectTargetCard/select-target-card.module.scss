.wrapper {
  align-items: center;

  background: var(--color-surface);
  border-radius: 12px;
  box-shadow: var(--shadow-border);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  padding: 14px 16px;

  transition: var(---transition);
  span {
    color: var(--color-gray-80);
    font: var(--font-text-2-medium);
  }
  &.active {
    span {
      color: var(--color-primary);
    }
  }

  &:hover {
    background-color: var(--color-gray-20);

    transition: var(---transition);
  }
}

.label {

  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
  margin-bottom: 8px;
}


.loader{
  margin-left: auto;
}