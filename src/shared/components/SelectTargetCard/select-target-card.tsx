import { FC } from 'react'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import { Loader } from '@/shared/ui'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import styles from './select-target-card.module.scss'
import { SelectTargetCardProps } from './select-target-card.d'

const cx = classNamesBind.bind(styles)

export const SelectTargetCard: FC<SelectTargetCardProps.Props> = props => {
  const {
    className,
    text,
    label,
    selected = {
      target_users: [],
      target_departments: [],
    },
    setOpen,
    modalSlot,
    isLoading,
    active,
  } = props

  const { t } = useTranslation()

  return (
    <>
      {label && <div className={cx('label')}>{label}</div>}
      <div
        className={cx('wrapper', className, {
          active:
            !!selected?.target_users.length || !!selected?.target_departments.length || active,
        })}
        onClick={() => {
          if (setOpen) setOpen(true)
        }}
      >
        {selected && selected.all_users_ids ? (
          <span>
            {t(`commons:employees_choosed`, {
              count: selected.all_users_ids.length,
            })}
          </span>
        ) : selected && !!selected.target_departments.length ? (
          <span>
            {t(`departments.choosed`, {
              count: selected.target_departments?.length,
            })}
          </span>
        ) : (
          <span>{text}</span>
        )}
        {isLoading && <Loader className={cx('loader')} size='28' />}
        {!isLoading && (
          <IconWrapper color={!selected.target_users.length ? 'gray80' : 'primary'}>
            <PlusIcon />
          </IconWrapper>
        )}
      </div>
      {modalSlot}
    </>
  )
}
