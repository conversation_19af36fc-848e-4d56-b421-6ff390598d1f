// @use 'mixins/icons';

.wrapper {
  align-items: center;
  display: flex;
  gap: 4px;
}

.filterButton {
  &.active {
    color: var(--color-primary, #3dbc87);
    // @include icons.color(var(--color-primary, #3dbc87));
  }
}

.label {
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
}

.multiselectLoader {
  height: auto;
  margin-top: 15px;
}

.itemsInner {
  > * {
    margin-bottom: 24px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.modalWrapper {
  max-width: 913px;
  // max-width: 613px;
  width: 100%;

  .title {
    color: var(--color-gray-70, #8e97af);
    font: var(--font-title-3-medium);

    margin-bottom: 20px;
  }

  .input {
    margin-bottom: 24px;
    &:last-child {
      margin-bottom: 0;
    }
  }

  .buttonWrapper {
    align-items: center;
    display: flex;
    gap: 16px;
    justify-content: end;
  }

  .items {
    display: grid;
    grid-gap: 27px;
    // grid-template-columns: repeat(4, 1fr);
    grid-template-columns: repeat(1, 1fr);
    margin-bottom: 32px;

    &.common {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  .selectWrapper {
    padding: 10px 8px 10px 12px;
    border: 2px solid var(--color-gray-60, #c9cedc);
    border-radius: 8px;
  }
}

.innerGrid {
  display: grid;
  grid-gap: 27px;
  grid-template-columns: repeat(2, 1fr);
  .items {
    grid-template-columns: repeat(2, 1fr);
  }
}

.itemTitle {
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
  margin-bottom: 8px;
}

.itemCheckbox {
  align-items: center;

  display: flex;
  gap: 12px;
  margin-bottom: 8px;

  span {
    max-width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--color-gray-90, #343b54);

    cursor: pointer;
    font: var(--font-text-2-normal);
  }
  &:last-child {
    margin-bottom: 0;
  }
}

.itemInputs {
  display: grid;
  grid-gap: 12px;
  grid-template-columns: 1fr 1fr;

  > div {
    position: relative;
    span {
      color: var(--color-gray-80, #5c6585);
      font: var(--font-text-2-normal);
      left: 12px;
      position: absolute;
      top: 50%;

      transform: translateY(-50%);
      z-index: 10;
    }
  }

  .itemInput {
    border: 2px solid var(--color-gray-60, #c9cedc);
    padding: 10px 12px 10px 35px;
    width: 100%;
    text-align: right;
  }
}
