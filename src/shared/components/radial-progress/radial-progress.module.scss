.progress {
  transform: rotate(-90deg);
  transform-origin: 50% 50%;

  transition: stroke-dashoffset 300ms ease;
}

.level {
  &-neutral {
    stroke: var(--color-statistics-neutral);
  }

  &-complementary {
    stroke: var(--color-statistics-complementary);
  }

  &-bad {
    stroke: var(--color-statistics-bad);
  }

  &-warning {
    stroke: var(--color-statistics-warning);
  }

  &-good {
    stroke: var(--color-statistics-good);
  }
}
