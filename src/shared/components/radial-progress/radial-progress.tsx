import { FC, useLayoutEffect, useState } from 'react'
import styles from './radial-progress.module.scss'
import classNamesBind from 'classnames/bind'
import { RadialProgressProps } from './radial-progress.d'

const cx = classNamesBind.bind(styles)

export const RadialProgress: FC<RadialProgressProps.Props> = props => {
  const { className, level, percent = 0, width = 152, height = 152, strokeWidth = 8 } = props

  const [radius, setRadius] = useState<number>(0)
  const [circumference, setCircumference] = useState<number>(0)
  const [offset, setOffset] = useState<number>(0)

  useLayoutEffect(() => {
    const updateProgress = () => {
      const newRadius = (width - strokeWidth) / 2
      const newCircumference = newRadius * 2 * Math.PI
      const newOffset = newCircumference - (percent / 100) * newCircumference
      setRadius(newRadius)
      setCircumference(newCircumference)
      setOffset(newOffset)
    }

    updateProgress()
  }, [percent])

  return (
    <svg
      className={cx(className)}
      width={width}
      height={height}
      viewBox={`0 0 ${width} ${height}`}
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle
        cx={width / 2}
        cy={height / 2}
        r={radius}
        fill='transparent'
        stroke='#F0F3F7'
        strokeWidth={8}
      />

      <circle
        cx={width / 2}
        cy={height / 2}
        r={radius}
        fill='transparent'
        strokeWidth={strokeWidth}
        className={cx('progress', `level-${level}`)}
        strokeDasharray={circumference}
        strokeDashoffset={offset}
      />
    </svg>
  )
}
