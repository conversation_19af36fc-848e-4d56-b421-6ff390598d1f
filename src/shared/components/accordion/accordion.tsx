import React, {
  useState,
  ReactNode,
  createContext,
  useContext,
  useCallback,
  useEffect,
} from 'react'

type AccordionProps = {
  multiple?: boolean
  children: ReactNode
  className?: string
}

type AccordionItemProps = {
  id: string
  children: ReactNode
  isDefaultOpen?: boolean
  className?: string
  classNameHeader?: string
  classNameBody?: string
  renderHeader: (open?: boolean) => ReactNode
  onOpen?: () => void
  onClose?: () => void
}

type AccordionContextType = {
  isOpen: (id: string) => boolean
  toggle: (id: string) => void
}

const AccordionContext = createContext<AccordionContextType | undefined>(undefined)

const useAccordion = () => {
  const context = useContext(AccordionContext)
  if (!context) {
    throw new Error('useAccordion must be used within an AccordionProvider')
  }
  return context
}

export const Accordion: React.FC<AccordionProps> = ({
  multiple = false,
  children,
  className = '',
}) => {
  const [openItems, setOpenItems] = useState<string[]>([])

  const isOpen = useCallback((id: string) => openItems.includes(id), [openItems])

  const toggle = useCallback(
    (id: string) => {
      setOpenItems(prevOpenItems => {
        if (multiple) {
          return prevOpenItems.includes(id)
            ? prevOpenItems.filter(itemId => itemId !== id)
            : [...prevOpenItems, id]
        }

        return prevOpenItems.includes(id) ? [] : [id]
      })
    },
    [multiple],
  )

  return (
    <AccordionContext.Provider value={{ isOpen, toggle }}>
      <div className={className}>{children}</div>
    </AccordionContext.Provider>
  )
}

// Компонент AccordionItem (отдельная секция)
export const AccordionItem: React.FC<AccordionItemProps> = ({
  id,
  children,
  isDefaultOpen = false,
  className = '',
  onOpen,
  onClose,
  classNameHeader,
  classNameBody,
  renderHeader,
}) => {
  const { isOpen, toggle } = useAccordion()
  const open = isOpen(id)

  useEffect(() => {
    if (open) {
      if (onOpen) onOpen()
    } else {
      if (onClose) onClose()
    }
  }, [open, onOpen, onClose])

  useEffect(() => {
    if (isDefaultOpen) toggle(id)
  }, [isDefaultOpen, id, toggle])

  return (
    <div className={className}>
      <div className={classNameHeader} onClick={() => toggle(id)}>
        {renderHeader(open)}
      </div>
      {open && <div className={classNameBody}>{children}</div>}
    </div>
  )
}
