import { OutputData, API, BlockMutationEvent } from '@editorjs/editorjs'
import { WrapperProps } from '@react-editor-js/core';

export declare namespace EditorProps {
    interface Own extends WrapperProps {
      data?: OutputData
      setData: React.Dispatch<React.SetStateAction<OutputData | undefined>>
      onSubmit: () => void
      onChange?: (api: API, event: BlockMutationEvent | BlockMutationEvent[]) => void
      stepId?: UUID
      isUserSide?: boolean
      isLoading?: boolean
    }
  
    type Props = Own;
  }
  
  export {};
  