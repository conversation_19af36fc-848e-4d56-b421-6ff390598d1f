.wrapper {
    margin-top: 16px;


    &.errorWrapper {
        border: 2px solid var(--color-component-warn);
        border-radius: 12px;
    }
}

.uploader {
    display: flex;
    align-items: center;
    gap: 8px;
    width: fit-content;
}

.edit {
    svg {
        path {
            stroke: none;
        }
    }
}

.noFile {
    color: var(--color-gray-80);
    font: var(--font-text-2-demibold);
}

.playerWrapper,
.actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 12px;
}

.actions {
    gap: 4px;

    .menuIcon {
        cursor: pointer;

        svg {
            path {
                stroke: none;
            }

            rect {
                fill: none !important;
            }
        }
    }
}

.player {
    svg {
        path {
            stroke: none;
        }
    }
}

.error {
    max-width: 60%;
    text-align: center;
    color: var(--color-component-warn);
    font: var(--font-text-1-normal);
    margin-top: 8px;
}