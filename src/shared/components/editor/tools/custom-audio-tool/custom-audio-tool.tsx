import React, { useCallback, useEffect, useRef, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './custom-audio-tool.module.scss'

import { CustomAudioToolProps } from './custom-audio-tool.d'
import Replace from '@/shared/ui/Icon/icons/components/Replace'
import Trash from '@/shared/ui/Icon/icons/components/Trash'
import Headphones from '@/shared/ui/Icon/icons/components/Headphones'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

import { AudioPlayer, FileUploader } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import DeleteConfirmModal from '@/shared/modals/delete-confirm-modal/delete-confirm-modal'
import ReplaceModal from '@/shared/modals/replace-modal/replace-modal'
import { transformToMB } from '../helpers'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'components__custom-audio'

export const CustomAudio: React.FC<CustomAudioToolProps.Props> = props => {
  const { data, onDataChange, sizeLimit, readOnly } = props
  const [audio, setAudio] = useState<string>(
    data?.audio && 'path' in data?.audio ? data?.audio?.path : '',
  )
  const [error, setError] = useState<string>('')

  const { t } = useTranslation(TRANSLATION_FILE)
  const [openReplace, setOpenReplace] = useState<boolean>(false)
  const [openDelete, setOpenDelete] = useState<boolean>(false)

  const wrapperRef = useRef<HTMLDivElement>(null)

  const onFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.files ? e.target.files[0] : undefined
      if (!!value) {
        if (sizeLimit && value.size > sizeLimit) {
          setError(
            t('commons:file_error.size_limit', {
              value: transformToMB(sizeLimit),
            }),
          )
          return
        }
        const objectUrl = URL.createObjectURL(value)
        setAudio(objectUrl)
        onDataChange({
          audio: value,
        })
      }
    },
    [onDataChange],
  )

  useEffect(() => {
    return () => URL.revokeObjectURL(audio)
  }, [])

  const openReplaceModal = () => setOpenReplace(true)
  const openDeleteModal = () => setOpenDelete(true)

  const deleteAudio = () => {
    setAudio('')
    onDataChange({
      audio: undefined,
    })
  }

  const replaceAudio = () => {
    deleteAudio()
    setTimeout(() => wrapperRef.current?.click(), 100)
  }

  return (
    <div className={cx('wrapper')}>
      {audio ? (
        <div className={cx('playerWrapper')}>
          <AudioPlayer className={cx('player')} audio={audio} />
          {!readOnly && (
            <div className={cx('actions')}>
              <IconWrapper
                size='32'
                color='gray70'
                className={cx('menuIcon')}
                onClick={openReplaceModal}
              >
                <Replace />
              </IconWrapper>
              <IconWrapper
                size='32'
                color='gray70'
                className={cx('menuIcon')}
                onClick={openDeleteModal}
              >
                <Trash />
              </IconWrapper>
            </div>
          )}
        </div>
      ) : (
        <>
          <FileUploader
            accept='audio/*'
            onChange={onFileChange}
            className={cx('uploader')}
            wrapperRef={wrapperRef}
            disabled={readOnly}
          >
            <IconWrapper color='gray80' size='24' className={cx('edit')}>
              <Headphones />
            </IconWrapper>
            <div className={cx('noFile')}>{t('commons:add_audio')}</div>
          </FileUploader>
          {!!error && <div className={cx('error')}>{error}</div>}
        </>
      )}
      {openDelete && (
        <DeleteConfirmModal
          open={openDelete}
          setOpen={setOpenDelete}
          title={t('audio_delete')}
          description={t('audio_delete_hint')}
          onConfirm={deleteAudio}
        />
      )}
      {openReplace && (
        <ReplaceModal
          open={openReplace}
          setOpen={setOpenReplace}
          title={t('audio_replace')}
          onConfirm={replaceAudio}
        />
      )}
    </div>
  )
}
