import { <PERSON>, Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useState } from 'react'
import styles from './custom-import.module.scss'
import classNamesBind from 'classnames/bind'
import { CustomImportProps } from './custom-import.d'
import { Modal } from '@/shared/components'
import { <PERSON><PERSON>, Loader } from '@/shared/ui'
import DownloadIcon from '@/shared/ui/Icon/icons/components/DownloadIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useConfig, useDownload } from '@/shared/hooks'
import { combineURLs } from '@/shared/helpers'
import { useTranslation } from 'react-i18next'
import { useDropzone } from 'react-dropzone'
import { ACCEPTED_FILE_TYPES } from './const'
import { themePresentationApi, themeQuizApi, themeScormApi } from '@/entities/themeCourse/model/api'
import { useParams } from 'react-router-dom'
import { useUploadEvent } from '@/shared/hooks/use-upload-event'
import { EVENT_STATUS_LS_KEY } from '@/shared/components/theme-content/const'
import { useNotification } from '@/shared/contexts/notifications'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__custom-import'
const IMPORT_BUTTON_ID = 'import-file-button'

const accepted = {
  [ACCEPTED_FILE_TYPES.zip]: [],
  [ACCEPTED_FILE_TYPES.zip_compressed]: [],
  [ACCEPTED_FILE_TYPES.pdf]: [],
  [ACCEPTED_FILE_TYPES.xlsx]: [],
  [ACCEPTED_FILE_TYPES.json]: [],
}

export const CustomImport: FC<CustomImportProps.Props> = ({
  stepId,
  createTheme,
  appendThemeInCourseIfNeeded,
}) => {
  const [open, setOpen] = useState<boolean>(false)
  const [status, setStatus] = useState<'initial' | 'created' | 'completed' | 'failed'>('initial')
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const { t } = useTranslation(TRANSLATION_FILE)
  const { theme_id = '', course_id = '', section_id = '' } = useParams()
  const { handleUpload, handleJsonUpload } = useUploadEvent({ setStatus: setStatus })
  const { add: addNotification } = useNotification()

  useEffect(() => {
    const statuses = localStorage.getItem(EVENT_STATUS_LS_KEY)
    if (statuses && stepId) {
      const jsonStatuses = JSON.parse(statuses)
      if (jsonStatuses[stepId]) {
        setStatus(jsonStatuses[stepId])
      } else {
        setOpen(true)
      }
    } else {
      setOpen(true)
    }
  }, [stepId])

  const [file, setFile] = useState<File | null>(null)
  const { acceptedFiles, getRootProps, getInputProps } = useDropzone({
    accept: accepted,
  })

  const [downloadQuiz] = themeQuizApi.useAddQuizFromFileMutation()
  const [downloadScorm] = themeScormApi.useCreateScormMutation()
  const [downloadPresentation] = themePresentationApi.useCreatePresentationMutation()
  const [uploadJsonQuizStep] = themePresentationApi.useUploadJsonQuizStepMutation()
  const [uploadJsonQuizStepInCourse] = themePresentationApi.useUploadJsonQuizStepInCourseMutation()

  const fileHandlers = {
    [ACCEPTED_FILE_TYPES.zip]: downloadScorm,
    [ACCEPTED_FILE_TYPES.zip_compressed]: downloadScorm,
    [ACCEPTED_FILE_TYPES.xlsx]: downloadQuiz,
    [ACCEPTED_FILE_TYPES.pdf]: downloadPresentation,
  }

  const handleReset = () => {
    setFile(() => null)
  }

  const config = useConfig()
  const [download] = useDownload()

  //TODO: Added test file here
  const onCsvTemplateClick = () => {
    download(
      combineURLs(
        config?.url?.startsWith(`https://`) ? config?.url || '' : `https://` + config?.url || '',
        '/lk/assets/legacy_files/quiz_template.xlsx',
      ),
      'quiz_template.xlsx',
    )
  }

  const handleImport = async () => {
    if (!file) return

    if (!theme_id) {
      createTheme()
      return
    }

    // Проверяем тип файла
    if (!Object.values(ACCEPTED_FILE_TYPES).includes(file?.type)) {
      addNotification({
        message: t('unsupported_file_type'),
        status: 'error',
        id: Date.now().toString(),
      })
      return
    }

    setIsLoading(true)

    try {
      const body = new FormData()
      body.append('file', file)

      const response =
        file.type === ACCEPTED_FILE_TYPES.json
          ? course_id && section_id
            ? uploadJsonQuizStepInCourse({
                courseId: course_id,
                sectionId: section_id,
                themeId: theme_id,
                body,
              })
            : uploadJsonQuizStep({
                themeId: theme_id,
                body,
              })
          : fileHandlers[file.type]({
              themeId: theme_id,
              body,
            })

      const res = await response.unwrap()

      // валидация json файла от сервера
      if (
        file.type === ACCEPTED_FILE_TYPES.json &&
        res &&
        'success' in res &&
        res.success === false
      ) {
        addNotification({
          message: t('json_structure_error'),
          status: 'error',
          id: Date.now().toString(),
        })
        return
      }

      if (stepId && res.id) {
        handleUpload({
          eventId: res.id,
          stepId: stepId,
        })
      }
      if (stepId && !res.id) {
        handleJsonUpload()
      }

      if (appendThemeInCourseIfNeeded) {
        await appendThemeInCourseIfNeeded()
      }

      setOpen(false)
      addNotification({
        message: t('file_uploaded_successfully'),
        status: 'success',
        id: Date.now().toString(),
      })
    } catch (error) {
      console.error('Upload error:', error)
      addNotification({
        message: t('upload_error'),
        status: 'error',
        id: Date.now().toString(),
      })
      setStatus('failed')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (acceptedFiles[0]) {
      setFile(acceptedFiles[0])
    }
  }, [acceptedFiles])

  const openModal = () => setOpen(true)
  const handleDropzoneClick: MouseEventHandler<HTMLElement> = e => {
    if ((e.target as HTMLElement).id !== IMPORT_BUTTON_ID) e.stopPropagation()
  }

  const content = {
    initial: <Button onClick={openModal}>{t('choose_file')}</Button>,
    created: <div className={cx('hint')}>{t('created')}</div>,
    completed: <div className={cx('hint')}>{t('success')}</div>,
    failed: <div className={cx('hint')}>{t('fail')}</div>,
  }

  if (isLoading) {
    return <Loader size='56' className={cx('loader')} />
  }

  return (
    <div className={cx('wrapper')}>
      <div className={cx('chooseWrapper')}>{content[status] ?? t('import_from_file')}</div>
      <Modal active={open} setActive={setOpen} className={cx('modal')}>
        <div className={cx('title')}>{t('import_from_file')}</div>
        <div className={cx('inner')}>
          <div
            {...getRootProps({
              className: cx('dropzone'),
              onClick: handleDropzoneClick,
            })}
          >
            <input {...getInputProps()} />
            <svg
              className={cx('bg')}
              width='194'
              height='246'
              viewBox='0 0 194 246'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M16 246H178C186.837 246 194 238.837 194 230V59.359C194 55.1053 192.306 51.0267 189.293 48.0244L145.774 4.66544C142.775 1.67762 138.714 0 134.481 0H16C7.16344 0 0 7.16344 0 16V230C0 238.837 7.16345 246 16 246Z'
                fill='#F0F3F7'
              />
              <path
                fillRule='evenodd'
                clipRule='evenodd'
                d='M138.165 0H128V54.0004C128 60.6278 133.373 66.0004 140 66.0004H194V57.8156C194 54.624 192.729 51.5639 190.467 49.312L144.204 3.24811C142.537 1.58894 140.434 0.468927 138.165 0Z'
                fill='#E1E4EB'
              />
            </svg>
            <div className={cx('info', 'first')}>
              <div className={cx('dropzoneHint')}>{t('dropzone_hint')}</div>
              <Button size='small' id={IMPORT_BUTTON_ID} className={cx('addButton')}>
                {file ? file.name : t('choose_file')}
              </Button>
              <span className={cx('help')}>({t('file_format')})</span>
            </div>
          </div>
        </div>
        <div className={cx('buttonWrapper')}>
          <div className={cx('customButton')} onClick={onCsvTemplateClick}>
            {t('download_template')}
            <IconWrapper>
              <DownloadIcon />
            </IconWrapper>
          </div>
          <>
            <Button color='gray' onClick={handleReset}>
              {t('commons:reset')}
            </Button>
            <Button disabled={!file} onClick={handleImport}>
              {t('commons:add')}
            </Button>
          </>
        </div>
      </Modal>
    </div>
  )
}
