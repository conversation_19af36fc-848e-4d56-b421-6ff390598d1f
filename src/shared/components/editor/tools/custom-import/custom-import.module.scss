.modal {
  display: grid;
  grid-gap: 32px;
  grid-template-rows: auto 1fr auto;
  max-height: fit-content;
  max-width: 624px;
  width: 100%;
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-4-normal);
}

.buttonWrapper {
  display: flex;
  gap: 16px;
  justify-content: end;
}

.file {
  left: -999px;

  opacity: 0;

  pointer-events: none;
  position: absolute;
}

.inner {
  align-items: center;

  display: flex;
  justify-content: center;
  min-height: 250px;
  position: relative;
  .bg {
    left: 50%;
    position: absolute;
    top: 50%;

    transform: translate(-50%, -50%);
    z-index: 4;
  }
}

.customButton {
  align-items: center;
  color: var(--color-gray-80, #5c6585);
  cursor: pointer;
  display: flex;
  flex-direction: row-reverse;
  font: var(--font-text-2-medium);
  gap: 4px;
  justify-content: center;
  margin-right: auto;
}

.info {
  position: relative;
  z-index: 5;

  &.first {
    margin-top: 75px;
    width: 194px;
    margin-left: auto;
    margin-right: auto;
    padding: 0 6px;

    .addButton {
      display: block;
      margin: 0 auto;
      max-width: 180px;
      overflow: hidden;
      text-overflow: ellipsis;

      white-space: nowrap;
    }

    .help {
      color: var(--color-gray-80, #5c6585);
      display: block;
      font: var(--font-caption-2-normal);
      margin-top: 12px;
      text-align: center;
    }
    .customButton {
      margin-top: 40px;
    }
  }
}

.dropzone {
  width: 100%;
  height: 100%;
  border: 4px dashed var(--color-gray-40);
  padding: 20px;
  min-height: 300px;
  border-radius: 8px;
}

.dropzoneHint {
  font: var(--font-text-2-normal);
  color: var(--color-gray-80);
  margin: 0 auto;
  text-align: center;
  margin-bottom: 8px;
}

.chooseWrapper {
  width: 100%;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hint {
  color: var(--color-gray-80);
  font: var(--font-text-1-normal);
  text-align: center;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
}

.loader {
  height: 300px;
  width: 100%;
}
