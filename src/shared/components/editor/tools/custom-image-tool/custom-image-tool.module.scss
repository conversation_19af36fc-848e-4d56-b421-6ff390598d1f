.wrapper {
  display: flex;
  justify-content: center;
  width: 100%;

  &.errorWrapper {
    border: 2px solid var(--color-component-warn);
    border-radius: 12px;
  }
}

.image {
  max-width: 100%;
  height: 100%;

  &.active {
    border: 2px solid var(--color-primary);
  }

  &:not(.active) {
    width: 100%;
    height: 500px;
  }

  span {
    border-radius: 12px;
  }

  &.readOnly {
    border: none;
  }
}

.content {
  justify-content: center;
  align-items: center;
}

.button {
  background-color: var(--color-gray-50);
  padding: 6px 16px !important;
}

.picture-inner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 81px !important;
  height: 64px !important;
  margin-bottom: 16px;

  svg {
    width: 81px !important;
    height: 64px !important;
  }
}

.ellipsis {
  position: absolute;
  right: 10px;
  top: 10px;
  height: 24px !important;
  width: 20px !important;
  cursor: pointer;
  border: 1px solid var(--color-gray-30);
  background-color: var(--white);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    path {
      stroke: none;
    }
  }
}

.positionWrapper {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 10px;
  height: 10px;

  .menu {
    background: var(--color-surface, #fff);
    border: 1px solid var(--color-gray-30, #ebeff2);
    border-radius: 8px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
    display: none;
    padding: 6px 1px;
    position: absolute;

    width: max-content;
    z-index: 12;
    .item {
      display: flex;
      align-items: center;
      gap: 12px;
      background: var(--color-surface, #fff);
      color: var(--color-gray-80, #5c6585);
      cursor: pointer;
      font-family: TT Norms Pro;
      font-size: 14px;
      font-style: normal;
      font-weight: 450;
      line-height: 18px;
      padding: 8px 16px;

      transition: var(--transition);
      &:hover {
        background: var(--color-gray-40, #f0f3f7);

        transition: var(--transition);
      }

      .menuIcon {
        svg {
          rect {
            fill: var(--color-gray-40) !important;
          }

          path {
            stroke: none;
          }
        }
      }
    }
    &.active {
      display: block;
    }
  }
}

.error {
  max-width: 60%;
  text-align: center;
  color: var(--color-component-warn);
  font: var(--font-text-1-normal);
  margin-top: 8px;
}
