export declare namespace CustomImageToolProps {
  type TStyle = {
    style: Partial<Record<keyof CSSStyleDeclaration, string | number>>
  }

  type TOptions = {
    options?: TStyle | undefined
  }
  type TData = {
    image?: File | { path?: string } | string | undefined
  } & TOptions

  interface Own {
    data: TData
    onDataChange: (arg: TData) => void
    sizeLimit?: number
    readOnly: boolean
  }

  type Props = Own
}

export {}
