import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'

import { Button, ResizableImage, ResizableImageProps, Image } from '@/shared/ui'
import Replace from '@/shared/ui/Icon/icons/components/Replace'
import Trash from '@/shared/ui/Icon/icons/components/Trash'
import Ellipsis from '@/shared/ui/Icon/icons/components/Ellipsis'
import Picture from '@/shared/ui/Icon/icons/components/Picture'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useEvent } from '@/shared/hooks'
import DeleteConfirmModal from '@/shared/modals/delete-confirm-modal/delete-confirm-modal'
import ReplaceModal from '@/shared/modals/replace-modal/replace-modal'
import { transformToMB } from '../helpers'

import styles from './custom-image-tool.module.scss'
import { CustomImageToolProps } from './custom-image-tool.d'

const cx = classNamesBind.bind(styles)

const ADD_BUTTON_ID = 'add-picture-button'
const TRANSLATION_FILE = 'components__custom-image'

export const CustomImage: React.FC<CustomImageToolProps.Props> = props => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const [openReplace, setOpenReplace] = useState<boolean>(false)
  const [openDelete, setOpenDelete] = useState<boolean>(false)
  const [error, setError] = useState<string>('')

  const { data, onDataChange, sizeLimit, readOnly } = props
  const { acceptedFiles, getRootProps, getInputProps, inputRef } = useDropzone({
    accept: {
      'image/*': [],
    },
    disabled: readOnly,
    maxSize: sizeLimit ?? undefined,
    onDrop: (_, fileRejections) => {
      if (!fileRejections.length) {
        return setError('')
      }
      fileRejections.forEach(file => {
        file.errors.forEach(err => {
          if (err.code === 'file-too-large' && !!sizeLimit) {
            setError(
              t('commons:file_error.size_limit', {
                value: transformToMB(sizeLimit),
              }),
            )
          }
        })
      })
    },
  })

  const [openMenu, setOpenMenu] = useState<boolean>(false)
  const [picture, setPicture] = useState<string>(() => {
    if (!data?.image) return ''
    if (typeof data.image === 'string') return data.image
    if (data.image instanceof File) return URL.createObjectURL(data.image)
    return data.image.path ?? ''
  })
  const [imageWidth, setImageWidth] = useState<number | null>(
    (data?.options?.style?.width as number) ?? null,
  )
  const imageHeight = data?.options?.style?.height as number

  const wrapper = useRef<HTMLDivElement>(null)

  const handleSizeChange = ({ width, height }: ResizableImageProps.Size) => {
    setImageWidth(width)
    onDataChange({
      image: acceptedFiles[0] ?? picture,
      options: {
        style: {
          width,
          height,
        },
      },
    })
  }

  useEffect(() => {
    if (acceptedFiles[0]) {
      const objectUrl = URL.createObjectURL(acceptedFiles[0])
      setPicture(objectUrl)
      onDataChange({
        image: acceptedFiles[0],
        options: undefined,
      })
      // free memory when ever this component is unmounted
      return () => URL.revokeObjectURL(objectUrl)
    }
  }, [acceptedFiles])

  const handleDropzoneClick: MouseEventHandler<HTMLElement> = e => {
    if ((e.target as HTMLElement).id !== ADD_BUTTON_ID) e.stopPropagation()
  }

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return
    if (e.composedPath().indexOf(wrapper.current) === -1) {
      setOpenMenu(() => false)
    }
  }, [])

  useEvent('click', handleOutsideClick, window)

  const replaceImage = () => {
    if (inputRef.current) {
      inputRef.current.click()
    }
  }

  const deleteImage = () => {
    setPicture('')
    onDataChange({
      image: undefined,
    })
  }

  const openReplaceModal = () => setOpenReplace(true)
  const openDeleteModal = () => setOpenDelete(true)

  const menu = useMemo(
    () => [
      {
        text: t('replace'),
        name: 'replace',
        handleClickMenuItem: openReplaceModal,
        icon: (
          <IconWrapper size='32' color='gray70' className={cx('menuIcon')}>
            <Replace />
          </IconWrapper>
        ),
      },
      {
        text: t('commons:delete'),
        name: 'delete',
        handleClickMenuItem: openDeleteModal,
        icon: (
          <IconWrapper size='32' color='gray70' className={cx('menuIcon')}>
            <Trash />
          </IconWrapper>
        ),
      },
    ],
    [t],
  )

  const handleOpenMenu = () => setOpenMenu(prev => !prev)

  const className = 'dropzone'

  const imageContent = picture ? (
    !readOnly && (
      <div className={cx('positionWrapper')} ref={wrapper}>
        <IconWrapper color='gray70' size='14' className={cx('ellipsis')} onClick={handleOpenMenu}>
          <Ellipsis />
        </IconWrapper>
        <div className={cx('menu', { active: openMenu })}>
          {menu.map(i => (
            <div
              key={`menu-item-${i.name}`}
              className={cx('item')}
              onClick={() => i.handleClickMenuItem()}
            >
              {i.icon}
              {i.text}
            </div>
          ))}
        </div>
      </div>
    )
  ) : (
    <div className={cx('picture-inner')}>
      <IconWrapper className={cx('icon')} color='gray60'>
        <Picture />
      </IconWrapper>
      <Button className={cx('button')} id={ADD_BUTTON_ID} size='small' color='gray'>
        {t('add_image')}
      </Button>
      {!!error && <div className={cx('error')}>{error}</div>}
    </div>
  )

  return (
    <div
      {...getRootProps({
        className,
        onClick: handleDropzoneClick,
      })}
    >
      <div
        className={cx('wrapper', {
          errorWrapper: !!error && !picture,
        })}
      >
        <input {...getInputProps()} />
        {readOnly && (
          <Image
            image={{ src: picture }}
            key={picture}
            className={cx('image', {
              active: !!picture,
              readOnly: readOnly,
            })}
            contentClassName={cx('content')}
            maxWidth={imageWidth}
            maxHeight={imageHeight}
          >
            {imageContent}
          </Image>
        )}

        {!readOnly && (
          <>
            <div style={{ display: 'none' }}>{imageWidth}</div>
            <ResizableImage
              src={picture}
              key={picture}
              className={cx('image', {
                active: !!picture,
                readOnly: readOnly,
              })}
              contentClassName={cx('content')}
              onSizeChange={handleSizeChange}
              width={imageWidth}
              height={imageHeight}
            >
              {imageContent}
            </ResizableImage>
          </>
        )}
      </div>
      {openDelete && (
        <DeleteConfirmModal
          open={openDelete}
          setOpen={setOpenDelete}
          title={t('image_delete')}
          description={t('image_delete_hint')}
          onConfirm={deleteImage}
        />
      )}
      {openReplace && (
        <ReplaceModal
          open={openReplace}
          setOpen={setOpenReplace}
          title={t('replace_image')}
          onConfirm={replaceImage}
          submitText={t('commons:replace')}
        />
      )}
    </div>
  )
}
