import React, { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './custom-video-tool.module.scss'

import { CustomVideoToolProps } from './custom-video-tool.d'
import { Button } from '@/shared/ui'
import Replace from '@/shared/ui/Icon/icons/components/Replace'
import Trash from '@/shared/ui/Icon/icons/components/Trash'
import Ellipsis from '@/shared/ui/Icon/icons/components/Ellipsis'
import PlayerPause from '@/shared/ui/Icon/icons/components/PlayerPause'
import PlayerPlay from '@/shared/ui/Icon/icons/components/PlayerPlay'
import PlayerSound from '@/shared/ui/Icon/icons/components/PlayerSound'
import FullscreenEnterIcon from '@/shared/ui/Icon/icons/components/FullscreenEnterIcon'
import FullscreenExitIcon from '@/shared/ui/Icon/icons/components/FullscreenExitIcon'
import Video from '@/shared/ui/Icon/icons/components/Video'
import { Icon<PERSON>rapper } from '@/shared/ui/Icon/IconWrapper'

import screenfull from 'screenfull'
import ReactPlayer from 'react-player'
import Duration from './components/duration/duration'
import { useDropzone } from 'react-dropzone'
import { useTranslation } from 'react-i18next'
import { useEvent } from '@/shared/hooks'
import { transformToMB } from '../helpers'
import DeleteConfirmModal from '@/shared/modals/delete-confirm-modal/delete-confirm-modal'
import ReplaceModal from '@/shared/modals/replace-modal/replace-modal'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'components__custom-video'
const ADD_BUTTON_ID = 'add-video-button'

let timeoutId: ReturnType<typeof setTimeout>

export const CustomVideo: React.FC<CustomVideoToolProps.Props> = props => {
  const { data, onDataChange, sizeLimit, readOnly } = props
  const [video, setVideo] = useState<string>(
    data?.video && 'path' in data?.video ? data?.video?.path : '',
  )
  const [showPlayPause, setShowPlayPause] = useState<boolean>(false)
  const { acceptedFiles, getRootProps, getInputProps, inputRef } = useDropzone({
    accept: {
      'video/*': [],
    },
    disabled: readOnly,
    maxSize: sizeLimit ?? undefined,
    onDrop: (_, fileRejections) => {
      if (!fileRejections.length) {
        return setError('')
      }
      fileRejections.forEach(file => {
        file.errors.forEach(err => {
          if (err.code === 'file-too-large' && !!sizeLimit) {
            setError(
              t('commons:file_error.size_limit', {
                value: transformToMB(sizeLimit),
              }),
            )
          }
        })
      })
    },
  })

  const isMobileIOS = /(iPad|iPhone|iPod)/.test(navigator.userAgent)

  const [openMenu, setOpenMenu] = useState<boolean>(false)
  const [openReplace, setOpenReplace] = useState<boolean>(false)
  const [openDelete, setOpenDelete] = useState<boolean>(false)
  const [error, setError] = useState<string>('')

  const openReplaceModal = () => setOpenReplace(true)
  const openDeleteModal = () => setOpenDelete(true)

  const { t } = useTranslation(TRANSLATION_FILE)

  useEffect(() => {
    if (acceptedFiles[0]) {
      const objectUrl = URL.createObjectURL(acceptedFiles[0])
      setVideo(objectUrl)
      onDataChange({
        video: acceptedFiles[0],
      })
      // free memory when ever this component is unmounted
      return () => URL.revokeObjectURL(objectUrl)
    }
  }, [acceptedFiles])

  {
    const [state, setState] = useState({
      url: '',
      pip: false,
      playing: false,
      controls: isMobileIOS,
      light: false,
      volume: 0.8,
      muted: false,
      played: 0,
      loaded: 0,
      duration: 0,
      playbackRate: 1.0,
      loop: false,
      volumeOpen: false,
      dropdownOpen: false,
      fullscreen: false,
      seeking: false,
      visible_button_refresh: true,
    })

    const player = useRef<ReactPlayer | null>(null)
    const playerWrapper = useRef(null)
    const wrapper = useRef<HTMLDivElement>(null)

    const handlePlayPause = () => {
      setState({ ...state, playing: !state.playing })
    }

    useEffect(() => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      if (!state.playing) {
        setShowPlayPause(true)
      } else {
        timeoutId = setTimeout(() => setShowPlayPause(false), 3000)
      }
    }, [state.playing])

    const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setState({ ...state, volume: parseFloat(e.target.value) })
    }

    const handlePlay = () => {
      if (!state.playing) {
        setState({ ...state, playing: true })
      }
    }

    const handleEnablePIP = () => {
      setState({ ...state, pip: true })
    }

    const handleDisablePIP = () => {
      setState({ ...state, pip: false })
    }

    const handlePause = () => {
      setState({ ...state, playing: false })
    }

    const handleSeekMouseDown = () => {
      setState({ ...state, seeking: true })
    }

    const handleSeekChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setState({ ...state, played: parseFloat(e.target.value) })
    }

    const handleSeekMouseUp: MouseEventHandler<HTMLInputElement> = e => {
      setState({ ...state, seeking: false })
      if (player?.current) {
        //@ts-ignore
        player.current.seekTo(parseFloat((e.target as HTMLInputElement)?.value))
      }
    }

    const handleProgress = (stateIn: any) => {
      // We only want to update time slider if we are not currently seeking
      if (!state.seeking) {
        setState({ ...state, ...stateIn })
      }
    }

    const handleEnded = () => {
      setState({ ...state, playing: false })
    }

    const handleDuration = (duration: number) => {
      setState({ ...state, duration })
    }

    const toggleVolume = () => {
      setState({ ...state, volumeOpen: !state.volumeOpen })
    }

    const handleClickFullscreen = () => {
      if (screenfull.isEnabled && playerWrapper.current) {
        if (screenfull.isFullscreen) {
          screenfull.exit()
        } else {
          screenfull.request(playerWrapper.current)
        }
      }
    }

    useEffect(() => {
      if (screenfull.isEnabled) {
        const handler = () => setState(prev => ({ ...prev, fullscreen: !prev.fullscreen }))
        screenfull.on('change', handler)

        return () => {
          screenfull.off('change', handler)
        }
      }
    }, [])

    const handleDropzoneClick: MouseEventHandler<HTMLElement> = e => {
      if ((e.target as HTMLElement).id !== ADD_BUTTON_ID) e.stopPropagation()
    }

    const replaceVideo = () => {
      if (inputRef.current) {
        inputRef.current.click()
      }
    }

    const deleteVideo = () => {
      setVideo('')
      onDataChange({
        video: undefined,
      })
    }

    const menu = useMemo(
      () => [
        {
          text: t('replace'),
          name: 'replace',
          handleClickMenuItem: openReplaceModal,
          icon: (
            <IconWrapper size='32' color='gray70' className={cx('menuIcon')}>
              <Replace />
            </IconWrapper>
          ),
        },
        {
          text: t('commons:delete'),
          name: 'delete',
          handleClickMenuItem: openDeleteModal,
          icon: (
            <IconWrapper size='32' color='gray70' className={cx('menuIcon')}>
              <Trash />
            </IconWrapper>
          ),
        },
      ],
      [],
    )

    const handleOpenMenu = () => setOpenMenu(prev => !prev)

    const handleOutsideClick = useCallback((e: Event) => {
      if (!wrapper.current) return
      if (e.composedPath().indexOf(wrapper.current) === -1) {
        setOpenMenu(() => false)
      }
    }, [])

    useEvent('click', handleOutsideClick, window)

    return (
      <div {...getRootProps({ className: 'dropzone', onClick: handleDropzoneClick })}>
        <div
          className={cx('wrapper', {
            errorWrapper: !!error && !video,
          })}
        >
          <input {...getInputProps()} />
          {video ? (
            <div className={cx('playerWrapper')} ref={playerWrapper}>
              <ReactPlayer
                className='react-player'
                ref={player}
                url={video}
                width='100%'
                height='100%'
                pip={state.pip}
                playing={state.playing}
                controls={state.controls}
                loop={state.loop}
                playbackRate={state.playbackRate}
                volume={state.volume}
                muted={state.muted}
                onPlay={handlePlay}
                onEnablePIP={handleEnablePIP}
                onDisablePIP={handleDisablePIP}
                onPause={handlePause}
                onEnded={handleEnded}
                onProgress={handleProgress}
                onDuration={handleDuration}
                onReady={() => setState({ ...state, visible_button_refresh: true })}
              />
              {!readOnly && (
                <div className={cx('positionWrapper')} ref={wrapper}>
                  <IconWrapper
                    color='gray70'
                    size='14'
                    className={cx('ellipsis')}
                    onClick={handleOpenMenu}
                  >
                    <Ellipsis />
                  </IconWrapper>
                  <div className={cx('menu', { active: openMenu })}>
                    {menu.map(i => (
                      <div
                        key={`menu-item-${i.name}`}
                        className={cx('item')}
                        onClick={() => i.handleClickMenuItem()}
                      >
                        {i.icon}
                        {i.text}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              {state.visible_button_refresh && !isMobileIOS && (
                <>
                  <button
                    type='button'
                    onClick={() => handlePlayPause()}
                    className={cx('playPause')}
                  >
                    <IconWrapper
                      className={cx('playPauseIcon', showPlayPause && 'visible')}
                      size='62'
                      color='white'
                    >
                      {state.playing ? <PlayerPause /> : <PlayerPlay />}
                    </IconWrapper>
                  </button>
                  <div className={cx('playerBar')}>
                    <div className={cx('duration')}>
                      <Duration
                        seconds={state.duration * (1 - state.played)}
                        className={cx('time')}
                        played={state.played}
                      />
                      <input
                        type='range'
                        className={cx('durationSlider', 'slider')}
                        min={0}
                        max={0.999999}
                        step='any'
                        value={state.played}
                        onMouseDown={handleSeekMouseDown}
                        onChange={handleSeekChange}
                        onMouseUp={handleSeekMouseUp}
                        onTouchStart={handleSeekMouseDown}
                        onTouchMove={e => {
                          const target = e.target as HTMLInputElement
                          handleSeekChange({ target } as React.ChangeEvent<HTMLInputElement>)
                        }}
                        onTouchEnd={e => {
                          const target = e.target as HTMLInputElement
                          handleSeekMouseUp({
                            target,
                          } as unknown as React.MouseEvent<HTMLInputElement>)
                        }}
                        style={{
                          background: `linear-gradient(to right, #53C394 ${state.played * 100}%, #fff ${state.played * 100}%)`,
                        }}
                      />
                    </div>
                    <div className={cx('volumeWrapper')}>
                      <IconWrapper
                        size='24'
                        className={cx('volume')}
                        color='white'
                        onClick={() => toggleVolume()}
                      >
                        <PlayerSound />
                      </IconWrapper>
                      {state.volumeOpen && (
                        <input
                          className={cx('volumeSlider', 'slider')}
                          width='50px'
                          type='range'
                          min={0}
                          max={1}
                          step='any'
                          value={state.volume}
                          onChange={handleVolumeChange}
                          style={{
                            background: `linear-gradient(to right, #53C394 ${state.volume * 100}%, #fff ${state.volume * 100}%)`,
                          }}
                        />
                      )}
                    </div>
                    <IconWrapper
                      className={cx('fullscreen')}
                      size='20'
                      color='white'
                      onClick={() => handleClickFullscreen()}
                    >
                      {!state.fullscreen ? <FullscreenEnterIcon /> : <FullscreenExitIcon />}
                    </IconWrapper>
                  </div>
                </>
              )}
            </div>
          ) : (
            <div className={cx('video-inner')}>
              <IconWrapper className={cx('icon')} color='gray60'>
                <Video />
              </IconWrapper>
              <Button className={cx('button')} id={ADD_BUTTON_ID} size='small' color='gray'>
                {t('add_video')}
              </Button>
              {!!error && <div className={cx('error')}>{error}</div>}
            </div>
          )}
        </div>
        {openDelete && (
          <DeleteConfirmModal
            open={openDelete}
            setOpen={setOpenDelete}
            title={t('video_delete')}
            description={t('video_delete_hint')}
            onConfirm={deleteVideo}
          />
        )}
        {openReplace && (
          <ReplaceModal
            open={openReplace}
            setOpen={setOpenReplace}
            title={t('replace_video')}
            onConfirm={replaceVideo}
            submitText={t('commons:replace')}
          />
        )}
      </div>
    )
  }
}
