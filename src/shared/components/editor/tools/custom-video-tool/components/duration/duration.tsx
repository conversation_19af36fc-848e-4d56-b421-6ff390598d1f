import React from 'react'
import { DurationProps } from './duration.d'

const format = (seconds: number) => {
  const date = new Date(seconds * 1000)
  const hh = date.getUTCHours()
  const mm = date.getUTCMinutes()
  const ss = pad(String(date.getUTCSeconds()))
  if (hh) {
    return `${hh}:${pad(String(mm))}:${ss}`
  }
  return `${mm}:${ss}`
}

const pad = (value: string) => `0${value}`.slice(-2)

const Duration: React.FC<DurationProps.Props> = ({ className, seconds, played }) => (
  <time
    dateTime={`P${Math.round(seconds)}S`}
    className={className}
    style={{ left: `calc(${played * 100}% - 16px)` }}
  >
    {format(seconds)}
  </time>
)

export default Duration
