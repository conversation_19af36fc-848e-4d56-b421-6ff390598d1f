import CustomTool from '@/shared/components/generic-tool'

import React from 'react'

import { CustomVideoToolProps } from './custom-video-tool.d'
import { CustomVideo } from './custom-video-tool'

const SIZE_LIMIT = 104857600

const CustomVideoSizeLimit: React.FC<CustomVideoToolProps.Props> = props => {
  const { data, onDataChange, readOnly } = props

  return (
    <CustomVideo
      data={data}
      onDataChange={onDataChange}
      sizeLimit={SIZE_LIMIT}
      readOnly={readOnly}
    />
  )
}

const CustomVideoTool = CustomTool.createTool(CustomVideoSizeLimit, {
  icon: `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="36" height="36" rx="6" fill="none"/>
  <path d="M8 18C8 16.6868 8.25866 15.3864 8.7612 14.1732C9.26375 12.9599 10.0003 11.8575 10.9289 10.9289C11.8575 10.0003 12.9599 9.26375 14.1732 8.7612C15.3864 8.25866 16.6868 8 18 8C19.3132 8 20.6136 8.25866 21.8268 8.7612C23.0401 9.26375 24.1425 10.0003 25.0711 10.9289C25.9997 11.8575 26.7362 12.9599 27.2388 14.1732C27.7413 15.3864 28 16.6868 28 18" stroke="currentColor" stroke-width="2"/>
  <path d="M7 18H10C10.5523 18 11 18.4477 11 19V27C11 27.5523 10.5523 28 10 28H9C7.89543 28 7 27.1046 7 26V18Z" fill="currentColor"/>
  <path d="M25 19C25 18.4477 25.4477 18 26 18H29V26C29 27.1046 28.1046 28 27 28H26C25.4477 28 25 27.5523 25 27V19Z" fill="currentColor"/>
  <path d="M14 24.5L15.5 21C16.1667 23.1667 17.5 27.4 17.5 27C17.5 26.6 18.5 21.5 19 19L20.5 24.5L22 22" stroke="currentColor" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>
  `,
  title: 'Видео',
})

export default CustomVideoTool
