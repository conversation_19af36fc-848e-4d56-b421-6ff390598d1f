.button {
  background-color: var(--color-gray-50);
  padding: 6px 16px !important;
}

.video-inner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.icon {
  width: 81px !important;
  height: 64px !important;
  margin-bottom: 16px;

  svg {
    width: 81px !important;
    height: 64px !important;

    path {
      stroke: none;
    }
  }
}

.wrapper {
  width: 100%;
  height: 500px;
  background-color: var(--color-gray-40);
  border-radius: 12px;

  &.errorWrapper {
    border: 2px solid var(--color-component-warn);
    border-radius: 12px;
  }

  @media (max-width: 1024px) {
    height: auto;
  }
}

.ellipsis {
  position: absolute;
  right: 10px;
  top: 10px;
  height: 24px !important;
  width: 20px !important;
  cursor: pointer;
  border: 1px solid var(--color-gray-30);
  background-color: var(--white);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    path {
      stroke: none;
    }
  }
}

.positionWrapper {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 10px;
  height: 10px;

  .menu {
    background: var(--color-surface, #fff);
    border: 1px solid var(--color-gray-30, #ebeff2);
    border-radius: 8px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
    display: none;
    padding: 6px 1px;
    position: absolute;

    width: max-content;
    z-index: 12;
    .item {
      display: flex;
      align-items: center;
      gap: 12px;
      background: var(--color-surface, #fff);
      color: var(--color-gray-80, #5c6585);
      cursor: pointer;
      font-family: TT Norms Pro;
      font-size: 14px;
      font-style: normal;
      font-weight: 450;
      line-height: 18px;
      padding: 8px 16px;

      transition: var(--transition);
      &:hover {
        background: var(--color-gray-40, #f0f3f7);

        transition: var(--transition);
      }

      .menuIcon {
        svg {
          rect {
            fill: var(--color-gray-40) !important;
          }

          path {
            stroke: none;
          }
        }
      }
    }
    &.active {
      display: block;
    }
  }
}

.playerWrapper {
  position: relative;
  height: 100%;
}

.playPause {
  position: absolute;
  width: 180px;
  height: 140px;
  margin: auto;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    path {
      stroke: none;
    }
  }

  .playPauseIcon {
    display: none;
  }

  &:hover {
    .playPauseIcon {
      display: block;
    }
  }

  @media (max-width: 1024px) {
    .playPauseIcon {
      &.visible {
        display: block;
      }
    }

    width: 100px;
    height: 100px;
  }
}

.playerBar {
  position: absolute;
  bottom: 0;
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: space-between;
  width: 100%;
  padding: 8px 16px;
  cursor: pointer;
}

.fullscreen {
  svg {
    path {
      stroke: none;
    }
  }
}

.duration {
  flex-grow: 1;
  position: relative;

  .durationSlider {
    width: 100%;
  }

  .time {
    position: absolute;
    bottom: 24px;
    color: white;
  }
}

.volume {
  svg {
    path {
      stroke: none;
    }
  }
}

.volumeWrapper {
  position: relative;
}

.volumeSlider {
  position: absolute;
  width: 100px;
  bottom: 70px;
  transform: rotate(-90deg);
  left: -38px;

  &::-webkit-slider-thumb {
    opacity: 0;
  }
}

.slider {
  -webkit-appearance: none;
  appearance: none;
  cursor: pointer;
  outline: none;
  border-radius: 15px;
  height: 10px;
  background: #ccc;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    height: 20px;
    width: 20px;
    background-color: #fff;
    border-radius: 50%;
    border: 2px solid #53c394;
    transition: 0.2s ease-in-out;
  }

  &::-moz-range-thumb {
    height: 20px;
    width: 20px;
    background-color: #f50;
    border-radius: 50%;
    border: none;
    transition: 0.2s ease-in-out;
  }
}

.error {
  max-width: 60%;
  text-align: center;
  color: var(--color-component-warn);
  font: var(--font-text-1-normal);
  margin-top: 8px;
}
