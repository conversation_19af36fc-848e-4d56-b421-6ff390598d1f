import { StreamLanguage } from '@codemirror/language'
import { Language } from './custom-code-tool.d'

export const languageLoaders = {
  java: () => import('@codemirror/lang-java').then(mod => mod.java()),
  javascript: () => import('@codemirror/lang-javascript').then(mod => mod.javascript()),
  python: () => import('@codemirror/lang-python').then(mod => mod.python()),
  go: () => import('@codemirror/lang-go').then(mod => mod.go()),
  php: () => import('@codemirror/lang-php').then(mod => mod.php()),
  cpp: () => import('@codemirror/lang-cpp').then(mod => mod.cpp()),
  csharp: () => import('@replit/codemirror-lang-csharp').then(mod => mod.csharp()),
  plaintext: () =>
    import('@codemirror/legacy-modes/mode/shell').then(mod => StreamLanguage.define(mod.shell)),
}

export const languagesList: {
  id: Language
  title: string
}[] = [
  {
    id: 'java',
    title: 'Java',
  },
  {
    id: 'javascript',
    title: 'JavaScript',
  },
  {
    id: 'python',
    title: 'Python',
  },
  {
    id: 'go',
    title: 'Go',
  },
  {
    id: 'php',
    title: 'PHP',
  },
  {
    id: 'cpp',
    title: 'C++',
  },
  {
    id: 'csharp',
    title: 'C#',
  },
  {
    id: 'plaintext',
    title: 'Plain Text',
  },
]
