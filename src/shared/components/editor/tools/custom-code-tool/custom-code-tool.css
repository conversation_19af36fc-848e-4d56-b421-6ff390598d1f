.cm-line.added-line {
  background: rgba(0, 255, 0, 0.2);
}
.cm-line.removed-line {
  background: rgba(255, 0, 0, 0.2);
}
.diff-toolbar {
  position: absolute;
  left: -50px;
  display: flex;
  gap: 5px;
  background: rgba(0, 0, 0, 0.7);
  padding: 2px 5px;
  border-radius: 5px;
  transition: opacity 0.2s ease-in-out;
}
.diff-toolbar button {
  border: none;
  cursor: pointer;
  color: white;
  padding: 2px 5px;
}
.diff-toolbar .add {
  background: green;
}
.diff-toolbar .remove {
  background: red;
}

@media (max-width: 1024px) {
  .cm-editor {
    background-color: #f7f6f3;
    caret-color: transparent !important;
  }

  .cm-activeLine {
    background-color: transparent !important;
  }

  .cm-activeLineGutter {
    background-color: transparent !important;
  }

  .cm-cursor {
    display: none !important;
  }
}
