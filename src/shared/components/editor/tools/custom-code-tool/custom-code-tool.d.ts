export type Language =
  | 'java'
  | 'javascript'
  | 'python'
  | 'go'
  | 'php'
  | 'cpp'
  | 'csharp'
  | 'plaintext'
export type DiffType = 'added' | 'removed'
export type DiffLines = Record<number, DiffType>

export declare namespace CustomCodeToolProps {
  type TOptions = {
    language: Language
    diffLines: {
      [key: number]: 'added' | 'removed'
    }
  }
  type TData = {
    code: string
  } & TOptions

  interface Own {
    data: TData
    onDataChange: (arg: TData) => void
    readOnly: boolean
  }

  type Props = Own
}

export {}
