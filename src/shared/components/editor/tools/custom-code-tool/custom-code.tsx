import React, { useEffect, useMemo, useRef, useState } from 'react'
import CodeMirror, { Extension } from '@uiw/react-codemirror'
import { EditorView, Decoration, ViewPlugin } from '@codemirror/view'
import { bbedit } from '@uiw/codemirror-theme-bbedit'

import classNamesBind from 'classnames/bind'
import styles from './custom-code-tool.module.scss'
import './custom-code-tool.css'
import { createPortal } from 'react-dom'
import { Select } from '@/shared/ui'
import { CustomCodeToolProps, DiffLines, DiffType, Language } from './custom-code-tool.d'
import { languageLoaders, languagesList } from './const'

const cx = classNamesBind.bind(styles)

const addedLine = Decoration.line({ attributes: { class: 'added-line' } })
const removedLine = Decoration.line({ attributes: { class: 'removed-line' } })

const diffPlugin = (diffLines: DiffLines) =>
  ViewPlugin.fromClass(
    class {
      decorations

      constructor(view: EditorView) {
        this.decorations = this.getDecorations(view)
      }

      update(update: { view: EditorView }) {
        try {
          this.decorations = this.getDecorations(update.view)
        } catch (error) {
          console.warn('Error updating diff decorations:', error)
          this.decorations = Decoration.set([])
        }
      }

      getDecorations(view: EditorView) {
        const decorations = []
        const totalLines = view.state.doc.lines

        for (const line in diffLines) {
          const lineNum = Number(line)
          if (lineNum >= 0 && lineNum < totalLines) {
            try {
              const linePos = view.state.doc.line(lineNum + 1).from
              const deco = diffLines[lineNum] === 'added' ? addedLine : removedLine
              decorations.push(deco.range(linePos))
            } catch (error) {
              console.warn(
                `Failed to get line ${lineNum + 1} in ${totalLines}-line document:`,
                error,
              )
            }
          }
        }
        return Decoration.set(decorations)
      }
    },
    { decorations: v => v.decorations },
  )

export const CustomCode: React.FC<CustomCodeToolProps.Props> = props => {
  const { data, onDataChange, readOnly } = props
  const [code, setCode] = useState<string>(data.code ?? '')
  const [diffLines, setDiffLines] = useState<DiffLines>(data.diffLines ?? {})
  const [selectedLang, setSelectedLang] = useState<Language>(data.language ?? 'javascript')
  const [hoveredLine, setHoveredLine] = useState<number | null>(null)
  const [toolbarPosition, setToolbarPosition] = useState<{ top: number; left: number } | null>(null)
  const [isToolbarVisible, setIsToolbarVisible] = useState<boolean>(false)
  const [languageExtension, setLanguageExtension] = useState<Extension | null>(null)

  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)
  const hideTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  useEffect(() => {
    const loadLang = async () => {
      try {
        const loader = languageLoaders[selectedLang]
        if (loader) {
          const extension = await loader()
          setLanguageExtension(extension)
        } else {
          setLanguageExtension(null)
        }
      } catch (e) {
        console.error('Failed to load language:', selectedLang, e)
        setLanguageExtension(null)
      }
    }
    loadLang()
  }, [selectedLang])

  const extensions = useMemo(() => {
    const base = [bbedit, diffPlugin(diffLines)]
    return languageExtension ? [languageExtension, ...base] : base
  }, [languageExtension, diffLines])

  const toggleDiff = (line: number | null, type: DiffType | 'clear') => {
    if (line === null) return
    setDiffLines(prev => {
      if (type === 'clear') {
        const newLines = { ...prev }
        delete newLines[line]
        return newLines
      }
      return {
        ...prev,
        [line]: type,
      }
    })
  }

  const handleChange = (value: string) => {
    setCode(value)

    setDiffLines(prev => {
      const newLines: DiffLines = {}
      const lineCount = value.split('\n').length

      for (const line in prev) {
        const lineNum = Number(line)
        if (lineNum >= 0 && lineNum < lineCount) {
          newLines[lineNum] = prev[lineNum]
        }
      }

      return newLines
    })
  }

  useEffect(() => {
    onDataChange({
      language: selectedLang,
      diffLines: diffLines,
      code: code,
    })
  }, [selectedLang, diffLines, code, onDataChange])

  const hideToolbarWithDelay = () => {
    if (readOnly) return
    hideTimeoutRef.current = setTimeout(() => {
      setIsToolbarVisible(false)
    }, 300)
  }

  const cancelHideToolbar = () => {
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
    }
    setIsToolbarVisible(true)
  }

  const updateToolbarPosition = (lineNumber: number) => {
    if (!viewRef.current || !editorRef.current) return

    try {
      const totalLines = viewRef.current.state.doc.lines
      if (lineNumber < 0 || lineNumber >= totalLines) {
        return
      }

      const pos = viewRef.current.state.doc.line(lineNumber + 1).from
      const domAtPos = viewRef.current.domAtPos(pos).node
      const lineElement = (
        domAtPos.nodeType === Node.TEXT_NODE ? domAtPos.parentNode : domAtPos
      ) as HTMLElement

      const line = lineElement.closest('.cm-line') as HTMLElement
      if (!line) return

      const editorRect = editorRef.current.getBoundingClientRect()
      const lineRect = line.getBoundingClientRect()
      setToolbarPosition({
        top: window.scrollY + lineRect.top + lineRect.height / 2 - 10,
        left: editorRect.right + 10,
      })
      setIsToolbarVisible(true)
    } catch (error) {
      console.warn(`Failed to update toolbar position for line ${lineNumber}:`, error)
    }
  }

  const stableBasicSetup = useMemo(() => {
    const isMobile = window.matchMedia('(max-width: 1024px)').matches
    return { lineNumbers: !isMobile }
  }, [])

  return (
    <div ref={editorRef}>
      <Select
        value={selectedLang}
        list={languagesList}
        className={cx('select')}
        handleChange={i => setSelectedLang(i.id as Language)}
        disabled={readOnly}
      />
      <CodeMirror
        value={code}
        onChange={handleChange}
        extensions={extensions}
        basicSetup={stableBasicSetup}
        onCreateEditor={view => {
          viewRef.current = view
        }}
        onKeyDown={event => {
          event.stopPropagation()
        }}
        onMouseMove={event => {
          if (!viewRef.current) return

          try {
            const target = event.target as HTMLElement
            const pos = viewRef.current.posAtDOM(target)
            const lineInfo = viewRef.current.state.doc.lineAt(pos)
            const line = lineInfo.number - 1
            const totalLines = viewRef.current.state.doc.lines

            if (line >= 0 && line < totalLines) {
              setHoveredLine(line)
              if (!readOnly) {
                updateToolbarPosition(line)
              }
            }
          } catch (error) {}
        }}
        onMouseLeave={hideToolbarWithDelay}
        readOnly={readOnly}
      />
      {isToolbarVisible &&
        !readOnly &&
        toolbarPosition &&
        createPortal(
          <div
            className='diff-toolbar'
            style={{
              position: 'fixed',
              top: `${toolbarPosition.top}px`,
              left: `${toolbarPosition.left}px`,
              background: 'rgba(0, 0, 0, 0.7)',
              padding: '5px',
              borderRadius: '5px',
              zIndex: 10000,
              display: 'flex',
              gap: '5px',
              overflow: 'hidden',
            }}
            onMouseEnter={cancelHideToolbar}
            onMouseLeave={hideToolbarWithDelay}
          >
            <button
              className={cx('button', 'add')}
              onClick={() => toggleDiff(hoveredLine, 'added')}
            >
              +
            </button>
            <button
              className={cx('button', 'remove')}
              onClick={() => toggleDiff(hoveredLine, 'removed')}
            >
              -
            </button>
            <button
              className={cx('button', 'clear')}
              onClick={() => toggleDiff(hoveredLine, 'clear')}
            >
              x
            </button>
          </div>,
          document.body,
        )}
    </div>
  )
}

export default CustomCode
