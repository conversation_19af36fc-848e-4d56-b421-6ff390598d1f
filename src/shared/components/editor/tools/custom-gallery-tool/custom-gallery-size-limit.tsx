import React from 'react'
import { CustomGalleryToolProps } from './custom-gallery-tool.d'
import { CustomGallery } from './custom-gallery-tool'
import CustomTool from '@/shared/components/generic-tool'

const SIZE_LIMIT = 5242880

export const CustomGallerySizeLimit: React.FC<CustomGalleryToolProps.Props> = props => {
  const { data, onDataChange, readOnly } = props

  return (
    <CustomGallery
      data={data}
      onDataChange={onDataChange}
      sizeLimit={SIZE_LIMIT}
      readOnly={readOnly}
    />
  )
}

const CustomGalleryTool = CustomTool.createTool(CustomGallerySizeLimit, {
  icon: `<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="36" height="36" rx="6" fill="none"/>
  <rect x="7" y="9" width="22" height="18" rx="1" stroke="currentColor" stroke-width="2"/>
  <path d="M7 25.0002L12.6528 18.809C13.0494 18.3747 13.7332 18.3747 14.1298 18.809L17.2509 22.2273C17.6353 22.6484 18.2934 22.6633 18.6965 22.2602L28.9565 12" stroke="currentColor" stroke-width="2"/>
  <circle cx="18.5" cy="13.5" r="0.75" stroke="currentColor" stroke-width="1.5"/>
  </svg>`,
  title: 'Галерея',
})

export default CustomGalleryTool
