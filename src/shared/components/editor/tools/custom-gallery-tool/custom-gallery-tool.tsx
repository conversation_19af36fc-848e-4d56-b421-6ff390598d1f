import React, { useCallback, useEffect, useRef, useState } from 'react'
import useEmblaCarousel from 'embla-carousel-react'
import { useGalleryButtons, useGalleryDots } from '@/shared/hooks/use-gallery'
import { Button, Image } from '@/shared/ui'
import Picture from '@/shared/ui/Icon/icons/components/Picture'
import Trash from '@/shared/ui/Icon/icons/components/Trash'
import ArrowDown from '@/shared/ui/Icon/icons/components/ArrowDown'
import Ellipsis from '@/shared/ui/Icon/icons/components/Ellipsis'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import classNamesBind from 'classnames/bind'
import styles from './custom-gallery-tool.module.scss'
import { v4 as uuid } from 'uuid'
import { CustomGalleryToolProps, TSlide } from './custom-gallery-tool.d'
import { useTranslation } from 'react-i18next'
import { useEvent } from '@/shared/hooks'
import { FileUploader } from '@/shared/components/file-uploader'
import { transformToMB } from '../helpers'
import DeleteConfirmModal from '@/shared/modals/delete-confirm-modal/delete-confirm-modal'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'components__custom-image'

export const CustomGallery: React.FC<CustomGalleryToolProps.Props> = props => {
  const { data, onDataChange, sizeLimit, readOnly } = props
  const [slides, setSlides] = useState<TSlide[]>()
  const [error, setError] = useState<string>('')

  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (data.gallery?.length) {
      setSlides(data.gallery)
    }
  }, [])

  const [emblaRef, emblaApi] = useEmblaCarousel()

  const { selectedIndex, scrollSnaps, onDotButtonClick } = useGalleryDots(emblaApi)

  const { prevBtnDisabled, nextBtnDisabled, onPrevButtonClick, onNextButtonClick } =
    useGalleryButtons(emblaApi)

  const [openMenu, setOpenMenu] = useState<boolean>(false)
  const [openDelete, setOpenDelete] = useState<boolean>(false)

  const openDeleteModal = () => setOpenDelete(true)

  const { t } = useTranslation(TRANSLATION_FILE)

  const wrapper = useRef<HTMLDivElement>(null)

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return
    if (e.composedPath().indexOf(wrapper.current) === -1) {
      setOpenMenu(() => false)
    }
  }, [])

  useEvent('click', handleOutsideClick, window)

  const deleteImage = () => {
    if (!slides) return
    const newSlides = [...slides]
    newSlides?.splice(
      slides?.findIndex(i => i.id === slides[selectedIndex].id),
      1,
    )

    setSlides(newSlides)
    onDataChange({
      gallery: newSlides,
    })
  }

  const onFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.files ? e.target.files[0] : undefined
    if (value) {
      if (sizeLimit && value.size > sizeLimit) {
        setError(
          t('commons:file_error.size_limit', {
            value: transformToMB(sizeLimit),
          }),
        )
        return
      }
      const objectUrl = URL.createObjectURL(value)
      const newSlides = slides?.length ? [...slides] : []
      newSlides?.push({
        id: uuid(),
        path: objectUrl,
        file: value,
      })
      await setSlides(newSlides)
      await onDataChange({
        gallery: newSlides,
      })
      onDotButtonClick(newSlides.length - 1)
    }
  }

  const addImage = () => {
    if (inputRef.current) {
      inputRef.current.click()
    }
  }

  const menu = [
    {
      text: t('add_image'),
      name: 'add',
      handleClickMenuItem: addImage,
      icon: (
        <IconWrapper size='32' color='gray70' className={cx('menuIcon')}>
          <Picture />
        </IconWrapper>
      ),
    },
    {
      text: t('commons:delete'),
      name: 'delete',
      handleClickMenuItem: openDeleteModal,
      icon: (
        <IconWrapper size='32' color='gray70' className={cx('menuIcon')}>
          <Trash />
        </IconWrapper>
      ),
    },
  ]

  const handleOpenMenu = () => setOpenMenu(prev => !prev)

  return (
    <div className={cx('wrapper')}>
      <div className={cx('viewport')}>
        <button
          className={cx('arrowWrapper', 'prev', {
            visible: slides && slides.length > 0,
          })}
          onClick={onPrevButtonClick}
          disabled={prevBtnDisabled}
        >
          <IconWrapper
            size='40'
            color={prevBtnDisabled ? 'gray60' : 'gray70'}
            className={cx('arrow', 'prev')}
          >
            <ArrowDown />
          </IconWrapper>
        </button>
        <input
          onChange={onFileChange}
          ref={inputRef}
          type='file'
          accept='image/*'
          disabled={readOnly}
          className={cx('input')}
        />
        {!readOnly && (
          <div
            className={cx('positionWrapper', {
              visible: slides && slides.length > 0,
            })}
            ref={wrapper}
          >
            <IconWrapper
              color='gray70'
              size='14'
              className={cx('ellipsis')}
              onClick={handleOpenMenu}
            >
              <Ellipsis />
            </IconWrapper>
            <div className={cx('menu', { active: openMenu })}>
              {menu.map(i => (
                <div
                  key={`menu-item-${i.name}`}
                  className={cx('item')}
                  onClick={() => {
                    handleOpenMenu()
                    i.handleClickMenuItem()
                  }}
                >
                  {i.icon}
                  {i.text}
                </div>
              ))}
            </div>
          </div>
        )}
        <div className={cx('embla__viewport')} ref={emblaRef}>
          <div className={cx('embla__container')}>
            {slides?.length && slides?.length > 0 ? (
              slides?.map(slide => (
                <Image
                  image={{ src: slide.path }}
                  key={slide.id}
                  className={cx('embla__slide')}
                  contentClassName={cx('content')}
                />
              ))
            ) : (
              <>
                <FileUploader
                  accept='image/*'
                  onChange={onFileChange}
                  className={cx('uploader')}
                  disabled={readOnly}
                >
                  <IconWrapper className={cx('icon')} color='gray60'>
                    <Picture />
                  </IconWrapper>
                  <Button className={cx('button')} size='small' color='gray'>
                    {t('add_image')}
                  </Button>
                </FileUploader>
                {!!error && <div className={cx('error')}>{error}</div>}
              </>
            )}
          </div>
        </div>
        <button
          className={cx('arrowWrapper', 'next', {
            visible: slides && slides.length > 0,
          })}
          onClick={onNextButtonClick}
          disabled={nextBtnDisabled}
        >
          <IconWrapper
            color={nextBtnDisabled ? 'gray60' : 'gray70'}
            size='40'
            className={cx('arrow', 'next')}
          >
            <ArrowDown />
          </IconWrapper>
        </button>
      </div>

      <div className={cx('embla__dots')}>
        {slides &&
          slides?.length > 0 &&
          scrollSnaps.map((_, index) => (
            <button
              key={uuid()}
              onClick={() => onDotButtonClick(index)}
              className={cx('embla__dot', {
                'embla__dot--selected': index === selectedIndex,
              })}
            />
          ))}
      </div>
      {openDelete && (
        <DeleteConfirmModal
          open={openDelete}
          setOpen={setOpenDelete}
          title={t('image_delete')}
          description={t('image_delete_hint')}
          onConfirm={deleteImage}
        />
      )}
    </div>
  )
}
