.wrapper {
  width: 100%;

  &.errorWrapper {
    border: 2px solid var(--color-component-warn);
    border-radius: 12px;
  }
}

.image {
  width: 100%;
  height: 100%;
  overflow: visible;

  &.active {
    border: 2px solid var(--color-primary);
  }

  span {
    border-radius: 12px;
  }
}

.content {
  justify-content: center;
  align-items: center;
}

.button {
  background-color: var(--color-gray-50);
  padding: 6px 16px !important;
}

.picture-inner {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 81px !important;
  height: 64px !important;
  margin-bottom: 16px;

  svg {
    width: 81px !important;
    height: 64px !important;
  }
}

.ellipsis {
  position: absolute;
  right: 10px;
  top: 10px;
  height: 24px !important;
  width: 20px !important;
  cursor: pointer;
  border: 1px solid var(--color-gray-30);
  background-color: var(--white);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    path {
      stroke: none;
    }
  }
}

.positionWrapper {
  position: absolute;
  display: none;
  top: 10px;
  right: 10px;
  width: 10px;
  height: 10px;
  z-index: 10;

  &.visible {
    display: block;
  }

  .menu {
    background: var(--color-surface, #fff);
    border: 1px solid var(--color-gray-30, #ebeff2);
    border-radius: 8px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
    display: none;
    padding: 6px 1px;
    position: absolute;
    right: 11px;
    top: 40px;

    width: max-content;
    z-index: 12;
    .item {
      display: flex;
      align-items: center;
      gap: 12px;
      background: var(--color-surface, #fff);
      color: var(--color-gray-80, #5c6585);
      cursor: pointer;
      font-family: TT Norms Pro;
      font-size: 14px;
      font-style: normal;
      font-weight: 450;
      line-height: 18px;
      padding: 8px 16px;

      transition: var(--transition);
      &:hover {
        background: var(--color-gray-40, #f0f3f7);

        transition: var(--transition);
      }

      .menuIcon {
        svg {
          rect {
            fill: var(--color-gray-40) !important;
          }

          path {
            stroke: none;
          }
        }
      }
    }
    &.active {
      display: block;
    }
  }
}

.viewport {
  display: flex;
  align-items: center;
  position: relative;
  height: 500px;

  @media (max-width: 1024px) {
    height: 300px;
  }
}

.input {
  display: none;
}

.arrowWrapper {
  position: absolute;
  background-color: transparent;
  top: 50%;
  padding: 0;
  display: none;

  &.visible {
    display: block;
  }

  &.prev {
    left: -32px;

    svg {
      transform: rotate(90deg);
    }
  }

  &.next {
    right: -32px;

    svg {
      transform: rotate(-90deg);
    }
  }
}

.embla {
  max-width: 48rem;
  margin: auto;

  &__viewport {
    overflow: hidden;
    width: 100%;
    height: 100%;
  }

  &__container {
    display: flex;
    touch-action: pan-y pinch-zoom;
    margin-left: calc(1rem * -1);
    max-height: 500px;
    height: 100%;
  }

  &__slide {
    transform: translate3d(0, 0, 0);
    flex: 0 0 100%;
    min-width: 0;
    padding-left: 1rem;
  }

  &__controls {
    display: grid;
    grid-template-columns: auto 1fr;
    justify-content: space-between;
    gap: 1.2rem;
    margin-top: 1.8rem;
  }

  &__buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.6rem;
    align-items: center;
  }

  &__button {
    -webkit-tap-highlight-color: rgba(var(--text-high-contrast-rgb-value), 0.5);
    -webkit-appearance: none;
    appearance: none;
    background-color: transparent;
    touch-action: manipulation;
    display: inline-flex;
    text-decoration: none;
    cursor: pointer;
    border: 0;
    padding: 0;
    margin: 0;
    box-shadow: inset 0 0 0 0.2rem var(--detail-medium-contrast);
    width: 3.6rem;
    height: 3.6rem;
    z-index: 1;
    border-radius: 50%;
    color: var(--text-body);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__dots {
    margin-top: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    height: 24px;
  }

  &__dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    padding: 2px;
    cursor: pointer;
    background-color: var(--color-gray-70);
    position: relative;

    &--selected {
      &::after {
        position: absolute;
        content: '';
        display: flex;
        border-radius: 50px;
        left: -4px;
        top: -4px;
        width: 12px;
        height: 12px;
        border: 2px solid var(--color-gray-70);
      }
    }
  }
}

.uploader {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 90%;
  margin: 0 auto;
  border: 2px solid var(--color-gray-40);
  border-radius: 12px;
}

.error {
  max-width: 60%;
  text-align: center;
  color: var(--color-component-warn);
  font: var(--font-text-1-normal);
  margin-top: 8px;
}
