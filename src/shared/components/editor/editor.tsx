import { useCallback, useState, Suspense, lazy, useMemo } from 'react'

import { EditorProps } from './editor.d'

import classNamesBind from 'classnames/bind'
import styles from './editor.module.scss'

import './editor.css'
import { useEditorLocale } from '@/shared/hooks/use-editor-locale/use-editor-locale'
import { useStableEditor } from '@/shared/hooks/use-stable-editor'
import { Button } from '@/shared/ui'
import { useTranslation } from 'react-i18next'
import { useAppDispatch, useAppSelector } from '@/store'
import { handleNextStep, selectDisableNext, selectLastStep } from '@/store/slices/user-course-slice'
import { CompleteModal } from '@/shared/modals/editors/complete'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { GOALS } from '@/shared/constants'

const cx = classNamesBind.bind(styles)

const LazyReactEditorJS = lazy(() => import('./lazy-editor'))

export const Editor: React.FC<EditorProps.Props> = ({
  data,
  setData,
  tools,
  onSubmit,
  onChange,
  stepId,
  readOnly,
  isUserSide,
  isLoading,
}) => {
  const dispatch = useAppDispatch()
  const isLastStep = useAppSelector(selectLastStep)
  const disableNext = useAppSelector(selectDisableNext)
  const [disabled, setDisabled] = useState<boolean>(false)
  const { currentConfig: i18nConfig } = useEditorLocale()
  const { t } = useTranslation()
  const analytics = useAnalytics()

  const editorId = useMemo(() => `react-editor-js-${stepId || 'default'}`, [stepId])

  const {
    editorCore,
    handleInitialize,
    handleSave: stableHandleSave,
  } = useStableEditor({
    stepId,
  })

  const handleReady = async () => {
    if (!editorCore.current) return

    try {
      const editor = editorCore.current.dangerouslyLowLevelInstance

      if (!editor) {
        console.warn('EditorJS instance not found')
        return
      }

      const [{ default: UndoModule }, { default: DragDropModule }] = await Promise.all([
        import('editorjs-undo'),
        import('editorjs-drag-drop'),
      ])

      new UndoModule({ editor })
      new DragDropModule(editor)
    } catch (error) {
      console.error('Error initializing EditorJS modules:', error)
    }
  }

  const handleSave = useCallback(async () => {
    const savedData = await stableHandleSave()
    if (savedData) {
      setData(savedData)
    }
  }, [stableHandleSave, setData])

  const handleNext = () => dispatch(handleNextStep())

  const onNext = async () => {
    setDisabled(true)
    await handleNext()
    setDisabled(false)
    analytics.event(GOALS['course-next-theme-item'].name, { stepId })
  }

  return (
    <>
      <div className={cx('wrapper')} key={stepId}>
        <Suspense fallback={null}>
          <LazyReactEditorJS
            holder={editorId}
            onInitialize={handleInitialize}
            tools={tools}
            i18n={i18nConfig}
            onReady={handleReady}
            onChange={(api, event) => {
              onChange?.(api, event)
              handleSave()
            }}
            defaultValue={data}
            readOnly={readOnly}
          />
        </Suspense>
      </div>
      {!readOnly && !isUserSide && (
        <Button
          onClick={onSubmit}
          disabled={isLoading}
          size='big'
          color='green'
          className={cx('button')}
        >
          {t('commons:save')}
        </Button>
      )}
      {isUserSide &&
        (isLastStep ? (
          <CompleteModal isUserSide={isUserSide} />
        ) : (
          <Button
            disabled={isUserSide && (disableNext || disabled)}
            onClick={onNext}
            size='big'
            color='green'
            className={cx('button')}
          >
            {t('commons:next')}
          </Button>
        ))}
    </>
  )
}
