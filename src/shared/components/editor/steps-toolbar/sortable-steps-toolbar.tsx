import { DndContext, Drag<PERSON>verlay, closestCenter } from '@dnd-kit/core'
import { SortableContext, rectSortingStrategy } from '@dnd-kit/sortable'
import { Step } from './steps-toolbar'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import Article from '@/shared/ui/Icon/icons/components/Article'
import Slide from '@/shared/ui/Icon/icons/components/Slide'
import Quiz from '@/shared/ui/Icon/icons/components/Quiz'
import Video from '@/shared/ui/Icon/icons/components/Video'
import Import from '@/shared/ui/Icon/icons/components/Import'
import ScormIcon from '@/shared/ui/Icon/icons/components/ScormIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { ConfirmReplaceStepModal } from './components/confirm-replace-step-modal'
import classNamesBind from 'classnames/bind'
import styles from './steps-toolbar.module.scss'
import { CSS } from '@dnd-kit/utilities'
import { useSortable } from '@dnd-kit/sortable'
import { useSwapSteps } from './use-swap-steps'

const cx = classNamesBind.bind(styles)

// Функция для рендеринга иконки по типу шага
const renderStepIcon = (type: string) => {
  switch (type) {
    case 'article':
      return <Article />
    case 'slide':
      return <Slide />
    case 'quiz':
      return <Quiz />
    case 'video':
      return <Video />
    case 'import':
      return <Import />
    case 'scorm':
      return <ScormIcon />
    default:
      return <Slide />
  }
}

type Props = {
  steps: Step[]
  activeStep?: Step
  onStepClick: (item: Step) => Promise<void>
  themeId?: UUID
  setActive: (step: Step) => void
  openModal: () => void
}

export default function SortableStepsToolbar(props: Props) {
  const { steps, activeStep, onStepClick, themeId, openModal } = props

  const swap = useSwapSteps({ steps, themeId, onStepClick })

  if (!swap) return null

  const {
    sensors,
    handleDragEnd,
    handleDragStart,
    handleReplaceSlideCancel,
    handleReplaceSlideConfirm,
    isReplaceModalOpen,
    activeId,
    activeStep: activeDragStep,
  } = swap

  return (
    <>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
        onDragStart={handleDragStart}
      >
        <SortableContext items={steps} strategy={rectSortingStrategy}>
          {steps.map((step, index) => (
            <StepBlock key={step.id} step={step} index={index} activeStep={activeStep} />
          ))}

          <DragOverlay>
            {activeId && activeDragStep && (
              <div className={cx('step')} key={activeDragStep.id}>
                <IconWrapper
                  size={'40'}
                  color='white'
                  className={cx('block', { active: activeStep?.id === activeDragStep.id })}
                >
                  {renderStepIcon(activeDragStep.type)}
                </IconWrapper>
              </div>
            )}
          </DragOverlay>
        </SortableContext>
      </DndContext>

      <div className={cx('step')}>
        <IconWrapper size='40' color='white' className={cx('block')} onClick={openModal}>
          <PlusIcon />
        </IconWrapper>
      </div>

      <ConfirmReplaceStepModal
        active={isReplaceModalOpen}
        onClose={handleReplaceSlideCancel}
        onConfirm={handleReplaceSlideConfirm}
      />
    </>
  )
}

const StepBlock = ({
  step,
  index,
  activeStep,
}: {
  step: Step
  index: number
  activeStep?: Step
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: step.id,
    data: step,
  })

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? '100' : 'auto',
    opacity: isDragging ? 0.3 : 1,
  }

  return (
    <div ref={setNodeRef} style={style}>
      <div className={cx('step')} key={step.id} {...attributes} {...listeners}>
        <div className={cx('label')}>{index + 1}</div>
        <IconWrapper
          size={'40'}
          color='white'
          className={cx('block', { active: activeStep?.id === step.id })}
        >
          {renderStepIcon(step.type)}
        </IconWrapper>
      </div>
    </div>
  )
}
