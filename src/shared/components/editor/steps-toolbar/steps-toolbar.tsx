/* eslint-disable @typescript-eslint/ban-ts-comment */
import classNamesBind from 'classnames/bind'
import styles from './steps-toolbar.module.scss'
import { StepsToolbarProps } from './steps-toolbar.d'
import { Button } from '@/shared/ui'
import Article from '@/shared/ui/Icon/icons/components/Article'
import Slide from '@/shared/ui/Icon/icons/components/Slide'
import Quiz from '@/shared/ui/Icon/icons/components/Quiz'
import Video from '@/shared/ui/Icon/icons/components/Video'
import Import from '@/shared/ui/Icon/icons/components/Import'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { lazy, Suspense, useState } from 'react'
import { NewStepModal } from '@/shared/modals/new-step-modal'
import { v4 as uuid } from 'uuid'
import { useTranslation } from 'react-i18next'
import ThemeStepsWarningModal from '@/shared/modals/theme-steps-warning/theme-steps-warning'
import { IIconSize, TIconColor } from '@/shared/ui/Icon/icon.d'
import { useSearchParams } from 'react-router-dom'

const cx = classNamesBind.bind(styles)

export type TMinifiedStepType = 'article' | 'slide' | 'quiz' | 'video' | 'import'

// Функция для рендеринга иконок шагов
const renderStepIcon = (
  type: TMinifiedStepType,
  size: IIconSize,
  color: TIconColor,
  className?: string,
  onClick?: () => void,
) => {
  const iconComponent = () => {
    switch (type) {
      case 'article':
        return <Article />
      case 'slide':
        return <Slide />
      case 'quiz':
        return <Quiz />
      case 'video':
        return <Video />
      case 'import':
        return <Import />
      default:
        return <Slide />
    }
  }

  return (
    <IconWrapper size={size} color={color} className={className} onClick={onClick}>
      {iconComponent()}
    </IconWrapper>
  )
}

const LazySortableSteps = lazy(() => import('./sortable-steps-toolbar'))

export type Step = {
  id: UUID
  type: TMinifiedStepType
  isNew?: boolean
}

export const StepsToolbar: React.FC<StepsToolbarProps.Props> = ({
  steps,
  activeStep,
  setActive,
  setSteps,
  themeId,
  onCreateFirstStepClick,
  allowSwap = false,
  isDisabled = false,
  isLoading = false,
}) => {
  const [searchParams, setSearchParams] = useSearchParams()
  const [open, setOpen] = useState<boolean>(Boolean(searchParams.get('openStepModal')))
  const [openWarning, setOpenWarning] = useState<boolean>(false)
  const [currentCallback, setCurrentCallback] = useState<() => void>()
  const { t } = useTranslation('pages__create-theme')

  const openModal = () => setOpen(true)

  const handleChoose = async (type: 'article' | 'slide' | 'quiz' | 'video' | 'import') => {
    const cb = () => {
      if (searchParams.has('openStepModal')) {
        searchParams.delete('openStepModal')
        setSearchParams(searchParams)
      }

      const id = uuid()
      setSteps &&
        setSteps(prev =>
          prev
            ? [
                ...prev,
                {
                  id,
                  type,
                  isNew: true,
                },
              ]
            : [
                {
                  id,
                  type,
                  isNew: true,
                },
              ],
        )
      setOpen(false)
      setActive({ id, type, isNew: true })
    }

    if (steps && steps?.length > 0) {
      await setCurrentCallback(() => cb)
      await setOpenWarning(true)
    } else {
      cb()
    }
  }

  const onStepClick = async (item: Step) => {
    const cb = () => setActive(item)
    await setCurrentCallback(() => cb)
    await setOpenWarning(true)
  }

  return (
    <div className={cx('wrapper')}>
      {steps && steps.length > 0 ? (
        !allowSwap ? (
          <>
            {steps?.map((item, index) => (
              <div className={cx('step')} key={item.id}>
                <div className={cx('label')}>{index + 1}</div>
                {renderStepIcon(
                  item.type,
                  '40' as IIconSize,
                  'white' as TIconColor,
                  cx('block', { active: activeStep?.id === item.id }),
                  () => onStepClick(item),
                )}
              </div>
            ))}
            <div className={cx('step')}>
              <IconWrapper size={'40'} color='white' className={cx('block')} onClick={openModal}>
                <PlusIcon />
              </IconWrapper>
            </div>
          </>
        ) : (
          <>
            <Suspense fallback={<div></div>}>
              <LazySortableSteps
                steps={steps}
                activeStep={activeStep}
                onStepClick={onStepClick}
                themeId={themeId}
                setActive={setActive}
                openModal={openModal}
              />
            </Suspense>
          </>
        )
      ) : (
        <Button
          color='darkGray'
          size='big'
          onClick={themeId ? openModal : onCreateFirstStepClick}
          disabled={isDisabled}
          loading={isLoading}
        >
          {t('create_first_step')}
        </Button>
      )}

      <NewStepModal open={open} setOpen={setOpen} onChoose={handleChoose} />
      <ThemeStepsWarningModal
        open={openWarning}
        setOpen={setOpenWarning}
        onConfirm={currentCallback}
      />
    </div>
  )
}
