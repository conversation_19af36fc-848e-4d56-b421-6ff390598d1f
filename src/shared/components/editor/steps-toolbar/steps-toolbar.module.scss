.block {
    background-color: var(--color-gray-60);
    border-radius: 8px;
    display: block;
    cursor: pointer;

    &.active {
        cursor: initial;
        background-color: var(--color-primary);
    }
}

.wrapper {
    margin-top: 24px;
    display: flex;
    gap: 8px;
    align-items: end;
    flex-wrap: wrap;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.label {
    font-size: 14px;
    line-height: 18px;
    color: var(--gray-gray-70, #8e97af);
}