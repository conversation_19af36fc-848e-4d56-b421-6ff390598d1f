import {
  DragEndEvent,
  DragStartEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import { sortableKeyboardCoordinates } from '@dnd-kit/sortable'
import { Step } from './steps-toolbar'
import { useCallback, useEffect, useState } from 'react'
import { themeStepsApi } from '@/entities/themeCourse/model/api'
import { TStepsSwapRequest } from '@/entities/themeCourse/model/types'

type TSwapStepsProps = {
  steps: Step[] | undefined
  themeId?: UUID
  onStepClick: (item: Step) => Promise<void>
}

export const useSwapSteps = ({ steps, themeId, onStepClick }: TSwapStepsProps) => {
  const [activeStep, setActiveStep] = useState<Step | null | undefined>(null)
  const [activeId, setActiveId] = useState<number | string | null>(null)
  const [isReplaceModalOpen, setIsReplaceModalOpen] = useState(false)
  const [data, setData] = useState<TStepsSwapRequest | null>()

  const [swapSteps] = themeStepsApi.useSwapStepsMutation()

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  useEffect(() => {
    if (!steps) return
    setActiveStep(steps.find(step => step.id === activeId))
  }, [activeId, steps])

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id)
  }

  const handleDragEnd = (event: DragEndEvent) => {
    setActiveId(null)
    const { active, over } = event

    if (!over) return

    if (active.id !== over.id && steps) {
      setIsReplaceModalOpen(true)
      const oldIndex = steps.findIndex(slide => slide.id === active.id) || 0
      const newIndex = steps.findIndex(slide => slide.id === over.id) || 0

      setData({
        step_from: {
          object_id: active.id as UUID,
          order_id: oldIndex + 1,
        },
        step_to: {
          object_id: over.id as UUID,
          order_id: newIndex + 1,
        },
      })
    } else {
      const { id, type, isNew } = active.data.current as Step

      onStepClick({
        id: id,
        type: type,
        isNew: isNew,
      })
    }
  }

  const handleReplaceSlideConfirm = useCallback(() => {
    if (!steps || !themeId || !data) return

    return swapSteps({
      themeId: themeId,
      body: data,
    })
      .unwrap()
      .finally(() => setIsReplaceModalOpen(false))
  }, [steps, themeId, data])

  const handleReplaceSlideCancel = useCallback(() => {
    setData(null)
    setIsReplaceModalOpen(false)
  }, [])

  return {
    sensors,
    handleDragEnd,
    handleDragStart,
    activeId,
    activeStep,
    handleReplaceSlideCancel,
    handleReplaceSlideConfirm,
    isReplaceModalOpen,
  }
}
