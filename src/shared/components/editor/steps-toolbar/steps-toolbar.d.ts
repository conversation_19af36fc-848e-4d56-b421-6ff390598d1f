import { OutputData } from '@editorjs/editorjs'
import { Step } from './steps-toolbar'

export declare namespace StepsToolbarProps {
  interface Own {
    steps?: Step[]
    setSteps?: React.Dispatch<React.SetStateAction<Step[]>>
    activeStep?: Step
    setActive: (item: Step) => void
    onSave?: (data: OutputData) => void
    allowSwap?: boolean
    themeId?: UUID
    onCreateFirstStepClick?: () => void
    isDisabled?: boolean
    isLoading?: boolean
  }

  type Props = Own
}

export {}
