// @ts-expect-error: Workaround for CJS
import { createReactEditorJS } from 'react-editor-js/dist/react-editor-js.cjs'
import type { WrapperProps, EditorCore } from '@react-editor-js/core'
import React, { useEffect, useRef, useCallback } from 'react'

const ReactEditorJS = createReactEditorJS()

const LazyEditor: React.FC<WrapperProps> = props => {
  const containerRef = useRef<HTMLDivElement>(null)
  const { holder, onInitialize, ...restProps } = props

  useEffect(() => {
    if (holder && containerRef.current) {
      containerRef.current.id = holder
    }
  }, [holder])

  const handleInitialize = useCallback(
    (instance: EditorCore) => {
      onInitialize?.(instance)
    },
    [onInitialize],
  )

  if (holder) {
    return (
      <div ref={containerRef} id={holder}>
        <ReactEditorJS {...restProps} holder={holder} onInitialize={handleInitialize} />
      </div>
    )
  }

  return <ReactEditorJS {...props} onInitialize={handleInitialize} />
}

export default LazyEditor
