import { ReactNode, useEffect } from 'react'
import { SUPPORTED_LANGUAGES } from '../constants/supportedLanguages'
import { useAppDispatch } from '@/store'
import { useTranslation } from 'react-i18next'
import { setCurrentLanguage } from '@/store/slices/language'

export const LanguageWatcher = (props: { children: ReactNode }) => {
  const dispatch = useAppDispatch()
  const { i18n } = useTranslation()

  useEffect(() => {
    if (!i18n.isInitialized) return

    const initialCurrentLanguage = SUPPORTED_LANGUAGES.filter(
      language => language.code === i18n.language,
    )[0]

    dispatch(setCurrentLanguage(initialCurrentLanguage))
  }, [dispatch, i18n.isInitialized, i18n.language])

  return <>{props.children}</>
}
