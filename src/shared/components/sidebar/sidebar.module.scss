.wrapper {

  background-color: var(--color-surface);
  background-color: var(--color-surface);
  display: flex;
  flex-direction: column;
  height: 100dvh;
  width: var(--sidebar-width);
}

.top {
  width: 100%;
}

.logo-wrapper {
  align-items: center;
  display: flex;
  min-height: 64px;
  padding: 15px 20px;
  width: 100%;

  .logo {
    max-height: 36px;
    max-width: 100%;
  }
}

.links {
  overflow-y: auto;
}

.sections {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.bottom {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: auto;
}

.role-switch {
  margin-bottom: 8px;
}
