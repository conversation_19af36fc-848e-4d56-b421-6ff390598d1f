import { FC, useLayoutEffect } from 'react'
import classNamesBind from 'classnames/bind'
import { SidebarProps } from './sidebar.d'
import styles from './sidebar.module.scss'
import { Logo } from '@/shared/components'
import { useAppDispatch, useAppSelector } from '@/store'
import { SidebarLinks } from '../sidebar-links'
import { SidebarRoleSwitcher } from '../sidebar-role-switcher'
import { SidebarUser } from '../sidebar-user'
import { LangSwitcher } from '../lang-switcher/lang-switcher'
import { selectCreateContent, setCreateContent } from '@/store/slices/sidebar-slice'

const cx = classNamesBind.bind(styles)

export const Sidebar: FC<SidebarProps.Props> = ({ content }) => {
  const createContent = useAppSelector(selectCreateContent)
  const dispatch = useAppDispatch()

  useLayoutEffect(() => {
    if (!createContent) dispatch(setCreateContent(DefaultSidebarContent))
  }, [])

  return (
    <div className={cx('top', 'wrapper')}>
      <div className={cx('logo-wrapper')}>
        <Logo />
      </div>
      {createContent?.()}
      {content}
    </div>
  )
}

export const DefaultSidebarContent = () => (
  <>
    <div className={cx('links')}>
      <SidebarLinks />
    </div>
    <div className={cx('bottom')}>
      <SidebarRoleSwitcher />
      <div>
        <SidebarUser />
        {import.meta.env.MODE !== 'production' && <LangSwitcher />}
      </div>
    </div>
  </>
)
