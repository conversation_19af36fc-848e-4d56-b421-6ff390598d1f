import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { SoundProps } from './sound.d'
import { useFunctionDebounce } from '@/shared/hooks'

import PauseCircleIcon from '@/shared/ui/Icon/icons/components/PauseCircleIcon'
import PlayCircleIcon from '@/shared/ui/Icon/icons/components/PlayCircleIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { Consumer, ISoundContext, SoundContext } from './context'

// Функция для рендеринга иконок воспроизведения
const renderPlayIcon = (playing: boolean, onClick: () => void) => {
  return (
    <IconWrapper color='primary' onClick={onClick} size='32'>
      {playing ? <PauseCircleIcon /> : <PlayCircleIcon />}
    </IconWrapper>
  )
}

// TODO refactoring
const Sound = Object.assign(
  (props: React.PropsWithChildren<SoundProps.Props>) => {
    const playerRef = useRef<HTMLAudioElement | null>(null)
    const player = playerRef.current
    const {
      src,
      active,
      startPosition,
      autoplay = false,
      updateInverval = 10,
      children,
      onUpdate,
      onEnd,
      // onStart, // TODO
      onLoad,
    } = props

    const [playing, setPlaying] = useState(false)

    const onUpdateDebounce = useFunctionDebounce(() => {
      if (!player) return

      const time = player.currentTime
      const { duration } = player

      if (!time || !duration) return

      if (onUpdate) onUpdate(time, duration)
    }, updateInverval)

    useEffect(() => {
      if (!player) return

      if (startPosition) player.currentTime = startPosition
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [])

    const play = useCallback(() => {
      try {
        player &&
          player.play().catch(err => {
            console.error(err)
          })
        // catch error in IE
      } catch (err) {
        console.log(err)
      }
    }, [player])

    const pause = useCallback(() => {
      player?.pause()
    }, [player])

    useEffect(() => {
      if (!active) {
        player?.pause()
        setPlaying(false)
      }
    }, [active, player])

    const onPlayClick = () => {
      setPlaying(ac => {
        if (ac) {
          pause()
        } else {
          play()
        }
        return !ac
      })
    }

    const _onEnd = () => {
      setPlaying(false)
      onEnd && onEnd()
    }

    const setCurrentTime = useCallback(
      (position?: number | null) => {
        if (!player || !position) return

        player.currentTime = (position / 100) * player.duration
      },
      [player],
    )

    const soundContext: ISoundContext = useMemo(
      () => ({
        setCurrentTime,
        play,
        pause,
      }),
      [setCurrentTime, play, pause],
    )

    return (
      <>
        <audio
          ref={playerRef}
          src={src}
          onTimeUpdate={onUpdateDebounce}
          onEnded={_onEnd}
          onLoad={onLoad}
          autoPlay={autoplay}
        />
        {renderPlayIcon(playing, onPlayClick)}
        <SoundContext.Provider value={soundContext}>{children}</SoundContext.Provider>
      </>
    )
  },
  {
    Consumer,
  },
)

export default Sound
