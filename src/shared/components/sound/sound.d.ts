/* eslint-disable @typescript-eslint/no-explicit-any */
export declare namespace SoundProps {
  interface Own {
    children?: any;
    src?: string;
    active?: boolean;
    startPosition?: number;
    autoplay?: boolean;
    updateInverval?: number;
    /**
     * [1-100]
     */
    position?: number;
    onUpdate?(position: number, duration: number): void;
    onEnd?(): void;
    onStart?(): void;
    onLoad?(): void;
  }

  type Props = Own;
}

export {};
