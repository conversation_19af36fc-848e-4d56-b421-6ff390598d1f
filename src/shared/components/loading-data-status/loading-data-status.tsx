import { QueryStatus } from '@reduxjs/toolkit/query'
import { FC } from 'react'
import styles from './loading-data-status.module.scss'
import classNamesBind from 'classnames/bind'
import { Button, Loader } from '@/shared/ui'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

type Props = {
  fetchStatus: QueryStatus
  data: unknown
  dataLength?: number
  texts?: {
    error?: string
    create?: string
    empty?: string
  }
  showCreateHandler?: boolean
  createHandler?: () => void
  wrapperClass?: string
}

export const LoadingDataStatus: FC<Props> = ({
  data,
  fetchStatus,
  dataLength,
  createHandler,
  texts,
  showCreateHandler = true,
  wrapperClass,
}) => {
  const { t } = useTranslation()

  return (
    <>
      {!data && fetchStatus === QueryStatus.pending && (
        <div className={cx('plug__wrapper')}>
          <Loader size='56' />
        </div>
      )}
      {!data && fetchStatus === QueryStatus.rejected && (
        <div className={cx('plug__wrapper')}>
          <Loader error={true} loading={false} />
          <h2>{texts?.error ?? t('commons:loading_status.error')}</h2>
        </div>
      )}
      {data && dataLength === 0 && fetchStatus === QueryStatus.fulfilled && (
        <div className={cx('empty-wrapper', wrapperClass)}>
          <span>{texts?.empty ?? t('commons:loading_status.empty')}</span>
          {showCreateHandler && (
            <Button onClick={createHandler}>
              {texts?.create ?? t('commons:loading_status.create')}
            </Button>
          )}
        </div>
      )}
    </>
  )
}
