import { FC, useEffect, useRef, useState } from 'react'
import styles from './notification-list.module.scss'
import classNamesBind from 'classnames/bind'
import { NotificationListProps } from './notification-list.d'
import { useNotification } from '@/shared/contexts/notifications'
import { v4 as uuid } from 'uuid'
import { INotification } from '@/shared/types/store/notification'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const Notification: FC<NotificationListProps.NotificationItem> = props => {
  const { item } = props

  const { t } = useTranslation()
  const interval = useRef<ReturnType<typeof setTimeout> | null>(null)

  const { close, add } = useNotification()

  const handleClose = (id: UUID | string) => {
    // Для анимации
    setIsInit(false)

    setTimeout(() => {
      close(id)
    }, 200)
  }

  // Для анимации
  const [isInit, setIsInit] = useState(false)

  useEffect(() => {
    setIsInit(true)
  }, [])

  useEffect(() => {
    interval.current = setTimeout(() => {
      handleClose(item.id)
    }, 15000)
  }, [])

  const handleMouseEnter = () => {
    interval.current && clearTimeout(interval.current)
    interval.current = null
  }

  const handleMouseLeave = () => {
    interval.current = setTimeout(() => {
      handleClose(item.id)
    }, 15000)
  }

  const onCopyClick = (detail: string) => {
    try {
      navigator.clipboard.writeText(detail)
      add({
        id: uuid(),
        status: 'success',
        message: `${t('commons:details_success_copy')} :)`,
      } as INotification)
    } catch (error) {
      add({
        id: uuid(),
        status: 'error',
        message: `${t('commons:details_fail_copy')} :(`,
      } as INotification)
    }
  }

  return (
    <div
      className={cx('item', `item--${item.status}`, {
        init: isInit,
      })}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {t(item.message)}
      <button className={cx('button')} onClick={() => handleClose(item.id)}>
        {t('commons:ok')}
      </button>
      {item.status === 'error' && !item?.withoutAdditionalInfo && (
        <>
          <div>{t('commons:details_in_console')}</div>
          <button className={cx('button')} onClick={() => onCopyClick(item.detail || '')}>
            {t('commons:copy')}
          </button>
        </>
      )}
    </div>
  )
}

export const NotificationList: FC<NotificationListProps.Props> = props => {
  const { className } = props

  const { items } = useNotification()

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('inner')}>
        {items && items.map(i => <Notification key={i.id} item={i} />)}
      </div>
    </div>
  )
}
