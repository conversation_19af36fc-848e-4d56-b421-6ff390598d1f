.wrapper {
  align-items: end;

  display: flex;
  height: 100dvh;
  justify-content: end;
  left: 0;
  padding: 32px;

  pointer-events: none;
  position: fixed;
  top: 0;

  width: 100vw;
  z-index: 1000000;

  .inner {
    max-width: 400px;
    width: 100%;
  }

  @media (max-width: 1024px) {
    align-items: start;
  }
}

.item {
  align-items: center;

  background: var(--snackbars-success, #3dbc87);
  border-radius: 8px;

  color: var(--color-surface, #fff);

  display: grid;
  font: var(--font-caption-1-normal);
  grid-gap: 0 16px;
  grid-template-columns: 1fr auto;

  margin-top: 8px;
  padding: 8px 16px;

  pointer-events: auto;

  position: relative;
  right: -500px;

  // Для анимации
  transition: var(--transition);

  white-space: pre-line;
  width: 100%;

  .button {
    appearance: none;

    background: transparent;
    border: 0;
    border-radius: 4px;

    color: var(--color-surface, #fff);
    cursor: pointer;
    font: var(--font-caption-1-demibold);

    justify-self: end;
    outline: none;
    padding: 4px 8px;

    transition: var(--transition);

    &:hover {
      background: rgba(255, 255, 255, 0.2);

      transition: var(--transition);
    }
  }

  &.init {
    right: 0;
  }

  &--error {
    background: var(--snackbars-error, #ff708b);
  }

  &--warning {
    background: var(--snackbars-warning, #ffba69);
  }

  &--neutral {
    background: var(--snackbars-neutral, #8e97af);
  }
}
