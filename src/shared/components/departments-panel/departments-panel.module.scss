// @use "mixins/icons";

.wrapper {
  align-items: center;

  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 12px;
  bottom: 32px;
  box-shadow:
    0 0 88px -4px rgba(24, 39, 75, 0.12),
    0 0 28px -6px rgba(24, 39, 75, 0.12);

  display: flex;
  justify-content: space-between;
  max-width: min(calc(100vw - 255px - 30px), calc(var(--page-container) - 64px));
  padding: 14px 24px 14px 16px;

  position: fixed;
  width: 100%;

  .counter {
    color: var(--color-primary, #3dbc87);
    font: var(--font-text-2-normal);
  }

  .buttons {
    align-items: center;
    display: flex;
    gap: 24px;

    .button {
      align-items: center;

      color: var(--color-gray-80, #5c6585);

      cursor: pointer;
      display: flex;
      font: var(--font-text-2-normal);
      gap: 8px;

      transition: var(--transition);

      svg {
        // @include icons.color(var(--color-gray-80, #5c6585));

        transition: var(--transition);
      }

      &:hover {
        color: var(--color-primary, #3dbc87);

        transition: var(--transition);

        svg {
          // @include icons.color(var(--color-primary, #3dbc87));

          transition: var(--transition);
        }
      }

      &.delete {
        &:hover {
          color: var(--color-statistics-bad-text, #ff4b60);

          svg {
            // @include icons.color(var(--color-statistics-bad-text, #ff4b60));
          }
        }
      }

      &.disabled {
        opacity: 0.7;

        pointer-events: none;
      }
    }
  }

  .cancel {
    align-items: center;

    color: var(--color-component-warn, #ff5f71);

    cursor: pointer;
    display: flex;
    font: var(--font-text-2-normal);
    gap: 8px;

    // @include icons.color(var(--color-component-warn, #ff5f71));
    transition: var(--transition);

    &:hover {
      color: var(--color-component-warn-hover, #ff5f71);
      // @include icons.color(var(--color-component-warn-hover, #ff5f71));
      transition: var(--transition);
    }
  }
}
