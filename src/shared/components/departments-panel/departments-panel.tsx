import React, { FC, useState } from 'react'
import styles from './departments-panel.module.scss'
import classNamesBind from 'classnames/bind'
import { DepartmentsPanelProps } from './departments-panel.d'
import { useDepartments } from '@/entities/department'
import TrashBoldIcon from '@/shared/ui/Icon/icons/components/TrashBoldIcon'
import FailIcon from '@/shared/ui/Icon/icons/components/FailIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { departmentAPI } from 'entities/department'
import { DeleteModal } from '@/shared/modals/delete-modal'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const DepartmentsPanel: FC<DepartmentsPanelProps.Props> = props => {
  const { className } = props

  const { checkedDeparments, setCheckedDeparments } = useDepartments({ isSkipDepartmentsReq: true })

  const [deleteDepartments, { isLoading: isDeleteLoading }] =
    departmentAPI.useDeleteDepartmentsMutation()

  const departmentsLength = checkedDeparments.length

  const { t } = useTranslation()
  const handleReset = () => setCheckedDeparments([])

  const [deletedIDs, setDeletedIDs] = useState<UUID[] | null>(null)
  const [openDeleteModal, setOpenDeleteModal] = useState(false)

  const onDeleteClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, IDs: UUID[]) => {
    e.stopPropagation()

    setDeletedIDs(IDs)
    setOpenDeleteModal(true)
  }

  const handleDeleteDeparments = async (IDs?: UUID[] | null) => {
    if (IDs && !!IDs.length) {
      if (!departmentsLength) return

      await deleteDepartments(checkedDeparments)
        .unwrap()
        .then(() => {
          setCheckedDeparments([])
        })
    }

    setDeletedIDs(null)
    setOpenDeleteModal(false)
  }

  if (!departmentsLength) return <></>

  const counterText = t('departments.choosed', { count: departmentsLength })

  return (
    <>
      <div className={cx('wrapper', className)}>
        <div className={cx('counter')}>{counterText}</div>
        <div className={cx('buttons')}>
          <div
            className={cx('button', 'delete', {
              disabled: isDeleteLoading,
            })}
            onClick={e => onDeleteClick(e, checkedDeparments)}
          >
            <IconWrapper>
              <TrashBoldIcon />
            </IconWrapper>
            {t('commons:delete_department')}
          </div>
          <div className={cx('cancel')} onClick={handleReset}>
            <IconWrapper>
              <FailIcon />
            </IconWrapper>
            {t('commons:cancel')}
          </div>
        </div>
      </div>

      {deletedIDs && openDeleteModal && (
        <DeleteModal
          ids={deletedIDs}
          onCloseIDs={handleDeleteDeparments}
          title=''
          active={openDeleteModal}
          setActive={setOpenDeleteModal}
        />
      )}
    </>
  )
}
