.wrapper {
  color: #5c6585;

  & .yellow,
  & .orange {
    display: none;
  }

  &.activePhishing {
    & .yellow,
    & .orange {
      display: flex;
    }

    & .red {
      display: none;
    }
  }
}

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.tabs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tab {
  font: var(--font-caption-1-demibold);
  color: var(--color-gray-70);
  cursor: pointer;

  &.active {
    cursor: initial;
    color: var(--color-gray-90);
  }
}

.list {
  display: inline-grid;
  gap: 8px;
  margin-bottom: 32px;
}

.line {
  height: 12px;
  border-radius: 4px;
  min-width: 4px;

  &.gray {
    background-color: var(--color-gray-50);
  }

  &.purple {
    background-color: var(--color-statistics-complementary);
  }

  &.yellow {
    background-color: var(--color-statistics-warning);
  }

  &.orange {
    background-color: var(--color-statistics-bad);
  }

  &.red {
    background-color: var(--color-component-warn);
  }
}

.stats {
  display: inline-grid;
  gap: 8px;
}

.info {
  padding-left: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;

  span {
    font: var(--font-caption-1-normal);
  }

  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
  }

  &.gray {
    &::before {
      background-color: var(--color-gray-50);
    }
  }

  &.purple {
    &::before {
      background-color: var(--color-statistics-complementary);
    }
  }

  &.yellow {
    &::before {
      background-color: var(--color-statistics-warning);
    }
  }

  &.orange {
    &::before {
      background-color: var(--color-statistics-bad);
    }
  }

  &.red {
    &::before {
      background-color: var(--color-component-warn);
    }
  }
}

.note {
  font: var(--font-caption-1-normal);
}

.download {
  cursor: pointer;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: -webkit-fill-available;
  color: #222;
}
