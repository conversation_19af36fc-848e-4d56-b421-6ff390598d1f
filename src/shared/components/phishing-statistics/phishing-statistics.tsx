import { FC, useState } from 'react'
import styles from './phishing-statistics.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingStatisticsProps } from './phishing-statistics.d'
import { Card } from '@/shared/ui'
import DownloadIcon from '@/shared/ui/Icon/icons/components/DownloadIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import Skeleton from 'react-loading-skeleton'

const cx = classNamesBind.bind(styles)

interface StatInfoProps {
  color: 'gray' | 'purple' | 'yellow' | 'orange' | 'red'
  translationKey: string
  value?: number
  isLoading?: boolean
}

const StatInfo: FC<StatInfoProps> = ({ color, translationKey, value, isLoading }) => {
  const { t } = useTranslation()

  return (
    <div className={cx('info', color)}>
      <div className={cx('note')}>{t(translationKey)}</div>
      {isLoading ? <Skeleton width={60} height={16} /> : <span>{value ?? 0}</span>}
    </div>
  )
}

const PHISHING_STATS_CONFIG = [
  { color: 'gray' as const, translationKey: 'commons:letter_sent', key: 'sent' as const },
  { color: 'purple' as const, translationKey: 'commons:letters_opened', key: 'opened' as const },
  { color: 'yellow' as const, translationKey: 'commons:followeds_link', key: 'followed' as const },
  {
    color: 'orange' as const,
    translationKey: 'commons:enter_personal_info',
    key: 'entered' as const,
  },
]

const ATTACHMENTS_STATS_CONFIG = [
  {
    color: 'red' as const,
    translationKey: 'commons:openeds_attachments',
    key: 'attachments' as const,
  },
]

const ALL_STATS_CONFIG = [...PHISHING_STATS_CONFIG, ...ATTACHMENTS_STATS_CONFIG]

export const PhishingStatistics: FC<PhishingStatisticsProps.Props> = props => {
  const {
    sent,
    opened,
    followed,
    entered,
    attachments,
    cardWidth,
    statsClassName,
    onClick,
    isHiddenDownloadBtn = false,
    isLoading,
  } = props

  const { t } = useTranslation()

  const [activeTab, setActiveTab] = useState<'phishing' | 'attachments'>('phishing')

  const noData = !sent && !opened && !followed && !entered && !attachments
  const statisticsData = { sent, opened, followed, entered, attachments }

  const openedWidth = opened !== undefined && sent !== undefined ? (opened / sent) * 100 : 2
  const followedWidth = followed !== undefined && sent !== undefined ? (followed / sent) * 100 : 2
  const enteredWidth = entered !== undefined && sent !== undefined ? (entered / sent) * 100 : 2
  const attachmentsWidth =
    attachments !== undefined && sent !== undefined ? (attachments / sent) * 100 : 2

  const currentStatsConfig =
    activeTab === 'phishing' ? PHISHING_STATS_CONFIG : ATTACHMENTS_STATS_CONFIG
  const skeletonCount = currentStatsConfig.length

  const content = isLoading ? (
    <>
      <div className={cx('list')}>
        {Array(skeletonCount)
          .fill(null)
          .map((_, index) => (
            <Skeleton key={index} width={'100%'} height={12} />
          ))}
      </div>
      <div className={cx('stats', statsClassName)}>
        {currentStatsConfig.map(config => (
          <StatInfo
            key={config.key}
            color={config.color}
            translationKey={config.translationKey}
            isLoading={true}
          />
        ))}
      </div>
    </>
  ) : noData ? (
    <div className={cx('placeholder')}>{t('commons:no_data')}</div>
  ) : (
    <>
      <div className={cx('list')}>
        <div className={cx('line', 'gray')} />
        <div className={cx('line', 'purple')} style={{ width: `${openedWidth}%` }} />
        <div className={cx('line', 'yellow')} style={{ width: `${followedWidth}%` }} />
        <div className={cx('line', 'orange')} style={{ width: `${enteredWidth}%` }} />
        <div className={cx('line', 'red')} style={{ width: `${attachmentsWidth}%` }} />
      </div>
      <div className={cx('stats', statsClassName)}>
        {ALL_STATS_CONFIG.map(config => (
          <StatInfo
            key={config.key}
            color={config.color}
            translationKey={config.translationKey}
            value={statisticsData[config.key]}
          />
        ))}
      </div>
    </>
  )

  return (
    <Card
      className={cx('wrapper', {
        activePhishing: activeTab === 'phishing',
      })}
      width={cardWidth ?? undefined}
      padding='normal'
    >
      <div className={cx('top')}>
        <div className={cx('tabs')}>
          <div
            className={cx('tab', {
              active: activeTab === 'phishing',
            })}
            onClick={() => setActiveTab('phishing')}
          >
            {t('commons:phishing')}
          </div>
          <div
            className={cx('tab', {
              active: activeTab === 'attachments',
            })}
            onClick={() => setActiveTab('attachments')}
          >
            {t('commons:attachments')}
          </div>
        </div>
        {!isHiddenDownloadBtn && !noData && !isLoading && (
          <div className={cx('download')} onClick={onClick}>
            <IconWrapper>
              <DownloadIcon />
            </IconWrapper>
          </div>
        )}
      </div>
      {content}
    </Card>
  )
}
