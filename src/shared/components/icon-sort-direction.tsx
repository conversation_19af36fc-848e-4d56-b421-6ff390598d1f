import { MouseEvent } from 'react'
import SortWithArrowUp from '../ui/Icon/icons/components/SortWithArrowUp'
import SortWithArrowDown from '../ui/Icon/icons/components/SortWithArrowDown'
import { IconWrapper } from '../ui/Icon/IconWrapper'

export type SortDirection = 'asc' | 'desc'

type Props = {
  disabled?: boolean
  direction?: SortDirection | null
  startDirection?: SortDirection | null
  onChange: (v: SortDirection, e?: MouseEvent<HTMLDivElement, MouseEvent>) => void
}

const DEFAULT_DIR: SortDirection = 'asc'

const getDirection = (dir?: SortDirection | null, startDirection?: SortDirection | null) => {
  if (dir) return dir === 'asc' ? 'desc' : 'asc'

  if (startDirection) {
    return startDirection === 'asc' ? 'desc' : 'asc'
  }

  return DEFAULT_DIR
}

// Функция для рендеринга иконок сортировки
const renderSortIcon = (
  direction?: SortDirection | null,
  startDirection?: SortDirection | null,
) => {
  const shouldShowUp = !direction && startDirection ? startDirection === 'asc' : direction === 'asc'

  return shouldShowUp ? <SortWithArrowUp /> : <SortWithArrowDown />
}

export const IconSortDirection = ({
  direction,
  startDirection,
  onChange,
  disabled = false,
}: Props) => {
  return (
    <IconWrapper
      onClick={e => {
        onChange(
          getDirection(direction, startDirection),
          e as unknown as MouseEvent<HTMLDivElement, MouseEvent>,
        )
      }}
      style={{ cursor: 'pointer' }}
      disabled={disabled}
      color={disabled ? 'gray60' : direction ? 'primary' : 'gray60'}
    >
      {renderSortIcon(direction, startDirection)}
    </IconWrapper>
  )
}
