import { FC } from 'react'
import { Checkbox } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './notify-employees-form.module.scss'

const cx = classNamesBind.bind(styles)

type LearningItem = {
  id: string
  title: string
}

type NotifyEmployeesFormProps = {
  items: LearningItem[]
  selectedValues: string[]
  onChange: (values: string[]) => void
  error?: string
}

export const NotifyEmployeesForm: FC<NotifyEmployeesFormProps> = ({
  items,
  selectedValues,
  onChange,
  error,
}) => {
  return (
    <div>
      <ul className={cx('filter__list')}>
        {items.map(item => {
          const isChecked = selectedValues.includes(item.id)
          return (
            <li key={item.id}>
              <Checkbox
                className={cx('checkbox')}
                customChecked={isChecked}
                onChange={() => {
                  let newValues: string[]

                  if (isChecked) {
                    newValues = selectedValues.filter(v => v !== item.id)
                  } else {
                    newValues = [...selectedValues, item.id]
                  }

                  onChange(newValues)
                }}
                label={<span className={cx('checkbox__text')}>{item.title}</span>}
              />
            </li>
          )
        })}
      </ul>

      {error && <span className='error-text'>{error}</span>}
    </div>
  )
}
