import { FC, useState } from 'react'
import styles from './select-template-card.module.scss'
import classNamesBind from 'classnames/bind'
import { SelectTemplateCardProps } from './select-template-card.d'
import { AssignTemplateModal } from '@/shared/modals/assign-template-modal'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

const cx = classNamesBind.bind(styles)

export const SelectTemplateCard: FC<SelectTemplateCardProps.Props> = props => {
  const { className, text, selected = [], onChange, label } = props

  const [active, setActive] = useState(false)

  const handleSelect = (courses: UUID[]) => onChange(courses || [])
  const handleClick = () => setActive(prev => !prev)

  const counterTitle = text

  return (
    <>
      {<div className={cx('label')}>{label}</div>}
      <div
        className={cx('wrapper', className, {
          active: !!selected.length,
        })}
        onClick={handleClick}
      >
        <span>{counterTitle}</span>
        <IconWrapper color={!selected.length ? 'gray80' : 'primary'}>
          <PlusIcon />
        </IconWrapper>
      </div>
      <AssignTemplateModal
        active={active}
        setActive={setActive}
        handleSelect={handleSelect}
        selected={selected}
      />
    </>
  )
}
