import classNamesBind from 'classnames/bind'
import styles from './user-course-header.module.scss'
import { UserCourseHeaderProps } from './user-course-header.d'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { StepsToolbar } from '../editor/steps-toolbar/steps-toolbar'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'components__user-course-header'

export const UserCourseHeader: React.FC<UserCourseHeaderProps.Props> = ({
  order,
  name,
  progress,
  activeStep,
  setActive,
}) => {
  const { completed_steps, total_points, total_steps, received_points } = progress
  const { t } = useTranslation(TRANSLATION_FILE)

  return (
    <div className={cx('wrapper')}>
      <div className={cx('title')}>
        {order} {name}
      </div>
      <div className={cx('progress')}>
        <span>
          {t('steps', {
            steps: completed_steps,
            maxSteps: total_steps,
          })}
        </span>
        <span>
          {t('points', {
            points: received_points,
            maxPoints: total_points,
          })}
        </span>
      </div>
      <StepsToolbar activeStep={activeStep} setActive={setActive} />
    </div>
  )
}
