import { SetStateAction } from 'react';
import { Step } from '../editor/steps-toolbar/steps-toolbar';

export declare namespace UserCourseHeaderProps {
  interface Own {
    order: number;
    name: string;
    progress: {
      total_steps: number;
      completed_steps: number;
      total_points: number;
      received_points: number;
    }
    activeStep?: Step;
    setActive: React.Dispatch<SetStateAction<Step | undefined>>
  }

  type Props = Own
}

export {}
