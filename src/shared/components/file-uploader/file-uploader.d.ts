import { MutableRefObject } from "react"

/* eslint-disable @typescript-eslint/no-explicit-any */
type Props = { onClick: () => void }
export declare namespace FileUploaderProps {
  interface Own
    extends React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> {
    className?: string
    children?: any
    value?: FileList
    register?: UseFormRegisterReturn<TFieldName>
    error?: string
    wrapperRef?: RefObject<HTMLDivElement>
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch
}

export {}
