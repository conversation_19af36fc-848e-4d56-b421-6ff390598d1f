import React, { useRef, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './file-uploader.module.scss'

import { FileUploaderProps } from './file-uploader.d'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

// about accept http://jsfiddle.net/dirtyd77/LzLcZ/144/
const FileUploader: React.FC<FileUploaderProps.Props> = props => {
  const {
    className,
    name,
    placeholder,
    children,
    value,
    onChange,
    error,
    wrapperRef,
    ...inputProps
  } = props
  const fileInputRef = useRef<HTMLInputElement | null>(null)
  const [currentFile, setCurrentFile] = useState<FileList | null>(null)

  const { t } = useTranslation()

  const _hasValue = value?.length
  const FileList = Array.from(value || [])
  const _value =
    FileList.length > 1
      ? t('old.file_uploader.files', { count: FileList.length || 0 })
      : FileList[0]?.name

  const handleClick = () => fileInputRef.current && fileInputRef.current.click()

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!fileInputRef.current) return
    onChange && onChange(e)

    if (e.target.files?.length) {
      setCurrentFile(e.target.files)
    } else {
      fileInputRef.current.files = currentFile
    }
  }

  return (
    <div onClick={handleClick} className={cx('wrapper', className)} ref={wrapperRef}>
      {children || (
        <div className={cx('defaultInput')}>
          <div className={cx('placeholder', { hasValue: _hasValue })}>{_value || placeholder}</div>
        </div>
      )}
      <input
        type='file'
        name={name}
        ref={fileInputRef}
        className={cx('fileInput')}
        onChange={handleChange}
        {...inputProps}
      />
      {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
    </div>
  )
}

export default FileUploader
