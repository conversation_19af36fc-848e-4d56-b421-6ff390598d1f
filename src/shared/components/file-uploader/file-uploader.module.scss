@use '../../../shared/assets/styles/mixins/text';

.wrapper {
  cursor: pointer;
}

.defaultInput {
  appearance: none;

  border: none;
  border: 1px solid var(--color-primary);
  border-radius: 10px;

  box-sizing: border-box;
  // padding: 15px 15px 15px 25px;
  height: 51px;
  outline: none;
  position: relative;

  transition: 300ms ease;
  &.disabled {
    border-color: var(--grey);
    color: var(--grey);
  }
}

.placeholder {
  @include text.main(16px);

  background-color: var(--white);
  border-radius: 10px;

  color: var(--brown-grey);
  left: 25px;
  position: absolute;
  top: 50%;
  transform: translate(0, -50%);

  width: calc(100% - 25px);
  &.hasValue {
    color: var(--black);
  }
}

.fileInput {
  display: none;
}

.errorText {
  margin-top: 12px;
}

.error,
.error:-webkit-autofill {
  border: 1px solid #fff;
  box-shadow:
    0 0 0 2px #ff586b,
    0 0 0 50px #fff inset !important;
  box-shadow:
    0 0 0 2px #ff586b,
    0 0 0 50px #fff inset !important;

  transition:
    ease 0.15s,
    margin 0s;
}
