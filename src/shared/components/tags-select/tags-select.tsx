import { FC, memo, useCallback, useMemo } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './tags-select.module.scss'
import { IListItem, MultiSelect, RenderListValue } from '@/shared/ui'
import { Tag } from '../tag/tag'
import { useTranslation } from 'react-i18next'
import { ITag, ITagWithNew } from '@/shared/types/store/tag'

type Props = {
  tagsList?: ITag[]
  selectedTags?: ITagWithNew[]
  label?: string
  className?: string
  inputBaseClassName?: string
  handleChange: (i: IListItem) => void
  isListTop?: boolean
  listWrapperId?: string
  renderListInnerWrapper?: (children: React.ReactNode) => React.ReactNode
}

const cx = classNamesBind.bind(styles)

export const TagsSelect: FC<Props> = memo(props => {
  const {
    label,
    tagsList,
    selectedTags,
    className,
    inputBaseClassName,
    handleChange,
    isListTop,
    listWrapperId,
    renderListInnerWrapper,
  } = props
  const { t } = useTranslation()

  const tagsListForSelect = useMemo(() => {
    if (!tagsList) return

    return tagsList.map(tag => ({ id: tag.id, title: tag.title }))
  }, [tagsList])

  const renderListValue = useCallback<RenderListValue>(
    (isActive, info) => {
      const { id: tagId } = info
      const tagInfo = tagsList?.find(tag => tag.id === tagId)

      return (
        <button type='button' onClick={() => handleChange(info)} className={cx('listWrapper')}>
          {tagInfo && (
            <span>
              <Tag info={tagInfo} isActive={isActive} />
            </span>
          )}
          {isActive && (
            <span className={cx('active')}>{t('dialogs.assign_employees.selected')}</span>
          )}
        </button>
      )
    },
    [handleChange, tagsList],
  )

  const renderValue = useCallback(() => {
    return (
      <div className={cx('selectedTags')}>
        {selectedTags &&
          selectedTags.map(tag => (
            <Tag
              key={tag.id}
              info={tag}
              className={cx('tag')}
              isActive
              onDelete={() => {
                handleChange({ id: tag.id, title: tag.title })
              }}
            />
          ))}
      </div>
    )
  }, [handleChange, selectedTags])

  const selectedTagsIds = useMemo(() => selectedTags?.map(tag => tag.id), [selectedTags])

  return (
    <div className={cx('wrapper', className)}>
      {tagsListForSelect && (
        <MultiSelect
          label={label}
          placeholder={t('commons:select_tag')}
          inputBaseClassName={inputBaseClassName}
          listItemClassName={cx('listItem')}
          customValue={selectedTagsIds}
          list={tagsListForSelect}
          onChange={handleChange}
          renderValue={renderValue}
          renderListValue={renderListValue}
          isListTop={isListTop}
          listWrapperId={listWrapperId}
          renderListInnerWrapper={renderListInnerWrapper}
        />
      )}
    </div>
  )
})

TagsSelect.displayName = 'TagsSelect'
