.wrapper {
  // display: flex;
  width: 100%;
}

.inner {
  > * > * {
    overflow: inherit !important;
  }
}

.header {
  align-items: center;
  display: flex;
}

.header-title {
  align-items: center;

  color: var(--blue-grey);

  cursor: default;
  display: flex;
  font-size: 14px;
  font-weight: normal;
  padding: 10px 15px;
}

.row {
  align-items: center;
  display: flex;
}

.col {
  padding: 10px 15px;
}
