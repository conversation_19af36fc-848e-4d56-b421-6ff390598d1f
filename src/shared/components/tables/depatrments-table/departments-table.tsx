/* eslint-disable @typescript-eslint/no-explicit-any */
import classNamesBind from 'classnames/bind'
import React, { useEffect, useMemo, useState } from 'react'
import { OldTableCell } from '@/shared/components/tables/table-cell'
import { List } from '@/shared/components/list'
import { DepartmentsTableProps } from './departments-table.d'
import styles from './departments-table.module.scss'
import { useTranslation } from 'react-i18next'
import { TDepartment } from '@/shared/types/store/organization-statistic'
import { departmentAPI } from '@/entities/department'

const cx = classNamesBind.bind(styles)

const DepartmentsTable: React.FC<DepartmentsTableProps.Props> = props => {
  const { className, organization_id } = props

  const { t } = useTranslation()

  const columns = useMemo(
    () => [
      {
        name: 'title' as const,
        text: t('components.departments_table.columns.title'),
        width: '30%',
      },
      {
        name: 'employees_count' as const,
        text: t('components.departments_table.columns.employees_count'),
        width: '13%',
      },
      {
        name: 'progress' as const,
        text: t('components.departments_table.columns.progress'),
        width: '10%',
      },
      {
        name: 'phishing' as const,
        text: t('commons:phishing'),
        width: '25%',
      },
      {
        name: 'risk_level' as const,
        text: t('commons:risk_level'),
        width: '13%',
      },
      {
        name: 'risk_level_change' as const,
        text: t('components.departments_table.columns.risk_level_change'),
        width: '10%',
      },
    ],
    [t],
  )

  const limit = 20

  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(0)

  const [departments, setDepartments] = useState<TDepartment[]>()
  const [isFetched, setIsFetched] = useState(false)
  const [isFetching, setIsFetching] = useState(true)

  const [getDepartmentsTrigger, { isLoading: isDepartmentsLoading }] =
    departmentAPI.useLazyGetDepartmentsQuery()

  const fetchData = async (newPage = 0) => {
    setIsFetching(true)
    try {
      const { data } = await getDepartmentsTrigger({
        organization_id,
        limit,
        offset: limit * newPage,
      })
      if (!data) return
      const { data: departments, total_count } = data

      if (newPage === 0) {
        setTotal(total_count)
        setDepartments(departments)
      } else {
        setDepartments(prev => [...(prev ? prev : []), ...departments])
      }

      setIsFetched(() => true)
    } catch (error) {
      console.log(error)
    }
    setIsFetching(false)
  }

  const fetchMore = async () => {
    setPage(prev => prev + 1)

    fetchData(page + 1)
  }

  useEffect(() => {
    if (isDepartmentsLoading || isFetched || departments) return

    fetchData()
  }, [departments])

  const canLoadMore = departments && departments.length < total && !isFetching

  const renderDepartment = (department: TDepartment | undefined, index: number) => (
    <DepartmentRow
      department={department}
      key={department ? department.id + '_id' : `_i` + index}
      columns={columns}
    />
  )

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('header')}>
        {columns.map(c => (
          <div className={cx('header-title')} style={{ width: c.width }} key={c.name}>
            {c.text}
          </div>
        ))}
      </div>
      <div className={cx('inner')}>
        <List
          data={departments}
          defaultCount={15}
          renderItem={renderDepartment}
          loadMore={fetchMore}
          canLoadMore={canLoadMore}
          loading={isDepartmentsLoading || isFetching}
          paginated
          needSkeleton
        />
      </div>
    </div>
  )
}

const DepartmentRow: React.FC<{
  department?: TDepartment
  columns: Array<{ width: string }>
}> = ({ department, columns }) => {
  const { id, title, statistic, users_count } = department || {}
  const loading = !department

  // с бэка нет данных пока
  // const change = risk_level_statistic?.change
  const change = 0

  const { t } = useTranslation()

  return (
    <div className={cx('row')} key={id}>
      <OldTableCell
        key='title'
        placeholder={loading}
        line
        div
        style={{ width: columns[0].width }}
        className={cx('col')}
      >
        {title}
      </OldTableCell>
      <OldTableCell
        key='employees'
        placeholder={loading}
        line
        div
        style={{ width: columns[1].width }}
        className={cx('col')}
      >
        {users_count}
      </OldTableCell>
      <OldTableCell
        key='progress'
        fixed={0}
        suffix='%'
        placeholder={loading}
        line
        div
        style={{ width: columns[2].width }}
        className={cx('col')}
      >
        {statistic?.progress.toFixed(0)}
      </OldTableCell>
      <OldTableCell
        key='phishing'
        placeholder={loading}
        line
        div
        style={{ width: columns[3].width }}
        className={cx('col')}
      >
        {[
          {
            value: statistic?.phishing_events?.sent,
            tooltip: t('components.departments_table.sent_tooltip'),
            size: '4em',
            suffix: t('components.departments_table.sent_suffix'),
          },
          {
            value: statistic?.phishing_events?.clicked,
            tooltip: t('components.departments_table.clicked_tooltip'),
            suffix: t('components.departments_table.sent_suffix'),
            size: '3em',
          },
          {
            value: statistic?.phishing_events?.entered_data,
            tooltip: t('components.departments_table.entered_data_tooltip'),
            suffix: t('components.departments_table.sent_suffix'),
          },
        ]}
      </OldTableCell>
      <OldTableCell
        key='risk_level'
        color={
          !statistic?.risk_level || statistic?.risk_level <= 4
            ? 'green'
            : statistic?.risk_level <= 7
              ? 'yellow'
              : 'red'
        }
        fixed={1}
        placeholder={loading}
        line
        div
        style={{ width: columns[4].width }}
        className={cx('col')}
      >
        {statistic?.risk_level}
      </OldTableCell>

      <OldTableCell
        key='change'
        color={!change || change === 0 ? 'black' : change > 0 ? 'red' : 'green'}
        fixed={1}
        placeholder={loading}
        line
        div
        style={{ width: columns[5].width }}
        className={cx('col')}
      >
        {/* {change && Math.round(change * 10) !== 0
          ? (change === 0 ? '\u00A0' : change > 0 ? '+' : '') + change.toFixed(1)
          : undefined} */}
        {undefined}
      </OldTableCell>
    </div>
  )
}

export default DepartmentsTable
