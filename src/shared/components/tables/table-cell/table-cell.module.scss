.wrapper {
  box-sizing: border-box;
}

.line {

  border-bottom: 1px solid var(--gray);
  box-sizing: border-box;
  // width: 100%;
  flex: 1 0;
  margin: 6px 16px;
}

.content {
  display: flex;
  justify-content: space-between;

  white-space: nowrap;
}

.contentValue {

  display: flex;
  flex-direction: column;

  flex-direction: column;
  max-width: 100%;
  position: relative;

  .text {
    overflow: hidden;

    text-overflow: ellipsis;
  }

  .tooltip {

    align-self: center;

    background-color: var(--white);
    border-radius: 5px;
    bottom: 150%;
    box-shadow: 0 0 20px 0 var(--light-grey);
    box-sizing: border-box;

    color: var(--battleship-grey);
    font-size: 12px;
    font-weight: normal;
    min-height: 28px;
    padding: 8px 5px;

    pointer-events: none;
    position: absolute;
    text-align: center;
    visibility: hidden;
    z-index: 2;

    &::after {

      border-color: var(--white) transparent transparent transparent;
      border-style: solid;
      border-width: 5px;
      box-shadow: 0 0 20px 0 var(--light-grey);

      content: "";
      left: 50%;

      margin-left: -5px;
      position: absolute;
      top: 100%;
    }
  }
  &:hover .tooltip {
    visibility: visible;
  }
}
.noData {
  opacity: 0.5;
}
