import React from "react";
import styles from "./table-cell.module.scss";
import classNamesBind from "classnames/bind";
import { TableCellProps, ITableCell } from "./table-cell.d";
import { OldText } from "@/shared/components";
import { useTranslation } from "react-i18next";

const cx = classNamesBind.bind(styles);

const OldTableCell: React.FC<TableCellProps.Props> = (props) => {
  const {
    className,
    children,
    data = children,
    suffix = "",
    itemSize,
    color = "black",
    line,
    tooltip,
    fixed,
    prefix = "",
    div = false,
    fontSize = "normal",
    placeholder,
    style = {},
  } = props;

  const datas = Array.isArray(data) ? data : [data];
  const tooltips = Array.isArray(tooltip) ? tooltip : [tooltip];
  const hasData = datas.some((d) => {
    const v = d && typeof d === "object" ? d.text || d.value : d;

    return v !== undefined && v !== null;
  });

  const getColor = (value: string | number | ITableCell | undefined | null) => {
    // TODO refactoring types and typecheck
    if (typeof value === "string" || typeof value === "number") return color;

    return value?.color || color;
  };

  const getText = (value?: string | number | ITableCell | null) => {
    const f =
      value && typeof value === "object" && value.fixed !== undefined
        ? value.fixed
        : fixed;
    const s =
      value && typeof value === "object" && value.suffix
        ? " " + value.suffix
        : suffix;
    const p =
      value && typeof value === "object" && value.prefix
        ? value.prefix
        : prefix;

    const text =
      typeof value === "string" || typeof value === "number"
        ? value
        : value?.text || value?.value;

    return (
      p +
      (typeof text === "number" && f !== undefined ? text.toFixed(f) : text) +
      s
    );
  };
  const getTooltip = (index: number) => {
    const d = datas[index];
    const tt =
      d && typeof d === "object" && d.tooltip ? d.tooltip : tooltips[index];

    if (!tt) return null;

    return <div className={cx("tooltip")}>{tt}</div>;
  };

  const getWidth = (index: number) => {
    const d = datas[index];
    const s = d && typeof d === "object" && d.size ? d.size : itemSize;

    if (!s || (datas.length > 2 && index >= datas.length - 1)) return undefined;

    return { width: s };
  };

  const Tag = (div ? "div" : "td") as React.ElementType;

  const { t } = useTranslation();

  return (
    <Tag className={cx("wrapper", className)} style={style}>
      <div className={cx("content")}>
        {hasData ? (
          datas.map((d, i) => (
            <span
              className={cx("contentValue")}
              style={getWidth(i)}
              key={i}
              title={getText(d)}
            >
              <OldText
                variant={fontSize}
                className={cx("text")}
                color={getColor(d)}
                bold
              >
                {getText(d)}
              </OldText>
              {getTooltip(i)}
            </span>
          ))
        ) : !placeholder ? (
          <OldText variant={fontSize} className={cx("noData")}>
            {t("commons:no_data")}
          </OldText>
        ) : (
          <OldText variant={fontSize} placeholder={{ width: 100 }}></OldText>
        )}
        {line && <div className={cx("line")} />}
      </div>
    </Tag>
  );
};

export { OldTableCell };
