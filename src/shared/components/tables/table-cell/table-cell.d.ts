import { TextVariantsType } from "react-app/components/old/Text/Text";
import { TextColor } from "react-app/components/old/Text/Text";

export interface ITableCell {
  value?: number | null;
  color?: TextColor;
  text?: string | number | null;
  fixed?: number;
  suffix?: string;
  prefix?: string;
  size?: string | number;
  tooltip?: string;
}
export type ITableCellData =
  | number
  | number
  | string
  | string[]
  | ITableCell
  | ITableCell[]
  | null;

export declare namespace TableCellProps {
  interface Own {
    className?: string;
    data?: ITableCellData;
    color?: TextColor;
    tooltip?: string | string[];
    suffix?: string;
    prefix?: string;
    itemSize?: string;
    line?: boolean;
    children?: ITableCellData;
    fixed?: number;
    div?: boolean;
    fontSize?: TextVariantsType;
    placeholder?: boolean;
    style?: object;
  }

  type Props = Own;
}

export {};
