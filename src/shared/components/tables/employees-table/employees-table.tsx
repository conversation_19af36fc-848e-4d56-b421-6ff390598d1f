/* eslint-disable @typescript-eslint/no-explicit-any */
import classNamesBind from 'classnames/bind'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { OldTableCell } from '@/shared/components/tables/table-cell'
import { List } from '@/shared/components/list'
import { EmployeesTableProps } from './employees-table.d'
import styles from './employees-table.module.scss'
import { useTranslation } from 'react-i18next'
import { IEmployee } from '@/shared/types/store/organization-statistic'
import { organizationStatisticAPI } from 'entities/statistic'

const cx = classNamesBind.bind(styles)

const EmployeesTable: React.FC<EmployeesTableProps.Props> = props => {
  const { className, organization_id } = props

  const limit = 20

  const [total, setTotal] = useState(0)
  const [page, setPage] = useState(0)
  const [employees, setEmployees] = useState<IEmployee[]>()
  const [isFetched, setIsFetched] = useState(false)
  const [isFetching, setIsFetching] = useState(true)

  const [getEmployeesTableTrigger, { isLoading }] =
    organizationStatisticAPI.useLazyGetEmployeesTableQuery()

  const fetchData = useCallback(
    async (newPage = 0) => {
      setIsFetching(true)
      try {
        const { data, total_count } = await getEmployeesTableTrigger(
          {
            organization_id,
            limit,
            offset: limit * newPage,
          },
          true,
        ).unwrap()

        if (newPage === 0) {
          setTotal(total_count)
          setEmployees(data)
        } else {
          setEmployees(prev => [...(prev ? prev : []), ...data])
        }

        setIsFetched(() => true)
      } catch (error) {
        console.log(error)
      }
      setIsFetching(false)
    },
    [page],
  )

  const fetchMore = useCallback(async () => {
    setPage(prev => prev + 1)

    fetchData(page + 1)
  }, [page, fetchData])

  useEffect(() => {
    if (isLoading || isFetched || employees) return

    fetchData()
  }, [employees, isLoading, isFetched, fetchData])

  const canLoadMore = employees && employees.length < total && !isFetching

  // const onOrderClick = useCallback((new_by: TOrderBy) => {
  // 	setOrderBy(({ by, asc }) => {
  // 		return {
  // 			by: new_by,
  // 			asc: by === new_by ? !asc : true,
  // 		}
  // 	})
  // }, [])

  const { t } = useTranslation()

  const columns = useMemo(
    () => [
      {
        name: 'full_name' as const,
        text: t('components.employees_table.columns.name'),
        width: '30%',
      },
      {
        name: 'progress' as const,
        text: t('components.employees_table.columns.progress'),
        width: '20%',
      },
      {
        name: 'phishing' as const,
        text: t('commons:phishing'),
        width: '30%',
      },
      {
        name: 'risk_level' as const,
        text: t('commons:risk_level'),
        width: '20%',
      },
    ],
    [t],
  )

  const renderEmployee = (e: any | undefined, index: number) => (
    <EmployeeRow employee={e} key={e ? 'id_' + e.id : 'i_' + index} columns={columns} />
  )

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('header')}>
        {columns.map(c => (
          <div className={cx('header-title')} style={{ width: c.width }} key={c.name}>
            {c.text}
          </div>
        ))}
      </div>
      <div className={cx('inner')}>
        <List
          data={employees}
          itemHeight={30}
          defaultCount={15}
          canLoadMore={canLoadMore}
          loadMore={fetchMore}
          loading={isLoading || isFetching}
          renderItem={renderEmployee}
          paginated
          needSkeleton
        />
      </div>
    </div>
  )
}

const EmployeeRow: React.FC<{
  employee?: IEmployee
  columns: Array<{ width: string }>
}> = ({ employee, columns }) => {
  const loading = !employee

  const { first_name = '', middle_name = '', last_name = '', statistic, email } = employee || {}
  const employeeFullName = `${last_name} ${first_name} ${middle_name}`
  const employeeTitle = employeeFullName.trim() ? employeeFullName : email || ''

  const { t } = useTranslation()

  return (
    <div className={cx('row')}>
      <OldTableCell
        key='full_name'
        placeholder={loading}
        line
        div
        className={cx('col')}
        style={{ width: columns[0].width }}
      >
        {employeeTitle}
      </OldTableCell>
      <OldTableCell
        key='progress'
        fixed={0}
        suffix='%'
        placeholder={loading}
        line
        div
        className={cx('col')}
        style={{ width: columns[1].width }}
      >
        {statistic?.progress.toFixed(0)}
      </OldTableCell>
      <OldTableCell
        key='phishing'
        placeholder={loading}
        line
        div
        className={cx('col')}
        style={{ width: columns[2].width }}
      >
        {[
          {
            value: statistic?.phishing_events.sent,
            tooltip: t('components.employees_table.sent_tooltip'),
            suffix: t('components.employees_table.sent_suffix'),
            // eslint-disable-next-line i18next/no-literal-string
            size: '4em',
          },
          {
            value: statistic?.phishing_events.clicked,
            tooltip: t('components.employees_table.clicked_tooltip'),
            suffix: t('components.employees_table.sent_suffix'),
            // eslint-disable-next-line i18next/no-literal-string
            size: '3em',
          },
          {
            value: statistic?.phishing_events.entered_data,
            tooltip: t('components.employees_table.entered_data_tooltip'),
            suffix: t('components.employees_table.sent_suffix'),
          },
        ]}
      </OldTableCell>
      <OldTableCell
        key='risk_level'
        color={
          !statistic?.risk_level || statistic?.risk_level <= 4
            ? 'green'
            : statistic?.risk_level <= 7
              ? 'yellow'
              : 'red'
        }
        fixed={1}
        placeholder={loading}
        line
        div
        className={cx('col')}
        style={{ width: columns[3].width }}
      >
        {statistic?.risk_level}
      </OldTableCell>
    </div>
  )
}

export default EmployeesTable
