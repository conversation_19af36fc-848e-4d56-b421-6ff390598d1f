/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useRef } from 'react'
import styles from './modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ModalProps } from './modal.d'
import { Portal } from '@/shared/ui'
import FailIcon from '@/shared/ui/Icon/icons/components/FailIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useScrollLock } from '@/shared/hooks'

const cx = classNamesBind.bind(styles)

const modalRootElement = document.querySelector('#modals')

export const Modal: React.FC<ModalProps.Props> = props => {
  const { className, closeClassName, active, setActive, children, onClose } = props

  const wrapper = useRef<HTMLDivElement>(null)

  const handleMouseDown = (e: React.MouseEvent<HTMLElement>) => {
    if (e.target === wrapper.current) {
      setActive(false)
      onClose && onClose()
    }
  }

  const handleClose = (e: MouseEvent | TouchEvent) => {
    e.stopPropagation()

    setActive(false)
    onClose && onClose()
  }

  useScrollLock(active)
  // useKeyPress('Escape', handleClose)
  // const modalRef = useOutsideClick(handleClose)

  if (!active) return <></>

  return (
    <Portal container={modalRootElement}>
      <div className={cx('wrapper')} ref={wrapper} onMouseDown={handleMouseDown}>
        <div
          // ref={modalRef as React.LegacyRef<HTMLDivElement>}
          className={cx('inner', className)}
        >
          <IconWrapper
            color='gray70'
            className={cx('close', closeClassName)}
            size='28'
            onClick={(e: any) => handleClose(e)}
          >
            <FailIcon />
          </IconWrapper>
          {children}
        </div>
      </div>
    </Portal>
  )
}
