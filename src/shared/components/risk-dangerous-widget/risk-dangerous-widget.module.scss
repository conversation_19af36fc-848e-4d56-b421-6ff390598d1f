.wrapper {
  width: 100%;
  font-size: 13px;
  line-height: 16px;
  height: 100%;
}

.total {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #343b54;
  margin-bottom: 24px;
  max-width: 60%;

  span {
    font-size: 34px;
    line-height: 44px;
  }
}

.title {
  color: #5c6585;
  margin-bottom: 16px;
}

.line {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0 8px 20px;
  position: relative;

  &::before {
    content: '';
    display: block;
    position: absolute;
    left: 0;
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  &.green {
    &::before {
      background-color: #3dbc87;
    }
  }

  &.yellow {
    &::before {
      background-color: #ffc700;
    }
  }

  &.red {
    &::before {
      background-color: #ff708b;
    }
  }
}

.marker {
  color: #5c6585;
}

.hint {
  margin-top: auto;
  color: #5c6585;
  font-size: 12px;
  line-height: 14px;

  a {
    color: var(--color-primary);
  }
}
