import classNamesBind from 'classnames/bind'
import styles from './risk-dangerous-widget.module.scss'
import { RiskDangerousProps } from './risk-dangerous-widget.d'
import { useTranslation } from 'react-i18next'
import { NavLink } from 'react-router-dom'
import { Card } from '@/shared/ui'
import Skeleton from 'react-loading-skeleton'

const cx = classNamesBind.bind(styles)

interface RiskLineData {
  range: string
  value: number
  color: 'green' | 'yellow' | 'red'
}

const RISK_LINES_CONFIG: Omit<RiskLineData, 'value'>[] = [
  { range: '0 - 4', color: 'green' },
  { range: '4.1 - 7', color: 'yellow' },
  { range: '7.1 - 10', color: 'red' },
]

const RiskLine = ({ range, value, color, isLoading }: RiskLineData & { isLoading?: boolean }) => (
  <div className={cx('line', color)}>
    <div className={cx('marker')}>{range}</div>
    <div className={cx('amount')}>
      {isLoading ? <Skeleton height={'100%'} width={'100px'} /> : value}
    </div>
  </div>
)

const RiskDangerousWidgetSkeleton = () => {
  const { t } = useTranslation(['pages__statistics'])

  return (
    <Card padding='normal' className={cx('wrapper')}>
      <div className={cx('total')}>
        <span>
          <Skeleton height={'100%'} width={'50px'} />
        </span>
        {t('total_users')}
      </div>
      <div className={cx('title')}>{t('users_risk_level')}</div>
      {RISK_LINES_CONFIG.map((config, index) => (
        <RiskLine key={index} {...config} value={0} isLoading />
      ))}
    </Card>
  )
}

export const RiskDangerousWidget = ({
  employees_count,
  values,
  isLoading,
  showLink = true,
}: RiskDangerousProps.Props) => {
  const { t } = useTranslation(['pages__statistics'])

  if (isLoading || !values) {
    return <RiskDangerousWidgetSkeleton />
  }

  const { low, middle, high } = values
  const riskLinesData: RiskLineData[] = [
    { ...RISK_LINES_CONFIG[0], value: low },
    { ...RISK_LINES_CONFIG[1], value: middle },
    { ...RISK_LINES_CONFIG[2], value: high },
  ]

  // TODO: Need to know the condition and change it
  const condition = middle + high > low

  return (
    <Card padding='normal' className={cx('wrapper')}>
      <div className={cx('total')}>
        <span>{employees_count}</span>
        {t('total_users')}
      </div>
      <div className={cx('title')}>{t('users_risk_level')}</div>
      {riskLinesData.map((lineData, index) => (
        <RiskLine key={index} {...lineData} />
      ))}
      {condition && (
        <div className={cx('hint')}>
          {t('risk_level_hint')}
          {showLink ? (
            <NavLink to={'/lk/admin/learning/assigned'} className={cx('link')}>
              {t('assign_courses')}
            </NavLink>
          ) : (
            t('assign_courses')
          )}
          <span> </span>
          {t('increase_knowledge')}
        </div>
      )}
    </Card>
  )
}
