.wrapper {
  align-items: start;

  background: var(--color-surface, #fff);

  cursor: pointer;

  display: grid;
  grid-gap: 16px;
  grid-template-columns: auto 1fr auto;
  padding: 16px;

  transition: var(--transition);

  &:hover {
    background-color: var(--color-gray-20, #f9fafc);

    transition: var(--transition);
  }
}

.inner {
  .title {

    color: var(--gray-gray-90, #343b54);
    font: var(--font-text-1-normal);

    margin-bottom: 6px;
  }
  .icons {
    align-items: center;
    display: flex;
    gap: 16px;

    margin-bottom: 16px;
  }
}

.info {
  align-items: center;
  display: flex;
  gap: 12px;
  &__date {
    color: var(--gray-gray-80, #5c6585);
    font: var(--font-caption-1-normal);
  }
  &__people {
    color: var(--gray-gray-80, #5c6585);
    font: var(--font-caption-1-normal);
  }
  &__risk-level {
    color: var(--gray-gray-80, #5c6585);
    font: var(--font-caption-1-normal);
    &.color {
      &--RED {
        color: #ff5b7a;
      }
      &--GREEN {
        color: #29ab74;
      }
      &--YELLOW {
        color: #ffab49;
      }
    }
  }
}
