import { FC } from 'react'
import styles from './phishing-campaign-card.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingCampaignCardProps } from './phishing-campaign-card.d'
import { IconWithData } from '@/shared/components'
import { hasAttachmentsVector, hasFormVector, getDatePeriod } from './helpers'
import { getRickLevelColor } from '@/shared/helpers'
import { PhishingCampaignProgress } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { useLocaleForDates } from '@/shared/hooks/use-locale-for-dates'
import { format } from '@/shared/helpers/date'

const TRANSLATION_FILE = 'pages__phishing-campaigns'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignCard: FC<PhishingCampaignCardProps.Props> = props => {
  const { className, data, onClick, withEndDate = true } = props
  const { t } = useTranslation()
  const dateLocale = useLocaleForDates()
  const {
    name,
    statistics,
    end_date,
    start_date,
    users_count,
    id,
    is_testing,
    is_autophish,
    email_send_end_date,
    by_tag,
  } = data
  const { incident_risk, opened_attachment, entered_data, clicked, opened, sent } = statistics

  const showAttachment = hasAttachmentsVector(data)
  const showFormStatistics = hasFormVector(data)
  const progress = users_count ? sent / users_count : 0
  const datePeriod = getDatePeriod(
    start_date,
    withEndDate ? end_date : start_date,
    ' — ',
    dateLocale,
  )

  return (
    <div className={cx('wrapper', className)} onClick={() => onClick(id)}>
      <div className={cx('image')}>
        <PhishingCampaignProgress
          progress={progress}
          isTesting={is_testing}
          isAutoPhishing={is_autophish}
        />
      </div>
      <div className={cx('inner')}>
        <div className={cx('title')}>{name}</div>
        <div className={cx('icons')}>
          <IconWithData icon='emailCloseSmall' value={sent} className={cx('icon-wrapper')} />
          <IconWithData icon='emailOpenSmall' value={opened} className={cx('icon-wrapper')} />
          {showFormStatistics && (
            <IconWithData icon='web2Small' value={clicked} className={cx('icon-wrapper')} />
          )}
          {showFormStatistics && (
            <IconWithData icon='lock3' value={entered_data} className={cx('icon-wrapper')} />
          )}
          {showAttachment && (
            <IconWithData
              icon='clipSmall'
              value={opened_attachment}
              className={cx('icon-wrapper')}
            />
          )}
        </div>
        <div className={cx('info')}>
          <div className={cx('info__date')}>
            {datePeriod}{' '}
            {!by_tag &&
              email_send_end_date &&
              `(${t(`${TRANSLATION_FILE}:email_send_end_date`, {
                date: format(new Date(email_send_end_date), 'd MMM. yyyy HH:mm', dateLocale),
              })})`}
          </div>
          <div className={cx('info__people')}>
            {t('commons:people_count', { count: users_count })}
          </div>
          <div className={cx('info__risk-level', `color--${getRickLevelColor(incident_risk)}`)}>
            {incident_risk}% {t('commons:incident').toLowerCase()}
          </div>
        </div>
      </div>
    </div>
  )
}
