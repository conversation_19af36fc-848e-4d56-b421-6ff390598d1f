import { type IPhishingCapmaign } from '@/entities/phishing'

export const hasAttachmentsVector = (data: IPhishingCapmaign) => {
  return !!data?.attack_vectors.some(v => v.type === 'attachments')
}

export const hasFormVector = (data: IPhishingCapmaign) => {
  return !!data?.attack_vectors.some(v => v.type === 'form')
}

export const getDatePeriod = (
  from: string | Date,
  to: string | Date,
  separator = ' — ',
  locale?: string,
): string => {
  const start = new Date(from)
  const end = new Date(to)
  const currentLocale = locale || 'ru-RU'

  const isCurrentYear = new Date().getFullYear() === start.getFullYear()
  const isSameYear = start.getFullYear() === end.getFullYear()
  const isSameMonth = isSameYear && start.getMonth() === end.getMonth()
  const isSameDay = isSameMonth && start.getDate() === end.getDate()

  const dayOptions: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  }
  const dayMonthOptions: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  }
  const dayMonthYearOptions: Intl.DateTimeFormatOptions = {
    day: 'numeric',
    month: 'short',
    year: 'numeric',
  }
  const timeOptions: Intl.DateTimeFormatOptions = {
    hour: 'numeric',
    minute: 'numeric',
  }

  if (isSameDay) {
    const dateStr = start.toLocaleDateString(
      currentLocale,
      isCurrentYear ? dayMonthOptions : dayMonthYearOptions,
    )
    const timeStr = start.toLocaleTimeString(currentLocale, timeOptions)
    return `${dateStr} ${timeStr}`
  } else if (isSameMonth) {
    const startDayStr = start.toLocaleDateString(currentLocale, dayOptions)
    const endDateStr = end.toLocaleDateString(
      currentLocale,
      isCurrentYear ? dayMonthOptions : dayMonthYearOptions,
    )
    return [startDayStr, endDateStr].join(separator)
  } else if (isSameYear) {
    const startDateStr = start.toLocaleDateString(currentLocale, dayMonthOptions)
    const endDateStr = end.toLocaleDateString(
      currentLocale,
      isCurrentYear ? dayMonthOptions : dayMonthYearOptions,
    )
    return [startDateStr, endDateStr].join(separator)
  } else {
    const startDateStr = start.toLocaleDateString(currentLocale, dayMonthYearOptions)
    const endDateStr = end.toLocaleDateString(currentLocale, dayMonthYearOptions)
    return [startDateStr, endDateStr].join(separator)
  }
}
