import classNamesBind from 'classnames/bind'
import styles from './assigned-courses-stats.module.scss'
import { useTranslation } from 'react-i18next'
import { UserPassedStatResponse } from '@/entities/courses/model/api/test-types'

const cx = classNamesBind.bind(styles)

type Props = { stats?: UserPassedStatResponse; className?: string }

export const AssignedCoursesStats = ({ stats, className }: Props) => {
  const { t } = useTranslation('components__assigned-courses-stats')

  if (!stats) return null

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('item')}>
        <p className={cx('statValue')}>
          {stats.courses.passed}
          <span className={cx('colorGrey')}>/</span>
          <span className={cx('totalValue', 'colorGrey')}>{stats.courses.total}</span>
        </p>
        <p className={cx('statName')}>{t('passed_courses')}</p>
      </div>
      <div className={cx('item')}>
        <p className={cx('statValue')}>
          {stats.quizes.passed}
          <span className={cx('colorGrey')}>/</span>
          <span className={cx('totalValue', 'colorGrey')}>{stats.quizes.total}</span>
        </p>
        <p className={cx('statName')}>{t('passed_tests')}</p>
      </div>
    </div>
  )
}
