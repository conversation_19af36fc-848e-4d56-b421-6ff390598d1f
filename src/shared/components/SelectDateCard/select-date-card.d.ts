import { ReactNode } from 'react'

export declare namespace SelectDateCardProps {
  interface Own {
    className?: string
    classNameLabel?: string
    wrapperClassName?: string
    label?: string
    text: string
    onChange: (date: Date | null) => void
    selected: Date | null
    inputSelected?: Date | null
    min?: Date | null
    max?: Date | null | undefined
    datePickerClassName?: string
    withoutTime?: boolean
    bottomSlot?: ReactNode
    customActive?: boolean
    onClick?: () => void
    contentSlot?: ReactNode
    wrapperContentClassname?: string
  }

  type Props = Own
}

export {}
