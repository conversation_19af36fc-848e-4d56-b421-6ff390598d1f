import { FC, useMemo, useState } from 'react'
import styles from './select-date-card.module.scss'
import classNamesBind from 'classnames/bind'
import { SelectDateCardProps } from './select-date-card.d'
import { SelectDateCard } from './select-date-card'
import { DateField } from '../date-field'
import { Button, IListItem, InputSelect } from '@/shared/ui'
import {
  compareDatesWithoutTime,
  getHours,
  getMinutes,
  isToday,
  isValidDate,
} from '@/shared/helpers/date'
import { CalendarDate, DateValue } from '@internationalized/date'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const hours = Array.from({ length: 24 }).map((_, i) => i)
const minutes = [0, 15, 30, 45]

const getCalendarDate = (date: Date) =>
  new CalendarDate(date.getFullYear(), date.getMonth() + 1, date.getDate())

const getDateFromCalendarDate = (calendarDate: DateValue) =>
  new Date(calendarDate.year, calendarDate.month - 1, calendarDate.day)

const getNearestTimeValues = (hours: number, mins: number): { hours: number; minutes: number } => {
  const nearestMinutes = minutes.find(mark => mins <= mark) ?? 0
  const newHours = nearestMinutes === 0 && mins > 45 ? (hours + 1) % 24 : hours

  return { hours: newHours, minutes: nearestMinutes }
}

type SelectDateCardWithInputProps = SelectDateCardProps.Props & {
  withoutTime?: boolean
  minForDateField?: Date | null
}

export const SelectDateCardWithInput: FC<SelectDateCardWithInputProps> = props => {
  const isSelectedFurther = useMemo(
    () => props.selected && props.min && compareDatesWithoutTime(props.selected, props.min),
    [props.selected, props.min],
  )
  const [value, setValue] = useState(props?.selected ?? null)
  const [active, setActive] = useState(false)
  const { t } = useTranslation()

  const onChange = (date: Date | null) => setValue(date)

  const [selectedHours, setSelectedHours] = useState<number>(0)
  const [selectedMinutes, setSelectedMinutes] = useState<number>(0)

  const isMinHoursFurther = props?.min && selectedHours <= props.min?.getHours()
  const minDateField = props?.minForDateField ?? props?.min

  const handleChangeSelectedHours = (i: IListItem) => {
    const hour = +i.id
    setSelectedHours(hour)
    if (!value) return

    const date = new Date(value)
    date?.setHours(hour)
    setValue(date)
  }

  const handleChangeSelectedMinutes = (i: IListItem) => {
    const minutes = +i.id
    setSelectedMinutes(minutes)

    if (!value) return

    const date = new Date(value)
    date?.setMinutes(minutes)
    setValue(date)
  }

  const isHourDisabled = (item: IListItem) => {
    if (minDateField && !isSelectedFurther && value && isToday(value)) {
      return Number(item.id) < minDateField?.getHours()
    }

    return false
  }

  const isMinuteDisabled = (item: IListItem) => {
    if (minDateField && !isSelectedFurther && isMinHoursFurther && value && isToday(value)) {
      return Number(item.id) < minDateField?.getMinutes()
    }

    return false
  }

  const checkIsValidDate = (): boolean => {
    if (!value) return false

    const isValid = isValidDate(value)

    if (!isValid) return false

    if (minDateField && new Date(minDateField) > new Date(value)) return false

    if (props.max && new Date(props.max) < new Date(value)) return false

    return true
  }

  return (
    <SelectDateCard
      {...props}
      className={props.className}
      selected={value}
      inputSelected={props.selected}
      onChange={date => {
        onChange(date)

        if (!date) return

        let newDate = new Date(date)

        const hoursValue = getHours(newDate)
        const minutesValue = getMinutes(newDate)

        const { hours: nearestHours, minutes: nearestMinutes } = getNearestTimeValues(
          hoursValue,
          minutesValue,
        )

        setSelectedHours(nearestHours)
        setSelectedMinutes(nearestMinutes)
        newDate.setHours(nearestHours)
        newDate.setMinutes(nearestMinutes)
        setValue(newDate)
      }}
      customActive={active}
      onClick={() => setActive(true)}
      bottomSlot={
        <div className={cx('bottom__actions')}>
          <div className={cx('separator')}></div>
          <DateField
            minValue={minDateField ? getCalendarDate(minDateField) : undefined}
            maxValue={props.max ? getCalendarDate(props.max) : undefined}
            value={value ? getCalendarDate(value) : undefined}
            onChange={date => {
              if (!date) return

              onChange(getDateFromCalendarDate(date))
            }}
            label={t('commons:date')}
          />
          <div className={cx('footer')}>
            {!props.withoutTime && (
              <div className={cx('time')}>
                <div className={cx('time-select')}>
                  <InputSelect
                    className={cx('select')}
                    wrapperClassName={cx('select__wrapper')}
                    list={hours.map(i => ({
                      title: i.toString().padStart(2, '0'),
                      id: i + '',
                    }))}
                    handleChange={handleChangeSelectedHours}
                    value={selectedHours + ''}
                    isOptionDisabled={isHourDisabled}
                    renderInput={inputProps => (
                      <input
                        {...inputProps}
                        onChange={e => inputProps.onChange(e.target.value)}
                        inputMode='numeric'
                        min={0}
                        max={23}
                        maxLength={2}
                        placeholder='0'
                        autoFocus
                        tabIndex={1}
                      />
                    )}
                  />
                </div>
                <div className={cx('time-separator')} />
                <div className={cx('time-select')}>
                  <InputSelect
                    wrapperClassName={cx('select__wrapper')}
                    className={cx('select')}
                    list={minutes.map(i => ({
                      title: i.toString().padStart(2, '0'),
                      id: i + '',
                    }))}
                    handleChange={handleChangeSelectedMinutes}
                    value={selectedMinutes + ''}
                    isOptionDisabled={isMinuteDisabled}
                    renderInput={inputProps => (
                      <input
                        {...inputProps}
                        onChange={e => inputProps.onChange(e.target.value)}
                        inputMode='numeric'
                        min={0}
                        max={59}
                        maxLength={2}
                        placeholder='0'
                        tabIndex={1}
                      />
                    )}
                  />
                </div>
              </div>
            )}
            <Button
              fullWidth
              disabled={!checkIsValidDate()}
              onClick={() => {
                props?.onChange(value)
                setActive(false)
              }}
              type='button'
              tabIndex={1}
            >
              {t('commons:choose')}
            </Button>
          </div>
        </div>
      }
    />
  )
}
