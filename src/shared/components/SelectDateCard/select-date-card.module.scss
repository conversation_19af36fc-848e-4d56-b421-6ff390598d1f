.wrapperWrapper {
  position: relative;
}

.wrapper {
  background: var(--color-surface);
  border-radius: 12px;
  box-shadow: var(--shadow-border);
  padding: 14px 16px;
  cursor: pointer;

  &:hover {
    background-color: var(--color-gray-20);

    transition: var(---transition);
  }
  
  &__content {
    display: flex;
    justify-content: space-between;
    align-items: center;

    transition: var(---transition);
    span {
      color: var(--color-gray-80);
      font: var(--font-text-2-medium);
    }
    &.active {
      span {
        color: var(--color-primary);
      }
    }

   
  }
}

.label {
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
  margin-bottom: 8px;
}

.footer {
  align-items: center;

  display: flex;
  gap: 16px;
}

.time {
  align-items: center;
  display: flex;
}

.separator {
  height: 1px;
  width: 100%;
  background-color: var(--color-gray-30);
}

.time-select {
  width: 64px;
}

.time-separator {
  align-items: center;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  margin: 0 4px;
  width: 2px;

  &::before,
  &::after {
    background-color: var(--color-gray-60);
    border-radius: 100%;

    content: '';
    display: inline-block;
    height: 2px;
    margin: 3px 0;
    width: 2px;
  }
}

.submit {
  flex-grow: 1;
  flex-shrink: 0;
  height: 100%;

  &_top {
    margin-left: auto;
  }
}

.select {
  border: 2px solid var(--color-gray-60);
  border-radius: 12px;

  > * {
    border: 0;
    padding: 8px 12px;
  }
  transition: 0.3s all;

  &:hover,&:active{
    border-color: var(--color-primary);
  }

  &__wrapper {
    padding: 12px 10px 12px 12px !important;
  }
}

.bottom__actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
}