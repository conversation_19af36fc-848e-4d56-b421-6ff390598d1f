import { FC, useEffect, useState } from 'react'
import styles from './select-date-card.module.scss'
import classNamesBind from 'classnames/bind'
import { SelectDateCardProps } from './select-date-card.d'
import { DatePicker, transform } from '@/shared/ui'
import CalendarBoldIcon from '@/shared/ui/Icon/icons/components/CalendarBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useLocale } from '@/shared/hooks/use-locale'

const cx = classNamesBind.bind(styles)

export const SelectDateCard: FC<SelectDateCardProps.Props> = props => {
  const {
    className,
    classNameLabel,
    wrapperClassName,
    withoutTime,
    datePickerClassName,
    text,
    min,
    inputSelected = null,
    selected = null,
    max,
    onChange,
    label,
    bottomSlot,
    customActive = false,
    onClick,
    contentSlot,
    wrapperContentClassname,
  } = props
  const locale = useLocale()

  const [active, setActive] = useState(customActive)

  const selectedValue = typeof inputSelected !== 'undefined' ? inputSelected : selected

  useEffect(() => {
    setActive(!!customActive)
  }, [customActive])

  const handleSelect = (date: Date | null) => onChange(date || null)

  const handleClick = () => {
    setActive(prev => !prev)
  }

  const getCounterTitle = () => {
    if (!selectedValue) return text

    if (withoutTime) return transform(selectedValue, 'd MMMM yyyy', locale)
    return transform(selectedValue, 'd MMMM yyyy, HH:mm', locale)
  }

  const counterTitle = getCounterTitle()

  return (
    <div className={cx('wrapperWrapper', wrapperClassName)}>
      {<div className={cx('label', classNameLabel)}>{label}</div>}
      <div
        className={cx('wrapper', className, {
          active: !!selectedValue,
        })}
        onClick={e => {
          e.stopPropagation()
          handleClick()
          onClick?.()
        }}
      >
        {contentSlot}
        <div className={cx('wrapper__content', wrapperContentClassname)}>
          <span>{counterTitle}</span>
          <IconWrapper color={!selectedValue ? 'gray80' : 'primary'}>
            <CalendarBoldIcon />
          </IconWrapper>
        </div>
      </div>
      <DatePicker
        bottomSlot={bottomSlot}
        withoutTime={withoutTime}
        min={min}
        max={max}
        active={active}
        setActive={setActive}
        handleSelect={handleSelect}
        selected={selected}
        className={datePickerClassName}
      />
    </div>
  )
}
