import React, { useRef, useState, useCallback } from 'react'
import { v4 as uuid } from 'uuid'
import classNames from 'classnames/bind'
import styles from './page-preview.module.scss'
import { useEvent } from '@/shared/hooks'

const cx = classNames.bind(styles)
interface PreviewProps {
  html: string | null | undefined
  wrapperClassName?: string
  className?: string
  viewportWidth?: number
  viewportHeight?: number
  checkIfDisabled?: () => void
}

const IFRAME_DOM_CONTENT_LOADED_MESSAGE = 'EDU_IFRAME_DOM_CONTENT_LOADED_MESSAGE'

export const PagePreview: React.FC<PreviewProps> = ({
  html,
  viewportWidth,
  viewportHeight,
  wrapperClassName,
  className,
  checkIfDisabled,
}) => {
  const iframeRef = useRef<HTMLIFrameElement | null>(null)
  const iframeWrapperRef = useRef<HTMLDivElement | null>(null)
  const wrapperRef = useRef<HTMLDivElement | null>(null)
  const [uniqueId] = useState(() => uuid())

  const MSG = IFRAME_DOM_CONTENT_LOADED_MESSAGE + '_' + uniqueId

  const contentReady = () => {
    const readyScript = `
			<script>
			document.addEventListener('DOMContentLoaded', () => {
				window.parent?.postMessage("${MSG}", "*")
			})
			</script>
    	`
    return html + readyScript
  }

  const fitContent = useCallback(() => {
    const wrapper = wrapperRef?.current
    const iframe = iframeRef.current
    const iframeWrapper = iframeWrapperRef.current

    const iframeDoc = iframe?.contentDocument?.documentElement

    if (!iframe || !iframeDoc || !iframeWrapper || !wrapper) return

    iframe.style.removeProperty('transform')
    iframe.style.removeProperty('width')
    iframe.style.removeProperty('height')

    iframeWrapper.style.removeProperty('width')
    iframeWrapper.style.removeProperty('height')

    iframeDoc.style.removeProperty('height')
    iframeDoc.style.removeProperty('overflow-y')

    const wrapperWidth = wrapper.offsetWidth
    const contentWidth = iframeDoc.offsetWidth

    const nextViewportWidth = viewportWidth || wrapperWidth

    const nextWidth = Math.max(contentWidth, nextViewportWidth)
    const scale = wrapperWidth / nextWidth

    iframeWrapper.style.setProperty('width', `${Math.round(nextWidth * scale)}px`)
    iframe.style.setProperty('width', `${nextWidth}px`)

    iframe.style.setProperty('transform-origin', `0 0`)
    iframe.style.setProperty('transform', `scale(${scale})`)

    const height = Math.max(
      viewportHeight || 0,
      iframeDoc?.offsetHeight || 0,
      iframeDoc?.scrollHeight || 0,
      wrapper.offsetHeight * (1 / scale),
    )

    iframeDoc.style.setProperty('height', `${height}px`)

    iframeDoc.style.setProperty('height', `${height}px`)
    iframe.style.setProperty('height', `${height}px`)
    iframeWrapper.style.setProperty('height', `${height * scale}px`)

    iframeDoc.style.setProperty('overflow-y', 'hidden')
    iframeDoc.style.setProperty('overflow-x', 'hidden')
    checkIfDisabled && checkIfDisabled()
  }, [viewportHeight, viewportWidth, checkIfDisabled])

  const onMessage = useCallback(
    (event: MessageEvent) => {
      if (event.data === MSG) {
        fitContent()
      }
    },
    [MSG, fitContent],
  )

  useEvent('resize', fitContent, window)
  useEvent('message', onMessage, window)

  // eslint-disable-next-line i18next/no-literal-string
  if (!html && html !== '') return <>no data</>

  return (
    <div ref={wrapperRef} className={cx('main-wrapper')}>
      <div className={cx('iframe-wrapper', wrapperClassName)} ref={iframeWrapperRef}>
        <iframe
          allowFullScreen
          sandbox='allow-same-origin allow-scripts'
          onLoad={fitContent}
          className={cx('iframe', className)}
          ref={iframeRef}
          srcDoc={contentReady()}
        />
      </div>
    </div>
  )
}
