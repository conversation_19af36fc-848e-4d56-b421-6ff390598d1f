import React from 'react'
import styles from './profile-info.module.scss'
import classNamesBind from 'classnames/bind'
import { ProfileInfoProps } from './profile-info.d'
import { getFullName } from '@/shared/helpers'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const ProfileInfo: React.FC<ProfileInfoProps.Props> = props => {
  const { info } = props
  const { t } = useTranslation()

  const { last_name, first_name, middle_name, email, department, position } = info

  const fullname = getFullName({ last_name, first_name, middle_name, email })

  return (
    <div className={cx('wrapper')}>
      <div className={cx('title')}>{t('commons:basic_information')}</div>
      <div className={cx('info')}>
        {fullname && (
          <div className={cx('item')}>
            <div className={cx('name')}>{t('commons:full_name')}</div>
            <div className={cx('value')}>{fullname}</div>
          </div>
        )}
        <div className={cx('item')}>
          <div className={cx('name')}>{t('commons:position')}</div>
          <div className={cx('value')}>{position ? position : '—'}</div>
        </div>
        <div className={cx('item')}>
          <div className={cx('name')}>{t('commons:department')}</div>
          <div className={cx('value')}>{department?.title ? department?.title : '—'}</div>
        </div>
        <div className={cx('item')}>
          {/* eslint-disable-next-line i18next/no-literal-string */}
          <div className={cx('name')}>Email</div>
          <div className={cx('value')}>{email ? email : '—'}</div>
        </div>
      </div>
    </div>
  )
}
