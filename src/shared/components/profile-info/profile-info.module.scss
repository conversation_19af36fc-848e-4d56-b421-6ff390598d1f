.wrapper {

  background: var(--color-surface, #fff);

  border: 1px solid var(--color-gray-30, #ebeff2);
  border-radius: 16px;
  margin-bottom: 12px;

  max-width: 446px;

  padding: 20px 24px;
}

.title {

  color: var(--color-gray-90, #343b54);
  font: var(--font-text-1-normal);

  margin-bottom: 24px;
}

.info {
  .item {
    align-content: start;
    display: grid;
    grid-gap: 30px;
    grid-template-columns: 75px 1fr;

    margin-bottom: 24px;
    &:last-child {
      margin-bottom: 0;
    }

    .name {
      color: var(--color-gray-90, #343b54);
      font: var(--font-text-2-normal);
    }
    .value {
      color: var(--color-gray-80, #5c6585);
      font: var(--font-text-2-normal);
    }
  }
}
