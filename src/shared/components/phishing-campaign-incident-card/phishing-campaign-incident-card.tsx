/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { FC } from 'react'
import styles from './phishing-campaign-incident-card.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingCampaignIncidentCardProps } from './phishing-campaign-incident-card.d'
import { Tooltip } from '@/shared/ui'
import CalendarBoldIcon from '@/shared/ui/Icon/icons/components/CalendarBoldIcon'
import PeopleBoldIcon from '@/shared/ui/Icon/icons/components/PeopleBoldIcon'
import EmailCloseSmallIcon from '@/shared/ui/Icon/icons/components/EmailCloseSmallIcon'
import EmailOpenSmallIcon from '@/shared/ui/Icon/icons/components/EmailOpenSmallIcon'
import Web2SmallIcon from '@/shared/ui/Icon/icons/components/Web2SmallIcon'
import Lock2Icon from '@/shared/ui/Icon/icons/components/Lock2Icon'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { type IPhishingCapmaign } from '@/entities/phishing'
import { getIncidentRiskLevelColor } from '@/shared/helpers'
import { useTranslation } from 'react-i18next'
import { useLocaleForDates } from '@/shared/hooks/use-locale-for-dates'
import { format } from '@/shared/helpers/date'
import { getDatePeriod } from '../phishing-campaign-card/helpers'

const TRANSLATION_FILE = 'pages__phishing-campaigns'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignIncidentCard: FC<PhishingCampaignIncidentCardProps.Props> = props => {
  const { t } = useTranslation()
  const dateLocale = useLocaleForDates()

  const {
    data,
    className,
    dateTooltip = t('commons:mailing_period'),
    infoType = 'date',
    withEndDate = true,
  } = props

  if (!data) return null

  const {
    users_count = 0,
    by_tag,
    email_send_end_date,
    statistics: { opened = 0, sent = 0, clicked = 0, opened_attachment = 0, entered_data = 0 },
  } = data as IPhishingCapmaign

  const incident_risk_level = getIncidentRiskLevelColor(data.statistics.incident_risk)

  const incident_risk = data?.statistics?.incident_risk || 0

  const getPeriod = (): string => {
    if (by_tag) {
      return new Intl.DateTimeFormat(dateLocale, { month: 'short', day: 'numeric' })
        .format(new Date(data?.start_date))
        .replace('.', '')
    }

    return 'start_date' in data && (data?.start_date || data?.end_date)
      ? getDatePeriod(
          data?.start_date,
          withEndDate ? data?.end_date : data?.start_date,
          ' — ',
          dateLocale,
        )
      : ''
  }

  const datePeriod = getPeriod()
  const hasAttachmentsVector = 'email' in data && data?.email?.attachments?.length > 0

  return (
    <div className={cx('incident-wrapper', className)}>
      <Tooltip content={t('commons:probability_of_incident_nbsp')}>
        <div className={cx('incident-title', `incident-level-${incident_risk_level}`)}>
          {t('commons:incident')}&nbsp;&nbsp;{incident_risk}%
        </div>
      </Tooltip>
      <div className={cx('info-wrapper')}>
        {infoType === 'date' && (
          <div className={cx('info-container')}>
            {datePeriod && (
              <div className={cx('info-period')}>
                <Tooltip
                  content={
                    <span className={cx('no-break')}>
                      {dateTooltip}&nbsp;
                      {format(
                        new Date(data?.start_date),
                        // eslint-disable-next-line i18next/no-literal-string
                        'd MMM. yyyy HH:mm',
                        dateLocale,
                      )}
                      {!by_tag &&
                        ` - ${format(
                          new Date(data?.end_date),
                          // eslint-disable-next-line i18next/no-literal-string
                          'd MMM. yyyy HH:mm',
                          dateLocale,
                        )}`}
                    </span>
                  }
                >
                  <div className={cx('info')}>
                    <IconWrapper color='gray70' size='24'>
                      <CalendarBoldIcon />
                    </IconWrapper>
                    <div className={cx('info-text')}>
                      <span>{datePeriod}</span>
                    </div>
                  </div>
                </Tooltip>
                {!by_tag && email_send_end_date && (
                  <Tooltip
                    content={
                      // eslint-disable-next-line i18next/no-literal-string
                      t(`${TRANSLATION_FILE}:email_send_end_date`, {
                        date: format(
                          new Date(email_send_end_date),
                          'd MMM. yyyy HH:mm',
                          dateLocale,
                        ),
                      })
                    }
                  >
                    <span className={cx('info-text')}>
                      {` (${t(`${TRANSLATION_FILE}:email_send_end_date`, {
                        date: format(new Date(email_send_end_date), 'dd MMM', dateLocale),
                      })})`}
                    </span>
                  </Tooltip>
                )}
              </div>
            )}
            <Tooltip content={<>{t('commons:employees_count_nbsp')}</>}>
              <div className={cx('info')}>
                <IconWrapper color='gray70' size='24'>
                  <PeopleBoldIcon />
                </IconWrapper>
                <div className={cx('info-text')}>
                  {users_count} {t('commons:short_people')}
                </div>
              </div>
            </Tooltip>
          </div>
        )}
        {infoType === 'messages' && (
          <>
            {!Number.isNaN(sent) && (
              <Tooltip content={t('commons:sent')}>
                <div className={cx('info')}>
                  <IconWrapper color='gray70' size='20'>
                    <EmailCloseSmallIcon />
                  </IconWrapper>
                  <div className={cx('info-text')}>{sent}</div>
                </div>
              </Tooltip>
            )}
            {!Number.isNaN(opened) && (
              <Tooltip content={t('commons:opened')}>
                <div className={cx('info')}>
                  <IconWrapper color='gray70' size='20'>
                    <EmailOpenSmallIcon />
                  </IconWrapper>
                  <div className={cx('info-text')}>{opened}</div>
                </div>
              </Tooltip>
            )}
            {!Number.isNaN(clicked) && (
              <Tooltip content={<>{t('commons:transition_on_link_nbsp')}</>}>
                <div className={cx('info')}>
                  <IconWrapper color='gray70' size='20'>
                    <Web2SmallIcon />
                  </IconWrapper>
                  <div className={cx('info-text')}>{clicked}</div>
                </div>
              </Tooltip>
            )}
            {!Number.isNaN(entered_data) && (
              <Tooltip content={<>{t('commons:entered_all_data_nbsp')}</>}>
                <div className={cx('info')}>
                  <IconWrapper color='gray70' size='20'>
                    <Lock2Icon />
                  </IconWrapper>
                  <div className={cx('info-text')}>{entered_data}</div>
                </div>
              </Tooltip>
            )}
            {hasAttachmentsVector && !Number.isNaN(opened_attachment) && (
              <Tooltip content={<>{t('commons:open_attachments_nbsp')}</>}>
                <div className={cx('info')}>
                  <IconWrapper color='gray70' size='20'>
                    <ClipBoldIcon />
                  </IconWrapper>
                  <div className={cx('info-text')}>{opened_attachment}</div>
                </div>
              </Tooltip>
            )}
          </>
        )}
      </div>
    </div>
  )
}
