/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, useCallback, useEffect, useMemo, useState } from 'react'
import styles from './tag-action.module.scss'
import classNamesBind from 'classnames/bind'
import { TagActionProps } from './tag-action.d'
import { Input, Select, IListItem } from '@/shared/ui'
import ArrowIcon from '@/shared/ui/Icon/icons/components/ArrowIcon'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { ActionType, ITag, ITagAction, IUpdateActionBody } from '@/shared/types/store/tag'
import { Link, NavigateFunction, useNavigate } from 'react-router-dom'
import { TagListType } from './config'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  deleteAction,
  selectTagsActions,
  selectTagsPhishingTemplates,
  selectTagsSelectedCourses,
  updateAction,
} from '@/store/slices/tags'
import { SelectAssignLetter } from '../select-assign-templates'
import { useTranslation } from 'react-i18next'
import { TFunction } from 'i18next'
import { checkTagIsCustom } from '@/shared/helpers/organization'
import { InputSelect } from '@/shared/ui/input-select'
import { ETagType } from '@/shared/types/enums'

const cx = classNamesBind.bind(styles)

type ActionProps = {
  info: ITagAction
  openModal: (id: string, courses: string[]) => void
  navigate?: NavigateFunction
  updateAction: (id: string, body: IUpdateActionBody) => void
  tagsData?: ITag[]
  phishingTemplatesIds?: UUID[]
  t: TFunction<'translation', undefined>
}

const PhishingAction = ({ info, updateAction, t }: ActionProps) => {
  const days = info?.defer_by !== undefined ? Number(info?.defer_by) : 0

  const domain = t('commons:days_count', { count: days })

  return (
    <div className={cx('phishing')}>
      <SelectAssignLetter
        selected={info?.phishing_templates}
        onChangeSelect={ids => {
          updateAction(info.id, { phishing_templates: ids })
        }}
      />
      <div className={cx('deferBy')}>
        {t('commons:send_phishing_via')}
        <Input
          value={info?.defer_by !== undefined ? String(info?.defer_by) : ''}
          onChange={value => {
            const numberedValue = value ? Number(value) : 0
            updateAction(info.id, { defer_by: numberedValue })
          }}
          name='deferBy'
          domain={domain}
          className={cx('inputDeferBy')}
        />
      </div>
      {info?.phishing_templates_campaign_id && (
        <Link
          className={cx('link')}
          to={
            `/lk/admin/phishing/phishing-by-tags/campaigns/` +
              info?.phishing_templates_campaign_id || ''
          }
        >
          {t('commons:mailing_statistics')}
        </Link>
      )}
    </div>
  )
}

const CourseAction = (props: ActionProps) => {
  const {
    info: { courses, id },
    openModal,
    t,
  } = props

  const handleCourseSelect = (id: UUID, courses: UUID[]) => openModal(id, courses)

  const courseCountTitle = t('commons:selected_course_count', { count: courses?.length || 0 })

  return (
    <>
      <div
        className={cx('courseSelector', {
          active: !!courses?.length,
        })}
        onClick={() => handleCourseSelect(id, courses!)}
      >
        <span className={cx('course-title')}>{courseCountTitle}</span>
        <IconWrapper>
          <ArrowIcon />
        </IconWrapper>
      </div>
    </>
  )
}

const LetterAction = (props: ActionProps) => {
  const { info, updateAction, t } = props

  const handleChangeInput = (value: string, name: string) => {
    if (name === 'text') updateAction(info.id, { text: value })
    if (name === 'theme') updateAction(info.id, { theme: value })
  }

  return (
    <>
      <Input
        placeholder={t('commons:mail_theme')}
        classNameWrapper={cx('inputWrapper')}
        required
        name='theme'
        onChange={handleChangeInput}
        value={info.theme || ''}
        fullWidth
      />
      <Input
        placeholder={t('commons:write_message')}
        classNameWrapper={cx('inputWrapper')}
        required
        name='text'
        onChange={handleChangeInput}
        value={info.text || ''}
        fullWidth
      />
    </>
  )
}

const validateDays = (value: number, days: number): number => {
  if (isNaN(value)) return days
  if (value > 365) return 365
  if (value < 1) return 1
  return value
}

const hours = Array.from({ length: 24 }).map((_, i) => i)
// const minutes = [0, 15, 30, 45]

const RegistrationLinkAction = ({ info, t, updateAction }: ActionProps) => {
  const days = info?.registration_link_days || 1
  const [daysValue, setDaysValue] = useState<string>(String(days) || '1')
  const [selectedHours, setSelectedHours] = useState<number>(
    info?.registration_link_time ? Number(info.registration_link_time.split(':')[0]) : 0,
  )

  const handleChangeSelectedHours = (i: IListItem) => {
    const newValue = +i.id
    setSelectedHours(newValue)
    updateAction(info.id, { registration_link_time: `${newValue}:00` })
  }

  const onDaysInputChange = (value: string) => {
    const numberedValue = Number(value)
    const validatedValue = validateDays(numberedValue, days)
    setDaysValue(String(validatedValue))
    if (!isNaN(numberedValue)) {
      updateAction(info.id, { registration_link_days: validatedValue })
    }
  }

  const domain = t('commons:days_count', { count: days })

  return (
    <div className={cx('registrationLinkRow')}>
      {t('commons:send_registration_link_every')}
      <Input
        className={cx('registrationLinkInput')}
        value={String(daysValue)}
        onChange={onDaysInputChange}
        name='days'
        // eslint-disable-next-line i18next/no-literal-string
        inputMode='numeric'
        domain={domain}
        // onBlur={onDaysInputBlur}
        maxLength={3}
        classNameDomain={cx('registrationLinkInput__domain')}
      />
      {t('commons:send_registration_link_in')}

      <div className={cx('registrationLinkTime')}>
        <InputSelect
          className={cx('select')}
          list={hours.map(i => ({
            title: i.toString().padStart(2, '0'),
            id: i + '',
          }))}
          handleChange={handleChangeSelectedHours}
          value={selectedHours + ''}
          renderInput={inputProps => (
            <input
              {...inputProps}
              onChange={e => inputProps.onChange(e.target.value)}
              inputMode='numeric'
              min={0}
              max={23}
              maxLength={2}
              placeholder='0'
            />
          )}
        />
      </div>

      <span className={cx('hours')}>
        {t('commons:send_registration_link_hours', { count: selectedHours })}
      </span>
    </div>
  )
}

const ACTIONS_MAP: Record<ActionType, (props: ActionProps) => React.ReactElement> = {
  course: CourseAction,
  letter: LetterAction,
  none: () => <></>,
  phishing: PhishingAction,
  registration_link: () => <></>,
}

export const TagAction: FC<TagActionProps.Props> = props => {
  const { className, index, info, activeTag, openModal } = props
  const { id, type: initType } = info
  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const { t } = useTranslation()
  const actions = useAppSelector(selectTagsActions)
  const [type, setType] = useState<ActionType>(initType ? initType : 'none')
  const selectedCourses = useAppSelector(selectTagsSelectedCourses)
  const phishingTemplatesIds = useAppSelector(selectTagsPhishingTemplates)

  const DEFAULT_TAG_ACTIONS: TagListType = useMemo(
    () => [
      { id: 'none', title: t('commons:select_action'), isMulti: true },
      { id: 'course', title: t('commons:assign_course') },
      { id: 'letter', title: t('commons:send_message'), isMulti: true },
    ],
    [t],
  )

  const ADDITIONAL_PHISHING_ACTIONS: TagListType = useMemo(
    () => [{ id: 'phishing', title: t('commons:send_phishing_email') }],
    [t],
  )

  const SEND_REGISTRATION_LINK_ACTIONS: TagListType = useMemo(
    () => [{ id: 'registration_link', title: t('commons:send_registration_link') }],
    [t],
  )

  const tagActionByTagType = useMemo(() => {
    let result = [...DEFAULT_TAG_ACTIONS]

    if (!activeTag) return result

    if (
      activeTag?.type === ETagType.newbie ||
      activeTag?.type === ETagType.not_registered ||
      checkTagIsCustom(activeTag)
    ) {
      result = [...result, ...ADDITIONAL_PHISHING_ACTIONS]
    }

    if (activeTag?.type === ETagType.not_registered) {
      result = [...result, ...SEND_REGISTRATION_LINK_ACTIONS]
    }

    return result
  }, [ADDITIONAL_PHISHING_ACTIONS, DEFAULT_TAG_ACTIONS, SEND_REGISTRATION_LINK_ACTIONS, activeTag])

  useEffect(() => {
    setType(initType ? initType : 'none')
  }, [id])

  const getSelectedCoursesByType = useCallback(
    (actionType: ActionType) => {
      if (actionType === 'course') return selectedCourses
    },
    [selectedCourses],
  )

  const handleChange = useCallback(
    (item: IListItem) => {
      const newType = item.id as ActionType
      setType(() => newType)

      dispatch(
        updateAction({
          id: info.id,
          body: {
            type: newType,
            courses: getSelectedCoursesByType(newType),
          },
        }),
      )
    },
    [info.id],
  )

  const SELECT_LIST = useMemo(
    () =>
      tagActionByTagType.filter(tagAction => {
        const existAction = actions?.find(existAction => existAction.type === tagAction.id)

        if (existAction) {
          if (id === existAction.id) return true
          if (tagAction.isMulti) return true
          return false
        }

        return true
      }),
    [actions, id, tagActionByTagType],
  )

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('title')}>
        {t('commons:action')} {index + 1}
      </div>
      <IconWrapper size='24' className={cx('delete')} onClick={() => dispatch(deleteAction(id))}>
        <CloseBoldIcon />
      </IconWrapper>
      <Select
        placeholder={t('commons:select_action')}
        list={SELECT_LIST}
        value={type}
        handleChange={handleChange}
        className={cx('inputWrapper')}
      />
      {type === 'registration_link' && (
        <RegistrationLinkAction
          {...{
            info,
            openModal,
            navigate,
            updateAction: (id, body) => dispatch(updateAction({ id, body })),
            phishingTemplatesIds,
            t,
          }}
        />
      )}
      {ACTIONS_MAP[type]({
        info,
        openModal,
        navigate,
        updateAction: (id, body) => dispatch(updateAction({ id, body })),
        phishingTemplatesIds,
        t,
      })}
    </div>
  )
}
