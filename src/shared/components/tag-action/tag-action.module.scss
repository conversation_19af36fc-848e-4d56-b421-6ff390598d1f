.wrapper {
  background: var(--color-gray-40, #f0f3f7);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 12px;

  margin-bottom: 16px;
  padding: 16px 24px 24px 16px;
  position: relative;
  &:last-child {
    margin-bottom: 0;
  }
  .title {
    color: var(--color-gray-90, #343b54);
    font: var(--font-text-2-medium);
    margin-bottom: 12px;
  }
  .delete {
    cursor: pointer;
    position: absolute;
    right: 12px;
    top: 12px;
  }
}

.inputWrapper {
  display: block;
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: 0;
  }
}

.courseSelector {
  align-items: center;

  background: var(--color-surface, #fff);
  border: 1px solid var(--color-gray-30, #ebeff2);

  border-radius: 12px;
  cursor: pointer;
  display: grid;
  grid-gap: 10px;
  grid-template-columns: calc(100% - 30px) 20px;
  justify-content: space-between;
  padding: 15px 16px;
  .course-title {
    color: var(--color-gray-80);
    font: var(--font-text-2-medium);
  }
  svg {
    height: 20px;
    width: 20px;
  }

  &.active {
    .course-title {
      color: var(--color-primary, #3dbc87);
    }
  }
}

.phishing {
  display: flex;
  flex-direction: column;
  gap: 12px;
  &__link {
    margin-top: 12px;
  }
}

.link {
  background-color: var(--color-primary);
  padding: 12px 20px;
  border-radius: 12px;
  color: var(--white);
  font: var(--font-text-2-normal);
  transition: var(--transition);
  text-align: center;

  &:hover {
    color: var(--white);
    transform: scale(1.01);
  }

  &:active {
    color: var(--white);
  }
}

.inputDeferBy {
  border-radius: 8px;
  padding: 8px 50px 8px 12px !important;
  width: 88px;
}

.deferBy {
  align-items: center;

  color: var(--color-gray-90, #343b54);
  display: flex;
  font: var(--font-text-2-normal);
  gap: 8px;
  justify-content: space-between;
}

.registrationLinkRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-normal);
  white-space: nowrap;
}

.registrationLinkInput {
  width: 100%;
  &:has(+ .registrationLinkInput__domain) {
    padding-right: 50px;
  }
}

.registrationLinkTime {
  display: flex;
  align-items: center;
  gap: 4px;
}

.select {
  flex-shrink: 0;
  width: 70px;
  background-color: var(--white);
  border: 1px solid #ebeff2;
  border-radius: 12px;

  > * {
    padding: 12px;
  }
  &.disabled {
    background-color: var(--color-gray-20);
  }
}

.hours {
  width: 60px;
  flex-shrink: 0;
}
