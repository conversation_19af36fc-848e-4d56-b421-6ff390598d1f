import { checkTagIsCustom } from '@/shared/helpers/organization'
import { ETagType } from '@/shared/types/enums'
import { ActionType, ITag } from '@/shared/types/store/tag'
import { IListItem } from '@/shared/ui'

export type TagListType = (IListItem & { id: ActionType })[]

export const getTagActionsByTagType = (
  default_tag_actions: TagListType,
  additional_phishing_actions: TagListType,
  tag?: ITag,
): (IListItem & { id: ActionType })[] => {
  let result = [...default_tag_actions]

  if (!tag) return result

  if (
    tag?.type === ETagType.newbie ||
    tag?.type === ETagType.not_registered ||
    checkTagIsCustom(tag)
  ) {
    result = [...result, ...additional_phishing_actions]
  }

  return result
}
