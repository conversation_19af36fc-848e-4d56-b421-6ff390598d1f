import React, {
  createContext,
  ReactNode,
  useContext,
  useLayoutEffect,
  useRef,
  useState,
} from 'react'
import { useResizeObserver } from '@/shared/hooks/use-resize-observer'

type OvefrlowItem = {
  id: string | number
  content?: ReactNode
}

interface OverflowContextType {
  visibleItems: OvefrlowItem[]
  overflowItems: OvefrlowItem[]
}

const OverflowContext = createContext<OverflowContextType | null>(null)

// Хук для использования контекста
const useOverflow = () => {
  const context = useContext(OverflowContext)
  if (!context) {
    throw new Error('useOverflow must be used within an OverflowProvider')
  }
  return context
}

// Провайдер контекста
interface OverflowProviderProps {
  items: OvefrlowItem[]
  children: React.ReactNode
  customContainerRef?: HTMLDivElement
  gapSize?: number
  dropdownDataId?: string
  className?: string
  additionalWidth?: number
}

export const OverflowProvider: React.FC<OverflowProviderProps> = ({
  customContainerRef,
  items,
  children,
  gapSize,
  dropdownDataId = 'dropdown',
  className,
  additionalWidth,
}) => {
  const [hiddenItems, setHiddenItems] = useState<Record<number | string, boolean>>({})
  const [containerWidth, setContainerWidth] = useState(0)
  const GAP_SIZE = gapSize ?? 24

  const containerRef = useRef<HTMLDivElement>(null)

  useResizeObserver(containerRef, entries => {
    const entry = entries[0]
    if (containerWidth === entry.contentRect.width) return

    setContainerWidth(entry.contentRect.width)
    setHiddenItems({})
  })

  useLayoutEffect(() => {
    const containerElement = containerRef.current
    const containerChildren = containerElement?.children

    if (!containerChildren) return

    const elementWidths: number[] = []
    let dropdownMenuWidth = 0

    for (let i = 0, l = containerChildren.length; i < l; i++) {
      const child = containerChildren[i]

      const clientRect = child.getBoundingClientRect()

      const { width } = clientRect

      const totalWidth = i > 0 ? GAP_SIZE + width : width

      if (child.getAttribute('data-id') === dropdownDataId) {
        dropdownMenuWidth = totalWidth
      } else {
        elementWidths.push(totalWidth)
      }
    }

    const hiddenItemsMap: Record<number | string, boolean> = {}
    let remaningContainerWidth = containerWidth - dropdownMenuWidth - (additionalWidth ?? 0)

    items.forEach((item, index) => {
      const itemWidth = elementWidths[index]

      if (itemWidth <= remaningContainerWidth) {
        remaningContainerWidth -= itemWidth
      } else {
        hiddenItemsMap[item.id] = true
      }
    })

    setHiddenItems(hiddenItemsMap)
  }, [items, containerWidth, GAP_SIZE, dropdownDataId, additionalWidth])

  return (
    <OverflowContext.Provider
      value={{
        visibleItems: items.filter(i => !hiddenItems[i.id]),
        overflowItems: items.filter(i => hiddenItems[i.id]),
      }}
    >
      <div
        className={className}
        style={{ display: 'flex' }}
        ref={customContainerRef ? undefined : containerRef}
      >
        {children}
      </div>
    </OverflowContext.Provider>
  )
}

export const OverflowMenu: React.FC<{
  renderDropdown?: (items: OvefrlowItem[]) => ReactNode
}> = ({ renderDropdown }) => {
  const { visibleItems, overflowItems } = useOverflow()

  return (
    <>
      {visibleItems.map(item => item.content)}
      {overflowItems.length > 0 && renderDropdown?.(overflowItems)}
    </>
  )
}
