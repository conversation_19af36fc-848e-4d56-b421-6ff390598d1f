import { FC } from 'react'
import styles from './select-card.module.scss'
import classNamesBind from 'classnames/bind'
import { SelectCardProps } from './select-card.d'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const SelectCard: FC<SelectCardProps.Props> = props => {
  const { t } = useTranslation()
  const { className, selectedItems, label, text, onDeleteItem, onMainClick } = props

  return (
    <div className={cx('wrapper')}>
      {label && <h2 className={cx('label')}>{label}</h2>}
      <div className={cx('main__wrapper', className)}>
        <div onClick={onMainClick} className={cx('main')}>
          <p className={cx('text')}>{text || `${t('commons:select_required_item')}...`}</p>
          <IconWrapper className={cx('icon')} size='20' color='primary'>
            <PlusIcon />
          </IconWrapper>
        </div>
        {selectedItems && selectedItems.length > 0 && (
          <ul className={cx('selected__list')}>
            {selectedItems.map(i => (
              <li key={i.id} className={cx('selected__list__item')}>
                {i.icon}
                <p className={cx('selected__list__item__text')}>{i.text}</p>
                {i.isDeleted && (
                  <IconWrapper
                    title={t('commons:delete_attachment')}
                    role='button'
                    className={cx('close-icon')}
                    onClick={() => {
                      if (onDeleteItem) onDeleteItem(i)
                    }}
                  >
                    <CloseBoldIcon />
                  </IconWrapper>
                )}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  )
}
