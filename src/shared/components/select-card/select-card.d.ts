import { ReactNode } from "react";

export declare namespace SelectCardProps {
  type SelectedItem = {
    id: string;
    icon?: ReactNode;
    text?: string;
    isDeleted?: boolean;
  };
  interface Own {
    className?: string;
    selectedItems?: SelectedItem[];
    label?: string;
    text?: string;
    onDeleteItem?: (item: SelectedItem) => void;
    onMainClick?: () => void;
  }

  type Props = Own;
}

export {};
