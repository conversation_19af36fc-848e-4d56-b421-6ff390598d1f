.label {

  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
  margin-bottom: 8px;
}

.text {
  color: var(--color-primary);

  font: var(--font-text-2-normal);
}

.main {
  align-items: center;

  border-radius: 12px;
  cursor: pointer;

  display: flex;
  height: 48px;
  justify-content: space-between;
  padding: 0 16px;

  transition: 200ms ease;
  &__wrapper {
    background: var(--color-surface);
    border-radius: 12px;
  }

  &:hover {
    background-color: var(--color-gray-20);
  }
}

.selected__list {

  background: var(--color-surface);
  border-bottom-left-radius: 16px;

  border-bottom-right-radius: 16px;
  display: flex;
  flex-direction: column;
  padding: 4px 16px;

  &__item {
    align-items: center;

    background-color: var(--white);
    display: flex;
    gap: 8px;

    &__text {
      color: var(--color-gray-90);
      font: var(--font-caption-1-normal);
    }
  }
}

.close-icon {

  cursor: pointer;
  margin-left: auto;
}
