import { FC } from "react";
import styles from "./template-logo.module.scss";
import classNamesBind from "classnames/bind";
import { TemplateLogoProps } from "./template-logo.d";

const cx = classNamesBind.bind(styles);

export const TemplateLogo: FC<TemplateLogoProps.Props> = (props) => {
  const { className, src, alt, name } = props;

  return (
    <div className={cx("wrapper", className)}>
      {src ? <img src={src} alt={alt} /> : <div>{name ? name[0] : "N"}</div>}
    </div>
  );
};
