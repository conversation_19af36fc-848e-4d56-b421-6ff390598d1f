.wrapper {
  display: flex;
  flex-direction: column;
  background: #f0f3f7;
  max-width: 100vw;
  width: 100%;
  min-height: 100dvh;
  .inner {
    display: grid;
    grid-gap: 80px;
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    height: 100%;
    margin: 0 auto;
    max-width: 296px;
    width: 100%;
    padding: 80px 0;

    @media (max-width: 1024px) {
      max-width: 362px;
    }
  }
  .form-wrapper {
    align-self: start;

    background: #fff;
    border: 1px solid #ebeff2;
    border-radius: 16px;
    display: flex;
    padding: 20px 24px;
  }
  .bottom-wrapper {
    max-width: 185px;
    margin-top: auto;
  }
}
