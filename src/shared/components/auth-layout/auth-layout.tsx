import { FC } from 'react'
import styles from './auth-layout.module.scss'
import classNamesBind from 'classnames/bind'
import { AuthLayoutProps } from './auth-layout.d'
import { Logo } from '@/shared/components'
import { LangSwitcher } from '../lang-switcher/lang-switcher'

const cx = classNamesBind.bind(styles)

export const AuthLayout: FC<AuthLayoutProps.Props> = props => {
  const { children, className, classNameWrapper, classNameInner, needLangSwitcher = true } = props

  return (
    <div className={cx('wrapper', classNameWrapper)}>
      <div className={cx('inner', classNameInner)}>
        <Logo className={cx('logo')} height={50} />
        <div className={cx('form-wrapper', className)}>{children}</div>
      </div>
      {import.meta.env.MODE !== 'production' && needLangSwitcher && (
        <div className={cx('bottom-wrapper')}>
          <LangSwitcher />
        </div>
      )}
    </div>
  )
}
