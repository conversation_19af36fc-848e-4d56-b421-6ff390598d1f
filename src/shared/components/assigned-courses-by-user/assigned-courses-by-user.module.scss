@use '../../../shared/assets/styles/mixins/media';

.content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.courses {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  align-items: flex-end;

  @include media.mqXl {
    grid-template-columns: repeat(2, 1fr);
  }
}

div.columnCard {
  align-items: flex-end;
}

.totalCoursesStats {
  display: flex;
}

.info-message {
  font: var(--font-text-1-medium);
  color: var(--color-gray-90);
  text-align: center;
}

.filterTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
