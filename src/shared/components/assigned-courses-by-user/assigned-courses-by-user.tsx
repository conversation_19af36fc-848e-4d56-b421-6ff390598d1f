import classNamesBind from 'classnames/bind'
import styles from './assigned-courses-by-user.module.scss'
import { AssignedCoursesStats } from '../assigned-courses-stats/assigned-courses-stats'
import { Tabs } from '../tabs'
import InfiniteScroll from 'react-infinite-scroll-component'
import { AssignedCourseCard } from '../assigned-course-card/assigned-course-card'
import { getAssignedCoursesDetailStatisticsByIdUrl } from '@/shared/configs/urls'
import { CourseStatistics } from '@/entities/courses'
import { ReactNode, useEffect, useMemo, useState } from 'react'
import {
  AssignedCourseByUser,
  UserAssignedCoursesStatus,
} from '@/entities/courses/model/api/test-types'
import { useSearchParams } from 'react-router-dom'
import { userStaticticsApi } from '@/pages/user/learning/endpoints'
import { useTranslation } from 'react-i18next'
import { v4 as uuid } from 'uuid'
import Skeleton from 'react-loading-skeleton'
import { formatDateDifference } from '@/shared/helpers/date'
import { isNumber } from '@/shared/helpers'
import { Tag } from '@/shared/components/tag'
import { COURSE_TAG_COLOR } from '@/shared/constants'

const cx = classNamesBind.bind(styles)

type Props = {
  employeeId: string
  statPosition?: 'top' | 'middle'
  howMuchMaxToShow?: number
  titleWhenCanShowMore?: ReactNode
}

const LIMIT = 12
const queryTypeName = 'coursesByUserType'

export const AssignedCoursesByUser = ({
  employeeId,
  statPosition = 'top',
  howMuchMaxToShow,
  titleWhenCanShowMore,
}: Props) => {
  const [currentQueryParameters, setSearchParams] = useSearchParams()
  const { t } = useTranslation('components__assigned-courses-by-user')

  const [page, setPage] = useState(1)
  const [renderCourses, setRenderCourses] = useState<AssignedCourseByUser[] | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [filterTags, setFilterTags] = useState<string[]>([])

  const rawType = (currentQueryParameters.get(queryTypeName) as string) || 'active'
  const typeForApi: UserAssignedCoursesStatus = rawType === 'completed' ? 'completed' : 'active'

  const handleTagClick = (tagName: string) => {
    if (filterTags.includes(tagName)) return

    setFilterTags(prev => [...prev, tagName])
  }

  const handleFilterTagDelete = (tagName: string) => {
    setFilterTags(prevTags => prevTags.filter(prevTag => prevTag !== tagName))
  }

  const { data: employeePassedStat } = userStaticticsApi.useGetUserPassedStatQuery({
    user_id: employeeId,
    status: typeForApi,
  })

  const {
    data: employeeAssignedCourses,
    isLoading: isEmployeeAssignedCoursesLoading,
    isFetching: isEmployeeAssignedCoursesFetching,
    isError: isEmployeeAssignedCoursesError,
  } = userStaticticsApi.useGetUserProgressByAssignedCoursesQuery({
    user_id: employeeId,
    status: typeForApi,
    limit: howMuchMaxToShow || LIMIT,
    offset: (page - 1) * (howMuchMaxToShow || LIMIT),
  })

  useEffect(() => {
    if (
      employeeAssignedCourses?.data &&
      !(isEmployeeAssignedCoursesLoading || isEmployeeAssignedCoursesFetching)
    ) {
      const filteredData = employeeAssignedCourses.data.filter(item => {
        if (rawType === 'shared' && item.visibility !== 'shared') return false
        if (rawType === 'active' && item.visibility === 'shared') return false

        return true
      })

      setRenderCourses(prevCourses => {
        if (!prevCourses) return [...filteredData]
        const uniqueIds: { [key: string]: boolean } = {}

        return [...prevCourses, ...filteredData].filter(item => {
          if (uniqueIds[item.assigned_course_id]) {
            return false
          } else {
            uniqueIds[item.assigned_course_id] = true
            return true
          }
        })
      })

      const usedLimit = howMuchMaxToShow || LIMIT
      setHasMore(!howMuchMaxToShow && employeeAssignedCourses.data.length >= usedLimit)
    }
  }, [
    isEmployeeAssignedCoursesFetching,
    isEmployeeAssignedCoursesLoading,
    employeeAssignedCourses,
    howMuchMaxToShow,
    rawType,
  ])

  const filteredRenderCourses = useMemo(() => {
    if (!renderCourses) return null

    if (filterTags.length === 0) return renderCourses

    return renderCourses.filter(item => {
      const courseTags = item.tags?.filter(Boolean) || []
      return filterTags.some(filterTag => courseTags.includes(filterTag))
    })
  }, [renderCourses, filterTags])

  const tabs = useMemo(
    () => [
      {
        value: t('tabs.active'),
        name: 'active',
      },
      {
        value: t('tabs.completed'),
        name: 'completed',
      },
      {
        value: t('tabs.shared'),
        name: 'shared',
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  )

  const handleTabClick = (value: string) => {
    if (!value || value === rawType) return
    const newQueryParameters: URLSearchParams = new URLSearchParams()

    newQueryParameters.set(queryTypeName, value)
    setPage(1)
    setRenderCourses(null)
    setHasMore(true)
    setSearchParams(newQueryParameters)
  }

  const isCanLoadMore = useMemo(() => {
    if (howMuchMaxToShow) return false

    if (filterTags.length > 0) return false

    return hasMore
  }, [howMuchMaxToShow, hasMore, filterTags.length])

  return (
    <div className={cx('content')}>
      {statPosition === 'top' ? (
        <>
          <AssignedCoursesStats stats={employeePassedStat} />
          <Tabs
            tabs={tabs}
            onClick={handleTabClick}
            active={rawType}
            backAdornment={titleWhenCanShowMore}
          />
        </>
      ) : (
        <>
          <Tabs
            tabs={tabs}
            onClick={handleTabClick}
            active={rawType}
            backAdornment={titleWhenCanShowMore}
          />
          <AssignedCoursesStats stats={employeePassedStat} />
        </>
      )}

      {filterTags.length > 0 && (
        <div className={cx('filterTags')}>
          {filterTags.map(filterTag => (
            <Tag
              key={filterTag}
              info={{ id: filterTag, title: filterTag, color: COURSE_TAG_COLOR }}
              onDelete={() => handleFilterTagDelete(filterTag)}
              isActive
            />
          ))}
        </div>
      )}

      {(isEmployeeAssignedCoursesLoading || !renderCourses) && !isEmployeeAssignedCoursesError && (
        <ul className={cx('courses')}>
          <CoursesLoadingSkeleton />
        </ul>
      )}
      {isEmployeeAssignedCoursesError && (
        <p className={cx('info-message')}>{t('commons:error_occurred_while_receiving_data')}</p>
      )}

      {renderCourses &&
        renderCourses.length === 0 &&
        !isEmployeeAssignedCoursesLoading &&
        !isEmployeeAssignedCoursesFetching && <p className={cx('info-message')}>{t('empty')}</p>}

      {renderCourses &&
        renderCourses.length > 0 &&
        filteredRenderCourses &&
        filteredRenderCourses.length === 0 &&
        filterTags.length > 0 &&
        !isEmployeeAssignedCoursesLoading &&
        !isEmployeeAssignedCoursesFetching && (
          <p className={cx('info-message')}>{t('commons:no_courses_with_selected_tags')}</p>
        )}

      {!isEmployeeAssignedCoursesLoading &&
        filteredRenderCourses &&
        filteredRenderCourses.length > 0 && (
          <InfiniteScroll
            className={cx('courses')}
            dataLength={filteredRenderCourses.length}
            next={() => {
              if (filterTags.length === 0) {
                setPage(prevPage => prevPage + 1)
              }
            }}
            hasMore={isCanLoadMore}
            loader={<CoursesLoadingSkeleton />}
            scrollableTarget={'page-wrapper'}
          >
            {filteredRenderCourses?.map(course => {
              const dateDiff = course.course_end_date
                ? formatDateDifference(new Date(), new Date(course.course_end_date))
                : null

              return (
                <AssignedCourseCard
                  key={course.assigned_course_id}
                  to={getAssignedCoursesDetailStatisticsByIdUrl(course.assigned_course_id)}
                  title={course.course_title}
                  pictureSrc={course.picture}
                  tags={course.tags}
                  onTagClick={handleTagClick}
                  footerCols={[
                    {
                      id: uuid(),
                      title: typeForApi === 'active' && dateDiff && (
                        <div>
                          {dateDiff?.day && t('days', { count: Number(dateDiff?.days) })}
                          {!dateDiff?.day &&
                            dateDiff?.hour &&
                            isNumber(dateDiff?.hours) &&
                            t('hour', { count: Number(dateDiff?.hours) })}
                        </div>
                      ),
                      value: <CourseStatistics testing={course.quiz} theory={course.theory} />,
                      columnClassName: cx('columnCard'),
                    },
                  ]}
                />
              )
            })}
          </InfiniteScroll>
        )}
    </div>
  )
}

const CoursesLoadingSkeleton = () => (
  <>
    <Skeleton height={'196px'} />
    <Skeleton height={'196px'} />
    <Skeleton height={'196px'} />
  </>
)
