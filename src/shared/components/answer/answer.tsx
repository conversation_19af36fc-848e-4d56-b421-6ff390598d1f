import classNamesBind from 'classnames/bind'
import React, { useCallback } from 'react'
import Skeleton from 'react-loading-skeleton'
import { AnswerProps } from './answer.d'
import styles from './answer.module.scss'
import { Checkbox } from '@/shared/ui/checkbox'

const cx = classNamesBind.bind(styles)

export const AnswerNew: React.FC<AnswerProps.Props> = props => {
  const { className, answer, active, onClick, multiple } = props

  const { id, text } = answer || {}

  const handleClick = useCallback(() => {
    id && onClick(id)
  }, [id, onClick])

  return (
    <div className={cx('wrapper', className)}>
      <Checkbox
        type={multiple ? 'square' : 'circle'}
        checkboxClassName={cx('checkbox')}
        iconClassName={cx('icon')}
        label={<div className={cx('text')}>{text || <Skeleton width='60%' />}</div>}
        onClick={handleClick}
        customChecked={active}
      />
    </div>
  )
}
