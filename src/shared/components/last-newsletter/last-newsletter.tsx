import { FC } from 'react'
import styles from './last-newsletter.module.scss'
import classNamesBind from 'classnames/bind'
import { LastNewsletterProps } from './last-newsletter.d'
import { Card } from '@/shared/ui'
import ArrowIcon from '@/shared/ui/Icon/icons/components/ArrowIcon'
import EmailPostedIcon from '@/shared/ui/Icon/icons/components/EmailPostedIcon'
import EmailOpenBoldIcon from '@/shared/ui/Icon/icons/components/EmailOpenBoldIcon'
import Web1BoldIcon from '@/shared/ui/Icon/icons/components/Web1BoldIcon'
import DataEnteredIcon from '@/shared/ui/Icon/icons/components/DataEnteredIcon'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { NavLink } from 'react-router-dom'
import { fromISO } from '@/shared/helpers/date'
import { useTranslation } from 'react-i18next'
import Skeleton from 'react-loading-skeleton'

const cx = classNamesBind.bind(styles)

export const LastNewsletter: FC<LastNewsletterProps.Props> = props => {
  const {
    sent,
    opened,
    clicked,
    entered_data,
    opened_attachment,
    isLoading,
    incident_risk,
    id,
    start_date,
    showLink = true,
    emptyNode,
  } = props

  const { t } = useTranslation()

  const getIncidentColor = () => {
    if (!incident_risk && incident_risk !== 0) return ''

    if (incident_risk > 50) {
      return 'red'
    } else if (incident_risk > 30) {
      return 'yellow'
    } else {
      return 'green'
    }
  }

  const incidentColor = getIncidentColor()

  const date = start_date ? fromISO(start_date, 'dd.MM.yyyy') : ''

  if (!isLoading && !id) {
    return (
      <Card className={cx('wrapper', 'wrapper_centered')} padding='normal'>
        {emptyNode ?? <p>{t('commons:no_data')}</p>}
      </Card>
    )
  }

  return (
    <Card className={cx('wrapper')} padding='normal'>
      <div className={cx('top')}>
        <div className={cx('title')}>{t('commons:latest_newsletter')}</div>
        {id && showLink && (
          <NavLink to={`/lk/admin/phishing/campaigns/${id}`}>
            <IconWrapper>
              <ArrowIcon />
            </IconWrapper>
          </NavLink>
        )}
      </div>
      <div className={cx('list')}>
        <div className={cx('line')}>
          <IconWrapper>
            <EmailPostedIcon />
          </IconWrapper>
          <div className={cx('note')}>{t('commons:letter_sent')}</div>
          <span>{isLoading ? <Skeleton width={40} height={16} /> : sent}</span>
        </div>
        <div className={cx('line')}>
          <IconWrapper>
            <EmailOpenBoldIcon />
          </IconWrapper>
          <div className={cx('note')}>{t('commons:opened')}</div>
          <span>{isLoading ? <Skeleton width={40} height={16} /> : opened}</span>
        </div>
        <div className={cx('line')}>
          <IconWrapper>
            <Web1BoldIcon />
          </IconWrapper>
          <div className={cx('note')}>{t('commons:transition')}</div>
          <span>{isLoading ? <Skeleton width={40} height={16} /> : clicked}</span>
        </div>
        <div className={cx('line')}>
          <IconWrapper>
            <DataEnteredIcon />
          </IconWrapper>
          <div className={cx('note')}>{t('commons:entereds_data')}</div>
          <span>{isLoading ? <Skeleton width={40} height={16} /> : entered_data}</span>
        </div>
        <div className={cx('line')}>
          <IconWrapper>
            <ClipBoldIcon />
          </IconWrapper>
          <div className={cx('note')}>{t('commons:openeds_attachments')}</div>
          <span>{isLoading ? <Skeleton width={40} height={16} /> : opened_attachment}</span>
        </div>
      </div>
      <div className={cx('bottom')}>
        <div className={cx('incident_risk', incidentColor)}>
          {t('commons:incident')}{' '}
          {isLoading ? (
            <Skeleton width={40} height={16} />
          ) : incident_risk ? (
            Number(incident_risk?.toFixed(1)) + '%'
          ) : (
            0 + '%'
          )}
        </div>
        <div className={cx('start_date')}>{date}</div>
      </div>
    </Card>
  )
}
