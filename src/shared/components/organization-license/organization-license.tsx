import { FC } from 'react'
import styles from './organization-license.module.scss'
import classNamesBind from 'classnames/bind'
import { OrganizationLicenseProps } from './organization-license.d'
import LicensesIcon from '@/shared/ui/Icon/icons/components/LicensesIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import { useGetBeautifulDateWithMonth } from '@/shared/hooks'

const cx = classNamesBind.bind(styles)

export const OrganizationLicense: FC<OrganizationLicenseProps.Props> = props => {
  const { t } = useTranslation()
  const { className, end_date, start_date, users_count, users_limit } = props

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('title')}>
        <span>{t('commons:employees_limit')}</span>
        <div className={cx('icon')}>
          <IconWrapper color='white'>
            <LicensesIcon />
          </IconWrapper>
        </div>
      </div>
      <div className={cx('count')}>
        {users_count} {t('commons:of')} {users_limit}
      </div>
      <div className={cx('date')}>
        <BeautifulDate date={start_date} /> — <BeautifulDate date={end_date} />
      </div>
    </div>
  )
}

const BeautifulDate = (props: { date: string }) => {
  const beautifulDate = useGetBeautifulDateWithMonth(props.date)
  return beautifulDate
}
