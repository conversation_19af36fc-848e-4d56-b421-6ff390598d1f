/* eslint-disable @typescript-eslint/no-explicit-any */
import styles from './reports-table.module.scss'
import classNamesBind from 'classnames/bind'
import { FileIcon, Loader } from '@/shared/ui'
import LoadIcon from '@/shared/ui/Icon/icons/components/LoadIcon'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import FileIcon2 from '@/shared/ui/Icon/icons/components/FileIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useReportsTable } from './use-reports-table'
import { IReport } from '@/shared/types/store/reports'
import { memo, useMemo } from 'react'
import { EStatus } from '@/shared/types/enums'
import { getExtensionFromUrl } from '@/shared/helpers'
import { getCorrectFilename } from './helper'
import { useTranslation } from 'react-i18next'
import { DOT_DATE_FORMAT, format, TIME_FORMAT } from '@/shared/helpers/date'
// import { getCorrectFilePathWithExt } from "./helper";

const cx = classNamesBind.bind(styles)

export type ReportsTableProps = {
  data?: IReport[]
  isLoading?: boolean
  isError?: boolean
}

const FILE_BY_ANY_REASON: Record<string, React.ReactElement> = {
  pdf: <FileIcon type={'PDF'} size='32' />,
  xlsx: <FileIcon type={'XLSX'} size='32' />,
  docx: <FileIcon type={'DOCX'} size='32' />,
}

export const ReportsTable: React.FC<ReportsTableProps> = memo(({ data, isLoading, isError }) => {
  const { download } = useReportsTable()
  const { t } = useTranslation('components__report-table')

  const REPORTS_STATUSES = useMemo<Record<EStatus, string>>(() => {
    return {
      complete: t('commons:done'),
      error: t('commons:error'),
      in_progress: t('commons:in_progress'),
    }
  }, [t])

  return (
    <table className={cx(styles.table, isLoading && 'table__opacity')}>
      <thead>
        <tr>
          <th className={cx('file-cell')}>{t('commons:file')}</th>
          <th className={cx('date-cell')}>{t('commons:date')}</th>
          <th className={cx('name-cell')}>{t('commons:filename')}</th>
          <th className={cx('status-cell')}>{t('commons:status')}</th>
          <th className={cx('open-cell')}>{t('commons:open')}</th>
        </tr>
      </thead>
      <tbody>
        {!isError && !isLoading && !data?.length && (
          <tr className={cx('plug__row')}>
            <td className={cx('plug')} colSpan={4}>
              <div className={cx('no-data')}>{t('commons:no_data')}</div>
            </td>
          </tr>
        )}
        {isLoading && (
          <tr className={cx('plug__row')}>
            <td className={cx('plug')} colSpan={4}>
              <Loader size='56' className='loader_centered' loading={true} />
            </td>
          </tr>
        )}
        {(isError || isLoading) && (
          <tr className={cx('plug__row')}>
            <td className={cx('plug')} colSpan={4}>
              <Loader error={true} size='56' loading={false} />
            </td>
          </tr>
        )}
        {!isError &&
          data?.map(report => {
            const ext = getExtensionFromUrl(report?.url || '')

            return (
              <tr key={report.id} className={cx('table-row')}>
                <td className={cx('file-cell')}>
                  {report.status === EStatus.in_progress && (
                    <IconWrapper color='gray80' size='24' className='animate-spin'>
                      <LoadIcon />
                    </IconWrapper>
                  )}
                  {report.status === EStatus.error && (
                    <IconWrapper color='red' size='24'>
                      <CloseBoldIcon />
                    </IconWrapper>
                  )}
                  {report.status !== EStatus.in_progress &&
                    report.status !== EStatus.error &&
                    ext &&
                    FILE_BY_ANY_REASON[ext]}
                </td>
                <td className={cx('date-cell')}>
                  {report.created_at ? (
                    <>
                      <p className={cx(styles.trancate, report.status)}>
                        {format(new Date(report.created_at), DOT_DATE_FORMAT)}
                      </p>
                      <p className={cx(styles.trancate, report.status)}>
                        {format(new Date(report.created_at), TIME_FORMAT)}
                      </p>
                    </>
                  ) : (
                    <p className={cx(styles.trancate, report.status)}>
                      {t('commons:  no_information')}
                    </p>
                  )}
                </td>
                <td className={cx('name-cell')}>
                  <p className={cx(styles.trancate, report.status)}>
                    {getCorrectFilename(report?.filename) || (
                      <span className={cx('underline')}>{t('error_filename')}</span>
                    )}
                  </p>
                </td>
                <td className={cx('status-cell')}>
                  <p className={cx(styles.trancate, report.status)}>
                    {REPORTS_STATUSES[report.status]}
                  </p>
                </td>
                <td className={cx('open-cell')}>
                  <IconWrapper
                    onClick={() => {
                      if (report.status !== EStatus.complete) return

                      if (report?.url) {
                        download(
                          report.url,
                          report.filename || report.id + '.' + ext?.toLocaleLowerCase(),
                        )
                      }
                    }}
                    color={
                      report.status === EStatus.error
                        ? 'red'
                        : report.status !== EStatus.complete
                          ? 'gray60'
                          : 'primary'
                    }
                    size='28'
                    className={cx(
                      'download__icon',
                      report.status !== EStatus.complete && 'disabled',
                    )}
                  >
                    <FileIcon2 />
                  </IconWrapper>
                </td>
              </tr>
            )
          })}
      </tbody>
    </table>
  )
})
