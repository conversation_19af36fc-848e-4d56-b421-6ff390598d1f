@use './../../../shared/assets/styles/mixins/text';

.table {
  background-color: var(--white);
  border-radius: 16px;
  padding: 12px 16px;
  width: 100%;

  .file-cell {
    width: 0%;
    margin-left: auto;
    margin-right: auto;
  }
  .name-cell {
    width: 65%;
  }
  .status-cell {
    width: 5%;
  }
  .download-cell {
    width: 5%;
  }
  .open-cell {
    width: 5%;
  }

  tbody {
    color: var(--color-gray-90);
    font: var(--font-caption-1-medium);
    position: relative;

    tr {
      border-top: 1px solid var(--color-gray-30);
      padding: 12px 16px;
    }
    td {
      padding: 12px 16px;

      vertical-align: middle;
    }
  }
  thead {
    color: var(--color-gray-70);

    font: var(--font-caption-1-medium);
    padding: 12px 16px;

    th {
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      padding: 12px 16px;
      text-transform: uppercase;

      text-align: start;
    }
  }

  &__opacity {
    opacity: 0.7;
  }
}

.underline {
  text-decoration: underline;
}

.download__icon {
  width: 100%;
  margin-right: auto;
  margin-left: 4px;
  cursor: pointer;
}

.trancate {
  @include text.max-lines(2);
}

.plug {
  &__row {
    min-height: 300px;
  }
  width: 100%;
  text-align: center;
  font: var(--font-title-3-normal);
  color: var(--color-gray-70);
}

.error {
  color: var(--color-error);
}

.complete {
  color: var(--color-primary);
}

.in_progress {
  color: var(--color-statistics-warning-text);
}

.disabled {
  cursor: not-allowed;
}

.no-data {
  height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
}
