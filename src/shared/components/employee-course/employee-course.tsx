import classNamesBind from "classnames/bind";
import React from "react";
import { Card } from "@/shared/ui";
import Skeleton from "react-loading-skeleton";
import { EmployeeCourseProps } from "./employee-course.d";
import styles from "./employee-course.module.scss";
import { useTranslation } from "react-i18next";
import { IEmployeeCourse, IMyScormCourse } from "@/shared/types/store/course";

const cx = classNamesBind.bind(styles);

const EmployeeCourse: React.FC<EmployeeCourseProps.Props> = (props) => {
  const { className, course, to } = props;

  const isScorm = !!(course as IMyScormCourse).assigned_course;

  const learningProgress =
    (isScorm
      ? (course as IMyScormCourse).statistics.learning
      : (course as IEmployeeCourse).passed_theory_percent?.toFixed()) || 0;
  const testingProgress =
    (isScorm
      ? (course as IMyScormCourse).statistics.quiz
      : (course as IEmployeeCourse).passed_tests_percent?.toFixed()) || 0;
  const title =
    (isScorm
      ? (course as IMyScormCourse).assigned_course.title
      : (course as IEmployeeCourse).title) || "";
  const picture =
    (isScorm
      ? (course as IMyScormCourse).assigned_course.picture
      : (course as IEmployeeCourse).picture) || "";

  const loading = !course;

  const { t } = useTranslation();

  return (
    <Card
      padding="normal"
      className={cx("wrapper", className, {
        disabled: isScorm,
      })}
      to={course && !isScorm ? to : undefined}
    >
      <div
        className={cx("background")}
        style={
          picture
            ? { backgroundImage: `url("${picture}")`, backgroundSize: "cover" }
            : undefined
        }
      />
      <div className={cx("content")}>
        <div className={cx("title")}>{title || <Skeleton width="100%" />}</div>
        <div className={cx("info")}>
          <div className={cx("field")}>
            <div className={cx("field")}>
              {loading ? (
                <Skeleton width="80%" />
              ) : (
                t("components.employee_course.learning", {
                  progress: learningProgress,
                })
              )}
            </div>
            {loading ? (
              <Skeleton width="80%" />
            ) : (
              t("components.employee_course.testing", {
                progress: testingProgress,
              })
            )}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default EmployeeCourse;
