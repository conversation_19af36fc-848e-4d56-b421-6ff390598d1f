@use "../../../shared/assets/styles/mixins/text";

.wrapper {

  border: 1px solid #e5e5e5;
  border-radius: 10px;
  box-sizing: border-box;

  display: flex;
  flex: 1 0 330px;
  flex-direction: column;
  height: 250px;
  min-width: 330px;
  overflow: hidden;
  position: relative;
  &:not(.disabled) {
    cursor: pointer;

    transition: 200ms ease;
    &:hover {
      transform: translate3d(0, 0, 0) scale(1.03);
    }
  }
}

.background {

  background-size: cover;

  display: flex;
  height: 100%;
  left: 0;
  opacity: 0.3;
  position: absolute;
  top: 0;
  width: 100%;
}

.content {

  box-sizing: border-box;
  height: 100%;
  left: 0;
  overflow: hidden;
  padding: 30px 40px;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
}

.title {
  @include text.title(25px);
  @include text.max-lines(3);
}

.field {
  @include text.title(20px);

  font-weight: normal;
  line-height: 140%;

  margin-top: 15px;
  + .field {
    margin-top: 5px;
  }
}
