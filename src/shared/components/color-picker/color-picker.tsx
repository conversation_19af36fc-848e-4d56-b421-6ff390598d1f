import { memo, useEffect, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './color-picker.module.scss'
const cx = classNamesBind.bind(styles)

type Props = {
  initColor: string
  disabled?: boolean
  onChange?: (color: string) => void
  className?: string
}

export const ColorPicker = memo(({ initColor, className, disabled, onChange }: Props) => {
  const [color, setColor] = useState(initColor)

  useEffect(() => {
    if (onChange) {
      onChange(color)
    }
  }, [color, onChange])

  return (
    <input
      type='color'
      className={cx('base', className)}
      value={initColor}
      onChange={evt => setColor(evt.currentTarget.value)}
      disabled={disabled}
    />
  )
})

ColorPicker.displayName = 'ColorPicker'
