.twofa {
  display: flex;
  gap: 12px;

  > * {
    width: 42px;
  }
  * {
    font-size: 18px;
  }
}

@keyframes blink {
  0%,
  100% {
    border-color: transparent;
  }
  50% {
    border-color: var(--color-error);
  }
}

.input {
  appearance: none;

  background: #fff;
  border: 1px solid #ebeff2;
  border-radius: 12px;

  box-shadow: 0 0 0 2px transparent, 0 0 0 50px transparent inset !important;
  box-shadow: 0 0 0 2px transparent, 0 0 0 50px transparent inset !important;
  box-sizing: border-box;

  color: var(--color-gray-90);
  font: var(--font-text-2-medium);

  max-width: 100%;
  outline: none;

  padding: 13px 14px;

  transition: ease 0.15s, margin 0s;

  &:-webkit-autofill {
    box-shadow: 0 0 0 2px transparent, 0 0 0 50px #fff inset !important;
    box-shadow: 0 0 0 2px transparent, 0 0 0 50px #fff inset !important;

    transition: ease 0.15s, margin 0s;
  }

  &::input-placeholder,
  &:placeholder,
  &::placeholder,
  &:input-placeholder,
  &::placeholder {
    color: var(--color-gray-70);
    font: var(--font-text-2-normal);
  }

  &:hover {
    border: 1px solid #c9cedc;

    transition: ease 0.15s, margin 0s;
  }

  &:focus,
  &:-webkit-autofill:focus {
    border: 1px solid #fff;
    box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 50px #fff inset !important;
    box-shadow: 0 0 0 2px var(--color-primary), 0 0 0 50px #fff inset !important;

    caret-color: var(--color-primary);

    transition: ease 0.15s, margin 0s;
  }

  &.disabled {
    background: #fff;
    border: 1px solid #ebeff2;
    color: #8e97af;

    font: var(--font-text-2-medium);
  }

  &.filled {
    border: 2px solid var(--color-primary);
  }
  &.error {
    animation: blink 0.8s infinite;

    &:focus {
      border: transparent;
    }
  }
}
