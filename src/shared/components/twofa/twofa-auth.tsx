/* eslint-disable react-hooks/rules-of-hooks */
import React, { useState, useEffect, useRef } from "react";

import classNamesBind from "classnames/bind";
import styles from "./twofa.module.scss";

const cx = classNamesBind.bind(styles);

type AuthCodeInputProps = {
  codeLength: number;
  onSuccess: (v: string) => void;
  wrapperClassName?: string;
  className?: string;
  inputProps?: React.DetailedHTMLProps<
    React.InputHTMLAttributes<HTMLInputElement>,
    HTMLInputElement
  >;
  isError?: boolean;
  isSuccess?: boolean;
  animationDuration?: number;
};

/**
 * @description компонент для двухфакторной авторизации(введения кода)
 * @param {
 *   codeLength,
 *   onSuccess,
 *   className,
 *   wrapperClassName,
 * }
 */
export const TwoFaAuth: React.FC<AuthCodeInputProps> = ({
  codeLength,
  onSuccess,
  className,
  wrapperClassName,
  isError,
  animationDuration = 2000,
}) => {
  const [code, setCode] = useState<string[]>(Array(codeLength).fill(""));
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, codeLength);
  }, [codeLength]);

  const [isErrorAnimating, setIsErrorAnimating] = useState(false);

  // Анимация ошибки
  useEffect(() => {
    if (isError) {
      setIsErrorAnimating(true);
      setTimeout(() => {
        setIsErrorAnimating(false);
      }, animationDuration);
    }
  }, [isError, animationDuration]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement>,
    index: number
  ) => {
    const value = e.target.value[e.target.value.length - 1] || "";
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);

    // Если значение введено, перемещаем фокус на следующий инпут
    if (value && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus();
    }

    // Если все символы введены
    if (newCode.every((char) => char)) {
      onSuccess(newCode.join(""));
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Backspace" && code[index] === "" && index > 0) {
      const newCode = [...code];
      newCode[index - 1] = "";
      setCode(newCode);

      // сместить фокус на предыдущий инпут
      if (inputRefs.current[index - 1]) {
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  return (
    <div className={cx("twofa", wrapperClassName)}>
      {Array(codeLength)
        .fill(0)
        .map((_, index) => (
          <input
            className={cx(
              "input",
              code[index] && "filled",
              className,
              isErrorAnimating && "error"
            )}
            key={index}
            type="text"
            maxLength={1}
            value={code[index]}
            onChange={(e) => handleChange(e, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            ref={(el) => {
              inputRefs.current[index] = el;
            }}
          />
        ))}
    </div>
  );
};
