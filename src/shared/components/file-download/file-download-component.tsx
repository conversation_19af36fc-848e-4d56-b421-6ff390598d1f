import React, { ChangeEvent, ReactNode, useEffect, useState } from 'react'
import { Button } from '../../ui'
import { useTranslation } from 'react-i18next'

type Props = {
  onFileLoaded?: (file: File) => void
  selectedFile: File | null
  withChoose?: boolean
  fileUrl?: string
  children?: ReactNode
  className?: string
}

export const FileDownloadComponent: React.FC<Props> = ({
  className,
  onFileLoaded,
  selectedFile: defaultFile,
  withChoose = false,
  fileUrl,
  children,
}) => {
  const { t } = useTranslation()
  const [selectedFile, setSelectedFile] = useState<File | null>(defaultFile ?? null)

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      onFileLoaded?.(file)
      setSelectedFile(file)
    }
  }

  useEffect(() => {
    setSelectedFile(defaultFile)
  }, [defaultFile])

  const handleDownload = () => {
    if (fileUrl) {
      const link = document.createElement('a')
      link.href = fileUrl
      link.setAttribute('download', '') // Set to empty string to force download
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    } else if (selectedFile) {
      const blob = new Blob([selectedFile], { type: selectedFile.type })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.setAttribute('download', selectedFile?.name ?? t('commons:your_file'))
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <div>
      {withChoose && <input type='file' onChange={handleFileChange} />}
      {children && (
        <div className={className} onClick={handleDownload}>
          {children}
        </div>
      )}
      {!children && <Button onClick={handleDownload}>{t('commons:download')}</Button>}
    </div>
  )
}
