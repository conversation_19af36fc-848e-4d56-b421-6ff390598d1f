/* eslint-disable no-empty */
import React from "react";

interface FileDownloadProps {
  url: string;
  children: React.ReactNode;
  fileName?: string;
  disabled?: boolean;
}

export const FileDownload: React.FC<FileDownloadProps> = ({
  url,
  children,
  fileName = "download",
  disabled,
}) => {
  const handleDownload = async () => {
    if (disabled) return;

    const URL = url;
    try {
      if (!URL) {
        console.error("url is required.");
        return;
      }

      let token = "";
      try {
        token = `Token ${JSON.parse(localStorage.getItem("token") || "")}`;
      } catch (error) {}

      const response = await fetch(URL, {
        method: "GET",
        headers: {
          Authorization: token,
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to download file. Status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      link.parentNode?.removeChild(link);
    } catch (error) {
      console.error("Error downloading file:", error);
    }
  };

  return (
    <div
      onClick={(e) => {
        e.stopPropagation();
        handleDownload();
      }}
    >
      {children}
    </div>
  );
};
