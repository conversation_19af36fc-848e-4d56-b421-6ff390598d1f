.wrapper {
  ul,
  li {
    list-style-type: none;
    background: var(--white);
  }

  @media (max-width: 1024px) {
    &.userMobile {
      display: grid;
      grid-template-columns: 1fr 1fr;
      background: var(--white);
    }
  }

  .active {
    .icon-fill {
      &-main {
        fill: var(--color-primary-90);
      }
      &-light {
        fill: var(--color-primary-50);
      }
    }
    .arrow {
      fill: var(--color-primary-90);

      transition: 300ms ease-out;
    }
  }

  .top_active {
    .icon-fill {
      &-main {
        fill: var(--color-primary-90);
      }
      &-light {
        fill: var(--color-primary-50);
      }
    }
    .arrow {
      transition: 300ms ease-out;
    }
  }
}

.item {
  .top_active {
    color: var(--color-primary);
  }
}

.link {
  &__content {
    align-items: center;

    color: var(--color-gray-80);

    cursor: pointer;
    display: flex;
    font: var(--font-text-1-normal);
    gap: 12px;
    justify-content: flex-start;
    min-height: 44px;
    padding: 0;
    padding: 12px 20px;
    word-break: break-word;
    transition: 0.1s ease-out;
    width: 100%;

    @media (max-width: 1024px) {
      &.userMobile {
        flex-direction: column;
        gap: 0;
        padding: 4px 8px;
        background: var(--white);
      }
    }

    &_active {
      color: var(--color-primary);
    }

    &:hover {
      background-color: var(--color-gray-40);
    }

    @media (max-width: 1024px) {
      &:hover {
        background-color: #fff;
      }
    }
  }
}

.list {
  display: flex;
  flex-direction: column;
  max-height: 0;
  overflow: hidden;

  transition: 0.6s linear;

  &__item {
    padding-left: 52px;
    padding-right: 20px;

    &.active {
      background-color: var(--color-primary-20);
      color: var(--color-primary);
    }
  }
  &_collapsed {
    pointer-events: none;

    transition: 0.4s linear;
    visibility: hidden;
  }
  &_expanded {
    max-height: 15em;
  }
}

.icon-fill {
  transition: 200ms ease;

  &-main {
    fill: var(--color-gray-70);
  }
  &-light {
    fill: var(--color-gray-60);
  }
}

.skeleton__wrapper {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.arrow {
  margin-left: auto;
}
