import styles from './sidebar-links.module.scss'
import classNamesBind from 'classnames/bind'

const cx = classNamesBind.bind(styles)

type LinksVariants =
  | 'statistics'
  | 'webtutor'
  | 'staff'
  | 'learning'
  | 'phishing'
  | 'organization'
  | 'settings'

type Props = {
  item: LinksVariants | string
  isMobile?: boolean
}

export const SidebarLinksIcon = ({ item, isMobile }: Props) => {
  if (item === 'statistics')
    return isMobile ? (
      <svg
        width='28'
        height='28'
        viewBox='0 0 28 28'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          d='M25.1125 13.9999C25.2575 13.9999 25.3754 14.1175 25.3719 14.2624C25.325 16.2978 24.7325 18.2851 23.6541 20.0158C22.5292 21.8207 20.921 23.2742 19.0118 24.2112C17.1027 25.1483 14.9691 25.5313 12.8531 25.317C10.7372 25.1025 8.72384 24.2992 7.04146 22.9982C5.35909 21.6972 4.07514 19.9506 3.33536 17.9566C2.59558 15.9628 2.4296 13.8014 2.85627 11.7179C3.28293 9.63434 4.28513 7.7122 5.74914 6.16957C7.15285 4.69047 8.92719 3.61708 10.8854 3.05962C11.0247 3.01992 11.1682 3.1044 11.2046 3.24471L13.9489 13.8035C13.979 13.9191 14.0835 13.9999 14.203 13.9999H25.1125Z'
          className={cx('icon-fill', 'icon-fill-light')}
        />
        <path
          d='M23.1877 12.25C23.4293 12.25 23.6264 12.054 23.613 11.8127C23.5547 10.7624 23.2862 9.7328 22.8221 8.78544C22.2938 7.70698 21.5258 6.76361 20.5769 6.02753C19.6279 5.29145 18.5232 4.78216 17.3472 4.53862C16.3141 4.3247 15.2502 4.32079 14.2183 4.52543C13.9813 4.57243 13.8405 4.81213 13.9005 5.04616L15.6658 11.9213C15.7155 12.1147 15.8899 12.25 16.0895 12.25H23.1877Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
      </svg>
    ) : (
      <svg
        width='20'
        height='20'
        viewBox='0 0 20 20'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          d='M16 9C16 8.44771 16.4477 8 17 8H18C18.5523 8 19 8.44772 19 9V18.5C19 18.7761 18.7761 19 18.5 19H16.5C16.2239 19 16 18.7761 16 18.5V9Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
        <path
          d='M11 15C11 14.4477 11.4477 14 12 14H13C13.5523 14 14 14.4477 14 15V18.5C14 18.7761 13.7761 19 13.5 19H11.5C11.2239 19 11 18.7761 11 18.5V15Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
        <path
          d='M6 13C6 12.4477 6.44772 12 7 12H8C8.55228 12 9 12.4477 9 13V18.5C9 18.7761 8.77614 19 8.5 19H6.5C6.22386 19 6 18.7761 6 18.5V13Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
        <path
          d='M1 18C1 17.4477 1.44772 17 2 17H3C3.55228 17 4 17.4477 4 18V18.5C4 18.7761 3.77614 19 3.5 19H1.5C1.22386 19 1 18.7761 1 18.5V18Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M19 2.5C19 3.32843 18.3284 4 17.5 4C17.3166 4 17.1409 3.96709 16.9785 3.90685L13.7896 7.73347C13.9232 7.9578 14 8.21994 14 8.5C14 9.32843 13.3284 10 12.5 10C11.6716 10 11 9.32843 11 8.5C11 8.47957 11.0004 8.45924 11.0012 8.439L8.62739 7.48947C8.35248 7.80245 7.94931 8 7.5 8C7.26843 8 7.04911 7.94752 6.85329 7.85381L3.85381 10.8533C3.94752 11.0491 4 11.2684 4 11.5C4 12.3284 3.32843 13 2.5 13C1.67157 13 1 12.3284 1 11.5C1 10.6716 1.67157 10 2.5 10C2.73157 10 2.95089 10.0525 3.14671 10.1462L6.14619 7.14671C6.05248 6.95089 6 6.73157 6 6.5C6 5.67157 6.67157 5 7.5 5C8.32843 5 9 5.67157 9 6.5C9 6.52043 8.99959 6.54076 8.99878 6.561L11.3726 7.51053C11.6475 7.19755 12.0507 7 12.5 7C12.6834 7 12.8591 7.03291 13.0215 7.09315L16.2104 3.26653C16.0768 3.0422 16 2.78006 16 2.5C16 1.67157 16.6716 1 17.5 1C18.3284 1 19 1.67157 19 2.5Z'
          className={cx('icon-fill', 'icon-fill-light')}
        />
      </svg>
    )
  if (item === 'webtutor')
    return (
      <svg
        width='20'
        height='20'
        viewBox='0 0 20 20'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M2.00207 8.90192V13.5335C2.00207 13.8096 1.77821 14.0335 1.50207 14.0335C1.22593 14.0335 1.00207 13.8096 1.00207 13.5335L1.00207 8.0794C0.985544 7.89652 1.06802 7.70585 1.24948 7.60079L8.49896 3.40372C9.42875 2.86543 10.5754 2.86543 11.5052 3.40372L18.7547 7.60079C19.0873 7.79337 19.0873 8.27363 18.7547 8.46621L11.5052 12.6633C10.5754 13.2016 9.42874 13.2016 8.49896 12.6633L2.00207 8.90192Z'
          className={cx('icon-fill', 'icon-fill-light')}
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M4 14.4665V11L8.49896 13.6047C9.42874 14.143 10.5754 14.143 11.5052 13.6047L16 11.0024V14.4689L13.5093 15.9109C11.3398 17.1669 8.66431 17.1669 6.49481 15.9109L4 14.4665Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
      </svg>
    )
  if (item === 'staff')
    return (
      <svg
        width='20'
        height='20'
        viewBox='0 0 20 20'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          d='M17 17C17.5523 17 18.0101 16.5479 17.9003 16.0066C17.8366 15.6924 17.7427 15.3842 17.6194 15.0866C17.3681 14.48 16.9998 13.9288 16.5355 13.4645C16.0712 13.0002 15.52 12.6319 14.9134 12.3806C14.3068 12.1293 13.6566 12 13 12C12.3434 12 11.6932 12.1293 11.0866 12.3806C10.48 12.6319 9.92876 13.0002 9.46447 13.4645C9.00017 13.9288 8.63188 14.48 8.3806 15.0866C8.25731 15.3842 8.16337 15.6924 8.09967 16.0066C7.98995 16.5479 8.44772 17 9 17L13 17H17Z'
          className={cx('icon-fill', 'icon-fill-light')}
        />
        <path
          d='M13 17C13.5523 17 14.0085 16.5492 13.9169 16.0046C13.842 15.5594 13.7169 15.123 13.5433 14.7039C13.2417 13.9759 12.7998 13.3145 12.2426 12.7574C11.6855 12.2002 11.0241 11.7583 10.2961 11.4567C9.56815 11.1552 8.78793 11 8 11C7.21207 11 6.43185 11.1552 5.7039 11.4567C4.97595 11.7583 4.31451 12.2002 3.75736 12.7574C3.20021 13.3145 2.75825 13.9759 2.45672 14.7039C2.28313 15.123 2.15804 15.5594 2.08314 16.0046C1.99152 16.5492 2.44772 17 3 17L8 17H13Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
        <circle cx='8' cy='7' r='3' className={cx('icon-fill', 'icon-fill-light')} />
        <circle cx='13' cy='9' r='2' className={cx('icon-fill', 'icon-fill-light')} />
      </svg>
    )
  if (item === 'learning')
    return isMobile ? (
      <svg
        width='28'
        height='28'
        viewBox='0 0 28 28'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M2.80329 12.4629V18.9471C2.80329 19.3336 2.48988 19.6471 2.10329 19.6471C1.71669 19.6471 1.40329 19.3336 1.40329 18.9471V11.3114C1.38015 11.0553 1.49562 10.7884 1.74966 10.6413L11.8989 4.7654C13.2006 4.01179 14.8059 4.01179 16.1077 4.7654L26.257 10.6413C26.7226 10.9109 26.7226 11.5833 26.257 11.8529L16.1077 17.7288C14.8059 18.4824 13.2006 18.4824 11.8989 17.7288L2.80329 12.4629Z'
          className={cx('icon-fill', 'icon-fill-light')}
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M5.59961 20.2535V15.4004L11.8982 19.047C13.1998 19.8006 14.8052 19.8006 16.1069 19.047L22.3996 15.4038V20.2568L18.9126 22.2757C15.8753 24.034 12.1296 24.034 9.09234 22.2757L5.59961 20.2535Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
      </svg>
    ) : (
      <svg
        width='20'
        height='20'
        viewBox='0 0 20 20'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M2.00207 8.90192V13.5335C2.00207 13.8096 1.77821 14.0335 1.50207 14.0335C1.22593 14.0335 1.00207 13.8096 1.00207 13.5335L1.00207 8.0794C0.985544 7.89652 1.06802 7.70585 1.24948 7.60079L8.49896 3.40372C9.42875 2.86543 10.5754 2.86543 11.5052 3.40372L18.7547 7.60079C19.0873 7.79337 19.0873 8.27363 18.7547 8.46621L11.5052 12.6633C10.5754 13.2016 9.42874 13.2016 8.49896 12.6633L2.00207 8.90192Z'
          className={cx('icon-fill', 'icon-fill-light')}
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M4 14.4665V11L8.49896 13.6047C9.42874 14.143 10.5754 14.143 11.5052 13.6047L16 11.0024V14.4689L13.5093 15.9109C11.3398 17.1669 8.66431 17.1669 6.49481 15.9109L4 14.4665Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
      </svg>
    )
  if (item === 'phishing')
    return (
      <svg
        width='20'
        height='20'
        viewBox='0 0 20 20'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M2.75725 7.65435C2.42399 7.45439 2 7.69445 2 8.08309V13C2 14.6569 3.34315 16 5 16H15C16.6569 16 18 14.6569 18 13V8.0831C18 7.69445 17.576 7.45439 17.2428 7.65435L10.5145 11.6913C10.1978 11.8813 9.80219 11.8813 9.4855 11.6913L2.75725 7.65435Z'
          className={cx('icon-fill', 'icon-fill-main')}
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M17.6042 5.50962C17.8272 5.89837 17.6287 6.36697 17.2444 6.59755L10.1642 10.8457C10.0059 10.9407 9.80807 10.9407 9.64973 10.8457L2.70853 6.68093C2.33489 6.45675 2.13475 6.00697 2.33544 5.62019C2.83491 4.65762 3.84066 4 5 4H15C16.1144 4 17.0869 4.60762 17.6042 5.50962Z'
          className={cx('icon-fill', 'icon-fill-light')}
        />
      </svg>
    )
  if (item === 'organization')
    return (
      <svg
        width='20'
        height='20'
        viewBox='0 0 20 20'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <rect
          x='9.5'
          y='5'
          width='2'
          height='9'
          transform='rotate(30 9.5 5)'
          className={cx('icon-fill', 'icon-fill-light')}
        />
        <rect
          x='15'
          y='14'
          width='2'
          height='9'
          transform='rotate(90 15 14)'
          className={cx('icon-fill', 'icon-fill-light')}
        />
        <circle cx='11' cy='5' r='3' className={cx('icon-fill', 'icon-fill-main')} />
        <circle cx='5' cy='15' r='3' className={cx('icon-fill', 'icon-fill-main')} />
        <circle cx='15' cy='15' r='3' className={cx('icon-fill', 'icon-fill-main')} />
      </svg>
    )
  if (item === 'settings')
    return (
      <svg
        width='20'
        height='20'
        viewBox='0 0 20 20'
        fill='none'
        xmlns='http://www.w3.org/2000/svg'
      >
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M8.10899 1.54601C8 1.75992 8 2.03995 8 2.6C8 3.54898 6.85265 4.02425 6.18162 3.35322C5.7856 2.9572 5.58759 2.75919 5.35926 2.685C5.15842 2.61975 4.94207 2.61975 4.74123 2.685C4.5129 2.75919 4.31489 2.9572 3.91888 3.35322L3.35319 3.9189L3.35319 3.9189L3.35319 3.91891C2.95717 4.31492 2.75916 4.51293 2.68498 4.74126C2.61972 4.9421 2.61972 5.15845 2.68498 5.35929C2.75916 5.58762 2.95717 5.78563 3.35319 6.18164C4.02421 6.85266 3.54896 8 2.6 8C2.03995 8 1.75992 8 1.54601 8.10899C1.35785 8.20487 1.20487 8.35785 1.10899 8.54601C1 8.75992 1 9.03995 1 9.6V10.4C1 10.9601 1 11.2401 1.10899 11.454C1.20487 11.6422 1.35785 11.7951 1.54601 11.891C1.75992 12 2.03995 12 2.6 12C3.54896 12 4.0242 13.1473 3.35319 13.8184C2.95717 14.2144 2.75916 14.4124 2.68497 14.6407C2.61971 14.8415 2.61971 15.0579 2.68497 15.2587C2.75916 15.4871 2.95717 15.6851 3.35319 16.0811L3.91887 16.6468C4.31489 17.0428 4.5129 17.2408 4.74123 17.315C4.94207 17.3803 5.15842 17.3803 5.35926 17.315C5.58759 17.2408 5.7856 17.0428 6.18161 16.6468C6.85264 15.9757 8 16.451 8 17.4C8 17.9601 8 18.2401 8.10899 18.454C8.20487 18.6422 8.35785 18.7951 8.54601 18.891C8.75992 19 9.03995 19 9.6 19H10.4C10.9601 19 11.2401 19 11.454 18.891C11.6422 18.7951 11.7951 18.6422 11.891 18.454C12 18.2401 12 17.9601 12 17.4C12 16.451 13.1474 15.9758 13.8184 16.6468L13.8184 16.6468C14.2144 17.0428 14.4124 17.2409 14.6407 17.315C14.8416 17.3803 15.0579 17.3803 15.2588 17.315C15.4871 17.2409 15.6851 17.0428 16.0811 16.6468L16.0811 16.6468L16.6468 16.0811C17.0428 15.6851 17.2408 15.4871 17.315 15.2588C17.3803 15.0579 17.3803 14.8416 17.315 14.6408C17.2408 14.4124 17.0428 14.2144 16.6468 13.8184C15.9758 13.1474 16.451 12 17.4 12C17.9601 12 18.2401 12 18.454 11.891C18.6422 11.7951 18.7951 11.6422 18.891 11.454C19 11.2401 19 10.9601 19 10.4V9.6C19 9.03995 19 8.75992 18.891 8.54601C18.7951 8.35785 18.6422 8.20487 18.454 8.10899C18.2401 8 17.9601 8 17.4 8C16.451 8 15.9758 6.85263 16.6468 6.1816L16.6468 6.18159C17.0428 5.78558 17.2408 5.58757 17.315 5.35925C17.3803 5.1584 17.3803 4.94206 17.315 4.74121C17.2408 4.51289 17.0428 4.31488 16.6468 3.91887L16.6468 3.91886L16.0811 3.35317L16.0811 3.35316C15.6851 2.95715 15.4871 2.75915 15.2588 2.68496C15.0579 2.6197 14.8416 2.6197 14.6407 2.68496C14.4124 2.75915 14.2144 2.95715 13.8184 3.35317L13.8184 3.35317C13.1473 4.02419 12 3.54896 12 2.6C12 2.03995 12 1.75992 11.891 1.54601C11.7951 1.35785 11.6422 1.20487 11.454 1.10899C11.2401 1 10.9601 1 10.4 1H9.6C9.03995 1 8.75992 1 8.54601 1.10899C8.35785 1.20487 8.20487 1.35785 8.10899 1.54601Z'
          className={cx('icon-fill', 'icon-fill-light')}
        />
        <circle cx='10' cy='10' r='3' className={cx('icon-fill', 'icon-fill-main')} />
      </svg>
    )

  return null
}
