import { FC } from 'react'
import styles from './sidebar-links.module.scss'
import classNamesBind from 'classnames/bind'
import { SidebarLinksProps } from './sidebar-links.d'
import { SidebarLinksIcon } from './sidebar-links-icon'
import { NavLink, useLocation } from 'react-router-dom'
import Skeleton from 'react-loading-skeleton'
import ArrowDownIcon from '@/shared/ui/Icon/icons/components/ArrowDown'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import { IPermissionGroup, useUserPermissions } from 'entities/employee'
import { useMediaQuery } from 'usehooks-ts'

const cx = classNamesBind.bind(styles)

type ItemProps = {
  item: IPermissionGroup
  url: string
  prefix: string
}

const ROUTE_PREFIX = '/lk/'

const isRoutesEquals = (locationRoute: string, route: string) =>
  locationRoute.startsWith(ROUTE_PREFIX + route)

const extractPathPrefix = (input: string, level = 3) => {
  const parts = input.split('/')
  if (parts.length >= 4 && level === 3) {
    return `/${parts[1]}/${parts[2]}/${parts[3]}`
  }
  if (level === 4) {
    return `/${parts[1]}/${parts[2]}/${parts[3]}/${parts[4]}`
  }

  return ''
}

const prepareAdminRoutes = (data?: IPermissionGroup[]) => {
  if (!data) return []

  const result = JSON.parse(JSON.stringify(data)) as IPermissionGroup[]
  const settingsItemIndex = result.findIndex(i => i.name === 'settings')

  if (settingsItemIndex !== -1) {
    result[settingsItemIndex].pages = [
      ...result[settingsItemIndex].pages.filter(i => i.name !== 'settings'),
      {
        name: 'emails',
      },
    ]
  }

  return result
}

const MenuItem = ({ item, url, prefix }: ItemProps) => {
  const { t } = useTranslation()

  const correctItemName = `${prefix}/${item.name}`
  const correctRouteItemName = `/lk/${prefix}/${item.name}`
  const { pathname } = useLocation()

  const isMobile = useMediaQuery('(max-width: 1024px)')

  if (item.pages && item.pages.length > 0) {
    const routeActive = isRoutesEquals(url, correctItemName)

    return (
      <li className={cx('item')} key={item.name}>
        <NavLink
          className={cx(
            'link__content',
            routeActive && 'link__content_active' && 'top_active',
            pathname.startsWith('/lk/user') && 'userMobile',
          )}
          to={`${correctRouteItemName}/${item?.pages[0]?.name}`}
          end={false}
        >
          <SidebarLinksIcon item={item.name} />
          <span>{t(`commons:${item.name}`)}</span>
          <IconWrapper
            className={cx('arrow', routeActive && 'arrow__rotate')}
            direction={routeActive ? 'down' : 'up'}
            color={routeActive ? 'primary' : undefined}
          >
            <ArrowDownIcon />
          </IconWrapper>
        </NavLink>
        {item?.pages && (
          <ul className={cx('list', routeActive ? 'list_expanded' : 'list_collapsed')}>
            {item?.pages?.map(({ name }) => {
              if (name === 'departments') return null

              return (
                <li key={name}>
                  <NavLink
                    to={`${correctRouteItemName}/${name}`}
                    className={({ isActive }) =>
                      cx('list__item', 'link__content', isActive && 'active')
                    }
                  >
                    {t(`commons:${name}`)}
                  </NavLink>
                </li>
              )
            })}
          </ul>
        )}
      </li>
    )
  }

  return (
    <li className={cx('item')} key={item.name}>
      <NavLink
        className={({ isActive }) =>
          cx(
            'link__content',
            isActive && 'link__content_active' && 'top_active',
            pathname.startsWith('/lk/user') && 'userMobile',
          )
        }
        to={correctRouteItemName}
      >
        <SidebarLinksIcon item={item.name} isMobile={isMobile} />
        <span>{t(`commons:${item.name}`)}</span>
      </NavLink>
    </li>
  )
}

export const SidebarLinks: FC<SidebarLinksProps.Props> = props => {
  const { className } = props

  const data = useUserPermissions()

  const { pathname } = useLocation()

  if (!data)
    return (
      <div className={cx('wrapper', className)}>
        <div className={cx('skeleton__wrapper')}>
          {[1, 2, 3, 4, 5].map(i => (
            <Skeleton key={i} height={44} width={'100%'} />
          ))}
        </div>
      </div>
    )

  const extractedUrl = extractPathPrefix(pathname)

  return (
    <div className={cx('wrapper', className, pathname.startsWith('/lk/user') && 'userMobile')}>
      {pathname.startsWith('/lk/user') &&
        data?.user.map((item, index) => (
          <MenuItem key={index} url={extractedUrl} prefix={'user'} item={item} />
        ))}
      {pathname.startsWith('/lk/admin') &&
        prepareAdminRoutes(data?.admin).map((item, index) => (
          <MenuItem key={index} url={extractedUrl} prefix={'admin'} item={item} />
        ))}
    </div>
  )
}
