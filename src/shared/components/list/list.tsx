/* eslint-disable @typescript-eslint/no-unnecessary-type-constraint */
/* eslint-disable @typescript-eslint/no-explicit-any */
import classNamesBind from 'classnames/bind'
import React, { useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import InfiniteScroll from 'react-infinite-scroll-component'
import { ListProps } from './list.d'
import styles from './list.module.scss'

const cx = classNamesBind.bind(styles)

export const List = <T extends any>(props: ListProps.Props<T>) => {
  const {
    data,
    renderItem,
    defaultCount = 10,
    // itemHeight = 70,
    error = false,
    errorElement,
    paginated = false,
    canLoadMore = false,
    loadMore,
    className,
    loading,
    needSkeleton,
    scrollableTarget = 'page-wrapper',
  } = props
  const offset = data?.length || 0

  const { t } = useTranslation()
  const empty = props.empty || t('old.list.no_data')

  const fetchMore = useCallback(() => {
    loadMore && loadMore({ variables: { offset, limit: 100 } })
  }, [offset, loadMore])

  if (error) return errorElement || <div className={cx('errorFallback')}>{t('old.list.error')}</div>

  const dataArray = data || (paginated ? [] : Array.from(new Array(defaultCount)))

  const renderItems = () =>
    (data && data.length) || canLoadMore || loading ? (
      dataArray?.map(renderItem)
    ) : (
      <div key='empty' className={cx('empty')}>
        {empty}
      </div>
    )

  if (paginated) {
    return (
      <InfiniteScroll
        className={className}
        dataLength={dataArray.length}
        next={fetchMore}
        hasMore={paginated && canLoadMore}
        loader={
          <React.Fragment key='loader'>
            {Array.from(new Array(defaultCount)).map(renderItem)}
          </React.Fragment>
        }
        scrollableTarget={scrollableTarget}
      >
        {needSkeleton && dataArray.length === 0 && loading ? (
          <React.Fragment key='loader'>
            {Array.from(new Array(defaultCount)).map(renderItem)}
          </React.Fragment>
        ) : (
          renderItems()
        )}
      </InfiniteScroll>
    )
  }

  return (
    <>
      {needSkeleton && dataArray.length === 0 && loading ? (
        <div className={className}>{Array.from(new Array(defaultCount)).map(renderItem)}</div>
      ) : (
        <div className={className}>{renderItems()}</div>
      )}
    </>
  )
}
