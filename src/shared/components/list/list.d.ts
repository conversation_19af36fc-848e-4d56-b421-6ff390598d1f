/* eslint-disable @typescript-eslint/no-explicit-any */
export declare namespace ListProps {
  type FetchMore = {
    variables: {
      limit: number;
      offset: number;
    };
  };
  interface Own<T> {
    className?: string;

    data: T[] | undefined;
    renderItem: (item: T, index: number) => React.ReactComponentElement<any>;
    itemHeight?: number;
    defaultCount?: number;

    loading?: boolean;
    error?: boolean;

    scrollContainer?: "window" | "parent";

    errorElement?: React.ReactComponentElement<any>;
    empty?: React.ReactComponentElement<any> | string;

    canLoadMore?: boolean;
    paginated?: boolean;
    needSkeleton?: boolean;
    scrollableTarget?: string;

    loadMore?(_: FetchMore): void;
  }

  interface Store {}

  interface Dispatch {}

  type Props<T> = Own<T> & Store & Dispatch;
}

export {};
