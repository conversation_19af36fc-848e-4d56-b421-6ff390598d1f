.wrapper {
  align-items: center;

  box-sizing: border-box;

  display: flex;

  flex: 1 0;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  position: relative;
  span {
    display: inline-block;
    flex: 1 0;
    height: 100%;
    max-height: 100%;
    width: 100%;
  }
}

.image {
  height: 100%;
  width: 100%;
  @supports (object-fit: cover) {
    display: none;
  }

  &.loaded {
    display: block;
  }
  &.wide {
    width: 100%;
  }
}

.fit {
  &-contain {
    object-fit: contain;
  }
  &-cover {
    object-fit: cover;
  }
  &-scale-down {
    object-fit: scale-down;
  }
}

.view {
  &-rounded {
    border-radius: 10px;
  }
  &-circle {
    border-radius: 50%;
  }
  &-square {
    border-radius: 0;
  }
}

.content {
  box-sizing: border-box;

  display: flex;
  flex-grow: 1;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}
