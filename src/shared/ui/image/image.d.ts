export declare namespace ImageProps {
  interface Own {
    className?: string
    image?: { src: string }
    active?: boolean
    alt?: string
    onClick?: () => void
    contentClassName?: string

    height?: number | string // TODO remove
    width?: number
    fit?: 'cover' | 'contain' | 'scale-down'
    view?: 'rounded' | 'circle' | 'square'
    wide?: boolean
    handleHeight?: (height: number) => void

    maxWidth?: number | null
    maxHeight?: number | null
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch
}

export {}
