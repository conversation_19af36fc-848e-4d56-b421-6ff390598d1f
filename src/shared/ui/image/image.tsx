import React, { PropsWithChildren, useCallback, useEffect, useRef, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './image.module.scss'

import { ImageProps } from './image.d'
import Skeleton from 'react-loading-skeleton'

const cx = classNamesBind.bind(styles)
// TODO USE
// https://www.freecodecamp.org/news/time-saving-css-techniques-to-create-responsive-images-ebb1e84f90d5/
// https://github.com/constancecchen/object-fit-polyfill
// https://github.com/fregante/object-fit-images
const Image: React.FC<PropsWithChildren<ImageProps.Props>> = props => {
  const {
    className,
    image,
    active = true,
    alt = '',
    onClick,
    height,
    children,
    contentClassName,
    fit = 'contain',
    view = 'rounded',
    wide = false,
    handleHeight,
    maxWidth,
    maxHeight,
  } = props

  const [loaded, setLoaded] = useState(false)

  const onLoad = useCallback(() => {
    setLoaded(true)
  }, [])

  const { src } = image || {} // TODO width and height

  const wrapper = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!wrapper.current || !active || !loaded) return

    handleHeight && handleHeight(wrapper.current.clientHeight)
  }, [loaded, active, wrapper])

  const sizeProps = {
    ...(maxWidth && { maxWidth }),
    ...(maxHeight && { maxHeight }),
  }

  return (
    // TODO use strset https://css-tricks.com/responsive-images-css/#srcset-in-css
    <div className={cx('wrapper', `view-${view}`, className)} onClick={onClick} ref={wrapper}>
      {(active || loaded) && (
        <img
          src={src}
          alt={alt}
          className={cx('image', `fit-${fit}`, {
            wide,
            loaded,
          })}
          onLoad={onLoad}
          data-object-fit={fit}
          {...((maxWidth || maxHeight) && {
            style: sizeProps,
          })}
        />
      )}
      {(!src || !loaded) && <Skeleton width='100%' height={height}></Skeleton>}
      <div className={cx('content', contentClassName)}>{children}</div>
    </div>
  )
}

export { Image }
