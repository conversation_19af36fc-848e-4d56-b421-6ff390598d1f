.wrapper {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  &.hasBackground {

    background: #fff;
    border-radius: 16px;
    box-shadow: 0 0 16px rgba(0, 0, 0, 0.08);
    padding: 8px;
  }
  .loader {

    animation: spin 0.7s linear infinite;
    transform-origin: center;

    @keyframes spin {
      100% {
        transform: rotate(360deg);
      }
    }
  }
  &.size {
    &--56 {
      svg {
        display: block;
        height: 56px;
        width: 56px;
      }
    }
    &--28 {
      svg {
        display: block;
        height: 28px;
        width: 28px;
      }
    }
  }
}

.gradient {
  &-from {
    stop-color: var(--color-primary);
  }
  &-to {
    stop-color: var(--color-primary-70);
  }
}

.fill-error {
  fill: var(--color-error);
}
