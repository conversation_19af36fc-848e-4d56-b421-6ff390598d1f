import '@testing-library/jest-dom'
import { Loader } from './loader'
import { renderTestScreen } from '@/shared/helpers/tests/render'

describe('[shared] [ui] Loader', () => {
    it(`Должен отрендерить Loader с загрузкой по дефолту`, () => {
        const { getByTestId } = renderTestScreen(
            <Loader />
        )

        const loader = getByTestId('Loader')
        const loaderSVG = getByTestId('Loader.SVG')
        const loaderSVGLoading = getByTestId('Loader.SVG.Loading')

        expect(loader).toBeInTheDocument()
        expect(loaderSVG).toBeInTheDocument()
        expect(loaderSVGLoading).toBeInTheDocument()
    })

    it(`Должен отрендерить Loader с ошибкой при error=true, loading=false`, () => {
        const { getByTestId, queryByTestId } = renderTestScreen(
            <Loader loading={false} error />
        )

        const loader = getByTestId('Loader')
        const loaderSVG = getByTestId('Loader.SVG')
        const loaderSVGLoading = queryByTestId('Loader.SVG.Loading')
        const loaderSVGError1 = getByTestId('Loader.SVG.Error1')
        const loaderSVGError2 = getByTestId('Loader.SVG.Error2')

        expect(loader).toBeInTheDocument()
        expect(loaderSVG).toBeInTheDocument()
        expect(loaderSVGLoading).not.toBeInTheDocument()
        expect(loaderSVGError1).toBeInTheDocument()
        expect(loaderSVGError2).toBeInTheDocument()
    })

    it(`Должен отрендерить Loader с успехом при success=true, loading=false`, () => {
        const { getByTestId, queryByTestId } = renderTestScreen(
            <Loader loading={false} success />
        )

        const loader = getByTestId('Loader')
        const loaderSVG = getByTestId('Loader.SVG')
        const loaderSVGLoading = queryByTestId('Loader.SVG.Loading')
        const loaderSVGSuccess = getByTestId('Loader.SVG.Success')

        expect(loader).toBeInTheDocument()
        expect(loaderSVG).toBeInTheDocument()
        expect(loaderSVGLoading).not.toBeInTheDocument()
        expect(loaderSVGSuccess).toBeInTheDocument()
    })

    it(`Должен отрендерить Loader с загрузкой при loading=error=true`, () => {
        const { getByTestId, queryByTestId } = renderTestScreen(
            <Loader loading error />
        )

        const loader = getByTestId('Loader')
        const loaderSVG = getByTestId('Loader.SVG')
        const loaderSVGLoading = queryByTestId('Loader.SVG.Loading')
        const loaderSVGError1 = queryByTestId('Loader.SVG.Error1')
        const loaderSVGError2 = queryByTestId('Loader.SVG.Error2')
        const loaderSVGSuccess = queryByTestId('Loader.SVG.Success')

        expect(loader).toBeInTheDocument()
        expect(loaderSVG).toBeInTheDocument()
        expect(loaderSVGLoading).toBeInTheDocument()
        expect(loaderSVGError1).not.toBeInTheDocument()
        expect(loaderSVGError2).not.toBeInTheDocument()
        expect(loaderSVGSuccess).not.toBeInTheDocument()
    })

    it(`Должен отрендерить пустой Loader с загрузкой при loading=success=true`, () => {
        const { getByTestId, queryByTestId } = renderTestScreen(
            <Loader loading success />
        )

        const loader = getByTestId('Loader')
        const loaderSVG = getByTestId('Loader.SVG')
        const loaderSVGLoading = queryByTestId('Loader.SVG.Loading')
        const loaderSVGError1 = queryByTestId('Loader.SVG.Error1')
        const loaderSVGError2 = queryByTestId('Loader.SVG.Error2')
        const loaderSVGSuccess = queryByTestId('Loader.SVG.Success')

        expect(loader).toBeInTheDocument()
        expect(loaderSVG).toBeInTheDocument()
        expect(loaderSVGLoading).toBeInTheDocument()
        expect(loaderSVGError1).not.toBeInTheDocument()
        expect(loaderSVGError2).not.toBeInTheDocument()
        expect(loaderSVGSuccess).not.toBeInTheDocument()
    })

    it(`Должен отрендерить пустой Loader с загрузкой при loading=error=success=true`, () => {
        const { getByTestId, queryByTestId } = renderTestScreen(
            <Loader loading error success />
        )

        const loader = getByTestId('Loader')
        const loaderSVG = getByTestId('Loader.SVG')
        const loaderSVGLoading = queryByTestId('Loader.SVG.Loading')
        const loaderSVGError1 = queryByTestId('Loader.SVG.Error1')
        const loaderSVGError2 = queryByTestId('Loader.SVG.Error2')
        const loaderSVGSuccess = queryByTestId('Loader.SVG.Success')

        expect(loader).toBeInTheDocument()
        expect(loaderSVG).toBeInTheDocument()
        expect(loaderSVGLoading).toBeInTheDocument()
        expect(loaderSVGError1).not.toBeInTheDocument()
        expect(loaderSVGError2).not.toBeInTheDocument()
        expect(loaderSVGSuccess).not.toBeInTheDocument()
    })

    it(`Должен отрендерить пустой Loader с загрузкой при loading=false error=success=true`, () => {
        const { getByTestId, queryByTestId } = renderTestScreen(
            <Loader error success loading={false} />
        )

        const loader = getByTestId('Loader')
        const loaderSVG = getByTestId('Loader.SVG')
        const loaderSVGLoading = queryByTestId('Loader.SVG.Loading')
        const loaderSVGError1 = queryByTestId('Loader.SVG.Error1')
        const loaderSVGError2 = queryByTestId('Loader.SVG.Error2')
        const loaderSVGSuccess = queryByTestId('Loader.SVG.Success')

        expect(loader).toBeInTheDocument()
        expect(loaderSVG).toBeInTheDocument()
        expect(loaderSVGLoading).not.toBeInTheDocument()
        expect(loaderSVGError1).not.toBeInTheDocument()
        expect(loaderSVGError2).not.toBeInTheDocument()
        expect(loaderSVGSuccess).not.toBeInTheDocument()
    })
})
