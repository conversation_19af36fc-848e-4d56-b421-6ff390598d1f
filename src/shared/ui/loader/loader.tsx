import { FC } from "react";
import styles from "./loader.module.scss";
import classNamesBind from "classnames/bind";
import { LoaderProps } from "./loader.d";

import { v4 as uuid } from "uuid";

const cx = classNamesBind.bind(styles);

export const Loader: FC<LoaderProps.Props> = (props) => {
  const {
    className,
    hasBackground = false,
    loading = true,
    error = false,
    success = false,
    size = "28",
    'data-testid': dataTestid = 'Loader'
  } = props;

  const _id = uuid();

  return (
    <div
      className={cx("wrapper", className, `size--${size}`, { hasBackground })}
      data-testid={dataTestid}
    >
      <div className={cx("inner")}>
        <svg
          className={cx("spinner")}
          width="72"
          height="72"
          viewBox="0 0 72 72"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          data-testid={`${dataTestid}.SVG`}
        >
          {loading && (
            <path
              className={cx("loader")}
              d="M51.3398 26.5824C53.4238 29.977 54.323 33.9669 53.8965 37.9272C53.4701 41.8875 51.742 45.5946 48.983 48.4676C46.2241 51.3406 42.5901 53.2173 38.6503 53.8038C34.7105 54.3903 30.6874 53.6534 27.2113 51.7085C23.7351 49.7637 21.0023 46.7207 19.4408 43.0563C17.8793 39.3919 17.5773 35.3131 18.5823 31.4587C19.5872 27.6044 21.8423 24.1922 24.9942 21.7567C28.1461 19.3212 32.0168 18 36 18"
              stroke={`url(#primary_gradient${_id})`}
              strokeWidth="4"
              strokeLinecap="round"
              data-testid={`${dataTestid}.SVG.Loading`}
            />
          )}

          {!loading && success && !error && (
            <path
              d="M20.4129 33.6535C19.6312 32.8731 18.3648 32.8743 17.5845 33.656C16.8042 34.4378 16.8053 35.7041 17.5871 36.4845L20.4129 33.6535ZM31.9545 48L30.5416 49.4155C31.3224 50.1948 32.5867 50.1948 33.3675 49.4155L31.9545 48ZM58.4129 24.4155C59.1947 23.6352 59.1958 22.3688 58.4155 21.5871C57.6352 20.8053 56.3688 20.8042 55.5871 21.5845L58.4129 24.4155ZM17.5871 36.4845L30.5416 49.4155L33.3675 46.5845L20.4129 33.6535L17.5871 36.4845ZM33.3675 49.4155L58.4129 24.4155L55.5871 21.5845L30.5416 46.5845L33.3675 49.4155Z"
              fill={`url(#primary_gradient_2${_id})`}
              data-testid={`${dataTestid}.SVG.Success`}
            />
          )}

          {!loading && !success && error && (
            <>
              <rect
                x="19"
                y="21.8281"
                width="4"
                height="44"
                rx="2"
                transform="rotate(-45 19 21.8281)"
                className={cx("fill-error")}
                data-testid={`${dataTestid}.SVG.Error1`}
              />
              <rect
                x="21.8281"
                y="52.9414"
                width="4"
                height="44"
                rx="2"
                transform="rotate(-135 21.8281 52.9414)"
                className={cx("fill-error")}
                data-testid={`${dataTestid}.SVG.Error2`}
              />
            </>
          )}

          <defs>
            <linearGradient
              id={`primary_gradient${_id}`}
              x1="18.5"
              y1="49.5"
              x2="56"
              y2="26.5"
              gradientUnits="userSpaceOnUse"
            >
              <stop className={cx("gradient-from")} />
              <stop offset="1" className={cx("gradient-to")} />
            </linearGradient>

            <linearGradient
              id={`primary_gradient_2${_id}`}
              x1="21.375"
              y1="43.9375"
              x2="47.6046"
              y2="19.4845"
              gradientUnits="userSpaceOnUse"
            >
              <stop className={cx("gradient-from")} />
              <stop offset="1" className={cx("gradient-to")} />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </div>
  );
};
