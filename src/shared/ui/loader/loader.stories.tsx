import type { Meta, StoryObj } from '@storybook/react'
import { Loader } from './loader'
import { loaderSizes } from './loader.d'

const meta: Meta<typeof Loader> = {
    title: 'UI/Loader',
    component: Loader,
    tags: ['autodocs'],
    argTypes: {
        hasBackground: {
            description: 'Имеется ли фон',
            control: 'boolean',
            table: {
                type: { summary: 'false | true' },
                defaultValue: { summary: 'false' },
            },

        },
        loading: {
            description: 'Загружается ли. Сильнее всех других статусов',
            control: 'boolean',
            table: {
                type: { summary: 'false | true' },
                defaultValue: { summary: 'true' },
            },

        },
        error: {
            description: 'Произошла ли ошибка. Отображается только когда loading и success = false',
            control: 'boolean',
            table: {
                type: { summary: 'false | true' },
                defaultValue: { summary: 'false' },
            },

        },
        success: {
            description: 'Загрузка успешно завершена. Отображается только когда loading и error = false',
            control: 'boolean',
            table: {
                type: { summary: 'false | true' },
                defaultValue: { summary: 'false' },
            },

        },
        size: {
            options: loaderSizes,
            description: 'Размер',
            control: 'select',
            table: {
                type: { summary: 'ILoaderSize' },
                defaultValue: { summary: '28' },
            }
        },
    }
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const Sizes: Story = {
    decorators: () => {
        return <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
            alignItems: 'start'
        }}>
            {loaderSizes.map(size => <>
                <Loader size={size} loading />
                <Loader size={size} success loading={false} />
                <Loader size={size} error loading={false} />
                <Loader size={size} hasBackground loading />
                <Loader size={size} hasBackground success loading={false} />
                <Loader size={size} hasBackground error loading={false} />
            </>)}
        </div>
    }
}