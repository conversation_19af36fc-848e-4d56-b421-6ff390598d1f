import React, {
  FC,
  useCallback,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
} from 'react'
import styles from './select.module.scss'
import classNamesBind from 'classnames/bind'
import { IListItem, SelectProps, SelectListProps } from './select.d'
import ChevroneMediumIcon from '../Icon/icons/components/ChevroneMediumIcon'
import LoadIcon from '../Icon/icons/components/LoadIcon'
import { IconWrapper } from '../Icon/IconWrapper'
import { HelpIcon } from '@/shared/ui'
import { ERole } from '@/shared/types/enums'
import { useDebounce, useEvent } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const SelectList: FC<SelectListProps.Props> = props => {
  const {
    className,
    disableActiveValue,
    list,
    handleChange,
    active,
    additionalStr,
    isOptionDisabled,
    wrapperRef,
    listWrapperId,
    renderListInnerWrapper,
  } = props

  const listRef = useRef<HTMLDivElement>(null)
  const activeItemRef = useRef<HTMLSpanElement | null>(null)
  const [shouldOpenUp, setShouldOpenUp] = useState(false)
  const [xOffset, setXOffset] = useState<number>(0)

  const handleClick = (e: React.MouseEvent<HTMLSpanElement, MouseEvent>, item: IListItem) => {
    e.stopPropagation()

    handleChange(item)
  }

  const checkPositioning = useCallback(() => {
    const wrapperEl = wrapperRef.current
    const listEl = listRef.current

    if (wrapperEl && listEl) {
      const wrapperRect = wrapperEl.getBoundingClientRect()
      const listRect = listEl.getBoundingClientRect()

      const spaceBelow = window.innerHeight - wrapperRect.bottom
      const spaceAbove = wrapperRect.top

      setShouldOpenUp(spaceBelow < listRect.height && spaceAbove > listRect.height)

      const spaceLeft = wrapperRect.left
      const spaceRight = window.innerWidth - wrapperRect.right

      let calculatedXOffset = 0

      if (spaceRight < listRect.width && spaceLeft > listRect.width) {
        calculatedXOffset = -Math.min(listRect.width - wrapperRect.width, spaceLeft)
      } else if (spaceLeft < listRect.width && spaceRight > listRect.width) {
        calculatedXOffset = Math.min(listRect.width - wrapperRect.width, spaceRight)
      }

      setXOffset(calculatedXOffset)
    }
  }, [])

  useLayoutEffect(() => {
    checkPositioning()
  }, [checkPositioning])

  useEffect(() => {
    if (activeItemRef.current && listRef.current) {
      const listEl = listRef.current
      const activeEl = activeItemRef.current

      const listRect = listEl.getBoundingClientRect()
      const activeRect = activeEl.getBoundingClientRect()

      const isOutOfView = activeRect.top < listRect.top || activeRect.bottom > listRect.bottom

      if (isOutOfView) {
        activeEl.scrollIntoView({ block: 'nearest', behavior: 'instant' })
      }
    }
  }, [active])

  return (
    <div
      ref={listRef}
      className={cx('listWrapper', `position_${shouldOpenUp ? 'top' : 'bottom'}`, className)}
      style={{ transform: `translateX(${xOffset}px)` }}
      onClick={e => e.stopPropagation()}
      id={listWrapperId}
    >
      {renderListInnerWrapper ? (
        renderListInnerWrapper(
          <ListInner
            list={list}
            isOptionDisabled={isOptionDisabled}
            additionalStr={additionalStr}
            disableActiveValue={disableActiveValue}
            active={active}
            handleClick={handleClick}
          />,
        )
      ) : (
        <ListInner
          list={list}
          isOptionDisabled={isOptionDisabled}
          additionalStr={additionalStr}
          disableActiveValue={disableActiveValue}
          active={active}
          handleClick={handleClick}
        />
      )}
    </div>
  )
}

type ListInnerProps = {
  list: IListItem[]
  isOptionDisabled?: (item: IListItem) => boolean
  additionalStr?: string
  disableActiveValue?: boolean
  active: IListItem | null
  handleClick: (e: React.MouseEvent<HTMLSpanElement, MouseEvent>, item: IListItem) => void
}

const ListInner: FC<ListInnerProps> = props => {
  const { list, isOptionDisabled, additionalStr, disableActiveValue, active, handleClick } = props

  const { t } = useTranslation()

  return (
    <div className={cx('listInner')}>
      {list && (
        <>
          {list.map(l => {
            const disabled = isOptionDisabled ? isOptionDisabled(l) : false
            const childrenElement = l?.content ? l?.content : (additionalStr || '') + l.title

            return (
              <span
                key={`list-item-${l.id}`}
                className={cx('listItem', {
                  active: !disableActiveValue && active && active.id === l.id,
                  disabled,
                })}
                onClick={e => !disabled && handleClick(e, l)}
                title={l.title}
              >
                {childrenElement}
              </span>
            )
          })}
          {list.length === 0 && (
            <span
              key={`list-item-ntf`}
              className={cx('listItem', 'not-available', {})}
              title={t('commons:no_data')}
            >
              {t('commons:no_data')}
            </span>
          )}
        </>
      )}
    </div>
  )
}

export const Select: FC<SelectProps.Props> = props => {
  const {
    className,
    list,
    textClassName,
    listClassName,
    wrapperClassName,
    placeholder,
    value,
    loading,
    customValue = false,
    handleChange,
    label,
    required,
    help = '',
    additionalStr,
    renderLabel,
    isOptionDisabled,
    withoutIcon = false,
    disableActiveValue,
    isEmptyDefaultValue,
    disabled,
    searchable = false,
    remoteSearch = false,
    listWrapperId,
    renderListInnerWrapper,
    handleChangeSearch,
  } = props

  const wrapper = useRef<HTMLDivElement>(null)
  const { t } = useTranslation()

  const [isActive, setIsActive] = useState(false)
  const [activeValue, setActiveValue] = useState<IListItem | null>(null)
  const [searchQuery, setSearchQuery] = useState(activeValue?.title ?? '')
  const searchRef = useRef<HTMLInputElement>(null)
  const debounceSearch = useDebounce(searchQuery, 600)

  const handleClick = (isActive: boolean) => {
    if (disabled) return
    const newActive = !isActive
    setIsActive(newActive)
  }

  useEffect(() => {
    searchRef.current?.focus()
  }, [isActive])

  const setNewActiveValue = (item: IListItem) => {
    setIsActive(false)
    setActiveValue(item)
    handleChange && handleChange(item)
  }

  // TEMPTODO - костыль, чтобы работал reset. Надо наверное будет переделать select с input
  useEffect(() => {
    if (customValue === false) {
      setActiveValue(list.filter(l => l.id === value)[0] || null)
      return
    }

    setActiveValue(list.filter(l => l.id === customValue)[0] || null)
  }, [customValue, list])

  useEffect(() => {
    // костыль для сброса значения
    if (isEmptyDefaultValue && value === null && activeValue !== null) {
      setActiveValue(null)
      return
    }

    // костыль для не установки значения по умолчанию
    if (activeValue || isEmptyDefaultValue) return

    const newActiveValue =
      value === ERole.system_admin
        ? list.filter(l => l.id === ERole.employee)[0]
        : list.filter(l => l.id === value)[0] ||
          list.filter(l => l.title === t('commons:without_department'))[0] ||
          list[0] ||
          null

    setActiveValue(newActiveValue)
    handleChange && handleChange(newActiveValue)
  }, [activeValue, handleChange, value])

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) {
      setIsActive(false)
    }
  }, [])

  useEvent('click', handleOutsideClick, window)

  const isCustomRenderLabel = renderLabel && activeValue

  const getSearchableValue = () => {
    if (!activeValue) return placeholder

    if (isCustomRenderLabel) return renderLabel(activeValue)

    if (!isCustomRenderLabel && activeValue) return (additionalStr || '') + activeValue.title

    return ''
  }

  const filteredList = useMemo(() => {
    let result = list

    if (remoteSearch) {
      return result
    }

    if (searchable) {
      result = result
        .filter(item => item.title.toLowerCase().includes(debounceSearch.toLowerCase()))
        .sort((a, b) => a.title.localeCompare(b.title))
    }

    return result
      .sort(a => (a.title === t('commons:without_department') ? -1 : 1))
      .sort(a => (a.hasPriority ? -1 : 1))
  }, [list, searchable, remoteSearch, debounceSearch, t])

  useEffect(() => {
    if (handleChangeSearch) {
      handleChangeSearch(debounceSearch)
    }
  }, [debounceSearch])

  return (
    <div ref={wrapper} className={cx(className, 'container')}>
      {label && (
        <div className={cx('label')}>
          {label}
          {required && <span> *</span>}
          {help && <HelpIcon text={help} />}
        </div>
      )}
      <div
        className={cx('wrapper', { loading, disabled }, wrapperClassName)}
        onClick={() => {
          if (isActive) return

          handleClick(isActive)
        }}
      >
        <span className={cx({ placeholder }, textClassName)}>
          {isActive && (searchable || remoteSearch) ? (
            <input
              placeholder={t('')}
              ref={searchRef}
              disabled={!searchable && !remoteSearch}
              type='text'
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className={cx('searchInput')}
            />
          ) : (
            <>{getSearchableValue()}</>
          )}
          {/* {activeValue && t(`commons:${activeValue.title}`)} */}
        </span>
        {!withoutIcon && (
          <IconWrapper
            onClick={() => handleClick(isActive)}
            size='20'
            color='gray80'
            direction={!isActive ? 'right' : 'left'}
            className={cx('icon', { loading })}
          >
            {loading ? <LoadIcon /> : <ChevroneMediumIcon />}
          </IconWrapper>
        )}
      </div>
      {isActive && (
        // Сортировка, чтобы отдел "без отдела" был первым
        <div className={cx('selectList')}>
          <SelectList
            wrapperRef={wrapper}
            className={cx(listClassName)}
            list={filteredList}
            handleChange={setNewActiveValue}
            active={activeValue}
            additionalStr={additionalStr}
            isOptionDisabled={isOptionDisabled}
            disableActiveValue={disableActiveValue}
            listWrapperId={listWrapperId}
            renderListInnerWrapper={renderListInnerWrapper}
          />
        </div>
      )}
    </div>
  )
}
