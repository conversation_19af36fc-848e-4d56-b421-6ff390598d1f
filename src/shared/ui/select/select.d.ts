import { ReactNode, RefObject } from 'react'

export interface IListItem<T = UUID> {
  id: T
  title: string
  isMulti?: boolean
  hasPriority?: boolean
  isTitle?: boolean
  content?: ReactNode
  children?: IListItem<T>[]
}

export declare namespace SelectProps {
  interface Own {
    className?: string
    wrapperClassName?: string
    textClassName?: string
    listClassName?: string
    list: IListItem[]
    placeholder?: string
    value?: UUID | null
    loading?: boolean
    handleChange?: (item: IListItem) => void
    label?: string
    required?: boolean
    help?: string
    additionalStr?: string
    customValue?: UUID | null
    isOptionDisabled?: (item: IListItem) => boolean
    additionalLabelSlot?: ReactNode
    renderLabel?: (item: IListItem) => ReactNode
    withoutIcon?: boolean
    disableActiveValue?: boolean
    isEmptyDefaultValue?: boolean
    disabled?: boolean
    searchable?: boolean
    remoteSearch?: boolean
    listWrapperId?: string
    renderListInnerWrapper?: (children: ReactNode) => ReactNode
    handleChangeSearch?: (search: string) => void
  }

  type Props = Own
}

export declare namespace SelectListProps {
  interface Own {
    className?: string
    list: IListItem[]
    handleChange: (item: IListItem) => void
    active: IListItem | null
    additionalStr?: string
    isOptionDisabled?: (item: IListItem) => boolean
    wrapperRef?: RefObject
    disableActiveValue?: boolean
    searchQuery?: string
    listWrapperId?: string
    renderListInnerWrapper?: (children: ReactNode) => ReactNode
  }

  type Props = Own
}

export {}
