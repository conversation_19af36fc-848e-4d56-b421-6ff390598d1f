.container {
  position: relative;
}

.wrapper {
  align-items: center;

  background: #fff;
  border: 1px solid #ebeff2;
  border-radius: 12px;
  cursor: pointer;

  display: flex;
  justify-content: space-between;
  padding: 15px 16px;
  position: relative;

  span {
    color: #343b54;
    display: block;
    font-family: 'TT Norms Pro';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    &.placeholder {
      color: #5c6585;
      width: 100%;
    }
  }
  &.loading {
    cursor: inherit;
  }

  &.disabled {
    cursor: initial;
  }
}

.position {
  &_top {
    top: auto !important;
    bottom: 100% !important;
  }

  &_bottom {
    top: 100% !important;
    bottom: auto !important;
  }

  &_left {
    left: auto !important;
    right: 100% !important;
  }

  &_right {
    left: 100% !important;
    right: auto !important;
  }
}

.label {
  align-items: center;

  color: #343b54;

  display: flex;
  font-family: 'TT Norms Pro';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  gap: 4px;
  line-height: 18px;
  margin-bottom: 8px;
  span {
    color: #ff4b60;
  }
}

.icon {
  margin-left: auto;
  &.loading {
    animation: spin 0.7s linear infinite;

    @keyframes spin {
      100% {
        transform: rotate(360deg);
      }
    }
  }
}

.listWrapper {
  width: 100%;
  background: var(--white);
  border: 1px solid #ebeff2;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  left: 0;
  max-height: 180px;
  overflow: -moz-scrollbars-none;
  -ms-overflow-style: none;
  overflow-y: scroll;
  padding: 8px 0;
  position: absolute;
  top: 100%;
  transform: translateY(4px);

  z-index: 10;
  &::-webkit-scrollbar {
    width: 0;
  }

  .listInner {
    height: max-content;
  }
  .listItem {
    box-orient: vertical;

    color: #5c6585;

    cursor: pointer;
    display: block;
    display: box;
    font-family: 'TT Norms Pro';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    -webkit-line-clamp: 2;
    line-height: 18px;
    max-height: 50px;

    overflow: hidden;
    padding: 8px 16px;
    text-overflow: initial;

    transition: var(--transition);
    white-space: initial;

    &:hover {
      background: #f0f3f7;

      transition: var(--transition);
    }

    &.active {
      background: #f0f3f7;

      transition: var(--transition);
    }

    &.disabled {
      color: var(--color-gray-60);
      cursor: initial;
    }
  }
}

.not-available {
  color: var(--color-gray-90);
  opacity: 1;
  cursor: not-allowed;

  &:hover {
    background-color: #fff !important;
  }
}


.searchInput {
  border: none;
  width: 100%;
  margin-right: auto;

  font: var(--font-text-2-normal);
  font-weight: 300;
  background: none;
  padding: 0;
}