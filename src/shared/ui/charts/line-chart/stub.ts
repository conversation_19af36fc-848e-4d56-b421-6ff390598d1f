import { addDays } from '@/shared/helpers/date'
import { LineChartProps } from './line-chart.d'

const getDate = (offset: number) => addDays(new Date(), -offset).toISOString()

const colors = ['gray', 'light_gray']

export const getStubs = (lines: LineChartProps.Props['lines']): LineChartProps.Props['lines'] => {
  return lines.map((l, i) => ({
    ...l,
    color: colors[i] || 'gray',
    data: Array.from({ length: 15 }).map((_, i) => ({
      date: getDate(i * 2),
      value: Math.random() * i * 10,
      color: 'gray',
    })),
  }))
}
