import React, { useCallback, useMemo, useRef } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './line-chart.module.scss'

import { LineChartProps } from './line-chart.d'
import {
  Area,
  CartesianGrid,
  ComposedChart,
  Line,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from 'recharts'
import { format, fromUnixTime, getUnixTime, parseISO } from '@/shared/helpers/date'
import { getStubs } from './stub'
import { v4 as uuid } from 'uuid'
import { IStatisticsMetricBaseFragment } from '@/shared/types/deprecated'
import { useTranslation } from 'react-i18next'
import { ChartColors, LineColors } from './colors'
import { LegendItem } from '../../legend-item'
import { useLocale } from '@/shared/hooks/use-locale'

const cx = classNamesBind.bind(styles)

export const LineChart: React.FC<LineChartProps.Props> = props => {
  const {
    lines,
    title,
    max,
    min,
    dimension = '%',
    height,
    className,
    loading,
    legend = true,
    customYTick = false,
  } = props
  const locale = useLocale()

  const stub = useMemo(() => getStubs(lines), [lines])
  let _lines = loading ? stub : lines

  const data = useMemo(() => {
    const values: number[] = []

    _lines.forEach(l => {
      const data = l.data?.map(i => Number(i.value?.toFixed(1)))

      if (data) values.push(...data)
    })

    const maxTick = Math.max(...values)
    const minTick = Math.min(...values)

    if (maxTick - minTick < 0.1) {
      _lines = _lines.map(l => {
        l.data = l.data?.map(d => ({ ...d, value: maxTick }))

        return l
      })
    }

    //get unique dates from all lines
    const dates = _lines
      .reduce((all, cur) => [...all, ...(cur.data || []).map(d => d.date)], [] as string[])
      .filter((v, i, a) => a.indexOf(v) === i)

    // get lines date:value dictionary
    const datas = _lines.map(
      l =>
        l.data?.reduce(
          (all, cur) => ({ ...all, [cur.date]: cur.value }),
          {} as Record<string, IStatisticsMetricBaseFragment['value']>,
        ) || {},
    )

    return dates
      .map(d => ({
        date: getUnixTime(parseISO(d)),
        ...datas.reduce((all, cur, i) => ({ ...all, ['line' + i]: cur[d] }), {}),
      }))
      .sort((a, b) => a.date - b.date)
  }, [_lines])

  let hasData = data && !!data.length && _lines.some(l => l.data?.some(d => !!d.value))

  const YtickFormatter = useCallback(
    (value: number) => (customYTick ? '' : value.toFixed(0) + dimension),
    [dimension],
  )
  const XtickFormatter = useCallback(
    (tick: number) => format(fromUnixTime(tick), 'd MMM', locale),
    [],
  )

  const uniqueChartId = useRef<string>(uuid() + '-line')

  const { t } = useTranslation()

  let YTicks: number[] = []

  _lines.forEach(l => {
    const data = l.data?.map(i => Number(i.value?.toFixed(1)))

    if (data) YTicks.push(...data)
  })

  YTicks = [...new Set(YTicks)].sort()

  const maxTick = Math.max(...YTicks)
  const minTick = Math.min(...YTicks, 0)
  const stepCount = maxTick - minTick <= 4 ? (maxTick - minTick < 0.5 ? 1 : 2) : 3
  const stepTick = (maxTick - minTick) / stepCount

  const customYTicks = []

  for (let i = 0; i <= stepCount; i++) {
    customYTicks.push(minTick + stepTick * i)
  }

  const YTicksWithDimension = [
    ...new Set(
      customYTicks.filter(t => typeof t === 'number').map(t => +(+t).toFixed(1) + dimension),
    ),
  ].reverse()

  YTicks.forEach(i => {
    if (('' + i).indexOf('NaN') !== -1) {
      hasData = false
      YTicks.length = 0
      YTicksWithDimension.length = 0
    }
  })

  if (maxTick < 0.1) hasData = false

  return (
    <div className={cx('wrapper', className)}>
      {title && <div className={styles.title}>{title}</div>}

      {hasData ? (
        <div className={styles.chart} style={{ height }}>
          <ResponsiveContainer debounce={300} width='100%' height={'100%'}>
            <ComposedChart data={data || []}>
              <defs name='chart base colors'>
                <ChartColors />
              </defs>
              <defs name='lines gradient'>
                <LineColors lines={_lines} id={uniqueChartId.current} />
              </defs>

              <XAxis
                domain={['dataMin ', 'dataMax']}
                dataKey={'date'}
                interval={'preserveStartEnd'}
                tickMargin={10}
                tickFormatter={XtickFormatter}
                scale='point'
                tick={{
                  fontSize: 12,
                  fill: loading ? '#dcdcdc' : '#3c3934',
                  fontWeight: 'normal',
                }}
                axisLine={false}
                tickLine={false}
              />

              <YAxis
                domain={[min !== undefined ? min : 'dataMin', max !== undefined ? max : 'dataMax']}
                interval={'preserveStartEnd'}
                tickMargin={5}
                tick={{
                  fontSize: 11,
                  fill: loading ? '#dcdcdc' : '#3c3934',
                  fontWeight: 'normal',
                }}
                type={'number'}
                tickFormatter={YtickFormatter}
                orientation='right'
                width={40}
                axisLine={false}
                tickLine={false}
              />

              <CartesianGrid stroke={'#f1f1f1'} horizontal={false} />

              {
                // Charts
                _lines.map((__, i) => (
                  <Area
                    key={'area' + i}
                    isAnimationActive={false}
                    strokeWidth={0}
                    type='basis'
                    dataKey={'line' + i}
                    fillOpacity={loading ? 0.05 : 0.1}
                    fill={`url(#${uniqueChartId.current + i})`}
                    // mask='url(#fade)' // 4000
                  />
                ))
              }
              {
                // Lines
                _lines.map((__, i) => (
                  <Line
                    key={'line' + i}
                    isAnimationActive={false}
                    strokeWidth={2}
                    type='basis'
                    dataKey={'line' + i}
                    stroke={`url(#${uniqueChartId.current + i})`}
                    dot={false}
                    strokeOpacity={loading ? 0.6 : 1}
                  />
                ))
              }
            </ComposedChart>
          </ResponsiveContainer>
        </div>
      ) : (
        <div className={cx('emptyData')} style={{ height }}>
          {t('commons:no_data')}
        </div>
      )}

      {customYTick && YTicksWithDimension && hasData && (
        <div className={cx('custom-y-tick', YTicksWithDimension.length === 1 ? 'center' : '')}>
          {YTicksWithDimension.map(t => {
            return (
              <span
                key={`${t}`}
                style={{
                  fontSize: `11px`,
                  fill: loading ? '#dcdcdc' : '#3c3934',
                  fontWeight: 'normal',
                }}
              >
                {t}
              </span>
            )
          })}
        </div>
      )}

      {legend && hasData && (
        <div className={cx('legend')}>
          {_lines.map((label, i) => (
            <LegendItem
              key={i}
              className={cx('legendItem')}
              data={label}
              // onClick={onClick.bind( null, label)}
            />
          ))}
        </div>
      )}
    </div>
  )
}
