/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
//@ts-nocheck

import React from "react";
import { Colors } from "@/shared/utils";
import { LineChartProps } from "./line-chart.d";
export const ChartColors: React.FC = () => {
  /* define color gradients */
  return (
    <>
      {Object.keys(Colors).map((color) => (
        <linearGradient
          key={color}
          id={color}
          x1="0"
          y1="0"
          x2="0"
          y2="1"
          // more about https://stackoverflow.com/questions/13223636/svg-gradient-for-perfectly-horizontal-path
          gradientUnits="userSpaceOnUse"
        >
          <stop
            offset="1%"
            stopColor={Colors[color as keyof typeof Colors]}
            stopOpacity={0.4}
          />
          <stop
            offset="35%"
            stopColor={Colors[color as keyof typeof Colors]}
            stopOpacity={0.05}
          />
        </linearGradient>
      ))}
    </>
  );
};

export const LineColors: React.FC<{
  lines: LineChartProps.Props["lines"];
  id: string;
}> = ({ lines, id }) => {
  return (
    <>
      {lines.map((line: { data: any[]; color: any }, i: string) => (
        <linearGradient
          key={id + i}
          id={id + i}
          x1="0"
          y1="0"
          x2="100%"
          y2="0"
          // more about https://stackoverflow.com/questions/13223636/svg-gradient-for-perfectly-horizontal-path
          gradientUnits="userSpaceOnUse"
        >
          {(line.data?.map((d: { color: any }) => d.color) || [line.color]).map(
            (color: string, i: string | number, arr: string | any[]) => (
              <stop
                offset={(100 / arr.length) * Number(i) + "%"}
                key={"stop" + i}
                stopColor={
                  Colors[
                  (color?.toLowerCase() as keyof typeof Colors) || line.color
                  ]
                }
              />
            )
          )}
        </linearGradient>
      ))}
    </>
  );
};
