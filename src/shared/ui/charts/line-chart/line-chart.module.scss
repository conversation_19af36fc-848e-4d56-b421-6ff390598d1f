@use "../../../assets/styles/mixins/text";

.wrapper {

  display: flex;
  flex-direction: column;
  position: relative;
  &,
  * {
    box-sizing: border-box;
  }
}

.custom-tick {
  left: 95%;
  position: absolute;
  &:first-child {
    top: 0;
  }
}

.custom-y-tick {

  display: flex;
  flex-direction: column;
  height: 215px;
  justify-content: space-between;
  position: absolute;
  right: 0;
  &.center {
    justify-content: center;
  }
}

.chart {

  flex-grow: 2;
  flex-shrink: 0;
  overflow: hidden;
  position: relative;
}

.title {
  @include text.title(20px);
  + .chart {
    margin-top: 15px;
  }
}

.legend {
  display: flex;
  flex-grow: 1;
  justify-content: center;
  margin-top: 10px;
  .legendItem + .legendItem {
    margin-left: 20px;
  }
}

.emptyData {
  @include text.title(20px);
  align-items: center;

  color: var(--gray-sub);
  display: flex;
  justify-content: center;
  margin: auto;
}
