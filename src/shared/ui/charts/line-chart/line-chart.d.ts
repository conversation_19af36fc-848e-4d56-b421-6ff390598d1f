import { IStatisticsMetricBaseFragment } from "react-app/new/types/deprecated/graphql";

export declare namespace LineChartProps {
  interface Own {
    className?: string;
    lines: Array<{
      title?: string;
      color: string;
      data?: Array<
        Omit<IStatisticsMetricBaseFragment, "color"> & { color?: string }
      >;
    }>;

    legend?: boolean;
    title?: string;
    max?: number;
    min?: number;
    dimension?: string;
    height: number | string;
    loading?: boolean;
    customYTick?: boolean;
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch;
}

export {};
