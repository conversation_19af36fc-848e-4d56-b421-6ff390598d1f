// @use "media";
@use "../../../assets/styles/mixins/text";

.wrapper {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  &,
  * {
    box-sizing: border-box;
  }
}

.content {
  align-items: center;
  display: flex;
  flex-grow: 1;
  justify-content: space-between;
}

.title {
  @include text.title(20px);
  + .content {
    margin-top: 15px;
  }
}

.legend {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  flex-shrink: 0;
  justify-content: center;
  margin-left: 20px;
  width: 40%;
  .legendItem + .legendItem {
    margin-top: 20px;
  }
}

.chart {

  flex-grow: 2;
  overflow: hidden;
  position: relative;
}

.chartPercentLabel {
  fill: var(--white);
  font-size: 10px;
  font-weight: bold;

  pointer-events: none;
  text-anchor: middle;
}
.pieCell {
  cursor: pointer;

  transition: 300ms ease;
  &:hover {
    opacity: 0.8;
  }
}

.chartLabel {
  display: flex;
  flex: 3 0;
  flex-direction: column;
  margin-left: 20px;

  // @include media.mobile {
  //   margin-left: 0;
  //   margin-top: 20px;
  //   padding-bottom: 10px;
  // }
}

.emptyData {
  @include text.title(20px);
  align-items: center;

  color: var(--gray-sub);
  display: flex;
  justify-content: center;
  margin: auto;
}
