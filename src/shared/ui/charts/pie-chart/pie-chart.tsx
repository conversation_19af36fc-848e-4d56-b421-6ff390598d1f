/* eslint-disable @typescript-eslint/ban-ts-comment */
//@ts-nocheck
/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useMemo } from "react";
import classNamesBind from "classnames/bind";
import styles from "./pie-chart.module.scss";

import { PieChartProps } from "./pie-chart.d";
import {
  Cell,
  Pie,
  PieChart as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  PieLabelRenderProps,
  ResponsiveContainer,
} from "recharts";
import { getStubs } from "./stub";
import { v4 } from "uuid";
import { useTranslation } from "react-i18next";
import { LegendItem } from "@/shared/ui/legend-item";
import { Colors } from "@/shared/utils";

const cx = classNamesBind.bind(styles);

const RADIAN = Math.PI / 180;

const renderCustomizedLabel = (props: PieLabelRenderProps) => {
  const {
    midAngle = 0,
    cx = 0,
    cy = 0,
    innerRadius = 0,
    outerRadius = 0,
    percent = 0,
  } = props;
  const radius = +innerRadius + (+outerRadius - +innerRadius) * 0.5;
  const x = +cx + radius * Math.cos(-midAngle * RADIAN);
  const y = +cy + radius * Math.sin(-midAngle * RADIAN);

  // filter "0" values
  if (percent < 0.05) return null;

  return (
    <text
      x={x}
      y={y}
      dominantBaseline="central"
      className={styles.chartPercentLabel}
    >
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const PieChart: React.FC<PieChartProps.Props> = (props) => {
  const {
    className,
    title,
    height,
    data,
    loading,
    legend = true,
    onClick,
  } = props;

  const pies = useMemo(() => {
    const _pies = loading ? getStubs(data) : data;

    // При простом округлении может сложиться ситуация, когда сумма частей будет > 100%
    // по этому мы просчитываем проценты всех делений и у первого деления подгоняем проценты под 100

    const total = _pies.reduce(
      (all: any, cur: { value: any }) => (cur.value ? all + cur.value : all),
      0
    );
    const arr = _pies
      .filter((p: { value: any }) => !!p.value)
      .map((p: { value: any }) => ({
        ...p,
        value: Math.round((p.value! * 100) / total),
      }));

    if (!arr.length) return arr;
    const _all = arr
      .slice(1)
      .reduce((all: any, cur: { value: any }) => all + cur.value, 0);
    arr[0].value = 100 - _all;
    return arr;
  }, [data, loading]);

  const hasData =
    pies && !!pies.length && pies.some((p: { value: any }) => !!p.value);

  // const isMultiline = pies.filter(p => p.value).length > 1
  const _onClick = (group?: string) =>
    group && onClick && !loading && onClick(group);

  const { t } = useTranslation();

  return (
    <div className={cx('wrapper', className)}>
      {title && <div className={styles.title}>{title}</div>}
      <div className={cx('content')}>
        {hasData ? (
          <div className={cx('chart')} style={{ height, width: height }}>
            <ResponsiveContainer debounce={300} width='100%' height='100%'>
              <RePieChart>
                <Pie
                  key={v4()} // force rerender on data loaded
                  isAnimationActive={false}
                  //@ts-ignore
                  isUpdateAnimationActive={false}
                  data={pies}
                  labelLine={false}
                  label={loading ? undefined : renderCustomizedLabel}
                  innerRadius='60%'
                  outerRadius='100%'
                  dataKey='value'
                >
                  {pies?.map((entry: { color: string; group: string | undefined }, index: any) => (
                    <Cell
                      className={cx('pieCell')}
                      key={`cell-${index}`}
                      strokeWidth={10}
                      opacity={loading ? 0.5 : 1}
                      fill={
                        Colors[
                        (entry.color.toLowerCase() as keyof typeof Colors) ||
                        // eslint-disable-next-line i18next/no-literal-string
                        'blue'
                        ]
                      }
                      onClick={_onClick.bind(null, entry.group)}
                    />
                  ))}
                </Pie>
              </RePieChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className={cx('emptyData')} style={{ height }}>
            {t('commons:no_data')}
          </div>
        )}
        {legend && hasData && (
          <div className={cx('legend')}>
            {pies?.map(
              (
                label: {
                  group?: any
                  id?: string | undefined
                  color?: string
                  title?: string | undefined
                },
                i: React.Key | null | undefined,
              ) => (
                <LegendItem
                  key={i}
                  className={cx('legendItem')}
                  //@ts-ignore
                  data={label}
                  onClick={_onClick.bind(null, label.group)}
                />
              ),
            )}
          </div>
        )}
      </div>
    </div>
  )
};

export { PieChart };
