export declare namespace PieChartProps {
  interface Own {
    className?: string;

    data: Array<{
      title?: string;
      color: string;
      value?: number;
      group?: string;
    }>;

    title?: string;
    height?: string | number;
    loading?: boolean;
    legend?: boolean;

    onClick?(group: string): void;
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch;
}

export {};
