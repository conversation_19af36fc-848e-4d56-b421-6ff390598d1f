import { ReactNode, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './text-area-autocomplete.module.scss'

const cx = classNamesBind.bind(styles)

type Props = {
  value?: string
  inputPlaceholder?: string
  leftAdornment?: ReactNode
  onChange?: (value: string) => void
}

export const TextAreaAutocomplete = ({
  value,
  leftAdornment,
  inputPlaceholder,
  onChange,
}: Props) => {
  const [isFocus, setIsFocus] = useState(false)
  const [isHover, setIsHover] = useState(false)

  const handleChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    if (!onChange) return

    onChange(evt.target.value)
  }

  return (
    <div className={cx('autocomplete', isFocus && 'focus', isHover && 'hover')}>
      {leftAdornment}
      <input
        className={cx('input')}
        onChange={handleChange}
        onBlur={() => setIsFocus(false)}
        onFocus={() => setIsFocus(true)}
        onMouseEnter={() => setIsHover(true)}
        onMouseLeave={() => setIsHover(false)}
        value={value || ''}
        placeholder={inputPlaceholder}
      />
    </div>
  )
}
