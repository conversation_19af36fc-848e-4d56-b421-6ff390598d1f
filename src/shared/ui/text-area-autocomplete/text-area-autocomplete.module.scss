.autocomplete {
  position: relative;
  box-sizing: border-box;
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  padding: 10px 14px;
  gap: 6px;
  color: initial;
  width: 100%;
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #ebeff2;
}

.hover {
  border-color: #c9cedc;
}

.focus {
  box-shadow:
    0 0 0 2px var(--color-primary),
    0 0 0 50px #fff inset !important;
  caret-color: var(--color-primary);
}

.input {
  width: 0;
  min-width: 30px;
  padding: 3px 0;
  flex-grow: 1;
  font: var(--font-text-2-medium);
  color: var(--color-gray-90, #343b54);
  opacity: 1;
  border: none;
}

.tag {
  display: inline-flex;
  width: max-content;
}
