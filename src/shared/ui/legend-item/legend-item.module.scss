.wrapper {
  align-items: center;

  cursor: pointer;
  display: flex;
}

.active {
  align-items: center;

  background-color: var(--white);
  border-radius: 40px;
  box-shadow: 0 0 10px 0 #dcdcdc;
  display: flex;
  flex-shrink: 0;
  height: 40px;
  justify-content: center;
  margin-right: 10px;
  width: 40px;
  .label {
    margin: 0;
  }
}

.label {

  border: solid 2px;
  border-radius: 6px;
  display: flex;
  flex-shrink: 0;
  height: 10px;
  margin-right: 10px;
  width: 10px;

  &.size {
    &-l {

      border: solid 3px;
      border-radius: 8px;
      height: 14px;
      width: 14px;
    }
  }

  &.color {
    &-red {
      border-color: var(--color-statistics-bad);
    }

    &-green {
      border-color: var(--color-statistics-good);
    }

    &-blue {
      border-color: var(--color-statistics-complementary);
    }

    &-yellow {
      border-color: var(--color-statistics-warning);
    }
    &-gray {
      border-color: var(--grey);
    }
    &-light_gray {
      border-color: var(--silver);
    }
  }
}

.title {
  font-size: 16px;
}

@media (min-width: 320px) and (max-width: 768px) {
  .wrapper {
    margin: 0;
    padding-left: 10px;
  }

  .label {

    border: solid 2px;
    border-radius: 6px;
    display: inline-block;
    height: 3px;
    margin-right: 10px;
    width: 3px;
  }
}
