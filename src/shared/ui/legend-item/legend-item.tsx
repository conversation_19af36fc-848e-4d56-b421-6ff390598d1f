import React from "react";
import classNamesBind from "classnames/bind";
import styles from "./legend-item.module.scss";

import { LegendItemProps } from "./legend-item.d";
import Skeleton from "react-loading-skeleton";

const cx = classNamesBind.bind(styles);

const LegendItem: React.FC<LegendItemProps.Props> = (props) => {
  const { className, data, active, size, onClick } = props;

  const _onClick = () => {
    onClick && onClick(data.id);
  };

  return (
    <div className={cx("wrapper", className)} onClick={_onClick}>
      <div className={cx("labelWrapper", { active })}>
        <div className={cx("label", "color-" + data.color, "size-" + size)} />
      </div>
      {"title" in data && (
        <div className={cx("title", "size-" + size)}>
          {data.title || <Skeleton width="100%" />}
        </div>
      )}
    </div>
  );
};

export { LegendItem };
