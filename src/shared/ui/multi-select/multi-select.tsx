import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import styles from './multi-select.module.scss'
import classNamesBind from 'classnames/bind'
import { MultiSelectProps, MultiSelectListProps, MultiSelectListItemProps } from './multi-select.d'
import { IListItem } from '@/shared/ui'
import ChevroneMediumIcon from '../Icon/icons/components/ChevroneMediumIcon'
import { IconWrapper } from '../Icon/IconWrapper'
import { useDebounce, useEvent } from '@/shared/hooks'
import { Checkbox } from '../checkbox'
import { useTranslation } from 'react-i18next'
import { filterList } from './lib'
import React from 'react'
import { LoadIcon } from '../Icon/icons/components'

const cx = classNamesBind.bind(styles)

const ListItem: FC<MultiSelectListItemProps.Props> = props => {
  const { listItemClassName, handleChange, info, isActive, renderListValue } = props
  const { title, isTitle } = info

  const handleClick = () => handleChange(info)

  return (
    <div title={title} className={cx('listItem', listItemClassName, { active: isActive })}>
      {isTitle ? (
        <span className={cx('title')}>{title}</span>
      ) : renderListValue ? (
        renderListValue(isActive, info)
      ) : (
        <Checkbox
          onChange={handleClick}
          customChecked={isActive}
          label={<span className={cx('checkboxLabel')}>{title}</span>}
        />
      )}
    </div>
  )
}
const List: FC<MultiSelectListProps.Props> = props => {
  const {
    className,
    listItemClassName,
    handleChange,
    selected,
    list,
    renderListValue,
    isListTop,
    renderListInnerWrapper,
    listWrapperId,
  } = props

  return (
    <div className={cx('listMainWrapper', { isListTop: isListTop })}>
      <div
        id={listWrapperId}
        className={cx('listWrapper', 'scrollbar', className)}
        style={styles}
        onClick={e => e.stopPropagation()}
      >
        {renderListInnerWrapper ? (
          renderListInnerWrapper(
            <ListInner
              list={list}
              selected={selected}
              listItemClassName={listItemClassName}
              handleChange={handleChange}
              renderListValue={renderListValue}
            />,
          )
        ) : (
          <ListInner
            list={list}
            selected={selected}
            listItemClassName={listItemClassName}
            handleChange={handleChange}
            renderListValue={renderListValue}
          />
        )}
      </div>
    </div>
  )
}

type ListInnerProps = {
  list: IListItem[]
  selected: UUID[]
  listItemClassName?: string
  handleChange: (i: IListItem) => void
  renderListValue?: (isActive: boolean, info: IListItem) => React.ReactNode
}

const ListInner: FC<ListInnerProps> = ({
  list,
  selected,
  listItemClassName,
  handleChange,
  renderListValue,
}) => {
  const { t } = useTranslation()

  return (
    <div className={cx('listInner')}>
      {list && (
        <>
          {list.map(l => {
            const isActive = selected.indexOf(l.id) !== -1

            return (
              <React.Fragment key={`list-item-${l.id}`}>
                <ListItem
                  info={l}
                  listItemClassName={listItemClassName}
                  handleChange={handleChange}
                  isActive={isActive}
                  renderListValue={renderListValue}
                />
                {l?.children &&
                  l?.children?.length > 0 &&
                  l?.children.map((element: IListItem) => {
                    const isNestedActive = selected.indexOf(element.id) !== -1

                    return (
                      <ListItem
                        key={`list-item-${element.id}`}
                        info={element}
                        listItemClassName={listItemClassName}
                        handleChange={handleChange}
                        isActive={isNestedActive}
                        renderListValue={renderListValue}
                      />
                    )
                  })}
              </React.Fragment>
            )
          })}
          {list.length === 0 && (
            <ListItem
              key={`list-item-ntf`}
              info={{ isTitle: true, title: t('commons:no_data') }}
              listItemClassName={cx(listItemClassName, 'not-available')}
              handleChange={() => {}}
              isActive={false}
              renderListValue={renderListValue}
            />
          )}
        </>
      )}
    </div>
  )
}

export const MultiSelect: FC<MultiSelectProps.Props> = props => {
  const {
    className,
    listItemClassName,
    inputBaseClassName,
    label,
    placeholder,
    list,
    initialValue = [],
    onChange,
    customValue,
    renderValue,
    renderListValue,
    isListTop,
    searchable = false,
    renderListInnerWrapper,
    listWrapperId,
    loading,
    remoteSearch = false,
    handleChangeSearch,
  } = props

  const { t } = useTranslation()

  const wrapper = useRef<HTMLDivElement>(null)

  const [isActive, setIsActive] = useState(false)
  const [selectedValues, setSelectedValues] = useState<UUID[]>(initialValue)
  const [searchQuery, setSearchQuery] = useState('')
  const debounceSearch = useDebounce(searchQuery, 600)

  const searchRef = useRef<HTMLInputElement>(null)

  const handleClick = (isActive: boolean) => setIsActive(!isActive)

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) {
      setIsActive(false)
    }
  }, [])

  useEffect(() => {
    searchRef.current?.focus()
  }, [isActive])

  useEvent('click', handleOutsideClick, window)

  const handleChange = (i: IListItem) => {
    const { id } = i

    if (customValue) return onChange && onChange(i)

    const position = selectedValues.indexOf(id)

    if (position === -1) {
      setSelectedValues(prev => [...prev, id])
    } else {
      setSelectedValues(prev => [...prev.slice(0, position), ...prev.slice(position + 1)])
    }
  }

  const filteredList = useMemo(() => {
    let result = list

    if (remoteSearch) {
      return result
    }

    if (!searchable) {
      const hasPriority = result.some(i => i.hasPriority)

      if (hasPriority) return result.sort(a => (a.hasPriority ? -1 : 1))

      return result
    }

    result = filterList(list, debounceSearch)

    const hasPriority = result.some(i => i.hasPriority)

    if (hasPriority) result.sort(a => (a.hasPriority ? -1 : 1))

    return result
  }, [list, debounceSearch, searchable, remoteSearch])

  useEffect(() => {
    if (handleChangeSearch) {
      handleChangeSearch(debounceSearch)
    }
  }, [debounceSearch])

  const value = customValue ? customValue : selectedValues
  const valueLength = value.length
  const title =
    valueLength === 0 ? placeholder : t('commons:select_values_count', { count: valueLength })

  return (
    <div
      className={cx('wrapper', className, {
        active: !!valueLength,
        loading,
      })}
    >
      {label && <div className={cx('label')}>{label}</div>}
      <div
        ref={wrapper}
        className={cx('inner', inputBaseClassName)}
        onClick={() => {
          if (isActive) return
          handleClick(isActive)
        }}
      >
        {isActive && (searchable || remoteSearch) ? (
          <input
            ref={searchRef}
            disabled={!searchable && !remoteSearch}
            type='text'
            value={searchQuery}
            onChange={e => {
              e.stopPropagation()
              setSearchQuery(e.target.value)
            }}
            className={cx('searchInput')}
          />
        ) : (
          <>
            {renderValue ? (
              <span className={cx('lineHeightFix', { placeholder })}>
                {value.length === 0 && placeholder}
                {renderValue()}
              </span>
            ) : (
              <span className={cx({ placeholder })}>
                {!selectedValues && placeholder}
                {selectedValues && title}
              </span>
            )}
          </>
        )}

        <IconWrapper
          onClick={() => handleClick(isActive)}
          size='20'
          color='gray80'
          direction={!isActive ? 'right' : 'left'}
          className={cx('icon', { loading })}
        >
          {loading ? <LoadIcon /> : <ChevroneMediumIcon />}
        </IconWrapper>

        {isActive && (
          <List
            listWrapperId={listWrapperId}
            searchable={searchable || remoteSearch}
            list={filteredList}
            listItemClassName={listItemClassName}
            handleChange={handleChange}
            selected={value}
            renderListValue={renderListValue}
            isListTop={isListTop}
            renderListInnerWrapper={renderListInnerWrapper}
          />
        )}
      </div>
    </div>
  )
}
