import { IListItem } from '../select'

const sortList = (list: IListItem[]): IListItem[] => {
  return list
    .sort((a, b) => a.title.localeCompare(b.title))
    .map(item => ({
      ...item,
      children: item.children ? sortList(item.children) : [],
    }))
}

export const filterList = (list: IListItem[], searchQuery: string): IListItem[] => {
  const lowerCaseQuery = searchQuery.toLowerCase()

  const filterItem = (item: IListItem): IListItem | null => {
    const filteredChildren = item.children ? item.children.map(filterItem).filter(Boolean) : []

    const isTitleMatch = item.title.toLowerCase().includes(lowerCaseQuery)

    if (item.isTitle) {
      if (filteredChildren && filteredChildren.length > 0) {
        return { ...item, children: (filteredChildren as IListItem[]) ?? [] }
      }
      return null
    }

    if (isTitleMatch || (filteredChildren && filteredChildren.length > 0)) {
      return { ...item, children: (filteredChildren as IListItem[]) ?? [] }
    }

    return null
  }

  const filteredList = list.map(filterItem).filter(Boolean) as IListItem[]

  // Сортировка по алфавиту всего списка, включая вложенные элементы
  const sortedList = sortList(filteredList)

  return sortedList
}