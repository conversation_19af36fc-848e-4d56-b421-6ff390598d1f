import { UUID } from 'react-app/new/types/IUser'
import { IListItem } from '../Select/Select'

type RenderValue = () => React.ReactNode | string

export type RenderListValue = (isActive: boolean, info: IListItem) => React.ReactNode

export declare namespace MultiSelectProps {
  interface Own {
    className?: string
    listItemClassName?: string
    inputBaseClassName?: string
    label?: React.ReactNode
    placeholder?: string
    list: IListItem[]
    initialValue?: UUID[]
    onChange?: (item: IListItem) => void
    customValue?: UUID[] | null
    renderValue?: RenderValue
    renderListValue?: RenderListValue
    isListTop?: boolean
    searchable?: boolean
    renderListInnerWrapper?: (children: React.ReactNode) => React.ReactNode
    listWrapperId?: string
    remoteSearch?: boolean
    handleChangeSearch?: (search: string) => void
    loading?: boolean
  }

  type Props = Own
}

export declare namespace MultiSelectListProps {
  interface Own {
    className?: string
    listItemClassName?: string
    renderListInnerWrapper?: (children: React.ReactNode) => React.ReactNode
    list: IListItem[]
    handleChange: (item: IListItem) => void
    selected: UUID[]
    renderListValue?: RenderListValue
    isListTop?: boolean
    searchable?: boolean
    listWrapperId?: string
  }

  type Props = Own
}

export declare namespace MultiSelectListItemProps {
  interface Own {
    listItemClassName?: string
    handleChange: (item: IListItem) => void
    info: IListItem
    isActive: boolean
    renderListValue?: RenderListValue
  }

  type Props = Own
}

export {}
