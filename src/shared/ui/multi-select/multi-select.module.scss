@use '../../../shared/assets/styles/mixins/text';

.wrapper {
  position: relative;

  width: 100%;

  &.loading {
    cursor: inherit;
  }
}

.active {
  .inner {
    border: 2px solid var(--color-primary-80, #68cba2);
  }
}

.label {
  display: flex;
  align-items: center;
  gap: 4px;

  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
  margin-bottom: 8px;
}

.checkboxLabel {
  box-orient: vertical;

  color: var(--color-gray-80, #5c6585);
  display: box;
  font: var(--font-text-2-normal);
  -webkit-line-clamp: 2;

  word-break: break-word;
  overflow: hidden;
  text-overflow: initial;
  white-space: initial;
  &.title {
    color: var(--color-gray-90, #343b54);
    font: var(--font-text-2-medium);
  }
}

.inner {
  align-items: center;

  border: 2px solid var(--color-gray-60, #c9cedc);
  border-radius: 8px;
  cursor: pointer;
  display: grid;
  gap: 8px;
  grid-template-columns: 1fr auto;
  justify-content: space-between;
  padding: 10px 8px 10px 12px;
  width: 100%;

  > span {
    color: var(--color-gray-80, #5c6585);
    font: var(--font-text-2-normal);
    overflow: hidden;
    text-overflow: ellipsis;

    white-space: nowrap;
  }
}

.listMainWrapper {
  background: #fff;
  border: 1px solid #ebeff2;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  left: 0;
  padding: 8px 0;
  position: absolute;
  top: 100%;
  transform: translateY(4px);

  width: 100%;
  z-index: 11;

  &.isListTop {
    top: auto;
    bottom: 0;
    transform: translateY(-52px);
  }
}

.listWrapper {
  max-height: 180px;
  overflow-y: scroll;
  .listInner {
    height: max-content;
  }
}

.listItem {
  align-items: center;

  cursor: pointer;
  display: grid;
  grid-gap: 8px;
  padding: 8px 16px;

  transition: var(--transition);
  &:hover {
    background: #f0f3f7;

    transition: var(--transition);
  }
  &.active {
    background: #f0f3f7;

    transition: var(--transition);
  }

  > span {
    box-orient: vertical;

    color: var(--color-gray-80, #5c6585);
    display: box;
    font: var(--font-text-2-normal);
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: initial;
    white-space: initial;
    word-break: break-word;

    &.title {
      color: var(--color-gray-90, #343b54);
      font: var(--font-text-2-medium);
    }
  }
}

span.lineHeightFix {
  line-height: 1.715;
}

.searchInput {
  border: none;
  width: 100%;
  height: 20px;
  margin-right: auto;

  font: var(--font-text-2-normal);
  font-weight: 300;
  background: none;
}

.not-available {
  color: var(--color-gray-90);
  opacity: 1;
  cursor: not-allowed;

  &:hover {
    background-color: #fff !important;
  }
}

.icon {
  &.loading {
    animation: spin 0.7s linear infinite;

    @keyframes spin {
      100% {
        transform: rotate(360deg);
      }
    }
  }
}
