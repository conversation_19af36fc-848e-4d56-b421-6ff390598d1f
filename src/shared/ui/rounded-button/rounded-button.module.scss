.positionWrapper {
  position: relative;
  .menu {

    background: var(--color-surface, #fff);
    border: 1px solid var(--color-gray-30, #ebeff2);

    border-radius: 8px;
    bottom: -5px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);

    display: none;
    padding: 6px 1px;
    position: absolute;
    right: 0;
    transform: translateY(100%);

    width: max-content;
    z-index: 12;
    .item {

      background: var(--color-surface, #fff);

      color: var(--color-gray-80, #5c6585);
      cursor: pointer;
      font-family: TT Norms Pro;
      font-size: 14px;
      font-style: normal;
      font-weight: 450;
      line-height: 18px;
      padding: 8px 16px;

      transition: var(--transition);
      &:hover {
        background: var(--color-gray-40, #f0f3f7);

        transition: var(--transition);
      }
    }
    &.active {
      display: block;
    }
  }

  &.size {
    &--24 {
      height: 24px;
      width: 24px;
    }
    &--40 {
      height: 40px;
      width: 40px;
    }
  }
}

.wrapper {
  align-items: center;

  background: transparent;

  border: 2px solid var(--color-primary);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  outline: none;

  &.hasBackground {
    background: var(--color-primary-90);
    border: none;

    transition: var(--transition);
    &:hover {
      background: var(--color-primary);

      transition: var(--transition);
    }
    &:active {
      background: var(--color-primary);

      transition: var(--transition);
    }

    &.linear {
      background: linear-gradient(58.48deg, #3dbc87 10.05%, #a9e1ca 90.02%);
      &:hover {
        background: linear-gradient(
          58.48deg,
          var(--color-primary) 10.05%,
          var(--color-primary)
        );
      }
      &:active {
        background: linear-gradient(
          58.48deg,
          var(--color-primary) 10.05%,
          var(--color-primary)
        );
      }
    }
  }

  &.size {
    &--24 {
      height: 24px;
      width: 24px;
    }
    &--40 {
      height: 40px;
      width: 40px;
    }
  }

  &.disabled {
    background: var(--color-primary-60);
    cursor: default;

    pointer-events: none;
  }
}
