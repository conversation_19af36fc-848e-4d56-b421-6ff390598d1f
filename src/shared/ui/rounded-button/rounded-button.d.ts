export interface IMenuItem {
  name?: string;
  onClick?: (name?: string) => void;
  text: string;
  isVisible?: boolean;
}

export declare namespace RoundedButtonProps {
  interface Own
    extends React.DetailedHTMLProps<
      React.ButtonHTMLAttributes<HTMLButtonElement>,
      HTMLButtonElement
    > {
    className?: string;
    size: "40" | "24";
    hasBackground?: boolean;
    disabled?: boolean;
    linear?: boolean;
    menu?: IMenuItem[];
  }

  type Props = Own;
}

export {};
