import React, { FC, useCallback, useRef, useState } from 'react'
import styles from './rounded-button.module.scss'
import classNamesBind from 'classnames/bind'
import { IMenuItem, RoundedButtonProps } from './rounded-button.d'
import PlusIcon from '../Icon/icons/components/PlusIcon'
import { IconWrapper } from '../Icon/IconWrapper'
import { useEvent } from '@/shared/hooks'

const cx = classNamesBind.bind(styles)

export const RoundedButton: FC<RoundedButtonProps.Props> = props => {
  const {
    className,
    hasBackground = true,
    disabled = false,
    onClick,
    size = '40',
    linear = false,
    menu,
  } = props

  const [openMenu, setOpenMenu] = useState(false)

  const handleClick = (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    if (!menu) return onClick && onClick(event)

    setOpenMenu(prev => !prev)
  }

  const handleClickMenuItem = (i: IMenuItem) => {
    i.onClick && i.onClick(i.name)
    setOpenMenu(() => false)
  }

  const wrapper = useRef<HTMLDivElement>(null)

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) {
      setOpenMenu(() => false)
    }
  }, [])

  useEvent('click', handleOutsideClick, window)

  return (
    <div className={cx('positionWrapper', `size--${size}`)} ref={wrapper}>
      <button
        className={cx('wrapper', `size--${size}`, className, {
          disabled,
          hasBackground,
          linear,
        })}
        onClick={handleClick}
        disabled={disabled}
      >
        <IconWrapper size={size === '40' ? '24' : '16'} color={hasBackground ? 'white' : 'primary'}>
          <PlusIcon />
        </IconWrapper>
      </button>
      {menu && (
        <div className={cx('menu', { active: openMenu })}>
          {menu.map(i => {
            if (i.isVisible === false) return <React.Fragment key={`menu-item-${i.name}`} />

            return (
              <div
                key={`menu-item-${i.name}`}
                className={cx('item')}
                onClick={() => handleClickMenuItem(i)}
              >
                {i.text}
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}
