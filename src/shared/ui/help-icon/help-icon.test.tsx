import '@testing-library/jest-dom'
import { HelpIcon } from './help-icon'
import { renderTestScreen } from '@/shared/helpers/tests/render'

const text = 'Очень длинный текст для данной подсказки, чтобы показать ее длину'

describe('[shared] [ui] HelpIcon', () => {
  it(`Должен отрендериться`, () => {
    const { queryByTestId } = renderTestScreen(<HelpIcon text={text} />)

    const tooltip = queryByTestId('HelpIcon.Text')

    expect(tooltip).toHaveTextContent(text)
  })
})
