import type { Meta, StoryObj } from '@storybook/react'
import { HelpIcon } from './help-icon'

const meta: Meta<typeof HelpIcon> = {
    title: 'UI/HelpIcon',
    component: HelpIcon,
    tags: ['autodocs'],
    argTypes: {
        tooltipClassname: {
            description: 'Classname для текста',
            table: {
                type: { summary: 'String' },
                readonly: true
            },
            control: 'text',
        },
        text: {
            description: 'Текст всплывающей подсказки',
            table: {
                type: { summary: 'String' },
            },
            control: 'text',
        },
    }
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: { text: 'Очень длинный текст для данной подсказки, чтобы показать ее длину' }
}

// export const WithTooltip: Story = {
//     args: { text: 'У данного тултипа применен :hover в storybook. Очень длинный текст для данной подсказки, чтобы показать ее длину' },
//     parameters: { pseudo: { hover: true } }
// }