import { FC } from 'react'
import styles from './help-icon.module.scss'
import classNamesBind from 'classnames/bind'
import { HelpIconProps } from './help-icon.d'
import QuestionIcon from '@/shared/ui/Icon/icons/components/QuestionIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

const cx = classNamesBind.bind(styles)

export const HelpIcon: FC<HelpIconProps.Props> = props => {
  const { className, text, tooltipClassname, 'data-testid': dataTestid = 'HelpIcon' } = props

  return (
    <div className={cx('wrapper', className)}>
      <IconWrapper size='20' color='gray60' className={cx('icon')} data-testid={dataTestid}>
        <QuestionIcon />
      </IconWrapper>
      <div className={cx('inner', tooltipClassname)} data-testid={`${dataTestid}.Text`}>
        {text}
      </div>
    </div>
  )
}
