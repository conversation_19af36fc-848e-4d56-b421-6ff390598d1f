.wrapper {
  position: relative;
  &:hover {
    .inner {
      display: block;
    }
  }
}

.icon {
  cursor: pointer;
}

.inner {
  background: var(--color-gray-90);

  color: var(--color-surface);

  display: none;
  font: var(--font-caption-2-normal);
  left: 0;
  max-width: 250px;
  opacity: 0.7;
  padding: 10px;
  position: absolute;
  top: 50%;
  transform: translateX(24px) translateY(-50%);
  width: max-content;
  z-index: 10;
}
