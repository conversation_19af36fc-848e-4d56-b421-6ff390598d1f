import type { Meta, StoryObj } from '@storybook/react'
import { Button } from './button'

const meta = {
    title: 'UI/Button',
    component: Button,
} satisfies Meta<typeof Button>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: { children: 'Нажми меня' }
}

export const ScreenshotTest: Story = {
    args: { children: 'Нажми меня' },
    decorators: () => {
        return <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
            alignItems: 'start'
        }}>
            <h3>Дефолтная</h3>
            <Button>Нажми меня</Button>

            <h3>Цвета</h3>
            <Button color='green'>Нажми меня</Button>
            <Button color='red'>Нажми меня</Button>
            <Button color='gray'>Нажми меня</Button>
            <Button color='darkGray'>Нажми меня</Button>

            <h3>Прозрачная</h3>
            <Button withoutBackground color='green'>Нажми меня</Button>
            <Button withoutBackground color='red'>Нажми меня</Button>
            <Button withoutBackground color='gray'>Нажми меня</Button>
            <Button withoutBackground color='darkGray'>Нажми меня</Button>

            <h3>Ссылка</h3>
            <Button isLink color='green'>Нажми меня</Button>
            <Button isLink color='red'>Нажми меня</Button>
            <Button isLink color='gray'>Нажми меня</Button>
            <Button isLink color='darkGray'>Нажми меня</Button>

            <h3>Рамка</h3>
            <Button color='border' view='border'>Нажми меня</Button>

            <h3>Загрузка и дизейбл</h3>
            <Button loading>Нажми меня</Button>
            <Button disabled>Нажми меня</Button>
            <Button loading disabled>Нажми меня</Button>

            <h3>На полную ширину</h3>
            <Button fullWidth>Нажми меня</Button>
            <Button fullWidth loading>Нажми меня</Button>
            <Button fullWidth disabled>Нажми меня</Button>
            <Button fullWidth loading disabled>Нажми меня</Button>

            <h3>С иконками</h3>
            <Button leftIcon='plus'>Нажми меня</Button>
            <Button rightIcon='plus'>Нажми меня</Button>
            <Button leftIcon='plus' rightIcon='plus'>Нажми меня</Button>
            <Button leftIcon='plus' rightIcon='plus' disabled>Нажми меня</Button>
            <Button leftIcon='plus' rightIcon='plus' loading>Нажми меня</Button>
            <Button leftIcon='plus' rightIcon='plus' loading disabled>Нажми меня</Button>
            <Button leftIcon='plus' rightIcon='plus' fullWidth>Нажми меня</Button>
            <Button leftIcon='plus' rightIcon='plus' fullWidth disabled>Нажми меня</Button>
            <Button leftIcon='plus' rightIcon='plus' fullWidth loading>Нажми меня</Button>
            <Button leftIcon='plus' rightIcon='plus' fullWidth loading disabled>Нажми меня</Button>




        </div>
    }
}