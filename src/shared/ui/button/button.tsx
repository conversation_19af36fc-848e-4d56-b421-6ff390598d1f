// eslint-disable-next-line @typescript-eslint/ban-ts-comment
//@ts-nocheck
import { FC, forwardRef } from 'react'
import styles from './button.module.scss'
import classNamesBind from 'classnames/bind'
import { ButtonProps } from './button.d'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import ClipSmallIcon from '@/shared/ui/Icon/icons/components/ClipSmallIcon'
import KeyIcon from '@/shared/ui/Icon/icons/components/KeyIcon'
import FilterIcon from '@/shared/ui/Icon/icons/components/FilterIcon'
import DownloadIcon from '@/shared/ui/Icon/icons/components/DownloadIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { TIconColor } from '@/shared/ui/Icon/icon.d'

const cx = classNamesBind.bind(styles)

// Функция для рендеринга иконок
const renderIcon = (iconName: string, color?: TIconColor, testId?: string) => {
  const iconWrapper = (IconComponent: FC<{ className?: string }>) => (
    <IconWrapper size='20' color={color} data-testid={testId}>
      <IconComponent />
    </IconWrapper>
  )

  switch (iconName) {
    case 'plus':
      return iconWrapper(PlusIcon)
    case 'clipBold':
      return iconWrapper(ClipBoldIcon)
    case 'clipSmall':
      return iconWrapper(ClipSmallIcon)
    case 'key':
      return iconWrapper(KeyIcon)
    case 'filter':
      return iconWrapper(FilterIcon)
    case 'download':
      return iconWrapper(DownloadIcon)
    default:
      return iconWrapper(PlusIcon) // fallback
  }
}

export const Button: FC<ButtonProps.Props> = forwardRef((props, ref) => {
  const {
    children,
    className,
    color = 'green',
    fullWidth,
    loading,
    size = 'big',
    disabled,
    rightIcon,
    leftIcon,
    isLink = false,
    withoutBackground = false,
    view,
    leftIconColor,
    'data-testid': dataTestid = 'Button',
    ...otherProps
  } = props

  return (
    <button
      data-testid={dataTestid}
      className={cx(
        'button',
        `color-${color}`,
        `size-${size}`,
        view && `view-${view}`,
        {
          fullWidth,
          withoutBackground,
          loading,
          disabled,
          isLink,
          icon: rightIcon,
        },
        className,
      )}
      disabled={disabled || loading}
      ref={ref}
      {...otherProps}
    >
      {leftIcon && !loading && renderIcon(leftIcon, leftIconColor, `${dataTestid}.LeftIcon`)}
      {children}
      {loading && <span data-testid={`${dataTestid}.Loader`} />}
      {rightIcon && !loading && renderIcon(rightIcon, undefined, `${dataTestid}.RightIcon`)}
    </button>
  )
})
