import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import { Button } from './button'
import { renderTestScreen } from '@/shared/helpers/tests/render'


describe('[shared] [ui] Button', () => {
    it('Должен отрендериться с нужным текстом', () => {
        const { getByTestId } = renderTestScreen(
            <Button>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).toBeInTheDocument()
        expect(button).toHaveTextContent('Нажми меня, пажаласта')
    })

    describe('Classname должен быть', () => {
        it('зеленый по дефолту', () => {
            const { getByTestId } = renderTestScreen(
                <Button>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('color-green')
        })

        it('зеленый если передан color = green', () => {
            const { getByTestId } = renderTestScreen(
                <Button color='green'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('color-green')
        })

        it('серый если передан color = gray', () => {
            const { getByTestId } = renderTestScreen(
                <Button color='gray'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('color-gray')
        })

        it('красный если передан color = red', () => {
            const { getByTestId } = renderTestScreen(
                <Button color='red'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('color-red')
        })

        it('темно-серый если передан color = darkGray', () => {
            const { getByTestId } = renderTestScreen(
                <Button color='darkGray'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('color-darkGray')
        })

        it('прозрачный с зеленой рамкой color = border', () => {
            const { getByTestId } = renderTestScreen(
                <Button color='border'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('color-border')
        })

    })

    it('Должен занимать всю ширину при fullWidth = true', () => {
        const { getByTestId } = renderTestScreen(
            <Button fullWidth>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).toHaveClass('fullWidth')
    })

    it('Не должен занимать всю ширину при fullWidth = false', () => {
        const { getByTestId } = renderTestScreen(
            <Button fullWidth={false}>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).not.toHaveClass('fullWidth')
    })

    it('Должен отображать лоадер при loading = true', () => {
        const { getByTestId } = renderTestScreen(
            <Button loading>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')
        const loader = getByTestId('Button.Loader')

        expect(loader).toBeInTheDocument()
        expect(button).toHaveAttribute('disabled')
    })

    it('Не должен отображать лоадер при loading = false', () => {
        const { queryByTestId, getByTestId } = renderTestScreen(
            <Button loading={false}>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')
        const loader = queryByTestId('Button.Loader')

        expect(loader).not.toBeInTheDocument()
        expect(button).not.toHaveAttribute('disabled')
    })

    describe('Size должен быть', () => {
        it('big по дефолту', () => {
            const { getByTestId } = renderTestScreen(
                <Button>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('size-big')
        })

        it('small если передан color = small', () => {
            const { getByTestId } = renderTestScreen(
                <Button size='small'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('size-small')
        })

        it('medium если передан color = medium', () => {
            const { getByTestId } = renderTestScreen(
                <Button size='medium'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('size-medium')
        })

        it('big если передан color = big', () => {
            const { getByTestId } = renderTestScreen(
                <Button size='big'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('size-big')
        })

        it('veryBig с зеленой рамкой color = veryBig', () => {
            const { getByTestId } = renderTestScreen(
                <Button size='veryBig'>Нажми меня, пажаласта</Button>
            )

            const button = getByTestId('Button')

            expect(button).toHaveClass('size-veryBig')
        })

    })

    it('Должен быть задизейблен при disabled = true', () => {
        const { getByTestId } = renderTestScreen(
            <Button disabled>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).toHaveAttribute('disabled')
    })

    it('Не должен быть задизейблен при disabled = false', () => {
        const { getByTestId } = renderTestScreen(
            <Button disabled={false}>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).not.toHaveAttribute('disabled')
    })

    it('Должен отрендерить иконку, если она передается в rightIcon', () => {
        const { getByTestId } = renderTestScreen(
            <Button rightIcon='plus'>Нажми меня, пажаласта</Button>
        )

        const icon = getByTestId('Button.RightIcon')

        expect(icon).toBeInTheDocument()
    })

    it('Должен отрендерить иконку, если она передается в leftIcon', () => {
        const { getByTestId } = renderTestScreen(
            <Button leftIcon='plus'>Нажми меня, пажаласта</Button>
        )

        const icon = getByTestId('Button.LeftIcon')

        expect(icon).toBeInTheDocument()
    })

    it('Должен отрендерить две иконки, если они переданы в leftIcon и rightIcon', () => {
        const { getByTestId } = renderTestScreen(
            <Button leftIcon='plus' rightIcon='plus'>Нажми меня, пажаласта</Button>
        )

        const leftIcon = getByTestId('Button.LeftIcon')
        const rightIcon = getByTestId('Button.RightIcon')

        expect(leftIcon).toBeInTheDocument()
        expect(rightIcon).toBeInTheDocument()
    })

    it('Должен быть ссылкой, если передан isLink = true', () => {
        const { getByTestId } = renderTestScreen(
            <Button isLink>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).toHaveClass('isLink')
    })

    it('Не должен быть ссылкой, если передан isLink = false', () => {
        const { getByTestId } = renderTestScreen(
            <Button isLink={false}>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).not.toHaveClass('isLink')
    })

    it('Не должен быть ссылкой по дефолту', () => {
        const { getByTestId } = renderTestScreen(
            <Button>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).not.toHaveClass('isLink')
    })

    it('Должен быть прозрачным, если передан isLink = true', () => {
        const { getByTestId } = renderTestScreen(
            <Button withoutBackground>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).toHaveClass('withoutBackground')
    })

    it('Не должен быть прозрачным, если передан withoutBackground = false', () => {
        const { getByTestId } = renderTestScreen(
            <Button withoutBackground={false}>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).not.toHaveClass('withoutBackground')
    })

    it('Не должен быть прозрачным по дефолту', () => {
        const { getByTestId } = renderTestScreen(
            <Button>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).not.toHaveClass('withoutBackground')
    })

    it('Должен быть без рамки по дефолту', () => {
        const { getByTestId } = renderTestScreen(
            <Button>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).not.toHaveClass('view')
    })

    it('Должен быть с рамкой по дефолту', () => {
        const { getByTestId } = renderTestScreen(
            <Button>Нажми меня, пажаласта</Button>
        )

        const button = getByTestId('Button')

        expect(button).not.toHaveClass('view')
    })

    it('Должен быть с рамкой по дефолту', async () => {
        const mockedOnClick = jest.fn()

        const { getByTestId } = renderTestScreen(
            <Button
                onClick={mockedOnClick}
            >
                Нажми меня, пажаласта
            </Button>
        )

        const button = getByTestId('Button')

        await userEvent.click(button)

        expect(mockedOnClick).toHaveBeenCalled()
    })

})
