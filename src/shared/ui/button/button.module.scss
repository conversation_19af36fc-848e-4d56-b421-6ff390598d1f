.button {
  --button-color: var(--color-primary);
  --button-hover-color: var(--color-primary-80);
  --button-focus-color: var(--color-primary-80);
  --button-loading-color: var(--color-primary-90);
  --button-disabled-color: #e1e4eb;

  --button-text-color: #fff;
  --button-hover-text-color: #fff;
  --button-focus-text-color: #fff;
  --button-disabled-text-color: #c9cedc;

  --button-loading-color-start: rgba(255, 255, 255, 1);
  --button-loading-color-end: rgba(255, 255, 255, 0.2);
  align-items: center;

  appearance: none;

  background: var(--button-color);
  border: 2px solid var(--button-color);

  color: #fff;
  color: var(--button-text-color);
  display: flex;

  font-family: "TT Norms Pro";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  gap: 4px;
  justify-content: center;
  line-height: 18px;
  outline: none;

  transition: var(--transition);

  &.icon {
    justify-content: space-between;
  }

  &.color {
    &-gray {
      // --button-color: #f0f3f7;
      --button-color: transparent;
      --button-hover-color: #e1e4eb;
      --button-focus-color: #e1e4eb;
      --button-loading-color: #e1e4eb;
      // --button-disabled-color: #f0f3f7;
      --button-disabled-color: transparent;

      --button-text-color: #8e97af;
      --button-hover-text-color: #5c6585;
      --button-focus-text-color: #5c6585;
      --button-disabled-text-color: #c9cedc;

      --button-loading-color-start: rgba(142, 151, 175, 1);
      --button-loading-color-end: rgba(142, 151, 175, 0.2);
    }
    &-red {
      --button-color: #f1465a;
      --button-hover-color: #ff5f71;
      --button-focus-color: #ff5f71;
      --button-loading-color: #ff5f71;
      --button-disabled-color: #e1e4eb;

      --button-text-color: #fff;
      --button-hover-text-color: #fff;
      --button-focus-text-color: #fff;
      --button-disabled-text-color: #c9cedc;

      // ???
      color: #fff !important;

      --button-loading-color-start: rgba(255, 255, 255, 1);
      --button-loading-color-end: rgba(255, 255, 255, 0.2);
    }

    &-darkGray {
      --button-color: #e1e4eb;
      --button-hover-color: #c9cedc;
      --button-focus-color: #c9cedc;
      --button-loading-color: #c9cedc;
      --button-disabled-color: #e1e4eb;

      --button-text-color: #5c6585;
      --button-hover-text-color: #5c6585;
      --button-focus-text-color: #5c6585;
      --button-disabled-text-color: #c9cedc;

      --button-loading-color-start: rgba(92, 101, 133, 1);
      --button-loading-color-end: rgba(92, 101, 133, 0.2);
    }
  }

  &.isLink {
    --button-color: transparent;
    --button-hover-color: transparent;
    --button-focus-color: transparent;
    --button-loading-color: transparent;
    --button-disabled-color: transparent;

    --button-text-color: #3dbc87;
    --button-hover-text-color: #68cba2;
    --button-focus-text-color: #68cba2;
    --button-disabled-text-color: #c9cedc;

    --button-loading-color-start: transparent;
    --button-loading-color-end: transparent;
    padding: 0 !important;
  }

  &:hover {
    background: var(--button-hover-color);
    border: 2px solid var(--button-hover-color);
    color: var(--button-hover-text-color);
  }

  &:focus {
    background: var(--button-focus-color);
    border: 2px solid var(--button-focus-color);
    color: var(--button-focus-text-color);
  }

  &.loading {
    background: var(--button-loading-color);
    border: 2px solid var(--button-loading-color);
    color: transparent;
  }
  &.disabled {
    background: var(--button-disabled-color);
    border: 2px solid var(--button-disabled-color);
    color: var(--button-disabled-text-color);
  }

  &.size {
    &-veryBig {
      border-radius: 12px;
      padding: 14px 25px;
    }
    &-big {
      border-radius: 12px;
      padding: 9px 18px;
    }
    &-medium {
      border-radius: 8px;
      padding: 4px 14px;
    }
    &-small {
      border-radius: 8px;

      font: var(--font-caption-1-demibold);
      padding: 2px 10px;
    }
  }

  &.loading {
    color: transparent !important;
    position: relative;
    span {
      animation: linear loading infinite 0.9s 0.3s;

      background: rgba(255, 255, 255, 1);
      border-radius: 50%;

      display: block;
      height: 4px;
      left: 50%;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 4px;
      &::after,
      &::before {
        background: #fff;
        border-radius: 50%;

        content: "";

        display: block;
        height: 4px;
        position: absolute;
        width: 4px;
      }
      &::after {
        animation: linear loading infinite 0.9s 0s;
        left: -8px;
      }
      &::before {
        animation: linear loading infinite 0.9s 0.6s;
        right: -8px;
      }
    }
  }

  &.withoutBackground {
    background: transparent !important;
  }

  &.fullWidth {
    width: 100%;
  }

  @keyframes loading {
    from {
      background: var(--button-loading-color-start);
    }
    to {
      background: var(--button-loading-color-end);
    }
  }

  &.view {
    &-border {
      background-color: var(--white) !important;
      border: 1px solid var(--button-color) !important;
      color: var(--button-color) !important;

      &:hover {
        color: var(--button-color);
      }
      &:focus {
        color: var(--button-color);
      }
    }
  }
}
