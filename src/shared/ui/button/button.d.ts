// TEMPTODO - надо будет добавить типы иконок после того, как будут иконки только
// import { Icons } from 'react-app/components/UI'

import { TIconColor } from "../Icon";
import { TIcons } from "../Icon/icons/icons.d";

export declare namespace ButtonProps {
  interface Own
    extends React.DetailedHTMLProps<
      React.ButtonHTMLAttributes<HTMLButtonElement>,
      HTMLButtonElement
    > {
    children: React.ReactNode;
    className?: string;
    color?: "green" | "gray" | "red" | "darkGray" | "border";
    leftIconColor?: TIconColor;
    view?: "border";
    fullWidth?: boolean;
    loading?: boolean;
    // TODO
    // veryBig это как я понял временная мера, чтобы был типа старый дизайн на новых компонентах
    size?: "small" | "medium" | "big" | "veryBig";
    disabled?: boolean;

    rightIcon?: TIcons;
    leftIcon?: TIcons;

    icon?: string;
    iconPosition?: "left" | "right";
    isLink?: boolean;
    link?: string;
    withoutBackground?: boolean;
  }

  type Props = Own & DataTest
}

export {};
