.tabs {
  display: flex;
  flex-direction: row;
  gap: 16px;

  &__item {
    align-items: center;

    color: var(--color-gray-70);
    display: flex;

    font: var(--font-title-4-medium);
    gap: 8px;

    svg,
    path {
      fill: var(--color-gray-70);
      stroke: var(--color-gray-70);
    }

    .badge {
      background: var(--color-gray-60, #c9cedc);
      border-radius: 20px;

      color: var(--color-surface, #fff);

      display: block;
      font: var(--font-text-2-medium);
      padding: 2px 8px;

      transition: var(--transition);
    }

    &.active {
      color: var(--color-gray-90);

      svg,
      path {
        fill: var(--color-gray-90);
        stroke: var(--color-gray-90);
      }

      .badge {
        background: var(--color-primary-90);

        transition: var(--transition);
      }
    }

    &:hover {
      color: var(--color-gray-90);

      svg,
      path {
        fill: var(--color-gray-90);
        stroke: var(--color-gray-90);
      }
    }
  }
}
