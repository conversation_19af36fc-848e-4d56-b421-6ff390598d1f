import React, { useState } from 'react'
import styles from './tabs-new.module.scss'
import classNamesBind from 'classnames/bind'
import { TabsNewProps } from './tabs-new.d'

const cx = classNamesBind.bind(styles)

export function TabItem<T>({ children }: TabsNewProps.ItemProps<T>) {
  return <>{children}</>
}
/**
 * @description Tabs component with content
 * @template T -> generic value(string by default)
 * @param defaultTab, activeTabClassname, className, onChange, tabClassname, children
 * @returns React component
 */
export function TabsNew<T>(props: TabsNewProps.Props<T>) {
  const {
    defaultTab,
    activeTab,
    activeTabClassname,
    tabsClassname,
    className,
    onChange,
    tabClassname,
    children,
    tabContentClassname = '',
    hiddenType = 'null',
    tabContentItemClassname,
  } = props

  const [internalActiveTab, setInternalActiveTab] = useState<T | undefined>(defaultTab)
  const isControlled = activeTab !== undefined
  const currentActiveTab = isControlled ? activeTab : internalActiveTab

  const handleChangeTab = (tab: T) => {
    if (onChange) {
      onChange(tab)
    }
    if (!isControlled) {
      setInternalActiveTab(tab)
    }
  }

  return (
    <div className={cx(`container`, className)}>
      <div className={cx('tabs', tabsClassname)}>
        {React.Children.map(children, child => {
          if (!React.isValidElement(child)) return null

          const { value, label, count } = child?.props as TabsNewProps.ItemProps<T>
          const isActive = value === currentActiveTab

          return (
            <button
              type='button'
              key={String(label)}
              onClick={() => handleChangeTab(value)}
              className={`${cx(
                'tabs__item',
                isActive && 'active',
              )} ${tabClassname} ${isActive && activeTabClassname}`}
            >
              {label}
              {count && <span className={cx('badge')}>{count}</span>}
            </button>
          )
        })}
      </div>
      <div className={`${tabContentClassname}`}>
        {React.Children.map(children, child => {
          if (!React.isValidElement(child)) return null

          const { value } = child.props as TabsNewProps.ItemProps<T>

          if (hiddenType === 'display') {
            return (
              <div
                className={tabContentItemClassname}
                style={{ display: value === currentActiveTab ? 'block' : 'none' }}
              >
                {child}
              </div>
            )
          }

          return value === currentActiveTab ? (
            <div className={tabContentItemClassname}>{child}</div>
          ) : null
        })}
      </div>
    </div>
  )
}

/*
! Пример использования в вашем компоненте
    const TABS_CONFIG = [
        {
        label: 'Tab 1',
        value: 1,
        content: <YourComponent1/>
        },
        {
        label: 'Tab 2',
        value: 2,
        content: <YourComponent2/>
        },
    ]

    ! Базовый таб 
    const DEFAULT_TAB = TABS_CONFIG[0]

  return (
    <TabsNew defaultTab={DEFAULT_TAB} onChange={handleTabChange} className="custom-tabs">
        {TABS_CONFIG.map((tab) => 
            <TabItem 
                label={tab.label} 
                value={tab.value}>
            {tab.content}
            </TabItem>)
        }
    </TabsNew>
  );

*/
