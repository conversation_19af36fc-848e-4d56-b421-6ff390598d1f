export declare namespace TabsNewProps {
  interface TabsProps<T> {
    defaultTab?: T;
    activeTab?: T;
    children: React.ReactNode;
    onChange?: (value: T) => void;
  }

  interface ItemProps<Value> {
    label: React.ReactNode;
    value: Value;
    count?: number;
    children: React.ReactNode;
  }

  interface Own<T> extends TabsProps<T> {
    className?: string
    activeTabClassname?: string
    tabClassname?: string
    tabsClassname?: string
    tabContentClassname?: string
    tabContentItemClassname?: string
    hiddenType?: 'null' | 'display'
  }

  type Props<Value = string> = Own<Value>;
}

export {};
