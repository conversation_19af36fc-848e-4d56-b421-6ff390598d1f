.wrapper {

  background: transparent;
  border: 2px solid #c9cedc;
  border-radius: 50%;
  cursor: pointer;
  height: 20px;

  transition: var(--transition);
  width: 20px;
  &:hover {
    border: 2px solid #8e97af;

    transition: var(--transition);
  }
  &.checked {
    background: var(--color-surface, #fff);
    border: 6px solid var(--color-primary);

    transition: var(--transition);
    &:hover {
      background: var(--color-primary-90);
      border: 2px solid var(--color-primary-90);

      transition: var(--transition);
    }
  }

  &.color-- {
    // &primary {

    // }
  }

  .icon {
    margin: -2px;
  }
}
