import React, { FC, useState } from 'react'
import styles from './radiobox.module.scss'
import classNamesBind from 'classnames/bind'
import { RadioboxProps } from './radiobox.d'

const cx = classNamesBind.bind(styles)

export const Radiobox: FC<RadioboxProps.Props> = props => {
  const {
    className,
    color = 'primary',
    onClick,
    checked,
    customChecked = null,
    customMinus = false,
  } = props

  const [isChecked, setIdChecked] = useState(checked || false)

  const handleChecked = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, isChecked: boolean) => {
    setIdChecked(() => !isChecked)

    onClick && onClick(!isChecked)
    e.stopPropagation()
  }

  const isCheckedWithCustom = (customChecked !== null ? customChecked : isChecked) || customMinus

  return (
    <div
      className={cx('wrapper', className, `color--${color}`, {
        checked: isCheckedWithCustom,
      })}
      onClick={e => handleChecked(e, isChecked)}
    ></div>
  )
}
