// TEMPTODO - надо будет добавить типы иконок после того, как будут иконки только
// import { Icons } from 'react-app/components/UI'

export declare namespace RadioboxProps {
  interface Own {
    className?: string;
    color?: "primary";
    type?: "circle" | "square";
    onClick?: (checked: boolean) => void;
    checked?: boolean;
    customChecked?: boolean;
    customMinus?: boolean;

    // color?: 'green' | 'gray' | 'red' | 'darkGray'
    // fullWidth?: boolean
    // loading?: boolean
    // size?: 'small' | 'medium' | 'big'
    // disabled?: boolean

    // icon?: string
    // iconPosition?: 'left' | 'right'
    // isLink?: boolean
    // link?: string
    // withoutBackground?: boolean
  }

  type Props = Own;
}

export {};
