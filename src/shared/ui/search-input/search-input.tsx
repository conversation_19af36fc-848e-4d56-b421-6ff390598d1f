import { FC, memo } from 'react'
import styles from './search-input.module.scss'
import classNamesBind from 'classnames/bind'
import { SearchInputProps } from './search-input.d'
import { Input } from '@/shared/ui'
import SearchIcon from '../Icon/icons/components/SearchIcon'
import LoadIcon from '../Icon/icons/components/LoadIcon'
import { IconWrapper } from '../Icon/IconWrapper'

const cx = classNamesBind.bind(styles)

export const SearchInput: FC<SearchInputProps.Props> = memo(props => {
  const { className, classNameWrapper, isLoading, ...otherProps } = props

  return (
    <div className={cx('wrapper', classNameWrapper)}>
      <Input className={cx('input', className)} {...otherProps} />
      <IconWrapper color='gray80' className={cx('icon')}>
        {isLoading ? <LoadIcon /> : <SearchIcon />}
      </IconWrapper>
    </div>
  )
})

SearchInput.displayName = 'SearchInput'
