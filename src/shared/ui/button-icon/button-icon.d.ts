import { IIconDirection, IIconSize } from '../Icon'
import { TIcons } from '../Icon/icons/icons.d'
import { DraggableAttributes, SyntheticListenerMap } from '@dnd-kit/core'

export const buttonIconColor = ['gray', 'red', 'green', 'gray70', 'primary'] as const
type IButtonIconColor = (typeof buttonIconColor)[number]

export const buttonIconSize = ['16', '24', '28', '32', '36', '44'] as const
type IButtonIconSize = (typeof buttonIconSize)[number]

export declare namespace ButtonIconProps {
  interface Own
    extends React.DetailedHTMLProps<
      React.ButtonHTMLAttributes<HTMLButtonElement>,
      HTMLButtonElement
    > {
    className?: string
    classNameForIcon?: string
    icon?: TIcons
    hasBackground?: boolean
    hasShadow?: boolean
    disabled?: boolean
    rounded?: boolean
    color?: IButtonIconColor
    size?: IButtonIconSize
    iconSize?: IIconSize
    direction?: IIconDirection
    dndListeners?: SyntheticListenerMap
    dndAttributes?: DraggableAttributes
  }

  type Props = Own & DataTest
}

export {}
