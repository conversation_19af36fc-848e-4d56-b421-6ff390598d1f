import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'

import { ButtonIcon } from './button-icon'
import { icons } from '../Icon/icons/icons.d'
import { direction, size } from '../Icon'
import { buttonIconColor, buttonIconSize } from './button-icon.d'

const meta = {
  title: 'UI/ButtonIcon',
  component: ButtonIcon,
  tags: ['autodocs'],
  argTypes: {
    classNameForIcon: {
      description: 'Classname для элемента',
      table: {
        type: { summary: 'String' },
        readonly: true
      },
      control: 'text',
    },
    icon: {
      options: icons,
      description: 'Название иконки',
      control: 'select',
      table: {
        type: { summary: 'TIcons' },
        defaultValue: { summary: 'editBold' },
      }
    },
    color: {
      options: buttonIconColor,
      description: 'Цвет иконки',
      control: 'select',
      table: {
        type: { summary: 'IButtonIconColor' },
        defaultValue: { summary: 'gray' },
      }
    },
    hasBackground: {
      description: 'Имеет ли кнопка фон',
      control: 'boolean',
      table: {
        type: { summary: 'false | true' },
        defaultValue: { summary: 'true' },
      }
    },
    disabled: {
      description: 'Дизейбл иконки',
      control: 'boolean',
      table: {
        type: { summary: 'false | true' },
        defaultValue: { summary: 'true' },
      }
    },
    onClick: {
      control: 'object',
      description: 'Функция, которая вызывается при клике на кнопку',
      table: {
        type: { summary: 'MouseEventHandler<HTMLButtonElement>' },
        readonly: true,
      }
    },
    size: {
      options: buttonIconSize,
      description: 'Размер кнопку',
      control: 'select',
      table: {
        type: { summary: 'IButtonIconSize' },
        defaultValue: { summary: '28' },
      }
    },
    iconSize: {
      options: size,
      description: 'Размер иконки',
      control: 'select',
      table: {
        type: { summary: 'IIconSize' },
        defaultValue: { summary: '20' },
      }
    },
    direction: {
      options: direction,
      description: 'Направление иконки',
      control: 'select',
      table: {
        type: { summary: 'IIconDirection' },
        defaultValue: { summary: 'up' },
      }
    },
    rounded: {
      description: 'Круглая ли кнопку',
      control: 'boolean',
      table: {
        type: { summary: 'false | true' },
        defaultValue: { summary: 'false' },
      }
    },
    hasShadow: {
      description: 'Имеется ли тень у кнопки',
      control: 'boolean',
      table: {
        type: { summary: 'false | true' },
        defaultValue: { summary: 'false' },
      }
    },
    type: {
      description: 'Тип кнопки (для работы с формами)',
      control: 'text',
      table: {
        type: { summary: '"submit" | "reset" | "button" | undefined' },
        defaultValue: { summary: 'undefined' },
        readonly: true
      }
    },
  }
} satisfies Meta<typeof ButtonIcon>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const Colors: Story = {
  decorators: () => {
    return <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '10px',
      alignItems: 'start'
    }}>
      {buttonIconColor.map(buttonIconColor => <ButtonIcon color={buttonIconColor} />)}
    </div>
  }
}

// Как будто это поле не работает
export const HasBackgrounds: Story = {
  decorators: () => {
    return <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '10px',
      alignItems: 'start'
    }}>
      {buttonIconColor.map(buttonIconColor => <ButtonIcon color={buttonIconColor} />)}
      {buttonIconColor.map(buttonIconColor => <ButtonIcon hasBackground color={buttonIconColor} />)}
    </div>
  }
}

export const Disable: Story = {
  decorators: () => {
    return <div style={{
      display: 'flex',
      flexDirection: 'column',
      gap: '10px',
      alignItems: 'start'
    }}>
      {buttonIconColor.map(buttonIconColor => <ButtonIcon color={buttonIconColor} />)}
      {buttonIconColor.map(buttonIconColor => <ButtonIcon disabled color={buttonIconColor} />)}
    </div>
  }
}

export const Sizes: Story = {
  decorators: () => {
    return <div style={{
      display: 'flex',
      gap: '20px',
      alignItems: 'start',
      flexWrap: 'wrap'
    }}>
      {buttonIconSize.map(buttonIconSize =>
        size.map(size => <ButtonIcon size={buttonIconSize} iconSize={size} color='primary' />)
      )}
    </div>
  }
}

export const Directions: Story = {
  decorators: () => {
    return <div style={{
      display: 'flex',
      gap: '10px',
      alignItems: 'start'
    }}>

      {buttonIconColor.map(color =>
        direction.map(direction => <ButtonIcon color={color} icon='arrow' direction={direction} />)
      )}
    </div>
  }
}

export const Rounded: Story = {
  decorators: () => {
    return <div style={{
      display: 'flex',
      gap: '10px',
      alignItems: 'start'
    }}>
      {buttonIconColor.map(color => <ButtonIcon rounded color={color} />)}
    </div>
  }
}

export const HasShadow: Story = {
  decorators: () => {
    return <div style={{
      display: 'flex',
      gap: '10px',
      alignItems: 'start'
    }}>
      {buttonIconColor.map(color => <ButtonIcon hasShadow color={color} />)}
    </div>
  }
}