.wrapper {
  align-items: center;

  background: transparent;
  border: none;

  border-radius: 8px;
  cursor: pointer;

  display: flex;
  justify-content: center;

  outline: none;

  transition: var(--transition);

  &.rounded {
    border-radius: 100%;
  }

  &.hasBackground {
    &.color {
      &--red {
        &:hover {
          background: rgba(241, 70, 90, 0.15);

          transition: var(--transition);
        }
      }
      &--gray,
      &--gray70 {
        &:hover {
          background: #e1e4eb;

          transition: var(--transition);
        }
      }
      &--green {
        &:hover {
          background: rgba(83, 195, 148, 0.15);

          transition: var(--transition);
        }
      }
      &--primary {
        background-color: var(--white);
        &:hover {
          background-color: var(--white);

          transition: var(--transition);
        }
      }
    }
  }
  &.hasShadow {
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.247);
  }

  &.size {
    &--16 {
      height: 16px;
      width: 16px;
    }
    &--24 {
      height: 24px;
      width: 24px;
    }
    &--28 {
      height: 28px;
      width: 28px;
    }
    &--32 {
      height: 32px;
      width: 32px;
      &:active {
        height: 30px;

        transform: scale(0.9375);
        width: 30px;
      }
    }
    &--36 {
      height: 36px;
      width: 36px;
      &:active {
        height: 35px;

        transform: scale(0.9375);
        width: 35px;
      }
    }
    &--44 {
      height: 44px;
      width: 44px;
      &:active {
        height: 43px;

        transform: scale(0.9375);

        width: 43px;
      }
    }
  }

  &.disabled {
    cursor: default;
    opacity: 0.5;

    pointer-events: none;
  }
}
