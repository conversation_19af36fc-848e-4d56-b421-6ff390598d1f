import { FC } from 'react'
import styles from './button-icon.module.scss'
import classNamesBind from 'classnames/bind'
import { ButtonIconProps } from './button-icon.d'
import { TIconColor, IIconSize, IIconDirection } from '../Icon'
import EditBoldIcon from '../Icon/icons/components/EditBoldIcon'
import ChevroneBoldIcon from '../Icon/icons/components/ChevroneBoldIcon'
import ChevroneMediumIcon from '../Icon/icons/components/ChevroneMediumIcon'
import CopyIcon from '../Icon/icons/components/CopyIcon'
import ArrowIcon from '../Icon/icons/components/ArrowIcon'
import SettingsBoldIcon from '../Icon/icons/components/SettingsBoldIcon'
import TrashBoldIcon from '../Icon/icons/components/TrashBoldIcon'
import CloseBoldIcon from '../Icon/icons/components/CloseBoldIcon'
import { IconWrapper } from '../Icon/IconWrapper'
import {
  EyeIcon,
  StatisticsIcon,
  StopCircleIcon,
  VerticalDotted,
  DragDotted,
  LoadIcon,
} from '../Icon/icons/components'

const cx = classNamesBind.bind(styles)

// Функция для рендеринга иконок
const renderIcon = (
  iconName: string,
  size: string,
  color: TIconColor,
  direction: string,
  className?: string,
  testId?: string,
) => {
  const iconWrapper = (IconComponent: FC<{ className?: string }>) => (
    <IconWrapper
      size={size as IIconSize}
      color={color}
      direction={direction as IIconDirection}
      className={className}
      data-testid={testId}
    >
      <IconComponent />
    </IconWrapper>
  )

  switch (iconName) {
    case 'editBold':
      return iconWrapper(EditBoldIcon)
    case 'chevroneBold':
      return iconWrapper(ChevroneBoldIcon)
    case 'chevroneMedium':
      return iconWrapper(ChevroneMediumIcon)
    case 'copy':
      return iconWrapper(CopyIcon)
    case 'arrow':
      return iconWrapper(ArrowIcon)
    case 'settingsBold':
      return iconWrapper(SettingsBoldIcon)
    case 'trashBold':
      return iconWrapper(TrashBoldIcon)
    case 'closeBold':
      return iconWrapper(CloseBoldIcon)
    case 'stopCircle':
      return iconWrapper(StopCircleIcon)
    case 'eye':
      return iconWrapper(EyeIcon)
    case 'statistics':
      return iconWrapper(StatisticsIcon)
    case 'verticalDotted':
      return iconWrapper(VerticalDotted)
    case 'dragDotted':
      return iconWrapper(DragDotted)
    case 'load':
      return iconWrapper(LoadIcon)
    default:
      return iconWrapper(EditBoldIcon) // fallback
  }
}

export const ButtonIcon: FC<ButtonIconProps.Props> = props => {
  const {
    className,
    classNameForIcon,
    icon = 'editBold',
    color = 'gray',
    hasBackground = true,
    disabled = false,
    onClick,
    size = '28',
    iconSize = '20',
    direction = 'up',
    rounded,
    hasShadow = false,
    dndListeners,
    dndAttributes,
    type,
    'data-testid': dataTestid = 'ButtonIcon',
    title,
  } = props

  const iconColor: {
    [key: string]: TIconColor
  } = {
    gray70: 'gray70',
    gray: 'gray80',
    green: 'primary',
    primary: 'primary',
    red: 'red',
  }

  return (
    <button
      type={type}
      className={cx(
        'wrapper',
        `color--${color}`,
        `size--${size}`,
        rounded && 'rounded',
        hasShadow && 'hasShadow',
        className,
        {
          disabled,
          hasBackground,
        },
      )}
      onClick={onClick}
      disabled={disabled}
      {...dndListeners}
      {...dndAttributes}
      data-testid={dataTestid}
      title={title}
    >
      {renderIcon(
        icon,
        iconSize,
        iconColor[color],
        direction,
        classNameForIcon,
        // eslint-disable-next-line i18next/no-literal-string
        `${dataTestid}.Icon`,
      )}
    </button>
  )
}
