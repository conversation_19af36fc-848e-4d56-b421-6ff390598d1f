import { ButtonIcon } from './button-icon'
import { renderTestScreen } from '@/shared/helpers/tests/render'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import { buttonIconColor, buttonIconSize } from './button-icon.d'

describe('[shared] [ui] ButtonIcon', () => {
  it('Кнопка должна отрендериться', async () => {
    const { getByTestId } = renderTestScreen(<ButtonIcon />)

    const button = getByTestId('ButtonIcon')

    expect(button).toBeInTheDocument()
  })

  it('Должен обрабатывать onClick событие', async () => {
    const mockedOnClick = jest.fn()

    const { getByTestId } = renderTestScreen(<ButtonIcon onClick={mockedOnClick} />)

    const button = getByTestId('ButtonIcon')

    await userEvent.click(button)

    expect(button).toBeInTheDocument()
    expect(mockedOnClick).toHaveBeenCalledTimes(1)
  })

  it('Не должен обрабатывать onClick событие при disabled = true', async () => {
    const mockedOnClick = jest.fn()

    const { getByTestId } = renderTestScreen(<ButtonIcon onClick={mockedOnClick} disabled />)

    const button = getByTestId('ButtonIcon')

    await userEvent.click(button)

    expect(button).toBeInTheDocument()
    expect(mockedOnClick).toHaveBeenCalledTimes(0)
  })

  for (let i = 0; i < buttonIconColor.length; i++) {
    it(`Должен отрендерить карточку с нужным классом = color--${buttonIconColor[i]}`, () => {
      const { getByTestId } = renderTestScreen(<ButtonIcon color={buttonIconColor[i]} />)

      const button = getByTestId('ButtonIcon')

      expect(button).toBeInTheDocument()
      expect(button).toHaveClass(`color--${buttonIconColor[i]}`)
    })
  }

  for (let i = 0; i < buttonIconSize.length; i++) {
    it(`Должен отрендерить карточку с нужным классом = size--${buttonIconSize[i]}`, () => {
      const { getByTestId } = renderTestScreen(<ButtonIcon size={buttonIconSize[i]} />)

      const button = getByTestId('ButtonIcon')

      expect(button).toBeInTheDocument()
      expect(button).toHaveClass(`size--${buttonIconSize[i]}`)
    })
  }
})
