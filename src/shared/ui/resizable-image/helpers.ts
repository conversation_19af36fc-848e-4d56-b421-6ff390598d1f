import { ResizerSide } from './resizable-image.d'

export function computeFinalDimensions(
  naturalWidth: number,
  naturalHeight: number,
  width?: number | null,
  height?: number | null,
  minWidth?: number,
  minHeight?: number,
): { finalWidth: number; finalHeight: number } {
  const aspectRatio = naturalWidth / naturalHeight

  // 1. Смотрим что пришло извне
  // Если ничего не передали — берём натуральные размеры
  let finalWidth = width ?? naturalWidth
  let finalHeight = height ?? naturalHeight

  // 2. Подгоняем пропорции в зависимости от того, какие параметры заданы
  if (width != null && height != null) {
    // Оба заданы — смотрим, не нарушают ли сильно соотношение сторон
    const ratioFromProps = width / height
    if (Math.abs(ratioFromProps - aspectRatio) > 0.01) {
      // Приоритет width, пересчитываем height
      finalHeight = finalWidth / aspectRatio
    }
  } else if (width != null) {
    // Задан только width, пересчитываем height
    finalHeight = finalWidth / aspectRatio
  } else if (height != null) {
    // Задан только height, пересчитываем width
    finalWidth = finalHeight * aspectRatio
  }

  // 3. Учитываем минимальные размеры, если заданы
  // Если итоговая ширина меньше minWidth, фиксируем её и пересчитываем высоту
  if (minWidth && finalWidth < minWidth) {
    finalWidth = minWidth
    finalHeight = finalWidth / aspectRatio
  }
  // Аналогично для высоты
  if (minHeight && finalHeight < minHeight) {
    finalHeight = minHeight
    finalWidth = finalHeight * aspectRatio
  }

  return { finalWidth, finalHeight }
}

export function calculateNewDimensions(
  currentResizer: ResizerSide | null,
  startWidth: number,
  startX: number,
  currentMouseX: number,
  aspectRatio: number,
  minWidth: number,
  minHeight: number,
  maxWidth: number,
): { width: number; height: number } {
  let newWidth = startWidth
  const deltaX = currentMouseX - startX

  if (currentResizer === ResizerSide.RIGHT) {
    newWidth = startWidth + deltaX * 2
  } else if (currentResizer === ResizerSide.LEFT) {
    newWidth = startWidth - deltaX * 2
  }

  // Ограничиваем по минимальной и максимальной ширине
  newWidth = Math.max(minWidth, Math.min(newWidth, maxWidth))

  // Считаем высоту по соотношению сторон
  let newHeight = newWidth / aspectRatio

  // Проверяем, не меньше ли высота минимальной
  if (newHeight < minHeight) {
    newHeight = minHeight
    newWidth = minHeight * aspectRatio
  }

  return { width: newWidth, height: newHeight }
}
