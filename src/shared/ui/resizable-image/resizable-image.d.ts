export declare namespace ResizableImageProps {
  interface Size {
    width: number
    height: number
  }

  interface Own {
    src?: string
    alt?: string
    width?: number | null
    height?: number
    minWidth?: number
    minHeight?: number
    active?: boolean
    className?: string
    contentClassName?: string
    fit?: 'cover' | 'contain' | 'scale-down'
    view?: 'rounded' | 'circle' | 'square'
    wide?: boolean

    onSizeChange?: (size: Size) => void
    onClick?: () => void
  }

  type Props = Own
}

enum ResizerSide {
  LEFT = 'left',
  RIGHT = 'right',
}

export { ResizerSide }
