/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useRef, useState, MouseEvent, PropsWithChildren, useEffect } from 'react'
import Skeleton from 'react-loading-skeleton'
import classNamesBind from 'classnames/bind'

import { ResizableImageProps, ResizerSide } from './resizable-image.d'
import styles from './resizable-image.module.scss'
import { calculateNewDimensions, computeFinalDimensions } from './helpers'

const cx = classNamesBind.bind(styles)

const ResizableImage: React.FC<PropsWithChildren<ResizableImageProps.Props>> = ({
  src = '',
  alt = '',
  width,
  height,
  minWidth = 64,
  minHeight = 64,
  onClick,
  onSizeChange,
  contentClassName,
  active = true,
  fit = 'contain',
  view = 'rounded',
  wide = false,
  className,
  children,
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)
  const currentActiveResizer = useRef<ResizerSide | null>(null)
  const startWidthRef = useRef(0) // width of the container when resizing starts
  const startXRef = useRef(0) // X-coordinate of the mouse when resizing starts
  const currentWidthValue = useRef(0)
  const currentHeightValue = useRef(0)

  const [loaded, setLoaded] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [currentWidth, setCurrentWidth] = useState(width ?? 0)

  const handleImageLoad = () => {
    setLoaded(true)
    if (!imageRef.current) return

    const { naturalWidth, naturalHeight } = imageRef.current
    const { finalWidth, finalHeight } = computeFinalDimensions(
      naturalWidth,
      naturalHeight,
      width,
      height,
      minWidth,
      minHeight,
    )

    setCurrentWidth(finalWidth)
    currentWidthValue.current = finalWidth
    currentHeightValue.current = finalHeight

    onSizeChange?.({ width: finalWidth, height: finalHeight })
  }

  const onMouseMove = (e: MouseEvent): void => {
    if (!currentActiveResizer.current || !imageRef.current) return

    const parent = containerRef.current?.parentElement
    const maxAvailableWidth = parent?.clientWidth || 999999
    const aspectRatio = imageRef.current.naturalWidth / imageRef.current.naturalHeight

    const { width: newWidth, height: newHeight } = calculateNewDimensions(
      currentActiveResizer.current,
      startWidthRef.current,
      startXRef.current,
      e.clientX,
      aspectRatio,
      minWidth,
      minHeight,
      maxAvailableWidth,
    )

    setCurrentWidth(newWidth)
    currentWidthValue.current = newWidth
    currentHeightValue.current = newHeight
  }

  function handleMouseDown(e: React.MouseEvent<HTMLDivElement>) {
    e.preventDefault()
    e.stopPropagation()
    const side = e.currentTarget.getAttribute('data-resizer') as ResizerSide
    if (!side || !containerRef.current) return

    currentActiveResizer.current = side
    startWidthRef.current = currentWidth
    startXRef.current = e.clientX

    setIsResizing(true)

    window.addEventListener('mousemove', onMouseMove as any)
    window.addEventListener('mouseup', onMouseUp)
  }

  const onMouseUp = () => {
    onSizeChange?.({ width: currentWidthValue.current, height: currentHeightValue.current })
    setIsResizing(false)
    cleanEventListeners()
  }

  const cleanEventListeners = () => {
    window.removeEventListener('mousemove', onMouseMove as any)
    window.removeEventListener('mouseup', onMouseUp)
  }

  useEffect(() => () => cleanEventListeners(), [])

  const pictureWidth = currentWidth > 0 ? currentWidth : 'auto'

  return (
    <div
      className={cx('wrapper', `view-${view}`, className, { resizing: isResizing })}
      onClick={onClick}
      ref={containerRef}
      style={{
        width: !src && !loaded ? '100%' : pictureWidth,
      }}
    >
      {(active || loaded) && (
        <img
          ref={imageRef}
          src={src}
          alt={alt}
          className={cx('image', `fit-${fit}`, {
            wide,
            loaded,
          })}
          onLoad={handleImageLoad}
          data-object-fit={fit}
        />
      )}

      {(!src || !loaded) && <Skeleton width='100%' height={height}></Skeleton>}

      <div className={cx('content', contentClassName)}>{children}</div>

      {src && loaded && (
        <>
          <div
            className={cx('resizer', 'resizer_left', {
              active: currentActiveResizer.current === ResizerSide.LEFT && isResizing,
            })}
            onMouseDown={handleMouseDown}
            data-resizer={ResizerSide.LEFT}
          >
            <div className={cx('resizer__handle')} />
          </div>
          <div
            className={cx('resizer', 'resizer_right', {
              active: currentActiveResizer.current === ResizerSide.RIGHT && isResizing,
            })}
            onMouseDown={handleMouseDown}
            data-resizer={ResizerSide.RIGHT}
          >
            <div className={cx('resizer__handle')} />
          </div>
        </>
      )}
    </div>
  )
}

export { ResizableImage }
