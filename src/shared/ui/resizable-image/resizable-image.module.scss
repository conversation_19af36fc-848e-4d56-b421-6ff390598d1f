.wrapper {
  width: fit-content;

  position: relative;
  span {
    display: inline-block;
    flex: 1 0;
    height: 100%;
    max-height: 100%;
    width: 100%;
  }
}

.image {
  height: 100%;
  width: 100%;

  @supports (object-fit: cover) {
    display: none;
  }

  &.loaded {
    display: block;
  }
  &.wide {
    width: 100%;
  }
}

.fit {
  &-contain {
    object-fit: contain;
  }
  &-cover {
    object-fit: cover;
  }
  &-scale-down {
    object-fit: scale-down;
  }
}

.view {
  &-rounded {
    border-radius: 10px;
    .image {
      border-radius: 8px;
    }
  }
  &-circle {
    border-radius: 50%;
  }
  &-square {
    border-radius: 0;
  }
}

.content {
  box-sizing: border-box;

  display: flex;
  flex-grow: 1;
  height: 100%;
  left: 0;
  position: absolute;
  top: 0;
  width: 100%;
}

.resizer {
  cursor: col-resize;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 1;

  display: flex;
  align-items: center;
  justify-content: center;

  width: 16px;
  height: 100%;
  padding: 0 6px;

  &_left {
    left: 0;
    padding-right: 0;
  }
  &_right {
    right: 0;
    padding-left: 0;
  }

  &__handle {
    width: 6px;
    height: 80%;
    max-height: 48px;
    border: 1px solid var(--color-gray-70);
    background-color: var(--white);
    border-radius: 6px;
    opacity: 0;
    transition: var(--transition);
    transition-property: opacity, border-color, background-color, box-shadow;

    .wrapper:hover & {
      opacity: 0.6;
    }
  }

  &:hover &__handle,
  .resizing &__handle {
    opacity: 0.8;
  }

  &.active &__handle {
    opacity: 1;
    border-color: var(--color-gray-90);
    box-shadow: var(--shadow-border);
  }
}
