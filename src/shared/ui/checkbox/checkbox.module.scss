.checkboxWrapper {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;
  font: var(--font-text-2-normal);

  &.disabled {
    color: var(--color-gray-80);
    cursor: default;
  }

  .checkboxInput {
    -webkit-appearance: none;
    appearance: none;

    width: 20px;
    height: 20px;
    flex-shrink: 0;

    background: transparent;
    border: 2px solid var(--color-gray-60);
    border-radius: 6px;
    cursor: pointer;

    transition: var(--transition);

    &.disabled {
      background: var(--color-gray-60);
      border: 2px solid var(--color-gray-60);

      &:hover {
        background: var(--color-gray-60);
        border: 2px solid var(--color-gray-60);
        transition: none;
      }
    }

    &:hover {
      border: 2px solid var(--color-gray-70);

      transition: var(--transition);
    }

    &.checked {
      background: var(--color-primary);
      border: 2px solid var(--color-primary);

      transition: var(--transition);

      &:hover {
        background: var(--color-primary-90);
        border: 2px solid var(--color-primary-90);

        transition: var(--transition);
      }

      &.disabled {
        background: var(--color-gray-60);
        border: 2px solid var(--color-gray-60);
        cursor: default;

        &:hover {
          background: var(--color-gray-60);
          border: 2px solid var(--color-gray-60);
          transition: none;
        }
      }
    }

    &.color-- {
      &red {
        &.checked {
          background: var(--stat-bad, #ff8577);
          border: 2px solid var(--stat-bad, #ff8577);

          &.disabled {
            background: var(--color-gray-50);
            border: 2px solid var(--color-gray-50);

            &:hover {
              background: var(--color-gray-50);
              border: 2px solid var(--color-gray-50);
              transition: none;
            }
          }
        }
      }
    }
  }

  .circle {
    position: relative;
    border-radius: 50px;
    width: 20px;
    height: 20px;

    &.checked::before {
      content: '';
      position: absolute;

      width: 11px;
      height: 11px;
      border-radius: 50px;

      background-color: var(--white);
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}

.checkboxWrapper.center {
  align-items: center;
}

.checkboxBase {
  display: flex;
  position: relative;

  .icon {
    position: absolute;
    top: 0;
    left: 0;
  }
}
