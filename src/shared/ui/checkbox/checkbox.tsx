import {
  ChangeEvent,
  FC,
  InputHTMLAttributes,
  ReactElement,
  memo,
  useCallback,
  useState,
} from 'react'
import styles from './checkbox.module.scss'
import classNamesBind from 'classnames/bind'
import MinusIcon from '../Icon/icons/components/MinusIcon'
import SuccessIcon from '../Icon/icons/components/SuccessIcon'
import { IconWrapper } from '../Icon/IconWrapper'

type Props = {
  label?: string | ReactElement
  initialChecked?: boolean
  customChecked?: boolean | null
  customMinus?: boolean
  className?: string
  checkboxClassName?: string
  iconClassName?: string
  color?: 'primary' | 'red'
  type?: 'circle' | 'square'
  variant?: 'top' | 'center'
  onChange?: (value: boolean) => void
}

const cx = classNamesBind.bind(styles)

type HTMLInputProps = Omit<
  InputHTMLAttributes<HTMLInputElement>,
  'value' | 'onChange' | 'className' | 'checked'
>

export const Checkbox: FC<Props & HTMLInputProps> = memo(
  ({
    label,
    initialChecked,
    className,
    checkboxClassName,
    iconClassName,
    customChecked = null,
    customMinus = false,
    color = 'primary',
    type = 'square',
    variant = 'top',
    onChange,
    ...props
  }) => {
    const [isChecked, setIsChecked] = useState(initialChecked || false)

    const handleChange = useCallback(
      (e: ChangeEvent<HTMLInputElement>) => {
        setIsChecked(prev => !prev)
        if (onChange) {
          onChange(customChecked !== null ? !customChecked : !isChecked)
        }
        e.stopPropagation()
      },
      [isChecked, customChecked, onChange],
    )

    const isCheckedWithCustom = (customChecked ?? isChecked) || customMinus

    return (
      <label
        className={cx('checkboxWrapper', className, variant, {
          disabled: props.disabled,
        })}
        onClick={e => e.stopPropagation()}
      >
        <div className={cx('checkboxBase')}>
          <input
            type='checkbox'
            className={cx('checkboxInput', checkboxClassName, type, `color--${color}`, {
              checked: isCheckedWithCustom,
              disabled: props.disabled,
            })}
            checked={isCheckedWithCustom}
            onChange={handleChange}
            {...props}
          />
          {isCheckedWithCustom && type === 'square' && (
            <IconWrapper size='20' color='white' className={cx('icon', iconClassName)}>
              {customMinus ? <MinusIcon /> : <SuccessIcon />}
            </IconWrapper>
          )}
        </div>
        {label}
      </label>
    )
  },
)

Checkbox.displayName = 'Checkbox'
