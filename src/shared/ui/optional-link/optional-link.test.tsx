import '@testing-library/jest-dom'
import { OptionalLink } from './optional-link'
import { renderTestScreen } from '@/shared/helpers/tests/render'
import userEvent from '@testing-library/user-event'

describe('[shared] [ui] OptionalLink', () => {
    it('Должен обрабатывать onClick событие при to=href=undefined', async () => {
        const mockedOnClick = jest.fn()

        const { getByTestId } = renderTestScreen(
            <OptionalLink onClick={mockedOnClick}>Нажми меня</OptionalLink>
        )

        const link = getByTestId('OptionalLink')

        await userEvent.click(link)

        expect(mockedOnClick).toHaveBeenCalledTimes(1)
    })
    it('Должен обрабатывать onClick событие при to!=undefined', async () => {
        const mockedOnClick = jest.fn()

        const { getByTestId } = renderTestScreen(
            <OptionalLink onClick={mockedOnClick} to='/'>Нажми меня</OptionalLink>
        )

        const link = getByTestId('OptionalLink')

        await userEvent.click(link)

        expect(mockedOnClick).toHaveBeenCalledTimes(1)
    })
    it('Должен обрабатывать onClick событие при href!=undefined', async () => {
        const mockedOnClick = jest.fn()

        const { getByTestId } = renderTestScreen(
            <OptionalLink onClick={mockedOnClick} href='/'>Нажми меня</OptionalLink>
        )

        const link = getByTestId('OptionalLink')

        await userEvent.click(link)

        expect(mockedOnClick).toHaveBeenCalledTimes(1)
    })
})
