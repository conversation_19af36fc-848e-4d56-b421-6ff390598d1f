import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { OptionalLink } from './optional-link'

const meta: Meta<typeof OptionalLink> = {
    title: 'UI/OptionalLink',
    component: OptionalLink,
    tags: ['autodocs'],
    argTypes: {
        to: {
            control: 'text',
            description: 'Путь до страницы, куда будет переход после клика',
            table: {
                type: { summary: 'string' },
                readonly: true,
            }
        },
        href: {
            control: 'text',
            description: 'Путь до страницы, куда будет переход после клика',
            table: {
                type: { summary: 'string' },
                readonly: true,
            }
        },
        onClick: {
            control: 'object',
            description: 'Функция, которая вызывается при клике на элемент',
            table: {
                type: { summary: '(e: React.MouseEvent<HTMLElement, MouseEvent>) => void;' },
                readonly: true,
            }
        },
    }
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: 'Текст или ReactNode в качестве children\'a'
    }
}

export const LinkTypes: Story = {
    decorators: () => {
        return <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
            alignItems: 'start'
        }}>
            <OptionalLink to='#'>Текст или ReactNode в качестве children'a</OptionalLink>
            <OptionalLink href='#'>Текст или ReactNode в качестве children'a</OptionalLink>
            <OptionalLink>Текст или ReactNode в качестве children'a</OptionalLink>
        </div>
    }
}