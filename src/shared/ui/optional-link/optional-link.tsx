import React from 'react'
import { <PERSON> } from 'react-router-dom'

import { OptionalLinkProps } from './optional-link.d'

export const OptionalLink: React.FC<OptionalLinkProps.Props> = props => {
  const {
    className,
    to,
    children,
    href,
    onClick,
    'data-testid': dataTestid = 'OptionalLink',
  } = props

  if (href) {
    return (
      <a href={href} className={className} onClick={onClick} data-testid={dataTestid}>
        {children}
      </a>
    )
  }

  if (to) {
    return (
      <Link to={to} className={className} onClick={onClick} data-testid={dataTestid}>
        {children}
      </Link>
    )
  }

  return (
    <div className={className} onClick={onClick} data-testid={dataTestid}>
      {children}
    </div>
  )
}
