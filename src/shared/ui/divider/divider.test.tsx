import { Divider } from './divider'
import '@testing-library/jest-dom'
import { renderTestScreen } from '../../helpers/tests/render'

describe('[shared] [ui] Divider', () => {
  describe('Горизонтальный', () => {
    it('Компонент должен иметь дефолтную высоту', () => {
      const { getByTestId } = renderTestScreen(
        <Divider direction='horizontal' />,
      )

      const divider = getByTestId('Divider')

      expect(divider).toBeInTheDocument()
      expect(divider.style.width).toEqual('100%')
      expect(divider.style.height).toEqual('1px')
    })

    it('Компонент должен принимать ту высоту, что передали', () => {
      const { getByTestId } = renderTestScreen(
        <Divider direction='horizontal' height='10px' />,
      )

      const divider = getByTestId('Divider')

      expect(divider).toBeInTheDocument()
      expect(divider.style.width).toEqual('100%')
      expect(divider.style.height).toEqual('10px')
    })
  })

  describe('Вертикальный', () => {
    it('Компонент должен иметь дефолтную ширину', () => {
      const { getByTestId } = renderTestScreen(
        <Divider direction='vertical' />,
      )

      const divider = getByTestId('Divider')

      expect(divider).toBeInTheDocument()
      expect(divider.style.width).toEqual('1px')
      expect(divider.style.height).toEqual('100%')
    })

    it('Компонент должен принимать ту ширину, что передали', () => {
      const { getByTestId } = renderTestScreen(
        <Divider direction='vertical' width='10px' />,
      )

      const divider = getByTestId('Divider')

      expect(divider).toBeInTheDocument()
      expect(divider.style.width).toEqual('10px')
      expect(divider.style.height).toEqual('100%')
    })
  })
})
