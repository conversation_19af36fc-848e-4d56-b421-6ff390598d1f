import type { Meta, StoryObj } from '@storybook/react'

import { Divider } from './divider'

const meta = {
  title: 'UI/Divider',
  component: Divider,
  argTypes: {
    direction: {
      control: 'select',
      description: 'Направление разделителя. Вертикальное или горизонтальное',
      options: ['vertical', 'horizontal']
    },
    width: {
      control: 'text',
      description: 'Ширина. По умолчанию 1px, для горизонтального 100%',
      table: {
        defaultValue: { summary: '1px' }
      }
    },
    height: {
      control: 'text',
      description: 'Высота. По умолчанию 1px, для вертикального 100%',
      table: {
        defaultValue: { summary: '1px' },
      }
    },
  }
} satisfies Meta<typeof Divider>

export default meta
type Story = StoryObj<typeof meta>

export const Horizontal: Story = {
  args: {
    direction: 'horizontal',
  },
}

export const Vertical: Story = {
  args: {
    direction: 'vertical',
  },
  decorators: [
    Story => (
      <div style={{ height: '500px' }}>
        <Story />
      </div>
    )
  ]
}

export const HorizontalWithHeight10px: Story = {
  args: {
    direction: 'horizontal',
    height: '10px'
  },
}

export const VerticalithWidth10px: Story = {
  args: {
    direction: 'vertical',
    width: '10px'
  },
  decorators: [
    Story => (
      <div style={{ height: '500px' }}>
        <Story />
      </div>
    )
  ]
}