import React, { FC } from "react";
import styles from "./divider.module.scss";
import classNamesBind from "classnames/bind";
import { DividerProps } from "./divider.d";

const cx = classNamesBind.bind(styles);

export const Divider: FC<DividerProps.Props> = (props) => {
  const { className, direction, height = '1px', width = '1px', 'data-testid': dataTestid = 'Divider' } = props;

  const dividerStyle: React.CSSProperties = {
    width: direction === "vertical" ? width : "100%",
    height: direction === "horizontal" ? height : "100%",
  };

  return <div style={dividerStyle} className={cx("wrapper", className)} data-testid={dataTestid}></div>;
};
