.wrapper {
  align-items: center;
  border-radius: 10px;

  border-style: solid;

  border-width: 1px;
  box-sizing: border-box;
  display: flex;

  font-family: var(--font-title), sans-serif;

  font-size: 16px;

  outline: none;
  text-decoration: none;

  transition: 300ms ease;
  &:not(.disabled) {
    cursor: pointer;
    &:hover {
      opacity: 0.9;
    }
    &:active {
      transform: scale(0.98);
    }
  }
}

.disabled {
  pointer-events: none;
}

.padding {
  &-s {
    padding: 10px 25px;
  }
  &-l {
    padding: 15px 25px;
  }
  &-xxxl {
    padding: 25px;
  }
}

.align {
  &-left {
    justify-content: flex-start;

    text-align: left;
  }
  &-center {
    justify-content: center;

    text-align: center;
  }
}

.view {
  &-border {
    &.color {
      &-red {

        background-color: transparent;
        border-color: var(--color-component-warn);
        color: var(--color-component-warn);
        &:hover {

          background-color: transparent;
          border-color: var(--color-component-warn-hover);
          color: var(--color-component-warn-hover);
        }
      }
      &-green {

        background-color: transparent;
        border-color: var(--color-primary);
        color: var(--color-primary);
        &:hover {

          background-color: transparent;
          border-color: var(--color-component-primary-hover);
          color: var(--color-component-primary-hover);
        }
      }
    }
    &.disabled {

      background-color: transparent;
      border-color: var(--gray) !important;
      color: var(--gray) !important;
    }
  }
  &-fill {
    &.color {
      &-red {

        background-color: var(--color-component-warn);
        border-color: var(--color-component-warn);
        color: var(--white);
        &:hover {
          background-color: var(--color-component-warn-hover);
          border-color: var(--color-component-warn-hover);
        }
      }
      &-green {

        background-color: var(--color-primary);
        border-color: var(--color-primary);
        color: var(--white);
        &:hover {
          background-color: var(--color-component-primary-hover);
          border-color: var(--color-component-primary-hover);
        }
      }
      &-gray {

        background-color: var(--gray);
        border-color: var(--gray);
        color: var(--color-primary);
        &:hover {
          background-color: var(--light-grey);
          border-color: var(--light-grey);
        }
      }
    }
    &.disabled {

      background-color: var(--gray) !important;
      border-color: var(--gray) !important;
      color: var(--brown-grey) !important;
    }
  }
}

.width {
  &-wide {
    width: 100%;
  }
  &-m {
    width: 240px; // TODO fix
  }
}
.bold {
  font-weight: bold;
}

.icon {
  margin-right: 10px;
}
