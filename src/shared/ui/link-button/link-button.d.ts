/* eslint-disable @typescript-eslint/no-explicit-any */
import { Icons } from "@/shared/ui";

export declare namespace LinkButtonProps {
  interface Own {
    className?: string;
    text?: string;
    align?: "center" | "left";
    width?: "xs" | "s" | "m" | "l" | "xl" | "xxl" | "xxxl" | "wide";
    padding?: "xs" | "s" | "m" | "l" | "xl" | "xxl" | "xxxl";
    font?: "normal";
    color?: "red" | "green" | "gray" | "light_gray"; // TODO ADD more colors to type
    view?: "border" | "fill";
    icon?: Icons;
    bold?: boolean;
    loading?: boolean;
    disabled?: boolean;
    to: string;
    onClick?(): void;
    children?: any;
  }

  type Props = Own;
}

export {};
