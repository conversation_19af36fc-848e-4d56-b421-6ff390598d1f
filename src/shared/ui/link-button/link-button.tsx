import React, { FC } from 'react'
import styles from './link-button.module.scss'
import classNamesBind from 'classnames/bind'
import { LinkButtonProps } from './link-button.d'

import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import DownloadIcon from '@/shared/ui/Icon/icons/components/DownloadIcon'
import ArrowIcon from '@/shared/ui/Icon/icons/components/ArrowIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { TIconColor } from '@/shared/ui/Icon/icon.d'

const cx = classNamesBind.bind(styles)

// Функция для рендеринга иконок
const renderIcon = (iconName: string, color?: TIconColor, className?: string) => {
  const iconWrapper = (IconComponent: FC<{ className?: string }>) => (
    <IconWrapper color={color} className={className}>
      <IconComponent />
    </IconWrapper>
  )

  switch (iconName) {
    case 'plus':
      return iconWrapper(PlusIcon)
    case 'download':
      return iconWrapper(DownloadIcon)
    case 'arrow':
      return iconWrapper(ArrowIcon)
    default:
      return iconWrapper(PlusIcon) // fallback
  }
}

const _LinkButton: React.FC<LinkButtonProps.Props> = props => {
  const {
    className,
    text,
    disabled,
    children = text,
    align = 'center',
    width = 'wide',
    font = 'normal',
    color = 'green',
    view = 'fill',
    bold = false,
    padding = 'l',
    icon,
    loading,
    onClick,
    to,
  } = props

  const { t } = useTranslation()

  return (
    <Link
      to={to}
      className={cx(
        'wrapper',
        className,
        `align-${align}`,
        `width-${width}`,
        `color-${color}`,
        `view-${view}`,
        `font-${font}`,
        `padding-${padding}`,
        {
          disabled: disabled || loading,
          bold,
        },
      )}
      onClick={onClick}
    >
      {icon && renderIcon(icon, view === 'border' ? 'primary' : 'white', cx('icon'))}
      {loading ? t('commons:loading_with_dots') : children}
    </Link>
  )
}

// Button.defaultProps = {}

export const LinkButton = _LinkButton
