import { useRef, useEffect } from 'react'

interface UseWidthAnimationProps {
  loading?: boolean
  value?: string | number | null
  valueIsEmpty: boolean
  hasActiveCourse?: boolean
}

export const useWidthAnimation = ({
  loading,
  value,
  valueIsEmpty,
  hasActiveCourse,
}: UseWidthAnimationProps) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLSpanElement>(null)

  useEffect(() => {
    if (!loading && containerRef.current && contentRef.current) {
      // Устанавливаем минимальную ширину
      const container = containerRef.current
      container.style.width = '64px'
      container.style.opacity = '1'

      const measureTimeout = setTimeout(() => {
        const originalVisibility = contentRef.current!.style.visibility
        contentRef.current!.style.visibility = 'visible'
        contentRef.current!.style.position = 'absolute'
        contentRef.current!.style.left = '-9999px'

        const contentWidth = contentRef.current!.offsetWidth

        // Показываем контент
        contentRef.current!.style.visibility = originalVisibility
        contentRef.current!.style.position = ''
        contentRef.current!.style.left = ''

        requestAnimationFrame(() => {
          container.style.width = `${Math.max(contentWidth, 64)}px`
        })
      }, 50)

      return () => clearTimeout(measureTimeout)
    } else if (loading && containerRef.current) {
      containerRef.current.style.width = '64px'
    }
  }, [loading, value, valueIsEmpty, hasActiveCourse])

  return {
    containerRef,
    contentRef,
  }
}
