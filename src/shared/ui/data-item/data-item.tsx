import React, { memo } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './data-item.module.scss'

import { DataItemProps } from './data-item.d'
import Skeleton from 'react-loading-skeleton'
import { useTranslation } from 'react-i18next'
import { useWidthAnimation } from './use-width-animation'

const cx = classNamesBind.bind(styles)

export const DataItem: React.FC<DataItemProps.Props> = memo(props => {
  const {
    className,
    hasActiveCourse = true,
    color,
    background,
    title,
    value,
    suffix,
    loading,
  } = props

  const { t } = useTranslation()
  const valueIsEmpty = value === undefined || value === null

  const { containerRef, contentRef } = useWidthAnimation({
    loading,
    value,
    valueIsEmpty,
    hasActiveCourse,
  })

  const renderContent = () => {
    if (loading) {
      return <></>
    }

    if (!hasActiveCourse) {
      return t('old.data_item.not_found')
    }

    if (valueIsEmpty) {
      return t('commons:no_data')
    }

    return (
      <>
        {value}
        {suffix}
      </>
    )
  }

  return (
    <div
      className={cx(
        'wrapper',
        background && `background-${background}`,
        color && `color-${color}`,
        className,
        {
          background,
          color,
        },
      )}
    >
      <div className={cx('content')}>
        <div ref={containerRef} className={cx('data', { loaded: !loading })}>
          <div className={cx('data-skeleton', { visible: loading })}>
            <Skeleton width='100%' className='data-skeleton-item' height={36} borderRadius={4} />
          </div>
          <span
            ref={contentRef}
            className={cx(!hasActiveCourse ? 'noCourse' : valueIsEmpty ? 'noData' : '')}
          >
            {renderContent()}
          </span>
        </div>
        <div className={cx('title', { hidden: !hasActiveCourse && !loading })}>{title}</div>
      </div>
    </div>
  )
})
