@use '../../assets/styles/mixins/colors';
@use '../../../shared/assets/styles/mixins/text';

.wrapper {
  align-items: flex-start;
  justify-content: center;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 20px 30px;

  &.background {
    @include colors.color using($color) {
      background-color: $color;
    }

    .title {
      color: var(--white);
    }
    .noData {
      color: var(--gray);
    }
  }
  &.color {
    @include colors.color using($color) {
      .data {
        color: $color;
      }
    }
  }
}

.row-reverse {
  flex-direction: row-reverse;
}

.col-reverse {
  flex-direction: column-reverse;
}

.content {
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
  height: 38px;
}

.noCourse {
  color: var(--battleship-grey);
  @include text.title(13px);
  white-space: pre;
}

.title {
  @include text.title(13px);
  line-height: 16px;
  max-width: 90px;
  opacity: 1;
  transition: opacity 0.3s ease-in-out;

  &.hidden {
    opacity: 0;
  }
}

.data {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  position: relative;
  width: 64px;
  min-width: 64px;
  height: 100%;

  font-family: GothamPro, sans-serif;
  font-size: 34px;
  line-height: normal;
  transition: width 0.4s ease-in-out;
  white-space: nowrap;
  overflow: hidden;

  &-skeleton {
    position: absolute;
    top: 50%;
    left: 0;
    z-index: 10;
    transform: translateY(-50%);
    width: 100%;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;

    &.visible {
      opacity: 1;
    }

    &-item {
      min-width: 64px;
    }
  }
}

.noData {
  @include text.title(13px);
  color: var(--battleship-grey);
  margin-right: auto;
}
