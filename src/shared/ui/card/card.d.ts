export const cardPadding = ['none', 'small', 'normal', 'big'] as const
type ICardPadding = (typeof CardPadding)[number]

export const cardMobilePadding = ['none', 'small', 'normal', 'big'] as const
type ICardMobilePadding = (typeof CardMobilePadding)[number]

export const cardWidth = ['wide', 'half', 'adaptive', 'flex'] as const
type ICardWidth= (typeof CardWidth)[number]

export const cardMobile = ['wide', 'half', 'adaptive'] as const
type ICardMobile = (typeof CardMobile)[number]

export declare namespace CardProps {
  interface Own extends React.HTMLProps<HTMLElement> {
    hoverable?: boolean

    // TODO refactoring
    padding?: ICardPadding
    mobilePadding?: ICardMobilePadding
    width?: ICardWidth
    mobile?: ICardMobile
    to?: string
  }

  type Props = Own & DataTest
}

export {}
