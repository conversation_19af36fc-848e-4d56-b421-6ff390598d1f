import React, { PropsWithChildren } from 'react'
import styles from './card.module.scss'
import classNamesBind from 'classnames/bind'
import { CardProps } from './card.d'

import { OptionalLink } from '../optional-link'

const cx = classNamesBind.bind(styles)

export const Card: React.FC<PropsWithChildren<CardProps.Props>> = props => {
  const {
    className,
    children,
    width = 'adaptive',
    padding = 'none',
    mobile = 'wide',
    mobilePadding = padding,
    onClick,
    hoverable = !!onClick,
    'data-testid': dataTestid = 'Card',
    ...otherProps
  } = props

  return (
    <OptionalLink
      className={cx(
        'wrapper',
        `width-${width}`,
        `padding-${padding}`,
        `mobile-${mobile}`,
        `mobile-padding-${mobilePadding}`,
        {
          hoverable,
        },
        className,
      )}
      onClick={onClick}
      data-testid={dataTestid}
      {...otherProps}
    >
      {children}
    </OptionalLink>
  )
}
