import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import { Card } from './card'
import { renderTestScreen } from '@/shared/helpers/tests/render'
import { cardPadding, cardMobilePadding, cardWidth, cardMobile } from './card.d'

describe('[shared] [ui] Card', () => {
    it('Должен отрендериться с нужным текстом', () => {
        const { getByTestId } = renderTestScreen(
            <Card>Нажми меня, пажаласта</Card>
        )

        const card = getByTestId('Card')

        expect(card).toBeInTheDocument()
        expect(card).toHaveTextContent('Нажми меня, пажаласта')
    })

    for (let i = 0; i < cardWidth.length; i++) {
        it(`Должен отрендерить карточку с нужным классом = ${cardWidth[i]}`, () => {
            const { getByTestId } = renderTestScreen(
                <Card width={cardWidth[i]} />
            )

            const card = getByTestId('Card')

            expect(card).toBeInTheDocument()
            expect(card).toHaveClass(`width-${cardWidth[i]}`)
        })
    }

    for (let i = 0; i < cardPadding.length; i++) {
        it(`Должен отрендерить карточку с нужным классом = ${cardPadding[i]}`, () => {
            const { getByTestId } = renderTestScreen(
                <Card padding={cardPadding[i]} />
            )

            const card = getByTestId('Card')

            expect(card).toBeInTheDocument()
            expect(card).toHaveClass(`padding-${cardPadding[i]}`)
            expect(card).toHaveClass(`mobile-padding-${cardPadding[i]}`)
        })
    }

    for (let i = 0; i < cardMobile.length; i++) {
        it(`Должен отрендерить карточку с нужным классом = ${cardMobile[i]}`, () => {
            const { getByTestId } = renderTestScreen(
                <Card mobile={cardMobile[i]} />
            )

            const card = getByTestId('Card')

            expect(card).toBeInTheDocument()
            expect(card).toHaveClass(`mobile-${cardMobile[i]}`)
        })
    }

    for (let i = 0; i < cardMobilePadding.length; i++) {
        it(`Должен отрендерить карточку с нужным классом = ${cardMobilePadding[i]}`, () => {
            const { getByTestId } = renderTestScreen(
                <Card mobilePadding={cardMobilePadding[i]} />
            )

            const card = getByTestId('Card')

            expect(card).toBeInTheDocument()
            expect(card).toHaveClass(`mobile-padding-${cardMobilePadding[i]}`)
        })
    }

    it(`олжен отрендерить карточку с классом hoverable при hoverable = true`, () => {
        const { getByTestId } = renderTestScreen(
            <Card hoverable />
        )

        const card = getByTestId('Card')

        expect(card).toBeInTheDocument()
        expect(card).toHaveClass(`hoverable`)
    })

    it(`Должен отрендерить карточку с классом hoverable при onClick != undefined`, () => {
        const mockedOnClick = jest.fn()

        const { getByTestId } = renderTestScreen(
            <Card onClick={mockedOnClick} />
        )

        const card = getByTestId('Card')

        expect(card).toBeInTheDocument()
        expect(card).toHaveClass(`hoverable`)
    })

    it('Должен обрабатывать onClick событие', async () => {
        const mockedOnClick = jest.fn()

        const { getByTestId } = renderTestScreen(
            <Card onClick={mockedOnClick} />
        )

        const card = getByTestId('Card')

        await userEvent.click(card)

        expect(mockedOnClick).toHaveBeenCalledTimes(1)
    })
})
