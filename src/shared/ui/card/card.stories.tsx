import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { Card } from './card'
import { cardPadding, cardMobilePadding, cardWidth, cardMobile } from './card.d'

const meta = {
    title: 'UI/Card',
    component: Card,
    tags: ['autodocs'],
    argTypes: {
        hoverable: {
            control: 'boolean',
            description: 'Должна ли работать hover',
            table: {
                type: { summary: 'boolean' },
                defaultValue: { summary: '!!onClick' },
            }
        },
        padding: {
            options: cardPadding,
            control: 'select',
            description: 'Размер padding\'a',
            table: {
                type: { summary: 'ICardPadding' },
                defaultValue: { summary: 'none' },
            }
        },
        mobilePadding: {
            options: cardMobilePadding,
            control: 'select',
            description: 'Размер padding\'a на мобильной версии (???) (возможно deprecated)',
            table: {
                type: { summary: 'ICardMobilePadding' },
                defaultValue: { summary: '= padding' },
            }
        },
        width: {
            options: cardWidth,
            control: 'select',
            description: 'Ширина карточки',
            table: {
                type: { summary: 'ICardWidth' },
                defaultValue: { summary: 'adaptive' },
            }
        },
        mobile: {
            options: cardMobile,
            control: 'select',
            description: 'Ширина карточки на мобильной версии (???) (возможно deprecated)',
            table: {
                type: { summary: 'ICardMobile' },
                defaultValue: { summary: 'wide' },
            }
        },
        to: {
            control: 'text',
            description: 'Путь до страницы, куда будет переход после клика',
            table: {
                type: { summary: 'string' },
                readonly: true,
            }
        },
        onClick: {
            control: 'object',
            description: 'Функция, которая вызывается при клике на элемент',
            table: {
                type: { summary: 'React.MouseEventHandler<HTMLElement>' },
                readonly: true,
            }
        },
    }
} satisfies Meta<typeof Card>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = { args: { children: 'Нажми меня' } }

export const Paddings: Story = {
    args: { children: 'Нажми меня' },
    decorators: () => {
        return <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
            alignItems: 'start'
        }}>
            <Card>Нажми на меня</Card>
            <Card padding='none'>Нажми на меня</Card>
            <Card padding='small'>Нажми на меня</Card>
            <Card padding='normal'>Нажми на меня</Card>
            <Card padding='big'>Нажми на меня</Card>
        </div>
    }
}

export const Width: Story = {
    args: { children: 'Нажми меня' },
    decorators: () => {
        return <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
            alignItems: 'start'
        }}>
            <Card padding='small'>Нажми на меня</Card>
            <Card padding='small' width='wide'>Нажми на меня</Card>
            <Card padding='small' width='half'>Нажми на меня</Card>
            <Card padding='small' width='adaptive'>Нажми на меня</Card>
            <Card padding='small' width='flex'>Нажми на меня</Card>
        </div>
    }
}