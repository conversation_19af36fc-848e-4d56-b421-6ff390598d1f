// @use 'media';

.wrapper {
  background-color: var(--color-surface);

  border-radius: 12px;
  box-sizing: border-box;

  color: initial;
  display: flex;
  flex-direction: column;

  text-decoration: none;

  &.hoverable {
    cursor: pointer;

    transition: 300ms ease;
  }
}

.width {
  &-wide {
    width: 100%;
  }
  &-half {
    width: 50%;
  }
  &-flex {
    flex: 1 0;
  }
}

.padding {
  &-none {
    padding: 0;
  }
  &-small {
    padding: 10px 20px;
  }
  &-normal {
    padding: 20px 30px;
  }
  &-big {
    padding: 30px 40px;
  }
}

.mobile-padding {
  // @include media.mobile {
  // 	&-none {
  // 		padding: 0;
  // 	}
  // 	&-small {
  // 		padding: 10px;
  // 	}
  // 	&-normal {
  // 		padding: 15px;
  // 	}
  // 	&-big {
  // 		padding: 25px;
  // 	}
  // }
}
