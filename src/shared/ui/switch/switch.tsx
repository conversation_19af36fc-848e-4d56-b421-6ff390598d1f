/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, useState } from 'react'
import styles from './switch.module.scss'
import classNamesBind from 'classnames/bind'
import { SwitchProps } from './switch.d'

const cx = classNamesBind.bind(styles)

export const Switch: FC<SwitchProps.Props> = props => {
  const { className, initialValue, customValue, text, onChange, disabled = false } = props

  const [value, setValue] = useState(initialValue !== undefined ? initialValue : false)

  const handleClick = () => {
    if (customValue !== undefined && onChange) {
      onChange(!value)
    } else if (onChange) {
      /**
       * TODO: onChange(value)
       * @description Вынес здесь из setValue,
       * т.к. ругается, если передаешь библиотечные функции,
       * а они вызываются в useState
       *
       * ? INFO - BATCHING
       * !!! WARNING - В React 17.* - onChange(value)
       * !!! WARNING - В React 18.* - onChange(!value)
       */
      onChange(!value)
      setValue((prev: any) => !prev)
    }
  }

  return (
    <div className={cx('wrapper', className)} onClick={handleClick}>
      <div
        className={cx('inner', {
          active: customValue !== undefined ? customValue : value,
          disabled,
        })}
      >
        <span></span>
      </div>
      {text && <span className={cx('text')}>{text}</span>}
    </div>
  )
}
