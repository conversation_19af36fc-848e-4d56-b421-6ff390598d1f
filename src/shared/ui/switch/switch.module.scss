.wrapper {
  align-items: center;
  display: flex;
  gap: 12px;
}

.inner {

  background: var(--color-gray-60, #c9cedc);
  border-radius: 16px;
  cursor: pointer;
  height: 24px;
  position: relative;

  transition: var(--transition);

  width: 40px;
  span {

    background: var(--color-surface, #fff);
    border-radius: 50%;
    filter: drop-shadow(0 1px 4px rgba(89, 102, 132, 0.1));
    height: 20px;
    left: 22px;
    position: absolute;
    top: 50%;
    transform: translateX(-100%) translateY(-50%);

    transition: var(--transition);

    width: 20px;
  }

  &:hover {
    background: var(--color-gray-70);

    transition: var(--transition);
  }

  &.active {
    background: var(--color-primary-90);

    transition: var(--transition);
    span {
      left: 38px;

      transition: var(--transition);
    }
    &:hover {
      background: var(--color-primary);

      transition: var(--transition);
    }
  }

  &.disabled {
    background: var(--color-gray-50);

    transition: var(--transition);
    span {
      background: var(--color-gray-20);

      transition: var(--transition);
    }
    &.active {
      background: var(--color-primary-60);

      transition: var(--transition);
    }
  }
}

.text {
  color: var(--color-gray-90);

  cursor: pointer;
  font: var(--font-text-2-normal);
}
