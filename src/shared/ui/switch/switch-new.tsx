import { FC, useEffect, useState } from 'react'
import styles from './switch.module.scss'
import classNamesBind from 'classnames/bind'

const cx = classNamesBind.bind(styles)

type SwitchProps = {
  value: boolean
  onChange?: (value: boolean) => void
  disabled?: boolean
  text?: string
}

export const SwitchNew: FC<SwitchProps> = ({ text, value, onChange, disabled = false }) => {
  const [isActive, setIsActive] = useState(value)

  const handleClick = () => {
    if (disabled) return

    const newValue = !isActive
    setIsActive(newValue)

    if (onChange) {
      onChange(newValue)
    }
  }

  useEffect(() => {
    setIsActive(!!value)
  }, [value])

  return (
    <div className={cx('wrapper')} onClick={handleClick}>
      <div
        className={cx('inner', {
          active: isActive !== undefined ? isActive : value,
          disabled,
        })}
      >
        <span></span>
      </div>
      {text && <span className={cx('text')}>{text}</span>}
    </div>
  )
}
