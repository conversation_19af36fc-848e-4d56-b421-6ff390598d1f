import { FormTitle } from './form-title'
import '@testing-library/jest-dom'
import { renderTestScreen } from '../../helpers/tests/render'

describe('[shared] [UI]: FormTitle', () => {
    it('Компонент должен отрендериться с детьми', () => {
        const { getByTestId } = renderTestScreen(
            <FormTitle >Тестовый заголовок формочки</FormTitle>,
        )

        const title = getByTestId('FormTitle')
        expect(title).toBeInTheDocument()
        expect(title).toHaveTextContent('Тестовый заголовок формочки')
    })
})
