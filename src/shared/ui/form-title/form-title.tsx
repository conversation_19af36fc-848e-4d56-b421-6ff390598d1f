import { FC } from 'react'
import styles from './form-title.module.scss'
import classNamesBind from 'classnames/bind'
import { FormTitleProps } from './form-title.d'

const cx = classNamesBind.bind(styles)

export const FormTitle: FC<FormTitleProps.Props> = props => {
  const { className, children, 'data-testid': dataTestid = 'FormTitle' } = props

  return (
    <div className={cx('title', className)} data-testid={dataTestid}>
      {children}
    </div>
  )
}
