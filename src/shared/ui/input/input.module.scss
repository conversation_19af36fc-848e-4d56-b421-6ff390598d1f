.input {
  appearance: none;

  background: #fff;
  border: 1px solid #ebeff2;
  border-radius: 12px;

  box-shadow:
    0 0 0 2px transparent,
    0 0 0 50px transparent inset !important;
  box-shadow:
    0 0 0 2px transparent,
    0 0 0 50px transparent inset !important;
  box-sizing: border-box;

  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);
  max-width: 100%;
  outline: none;

  padding: 13px 14px;

  transition:
    ease 0.15s,
    margin 0s;

  &:-webkit-autofill {
    box-shadow:
      0 0 0 2px transparent,
      0 0 0 50px #fff inset !important;
    box-shadow:
      0 0 0 2px transparent,
      0 0 0 50px #fff inset !important;

    transition:
      ease 0.15s,
      margin 0s;
  }

  &::input-placeholder,
  &:placeholder,
  &::placeholder,
  &:input-placeholder,
  &::placeholder {
    color: var(--color-gray-70, #8e97af);
    font: var(--font-text-2-normal);
  }

  &.fullWidth {
    width: 100%;
  }

  &:hover {
    border: 1px solid #c9cedc;

    transition:
      ease 0.15s,
      margin 0s;
  }

  &:focus,
  &:-webkit-autofill:focus {
    border: 1px solid #fff;
    box-shadow:
      0 0 0 2px var(--color-primary),
      0 0 0 50px #fff inset !important;
    box-shadow:
      0 0 0 2px var(--color-primary),
      0 0 0 50px #fff inset !important;

    caret-color: var(--color-primary);

    transition:
      ease 0.15s,
      margin 0s;
  }

  &.error,
  &.error:-webkit-autofill {
    border: 1px solid #fff;
    box-shadow:
      0 0 0 2px #ff586b,
      0 0 0 50px #fff inset !important;
    box-shadow:
      0 0 0 2px #ff586b,
      0 0 0 50px #fff inset !important;

    transition:
      ease 0.15s,
      margin 0s;
  }

  &.disabled {
    background: #fff;
    // border: 1px solid #ebeff2;
    color: #8e97af;

    font: var(--font-text-2-medium);
  }

  &:has(+ .domainName) {
    padding-right: 100px;
  }
}

.inputWrapper {
  display: block;
  position: relative;

  .label {
    align-items: center;

    color: var(--color-gray-90, #343b54);
    display: flex;
    font: var(--font-text-2-medium);
    gap: 8px;

    margin-bottom: 8px;
    span {
      color: var(--color-statistics-bad, #ff8577);
    }
  }
  .domainName {
    background: #fff;

    color: #5c6585;
    font-family: 'TT Norms Pro';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);

    user-select: none;
    pointer-events: none;
  }

  .errorText {
    margin-top: 12px;
  }
}

.bordered {
  border-color: var(--color-primary);

  &:hover,
  &:active,
  &:focus {
    border: 1px solid var(--color-primary);
    box-shadow: none !important;
  }
}
