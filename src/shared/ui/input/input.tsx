import React, { useState, forwardRef } from 'react'
import styles from './input.module.scss'
import classNamesBind from 'classnames/bind'
import { InputProps } from './input.d'

import { HelpIcon } from '@/shared/ui/help-icon'

const cx = classNamesBind.bind(styles)

export const Input = forwardRef<HTMLInputElement, InputProps.Props>((props, ref) => {
  const {
    classNameWrapper,
    className,
    classNameDomain,
    disabled,
    placeholder,
    domain,
    value,
    name,
    error,
    onChange,
    onBlur,
    bordered,
    fullWidth,
    required,
    label,
    labelClassName,
    help = '',
    register,
    readOnly = true,
    slot,
    ...otherProps
  } = props

  const _onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled || !onChange) return

    onChange(e.target.value, name || '', e)
  }

  const _onBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    if (!(disabled || !onBlur)) {
      onBlur && onBlur(e)
    }
    register?.onBlur(e)
  }
  const isDate = otherProps?.type?.includes('date')

  const [readonly, setReadonly] = useState(isDate ? false : readOnly)

  return (
    <label className={cx('inputWrapper', classNameWrapper)}>
      {label && (
        <div className={cx('label', labelClassName)}>
          {label} {required && <span>*</span>}
          {help && <HelpIcon text={help} />}
        </div>
      )}
      {slot}
      <input
        ref={ref}
        className={cx('input', className, {
          fullWidth,
          error,
          disabled,
          domain,
          bordered,
        })}
        name={name}
        disabled={disabled}
        placeholder={placeholder}
        required={required}
        value={value}
        onChange={_onChange}
        {...otherProps}
        {...register}
        type={otherProps?.type ?? 'text'}
        onBlur={_onBlur}
        autoComplete='off'
        readOnly={readonly}
        onFocus={() => setReadonly(false)}
      />
      {domain && <div className={cx('domainName', classNameDomain)}>{domain}</div>}
      {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
    </label>
  )
})

Input.displayName = 'Input'
