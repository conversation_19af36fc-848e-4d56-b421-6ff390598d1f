import { ReactNode } from 'react'
import { UseFormRegisterReturn, TFieldName } from 'react-hook-form'

export declare namespace InputProps {
  interface Own
    extends React.DetailedHTMLProps<React.InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> {
    className?: string
    classNameWrapper?: string
    classNameDomain?: string
    placeholder?: string
    resize?: boolean
    bordered?: boolean
    domain?: string
    value?: string
    name?: string
    error?: string | boolean
    help?: string | React.ReactElement
    label?: string
    labelClassName?: string
    onChange?: (value: string, name: string, e) => void
    fullWidth?: boolean
    required?: boolean
    register?: UseFormRegisterReturn<TFieldName>
    slot?: ReactNode
  }

  type Props = Own
}

export {}
