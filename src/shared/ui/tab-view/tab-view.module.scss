.wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.header {
  align-items: flex-end;

  display: flex;
  height: 40px;
  width: 100%;
  z-index: 1;
  .title {
    align-items: center;

    background-color: var(--gray);
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    box-shadow: 0 -20px 40px 0 var(--silver);
    cursor: pointer;
    display: flex;
    height: 40px;
    justify-content: center;
    margin-right: 5px;

    transition: 200ms ease;
    width: 260px;
    &:not(.active) {
      height: 30px;
    }
    &:hover {
      // background-color: var(--gray);
      height: 40px;
    }
    &.active {
      background-color: var(--white);
    }
  }
}
.content {
  border-top-left-radius: 0%;
  width: 100%;
}
