import React, { useMemo, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './tab-view.module.scss'

import { TabType, TabViewProps } from './tab-view.d'
import { Card } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

const TabView: React.FC<TabViewProps.Props> = props => {
  const { className, headerClass, contentClass, tabs: propsedTabs } = props

  const tabs = useMemo(
    () =>
      React.Children.map<TabType | null, TabType>(propsedTabs, ch =>
        React.isValidElement<TabType>(ch) ? ch : null,
      )
        .filter(e => !!e)
        .map(c => c.props),
    [propsedTabs],
  )

  const [active, setActive] = useState<number | string>(tabs[0]?.id)
  const activeTab = useMemo(() => tabs.find(c => c.id === active), [tabs, active])

  if (tabs.length === 0) {
    console?.warn('tabView has no children')

    return null
  }

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('header', headerClass)}>
        {tabs.map(({ id, title }) => (
          <div
            key={id}
            className={cx('title', { active: id === active })}
            onClick={setActive.bind(null, id)}
          >
            {title}
          </div>
        ))}
      </div>
      <Card padding='normal' className={cx('content', contentClass)}>
        {activeTab?.children}
      </Card>
    </div>
  )
}

export const Tab: React.FC<TabViewProps.TabProps> = props => {
  const { children } = props
  return React.isValidElement(children) ? children : null
}

export default TabView
