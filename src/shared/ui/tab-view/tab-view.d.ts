/* eslint-disable @typescript-eslint/no-explicit-any */
export type TabType = React.ReactElement<TabViewProps.TabProps>

export declare namespace TabViewProps {
  interface Own {
    className?: string
    children?: TabType | TabType[]
    contentClass?: string
    headerClass?: string
    tabs: TabType | TabType[]
  }

  interface Store {}

  interface Dispatch {}

  interface TabProps {
    id: string | number
    className?: string
    title: string
    children: any
  }

  type Props = Own & Store & Dispatch
}

export {}
