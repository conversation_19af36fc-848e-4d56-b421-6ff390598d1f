import { test, expect } from '@playwright/test';

test.describe('PageTitle (Storybook)', () => {
  test('Должен отрендерить Default', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-pagetitle--default');

    const title = page.locator('[data-testid="PageTitle"]');
    await expect(title).toBeVisible();
    await expect(title).toHaveText('Заголовок страницы');

    await expect(title).toHaveScreenshot('pagetitle-default.png');
  });

  test('Должен отрендерить WithLongText', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-pagetitle--with-long-text');

    const title = page.locator('[data-testid="PageTitle"]');
    await expect(title).toBeVisible();
    await expect(title).toHaveText('Очень длинный заголовок, который может занимать несколько строк, но наверное нужно побольше, чтобы он точно мог занять несколько строк');

    await expect(title).toHaveScreenshot('pagetitle-long-text.png');
  });

  test('Должен отрендерить WithJSX', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-pagetitle--with-jsx');

    const title = page.locator('[data-testid="PageTitle"]');
    await expect(title).toBeVisible();
    await expect(title).toContainText('Подчеркнутый');
    await expect(title).toContainText('заголовок');

    await expect(title).toHaveScreenshot('pagetitle-with-jsx.png');
  });
});
