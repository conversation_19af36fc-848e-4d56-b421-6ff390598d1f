import { FC } from "react";
import styles from "./page-title.module.scss";
import classNamesBind from "classnames/bind";
import { PageTitleProps } from "./page-title.d";

const cx = classNamesBind.bind(styles);

/**
 * Компонент заголовка страницы.
 *
 * @component
 * @example
 * ```tsx
 * <PageTitle>Главная страница</PageTitle>
 * ```
 *
 * @param {PageTitleProps} props - Свойства компонента.
 * @param {React.ReactNode} props.children - Заголовок страницы (текст или элементы).
 * @param {string} [props.className] - Дополнительные CSS-классы.
 * @param {string} [props.data-testid="PageTitle"] - Тестовый идентификатор для тестирования.
 *
 * @returns {JSX.Element | null} Возвращает элемент `<h1>` с заголовком или `null`, если `children` пустой.
 */
export const PageTitle: FC<PageTitleProps> = (props) => {
  const { children, className, 'data-testid': dataTestid = 'PageTitle' } = props;

  if (!children || (typeof children === "string" && !children.trim())) return null;

  return <h1 className={cx("title", className)} data-testid={dataTestid}>{children}</h1>;
};
