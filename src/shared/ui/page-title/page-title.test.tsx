import { PageTitle } from './page-title'
import '@testing-library/jest-dom'
import { renderTestScreen } from '../../helpers/tests/render'

describe('[shared] [ui] PageTitle', () => {
  it("Должен рендериться с текстом", () => {
    const { getByTestId } = renderTestScreen(<PageTitle>Заголовок</PageTitle>);

    expect(getByTestId("PageTitle")).toBeInTheDocument();
    expect(getByTestId("PageTitle")).toHaveTextContent("Заголовок");
  });

  it("Должен корректно рендерить JSX внутри children", () => {
    const { getByTestId } = renderTestScreen(
      <PageTitle>
        <span>Тест</span>
      </PageTitle>
    );
    expect(getByTestId("PageTitle")).toContainHTML("<span>Тест</span>");
  })

  it("Не должен рендериться, если children пустой", () => {
    const { container } = renderTestScreen(<PageTitle>{""}</PageTitle>);

    expect(container.firstChild).toBeNull();
  });

  it("Не должен рендериться, если children содержит только пробелы", () => {
    const { container } = renderTestScreen(<PageTitle>{"   "}</PageTitle>);

    expect(container.firstChild).toBeNull();
  });

  it("Должен корректно рендерить вложенные элементы", () => {
    const { getByTestId } = renderTestScreen(
      <PageTitle>
        <strong>Вложенный</strong> текст
      </PageTitle>
    );
    expect(getByTestId("PageTitle")).toContainHTML(
      "<strong>Вложенный</strong> текст"
    );
  });

  it("Должен использовать memo и не перерисовываться, если пропсы не изменились", () => {
    const { container, rerender } = renderTestScreen(<PageTitle>Тест</PageTitle>);
    const firstRender = container.innerHTML;

    rerender(<PageTitle>Тест</PageTitle>);
    const secondRender = container.innerHTML;

    expect(firstRender).toBe(secondRender);
  });


  it("Должен соответствовать снепшоту", () => {
    const { asFragment } = renderTestScreen(<PageTitle>Снепшот</PageTitle>);

    expect(asFragment()).toMatchSnapshot();
  });
});
