import type { Meta, StoryObj } from '@storybook/react';
import { PageTitle } from './page-title';

const meta: Meta<typeof PageTitle> = {
  title: 'UI/PageTitle',
  component: PageTitle,
  argTypes: {
    children: {
      description: 'Текст заголовка',
      control: { type: 'text' },
      table: {
        type: { summary: 'ReactNode' },
        defaultValue: { summary: '—' },
      },
    },
    className: {
      description: 'Дополнительный CSS-класс',
      control: { type: 'text' },
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '—' },
      },
    },
    'data-testid': {
      table: { defaultValue: { summary: '"PageTitle"' } },
    },
  },
};

export default meta;
type Story = StoryObj<typeof PageTitle>;

export const Default: Story = {
  args: {
    children: 'Заголовок страницы',
  },
};

export const WithLongText: Story = {
  args: {
    children: 'Очень длинный заголовок, который может занимать несколько строк, но наверное нужно побольше, чтобы он точно мог занять несколько строк',
  },
};

export const WithJSX: Story = {
  args: {
    children: (
      <>
        <u>Подчеркнутый</u> заголовок
      </>
    ),
  },
};
