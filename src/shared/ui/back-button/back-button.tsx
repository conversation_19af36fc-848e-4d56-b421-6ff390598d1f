import { FC } from 'react'
import styles from './back-button.module.scss'
import classNamesBind from 'classnames/bind'
import { BackButtonProps } from './back-button.d'
import ChevroneBoldIcon from '../Icon/icons/components/ChevroneBoldIcon'
import { IconWrapper } from '../Icon/IconWrapper'
import { useNavigate } from 'react-router-dom'
import { Trans } from 'react-i18next'

const cx = classNamesBind.bind(styles)

/**
 * Компонент кнопки "Назад".
 *
 * Позволяет пользователю вернуться на предыдущую страницу или перейти по указанному маршруту.
 *
 * @component
 * @param {BackButtonProps} props - Свойства компонента
 * @param {string} [props.className] - Дополнительные CSS-классы для стилизации компонента
 * @param {string} [props.route] - URL-адрес, на который будет осуществлен переход. Если не указан, выполняется переход назад (-1)
 * @param {string} [props.data-testid="BackButton"] - Тестовый идентификатор (по умолчанию "BackButton")
 * @returns {JSX.Element} Компонент кнопки "Назад"
 */
export const BackButton: FC<BackButtonProps> = props => {
  const { className, route, arrowSize, 'data-testid': dataTestid = 'BackButton' } = props

  const navigate = useNavigate()
  const goBack = () => {
    if (route) {
      navigate(route)
    } else {
      navigate(-1)
    }
  }

  return (
    <div className={cx('wrapper', className)} onClick={goBack} data-testid={dataTestid}>
      <IconWrapper
        size={arrowSize || '20'}
        color='gray70'
        direction='down'
        className={cx('icon')}
        data-testid={`${dataTestid}.Icon`}
      >
        <ChevroneBoldIcon />
      </IconWrapper>
      <Trans i18nKey='old.back_button.back' />
    </div>
  )
}
