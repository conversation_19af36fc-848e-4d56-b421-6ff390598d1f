import { BackButton } from './back-button'
import { renderTestScreen } from '@/shared/helpers/tests/render'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import { addI18nResources, initI18n } from '@/shared/helpers/tests/i18n'

const mockedUsedNavigate = jest.fn()

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockedUsedNavigate,
}))

beforeAll(() => {
  initI18n()
})

describe('[shared] [ui] BackButton', () => {
  beforeAll(() => {
    addI18nResources({ old: { back_button: { back: 'Назад' } } })
  })

  beforeEach(() => {
    mockedUsedNavigate.mockClear()
  })

  it('Должен отрендериться с текстом', () => {
    const { getByTestId } = renderTestScreen(<BackButton />)

    const button = getByTestId('BackButton')

    expect(button).toBeInTheDocument()
    expect(button.textContent).toBe('Назад')
  })

  it('Должен изменять роутинг на переданный route', async () => {
    const route = '/testing'

    const { getByTestId } = renderTestScreen(<BackButton route={route} />)
    const button = getByTestId('BackButton')

    await userEvent.click(button)

    expect(mockedUsedNavigate).toHaveBeenCalledTimes(1)
    expect(mockedUsedNavigate).toHaveBeenCalledWith(route)
  })

  it('Должен изменять роутинг на предыдущий при отсутствии route', async () => {
    const { getByTestId } = renderTestScreen(<BackButton />)
    const button = getByTestId('BackButton')

    await userEvent.click(button)

    expect(mockedUsedNavigate).toHaveBeenCalledTimes(1)
    expect(mockedUsedNavigate).toHaveBeenCalledWith(-1)
  })
})
