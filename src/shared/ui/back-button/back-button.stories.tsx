import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'

import { BackButton } from './back-button'

const meta: Meta<typeof BackButton> = {
  title: 'UI/BackButton',
  component: BackButton,
  argTypes: {
    route: {
      description: 'Ссылка на страницу, на которую будет переход. Если не указано, выполняется переход на -1 (назад).',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: '-1' },
      },
      control: 'text',
    },
    'data-testid': { table: { defaultValue: { summary: '"BackButton"' }, } }
  }
};
 
export default meta;
type Story = StoryObj<typeof BackButton>;
 
export const Default: Story = {};