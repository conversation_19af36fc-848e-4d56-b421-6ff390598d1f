import { test, expect } from '@playwright/test';

test.describe('BackButton (Storybook)', () => {
  test.beforeEach(async ({ page }) => {
    // Для того, чтобы navigate(-1) сработал
    await page.goto('http://localhost:9009/');
    await expect(page).not.toHaveURL('about:blank');
  });

  test('Должен отрендерить Default', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-backbutton--default');

    const backButton = page.locator('[data-testid="BackButton"]');
    await expect(backButton).toBeVisible();
    await expect(backButton).toContainText('Назад');

    const icon = page.locator('[data-testid="BackButton.Icon"]');
    await expect(icon).toBeVisible();

    await expect(backButton).toHaveScreenshot('backbutton-default.png');
  });

  test('Должен обработать onClick и перейти назад', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-backbutton--default');

    const backButton = page.locator('[data-testid="BackButton"]');
    await expect(backButton).toBeVisible();

    await backButton.click();

    // Проверяем, что переход сработал (на один шаг назад)
    await expect(page).toHaveURL(/.*localhost:9009.*/);
  });

  test('Должен обработать onClick с кастомным маршрутом', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-backbutton--default&args=route:custom-path');

    const backButton = page.locator('[data-testid="BackButton"]');
    await expect(backButton).toBeVisible();

    await backButton.click();

    // Проверяем, что редирект произошел на /custom-path
    await expect(page).toHaveURL(/.*\/custom-path/);
  });
});
