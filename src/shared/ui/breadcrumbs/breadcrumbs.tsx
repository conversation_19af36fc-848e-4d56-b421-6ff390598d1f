import React, { FC } from 'react'
import styles from './breadcrumbs.module.scss'
import classNamesBind from 'classnames/bind'
import { BreadcrumbsProps, BreadcrumbItem } from './breadcrumbs.d'
import ChevroneSmallIcon from '../Icon/icons/components/ChevroneSmallIcon'
import { IconWrapper } from '../Icon/IconWrapper'
import { useNavigate } from 'react-router-dom'
import Skeleton from 'react-loading-skeleton'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

/**
 * Компонент `Breadcrumbs` (хлебные крошки) для навигации по страницам.
 *
 * @component
 * @example
 * const items = [
 *   { id: '/', text: 'Главная', clickable: true },
 *   { id: '/catalog', text: 'Каталог', clickable: true },
 *   { id: '/product', text: 'Товар', clickable: false }
 * ];
 * return <Breadcrumbs items={items} />;
 *
 * @param {BreadcrumbsProps.Props} props Свойства компонента
 * @param {string} [props.className] Дополнительный CSS-класс для компонента
 * @param {BreadcrumbItem[]} props.items Массив элементов хлебных крошек
 * @param {(id: string) => void} [props.onClick] Функция, вызываемая при клике на элемент
 * @param {string} [props.data-testid="Breadcrumbs"] Тестовый идентификатор (по умолчанию "Breadcrumbs")
 *
 * @returns {JSX.Element | null} Разметка хлебных крошек или `null`, если массив `items` пуст.
 */
export const Breadcrumbs: FC<BreadcrumbsProps> = props => {
  const { className, items, onClick, 'data-testid': dataTestid = 'Breadcrumbs' } = props
  const { t } = useTranslation()

  const navigate = useNavigate()

  if (!items.length) return null

  const handleClick = (item: BreadcrumbItem) => {
    if (!item.clickable) return
    if (onClick) return onClick(item.id, item.text)

    navigate(item.id)
  }

  return (
    <div className={cx('wrapper', className)} data-testid={dataTestid}>
      {items.map((item, i) => {
        const isLast = i === items.length - 1

        const content = item?.isLoading ? (
          <Skeleton containerTestId={`${dataTestid}.Skeleton.${i}`} width={120} height={20} />
        ) : (
          t(item?.text)
        )

        return (
          <React.Fragment key={`${item.id}-${item.text}`}>
            <div
              className={cx('item', {
                clickable: !!item.clickable && !isLast,
                active: isLast,
              })}
              onClick={() => handleClick(item)}
              data-testid={`${dataTestid}.${i}`}
            >
              {content}
            </div>
            {!isLast && (
              <IconWrapper color='gray70' data-testid={`${dataTestid}.${i}.Icon`}>
                <ChevroneSmallIcon />
              </IconWrapper>
            )}
          </React.Fragment>
        )
      })}
    </div>
  )
}
