import { Breadcrumbs } from './breadcrumbs'
import { renderTestScreen } from '@/shared/helpers/tests/render'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import { initI18n } from '@/shared/helpers/tests/i18n'
import { BreadcrumbItem } from '.'

const mockedUsedNavigate = jest.fn()

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockedUsedNavigate,
}))

beforeAll(() => {
  initI18n()
})

describe('[shared] [ui] Breadcrumbs', () => {
  const items: BreadcrumbItem[] = [
    { id: '/', text: 'Главная', clickable: true },
    { id: '/catalog', text: 'Каталог', clickable: true },
    { id: '/product', text: 'Товар', clickable: false },
  ]

  it('Должен рендерит хлебные крошки корректно', () => {
    const { getByTestId, getByText } = renderTestScreen(<Breadcrumbs items={items} />)

    expect(getByTestId('Breadcrumbs')).toBeInTheDocument()
    expect(getByText('Главная')).toBeInTheDocument()
    expect(getByText('Каталог')).toBeInTheDocument()
    expect(getByText('Товар')).toBeInTheDocument()
  })

  it('Не должен рендерить компонент, если items пуст', () => {
    const { queryByTestId } = renderTestScreen(<Breadcrumbs items={[]} />)
    expect(queryByTestId('Breadcrumbs')).not.toBeInTheDocument()
  })

  it('Должен рендерит Skeleton, если isLoading=true', () => {
    const loadingItems = [{ id: '/loading', text: 'Loading', isLoading: true }]
    const { getByTestId } = renderTestScreen(<Breadcrumbs items={loadingItems} />)

    expect(getByTestId('Breadcrumbs')).toBeInTheDocument()
    expect(getByTestId('Breadcrumbs.0')).toBeInTheDocument()
    expect(getByTestId('Breadcrumbs.Skeleton.0')).toContainHTML('<span')
  })

  it('Должен обрабатывать клик по крошке с onClick', async () => {
    const onClickMock = jest.fn()
    const { getByTestId } = renderTestScreen(<Breadcrumbs items={items} onClick={onClickMock} />)

    const firstItem = getByTestId('Breadcrumbs.0')

    await userEvent.click(firstItem)

    expect(onClickMock).toHaveBeenCalledWith('/', 'Главная')
  })

  test('Не должен вызывать onClick у не-кликабельного элемента', async () => {
    const onClickMock = jest.fn()
    const { getByTestId } = renderTestScreen(<Breadcrumbs items={items} onClick={onClickMock} />)

    const lastItem = getByTestId('Breadcrumbs.2')

    await userEvent.click(lastItem)

    expect(onClickMock).not.toHaveBeenCalled()
  })

  test('Рендерит иконки-разделители между элементами', () => {
    const { getByTestId, queryByTestId } = renderTestScreen(<Breadcrumbs items={items} />)

    expect(getByTestId('Breadcrumbs.0.Icon')).toBeInTheDocument()
    expect(getByTestId('Breadcrumbs.1.Icon')).toBeInTheDocument()
    expect(queryByTestId('Breadcrumbs.2.Icon')).not.toBeInTheDocument() // Последний не должен иметь иконку
  })

  it('Должен срабатывать navigate, с ожидаемым аргументом, при условии, что не передали onClick', async () => {
    const { getByTestId } = renderTestScreen(<Breadcrumbs items={items} />)

    const firstItem = getByTestId('Breadcrumbs.0')

    await userEvent.click(firstItem)

    expect(mockedUsedNavigate).toHaveBeenCalledWith('/')
  })
})
