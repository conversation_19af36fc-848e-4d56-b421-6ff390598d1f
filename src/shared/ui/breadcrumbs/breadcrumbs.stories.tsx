import type { Meta, StoryObj } from '@storybook/react'

import { Breadcrumbs } from './breadcrumbs'
import { action } from '@storybook/addon-actions';

const meta: Meta<typeof Breadcrumbs> = {
  title: 'UI/Breadcrumbs',
  component: Breadcrumbs,
  argTypes: {
    items: {
      description: 'Массив элементов хлебных крошек',
      control: { type: 'object' },
      table: {
        type: { summary: 'BreadcrumbItem[]' },
        defaultValue: { summary: '[]' },
      },
    },
    onClick: {
      description: 'Функция, вызываемая при клике на элемент',
      action: 'clicked',
      table: {
        type: { summary: '(id: string) => void' },
        defaultValue: { summary: 'navigate(item.id)' },
      },
    },
    'data-testid': { table: { defaultValue: { summary: '"Breadcrumbs"' }, } }
  },
}

export default meta;
type Story = StoryObj<typeof Breadcrumbs>;


export const Default: Story = {
  args: {
    items: [
      { id: '/', text: 'Главная', clickable: true },
      { id: '/catalog', text: 'Каталог', clickable: true },
      { id: '/product', text: 'Товар', clickable: false },
    ],
  }
}

export const WithLoading: Story = {
  args: {
    items: [
      { id: '/', text: 'Главная', clickable: true },
      { id: '/catalog', text: 'Каталог', isLoading: true },
      { id: '/product', text: 'Товар', clickable: false },
    ],
    onClick: action('clicked'),
  },
};

export const WithCustomOnClick: Story = {
  args: {
    items: [
      { id: '/', text: 'Главная', clickable: true },
      { id: '/catalog', text: 'Каталог', clickable: true },
      { id: '/product', text: 'Товар', clickable: false },
    ],
    onClick: (id) => alert(`Clicked: ${id}`),
  },
};
