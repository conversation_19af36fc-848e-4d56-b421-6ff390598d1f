/**
 * Интерфейс элемента хлебных крошек.
 *
 * @interface
 * @property {string} id - Уникальный идентификатор или путь элемента
 * @property {string} text - Текст, отображаемый в хлебных крошках
 * @property {boolean} [clickable] - Флаг, разрешающий клик (по умолчанию `false`)
 * @property {boolean} [isLoading] - Флаг загрузки (если `true`, отображается скелетон)
 */
export interface BreadcrumbItem {
  id: string
  text: string
  clickable?: boolean
  isLoading?: boolean
}

/**
 * Свойства компонента `Breadcrumbs`.
 *
 * @interface
 * @extends DataTest
 * @extends ClassName
 * @property {BreadcrumbItem[]} items - Массив элементов хлебных крошек
 * @property {(id: string | UUID) => void} [onClick] - Функция, вызываемая при клике на элемент
 */
export interface BreadcrumbsProps extends DataTest, ClassName {
  items: BreadcrumbItem[]
  onClick?: (id: string | UUID, name?: string) => void
}
