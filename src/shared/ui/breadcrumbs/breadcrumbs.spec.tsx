import { test, expect } from '@playwright/test';

test.describe('Breadcrumbs (Storybook)', () => {
  test('Должен отрендерить Default', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-breadcrumbs--default');

    const breadcrumbs = page.locator('[data-testid="Breadcrumbs"]');
    await expect(breadcrumbs).toBeVisible();
    await expect(breadcrumbs).toContainText('Главная');
    await expect(breadcrumbs).toContainText('Каталог');
    await expect(breadcrumbs).toContainText('Товар');

    await expect(breadcrumbs).toHaveScreenshot('breadcrumbs-default.png');
  });

  test('Должен отрендерить withLoading', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-breadcrumbs--with-loading');

    const breadcrumbs = page.locator('[data-testid="Breadcrumbs"]');
    await expect(breadcrumbs).toBeVisible();

    // Проверяем, что загрузка отображается (если Skeleton имеет data-testid)
    const loadingItem = page.locator('[data-testid="Breadcrumbs.Skeleton.1"]');
    await expect(loadingItem).toBeVisible();

    await expect(breadcrumbs).toHaveScreenshot('breadcrumbs-loading.png');
  });

  test('Должен отрендерить withCustomOnClick и обработать onClick', async ({ page }) => {
    await page.goto('http://localhost:9009/iframe.html?id=ui-breadcrumbs--with-custom-on-click');

    const breadcrumbs = page.locator('[data-testid="Breadcrumbs"]');
    await expect(breadcrumbs).toBeVisible();

    // Кликаем по "Каталог"
    await page.locator('[data-testid="Breadcrumbs.1"]').click();

    // Проверяем, что был вызван alert (Playwright перехватывает alert'ы)
    page.once('dialog', async (dialog) => {
      expect(dialog.message()).toContain('Clicked: /catalog');
      await dialog.dismiss();
    });

    await expect(breadcrumbs).toHaveScreenshot('breadcrumbs-custom-click.png');
  });
});
