.wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  padding: 15px 16px;
  cursor: pointer;

  &.loading {
    cursor: inherit;
  }

  &.disabled {
    cursor: initial;
  }
}

.label {
  align-items: center;
  color: #343b54;
  display: flex;
  font-family: 'TT Norms Pro';
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  gap: 4px;
  line-height: 18px;
  margin-bottom: 8px;

  span {
    color: #ff4b60;
  }
}

.icon {
  margin-left: auto;

  &.loading {
    animation: spin 0.7s linear infinite;

    @keyframes spin {
      100% {
        transform: rotate(360deg);
      }
    }
  }
}

.input {
  border: none;
  outline: none;
  width: 100%;
  font-family: inherit;
  padding: 0;
  font-size: 14px;
}
