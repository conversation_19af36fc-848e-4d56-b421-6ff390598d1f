import { FC, useCallback, useEffect, useRef, useState } from 'react'
import classNamesBind from 'classnames/bind'

import { HelpIcon } from '@/shared/ui'
import { useEvent } from '@/shared/hooks'
import type { InputSelectProps } from './input-select.d'
import type { IListItem } from '../select/select.d'
import styles from './input-select.module.scss'
import { SelectList } from '../select/select'
import ChevroneMediumIcon from '../Icon/icons/components/ChevroneMediumIcon'
import LoadIcon from '../Icon/icons/components/LoadIcon'
import { IconWrapper } from '../Icon/IconWrapper'

const cx = classNamesBind.bind(styles)

export const InputSelect: FC<InputSelectProps.Props> = ({
  className,
  list,
  listClassName,
  wrapperClassName,
  value,
  loading,
  handleChange,
  label,
  required,
  help = '',
  additionalStr,
  isOptionDisabled,
  withoutIcon = false,
  disableActiveValue,
  disabled,
  renderInput,
}) => {
  const wrapper = useRef<HTMLDivElement>(null)
  const inputNodeRef = useRef<HTMLInputElement>(null)

  const [isOpen, setIsOpen] = useState(false)
  const [activeValue, setActiveValue] = useState<IListItem | null>(null)
  const [inputValue, setInputValue] = useState<string>('')

  const closeSelect = () => {
    setIsOpen(false)
  }

  const toggleSelect = () => {
    if (disabled) return
    setIsOpen(prev => {
      const newState = !prev
      // Если список открывается, фокусируем инпут
      if (newState) {
        setTimeout(setFocusToInput, 0)
      }
      return newState
    })
  }

  const setNewActiveValue = (item: IListItem) => {
    setActiveValue(item)
    handleChange?.(item)
  }

  const handleSelectItemClick = (item: IListItem) => {
    setInputValue(item.title)
    setNewActiveValue(item)
    closeSelect()
    setTimeout(setFocusToInput, 0)
  }

  const setFocusToInput = () => {
    inputNodeRef.current?.focus()
    inputNodeRef.current?.select()
  }

  const handleInputChange = (inputValue: string) => {
    const item = list.find(l => l.title === inputValue || l.id === inputValue) || null
    setInputValue(inputValue)
    // Если нашли элемент, то устанавливаем его в качестве активного
    if (item) {
      setActiveValue(item)
    }
    // Если введено значение и список не открыт, то открываем
    if (inputValue !== '' && !isOpen) {
      setIsOpen(true)
    }
  }

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const relatedTarget = e.relatedTarget as HTMLElement

    if (relatedTarget !== inputNodeRef.current && relatedTarget !== null) {
      setIsOpen(false)
    }

    if (activeValue) {
      setNewActiveValue(activeValue)
    }

    if (activeValue?.title !== inputValue && activeValue?.id !== inputValue) {
      setIsOpen(false)
      setInputValue(activeValue?.title || value || '')
    }
  }

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) {
      closeSelect()
    }
  }, [])

  useEffect(() => {
    const item = list.find(l => l.id === value)
    setActiveValue(item || null)
    setInputValue(item?.title || '')
  }, [value, list])

  useEvent('click', handleOutsideClick, window)

  return (
    <div className={cx(className)}>
      {label && (
        <div className={cx('label')}>
          {label}
          {required && <span> *</span>}
          {help && <HelpIcon text={help} />}
        </div>
      )}
      <div
        ref={wrapper}
        className={cx('wrapper', { loading, disabled }, wrapperClassName)}
        onClick={toggleSelect}
      >
        {!renderInput && !handleChange && disabled && <div className={cx('input')}>{value}</div>}
        {renderInput?.({
          value: inputValue,
          ref: inputNodeRef,
          onChange: handleInputChange,
          onBlur: handleInputBlur,
          className: cx('input'),
        })}
        {!withoutIcon && (
          <IconWrapper
            size='20'
            color='gray80'
            direction={!isOpen ? 'right' : 'left'}
            className={cx('icon', { loading })}
          >
            {loading ? <LoadIcon /> : <ChevroneMediumIcon />}
          </IconWrapper>
        )}
        {isOpen && (
          <SelectList
            wrapperRef={wrapper}
            className={cx(listClassName)}
            list={list}
            handleChange={handleSelectItemClick}
            active={activeValue}
            additionalStr={additionalStr}
            isOptionDisabled={isOptionDisabled}
            disableActiveValue={disableActiveValue}
          />
        )}
      </div>
    </div>
  )
}
