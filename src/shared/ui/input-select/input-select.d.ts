import type { ReactNode } from 'react'
import type { IListItem } from '../select/select.d'

export declare namespace InputSelectProps {
  interface Own {
    className?: string
    wrapperClassName?: string
    listClassName?: string
    list: IListItem[]
    value?: string | null
    loading?: boolean
    handleChange?: (item: IListItem) => void
    label?: string
    required?: boolean
    help?: string
    additionalStr?: string
    isOptionDisabled?: (item: IListItem) => boolean
    withoutIcon?: boolean
    disableActiveValue?: boolean
    disabled?: boolean
    renderInput?: (inputProps: {
      ref: React.RefObject<HTMLInputElement>
      value: string
      onChange: (value: string) => void
      onBlur: (e: React.FocusEvent<HTMLInputElement>) => void
      className: string
    }) => ReactNode
  }

  type Props = Own
}
