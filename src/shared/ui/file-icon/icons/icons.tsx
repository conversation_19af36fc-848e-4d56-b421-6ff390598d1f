import React, { FC } from "react";
import { IconsProps, TFileIcons } from "./icons.d";

const PDFIcon: FC<IconsProps.Props> = (props) => {
  const { className } = props;

  return (
    <svg
      data-testid='PDF'
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M8 30H24C25.6569 30 27 28.6569 27 27V9.24264C27 8.44699 26.6839 7.68393 26.1213 7.12132L21.8787 2.87868C21.3161 2.31607 20.553 2 19.7574 2H8C6.34315 2 5 3.34315 5 5V27C5 28.6569 6.34315 30 8 30Z"
        fill="#C9CEDC"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19 2C19 2.00002 19 2.00004 19 2.00006V8.00006C19 9.10463 19.8954 10.0001 21 10.0001H27V9.24264C27 8.44699 26.6839 7.68393 26.1213 7.12132L21.8787 2.87868C21.3161 2.31607 20.553 2 19.7574 2H19Z"
        fill="#8E97AF"
      />
      <path
        d="M3.041 27.73V24.012H3.054C3.34 24.493 4.146 25.13 5.264 25.13C6.824 25.13 8.189 23.765 8.189 21.88C8.189 19.995 6.824 18.63 5.264 18.63C4.146 18.63 3.34 19.267 3.054 19.748H3.041V18.76H1.975V27.73H3.041ZM5.095 24.116C3.964 24.116 3.041 23.18 3.041 21.88C3.041 20.58 3.964 19.644 5.095 19.644C6.2 19.644 7.136 20.58 7.136 21.88C7.136 23.18 6.2 24.116 5.095 24.116ZM12.2408 25.13C13.3588 25.13 14.1648 24.493 14.4638 24.012H14.4768V25H15.5428V15.9H14.4768V19.748H14.4638C14.1648 19.267 13.3588 18.63 12.2408 18.63C10.6808 18.63 9.31584 19.995 9.31584 21.88C9.31584 23.765 10.6808 25.13 12.2408 25.13ZM12.4228 24.116C11.3178 24.116 10.3818 23.18 10.3818 21.88C10.3818 20.58 11.3178 19.644 12.4228 19.644C13.5408 19.644 14.4768 20.58 14.4768 21.88C14.4768 23.18 13.5408 24.116 12.4228 24.116ZM19.2421 25V19.774H20.7631V18.76H19.2421V17.811C19.2421 17.135 19.5931 16.784 20.1781 16.784C20.4771 16.784 20.7761 16.901 20.9321 16.979V15.965C20.7631 15.887 20.3991 15.77 19.9961 15.77C18.8521 15.77 18.1761 16.446 18.1761 17.72V18.76H16.9931V19.774H18.1761V25H19.2421Z"
        fill="#D13A30"
      />
    </svg>
  );
};

const DOCXIcon: FC<IconsProps.Props> = (props) => {
  const { className } = props;

  return (
    <svg
      data-testid='DOCX'
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M8 30H24C25.6569 30 27 28.6569 27 27V9.24264C27 8.44699 26.6839 7.68393 26.1213 7.12132L21.8787 2.87868C21.3161 2.31607 20.553 2 19.7574 2H8C6.34315 2 5 3.34315 5 5V27C5 28.6569 6.34315 30 8 30Z"
        fill="#C9CEDC"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19 2C19 2.00002 19 2.00004 19 2.00006V8.00006C19 9.10463 19.8954 10.0001 21 10.0001H27V9.24264C27 8.44699 26.6839 7.68393 26.1213 7.12132L21.8787 2.87868C21.3161 2.31607 20.553 2 19.7574 2H19Z"
        fill="#8E97AF"
      />
      <path
        d="M4.484 25.13C5.602 25.13 6.408 24.493 6.707 24.012H6.72V25H7.786V15.9H6.72V19.748H6.707C6.408 19.267 5.602 18.63 4.484 18.63C2.924 18.63 1.559 19.995 1.559 21.88C1.559 23.765 2.924 25.13 4.484 25.13ZM4.666 24.116C3.561 24.116 2.625 23.18 2.625 21.88C2.625 20.58 3.561 19.644 4.666 19.644C5.784 19.644 6.72 20.58 6.72 21.88C6.72 23.18 5.784 24.116 4.666 24.116ZM12.5903 25.13C14.3713 25.13 15.8013 23.7 15.8013 21.88C15.8013 20.06 14.3713 18.63 12.5903 18.63C10.8093 18.63 9.37931 20.06 9.37931 21.88C9.37931 23.7 10.8093 25.13 12.5903 25.13ZM12.5903 24.116C11.3943 24.116 10.4453 23.154 10.4453 21.88C10.4453 20.606 11.3943 19.644 12.5903 19.644C13.7863 19.644 14.7353 20.606 14.7353 21.88C14.7353 23.154 13.7863 24.116 12.5903 24.116ZM20.1053 25.13C21.3793 25.13 22.3023 24.415 22.7443 23.843L21.9643 23.193C21.6133 23.622 20.9893 24.116 20.1053 24.116C18.9353 24.116 17.9863 23.154 17.9863 21.88C17.9863 20.606 18.9353 19.644 20.1053 19.644C20.9893 19.644 21.5873 20.138 21.9253 20.541L22.6923 19.891C22.2763 19.345 21.3793 18.63 20.1053 18.63C18.3503 18.63 16.9203 20.06 16.9203 21.88C16.9203 23.7 18.3503 25.13 20.1053 25.13ZM24.5347 25L26.2767 22.478H26.3027L28.0447 25H29.2927L26.9787 21.75L29.0977 18.76H27.8887L26.3417 21.022H26.3157L24.7687 18.76H23.5077L25.6397 21.75L23.3127 25H24.5347Z"
        fill="#2E64DA"
      />
    </svg>
  );
};

const XLSXIcon: FC<IconsProps.Props> = (props) => {
  const { className } = props;

  return (
    <svg
      data-testid='XLSX'
      width="32"
      height="32"
      viewBox="0 0 32 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M8 30H24C25.6569 30 27 28.6569 27 27V9.24264C27 8.44699 26.6839 7.68393 26.1213 7.12132L21.8787 2.87868C21.3161 2.31607 20.553 2 19.7574 2H8C6.34315 2 5 3.34315 5 5V27C5 28.6569 6.34315 30 8 30Z"
        fill="#C9CEDC"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M19 2C19 2.00002 19 2.00004 19 2.00006V8.00006C19 9.10463 19.8954 10.0001 21 10.0001H27V9.24264C27 8.44699 26.6839 7.68393 26.1213 7.12132L21.8787 2.87868C21.3161 2.31607 20.553 2 19.7574 2H19Z"
        fill="#8E97AF"
      />
      <path
        d="M2.521 25L4.263 22.478H4.289L6.031 25H7.279L4.965 21.75L7.084 18.76H5.875L4.328 21.022H4.302L2.755 18.76H1.494L3.626 21.75L1.299 25H2.521ZM9.69487 25V15.9H8.62887V25H9.69487ZM13.6233 25.13C15.0663 25.13 15.9633 24.246 15.9633 23.206C15.9633 21.009 12.5573 21.594 12.5573 20.411C12.5573 19.995 12.9473 19.605 13.6883 19.605C14.3383 19.605 14.8323 19.93 15.1053 20.19L15.8073 19.501C15.4433 19.124 14.7283 18.63 13.6883 18.63C12.3363 18.63 11.4913 19.475 11.4913 20.411C11.4913 22.569 14.8973 21.984 14.8973 23.206C14.8973 23.726 14.4553 24.155 13.6233 24.155C12.8823 24.155 12.2453 23.674 11.9333 23.31L11.2313 23.999C11.6083 24.493 12.4793 25.13 13.6233 25.13ZM18.0474 25L19.7894 22.478H19.8154L21.5574 25H22.8054L20.4914 21.75L22.6104 18.76H21.4014L19.8544 21.022H19.8284L18.2814 18.76H17.0204L19.1524 21.75L16.8254 25H18.0474Z"
        fill="#1F7244"
      />
    </svg>
  );
};

export const FileIconsList: Record<
  TFileIcons,
  (args: IconsProps.Props) => React.ReactElement
> = {
  PDF: (args) => <PDFIcon {...args} />,
  DOCX: (args) => <DOCXIcon {...args} />,
  XLSX: (args) => <XLSXIcon {...args} />,
};
