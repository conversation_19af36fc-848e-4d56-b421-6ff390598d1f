import { FC } from 'react'
import styles from './file-icon.module.scss'
import classNamesBind from 'classnames/bind'
import { FileIconProps } from './file-icon.d'
import { FileIconsList } from './icons/icons'

const cx = classNamesBind.bind(styles)

export const FileIcon: FC<FileIconProps.Props> = props => {
  const { className, type, size = '32', 'data-testid': dataTestid = 'FileIcon' } = props

  return (
    <div className={cx('wrapper', `size--${size}`, className)} data-testid={dataTestid}>
      {FileIconsList[type]({
        className,
      })}
    </div>
  )
}
