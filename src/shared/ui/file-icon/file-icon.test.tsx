import '@testing-library/jest-dom'
import { FileIcon } from './file-icon'
import { renderTestScreen } from '@/shared/helpers/tests/render'
import { fileIcons } from './icons/icons.d'

describe('[shared] [ui] FileIcon', () => {
    for (let i = 0; i < fileIcons.length; i++) {
        it(`Должен отрендерить иконку = ${fileIcons[i]}`, () => {
            const { getByTestId } = renderTestScreen(
                <FileIcon type={fileIcons[i]} />
            )

            const icon = getByTestId('FileIcon')
            const iconSVG = getByTestId((fileIcons[i]))

            expect(icon).toBeInTheDocument()
            expect(iconSVG).toBeInTheDocument()
        })
    }
})
