import type { Meta, StoryObj } from '@storybook/react'
import { FileIcon } from './file-icon'
import { fileIcons } from './icons/icons.d'
import { fileIconSize } from './file-icon.d'

const meta: Meta<typeof FileIcon> = {
    title: 'UI/FileIcon',
    component: FileIcon,
    tags: ['autodocs'],
    argTypes: {
        type: {
            options: fileIcons,
            description: 'Название иконки',
            control: 'select',
            table: {
                type: { summary: 'TFileIcons' },
            }
        },
        size: {
            options: fileIconSize,
            description: 'Размер иконки',
            control: 'select',
            table: {
                type: { summary: 'IFileIconSize' },
                defaultValue: { summary: '32' },
            }
        },
    }
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = { args: { type: 'PDF' } }

export const Sizes: Story = {
    decorators: () => {
        return <div style={{
            display: 'flex',
            gap: '10px',
            alignItems: 'start'
        }}>
            {fileIconSize.map(size => fileIcons.map(type => <FileIcon type={type} size={size} />))}
        </div>
    }
}