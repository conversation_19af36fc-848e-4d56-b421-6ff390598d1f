import { UseFormRegisterReturn, TFieldName } from 'react-hook-form'

export declare namespace TextareaProps {
  interface Own
    extends React.DetailedHTMLProps<
      React.TextareaHTMLAttributes<HTMLTextAreaElement>,
      HTMLTextAreaElement
    > {
    className?: string
    classNameWrapper?: string
    placeholder?: string
    resize?: boolean
    value?: string
    name?: string
    error?: string
    label?: string
    view?: 'bordered'
    onChange?: (value: string, name: string, e) => void
    fullWidth?: boolean
    required?: boolean
    register?: UseFormRegisterReturn<TFieldName>
  }

  type Props = Own
}

export {}
