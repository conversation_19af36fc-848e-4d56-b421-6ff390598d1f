import React, { FC, useState } from "react";
import styles from "./text-area.module.scss";
import classNamesBind from "classnames/bind";
import { TextareaProps } from "./text-area.d";

const cx = classNamesBind.bind(styles);

export const Textarea: FC<TextareaProps.Props> = (props) => {
  const {
    classNameWrapper,
    className,
    disabled,
    placeholder,
    resize = true,
    value,
    name,
    error,
    onChange,
    onBlur,
    fullWidth = true,
    required,
    label,
    view,
    register,
    ...otherProps
  } = props;

  const autoResize = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    e.target.style.height = `${e.target.scrollHeight + 2}px`;
  };

  const _onChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (disabled) return;

    if (resize) autoResize(e);

    if (!onChange) return register?.onBlur(e);

    return onChange(e.target.value, name || "", e);
  };

  const _onBlur = (e: React.FocusEvent<HTMLTextAreaElement>) => {
    if (!(disabled || !onBlur)) {
      onBlur && onBlur(e);
    }
    register?.onBlur(e);
  };

  const [readonly, setReadonly] = useState(true);

  return (
    <div className={cx("inputWrapper", classNameWrapper)}>
      {label && (
        <div className={cx("label")}>
          {label} {required && <span>*</span>}
        </div>
      )}
      <textarea
        className={cx("input", view, className, {
          fullWidth,
          error,
          disabled,
        })}
        name={name}
        disabled={disabled}
        placeholder={placeholder}
        required={required}
        value={value}
        {...otherProps}
        {...register}
        onBlur={_onBlur}
        onChange={_onChange}
        autoComplete="off"
        readOnly={readonly}
        onFocus={() => setReadonly(false)}
      />
      {error && <div className={cx("error-text", "errorText")}>{error}</div>}
    </div>
  );
};
