.input {
  min-height: 144px;
  resize: none;
  appearance: none;
  outline: none;
  box-sizing: border-box;

  background: #ffffff;
  border: 1px solid #ebeff2;
  transition: ease 0.15s, margin 0s;
  border-radius: 12px;

  padding: 13px 14px;
  max-width: 100%;
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-2-medium);

  -webkit-box-shadow: 0px 0px 0px 2px transparent, 0 0 0 50px transparent inset !important;
  box-shadow: 0px 0px 0px 2px transparent, 0 0 0 50px transparent inset !important;

  &:-webkit-autofill {
    -webkit-box-shadow: 0px 0px 0px 2px transparent, 0 0 0 50px white inset !important;
    box-shadow: 0px 0px 0px 2px transparent, 0 0 0 50px white inset !important;
    transition: ease 0.15s, margin 0s;
  }

  &::-webkit-input-placeholder,
  &:-moz-placeholder,
  &::-moz-placeholder,
  &:-ms-input-placeholder,
  &::placeholder {
    font: var(--font-text-2-normal);
    color: var(--color-gray-70, #8e97af);
  }

  &.fullWidth {
    width: 100%;
  }

  &:hover {
    border: 1px solid #c9cedc;
    transition: ease 0.15s, margin 0s;
  }

  &:focus,
  &:-webkit-autofill:focus {
    -webkit-box-shadow: 0px 0px 0px 2px var(--color-primary),
      0 0 0 50px white inset !important;
    box-shadow: 0px 0px 0px 2px var(--color-primary), 0 0 0 50px white inset !important;
    border: 1px solid #fff;
    transition: ease 0.15s, margin 0s;

    caret-color: var(--color-primary);
  }

  &.error,
  &.error:-webkit-autofill {
    -webkit-box-shadow: 0px 0px 0px 2px #ff586b, 0 0 0 50px white inset !important;
    box-shadow: 0px 0px 0px 2px #ff586b, 0 0 0 50px white inset !important;
    border: 1px solid #fff;
    transition: ease 0.15s, margin 0s;
  }

  &.disabled {
    background: #ffffff;
    border: 1px solid #ebeff2;

    font-family: "TT Norms Pro";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: #8e97af;
  }

  &.domain {
    padding-right: 100px;
  }

  &.bordered {
    border: 1px solid var(--color-primary);
  }
}

.inputWrapper {
  position: relative;

  .label {
    color: var(--color-gray-90, #343b54);
    font: var(--font-text-2-medium);
    margin-bottom: 8px;
    span {
      color: var(--color-statistics-bad, #ff8577);
    }
  }
  .domainName {
    font-family: "TT Norms Pro";
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    line-height: 18px;
    color: #5c6585;
    position: absolute;
    right: 16px;
    top: 50%;
    transform: translateY(-50%);
    user-select: none;
    background: #fff;
  }

  .errorText {
    margin-top: 12px;
  }
}
