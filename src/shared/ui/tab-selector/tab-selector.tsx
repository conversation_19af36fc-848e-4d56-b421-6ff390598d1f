import { FC } from 'react'
import styles from './tab-selector.module.scss'
import classNamesBind from 'classnames/bind'
import { ITabSelector, TabSelectorProps } from './tab-selector.d'
import { HelpIcon } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

export const TabSelector: FC<TabSelectorProps.Props> = props => {
  const {
    className,
    label,
    items,
    itemClassName,
    wrapperClass,
    active,
    onChange,
    help,
    'data-testid': dataTestid = '',
  } = props

  if (items.length === 0) return null

  const handleClick = (item: ITabSelector) => onChange(item)

  const getWidth = () => {
    if (!items?.length) return 0

    return 100 / items.length
  }

  const getOffset = () => {
    if (!items.length) return 0

    const index = items.map(s => s.name).indexOf(active)

    return Math.max(index, 0) * getWidth()
  }

  const style = {
    left: `${getOffset()}%`,
    width: `${getWidth()}%`,
  }

  return (
    <div className={cx('wrapper', className)} data-testid={`${dataTestid}.TabSelector`}>
      {label && (
        <div className={cx('label')}>
          <div data-testid={`${dataTestid}.TabLabel`}>{label}</div>
          {help && <HelpIcon text={help} />}
        </div>
      )}
      <div className={cx('inner', wrapperClass)}>
        <div className={cx('highlightWrapper')} style={style}>
          <span />
        </div>

        {items.map(item => {
          return (
            <div
              key={`tab-${item.text}-${item.name}`}
              className={cx('item', itemClassName, {
                active: item.name === active,
              })}
              onClick={() => handleClick(item)}
              data-testid={`${dataTestid}.Tab.${item.name}`}
            >
              {item.text}
            </div>
          )
        })}
      </div>
    </div>
  )
}
