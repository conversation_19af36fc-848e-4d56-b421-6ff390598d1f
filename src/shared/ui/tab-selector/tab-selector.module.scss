.label {
  align-items: center;

  color: var(--color-gray-90, #343b54);
  display: flex;
  font: var(--font-text-2-medium);
  gap: 8px;

  span {
    color: var(--color-statistics-bad, #ff8577);
  }
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;

  height: 100%;
}

.inner {
  background: var(--color-gray-50, #e1e4eb);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 12px;
  box-shadow: var(--shadow-border);

  display: flex;
  gap: 4px;
  padding: 4px;
  position: relative;
  width: 100%;
  align-items: stretch;
}

.item {
  background: transparent;
  border-radius: 8px;
  color: var(--color-gray-90, #343b54);
  cursor: pointer;
  font: var(--font-text-2-normal);
  padding: 9px 16px;
  text-align: center;
  z-index: 1;
  transition: var(--transition);

  height: 100%;
  min-height: 100%;
  width: 100%;

  &:hover {
    background: var(--color-gray-60, #c9cedc);

    transition: var(--transition);
  }
  &.active {
    &:hover {
      background: transparent;

      transition: var(--transition);
    }
    // 	background: var(--color-surface, #fff);
    // 	transition: var(--transition);
  }
}

.highlightWrapper {
  height: 100%;
  left: 0;
  padding: 4px;

  pointer-events: none;
  position: absolute;
  top: 0;

  transition: 300ms ease;
  span {
    background: var(--color-surface);
    border-radius: 8px;
    display: block;
    height: 100%;
    width: 100%;
  }
}
