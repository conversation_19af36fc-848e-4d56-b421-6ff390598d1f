// import { TabSelector } from './tab-selector'
// import { renderTestScreen } from '@/shared/helpers/tests/render'
// import userEvent from '@testing-library/user-event'
// import '@testing-library/jest-dom'
// import { ITabSelector } from './tab-selector'

// const mockedUsedNavigate = jest.fn()

// jest.mock('react-router-dom', () => ({
//   ...jest.requireActual('react-router-dom'),
//   useNavigate: () => mockedUsedNavigate,
// }))

// describe('TabSelector', () => {
// //   it('Табы должны отрендериться', async () => {
// //     const items = [
// //       {
// //         name: 'first_tab',
// //         text: 'Первый таб',
// //       },
// //       {
// //         name: 'second_tab',
// //         text: 'Второй таб',
// //       },
// //     ]
// //     const { getByTestId } = renderTestScreen(
// //       <TabSelector active={'first_tab'} onChange={() => null} items={items} />,
// //     )

// //     for (let i = 0; i < items.length; i++) {
// //       expect(getByTestId(`.Tab.${items[i].name}`).textContent).toEqual(items[i].text)
// //     }
// //   })
// //   it('Должен срабатывать onChange, с ожидаемым аргументом', async () => {
// //     const mockedOnClick = jest.fn()

// //     const items = [
// //       {
// //         name: 'first_tab',
// //         text: 'Первый таб',
// //       },
// //       {
// //         name: 'second_tab',
// //         text: 'Второй таб',
// //       },
// //     ]

// //     const handleChange = (item: ITabSelector) => {
// //       mockedOnClick(item)
// //     }

// //     const { getByTestId } = renderTestScreen(
// //       <TabSelector active={'first_tab'} onChange={handleChange} items={items} />,
// //     )

// //     const button = getByTestId('.Tab.first_tab')

// //     await userEvent.click(button)

// //     expect(mockedOnClick).toHaveBeenCalledWith({
// //       name: 'first_tab',
// //       text: 'Первый таб',
// //     })
// //     expect(mockedOnClick).toHaveBeenCalled()
// //   })
// //   it('Label и help должны отрендериться', async () => {
// //     const items = [
// //       {
// //         name: 'first_tab',
// //         text: 'Первый таб',
// //       },
// //       {
// //         name: 'second_tab',
// //         text: 'Второй таб',
// //       },
// //     ]
// //     const { getByTestId } = renderTestScreen(
// //       <TabSelector
// //         label='Таб лейбл'
// //         help='Таб хэлп'
// //         active={'first_tab'}
// //         onChange={() => null}
// //         items={items}
// //       />,
// //     )

// //     const label = getByTestId('.TabLabel')
// //     const helpIcon = getByTestId('.HelpIcon.Icon')
// //     const helpText = getByTestId('.HelpIcon.Text')

// //     expect(label.textContent).toEqual('Таб лейбл')
// //     expect(helpIcon).toBeInTheDocument()
// //     expect(helpText.textContent).toEqual('Таб хэлп')
// //   })

// //   it('Если передаем пустой массив items - должен возвращать null', async () => {
// //     const { queryByTestId } = renderTestScreen(
// //       <TabSelector items={[]} active='' onChange={() => null} />,
// //     )

// //     expect(queryByTestId(`.TabSelector`)).toBeNull()
// //   })
// })
