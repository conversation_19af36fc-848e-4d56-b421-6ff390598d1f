// import type { <PERSON>a, StoryObj } from '@storybook/react'

// import { TabSelector } from './tab-selector'
// import { useArgs } from '@storybook/preview-api'
// import { ITabSelector } from './tab-selector.d'

// const meta = {
//   title: 'UI/TabSelector',
//   component: TabSelector,
// } satisfies Meta<typeof TabSelector>

// export default meta
// type Story = StoryObj<typeof meta>

// export const Default: Story = {
//   args: {
//     items: [
//       {
//         name: 'first_tab',
//         text: 'Первый таб',
//       },
//       {
//         name: 'second_tab',
//         text: 'Второй таб',
//       },
//     ],
//     active: 'first_tab',
//     onChange: () => null,
//   },
//   decorators: (_Story, props) => {
//     const [_, updateArgs] = useArgs()

//     const handle = (item: ITabSelector) => {
//       updateArgs({ ...props.args, active: item.name })
//     }

//     return <TabSelector {...props.args} onChange={handle} />
//   },
// }
