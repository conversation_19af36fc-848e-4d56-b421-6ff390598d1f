import { FC, Fragment } from 'react'
import styles from './pagination.module.scss'
import classNamesBind from 'classnames/bind'
import { PaginationProps } from './pagination.d'
import { ButtonIcon } from '@/shared/ui'
import { getPages } from '@/shared/helpers'

const cx = classNamesBind.bind(styles)

export const Pagination: FC<PaginationProps.Props> = props => {
  const { className, limit, total, onChange, currentPage, withEmptySpace = false } = props

  const pageWithAddition = currentPage + 1
  const totalPage = Math.ceil(total / limit)
  const pages = getPages(pageWithAddition, totalPage)

  const onPageChange = (p: number) => onChange(p - 1)

  if (pages.length < 2) {
    return withEmptySpace ? <div className={cx('empty')}></div> : <></>
  }

  return (
    <div className={cx('wrapper', className)}>
      {pageWithAddition !== 1 && (
        <ButtonIcon
          icon='chevroneMedium'
          direction='down'
          onClick={() => onPageChange(pageWithAddition - 1)}
        />
      )}
      <div className={cx('numbers')}>
        {pages.map((pageNumber, i) => {
          const hasNextPage = pages[i + 1] && pages[i + 1] - pageNumber > 1
          const isActivePage = pageWithAddition === pageNumber

          return (
            <Fragment key={`page-${pageNumber}-${totalPage}`}>
              <div
                className={cx('number', { active: isActivePage })}
                onClick={() => !isActivePage && onPageChange(pageNumber)}
              >
                {pageNumber}
              </div>
              {hasNextPage && (
                <div className={cx('number')} key={`page-${pageNumber}-${totalPage}-dots`}>
                  ...
                </div>
              )}
            </Fragment>
          )
        })}
      </div>
      {pageWithAddition !== totalPage && (
        <ButtonIcon icon='chevroneMedium' onClick={() => onPageChange(pageWithAddition + 1)} />
      )}
    </div>
  )
}
