.wrapper {
  align-items: center;

  display: flex;
  gap: 4px;
  justify-content: end;
  margin-top: 20px;
}

.numbers {
  align-items: center;
  display: flex;
  gap: 4px;

  &.disabled {
    cursor: default;
    pointer-events: none;

    .number {
      color: var(--color-gray-60);

      &.active {
        background: var(--color-gray-50);
        color: var(--button-disabled-text-color);
      }
    }
  }
}

.number {
  align-items: center;

  background: transparent;
  border-radius: 8px;

  color: var(--color-gray-80, #5c6585);
  cursor: pointer;

  display: flex;

  font: var(--font-text-1-medium);
  justify-content: center;
  min-height: 25px;
  min-width: 25px;
  padding: 7px 8px 8px;

  transition: var(--transition);

  &:hover {
    background: var(--color-gray-50, #e1e4eb);

    transition: var(--transition);
  }
  &.active {
    background: var(--color-primary-90);
    color: var(--color-surface, #fff);
    cursor: default;

    transition: var(--transition);
  }
}

.empty {
  height: 35px;
  margin-top: 20px;
}
