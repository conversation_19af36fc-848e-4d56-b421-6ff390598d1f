import type { Meta, StoryObj } from '@storybook/react'
import { ButtonCard } from './button-card'
import { icons } from '../Icon/icons/icons.d'

const meta = {
    title: 'UI/ButtonCard',
    component: ButtonCard,
    tags: ['autodocs'],
    argTypes: {
        fullWidth: {
            control: 'boolean',
            description: 'Должна ли растягиваться карточка на всю ширину',
            table: {
                type: { summary: 'boolean' },
                defaultValue: { summary: 'false' },
            }
        },
        loading: {
            control: 'boolean',
            description: 'Происходит ли загрузка',
            table: {
                type: { summary: 'boolean' },
                defaultValue: { summary: 'false' },
            }
        },
        icon: {
            options: icons,
            description: 'Название иконки',
            control: 'select',
            table: {
                type: { summary: 'TIcons' },
                defaultValue: { summary: 'plus' },
            }
        },
    }
} satisfies Meta<typeof ButtonCard>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: { children: 'Нажми меня' }
}

export const ScreenshotTest: Story = {
    args: { children: 'Нажми меня' },
    decorators: () => {
        return <div style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '10px',
            alignItems: 'start'
        }}>
            <ButtonCard>Нажми меня</ButtonCard>
            <ButtonCard loading>Нажми меня</ButtonCard>
            <ButtonCard disabled>Нажми меня</ButtonCard>
            <ButtonCard loading disabled>Нажми меня</ButtonCard>

            <ButtonCard fullWidth>Нажми меня</ButtonCard>
            <ButtonCard fullWidth loading>Нажми меня</ButtonCard>
            <ButtonCard fullWidth disabled>Нажми меня</ButtonCard>
            <ButtonCard fullWidth loading disabled>Нажми меня</ButtonCard>

            <ButtonCard icon="box" fullWidth>Нажми меня</ButtonCard>
            <ButtonCard icon="box" fullWidth loading>Нажми меня</ButtonCard>
            <ButtonCard icon="box" fullWidth disabled>Нажми меня</ButtonCard>
            <ButtonCard icon="box" fullWidth loading disabled>Нажми меня</ButtonCard>
        </div>
    }
}