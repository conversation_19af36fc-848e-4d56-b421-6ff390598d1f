.button {

  --button-color: var(--color-surface);
  --button-hover-color: var(--color-gray-20);
  --button-focus-color: var(--color-gray-20);
  // --button-loading-color: var(--color-primary-90);
  --button-disabled-color: var(--color-gray-20);

  --button-text-color: var(--color-primary);
  --button-hover-text-color: var(--color-primary);
  --button-focus-text-color: var(--color-primary);
  --button-disabled-text-color: var(--color-gray-60);
  align-items: center;

  appearance: none;

  background: var(--button-color);

  border: 1px solid var(--color-gray-30);
  border: 2px solid var(--button-color);
  border-radius: 12px;

  color: #fff;
  color: var(--button-text-color);

  display: flex;

  font-family: "TT Norms Pro";
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  justify-content: space-between;
  line-height: 18px;
  outline: none;
  padding: 14px;

  transition: var(--transition);

  &:hover {

    background: var(--button-hover-color);
    border: 2px solid var(--button-hover-color);
    color: var(--button-hover-text-color);
  }

  &:focus {

    background: var(--button-focus-color);
    border: 2px solid var(--button-focus-color);
    color: var(--button-focus-text-color);
  }

  &.disabled {

    background: var(--button-disabled-color);
    border: 2px solid var(--button-disabled-color);
    color: var(--button-disabled-text-color);
  }

  &.loading {

    color: transparent !important;
    position: relative;
    span {

      animation: linear loading infinite 0.9s 0.3s;

      background: rgba(255, 255, 255, 1);
      border-radius: 50%;

      display: block;
      height: 4px;
      left: 50%;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 4px;
      &::after,
      &::before {

        background: #fff;
        border-radius: 50%;

        content: "";

        display: block;
        height: 4px;
        position: absolute;
        width: 4px;
      }
      &::after {

        animation: linear loading infinite 0.9s 0s;
        left: -8px;
      }
      &::before {

        animation: linear loading infinite 0.9s 0.6s;
        right: -8px;
      }
    }
  }

  &.withoutBackground {
    background: transparent !important;
  }

  &.fullWidth {
    width: 100%;
  }
}
