import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import { ButtonCard } from './button-card'
import { renderTestScreen } from '@/shared/helpers/tests/render'

describe('[shared] [ui] ButtonCard', () => {
  it('Должен отрендериться с нужным текстом', () => {
    const { getByTestId } = renderTestScreen(<ButtonCard>Нажми меня, пажаласта</ButtonCard>)

    const button = getByTestId('ButtonCard')

    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Нажми меня, пажаласта')
  })

  it('Должен занимать всю ширину при fullWidth', () => {
    const { getByTestId } = renderTestScreen(
      <ButtonCard fullWidth>Нажми меня, ну пажаласта</ButtonCard>,
    )

    const button = getByTestId('ButtonCard')

    expect(button).toHaveClass('fullWidth')
  })

  it('Должен отображать состояние загрузки', () => {
    const { getByTestId } = renderTestScreen(<ButtonCard loading>Может нажмешь уже?</ButtonCard>)

    const button = getByTestId('ButtonCard')
    const loader = getByTestId('ButtonCard.Loader')

    expect(loader).toBeInTheDocument()
    expect(button).toHaveClass('loading')
    expect(button).toBeDisabled()
  })

  it('Должен быть отключен, если передан disabled', () => {
    const { getByTestId } = renderTestScreen(<ButtonCard disabled>А теперь не сможешь</ButtonCard>)

    const button = getByTestId('ButtonCard')

    expect(button).toBeDisabled()
  })

  it('Должен обрабатывать onClick событие', async () => {
    const mockedOnClick = jest.fn()

    const { getByTestId } = renderTestScreen(
      <ButtonCard onClick={mockedOnClick}>Нажми меня</ButtonCard>,
    )

    const button = getByTestId('ButtonCard')

    await userEvent.click(button)

    expect(mockedOnClick).toHaveBeenCalledTimes(1)
  })
})
