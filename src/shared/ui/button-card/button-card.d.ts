import { TIcons } from '../Icon/icons/icons.d'

export declare namespace ButtonCardProps {
  interface Own
    extends React.DetailedHTMLProps<
      React.ButtonHTMLAttributes<HTMLButtonElement>,
      HTMLButtonElement
    > {
    children: React.ReactNode
    className?: string
    fullWidth?: boolean
    loading?: boolean
    disabled?: boolean

    icon?: TIcons
  }

  type Props = Own & DataTest
}

export {}
