import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Ellipsis: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Ellipsis'
      className={className}
      width='14'
      height='14'
      viewBox='0 0 14 14'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7 0C7.82843 0 8.5 0.671573 8.5 1.5C8.5 2.32843 7.82843 3 7 3C6.17157 3 5.5 2.32843 5.5 1.5C5.5 0.671573 6.17157 0 7 0ZM7 5.5C7.82843 5.5 8.5 6.17157 8.5 7C8.5 7.82843 7.82843 8.5 7 8.5C6.17157 8.5 5.5 7.82843 5.5 7C5.5 6.17157 6.17157 5.5 7 5.5ZM7 11C7.82843 11 8.5 11.6716 8.5 12.5C8.5 13.3284 7.82843 14 7 14C6.17157 14 5.5 13.3284 5.5 12.5C5.5 11.6716 6.17157 11 7 11Z'
        fill='#8E97AF'
      />
    </svg>
  )
}

export default Ellipsis