import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ImageIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Image'
      className={className}
      xmlns='http://www.w3.org/2000/svg'
      width='48'
      height='48'
      viewBox='0 0 48 48'
      fill='none'
    >
      <path
        d='M30.9968 14.5C29.6161 14.5 28.4968 15.6193 28.4968 17C28.4968 18.3807 29.6161 19.5 30.9968 19.5C32.3775 19.5 33.4968 18.3807 33.4968 17C33.4968 15.6193 32.3775 14.5 30.9968 14.5ZM6 10.75C6 8.12665 8.12665 6 10.75 6H37.25C39.8734 6 42 8.12665 42 10.75V37.25C42 38.1078 41.7726 38.9125 41.3748 39.6071L26.6304 25.0849C25.1709 23.6485 22.8289 23.6486 21.3695 25.0851L6.62556 39.6078C6.22753 38.913 6 38.1081 6 37.25V10.75ZM25.9968 17C25.9968 19.7614 28.2354 22 30.9968 22C33.7582 22 35.9968 19.7614 35.9968 17C35.9968 14.2386 33.7582 12 30.9968 12C28.2354 12 25.9968 14.2386 25.9968 17ZM8.39363 41.3753C9.08807 41.7728 9.8925 42 10.75 42H37.25C38.1078 42 38.9124 41.7726 39.607 41.3749L24.8769 26.8668C24.3903 26.388 23.6097 26.388 23.1232 26.8668L8.39363 41.3753Z'
        fill='#C9CEDC'
      />
    </svg>
  )
}

export default ImageIcon