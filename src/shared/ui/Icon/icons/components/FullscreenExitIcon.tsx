import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FullscreenExitIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='FullscreenExit'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
    >
      <g id='exit-fullscreen' stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
        <rect id='Rectangle' x='0' y='0' width='28' height='28'></rect>
        <path
          d='M5.88337887,15.0067277 L6,15 L12,15 L12.0752385,15.0027879 L12.0752385,15.0027879 L12.2007258,15.0202401 L12.2007258,15.0202401 L12.3121425,15.0497381 L12.3121425,15.0497381 L12.4232215,15.0936734 L12.4232215,15.0936734 L12.5207088,15.1459955 L12.5207088,15.1459955 L12.6170223,15.2129277 L12.6170223,15.2129277 L12.7071408,15.2929109 L12.7071408,15.2929109 C12.7425008,15.3282873 12.774687,15.3656744 12.8036654,15.4046934 L12.8753288,15.5159379 L12.8753288,15.5159379 L12.9287745,15.628664 L12.9287745,15.628664 L12.9641549,15.734007 L12.9641549,15.734007 L12.9932723,15.8833789 L12.9932723,15.8833789 L13,16 L13,22 C13,22.5522847 12.5522847,23 12,23 C11.4871642,23 11.0644928,22.6139598 11.0067277,22.1166211 L11,22 L11,18.414 L5.70710678,23.7071068 C5.31658249,24.0976311 4.68341751,24.0976311 4.29289322,23.7071068 C3.93240926,23.3466228 3.90467972,22.7793918 4.20970461,22.3871006 L4.29289322,22.2928932 L9.584,17 L6,17 C5.48716416,17 5.06449284,16.6139598 5.00672773,16.1166211 L5,16 C5,15.4871642 5.38604019,15.0644928 5.88337887,15.0067277 Z M23.7071068,4.29289322 C24.0675907,4.65337718 24.0953203,5.22060824 23.7902954,5.61289944 L23.7071068,5.70710678 L18.414,11 L22,11 C22.5128358,11 22.9355072,11.3860402 22.9932723,11.8833789 L23,12 C23,12.5128358 22.6139598,12.9355072 22.1166211,12.9932723 L22,13 L15.9247615,12.9972121 L15.9247615,12.9972121 L15.7992742,12.9797599 L15.7992742,12.9797599 L15.6878575,12.9502619 L15.6878575,12.9502619 L15.5767785,12.9063266 L15.5767785,12.9063266 L15.4792912,12.8540045 L15.4792912,12.8540045 L15.3829777,12.7870723 L15.3829777,12.7870723 L15.2928592,12.7070891 L15.2928592,12.7070891 C15.2574992,12.6717127 15.225313,12.6343256 15.1963346,12.5953066 L15.1246712,12.4840621 L15.1246712,12.4840621 L15.0712255,12.371336 L15.0712255,12.371336 L15.0358451,12.265993 L15.0358451,12.265993 L15.0067277,12.1166211 L15.0067277,12.1166211 L15,12 L15,6 C15,5.44771525 15.4477153,5 16,5 C16.5128358,5 16.9355072,5.38604019 16.9932723,5.88337887 L17,6 L17,9.584 L22.2928932,4.29289322 C22.6834175,3.90236893 23.3165825,3.90236893 23.7071068,4.29289322 Z'
          id='Shape'
          fill='#000000'
          fillRule='nonzero'
          transform='translate(14.000000, 14.000000) scale(-1, -1) translate(-14.000000, -14.000000) '
        ></path>
      </g>
    </svg>
  )
}

export default FullscreenExitIcon