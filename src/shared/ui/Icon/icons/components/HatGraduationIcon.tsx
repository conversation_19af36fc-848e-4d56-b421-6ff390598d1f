import { FC } from 'react'
import { IconsProps } from '../icons.d'

const HatGraduationIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='HatGraduation'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M8.5063 3.40098C9.4313 2.86992 10.5687 2.86992 11.4937 3.40098L18.749 7.5664C18.9042 7.65555 19 7.82095 19 8.00002C19 8.17908 18.9042 8.34448 18.7489 8.43363L16 10.0118V14.5C16 14.6326 15.9473 14.7598 15.8536 14.8535L15.852 14.8551L15.8496 14.8574L15.8428 14.8642L15.8201 14.8859C15.801 14.9039 15.7741 14.9288 15.7394 14.9596C15.6701 15.0213 15.5696 15.1067 15.4389 15.2078C15.1777 15.41 14.7948 15.6761 14.2978 15.9412C13.3033 16.4716 11.8479 17 10 17C8.15211 17 6.69675 16.4716 5.70221 15.9412C5.20518 15.6761 4.82226 15.41 4.5611 15.2078C4.43043 15.1067 4.32994 15.0213 4.26059 14.9596C4.22591 14.9288 4.19898 14.9039 4.17992 14.8859C4.07226 14.7819 4 14.6537 4 14.5V10.0118L2 8.86361L2 13.5C2 13.7761 1.77614 14 1.5 14C1.22386 14 1 13.7761 1 13.5V8.00002C1 7.81064 1.10529 7.64582 1.26052 7.56096L8.5063 3.40098ZM11.4937 12.599C10.5687 13.13 9.43131 13.13 8.50632 12.599L5 10.5859V14.2772C5.04686 14.3168 5.10469 14.364 5.17327 14.4171C5.39649 14.5899 5.73232 14.8239 6.17279 15.0588C7.05325 15.5284 8.34789 16 10 16C11.6521 16 12.9467 15.5284 13.8272 15.0588C14.2677 14.8239 14.6035 14.5899 14.8267 14.4171C14.8953 14.364 14.9531 14.3168 15 14.2772V10.5859L11.4937 12.599ZM10.9958 4.26822C10.3791 3.91418 9.62086 3.91418 9.0042 4.26822L2.50423 8.00001L9.00421 11.7317C9.62087 12.0858 10.3791 12.0858 10.9958 11.7317L17.4958 8.00001L10.9958 4.26822Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default HatGraduationIcon