import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ModulesIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Modules'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M18 6L10.6857 3.07428C10.5665 3.0266 10.4335 3.0266 10.3143 3.07428L3 6M18 6L10.5 9M18 6V9M10.5 9L3 6M10.5 9V12M3 6V14.6615C3 14.8659 3.12448 15.0498 3.3143 15.1257L10.5 18M10.5 18L17.6857 15.1257C17.8755 15.0498 18 14.8659 18 14.6615V12M10.5 18V15M10.5 12C13.4289 10.8284 15.0711 10.1716 18 9M10.5 12V15M18 9V12M10.5 15L18 12'
        stroke='#343B54'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default ModulesIcon