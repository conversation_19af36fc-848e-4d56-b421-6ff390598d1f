import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FireIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Fire'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14.4292 9C10.8224 4.6 11.6551 3.83333 11.9891 3C6.66983 5.37535 -0.611681 11.5041 7.91686 18C7.4159 17.5 6.41402 15 8.91878 13C10.9226 11.4 11.1533 10.8333 10.9863 10C12.5894 11.6 11.9245 13.5 11.4235 15C12.2251 14.6 13.0934 13.5 13.4273 13C13.8281 15.4 12.9264 17.3333 12.4254 18C14.5962 16.8333 18.0361 13.4 14.4292 9Z'
        stroke='#343B54'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default FireIcon