import { FC } from 'react'
import { IconsProps } from '../icons.d'

const LockIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Lock'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect x='6' y='11' width='16' height='13' rx='3' stroke='#343B54' strokeWidth='2' />
      <path
        d='M18 6.85714C18 7.40943 18.4477 7.85714 19 7.85714C19.5523 7.85714 20 7.40943 20 6.85714H18ZM10 11V6H8V11H10ZM12 4H16V2H12V4ZM18 6V6.85714H20V6H18ZM16 4C17.1046 4 18 4.89543 18 6H20C20 3.79086 18.2091 2 16 2V4ZM10 6C10 4.89543 10.8954 4 12 4V2C9.79086 2 8 3.79086 8 6H10Z'
        fill='#343B54'
      />
      <rect x='13' y='15' width='2' height='5' rx='1' fill='#343B54' />
    </svg>
  )
}

export default LockIcon