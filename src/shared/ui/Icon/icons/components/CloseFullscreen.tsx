import { FC } from 'react'
import { IconsProps } from '../icons.d'

const CloseFullscreen: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='CloseFullscreen'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_12748_64543)'>
        <path
          d='M3.14285 8.19653C2.85075 8.19041 2.62219 7.94856 2.63234 7.65634C2.6425 7.36413 2.88753 7.1322 3.17963 7.13832L6.48868 7.24239L2.62438 3.37863C2.41914 3.17339 2.42145 2.83831 2.62955 2.63022C2.83764 2.42212 3.17272 2.41981 3.37796 2.62505L7.24339 6.48993L7.1382 3.17975C7.13208 2.88765 7.36401 2.64262 7.65622 2.63246C7.94844 2.62231 8.19029 2.85087 8.19641 3.14298L8.32551 7.59457C8.32889 7.75598 8.27791 7.90607 8.18972 8.02766C8.16925 8.05943 8.14509 8.08954 8.11725 8.11738C8.08832 8.14631 8.05693 8.17127 8.02378 8.19224C7.90305 8.27829 7.75481 8.32789 7.59552 8.32456L3.14285 8.19653Z'
          fill='white'
        />
        <path
          d='M16.6032 8.19653C16.8953 8.19041 17.1239 7.94856 17.1138 7.65634C17.1036 7.36413 16.8586 7.1322 16.5665 7.13832L13.2574 7.24239L17.1217 3.37863C17.327 3.17339 17.3246 2.83831 17.1165 2.63022C16.9084 2.42212 16.5734 2.41981 16.3681 2.62505L12.5027 6.48993L12.6079 3.17975C12.614 2.88765 12.3821 2.64262 12.0899 2.63246C11.7977 2.62231 11.5558 2.85087 11.5497 3.14298L11.4206 7.59457C11.4172 7.75598 11.4682 7.90607 11.5564 8.02766C11.5768 8.05943 11.601 8.08954 11.6288 8.11738C11.6578 8.14631 11.6892 8.17127 11.7223 8.19224C11.843 8.27829 11.9913 8.32789 12.1506 8.32456L16.6032 8.19653Z'
          fill='white'
        />
        <path
          d='M16.6032 11.5508C16.8953 11.5569 17.1239 11.7988 17.1138 12.091C17.1036 12.3832 16.8586 12.6151 16.5665 12.609L13.2574 12.5049L17.1217 16.3687C17.327 16.5739 17.3246 16.909 17.1165 17.1171C16.9084 17.3252 16.5734 17.3275 16.3681 17.1223L12.5027 13.2574L12.6079 16.5676C12.614 16.8597 12.3821 17.1047 12.0899 17.1149C11.7977 17.125 11.5558 16.8964 11.5497 16.6043L11.4206 12.1527C11.4172 11.9913 11.4682 11.8412 11.5564 11.7197C11.5768 11.6879 11.601 11.6578 11.6288 11.6299C11.6578 11.601 11.6892 11.576 11.7223 11.5551C11.843 11.469 11.9913 11.4194 12.1506 11.4228L16.6032 11.5508Z'
          fill='white'
        />
        <path
          d='M3.14285 11.5508C2.85075 11.5569 2.62219 11.7988 2.63234 12.091C2.6425 12.3832 2.88753 12.6151 3.17963 12.609L6.48868 12.5049L2.62438 16.3687C2.41914 16.5739 2.42145 16.909 2.62955 17.1171C2.83764 17.3252 3.17272 17.3275 3.37796 17.1223L7.24339 13.2574L7.1382 16.5676C7.13208 16.8597 7.36401 17.1047 7.65622 17.1149C7.94844 17.125 8.19029 16.8964 8.19641 16.6043L8.32551 12.1527C8.32889 11.9913 8.27791 11.8412 8.18972 11.7197C8.16925 11.6879 8.14509 11.6578 8.11725 11.6299C8.08832 11.601 8.05693 11.576 8.02378 11.5551C7.90305 11.469 7.75481 11.4194 7.59552 11.4228L3.14285 11.5508Z'
          fill='white'
        />
      </g>
      <defs>
        <clipPath id='clip0_12748_64543'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default CloseFullscreen