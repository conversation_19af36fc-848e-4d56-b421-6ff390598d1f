import { FC } from 'react'
import { IconsProps } from '../icons.d'

const OpenFullscreen: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='OpenFullscreen'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_12747_64523)'>
        <path
          d='M7.60324 2.55079C7.89534 2.55691 8.12391 2.79875 8.11375 3.09097C8.1036 3.38319 7.85857 3.61511 7.56646 3.609L4.25741 3.50493L8.12171 7.36868C8.32696 7.57393 8.32464 7.909 8.11655 8.1171C7.90845 8.32519 7.57337 8.32751 7.36813 8.12227L3.5027 4.25738L3.6079 7.56756C3.61402 7.85967 3.38209 8.1047 3.08987 8.11485C2.79765 8.12501 2.55581 7.89644 2.54969 7.60434L2.42058 3.15275C2.4172 2.99133 2.46818 2.84125 2.55638 2.71965C2.57685 2.68789 2.601 2.65778 2.62884 2.62994C2.65777 2.601 2.68916 2.57605 2.72232 2.55507C2.84304 2.46902 2.99129 2.41942 3.15057 2.42276L7.60324 2.55079Z'
          fill='white'
        />
        <path
          d='M12.1429 2.55079C11.8508 2.55691 11.6222 2.79875 11.6323 3.09097C11.6425 3.38319 11.8875 3.61511 12.1796 3.609L15.4887 3.50493L11.6244 7.36868C11.4191 7.57393 11.4215 7.909 11.6295 8.1171C11.8376 8.32519 12.1727 8.32751 12.378 8.12227L16.2434 4.25738L16.1382 7.56756C16.1321 7.85967 16.364 8.1047 16.6562 8.11485C16.9484 8.12501 17.1903 7.89644 17.1964 7.60434L17.3255 3.15275C17.3289 2.99133 17.2779 2.84125 17.1897 2.71965C17.1692 2.68789 17.1451 2.65778 17.1173 2.62994C17.0883 2.601 17.0569 2.57605 17.0238 2.55507C16.9031 2.46902 16.7548 2.41942 16.5955 2.42276L12.1429 2.55079Z'
          fill='white'
        />
        <path
          d='M12.1429 17.1965C11.8508 17.1904 11.6222 16.9486 11.6323 16.6563C11.6425 16.3641 11.8875 16.1322 12.1796 16.1383L15.4887 16.2424L11.6244 12.3786C11.4191 12.1734 11.4215 11.8383 11.6295 11.6302C11.8376 11.4221 12.1727 11.4198 12.378 11.625L16.2434 15.4899L16.1382 12.1798C16.1321 11.8876 16.364 11.6426 16.6562 11.6325C16.9484 11.6223 17.1903 11.8509 17.1964 12.143L17.3255 16.5946C17.3289 16.756 17.2779 16.9061 17.1897 17.0277C17.1692 17.0594 17.1451 17.0895 17.1173 17.1174C17.0883 17.1463 17.0569 17.1713 17.0238 17.1922C16.9031 17.2783 16.7548 17.3279 16.5955 17.3246L12.1429 17.1965Z'
          fill='white'
        />
        <path
          d='M7.60324 17.1965C7.89534 17.1904 8.12391 16.9486 8.11375 16.6563C8.1036 16.3641 7.85857 16.1322 7.56646 16.1383L4.25741 16.2424L8.12171 12.3786C8.32696 12.1734 8.32464 11.8383 8.11655 11.6302C7.90845 11.4221 7.57337 11.4198 7.36813 11.625L3.5027 15.4899L3.6079 12.1798C3.61402 11.8876 3.38209 11.6426 3.08987 11.6325C2.79765 11.6223 2.55581 11.8509 2.54969 12.143L2.42058 16.5946C2.4172 16.756 2.46818 16.9061 2.55638 17.0277C2.57685 17.0594 2.601 17.0895 2.62884 17.1174C2.65777 17.1463 2.68916 17.1713 2.72232 17.1922C2.84304 17.2783 2.99129 17.3279 3.15057 17.3246L7.60324 17.1965Z'
          fill='white'
        />
      </g>
      <defs>
        <clipPath id='clip0_12747_64523'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default OpenFullscreen