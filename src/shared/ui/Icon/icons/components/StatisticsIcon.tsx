import { FC } from 'react'
import { IconsProps } from '../icons.d'

const StatisticsIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Statistics'
      className={className}
      width='28px'
      height='28px'
      viewBox='0 0 28 28'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g id='pie' stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
        <rect id='Rectangle' x='0' y='0' width='28' height='28'></rect>
        <path
          d='M12,4 C12.5128358,4 12.9355072,4.38604019 12.9932723,4.88337887 L13,5 L13,15 L23,15 C23.4733869,15 23.8699473,15.3289337 23.9735893,15.7707092 L23.9932723,15.8833789 L24,16 C24,21.9201399 19.2661669,26 13,26 C6.92486775,26 2,21.0751322 2,15 C2,8.73383309 6.07986014,4 12,4 Z M11,6.058 L10.7344631,6.09340432 C6.68646146,6.70461857 4,10.2564528 4,15 C4,19.9705627 8.02943725,24 13,24 C17.7435472,24 21.2953814,21.3135385 21.906641,17.2655369 L21.941,17 L12,17 C11.5266131,17 11.1300527,16.6710663 11.0264107,16.2292908 L11.0067277,16.1166211 L11,16 L11,6.058 Z M16,2 C21.5228475,2 26,6.4771525 26,12 C26,12.5128358 25.6139598,12.9355072 25.1166211,12.9932723 L25,13 L16,13 C15.4871642,13 15.0644928,12.6139598 15.0067277,12.1166211 L15,12 L15,3 C15,2.44771525 15.4477153,2 16,2 Z M17,4.062 L17,11 L23.937,11 L23.9066733,10.7741848 C23.391758,7.42554777 20.8003995,4.76152978 17.4887342,4.13826714 L17.2258152,4.0933267 L17,4.062 Z'
          id='Shape'
          fill='#000000'
          fillRule='nonzero'
        ></path>
      </g>
    </svg>
  )
}

export default StatisticsIcon