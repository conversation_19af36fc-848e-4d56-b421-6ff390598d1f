import { FC } from 'react'
import { IconsProps } from '../icons.d'

const SettingsSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='SettingsSmall'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.0122 2.25006C12.7462 2.25852 13.4773 2.34332 14.1937 2.5031C14.5064 2.57285 14.7403 2.83357 14.7758 3.15202L14.946 4.67887C15.0231 5.37992 15.615 5.9109 16.3206 5.91164C16.5103 5.91194 16.6979 5.87244 16.8732 5.79489L18.2738 5.17962C18.5651 5.05165 18.9055 5.12142 19.1229 5.35368C20.1351 6.4347 20.8889 7.73121 21.3277 9.14564C21.4223 9.45064 21.3134 9.78209 21.0564 9.97156L19.8149 10.8867C19.4607 11.1469 19.2516 11.5601 19.2516 11.9995C19.2516 12.439 19.4607 12.8522 19.8157 13.113L21.0582 14.0284C21.3153 14.2178 21.4243 14.5493 21.3297 14.8544C20.8911 16.2685 20.1377 17.565 19.1261 18.6462C18.9089 18.8784 18.5688 18.9483 18.2775 18.8207L16.8712 18.2046C16.4688 18.0285 16.0068 18.0543 15.6265 18.274C15.2463 18.4938 14.9933 18.8813 14.945 19.3178L14.7759 20.8445C14.741 21.1593 14.5122 21.4182 14.204 21.4916C12.7556 21.8362 11.2465 21.8362 9.79803 21.4916C9.48991 21.4182 9.26105 21.1593 9.22618 20.8445L9.05736 19.32C9.00777 18.8843 8.75434 18.498 8.37442 18.279C7.99451 18.06 7.5332 18.0344 7.1322 18.2094L5.72557 18.8257C5.43422 18.9534 5.09403 18.8833 4.87678 18.651C3.86462 17.5685 3.11119 16.2706 2.6732 14.8549C2.57886 14.5499 2.68786 14.2187 2.94485 14.0294L4.18818 13.1134C4.54232 12.8532 4.75147 12.44 4.75147 12.0005C4.75147 11.5611 4.54232 11.1479 4.18771 10.8873L2.94516 9.97291C2.6878 9.78351 2.5787 9.45184 2.67337 9.14664C3.11212 7.73221 3.86594 6.4357 4.87813 5.35468C5.09559 5.12242 5.43594 5.05265 5.72724 5.18062L7.12762 5.79579C7.53056 5.97262 7.9938 5.94591 8.37577 5.72275C8.75609 5.50215 9.00929 5.11428 9.05817 4.6777L9.22824 3.15202C9.26376 2.83341 9.49786 2.5726 9.8108 2.503C10.5281 2.34348 11.26 2.25871 12.0122 2.25006ZM12.0124 3.74996C11.5583 3.75531 11.1056 3.79449 10.6578 3.86708L10.5489 4.84425C10.4471 5.75374 9.92003 6.56108 9.13042 7.01909C8.33597 7.48323 7.36736 7.53909 6.52458 7.16923L5.62629 6.77462C5.05436 7.46879 4.59914 8.25141 4.27852 9.09174L5.07632 9.67885C5.81513 10.2217 6.25147 11.0837 6.25147 12.0005C6.25147 12.9173 5.81513 13.7794 5.0771 14.3216L4.27805 14.9103C4.59839 15.7521 5.05368 16.5362 5.626 17.2317L6.53113 16.8351C7.36923 16.4692 8.33124 16.5228 9.12353 16.9795C9.91581 17.4362 10.4443 18.2418 10.548 19.1527L10.657 20.1366C11.5466 20.2878 12.4555 20.2878 13.3451 20.1366L13.4541 19.1527C13.5549 18.2421 14.0828 17.4337 14.876 16.9753C15.6692 16.5169 16.6332 16.4631 17.4728 16.8305L18.3772 17.2268C18.949 16.5323 19.4041 15.7495 19.7247 14.909L18.9267 14.3212C18.1879 13.7784 17.7516 12.9163 17.7516 11.9995C17.7516 11.0827 18.1879 10.2207 18.9258 9.67853L19.7227 9.09116C19.4021 8.25067 18.9468 7.4679 18.3748 6.77362L17.4783 7.16743C17.113 7.32907 16.7178 7.41226 16.3187 7.41164C14.849 7.4101 13.6155 6.30361 13.4551 4.84389L13.3462 3.86676C12.9007 3.79426 12.4526 3.75518 12.0124 3.74996ZM11.9997 8.25001C14.0708 8.25001 15.7497 9.92894 15.7497 12C15.7497 14.0711 14.0708 15.75 11.9997 15.75C9.92863 15.75 8.2497 14.0711 8.2497 12C8.2497 9.92894 9.92863 8.25001 11.9997 8.25001ZM11.9997 9.75001C10.7571 9.75001 9.7497 10.7574 9.7497 12C9.7497 13.2427 10.7571 14.25 11.9997 14.25C13.2423 14.25 14.2497 13.2427 14.2497 12C14.2497 10.7574 13.2423 9.75001 11.9997 9.75001Z'
        fill='#8E97AF'
      />
    </svg>
  )
}

export default SettingsSmallIcon