import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FishIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Fish'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_4003_62157)'>
        <path
          d='M12.7651 5.78269C12.6318 4.56759 11.5567 3.58786 9.99929 3.5C8.9611 3.44143 5.99945 5.00002 5.99945 5.00002C5.99945 5.00002 7.5 6.56915 7.5 7.5'
          stroke='#8E97AF'
          strokeLinecap='round'
        />
        <path
          d='M1 8C4.31579 13.1606 12.5579 20.5888 19 11'
          stroke='#8E97AF'
          strokeLinecap='round'
        />
        <path
          d='M1 15C4.31579 9.19429 12.5579 0.21261 19 11'
          stroke='#8E97AF'
          strokeLinecap='round'
        />
        <path
          d='M14 10.5C14 10.7761 13.7761 11 13.5 11C13.2239 11 13 10.7761 13 10.5C13 10.2239 13.2239 10 13.5 10C13.7761 10 14 10.2239 14 10.5Z'
          stroke='#8E97AF'
          strokeWidth='2'
        />
      </g>
      <defs>
        <clipPath id='clip0_4003_62157'>
          <rect width='20' height='20' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default FishIcon