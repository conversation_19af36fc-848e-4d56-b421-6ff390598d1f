import { FC } from 'react'
import { IconsProps } from '../icons.d'

const PlayerSound: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='PlayerSound'
      className={className}
      width='22'
      height='22'
      viewBox='0 0 22 22'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M17.4026 3C16.8135 3 16.3359 3.47756 16.3359 4.06667V14.2C16.3359 14.7891 16.8135 15.2667 17.4026 15.2667C17.9917 15.2667 18.4693 14.7891 18.4693 14.2V4.06667C18.4693 3.47756 17.9917 3 17.4026 3ZM13.137 4.59985C12.5479 4.59985 12.0703 5.07742 12.0703 5.66652V14.1999C12.0703 14.789 12.5479 15.2665 13.137 15.2665C13.7261 15.2665 14.2036 14.789 14.2036 14.1999V5.66652C14.2036 5.07742 13.7261 4.59985 13.137 4.59985ZM4.60182 7.79997C4.01272 7.79997 3.53516 8.27753 3.53516 8.86664V14.2C3.53516 14.7891 4.01272 15.2666 4.60182 15.2666C5.19093 15.2666 5.66849 14.7891 5.66849 14.2V8.86663C5.66849 8.27753 5.19093 7.79997 4.60182 7.79997ZM7.80078 7.2667C7.80078 6.67759 8.27834 6.20003 8.86745 6.20003C9.45655 6.20003 9.93411 6.6776 9.93411 7.2667V14.2C9.93411 14.7891 9.45655 15.2667 8.86745 15.2667C8.27834 15.2667 7.80078 14.7891 7.80078 14.2V7.2667ZM3.16797 17.6667C3.16797 17.3905 3.39183 17.1667 3.66797 17.1667H7.62814C7.82591 16.678 8.30499 16.3333 8.86458 16.3333C9.42418 16.3333 9.90326 16.678 10.101 17.1667H18.3384C18.6146 17.1667 18.8384 17.3905 18.8384 17.6667C18.8384 17.9428 18.6146 18.1667 18.3384 18.1667H10.101C9.90315 18.6553 9.42412 18.9999 8.86458 18.9999C8.30505 18.9999 7.82602 18.6553 7.6282 18.1667H3.66797C3.39183 18.1667 3.16797 17.9428 3.16797 17.6667Z'
        fill='white'
      />
    </svg>
  )
}

export default PlayerSound