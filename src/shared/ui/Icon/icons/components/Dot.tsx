import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Dot: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Dot'
      className={className}
      width='6'
      height='6'
      viewBox='0 0 6 6'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='3' cy='3' r='3' fill='#3DBC87' />
    </svg>
  )
}

export default Dot