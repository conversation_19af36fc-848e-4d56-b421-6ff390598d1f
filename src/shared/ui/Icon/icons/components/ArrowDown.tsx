import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ArrowDown: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='ArrowDown'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M7 8.5L10 11.5L13 8.5'
        stroke='#5C6585'
        strokeLinecap='round'
        strokeLinejoin='round'
      ></path>
    </svg>
  )
}

export default ArrowDown
