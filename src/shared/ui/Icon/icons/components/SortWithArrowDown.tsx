import { FC } from 'react'
import { IconsProps } from '../icons.d'

const SortWithArrowDown: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='SortWithArrowDown'
      className={className}
      xmlns='http://www.w3.org/2000/svg'
      width='800px'
      height='800px'
      viewBox='0 0 24 24'
      fill='none'
    >
      <path
        d='M13 12H21M13 8H21M13 16H21M6 7V17M6 17L3 14M6 17L9 14'
        stroke='#000000'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default SortWithArrowDown