import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailToSidebar: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailToSidebar'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#8e97af'
        d='M2.75725 7.65435C2.42399 7.45439 2 7.69445 2 8.08309V13C2 14.6569 3.34315 16 5 16H15C16.6569 16 18 14.6569 18 13V8.0831C18 7.69445 17.576 7.45439 17.2428 7.65435L10.5145 11.6913C10.1978 11.8813 9.80219 11.8813 9.4855 11.6913L2.75725 7.65435Z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#c9cedc'
        d='M17.6042 5.50962C17.8272 5.89837 17.6287 6.36697 17.2444 6.59755L10.1642 10.8457C10.0059 10.9407 9.80807 10.9407 9.64973 10.8457L2.70853 6.68093C2.33489 6.45675 2.13475 6.00697 2.33544 5.62019C2.83491 4.65762 3.84066 4 5 4H15C16.1144 4 17.0869 4.60762 17.6042 5.50962Z'
      />
    </svg>
  )
}

export default EmailToSidebar