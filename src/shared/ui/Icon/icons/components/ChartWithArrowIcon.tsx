import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ChartWithArrowIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='ChartWithArrow'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill='#8e97af'
        d='M16 9C16 8.44771 16.4477 8 17 8H18C18.5523 8 19 8.44772 19 9V18.5C19 18.7761 18.7761 19 18.5 19H16.5C16.2239 19 16 18.7761 16 18.5V9Z'
      />
      <path
        fill='#8e97af'
        d='M11 15C11 14.4477 11.4477 14 12 14H13C13.5523 14 14 14.4477 14 15V18.5C14 18.7761 13.7761 19 13.5 19H11.5C11.2239 19 11 18.7761 11 18.5V15Z'
      />
      <path
        fill='#8e97af'
        d='M6 13C6 12.4477 6.44772 12 7 12H8C8.55228 12 9 12.4477 9 13V18.5C9 18.7761 8.77614 19 8.5 19H6.5C6.22386 19 6 18.7761 6 18.5V13Z'
      />
      <path
        fill='#8e97af'
        d='M1 18C1 17.4477 1.44772 17 2 17H3C3.55228 17 4 17.4477 4 18V18.5C4 18.7761 3.77614 19 3.5 19H1.5C1.22386 19 1 18.7761 1 18.5V18Z'
      />
      <path
        fill='#c9cedc'
        fillRule='evenodd'
        clipRule='evenodd'
        d='M19 2.5C19 3.32843 18.3284 4 17.5 4C17.3166 4 17.1409 3.96709 16.9785 3.90685L13.7896 7.73347C13.9232 7.9578 14 8.21994 14 8.5C14 9.32843 13.3284 10 12.5 10C11.6716 10 11 9.32843 11 8.5C11 8.47957 11.0004 8.45924 11.0012 8.439L8.62739 7.48947C8.35248 7.80245 7.94931 8 7.5 8C7.26843 8 7.04911 7.94752 6.85329 7.85381L3.85381 10.8533C3.94752 11.0491 4 11.2684 4 11.5C4 12.3284 3.32843 13 2.5 13C1.67157 13 1 12.3284 1 11.5C1 10.6716 1.67157 10 2.5 10C2.73157 10 2.95089 10.0525 3.14671 10.1462L6.14619 7.14671C6.05248 6.95089 6 6.73157 6 6.5C6 5.67157 6.67157 5 7.5 5C8.32843 5 9 5.67157 9 6.5C9 6.52043 8.99959 6.54076 8.99878 6.561L11.3726 7.51053C11.6475 7.19755 12.0507 7 12.5 7C12.6834 7 12.8591 7.03291 13.0215 7.09315L16.2104 3.26653C16.0768 3.0422 16 2.78006 16 2.5C16 1.67157 16.6716 1 17.5 1C18.3284 1 19 1.67157 19 2.5Z'
      />
    </svg>
  )
}

export default ChartWithArrowIcon