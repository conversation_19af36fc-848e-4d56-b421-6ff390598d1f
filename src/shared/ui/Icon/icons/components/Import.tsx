import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Import: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Import'
      className={className}
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='40' height='40' rx='8' fill='none' />
      <rect x='28' y='10' width='3' height='20' rx='1.5' fill='white' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.50391 18.5C7.67548 18.5 7.00391 19.1716 7.00391 20C7.00391 20.8285 7.67548 21.5 8.50391 21.5L20.8816 21.5L16.3739 26.0077C15.7881 26.5935 15.7881 27.5432 16.3739 28.129C16.9597 28.7148 17.9094 28.7148 18.4952 28.129L25.5629 21.0614L25.5665 21.0577C26.1523 20.4719 26.1523 19.5222 25.5665 18.9364L18.4955 11.8653C17.9097 11.2796 16.9599 11.2796 16.3741 11.8653C15.7884 12.4511 15.7884 13.4009 16.3741 13.9867L20.8875 18.5L8.50391 18.5Z'
        fill='white'
      />
    </svg>
  )
}

export default Import