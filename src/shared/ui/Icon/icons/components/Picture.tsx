import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Picture: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Picture'
      className={className}
      width='81'
      height='64'
      viewBox='0 0 81 64'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect x='6.5' y='6' width='68' height='52' rx='6' stroke='#C9CEDC' strokeWidth='4' />
      <path
        d='M6.5 52L25.5847 32.7912C25.9757 32.3976 26.6124 32.3976 27.0035 32.7912L39.7548 45.6255C40.1334 46.0065 40.7453 46.0204 41.1408 45.657L74.5 15'
        stroke='#C9CEDC'
        strokeWidth='4'
      />
      <circle cx='40' cy='21.5' r='5' stroke='#C9CEDC' strokeWidth='3' />
    </svg>
  )
}

export default Picture