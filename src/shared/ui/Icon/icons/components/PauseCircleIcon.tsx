import { FC } from 'react'
import { IconsProps } from '../icons.d'

const PauseCircleIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='PauseCircle'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='12' cy='12' r='10' stroke='#343B54' strokeWidth='2' />
      <rect x='9' y='7' width='2' height='10' rx='1' fill='#343B54' />
      <rect x='13' y='7' width='2' height='10' rx='1' fill='#343B54' />
    </svg>
  )
}

export default PauseCircleIcon