import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ChapterIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Chapter'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect x='3.5' y='3.5' width='13' height='3' rx='1.5' stroke='#343B54' />
      <rect x='3.5' y='8.5' width='13' height='3' rx='1.5' stroke='#343B54' />
      <rect x='3.5' y='13.5' width='13' height='3' rx='1.5' stroke='#343B54' />
    </svg>
  )
}

export default ChapterIcon