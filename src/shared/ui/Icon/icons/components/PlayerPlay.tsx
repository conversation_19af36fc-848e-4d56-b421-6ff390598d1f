import { FC } from 'react'
import { IconsProps } from '../icons.d'

const PlayerPlay: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='PlayerPlay'
      className={className}
      width='62'
      height='62'
      viewBox='0 0 62 62'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M49.8984 25.8038C53.8984 28.1132 53.8984 33.8867 49.8984 36.1961L26.0484 49.9659C22.0484 52.2753 17.0484 49.3886 17.0484 44.7698L17.0484 17.2302C17.0484 12.6114 22.0484 9.72462 26.0484 12.034L49.8984 25.8038Z'
        fill='white'
      />
    </svg>
  )
}

export default PlayerPlay