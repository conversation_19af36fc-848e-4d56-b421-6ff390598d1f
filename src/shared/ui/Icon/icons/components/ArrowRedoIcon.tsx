import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ArrowRedoIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='ArrowRedo'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14 22L6 14C3.79086 11.7909 3.79086 8.20914 6 6V6C8.20914 3.79086 11.7909 3.79086 14 6L21 13M21 13V5M21 13H13'
        stroke='#343B54'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default ArrowRedoIcon