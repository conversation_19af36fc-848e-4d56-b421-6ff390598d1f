import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ArrowIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Arrow'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M13.0602 4.97049C12.7593 4.68587 12.2846 4.69909 12 5.00002C11.7154 5.30096 11.7286 5.77565 12.0295 6.06027L15.4737 9.25002H2.75C2.33579 9.25002 2 9.5858 2 10C2 10.4142 2.33579 10.75 2.75 10.75H15.476L12.0295 13.9398C11.7286 14.2244 11.7154 14.6991 12 15C12.2846 15.301 12.7593 15.3142 13.0602 15.0295L17.6826 10.7276C17.8489 10.5703 17.9489 10.3702 17.9826 10.1614C17.994 10.1094 18 10.0554 18 10C18 9.94243 17.9935 9.88635 17.9812 9.83248C17.9462 9.62668 17.8467 9.42978 17.6826 9.27457L13.0602 4.97049Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default ArrowIcon