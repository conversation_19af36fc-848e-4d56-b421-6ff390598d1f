import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EyeDotIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EyeDot'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M3 12C3 11.0807 3.18106 10.1705 3.53284 9.32122C3.88463 8.47194 4.40024 7.70026 5.05025 7.05025C5.70026 6.40024 6.47194 5.88463 7.32122 5.53284C8.17049 5.18106 9.08075 5 10 5C10.9193 5 11.8295 5.18106 12.6788 5.53284C13.5281 5.88463 14.2997 6.40024 14.9497 7.05025C15.5998 7.70026 16.1154 8.47194 16.4672 9.32122C16.8189 10.1705 17 11.0807 17 12'
        stroke='#343B54'
        strokeWidth='2'
        strokeLinecap='round'
      />
      <circle cx='10' cy='12' r='3' fill='#343B54' />
    </svg>
  )
}

export default EyeDotIcon