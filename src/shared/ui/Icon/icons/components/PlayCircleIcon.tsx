import { FC } from 'react'
import { IconsProps } from '../icons.d'

const PlayCircleIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='PlayCircle'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='12' cy='12' r='10' stroke='#343B54' strokeWidth='2' />
      <path
        d='M16.5 11.134C17.1667 11.5189 17.1667 12.4811 16.5 12.866L10.5 16.3301C9.83333 16.715 9 16.2339 9 15.4641L9 8.5359C9 7.7661 9.83333 7.28497 10.5 7.66987L16.5 11.134Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default PlayCircleIcon