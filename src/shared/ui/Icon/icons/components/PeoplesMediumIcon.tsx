import { FC } from 'react'
import { IconsProps } from '../icons.d'

const PeoplesMediumIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='PeoplesMedium'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fill='#c9cedc'
        d='M17 17C17.5523 17 18.0101 16.5479 17.9003 16.0066C17.8366 15.6924 17.7427 15.3842 17.6194 15.0866C17.3681 14.48 16.9998 13.9288 16.5355 13.4645C16.0712 13.0002 15.52 12.6319 14.9134 12.3806C14.3068 12.1293 13.6566 12 13 12C12.3434 12 11.6932 12.1293 11.0866 12.3806C10.48 12.6319 9.92876 13.0002 9.46447 13.4645C9.00017 13.9288 8.63188 14.48 8.3806 15.0866C8.25731 15.3842 8.16337 15.6924 8.09967 16.0066C7.98995 16.5479 8.44772 17 9 17L13 17H17Z'
      />
      <path
        fill='#8e97af'
        d='M13 17C13.5523 17 14.0085 16.5492 13.9169 16.0046C13.842 15.5594 13.7169 15.123 13.5433 14.7039C13.2417 13.9759 12.7998 13.3145 12.2426 12.7574C11.6855 12.2002 11.0241 11.7583 10.2961 11.4567C9.56815 11.1552 8.78793 11 8 11C7.21207 11 6.43185 11.1552 5.7039 11.4567C4.97595 11.7583 4.31451 12.2002 3.75736 12.7574C3.20021 13.3145 2.75825 13.9759 2.45672 14.7039C2.28313 15.123 2.15804 15.5594 2.08314 16.0046C1.99152 16.5492 2.44772 17 3 17L8 17H13Z'
      />
      <circle fill='#c9cedc' cx='8' cy='7' r='3' />
      <circle fill='#c9cedc' cx='13' cy='9' r='2' />
    </svg>
  )
}

export default PeoplesMediumIcon