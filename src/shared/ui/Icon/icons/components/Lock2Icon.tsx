import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Lock2Icon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Lock2'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12 2.00427C13.8753 2.00427 15.3342 3.21056 15.9276 5.00725C16.0575 5.40057 15.844 5.82473 15.4507 5.95464C15.0574 6.08455 14.6332 5.87102 14.5033 5.47771C14.1018 4.26198 13.1854 3.50427 12 3.50427C10.5927 3.50427 9.57976 4.46215 9.50392 6.05574L9.49935 6.25001L9.499 7.99927L17.75 8.00001C18.9409 8.00001 19.9156 8.92517 19.9948 10.096L20 10.25V19.7463C20 20.9371 19.0748 21.9119 17.904 21.9911L17.75 21.9963H6.25C5.05914 21.9963 4.08436 21.0711 4.00519 19.9003L4 19.7463V10.25C4 9.05914 4.92516 8.08436 6.09595 8.0052L6.25 8.00001L7.999 7.99927L7.99935 6.25001C7.99935 3.71154 9.70838 2.00427 12 2.00427ZM17.75 9.50001H6.25C5.8703 9.50001 5.55651 9.78216 5.50685 10.1482L5.5 10.25V19.7463C5.5 20.126 5.78215 20.4398 6.14823 20.4894L6.25 20.4963H17.75C18.1297 20.4963 18.4435 20.2141 18.4932 19.8481L18.5 19.7463V10.25C18.5 9.87031 18.2178 9.55652 17.8518 9.50685L17.75 9.50001ZM12 13.4995C12.8277 13.4995 13.4986 14.1705 13.4986 14.9981C13.4986 15.8258 12.8277 16.4968 12 16.4968C11.1723 16.4968 10.5014 15.8258 10.5014 14.9981C10.5014 14.1705 11.1723 13.4995 12 13.4995Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default Lock2Icon