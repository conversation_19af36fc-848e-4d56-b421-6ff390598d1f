import { FC } from 'react'
import { IconsProps } from '../icons.d'

const RiskLevelCircleIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='RiskLevelCircle'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='10' cy='10' r='7.5' stroke='#343B54' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10 6C9.58579 6 9.25 6.33579 9.25 6.75V11.25C9.25 11.6642 9.58579 12 10 12C10.4142 12 10.75 11.6642 10.75 11.25V6.75C10.75 6.33579 10.4142 6 10 6ZM10 14.5C10.4142 14.5 10.75 14.1642 10.75 13.75C10.75 13.3358 10.4142 13 10 13C9.58579 13 9.25 13.3358 9.25 13.75C9.25 14.1642 9.58579 14.5 10 14.5Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default RiskLevelCircleIcon