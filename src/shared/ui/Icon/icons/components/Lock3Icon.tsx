import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Lock3Icon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Lock3'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9.99715 2C11.4366 2 12.6632 3.01028 12.9411 4.4021C12.9952 4.6729 12.8195 4.93626 12.5487 4.99032C12.2779 5.04439 12.0145 4.86869 11.9605 4.5979C11.7758 3.67278 10.9589 3 9.99715 3C8.94166 3 8.07694 3.81675 8.00051 4.85271L7.99502 5.00213L7.995 7H14C15.1046 7 16 7.89543 16 9V16C16 17.1046 15.1046 18 14 18H6C4.89543 18 4 17.1046 4 16V9C4 7.89543 4.89543 7 6 7H6.995L6.99502 5.00213C6.99502 3.3441 8.33912 2 9.99715 2ZM14 8H6C5.44772 8 5 8.44772 5 9V16C5 16.5523 5.44772 17 6 17H14C14.5523 17 15 16.5523 15 16V9C15 8.44772 14.5523 8 14 8ZM10 11.5C10.5523 11.5 11 11.9477 11 12.5C11 13.0523 10.5523 13.5 10 13.5C9.44772 13.5 9 13.0523 9 12.5C9 11.9477 9.44772 11.5 10 11.5Z'
        fill='#5C6585'
      />
    </svg>
  )
}

export default Lock3Icon