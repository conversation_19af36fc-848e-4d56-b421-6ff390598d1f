import { FC } from 'react'
import { IconsProps } from '../icons.d'

const RefreshIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Refresh'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10.2099 5.23277C11.7826 4.81676 13.451 4.96148 14.9286 5.64206C16.4061 6.32264 17.6005 7.49658 18.3065 8.96217C19.0124 10.4278 19.1859 12.0935 18.7971 13.6731C18.4083 15.2527 17.4815 16.6476 16.1759 17.618C14.8703 18.5885 13.2675 19.0739 11.6428 18.9909C10.0182 18.9079 8.47319 18.2616 7.2733 17.1632C6.07341 16.0647 5.29356 14.5826 5.06776 12.9716C4.9911 12.4247 4.48558 12.0435 3.93864 12.1201C3.3917 12.1968 3.01046 12.7023 3.08712 13.2492C3.37744 15.3205 4.3801 17.2261 5.92281 18.6384C7.46553 20.0507 9.45196 20.8816 11.5408 20.9883C13.6296 21.095 15.6904 20.4709 17.369 19.2232C19.0476 17.9754 20.2393 16.182 20.7392 14.1511C21.2391 12.1202 21.016 9.97854 20.1083 8.09421C19.2006 6.20989 17.665 4.70053 15.7653 3.8255C13.8656 2.95047 11.7204 2.7644 9.69843 3.29927C7.84427 3.78975 6.19984 4.85836 5 6.34314V6.00001C5 5.44772 4.55228 5.00001 4 5.00001C3.44772 5.00001 3 5.44772 3 6.00001V9.00001C3 9.55229 3.44772 10 4 10H7C7.55228 10 8 9.55229 8 9.00001C8 8.44772 7.55228 8.00001 7 8.00001H6.24857C6.27036 7.9752 6.29113 7.94909 6.31077 7.92169C7.25855 6.59954 8.63722 5.64878 10.2099 5.23277Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default RefreshIcon