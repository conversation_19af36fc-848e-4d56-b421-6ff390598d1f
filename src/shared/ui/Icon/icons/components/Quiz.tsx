import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Quiz: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Quiz'
      className={className}
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='40' height='40' rx='8' fill='none' />
      <rect x='8.25' y='8.25' width='9.5' height='9.5' rx='2.75' stroke='white' strokeWidth='2.5' />
      <rect
        x='8.25'
        y='22.25'
        width='9.5'
        height='9.5'
        rx='2.75'
        stroke='white'
        strokeWidth='2.5'
      />
      <rect
        x='22.25'
        y='8.25'
        width='9.5'
        height='9.5'
        rx='2.75'
        stroke='white'
        strokeWidth='2.5'
      />
      <rect
        x='22.25'
        y='22.25'
        width='9.5'
        height='9.5'
        rx='2.75'
        stroke='white'
        strokeWidth='2.5'
      />
    </svg>
  )
}

export default Quiz