import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FormatBoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='FormatBold'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12 2H12.4286C14.0854 2 15.4286 3.34315 15.4286 5V7.42857C15.4286 8.84873 16.5798 10 18 10V10'
        stroke='#5C6585'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M8 2H7.57143C5.91457 2 4.57143 3.34315 4.57143 5V7.42857C4.57143 8.84873 3.42016 10 2 10V10'
        stroke='#5C6585'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M12 18L12.4286 18C14.0854 18 15.4286 16.6569 15.4286 15L15.4286 12.5714C15.4286 11.1513 16.5798 10 18 10V10'
        stroke='#5C6585'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M8 18L7.57143 18C5.91457 18 4.57143 16.6569 4.57143 15L4.57143 12.5714C4.57143 11.1513 3.42016 10 2 10V10'
        stroke='#5C6585'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default FormatBoldIcon