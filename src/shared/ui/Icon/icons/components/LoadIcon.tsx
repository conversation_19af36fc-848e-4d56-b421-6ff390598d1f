import { FC } from 'react'
import { IconsProps } from '../icons.d'

const LoadIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Load'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15.9441 8.06864C16.352 9.32391 16.352 10.6761 15.9441 11.9314C15.5362 13.1866 14.7415 14.2806 13.6737 15.0564C12.6059 15.8322 11.3199 16.25 10 16.25C8.68013 16.25 7.39414 15.8322 6.32634 15.0564C5.25854 14.2806 4.46376 13.1866 4.0559 11.9314C3.64803 10.6761 3.64803 9.32392 4.0559 8.06864C4.46376 6.81337 5.25854 5.71944 6.32634 4.94364C7.39414 4.16784 8.68013 3.75 10 3.75'
        stroke='#8E97AF'
        strokeWidth='1.5'
      />
    </svg>
  )
}

export default LoadIcon