import { FC } from 'react'
import { IconsProps } from '../icons.d'

const OrganizationCirclesIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='OrganizationCircles'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect fill='#c9cedc' x='9.5' y='5' width='2' height='9' transform='rotate(30 9.5 5)' />
      <rect fill='#c9cedc' x='15' y='14' width='2' height='9' transform='rotate(90 15 14)' />
      <circle fill='#8e97af' cx='11' cy='5' r='3' />
      <circle fill='#8e97af' cx='5' cy='15' r='3' />
      <circle fill='#8e97af' cx='15' cy='15' r='3' />
    </svg>
  )
}

export default OrganizationCirclesIcon