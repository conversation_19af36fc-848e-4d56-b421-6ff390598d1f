import { FC } from 'react'
import { IconsProps } from '../icons.d'

const DepartmentIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Department'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14.754 10C15.7205 10 16.504 10.7835 16.504 11.75V15H16.5V15.25C16.5 15.6642 16.1642 16 15.75 16C15.3358 16 15 15.6642 15 15.25V13H15.004V11.75C15.004 11.6119 14.8921 11.5 14.754 11.5H9.25193C9.11386 11.5 9.00193 11.6119 9.00193 11.75V15H9V15.25C9 15.6642 8.66421 16 8.25 16C7.83579 16 7.5 15.6642 7.5 15.25V13H7.50193V11.75C7.50193 10.7835 8.28543 10 9.25193 10H14.754ZM20.5 11.75V15.25C20.5 15.6642 20.8358 16 21.25 16C21.6642 16 22 15.6642 22 15.25V11.75C22 10.7835 21.2165 10 20.25 10L16.8747 9.99906C17.218 10.4145 17.4417 10.9325 17.4928 11.5002L20.25 11.5C20.3881 11.5 20.5 11.6119 20.5 11.75ZM2 15.25C2 15.6642 2.33579 16 2.75 16C3.16421 16 3.5 15.6642 3.5 15.25V11.75C3.5 11.6119 3.61193 11.5 3.75 11.5L6.51312 11.5002C6.56424 10.9325 6.78791 10.4145 7.13128 9.99906L3.75 10C2.7835 10 2 10.7835 2 11.75V15.25ZM12 3C13.6569 3 15 4.34315 15 6C15 7.65685 13.6569 9 12 9C10.3431 9 9 7.65685 9 6C9 4.34315 10.3431 3 12 3ZM12 4.5C11.1716 4.5 10.5 5.17157 10.5 6C10.5 6.82843 11.1716 7.5 12 7.5C12.8284 7.5 13.5 6.82843 13.5 6C13.5 5.17157 12.8284 4.5 12 4.5ZM18.5 4C19.8807 4 21 5.11929 21 6.5C21 7.88071 19.8807 9 18.5 9C17.1193 9 16 7.88071 16 6.5C16 5.11929 17.1193 4 18.5 4ZM18.5 5.5C17.9477 5.5 17.5 5.94772 17.5 6.5C17.5 7.05228 17.9477 7.5 18.5 7.5C19.0523 7.5 19.5 7.05228 19.5 6.5C19.5 5.94772 19.0523 5.5 18.5 5.5ZM5.5 4C6.88071 4 8 5.11929 8 6.5C8 7.88071 6.88071 9 5.5 9C4.11929 9 3 7.88071 3 6.5C3 5.11929 4.11929 4 5.5 4ZM5.5 5.5C4.94772 5.5 4.5 5.94772 4.5 6.5C4.5 7.05228 4.94772 7.5 5.5 7.5C6.05228 7.5 6.5 7.05228 6.5 6.5C6.5 5.94772 6.05228 5.5 5.5 5.5ZM2.75 17C2.33579 17 2 17.3358 2 17.75V18.25C2 20.3211 3.67893 22 5.75 22H18.25C20.3211 22 22 20.3211 22 18.25V17.75C22 17.3358 21.6642 17 21.25 17H2.75ZM5.75 20.5C4.59186 20.5 3.63809 19.625 3.51373 18.5H20.4863C20.3619 19.625 19.4081 20.5 18.25 20.5H5.75Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default DepartmentIcon