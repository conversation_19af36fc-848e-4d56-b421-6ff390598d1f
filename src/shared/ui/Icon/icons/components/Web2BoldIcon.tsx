import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Web2BoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Web2Bold'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M11.9996 1.99896C17.5233 1.99896 22.0011 6.47681 22.0011 12.0005C22.0011 17.5242 17.5233 22.0021 11.9996 22.0021C6.47589 22.0021 1.99805 17.5242 1.99805 12.0005C1.99805 6.47681 6.47589 1.99896 11.9996 1.99896ZM14.9385 16.5003H9.06069C9.71273 18.9144 10.8461 20.5021 11.9996 20.5021C13.1531 20.5021 14.2865 18.9144 14.9385 16.5003ZM7.50791 16.5008L4.78542 16.5007C5.74376 18.0337 7.17721 19.2393 8.87959 19.9113C8.35731 19.0915 7.92632 18.0652 7.60932 16.8958L7.50791 16.5008ZM19.2138 16.5007L16.4913 16.5008C16.1675 17.8346 15.6999 19.0004 15.1185 19.9114C16.7155 19.2813 18.0752 18.1824 19.0286 16.7842L19.2138 16.5007ZM7.09302 9.99986H3.73542L3.73066 10.0171C3.57858 10.6534 3.49805 11.3176 3.49805 12.0005C3.49805 13.0567 3.69064 14.0678 4.04261 15.0008L7.21577 15.0005C7.07347 14.0514 6.99805 13.0431 6.99805 12.0005C6.99805 11.3165 7.03051 10.6473 7.09302 9.99986ZM15.3965 9.99993H8.60267C8.53465 10.6402 8.49805 11.3099 8.49805 12.0005C8.49805 13.06 8.58419 14.0703 8.73778 15.0006H15.2614C15.415 14.0703 15.5011 13.06 15.5011 12.0005C15.5011 11.3099 15.4645 10.6402 15.3965 9.99993ZM20.2642 9.99903L16.9062 9.99988C16.9687 10.6473 17.0011 11.3165 17.0011 12.0005C17.0011 13.0431 16.9257 14.0514 16.7834 15.0005L19.9566 15.0008C20.3086 14.0678 20.5011 13.0567 20.5011 12.0005C20.5011 11.3111 20.4191 10.6409 20.2642 9.99903ZM8.88065 4.08967L8.85774 4.09838C6.81043 4.91309 5.15441 6.5004 4.24975 8.50027L7.29787 8.50063C7.61122 6.74784 8.15807 5.22192 8.88065 4.08967ZM11.9996 3.49896L11.8839 3.50426C10.6185 3.62002 9.39603 5.62198 8.82831 8.50021H15.1709C14.6048 5.63005 13.3875 3.63125 12.1259 3.50528L11.9996 3.49896ZM15.1196 4.08972L15.2264 4.26382C15.8957 5.37629 16.4038 6.83617 16.7013 8.50063L19.7494 8.50027C18.8848 6.58886 17.3338 5.05432 15.4108 4.211L15.1196 4.08972Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default Web2BoldIcon