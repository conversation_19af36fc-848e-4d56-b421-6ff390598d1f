import { FC } from 'react'
import { IconsProps } from '../icons.d'

const SortArrowDownIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='SortArrowDown'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path d='M10 2L13.4641 8H6.5359L10 2Z' fill='#C9CEDC' />
      <path d='M10 18L13.4641 12H6.5359L10 18Z' fill='#5C6585' />
    </svg>
  )
}

export default SortArrowDownIcon