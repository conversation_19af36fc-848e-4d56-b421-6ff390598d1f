import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailSmall'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.125 7C13.1605 7 14 7.86099 14 8.92308V15.0769C14 16.139 13.1605 17 12.125 17H3.875C2.83947 17 2 16.139 2 15.0769V8.92308C2 7.86099 2.83947 7 3.875 7H12.125ZM13.25 10.0469L8.19013 13.1007C8.09234 13.1597 7.97496 13.1696 7.87063 13.1302L7.80987 13.1007L2.75 10.0485V15.0769C2.75 15.7142 3.25368 16.2308 3.875 16.2308H12.125C12.7463 16.2308 13.25 15.7142 13.25 15.0769V10.0469ZM12.125 7.76923H3.875C3.25368 7.76923 2.75 8.28583 2.75 8.92308V9.15538L8 12.323L13.25 9.15462V8.92308C13.25 8.28583 12.7463 7.76923 12.125 7.76923Z'
        fill='#343B54'
      />
      <mask id='path-2-inside-1_8548_116812' fill='white'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M16.0004 6.92308C16.0004 5.86099 15.1609 5 14.1254 5H5.87538C4.98157 5 4.23383 5.64145 4.0459 6.5H4.82841C4.99293 6.07205 5.39968 5.76923 5.87538 5.76923H14.1254C14.7467 5.76923 15.2504 6.28583 15.2504 6.92308V7.15462L14.3366 7.70608C14.4419 7.94934 14.5003 8.21764 14.5004 8.49958L15.2504 8.04692V13.0769C15.2504 13.5793 14.9373 14.0067 14.5004 14.1651V14.9615C15.3562 14.7833 16.0004 14.0073 16.0004 13.0769V6.92308Z'
        />
      </mask>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M16.0004 6.92308C16.0004 5.86099 15.1609 5 14.1254 5H5.87538C4.98157 5 4.23383 5.64145 4.0459 6.5H4.82841C4.99293 6.07205 5.39968 5.76923 5.87538 5.76923H14.1254C14.7467 5.76923 15.2504 6.28583 15.2504 6.92308V7.15462L14.3366 7.70608C14.4419 7.94934 14.5003 8.21764 14.5004 8.49958L15.2504 8.04692V13.0769C15.2504 13.5793 14.9373 14.0067 14.5004 14.1651V14.9615C15.3562 14.7833 16.0004 14.0073 16.0004 13.0769V6.92308Z'
        fill='#C4C4C4'
      />
      <path
        d='M4.0459 6.5L3.06903 6.28617L2.80333 7.5H4.0459V6.5ZM4.82841 6.5V7.5H5.51531L5.7618 6.85885L4.82841 6.5ZM15.2504 7.15462L15.7671 8.01078L16.2504 7.71911V7.15462H15.2504ZM14.3366 7.70608L13.8199 6.84991L13.0717 7.30145L13.4189 8.10339L14.3366 7.70608ZM14.5004 8.49958L13.5004 8.49978L13.5007 10.2709L15.0171 9.35573L14.5004 8.49958ZM15.2504 8.04692H16.2504V6.27537L14.7337 7.19077L15.2504 8.04692ZM14.5004 14.1651L14.1596 13.225L13.5004 13.4639V14.1651H14.5004ZM14.5004 14.9615H13.5004V16.1912L14.7042 15.9405L14.5004 14.9615ZM14.1254 6C14.5849 6 15.0004 6.38926 15.0004 6.92308H17.0004C17.0004 5.33272 15.7369 4 14.1254 4V6ZM5.87538 6H14.1254V4H5.87538V6ZM5.02277 6.71383C5.11601 6.28787 5.47799 6 5.87538 6V4C4.48514 4 3.35164 4.99503 3.06903 6.28617L5.02277 6.71383ZM4.0459 7.5H4.82841V5.5H4.0459V7.5ZM5.87538 4.76923C4.95997 4.76923 4.19853 5.35168 3.89501 6.14115L5.7618 6.85885C5.77358 6.82823 5.79338 6.80427 5.81467 6.78913C5.83414 6.77529 5.85365 6.76923 5.87538 6.76923V4.76923ZM14.1254 4.76923H5.87538V6.76923H14.1254V4.76923ZM16.2504 6.92308C16.2504 5.75756 15.3227 4.76923 14.1254 4.76923V6.76923C14.1707 6.76923 14.2504 6.81409 14.2504 6.92308H16.2504ZM16.2504 7.15462V6.92308H14.2504V7.15462H16.2504ZM14.8533 8.56225L15.7671 8.01078L14.7337 6.29845L13.8199 6.84991L14.8533 8.56225ZM13.4189 8.10339C13.471 8.22364 13.5003 8.35698 13.5004 8.49978L15.5004 8.49937C15.5003 8.0783 15.4129 7.67504 15.2543 7.30877L13.4189 8.10339ZM14.7337 7.19077L13.9837 7.64342L15.0171 9.35573L15.7671 8.90308L14.7337 7.19077ZM16.2504 13.0769V8.04692H14.2504V13.0769H16.2504ZM14.8412 15.1052C15.6736 14.8035 16.2504 14.0005 16.2504 13.0769H14.2504C14.2504 13.1582 14.2011 13.2099 14.1596 13.225L14.8412 15.1052ZM13.5004 14.1651V14.9615H15.5004V14.1651H13.5004ZM15.0004 13.0769C15.0004 13.5412 14.6811 13.9025 14.2966 13.9825L14.7042 15.9405C16.0313 15.6642 17.0004 14.4733 17.0004 13.0769H15.0004ZM15.0004 6.92308V13.0769H17.0004V6.92308H15.0004Z'
        fill='#343B54'
        mask='url(#path-2-inside-1_8548_116812)'
      />
      <mask id='path-4-inside-2_8548_116812' fill='white'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M18.0004 4.92308C18.0004 3.86099 17.1609 3 16.1254 3H7.87538C6.98157 3 6.23383 3.64145 6.0459 4.5H6.82841C6.99293 4.07205 7.39968 3.76923 7.87538 3.76923H16.1254C16.7467 3.76923 17.2504 4.28583 17.2504 4.92308V5.15462L16.3009 5.72765C16.4004 5.96528 16.4555 6.22622 16.4555 6.5V6.52669L17.2504 6.04692V11.0769C17.2504 11.5964 16.9157 12.0356 16.4555 12.1803V12.9703C17.3336 12.8103 18.0004 12.0235 18.0004 11.0769V4.92308Z'
        />
      </mask>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M18.0004 4.92308C18.0004 3.86099 17.1609 3 16.1254 3H7.87538C6.98157 3 6.23383 3.64145 6.0459 4.5H6.82841C6.99293 4.07205 7.39968 3.76923 7.87538 3.76923H16.1254C16.7467 3.76923 17.2504 4.28583 17.2504 4.92308V5.15462L16.3009 5.72765C16.4004 5.96528 16.4555 6.22622 16.4555 6.5V6.52669L17.2504 6.04692V11.0769C17.2504 11.5964 16.9157 12.0356 16.4555 12.1803V12.9703C17.3336 12.8103 18.0004 12.0235 18.0004 11.0769V4.92308Z'
        fill='#C4C4C4'
      />
      <path
        d='M6.0459 4.5L5.06903 4.28617L4.80333 5.5H6.0459V4.5ZM6.82841 4.5V5.5H7.51531L7.7618 4.85885L6.82841 4.5ZM17.2504 5.15462L17.7671 6.01078L18.2504 5.71911V5.15462H17.2504ZM16.3009 5.72765L15.7842 4.87148L15.0448 5.31767L15.3786 6.11411L16.3009 5.72765ZM16.4555 6.52669H15.4555V8.29824L16.9722 7.38284L16.4555 6.52669ZM17.2504 6.04692H18.2504V4.27537L16.7337 5.19077L17.2504 6.04692ZM16.4555 12.1803L16.1556 11.2263L15.4555 11.4464V12.1803H16.4555ZM16.4555 12.9703H15.4555V14.1689L16.6347 13.9541L16.4555 12.9703ZM16.1254 4C16.5849 4 17.0004 4.38926 17.0004 4.92308H19.0004C19.0004 3.33272 17.7369 2 16.1254 2V4ZM7.87538 4H16.1254V2H7.87538V4ZM7.02277 4.71383C7.11601 4.28787 7.47799 4 7.87538 4V2C6.48514 2 5.35164 2.99503 5.06903 4.28617L7.02277 4.71383ZM6.0459 5.5H6.82841V3.5H6.0459V5.5ZM7.87538 2.76923C6.95997 2.76923 6.19853 3.35168 5.89501 4.14115L7.7618 4.85885C7.77358 4.82823 7.79338 4.80427 7.81467 4.78913C7.83414 4.77529 7.85365 4.76923 7.87538 4.76923V2.76923ZM16.1254 2.76923H7.87538V4.76923H16.1254V2.76923ZM18.2504 4.92308C18.2504 3.75756 17.3227 2.76923 16.1254 2.76923V4.76923C16.1707 4.76923 16.2504 4.81409 16.2504 4.92308H18.2504ZM18.2504 5.15462V4.92308H16.2504V5.15462H18.2504ZM16.8176 6.58381L17.7671 6.01078L16.7337 4.29845L15.7842 4.87148L16.8176 6.58381ZM15.3786 6.11411C15.4278 6.23154 15.4555 6.36126 15.4555 6.5H17.4555C17.4555 6.09117 17.3731 5.69902 17.2232 5.34118L15.3786 6.11411ZM15.4555 6.5V6.52669H17.4555V6.5H15.4555ZM16.7337 5.19077L15.9387 5.67053L16.9722 7.38284L17.7671 6.90308L16.7337 5.19077ZM18.2504 11.0769V6.04692H16.2504V11.0769H18.2504ZM16.7553 13.1343C17.634 12.8581 18.2504 12.0313 18.2504 11.0769H16.2504C16.2504 11.1614 16.1975 11.2132 16.1556 11.2263L16.7553 13.1343ZM15.4555 12.1803V12.9703H17.4555V12.1803H15.4555ZM17.0004 11.0769C17.0004 11.5497 16.6699 11.9148 16.2763 11.9865L16.6347 13.9541C17.9973 13.7059 19.0004 12.4973 19.0004 11.0769H17.0004ZM17.0004 4.92308V11.0769H19.0004V4.92308H17.0004Z'
        fill='#343B54'
        mask='url(#path-4-inside-2_8548_116812)'
      />
    </svg>
  )
}

export default EmailSmallIcon