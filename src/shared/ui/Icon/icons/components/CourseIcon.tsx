import { FC } from 'react'
import { IconsProps } from '../icons.d'

const CourseIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Course'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect x='2.5' y='4.5' width='11' height='13' rx='1.5' stroke='#343B54' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8 3H16C16.5523 3 17 3.44772 17 4V14C17 14.5523 16.5523 15 16 15H14V16H16C17.1046 16 18 15.1046 18 14V4C18 2.89543 17.1046 2 16 2H8C6.89543 2 6 2.89543 6 4H7C7 3.44772 7.44772 3 8 3Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default CourseIcon