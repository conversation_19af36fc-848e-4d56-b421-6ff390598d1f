import { FC } from 'react'
import { IconsProps } from '../icons.d'

const LicensesIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Licenses'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.45455 1.36365H18.5455C19.9006 1.36365 21 2.46305 21 3.81819V20.1818C21 21.537 19.9006 22.6364 18.5455 22.6364H5.45455C4.0994 22.6364 3 21.537 3 20.1818V3.81819C3 2.46305 4.0994 1.36365 5.45455 1.36365ZM18.5458 3H5.4549C5.0035 3 4.63672 3.36678 4.63672 3.81818V20.1818C4.63672 20.6332 5.0035 21 5.4549 21H18.5458C18.9972 21 19.364 20.6332 19.364 20.1818V3.81818C19.364 3.36678 18.9972 3 18.5458 3ZM10.3636 14.4546V16.0909H7.09091V14.4546H10.3636ZM16.9091 7.9091V6.27274H7.09091V7.9091H16.9091ZM16.9091 10.3636V12H7.09091V10.3636H16.9091ZM12.0415 15.9918C12.361 15.6722 12.8791 15.6722 13.1986 15.9918L13.7768 16.57L15.5121 14.8341C15.8316 14.5145 16.3496 14.5145 16.6692 14.8339C16.9888 15.1534 16.9889 15.6714 16.6694 15.991L14.3556 18.3056C14.0361 18.6253 13.518 18.6253 13.1984 18.3057L12.0415 17.1488C11.722 16.8293 11.722 16.3113 12.0415 15.9918Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default LicensesIcon