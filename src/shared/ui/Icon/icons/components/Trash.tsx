import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Trash: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Trash'
      className={className}
      width='36'
      height='36'
      viewBox='0 0 36 36'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='36' height='36' rx='6' fill='#F0F3F7' />
      <g clipPath='url(#clip0_12724_62833)'>
        <path
          d='M12.1667 24.6665C12.1667 25.5857 12.9142 26.3332 13.8333 26.3332H22.1667C23.0858 26.3332 23.8333 25.5857 23.8333 24.6665V14.6665H24.6667C25.1269 14.6665 25.5 14.2934 25.5 13.8332C25.5 13.3729 25.1269 12.9998 24.6667 12.9998H22.1667V11.3332C22.1667 10.414 21.4192 9.6665 20.5 9.6665H15.5C14.5808 9.6665 13.8333 10.414 13.8333 11.3332V12.9998H11.3333C10.8731 12.9998 10.5 13.3729 10.5 13.8332C10.5 14.2934 10.8731 14.6665 11.3333 14.6665H12.1667V24.6665ZM15.5 11.3332H20.5V12.9998H15.5V11.3332ZM14.6667 14.6665H22.1667L22.1675 24.6665H13.8333V14.6665H14.6667Z'
          fill='#8E97AF'
        />
        <path
          d='M15.5 17.1668C15.5 16.7066 15.8731 16.3335 16.3333 16.3335C16.7936 16.3335 17.1667 16.7066 17.1667 17.1668V22.1668C17.1667 22.6271 16.7936 23.0002 16.3333 23.0002C15.8731 23.0002 15.5 22.6271 15.5 22.1668V17.1668ZM18.8333 17.1668C18.8333 16.7066 19.2064 16.3335 19.6667 16.3335C20.1269 16.3335 20.5 16.7066 20.5 17.1668V22.1668C20.5 22.6271 20.1269 23.0002 19.6667 23.0002C19.2064 23.0002 18.8333 22.6271 18.8333 22.1668V17.1668Z'
          fill='#8E97AF'
        />
      </g>
      <defs>
        <clipPath id='clip0_12724_62833'>
          <rect width='20' height='20' fill='white' transform='translate(8 8)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default Trash