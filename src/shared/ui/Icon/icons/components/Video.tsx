import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Video: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Video'
      className={className}
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='40' height='40' rx='8' fill='none' />
      <path
        d='M27.75 18.701C28.75 19.2783 28.75 20.7217 27.75 21.299L15.75 28.2272C14.75 28.8046 13.5 28.0829 13.5 26.9282L13.5 13.0718C13.5 11.9171 14.75 11.1954 15.75 11.7728L27.75 18.701Z'
        stroke='white'
        strokeWidth='3'
      />
    </svg>
  )
}

export default Video