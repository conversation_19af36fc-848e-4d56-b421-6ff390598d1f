import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailWarningIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailWarning'
      className={className}
      width='20'
      height='21'
      viewBox='0 0 20 21'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_8548_116614)'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M18 6.50232C18 5.12161 16.8807 4.00232 15.5 4.00232H4.5C3.11929 4.00232 2 5.12161 2 6.50232V14.5023C2 15.883 3.11929 17.0023 4.5 17.0023H10.207C10.1159 16.68 10.0534 16.3457 10.0224 16.0023H4.5C3.67157 16.0023 3 15.3307 3 14.5023V7.96532L9.74649 11.9333L9.82751 11.9716C9.96661 12.0228 10.1231 12.01 10.2535 11.9333L17 7.96332V10.2094C17.3486 10.308 17.6832 10.4401 18 10.602V6.50232ZM4.5 5.00232H15.5C16.3284 5.00232 17 5.67389 17 6.50232V6.80332L10 10.9222L3 6.80432V6.50232C3 5.67389 3.67157 5.00232 4.5 5.00232Z'
          fill='#343B54'
        />
        <path
          d='M19.5 15.5023C19.5 17.7115 17.7091 19.5023 15.5 19.5023C13.2909 19.5023 11.5 17.7115 11.5 15.5023C11.5 13.2932 13.2909 11.5023 15.5 11.5023C17.7091 11.5023 19.5 13.2932 19.5 15.5023Z'
          stroke='#343B54'
        />
        <rect x='15' y='13.0023' width='1' height='3' rx='0.5' fill='#5C6585' />
        <circle cx='15.5' cy='17.113' r='0.625' fill='#5C6585' />
      </g>
      <defs>
        <clipPath id='clip0_8548_116614'>
          <rect width='20' height='20' fill='white' transform='translate(0 0.00231934)' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default EmailWarningIcon