import { FC } from 'react'
import { IconsProps } from '../icons.d'

const PlayerPause: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='PlayerPause'
      className={className}
      width='62'
      height='62'
      viewBox='0 0 62 62'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M21.7008 9.30005C19.1327 9.30005 17.0508 11.3819 17.0508 13.95V48.05C17.0508 50.6182 19.1327 52.7001 21.7008 52.7001C24.2689 52.7001 26.3508 50.6182 26.3508 48.0501V13.95C26.3508 11.3819 24.2689 9.30005 21.7008 9.30005ZM40.3004 9.30005C37.7323 9.30005 35.6504 11.3819 35.6504 13.95V48.05C35.6504 50.6182 37.7323 52.7001 40.3004 52.7001C42.8685 52.7001 44.9504 50.6182 44.9504 48.0501V13.95C44.9504 11.3819 42.8685 9.30005 40.3004 9.30005Z'
        fill='white'
      />
    </svg>
  )
}

export default PlayerPause