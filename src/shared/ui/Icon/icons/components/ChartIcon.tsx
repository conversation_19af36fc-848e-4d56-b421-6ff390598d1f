import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ChartIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Chart'
      className={className}
      width='48'
      height='48'
      viewBox='0 0 48 48'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect
        x='5'
        y='42'
        width='36'
        height='10'
        rx='4'
        transform='rotate(-90 5 42)'
        fill='#C9CEDC'
      />
      <rect
        x='19'
        y='42'
        width='24'
        height='10'
        rx='4'
        transform='rotate(-90 19 42)'
        fill='#C9CEDC'
      />
      <rect
        x='33'
        y='42'
        width='16'
        height='10'
        rx='4'
        transform='rotate(-90 33 42)'
        fill='#C9CEDC'
      />
    </svg>
  )
}

export default ChartIcon