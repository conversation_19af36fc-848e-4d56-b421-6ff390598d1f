import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EducationMediumIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EducationMedium'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#c9cedc'
        d='M2.00207 8.90192V13.5335C2.00207 13.8096 1.77821 14.0335 1.50207 14.0335C1.22593 14.0335 1.00207 13.8096 1.00207 13.5335L1.00207 8.0794C0.985544 7.89652 1.06802 7.70585 1.24948 7.60079L8.49896 3.40372C9.42875 2.86543 10.5754 2.86543 11.5052 3.40372L18.7547 7.60079C19.0873 7.79337 19.0873 8.27363 18.7547 8.46621L11.5052 12.6633C10.5754 13.2016 9.42874 13.2016 8.49896 12.6633L2.00207 8.90192Z'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        fill='#8e97af'
        d='M4 14.4665V11L8.49896 13.6047C9.42874 14.143 10.5754 14.143 11.5052 13.6047L16 11.0024V14.4689L13.5093 15.9109C11.3398 17.1669 8.66431 17.1669 6.49481 15.9109L4 14.4665Z'
      />
    </svg>
  )
}

export default EducationMediumIcon