import { FC } from 'react'
import { IconsProps } from '../icons.d'

const PlayIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Play'
      width='28px'
      height='28px'
      viewBox='0 0 28 28'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      className={className}
    >
      <g stroke='none' strokeWidth='1' fill='none' fillRule='evenodd'>
        <rect x='0' y='0' width='28' height='28'></rect>
        <path
          d='M8,8.53238076 L8,19.4676192 C8,20.5721887 8.8954305,21.4676192 10,21.4676192 C10.3624879,21.4676192 10.7181607,21.3691036 11.0289915,21.1826051 L20.1416902,15.7149859 C21.0888508,15.1466895 21.3959809,13.918169 20.8276846,12.9710085 C20.6587851,12.6895093 20.4231895,12.4539137 20.1416902,12.2850141 L11.0289915,6.81739491 C10.081831,6.24909859 8.85331047,6.55622872 8.28501415,7.50338925 C8.09851567,7.81422005 8,8.16989287 8,8.53238076 Z'
          fill='#000000'
        ></path>
      </g>
    </svg>
  )
}

export default PlayIcon