import { FC } from 'react'
import { IconsProps } from '../icons.d'

const OrganizationBoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='OrganizationBold'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M11.7498 1.99774C9.67751 1.99774 7.99756 3.6777 7.99756 5.75003C7.99756 7.56557 9.28697 9.07995 11 9.42738V11.5H7.75C6.50736 11.5 5.5 12.5074 5.5 13.75V14.5754C3.78819 14.9238 2.5 16.4376 2.5 18.2523C2.5 20.3246 4.17996 22.0046 6.25228 22.0046C8.32461 22.0046 10.0046 20.3246 10.0046 18.2523C10.0046 16.436 8.71409 14.9211 7 14.5745V13.75C7 13.3358 7.33579 13 7.75 13H15.75C16.1642 13 16.5 13.3358 16.5 13.75V14.5754C14.7882 14.9238 13.5 16.4376 13.5 18.2523C13.5 20.3246 15.18 22.0046 17.2523 22.0046C19.3246 22.0046 21.0046 20.3246 21.0046 18.2523C21.0046 16.436 19.7141 14.9211 18 14.5745V13.75C18 12.5074 16.9926 11.5 15.75 11.5H12.5V9.42731C14.2129 9.07976 15.5021 7.56546 15.5021 5.75003C15.5021 3.6777 13.8222 1.99774 11.7498 1.99774ZM9.49756 5.75003C9.49756 4.50612 10.5059 3.49774 11.7498 3.49774C12.9937 3.49774 14.0021 4.50612 14.0021 5.75003C14.0021 6.99393 12.9937 8.00231 11.7498 8.00231C10.5059 8.00231 9.49756 6.99393 9.49756 5.75003ZM4 18.2523C4 17.0084 5.00838 16 6.25228 16C7.49619 16 8.50457 17.0084 8.50457 18.2523C8.50457 19.4962 7.49619 20.5046 6.25228 20.5046C5.00838 20.5046 4 19.4962 4 18.2523ZM17.2523 16C18.4962 16 19.5046 17.0084 19.5046 18.2523C19.5046 19.4962 18.4962 20.5046 17.2523 20.5046C16.0084 20.5046 15 19.4962 15 18.2523C15 17.0084 16.0084 16 17.2523 16Z'
        fill='#3DBC87'
      />
    </svg>
  )
}

export default OrganizationBoldIcon