import { FC } from 'react'
import { IconsProps } from '../icons.d'

const BurgerIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Burger'
      className={className}
      width='16'
      height='12'
      viewBox='0 0 16 12'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_15240_28985)'>
        <rect width='16' height='2.11765' rx='1.05882' fill='#8E97AF' />
        <rect y='4.94141' width='16' height='2.11765' rx='1.05882' fill='#8E97AF' />
        <rect y='9.88281' width='10.6667' height='2.11765' rx='1.05882' fill='#8E97AF' />
      </g>
      <defs>
        <clipPath id='clip0_15240_28985'>
          <rect width='16' height='12' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default BurgerIcon