import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ClipSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='ClipSmall'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M4.82816 10.4853L10.485 4.82845C11.6566 3.65688 13.5561 3.65688 14.7277 4.82845C15.8992 6.00002 15.8992 7.89952 14.7277 9.07109L8.01014 15.7886C7.42436 16.3744 6.47461 16.3744 5.88882 15.7886C5.30304 15.2028 5.30304 14.2531 5.88882 13.6673L11.8992 7.65687C12.0945 7.46161 12.0945 7.14503 11.8992 6.94977C11.704 6.75451 11.3874 6.75451 11.1921 6.94977L5.18172 12.9602C4.20541 13.9365 4.20541 15.5194 5.18172 16.4957C6.15803 17.472 7.74094 17.472 8.71725 16.4957L15.4348 9.77819C16.9969 8.2161 16.9969 5.68344 15.4348 4.12134C13.8727 2.55924 11.34 2.55924 9.77791 4.12134L4.12106 9.77819C3.92579 9.97346 3.92579 10.29 4.12106 10.4853C4.31632 10.6806 4.6329 10.6806 4.82816 10.4853Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default ClipSmallIcon