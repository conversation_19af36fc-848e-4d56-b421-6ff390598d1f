import { FC } from 'react'
import { IconsProps } from '../icons.d'

const TrashBoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='TrashBold'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_541_2303)'>
        <rect x='4' y='7' width='20' height='2' rx='1' fill='#343B54' />
        <path
          d='M19 8V4C19 3.44772 18.5523 3 18 3H10C9.44772 3 9 3.44772 9 4V8'
          stroke='#343B54'
          strokeWidth='2'
          strokeLinejoin='round'
        />
        <path
          d='M21 8V24C21 24.5523 20.5523 25 20 25H8C7.44772 25 7 24.5523 7 24V8'
          stroke='#343B54'
          strokeWidth='2'
          strokeLinejoin='round'
        />
        <rect x='15' y='11' width='2' height='11' rx='1' fill='#343B54' />
        <rect x='11' y='11' width='2' height='11' rx='1' fill='#343B54' />
      </g>
      <defs>
        <clipPath id='clip0_541_2303'>
          <rect width='28' height='28' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default TrashBoldIcon