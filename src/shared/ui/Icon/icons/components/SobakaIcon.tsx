import { FC } from 'react'
import { IconsProps } from '../icons.d'

const SobakaIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Sobaka'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_4238_52828)'>
        <path
          d='M11.79 20.926C14.232 20.926 16.102 19.892 16.806 19.1L15.97 18.044C15.354 18.66 13.792 19.496 11.79 19.496C7.984 19.496 4.904 16.416 4.904 12.5C4.904 8.584 7.984 5.504 11.79 5.504C15.816 5.504 18.676 8.364 18.676 12.06C18.676 14.326 17.774 15.25 16.806 15.25C16.036 15.25 15.464 14.656 15.464 13.644V8.65H13.946V9.794H13.902C13.616 9.266 12.714 8.54 11.416 8.54C9.436 8.54 7.676 10.3 7.676 12.61C7.676 14.92 9.436 16.68 11.416 16.68C12.912 16.68 13.902 15.756 14.232 15.052H14.276C14.562 15.756 15.464 16.68 16.806 16.68C18.676 16.68 20.194 15.184 20.194 12.06C20.194 7.616 16.63 4.074 11.79 4.074C7.17 4.074 3.386 7.836 3.386 12.5C3.386 17.164 7.17 20.926 11.79 20.926ZM11.57 15.25C10.25 15.25 9.194 14.194 9.194 12.61C9.194 11.026 10.25 9.97 11.57 9.97C12.89 9.97 13.946 11.026 13.946 12.61C13.946 14.194 12.89 15.25 11.57 15.25Z'
          fill='black'
        />
      </g>
      <defs>
        <clipPath id='clip0_4238_52828'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default SobakaIcon