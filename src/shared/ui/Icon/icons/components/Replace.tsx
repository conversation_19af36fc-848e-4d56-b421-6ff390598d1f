import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Replace: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Replace'
      className={className}
      width='36'
      height='36'
      viewBox='0 0 36 36'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='36' height='36' rx='6' fill='#F0F3F7' />
      <path
        d='M17.885 11.751C16.323 11.7795 14.7719 12.3893 13.5806 13.5806C11.1398 16.0213 11.1398 19.9786 13.5806 22.4194C13.7955 22.6343 14.0218 22.83 14.2574 23.0066C14.5888 23.2551 14.656 23.7252 14.4076 24.0566C14.1591 24.388 13.689 24.4552 13.3576 24.2068C13.0653 23.9876 12.7852 23.7453 12.5199 23.48C9.49336 20.4535 9.49336 15.5465 12.5199 12.5199C13.8672 11.1727 15.5881 10.4251 17.3484 10.2773L16.7626 9.69146C16.4697 9.39857 16.4697 8.9237 16.7626 8.6308C17.0555 8.33791 17.5303 8.33791 17.8232 8.6308L19.9446 10.7521C20.2375 11.045 20.2375 11.5199 19.9446 11.8128L17.8232 13.9341C17.5303 14.227 17.0555 14.227 16.7626 13.9341C16.4697 13.6412 16.4697 13.1663 16.7626 12.8734L17.885 11.751ZM18.115 24.2489C19.677 24.2204 21.2281 23.6106 22.4194 22.4193C24.8602 19.9786 24.8602 16.0213 22.4194 13.5805C22.2045 13.3656 21.9782 13.1699 21.7426 12.9933C21.4112 12.7448 21.344 12.2747 21.5924 11.9433C21.8409 11.6119 22.311 11.5446 22.6424 11.7931C22.9347 12.0123 23.2148 12.2546 23.4801 12.5198C26.5066 15.5464 26.5066 20.4534 23.4801 23.48C22.1328 24.8272 20.4119 25.5748 18.6516 25.7226L19.2374 26.3084C19.5303 26.6013 19.5303 27.0762 19.2374 27.3691C18.9445 27.662 18.4696 27.662 18.1768 27.3691L16.0554 25.2478C15.7625 24.9549 15.7625 24.48 16.0554 24.1871L18.1768 22.0658C18.4696 21.7729 18.9445 21.7729 19.2374 22.0658C19.5303 22.3587 19.5303 22.8336 19.2374 23.1264L18.115 24.2489Z'
        fill='#8E97AF'
      />
    </svg>
  )
}

export default Replace