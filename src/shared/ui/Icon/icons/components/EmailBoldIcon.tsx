import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailBoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailBold'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_469_1418)'>
        <path
          d='M6.84401 11L13.4176 11.0018C14.615 11.0189 15.2628 11.1594 15.9351 11.5189C16.5982 11.8735 17.1246 12.4 17.4793 13.0631C17.8388 13.7354 17.9792 14.3832 17.9964 15.5806L17.9982 15.8458V20.1542C17.9982 21.5244 17.8645 22.2166 17.4793 22.9369C17.1246 23.6 16.5982 24.1265 15.9351 24.4811C15.2628 24.8406 14.615 24.9811 13.4176 24.9982L6.84401 25C5.47382 25 4.7816 24.8663 4.06132 24.4811C3.39816 24.1265 2.87173 23.6 2.51707 22.9369C2.15754 22.2646 2.01712 21.6168 2 20.4194V15.5806C2.01712 14.3832 2.15754 13.7354 2.51707 13.0631C2.87173 12.4 3.39816 11.8735 4.06132 11.5189C4.73358 11.1594 5.3814 11.0189 6.5788 11.0018L6.84401 11ZM15.9982 15.914L13.6936 18.2186C12.4892 19.3841 11.2575 20 9.99818 20C8.81757 20 7.66123 19.4587 6.51928 18.4212L6.29108 18.2071L3.99818 15.915V20.1542L4.00286 20.5719C4.02193 21.3313 4.10149 21.6586 4.2807 21.9937C4.44896 22.3083 4.68989 22.5492 5.00451 22.7175C5.30913 22.8804 5.6073 22.961 6.22954 22.9886L6.42627 22.9953L6.84401 23H13.1524L13.5701 22.9953C14.3295 22.9762 14.6568 22.8967 14.9919 22.7175C15.3065 22.5492 15.5474 22.3083 15.7157 21.9937C15.8786 21.689 15.9591 21.3909 15.9868 20.7686L15.9935 20.5719L15.9982 20.1542V15.914ZM15.9798 7C17.648 7 18.5294 7.17431 19.4235 7.65245C20.2496 8.09427 20.9039 8.74859 21.3457 9.57471C21.794 10.4129 21.9752 11.24 21.9961 12.7146L21.9982 13.0184V14.9816C21.9982 16.6498 21.8239 17.5312 21.3457 18.4253C20.9039 19.2514 20.2496 19.9057 19.4235 20.3475C19.2825 20.423 19.1418 20.4908 18.9982 20.5516L18.9982 18.2256C19.2333 18.0179 19.4283 17.7697 19.5821 17.4821C19.8276 17.0231 19.948 16.5648 19.9852 15.6863L19.9928 15.4576L19.9982 14.9816V13.0184L19.9928 12.5424C19.9671 11.5116 19.848 11.0151 19.5821 10.5179C19.3267 10.0403 18.9579 9.67149 18.4803 9.41608C17.9856 9.15151 17.4917 9.03226 16.4716 9.00581L15.9798 9L11.7698 9.00133C10.5807 9.01477 10.0489 9.13116 9.51609 9.41608C9.22846 9.56991 8.98027 9.76488 8.77255 9.99997L6.44636 10.0004C6.50722 9.85673 6.57514 9.71588 6.65064 9.57471C7.09245 8.74859 7.74677 8.09427 8.57289 7.65245C9.41107 7.20419 10.2382 7.02297 11.7128 7.00207L12.0166 7H15.9798ZM13.1524 13L6.62537 13.0011C5.72137 13.0112 5.36752 13.0884 5.00451 13.2825C4.82862 13.3766 4.67576 13.4934 4.54575 13.633L7.6936 16.7814C8.55587 17.6159 9.32417 18 9.99818 18C10.6241 18 11.3312 17.6688 12.1101 16.9625L12.2911 16.7929L15.4506 13.633C15.3206 13.4934 15.1677 13.3766 14.9919 13.2825C14.6568 13.1033 14.3295 13.0238 13.5701 13.0047L13.1524 13ZM19.9798 3C21.648 3 22.5294 3.17431 23.4235 3.65245C24.2496 4.09427 24.9039 4.74859 25.3457 5.57471C25.794 6.41289 25.9752 7.23998 25.9961 8.71465L25.9982 9.01841V10.9816C25.9982 12.6498 25.8239 13.5312 25.3457 14.4253C24.9039 15.2514 24.2496 15.9057 23.4235 16.3475C23.2825 16.423 23.1418 16.4908 22.9982 16.5516L22.9982 14.2256C23.2333 14.0179 23.4283 13.7697 23.5821 13.4821C23.8276 13.0231 23.948 12.5648 23.9852 11.6863L23.9928 11.4576L23.9982 10.9816V9.01841L23.9928 8.54236C23.9671 7.51156 23.848 7.01515 23.5821 6.51791C23.3267 6.04032 22.9579 5.67149 22.4803 5.41608C21.9856 5.15151 21.4917 5.03226 20.4716 5.00581L19.9798 5L15.7698 5.00133C14.5807 5.01477 14.0489 5.13116 13.5161 5.41608C13.2285 5.56991 12.9803 5.76488 12.7725 5.99997L10.4464 6.00041C10.5072 5.85673 10.5751 5.71588 10.6506 5.57471C11.0925 4.74859 11.7468 4.09427 12.5729 3.65245C13.4111 3.20419 14.2382 3.02298 15.7128 3.00207L16.0166 3H19.9798Z'
          fill='#343B54'
        />
      </g>
      <defs>
        <clipPath id='clip0_469_1418'>
          <rect width='28' height='28' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default EmailBoldIcon