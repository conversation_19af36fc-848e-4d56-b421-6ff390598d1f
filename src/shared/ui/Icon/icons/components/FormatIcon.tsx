import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FormatIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Format'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9 2H8C6.34315 2 5 3.34315 5 5V9C5 10.6569 3.65685 12 2 12V12'
        stroke='#C9CEDC'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M9 22L8 22C6.34314 22 5 20.6569 5 19L5 15C5 13.3431 3.65686 12 2 12V12'
        stroke='#C9CEDC'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M15 2H16C17.6569 2 19 3.34315 19 5V9C19 10.6569 20.3431 12 22 12V12'
        stroke='#C9CEDC'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M15 22L16 22C17.6569 22 19 20.6569 19 19L19 15C19 13.3431 20.3431 12 22 12V12'
        stroke='#C9CEDC'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default FormatIcon