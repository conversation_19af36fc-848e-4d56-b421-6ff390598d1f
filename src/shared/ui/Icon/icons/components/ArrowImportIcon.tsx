import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ArrowImportIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='ArrowImport'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M17.25 3.75C17.6642 3.75 18 4.08579 18 4.5V15.5C18 15.9142 17.6642 16.25 17.25 16.25C16.8358 16.25 16.5 15.9142 16.5 15.5V4.5C16.5 4.08579 16.8358 3.75 17.25 3.75ZM2 10C2 9.58579 2.33579 9.25 2.75 9.25H12.9393L10.2197 6.53033C9.92678 6.23744 9.92678 5.76256 10.2197 5.46967C10.5126 5.17678 10.9874 5.17678 11.2803 5.46967L15.277 9.46634C15.2891 9.47836 15.3009 9.49079 15.3122 9.50361C15.3626 9.56051 15.4028 9.62323 15.4329 9.68943C15.476 9.78406 15.5 9.88923 15.5 10C15.5 10.1122 15.4753 10.2187 15.4312 10.3143C15.4019 10.3778 15.3633 10.438 15.3153 10.4929C15.3029 10.5071 15.29 10.5208 15.2766 10.534L11.2803 14.5303C10.9874 14.8232 10.5126 14.8232 10.2197 14.5303C9.92678 14.2374 9.92678 13.7626 10.2197 13.4697L12.9393 10.75H2.75C2.33579 10.75 2 10.4142 2 10Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default ArrowImportIcon