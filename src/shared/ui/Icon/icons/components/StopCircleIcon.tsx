import { FC } from 'react'
import { IconsProps } from '../icons.d'

const StopCircleIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='StopCircle'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='12' cy='12' r='10' stroke='#343B54' strokeWidth='2' />
      <rect x='8' y='8' width='8' height='8' rx='1' fill='#343B54' />
    </svg>
  )
}

export default StopCircleIcon