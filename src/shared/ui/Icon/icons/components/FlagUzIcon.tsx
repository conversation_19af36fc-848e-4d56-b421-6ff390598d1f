import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FlagUzIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='FlagUz'
      className={className}
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
      width='1em'
      height='1em'
      viewBox='0 0 36 36'
    >
      <path fill='#1eb53a' d='M0 0h36v36H0z' />
      <path fill='#0099b5' d='M0 0h36v18H0z' />
      <path fill='#ce1126' d='M0 11.5h36v13H0z' />
      <path fill='#fff' d='M0 12.2h36v11.5H0z' />
      <circle cx='10' cy='5.8' r='4.3' fill='#fff' />
      <circle cx='11.5' cy='5.8' r='4.3' fill='#0099b5' />
      <g transform='matrix(0.14 0 0 0.14 19.5 9.2)' fill='#fff'>
        <g id='e'>
          <g id='d'>
            <g id='c'>
              <g id='b'>
                <path id='a' d='M0-6v6h3' transform='rotate(18 0 -6)' />
                <use xlinkHref='#a' transform='scale(-1 1)' width='100%' height='100%' />
              </g>
              <use xlinkHref='#b' transform='rotate(72)' width='100%' height='100%' />
            </g>
            <use xlinkHref='#b' transform='rotate(-72)' width='100%' height='100%' />
            <use xlinkHref='#c' transform='rotate(144)' width='100%' height='100%' />
          </g>
          <use xlinkHref='#d' y='-24' width='100%' height='100%' />
          <use xlinkHref='#d' y='-48' width='100%' height='100%' />
        </g>
        <use xlinkHref='#e' x='24' width='100%' height='100%' />
        <use xlinkHref='#e' x='48' width='100%' height='100%' />
        <use xlinkHref='#d' x='-48' width='100%' height='100%' />
        <use xlinkHref='#d' x='-24' width='100%' height='100%' />
        <use xlinkHref='#d' x='-24' y='-24' width='100%' height='100%' />
      </g>
    </svg>
  )
}

export default FlagUzIcon