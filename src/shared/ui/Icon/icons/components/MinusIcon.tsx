import { FC } from 'react'
import { IconsProps } from '../icons.d'

const MinusIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Minus'
      className={`${className} ${'checkbox-path'}`}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14 10L8.28571 10L6 10'
        stroke='white'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
        className='ng-star-inserted'
      ></path>
    </svg>
  )
}

export default MinusIcon