import { FC } from 'react'
import { IconsProps } from '../icons.d'

const DownloadIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Download'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M5.75 15H14.75C15.1642 15 15.5 15.3358 15.5 15.75C15.5 16.1297 15.2178 16.4435 14.8518 16.4932L14.75 16.5H5.75C5.33579 16.5 5 16.1642 5 15.75C5 15.3703 5.28215 15.0565 5.64823 15.0068L5.75 15H14.75H5.75ZM10.1482 3.00685L10.25 3C10.6297 3 10.9435 3.28215 10.9932 3.64823L11 3.75V11.438L13.2552 9.18414C13.5215 8.91787 13.9381 8.89366 14.2317 9.11152L14.3159 9.18414C14.5821 9.4504 14.6063 9.86707 14.3885 10.1607L14.3159 10.2448L10.7803 13.7803C10.5141 14.0466 10.0974 14.0708 9.80379 13.8529L9.71967 13.7803L6.18414 10.2448C5.89124 9.9519 5.89124 9.47703 6.18414 9.18414C6.4504 8.91787 6.86707 8.89366 7.16068 9.11152L7.2448 9.18414L9.5 11.44V3.75C9.5 3.3703 9.78215 3.05651 10.1482 3.00685L10.25 3L10.1482 3.00685Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default DownloadIcon