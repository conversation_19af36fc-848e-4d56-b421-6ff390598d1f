import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FailIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Fail'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M17.3037 6.69664C16.9132 6.30611 16.28 6.30611 15.8895 6.69664L12 10.5862L8.11049 6.69667C7.71996 6.30615 7.0868 6.30615 6.69627 6.69667C6.30575 7.0872 6.30575 7.72036 6.69627 8.11088L10.5858 12.0004L6.69709 15.889C6.30657 16.2796 6.30657 16.9127 6.69709 17.3032C7.08762 17.6938 7.72078 17.6938 8.11131 17.3032L12 13.4146L15.8887 17.3033C16.2792 17.6938 16.9124 17.6938 17.3029 17.3033C17.6934 16.9127 17.6934 16.2796 17.3029 15.8891L13.4142 12.0004L17.3037 8.11085C17.6942 7.72033 17.6942 7.08716 17.3037 6.69664Z'
        fill='#FF4B60'
      />
    </svg>
  )
}

export default FailIcon