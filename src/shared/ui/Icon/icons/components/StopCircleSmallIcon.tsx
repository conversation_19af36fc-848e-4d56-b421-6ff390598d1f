import { FC } from 'react'
import { IconsProps } from '../icons.d'

const StopCircleSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='StopCircleSmall'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='10' cy='10' r='6.5' stroke='#343B54' />
      <rect x='7' y='7' width='6' height='6' rx='1' fill='#343B54' />
    </svg>
  )
}

export default StopCircleSmallIcon