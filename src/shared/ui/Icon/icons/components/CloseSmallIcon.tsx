import { FC } from 'react'
import { IconsProps } from '../icons.d'

const CloseSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='CloseSmall'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.34274 15.6569C4.538 15.8521 4.85458 15.8521 5.04984 15.6569L9.99959 10.7071L14.9493 15.6569C15.1446 15.8521 15.4612 15.8521 15.6564 15.6569C15.8517 15.4616 15.8517 15.145 15.6564 14.9498L10.7067 10L15.6564 5.05027C15.8517 4.85501 15.8517 4.53843 15.6564 4.34316C15.4612 4.1479 15.1446 4.1479 14.9493 4.34316L9.99959 9.29291L5.04984 4.34316C4.85458 4.1479 4.538 4.1479 4.34274 4.34316C4.14748 4.53843 4.14748 4.85501 4.34274 5.05027L9.29248 10L4.34274 14.9498C4.14747 15.145 4.14747 15.4616 4.34274 15.6569Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default CloseSmallIcon