import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EyeIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Eye'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_8548_116620)'>
        <circle cx='14' cy='16' r='4' stroke='#343B54' strokeWidth='2' />
        <path
          d='M25 14C25 14 19.7794 7 14 7C8.22064 7 3 14 3 14'
          stroke='#343B54'
          strokeWidth='2'
          strokeLinecap='round'
        />
      </g>
      <defs>
        <clipPath id='clip0_8548_116620'>
          <rect width='28' height='28' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default EyeIcon