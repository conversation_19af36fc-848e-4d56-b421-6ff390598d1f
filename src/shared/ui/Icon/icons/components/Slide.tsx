import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Slide: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Slide'
      className={className}
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='40' height='40' rx='8' fill='none' />
      <rect x='9.5' y='11.5' width='21' height='11' rx='2.5' stroke='white' strokeWidth='3' />
      <rect x='8' y='27' width='24' height='3' rx='1.5' fill='white' />
    </svg>
  )
}

export default Slide