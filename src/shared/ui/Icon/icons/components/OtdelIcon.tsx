import { FC } from 'react'
import { IconsProps } from '../icons.d'

const OtdelIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Otdel'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9.98735 2.98914C11.2175 2.98914 12.2147 3.98636 12.2147 5.2165C12.2147 6.44664 11.2175 7.44386 9.98735 7.44386C8.75721 7.44386 7.75999 6.44664 7.75999 5.2165C7.75999 3.98636 8.75721 2.98914 9.98735 2.98914ZM9.98735 3.98914C9.3095 3.98914 8.75999 4.53864 8.75999 5.2165C8.75999 5.89435 9.3095 6.44386 9.98735 6.44386C10.6652 6.44386 11.2147 5.89435 11.2147 5.2165C11.2147 4.53864 10.6652 3.98914 9.98735 3.98914ZM3.01562 11.5C3.01562 11.7761 2.79177 12 2.51562 12C2.23948 12 2.01562 11.7761 2.01562 11.5V9.01419C2.01562 8.50135 2.40167 8.07868 2.899 8.02092L3.01562 8.01419L5.77285 8.01359C5.63086 8.25885 5.53947 8.53706 5.51295 8.83393L5.50494 9.01419H3.01562V11.5ZM17.49 12C17.7661 12 17.99 11.7761 17.99 11.5V9.01419L17.9832 8.89757C17.9255 8.40023 17.5028 8.01419 16.99 8.01419L14.2056 8.01359C14.3525 8.26737 14.4453 8.55642 14.468 8.86493L14.4735 9.01419H16.99V11.5C16.99 11.7761 17.2138 12 17.49 12ZM12.4735 8.01419C12.9863 8.01419 13.409 8.40023 13.4668 8.89757L13.4735 9.01419V11.5C13.4735 11.7761 13.2496 12 12.9735 12C12.6974 12 12.4735 11.7761 12.4735 11.5V9.01419H7.50494V11.5C7.50494 11.7761 7.28108 12 7.00494 12C6.7288 12 6.50494 11.7761 6.50494 11.5V9.01419C6.50494 8.50135 6.89098 8.07868 7.38832 8.02092L7.50494 8.01419H12.4735ZM16.8878 5.53056C16.8878 4.47388 16.0312 3.61727 14.9745 3.61727C13.9178 3.61727 13.0612 4.47388 13.0612 5.53056C13.0612 6.58725 13.9178 7.44386 14.9745 7.44386C16.0312 7.44386 16.8878 6.58725 16.8878 5.53056ZM14.0612 5.53056C14.0612 5.02616 14.4701 4.61727 14.9745 4.61727C15.4789 4.61727 15.8878 5.02616 15.8878 5.53056C15.8878 6.03496 15.4789 6.44386 14.9745 6.44386C14.4701 6.44386 14.0612 6.03496 14.0612 5.53056ZM5.01595 3.61727C6.07264 3.61727 6.92925 4.47388 6.92925 5.53056C6.92925 6.58725 6.07264 7.44386 5.01595 7.44386C3.95927 7.44386 3.10266 6.58725 3.10266 5.53056C3.10266 4.47388 3.95927 3.61727 5.01595 3.61727ZM5.01595 4.61727C4.51155 4.61727 4.10266 5.02616 4.10266 5.53056C4.10266 6.03496 4.51155 6.44386 5.01595 6.44386C5.52035 6.44386 5.92925 6.03496 5.92925 5.53056C5.92925 5.02616 5.52035 4.61727 5.01595 4.61727ZM2.5 13C2.22386 13 2 13.2239 2 13.5V14.5C2 15.8807 3.11929 17 4.5 17H15.5C16.8807 17 18 15.8807 18 14.5V13.5C18 13.2239 17.7761 13 17.5 13H2.5ZM3 14.5V14H17V14.5C17 15.3284 16.3284 16 15.5 16H4.5C3.67157 16 3 15.3284 3 14.5Z'
        fill='#212121'
      />
    </svg>
  )
}

export default OtdelIcon