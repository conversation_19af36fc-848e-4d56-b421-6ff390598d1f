import { FC } from 'react'
import { IconsProps } from '../icons.d'

const PeopleSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='PeopleSmall'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.4745 8.01419C12.9873 8.01419 13.41 8.40023 13.4678 8.89757L13.4745 9.01419V13.3816C13.4745 15.3059 11.9145 16.8658 9.9902 16.8658C8.13003 16.8658 6.61032 15.4081 6.51107 13.5727L6.50591 13.3816V9.01419C6.50591 8.50135 6.89195 8.07868 7.38929 8.02092L7.50591 8.01419H12.4745ZM12.4745 9.01419H7.50591V13.3816C7.50591 14.7536 8.61817 15.8658 9.9902 15.8658C11.3074 15.8658 12.3851 14.8408 12.4692 13.5449L12.4745 13.3816V9.01419ZM5.77382 8.01359C5.63184 8.25885 5.54045 8.53706 5.51393 8.83394L5.50591 9.01419H3.0166V12.7533C3.0166 13.7785 3.84768 14.6096 4.87287 14.6096C5.14409 14.6096 5.40172 14.5514 5.63397 14.4469C5.71323 14.7755 5.8298 15.0906 5.97826 15.387C5.63862 15.5306 5.26497 15.6096 4.87287 15.6096C3.35174 15.6096 2.10833 14.4205 2.02145 12.9211L2.0166 12.7533V9.01419C2.0166 8.50135 2.40264 8.07868 2.89998 8.02092L3.0166 8.01419L5.77382 8.01359ZM14.2066 8.01359L16.9909 8.01419C17.5038 8.01419 17.9264 8.40023 17.9842 8.89757L17.9909 9.01419V12.7541C17.9909 14.3311 16.7125 15.6096 15.1354 15.6096C14.7345 15.6096 14.3529 15.527 14.0067 15.3778C14.1549 15.0801 14.2707 14.7647 14.3501 14.435C14.5884 14.5472 14.8547 14.6096 15.1354 14.6096C16.1114 14.6096 16.9114 13.8561 16.9854 12.8991L16.9909 12.7541V9.01419H14.4745L14.469 8.86493C14.4462 8.55642 14.3535 8.26737 14.2066 8.01359ZM9.98833 2.98914C11.2185 2.98914 12.2157 3.98636 12.2157 5.2165C12.2157 6.44664 11.2185 7.44386 9.98833 7.44386C8.75819 7.44386 7.76097 6.44664 7.76097 5.2165C7.76097 3.98636 8.75819 2.98914 9.98833 2.98914ZM14.9755 3.61727C16.0322 3.61727 16.8888 4.47388 16.8888 5.53056C16.8888 6.58725 16.0322 7.44386 14.9755 7.44386C13.9188 7.44386 13.0622 6.58725 13.0622 5.53056C13.0622 4.47388 13.9188 3.61727 14.9755 3.61727ZM5.01693 3.61727C6.07361 3.61727 6.93022 4.47388 6.93022 5.53056C6.93022 6.58725 6.07361 7.44386 5.01693 7.44386C3.96024 7.44386 3.10363 6.58725 3.10363 5.53056C3.10363 4.47388 3.96024 3.61727 5.01693 3.61727ZM9.98833 3.98914C9.31048 3.98914 8.76097 4.53864 8.76097 5.2165C8.76097 5.89435 9.31048 6.44386 9.98833 6.44386C10.6662 6.44386 11.2157 5.89435 11.2157 5.2165C11.2157 4.53864 10.6662 3.98914 9.98833 3.98914ZM14.9755 4.61727C14.4711 4.61727 14.0622 5.02616 14.0622 5.53056C14.0622 6.03496 14.4711 6.44386 14.9755 6.44386C15.4799 6.44386 15.8888 6.03496 15.8888 5.53056C15.8888 5.02616 15.4799 4.61727 14.9755 4.61727ZM5.01693 4.61727C4.51253 4.61727 4.10363 5.02616 4.10363 5.53056C4.10363 6.03496 4.51253 6.44386 5.01693 6.44386C5.52133 6.44386 5.93022 6.03496 5.93022 5.53056C5.93022 5.02616 5.52133 4.61727 5.01693 4.61727Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default PeopleSmallIcon