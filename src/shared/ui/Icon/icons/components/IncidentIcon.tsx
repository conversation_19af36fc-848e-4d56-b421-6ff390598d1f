import { FC } from 'react'
import { IconsProps } from '../icons.d'

const IncidentIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Incident'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M1.83827 16.5097L9.12842 3.54947C9.51073 2.86982 10.4893 2.86982 10.8716 3.54947L18.1617 16.5097C18.5367 17.1763 18.055 18 17.2902 18H2.70985C1.94502 18 1.46331 17.1763 1.83827 16.5097Z'
        stroke='#343B54'
        strokeLinejoin='round'
      />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M10 7.5C9.58579 7.5 9.25 7.83579 9.25 8.25V12.75C9.25 13.1642 9.58579 13.5 10 13.5C10.4142 13.5 10.75 13.1642 10.75 12.75V8.25C10.75 7.83579 10.4142 7.5 10 7.5ZM10 16C10.4142 16 10.75 15.6642 10.75 15.25C10.75 14.8358 10.4142 14.5 10 14.5C9.58579 14.5 9.25 14.8358 9.25 15.25C9.25 15.6642 9.58579 16 10 16Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default IncidentIcon