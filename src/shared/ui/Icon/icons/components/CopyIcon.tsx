import { FC } from 'react'
import { IconsProps } from '../icons.d'

const CopyIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Copy'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect x='8' y='3' width='13' height='13' rx='3' stroke='#C9CEDC' strokeWidth='2' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7 7H6C3.79086 7 2 8.79086 2 11V18C2 20.2091 3.79086 22 6 22H13C15.2091 22 17 20.2091 17 18V17H15V18C15 19.1046 14.1046 20 13 20H6C4.89543 20 4 19.1046 4 18V11C4 9.89543 4.89543 9 6 9H7V7Z'
        fill='#C9CEDC'
      />
    </svg>
  )
}

export default CopyIcon