import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Settings: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Settings'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.0132 2.25C12.7471 2.25846 13.4782 2.34326 14.1947 2.50304C14.5074 2.57279 14.7413 2.83351 14.7768 3.15196L14.947 4.67881C15.024 5.37986 15.6159 5.91084 16.3216 5.91158C16.5113 5.91188 16.6989 5.87238 16.8742 5.79483L18.2748 5.17956C18.5661 5.05159 18.9064 5.12136 19.1239 5.35362C20.1361 6.43464 20.8899 7.73115 21.3287 9.14558C21.4233 9.45058 21.3144 9.78203 21.0573 9.9715L19.8159 10.8866C19.4617 11.1468 19.2526 11.56 19.2526 11.9995C19.2526 12.4389 19.4617 12.8521 19.8166 13.1129L21.0592 14.0283C21.3163 14.2177 21.4253 14.5492 21.3307 14.8543C20.8921 16.2685 20.1387 17.5649 19.1271 18.6461C18.9098 18.8783 18.5697 18.9483 18.2785 18.8206L16.8721 18.2045C16.4698 18.0284 16.0077 18.0542 15.6275 18.274C15.2473 18.4937 14.9942 18.8812 14.9459 19.3177L14.7769 20.8444C14.742 21.1592 14.5131 21.4182 14.205 21.4915C12.7566 21.8361 11.2475 21.8361 9.79901 21.4915C9.49088 21.4182 9.26202 21.1592 9.22716 20.8444L9.05834 19.32C9.00875 18.8843 8.75532 18.498 8.3754 18.279C7.99549 18.06 7.53418 18.0343 7.13318 18.2094L5.72655 18.8256C5.4352 18.9533 5.09501 18.8833 4.87776 18.6509C3.8656 17.5685 3.11217 16.2705 2.67418 14.8548C2.57984 14.5499 2.68884 14.2186 2.94582 14.0293L4.18915 13.1133C4.54329 12.8531 4.75245 12.4399 4.75245 12.0005C4.75245 11.561 4.54329 11.1478 4.18869 10.8873L2.94614 9.97285C2.68877 9.78345 2.57967 9.45178 2.67434 9.14658C3.1131 7.73215 3.86692 6.43564 4.8791 5.35462C5.09657 5.12236 5.43691 5.05259 5.72822 5.18056L7.1286 5.79572C7.53154 5.97256 7.99478 5.94585 8.37675 5.72269C8.75707 5.50209 9.01026 5.11422 9.05914 4.67764L9.22922 3.15196C9.26474 2.83335 9.49884 2.57254 9.81178 2.50294C10.5291 2.34342 11.2609 2.25865 12.0132 2.25ZM12.0133 3.7499C11.5593 3.75524 11.1065 3.79443 10.6588 3.86702L10.5499 4.84418C10.448 5.75368 9.92101 6.56102 9.1314 7.01903C8.33695 7.48317 7.36834 7.53903 6.52556 7.16917L5.62727 6.77456C5.05534 7.46873 4.60012 8.25135 4.2795 9.09168L5.0773 9.67879C5.81611 10.2216 6.25245 11.0837 6.25245 12.0005C6.25245 12.9172 5.81611 13.7793 5.07807 14.3215L4.27902 14.9102C4.59936 15.752 5.05465 16.5361 5.62698 17.2316L6.53211 16.8351C7.3702 16.4692 8.33222 16.5227 9.12451 16.9794C9.91679 17.4361 10.4453 18.2417 10.549 19.1526L10.6579 20.1365C11.5476 20.2878 12.4564 20.2878 13.3461 20.1365L13.455 19.1527C13.5558 18.2421 14.0837 17.4337 14.877 16.9753C15.6702 16.5168 16.6342 16.463 17.4738 16.8305L18.3782 17.2267C18.95 16.5323 19.4051 15.7495 19.7257 14.909L18.9277 14.3211C18.1889 13.7783 17.7526 12.9162 17.7526 11.9995C17.7526 11.0827 18.1889 10.2206 18.9268 9.67847L19.7237 9.09109C19.403 8.25061 18.9478 7.46784 18.3757 6.77356L17.4793 7.16737C17.1139 7.32901 16.7188 7.4122 16.3196 7.41158C14.85 7.41004 13.6165 6.30355 13.4561 4.84383L13.3472 3.8667C12.9016 3.7942 12.4536 3.75512 12.0133 3.7499ZM12.0007 8.24995C14.0717 8.24995 15.7507 9.92888 15.7507 12C15.7507 14.071 14.0717 15.75 12.0007 15.75C9.9296 15.75 8.25067 14.071 8.25067 12C8.25067 9.92888 9.9296 8.24995 12.0007 8.24995ZM12.0007 9.74995C10.758 9.74995 9.75067 10.7573 9.75067 12C9.75067 13.2426 10.758 14.25 12.0007 14.25C13.2433 14.25 14.2507 13.2426 14.2507 12C14.2507 10.7573 13.2433 9.74995 12.0007 9.74995Z'
        fill='#5C6585'
      />
    </svg>
  )
}

export default Settings
