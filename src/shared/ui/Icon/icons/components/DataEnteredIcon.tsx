import { FC } from 'react'
import { IconsProps } from '../icons.d'

const DataEnteredIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='DataEntered'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M13.2166 8.07129H6.78795C5.72283 8.07129 4.85938 8.93474 4.85938 9.99986V14.4999C4.85938 15.565 5.72283 16.4285 6.78795 16.4285H13.2166C14.2817 16.4285 15.1451 15.565 15.1451 14.4999V9.99986C15.1451 8.93474 14.2817 8.07129 13.2166 8.07129Z'
        stroke='#5C6585'
        strokeWidth='1.2'
      />
      <path
        d='M12.5691 5.40857C12.5691 5.76362 12.857 6.05143 13.212 6.05143C13.5671 6.05143 13.8549 5.76362 13.8549 5.40857H12.5691ZM7.42633 8.07184V4.85756H6.14062V8.07184H7.42633ZM8.71205 3.57184H11.2834V2.28613H8.71205V3.57184ZM12.5691 4.85756V5.40857H13.8549V4.85756H12.5691ZM11.2834 3.57184C11.9935 3.57184 12.5691 4.14747 12.5691 4.85756H13.8549C13.8549 3.43739 12.7036 2.28613 11.2834 2.28613V3.57184ZM7.42633 4.85756C7.42633 4.14747 8.00195 3.57184 8.71205 3.57184V2.28613C7.29189 2.28613 6.14062 3.43739 6.14062 4.85756H7.42633Z'
        fill='#5C6585'
      />
      <path
        d='M10.6451 11.2855C10.6451 10.9304 10.3573 10.6426 10.0022 10.6426C9.6472 10.6426 9.35938 10.9304 9.35938 11.2855V13.2141C9.35938 13.5691 9.6472 13.8569 10.0022 13.8569C10.3573 13.8569 10.6451 13.5691 10.6451 13.2141V11.2855Z'
        fill='#5C6585'
      />
    </svg>
  )
}

export default DataEnteredIcon