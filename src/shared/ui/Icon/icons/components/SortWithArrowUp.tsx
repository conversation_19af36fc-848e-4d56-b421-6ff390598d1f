import { FC } from 'react'
import { IconsProps } from '../icons.d'

const SortWithArrowUp: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='SortWithArrowUp'
      className={className}
      xmlns='http://www.w3.org/2000/svg'
      width='800px'
      height='800px'
      viewBox='0 0 24 24'
      fill='none'
    >
      <path
        d='M13 12H21M13 8H21M13 16H21M6 7V17M6 7L3 10M6 7L9 10'
        stroke='#000000'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default SortWithArrowUp