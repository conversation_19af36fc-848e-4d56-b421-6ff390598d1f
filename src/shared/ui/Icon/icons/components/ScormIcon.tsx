import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ScormIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Scorm'
      className={className}
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='40' height='40' rx='8' fill='none' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M17.5 16H15C12.7909 16 11 17.7909 11 20V30C11 32.2091 12.7909 34 15 34H25C27.2091 34 29 32.2091 29 30V20C29 17.7909 27.2091 16 25 16H22.5V19H25C25.5523 19 26 19.4477 26 20V30C26 30.5523 25.5523 31 25 31H15C14.4477 31 14 30.5523 14 30V20C14 19.4477 14.4477 19 15 19H17.5V16Z'
        fill='white'
      />
      <rect x='18.5' y='13' width='3' height='13' rx='1.5' fill='white' />
      <circle cx='20' cy='10.4546' r='4.5' fill='white' />
    </svg>
  )
}

export default ScormIcon