import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EditSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EditSmall'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M13.2452 2.81742C14.332 1.73062 16.0941 1.73062 17.1809 2.81742C18.2224 3.85895 18.2658 5.52063 17.3111 6.61383L17.1809 6.7531L7.57506 16.3589C7.36997 16.564 7.12323 16.7215 6.85236 16.8214L6.68708 16.8742L2.63211 17.9801C2.28552 18.0747 1.96546 17.7861 2.00301 17.4456L2.01817 17.3662L3.12407 13.3112C3.20039 13.0314 3.33646 12.7722 3.52212 12.5512L3.63939 12.4232L13.2452 2.81742ZM12.385 5.09232L4.34649 13.1304C4.2542 13.2226 4.18117 13.3318 4.13111 13.4514L4.08884 13.5743L3.2122 16.785L6.42397 15.9095C6.50791 15.8866 6.58815 15.8529 6.66278 15.8096L6.77028 15.7375L6.86796 15.6518L14.906 7.61332L12.385 5.09232ZM16.4738 3.52453C15.8162 2.86693 14.7727 2.8304 14.0722 3.41493L13.9523 3.52453L13.092 4.38532L15.613 6.90632L16.4738 6.04599C17.1314 5.38839 17.1679 4.34491 16.5834 3.64444L16.4738 3.52453Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default EditSmallIcon