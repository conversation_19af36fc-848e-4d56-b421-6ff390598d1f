import { FC } from 'react'
import { IconsProps } from '../icons.d'

const TopicIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Topic'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect x='3.5' y='2.5' width='13' height='15' rx='1.5' stroke='#343B54' />
      <rect
        x='5.25'
        y='5.25'
        width='9.5'
        height='0.5'
        rx='0.25'
        stroke='#343B54'
        strokeWidth='0.5'
      />
      <rect
        x='5.25'
        y='8.25'
        width='9.5'
        height='0.5'
        rx='0.25'
        stroke='#343B54'
        strokeWidth='0.5'
      />
      <rect
        x='5.25'
        y='11.25'
        width='5.5'
        height='0.5'
        rx='0.25'
        stroke='#343B54'
        strokeWidth='0.5'
      />
    </svg>
  )
}

export default TopicIcon