import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Web1BoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Web1Bold'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15.7148 20.9506C14.871 22.8069 14.1142 23 14 23C13.8858 23 13.129 22.8069 12.2852 20.9506C11.615 19.4762 11.1293 17.3982 11.0222 15H16.9778C16.8707 17.3982 16.385 19.4762 15.7148 20.9506ZM16.9778 13H11.0222C11.1293 10.6018 11.615 8.52381 12.2852 7.04943C13.129 5.19311 13.8858 5 14 5C14.1142 5 14.871 5.19311 15.7148 7.04943C16.385 8.52381 16.8707 10.6018 16.9778 13ZM18.9796 15C18.8582 17.9638 18.2025 20.5886 17.2259 22.4046C20.3052 21.2219 22.5697 18.3956 22.9451 15H18.9796ZM22.9451 13H18.9796C18.8582 10.0362 18.2025 7.4114 17.2259 5.59542C20.3052 6.77809 22.5697 9.60437 22.9451 13ZM9.02038 13C9.14178 10.0362 9.79753 7.4114 10.7741 5.59542C7.69476 6.77809 5.4303 9.60437 5.05493 13H9.02038ZM5.05493 15C5.4303 18.3956 7.69476 21.2219 10.7741 22.4046C9.79754 20.5886 9.14178 17.9638 9.02038 15H5.05493ZM25 14C25 7.92487 20.0751 3 14 3C7.92487 3 3 7.92487 3 14C3 20.0751 7.92487 25 14 25C20.0751 25 25 20.0751 25 14Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default Web1BoldIcon