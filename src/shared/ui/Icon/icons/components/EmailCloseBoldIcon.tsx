import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailCloseBoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailCloseBold'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M24 10.4V17.6C24 18.7531 23.9984 19.4977 23.9521 20.0643C23.9078 20.6077 23.8324 20.8091 23.782 20.908C23.5903 21.2843 23.2843 21.5903 22.908 21.782C22.8091 21.8324 22.6077 21.9078 22.0643 21.9521C21.4977 21.9984 20.7531 22 19.6 22H8.4C7.24689 22 6.50235 21.9984 5.93567 21.9521C5.39235 21.9078 5.19091 21.8324 5.09202 21.782C4.71569 21.5903 4.40973 21.2843 4.21799 20.908C4.1676 20.8091 4.09225 20.6077 4.04785 20.0643C4.00156 19.4977 4 18.7531 4 17.6V10.4213L9.26867 14.3834C10.9271 15.6305 11.7563 16.2541 12.6628 16.4952C13.4633 16.7081 14.3027 16.7083 15.1032 16.4958C16.0098 16.2551 16.8393 15.632 18.4983 14.3857L23.8926 10.3332L23.8926 10.3332C23.9287 10.3061 23.9645 10.2792 24 10.2525C24 10.3009 24 10.3501 24 10.4ZM23.9375 7.77867C23.6002 8.03874 23.2055 8.33565 22.7392 8.68594L17.345 12.7384C15.5494 14.0873 15.0717 14.4035 14.6156 14.5246C14.1353 14.6521 13.6316 14.6519 13.1514 14.5242C12.6953 14.4029 12.2178 14.0865 10.4228 12.7366L4.97978 8.64352C4.63092 8.38118 4.32218 8.14877 4.04756 7.93935C4.04765 7.93812 4.04775 7.9369 4.04785 7.93567C4.09225 7.39235 4.1676 7.19091 4.21799 7.09202C4.40973 6.71569 4.71569 6.40973 5.09202 6.21799C5.19091 6.1676 5.39235 6.09225 5.93567 6.04785C6.50235 6.00156 7.24689 6 8.4 6H14H19.6C20.7531 6 21.4977 6.00156 22.0643 6.04785C22.6077 6.09225 22.8091 6.1676 22.908 6.21799C23.2843 6.40973 23.5903 6.71569 23.782 7.09202C23.8272 7.18074 23.8925 7.35201 23.9375 7.77867ZM25.8936 7.30333C25.8297 6.86296 25.7275 6.50491 25.564 6.18404C25.1805 5.43139 24.5686 4.81947 23.816 4.43597C22.9603 4 21.8402 4 19.6 4H14H8.4C6.15979 4 5.03969 4 4.18404 4.43597C3.43139 4.81947 2.81947 5.43139 2.43597 6.18404C2 7.03969 2 8.15979 2 10.4V17.6C2 19.8402 2 20.9603 2.43597 21.816C2.81947 22.5686 3.43139 23.1805 4.18404 23.564C5.03969 24 6.15979 24 8.4 24H19.6C21.8402 24 22.9603 24 23.816 23.564C24.5686 23.1805 25.1805 22.5686 25.564 21.816C26 20.9603 26 19.8402 26 17.6V10.4C26 9.74782 26 9.19059 25.9892 8.70717C25.9769 8.15275 25.9504 7.69541 25.8936 7.30333Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default EmailCloseBoldIcon