import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailOpenBoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailOpenBold'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M22 12.3657V17.6001C22 18.7532 21.9984 19.4977 21.9521 20.0644C21.9078 20.6077 21.8324 20.8092 21.782 20.9081C21.5903 21.2844 21.2843 21.5903 20.908 21.7821C20.8091 21.8325 20.6077 21.9078 20.0643 21.9522C19.4977 21.9985 18.7531 22.0001 17.6 22.0001H10.4C9.24689 22.0001 8.50235 21.9985 7.93567 21.9522C7.39235 21.9078 7.19091 21.8325 7.09202 21.7821C6.71569 21.5903 6.40973 21.2844 6.21799 20.9081C6.1676 20.8092 6.09225 20.6077 6.04785 20.0644C6.00156 19.4977 6 18.7532 6 17.6001V12.3771C6.20125 12.5232 6.41403 12.6768 6.63888 12.8391L9.19132 14.6818L9.19133 14.6818C10.8845 15.9041 11.731 16.5153 12.6565 16.7516C13.4737 16.9602 14.3307 16.9604 15.148 16.7522C16.0736 16.5163 16.9204 15.9056 18.6142 14.684L21.1248 12.8734C21.4396 12.6463 21.7309 12.4363 21.9999 12.24C22 12.2811 22 12.323 22 12.3657ZM23.7994 9.50202C23.7402 9.26929 23.6634 9.06044 23.564 8.86535C23.4238 8.59006 23.2529 8.33361 23.0559 8.10027C23.0548 8.12121 23.0534 8.14213 23.0516 8.16303C22.9425 8.03425 22.825 7.91217 22.6998 7.79761C22.1693 7.31236 21.4478 7.01982 20.0048 6.43475L16.4048 4.97506C15.5168 4.61498 15.0727 4.43494 14.6139 4.36367C14.2071 4.30049 13.7929 4.30049 13.3861 4.36367C12.9273 4.43494 12.4832 4.61498 11.5952 4.97506L7.99517 6.43475L7.99517 6.43475C6.5522 7.01982 5.83072 7.31236 5.30023 7.79761C5.09866 7.98199 4.91723 8.18587 4.75829 8.40573C4.75433 8.38836 4.7506 8.37095 4.74711 8.35349C4.63127 8.51537 4.52716 8.68638 4.43597 8.86535C4.33657 9.06044 4.25983 9.26928 4.20059 9.50201C4 10.145 4 10.9196 4 12.3658V17.6001C4 19.8403 4 20.9604 4.43597 21.816C4.81947 22.5687 5.43139 23.1806 6.18404 23.5641C7.03969 24.0001 8.15979 24.0001 10.4 24.0001H17.6C19.8402 24.0001 20.9603 24.0001 21.816 23.5641C22.5686 23.1806 23.1805 22.5687 23.564 21.816C24 20.9604 24 19.8403 24 17.6001V12.3657C24 10.9196 24 10.145 23.7994 9.50202ZM6.14627 9.9983C6.25803 9.72237 6.43008 9.47462 6.65012 9.27335C6.85155 9.0891 7.15651 8.93295 8.74668 8.28819L12.3467 6.8285C13.321 6.43343 13.5169 6.36733 13.693 6.33998C13.8965 6.30839 14.1035 6.30839 14.307 6.33998C14.4831 6.36733 14.679 6.43343 15.6533 6.8285L19.2533 8.28818C20.8435 8.93295 21.1485 9.0891 21.3499 9.27335C21.5402 9.44744 21.6946 9.6563 21.8052 9.8881C21.3476 10.2478 20.7519 10.6786 19.9473 11.2589L17.4367 13.0695C15.6035 14.3917 15.1158 14.7015 14.6502 14.8202C14.1598 14.9451 13.6456 14.945 13.1553 14.8198C12.6897 14.701 12.2022 14.3909 10.3696 13.0679L7.81712 11.2252C7.1177 10.7203 6.57626 10.3285 6.14627 9.9983Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default EmailOpenBoldIcon