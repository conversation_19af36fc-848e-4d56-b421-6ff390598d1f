import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EditBoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EditBold'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_8548_116619)'>
        <path
          d='M22.1806 5.59484L22.3815 5.7957C23.5238 6.93806 23.553 8.77039 22.4499 9.93532C20.7555 11.7247 18.4964 14.084 16.4389 16.1467C14.3461 18.2447 12.5539 19.9408 11.7511 20.488C11.1267 20.9136 9.679 21.5685 8.23068 21.9778C7.51356 22.1805 6.85458 22.3058 6.34074 22.326C5.85925 22.3448 5.69516 22.2643 5.67557 22.2586C5.66511 22.2424 5.64398 22.2041 5.62151 22.1304C5.58617 22.0143 5.55815 21.8443 5.55239 21.6137C5.54079 21.1495 5.62142 20.5483 5.79032 19.8771C6.13061 18.525 6.77417 17.1 7.42938 16.2398C8.03666 15.4426 9.76237 13.6486 11.8438 11.5706C13.9 9.51761 16.2376 7.25172 18.0135 5.54415C19.1801 4.42249 21.0299 4.44407 22.1806 5.59484Z'
          stroke='#343B54'
          strokeWidth='2'
        />
        <rect
          x='16.8438'
          y='6.84393'
          width='6.02502'
          height='2.12132'
          transform='rotate(45 16.8438 6.84393)'
          fill='#343B54'
        />
      </g>
      <defs>
        <clipPath id='clip0_8548_116619'>
          <rect width='28' height='28' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default EditBoldIcon