import { FC } from 'react'
import { IconsProps } from '../icons.d'

const AudioIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Audio'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M10 3C6.13401 3 3 6.13401 3 10V11H4.5C4.77614 11 5 11.2239 5 11.5V17.5C5 17.7761 4.77614 18 4.5 18H4C2.89543 18 2 17.1046 2 16V10C2 5.58172 5.58172 2 10 2C14.4183 2 18 5.58172 18 10V16C18 17.1046 17.1046 18 16 18H15.5C15.2239 18 15 17.7761 15 17.5V11.5C15 11.2239 15.2239 11 15.5 11H17V10C17 6.13401 13.866 3 10 3ZM10.5 10.5C10.5 10.2239 10.2761 10 10 10C9.72386 10 9.5 10.2239 9.5 10.5V17.5C9.5 17.7761 9.72386 18 10 18C10.2761 18 10.5 17.7761 10.5 17.5V10.5ZM7.5 12C7.77614 12 8 12.2239 8 12.5V16.5C8 16.7761 7.77614 17 7.5 17C7.22386 17 7 16.7761 7 16.5V12.5C7 12.2239 7.22386 12 7.5 12ZM13 12.5C13 12.2239 12.7761 12 12.5 12C12.2239 12 12 12.2239 12 12.5V16.5C12 16.7761 12.2239 17 12.5 17C12.7761 17 13 16.7761 13 16.5V12.5Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default AudioIcon