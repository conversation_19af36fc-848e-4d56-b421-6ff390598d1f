import { FC } from 'react'
import { IconsProps } from '../icons.d'

const VideoBig: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='VideoBig'
      className={className}
      width='80'
      height='64'
      viewBox='0 0 80 64'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect x='6' y='6' width='68' height='52' rx='6' stroke='#C9CEDC' strokeWidth='4' />
      <path
        d='M49.5 30.2679C50.8333 31.0378 50.8333 32.9623 49.5 33.7321L34.5 42.3923C33.1667 43.1621 31.5 42.1999 31.5 40.6603L31.5 23.3397C31.5 21.8001 33.1667 20.8379 34.5 21.6077L49.5 30.2679Z'
        fill='#C9CEDC'
      />
    </svg>
  )
}

export default VideoBig