import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ArrowReplaceIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='ArrowReplace'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9.88501 3.75102C8.32299 3.77953 6.77186 4.38929 5.58059 5.58055C3.13981 8.02133 3.13981 11.9786 5.58059 14.4194C5.79547 14.6343 6.02176 14.83 6.25739 15.0066C6.5888 15.2551 6.65604 15.7252 6.40758 16.0566C6.15911 16.388 5.68902 16.4552 5.3576 16.2068C5.06528 15.9876 4.78521 15.7453 4.51993 15.48C1.49336 12.4535 1.49336 7.54645 4.51993 4.51989C5.86716 3.17266 7.58814 2.42506 9.34839 2.27728L8.76258 1.69146C8.46969 1.39857 8.46969 0.923696 8.76258 0.630803C9.05547 0.337909 9.53035 0.33791 9.82324 0.630803L11.9446 2.75212C12.2375 3.04502 12.2375 3.51989 11.9446 3.81278L9.82324 5.9341C9.53035 6.227 9.05547 6.227 8.76258 5.9341C8.46969 5.64121 8.46969 5.16634 8.76258 4.87344L9.88501 3.75102ZM10.115 16.2489C11.677 16.2204 13.2281 15.6106 14.4194 14.4193C16.8602 11.9786 16.8602 8.02129 14.4194 5.58051C14.2045 5.36563 13.9782 5.16994 13.7426 4.99328C13.4112 4.74481 13.344 4.27472 13.5924 3.9433C13.8409 3.61189 14.311 3.54465 14.6424 3.79312C14.9347 4.01228 15.2148 4.25457 15.4801 4.51985C18.5066 7.54641 18.5066 12.4534 15.4801 15.48C14.1328 16.8272 12.4119 17.5748 10.6516 17.7226L11.2374 18.3084C11.5303 18.6013 11.5303 19.0762 11.2374 19.3691C10.9445 19.662 10.4696 19.662 10.1768 19.3691L8.05543 17.2478C7.76253 16.9549 7.76253 16.48 8.05543 16.1871L10.1768 14.0658C10.4696 13.7729 10.9445 13.7729 11.2374 14.0658C11.5303 14.3587 11.5303 14.8336 11.2374 15.1264L10.115 16.2489Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default ArrowReplaceIcon