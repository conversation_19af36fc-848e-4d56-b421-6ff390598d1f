import { FC } from 'react'
import { IconsProps } from '../icons.d'

const OrganizationSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='OrganizationSmall'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9.99992 2C8.34271 2 6.99927 3.34344 6.99927 5.00066C6.99927 6.48754 8.08073 7.72183 9.49999 7.95985V9.5H6.5C5.67157 9.5 5 10.1716 5 11V12.0416C3.5811 12.2799 2.5 13.514 2.5 15.0007C2.5 16.6579 3.84344 18.0013 5.50066 18.0013C7.15787 18.0013 8.50131 16.6579 8.50131 15.0007C8.50131 13.5136 7.41954 12.2791 6 12.0414V11C6 10.7239 6.22386 10.5 6.5 10.5H13.5C13.7761 10.5 14 10.7239 14 11V12.0416C12.5811 12.2799 11.5 13.514 11.5 15.0007C11.5 16.6579 12.8434 18.0013 14.5007 18.0013C16.1579 18.0013 17.5013 16.6579 17.5013 15.0007C17.5013 13.5136 16.4195 12.2791 15 12.0414V11C15 10.1716 14.3284 9.5 13.5 9.5H10.5V7.95983C11.9192 7.72176 13.0006 6.48749 13.0006 5.00066C13.0006 3.34344 11.6571 2 9.99992 2ZM7.99927 5.00066C7.99927 3.89572 8.89499 3 9.99992 3C11.1049 3 12.0006 3.89572 12.0006 5.00066C12.0006 6.10559 11.1049 7.00131 9.99992 7.00131C8.89499 7.00131 7.99927 6.10559 7.99927 5.00066ZM3.5 15.0007C3.5 13.8957 4.39572 13 5.50066 13C6.60559 13 7.50131 13.8957 7.50131 15.0007C7.50131 16.1056 6.60559 17.0013 5.50066 17.0013C4.39572 17.0013 3.5 16.1056 3.5 15.0007ZM14.5007 13C15.6056 13 16.5013 13.8957 16.5013 15.0007C16.5013 16.1056 15.6056 17.0013 14.5007 17.0013C13.3957 17.0013 12.5 16.1056 12.5 15.0007C12.5 13.8957 13.3957 13 14.5007 13Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default OrganizationSmallIcon