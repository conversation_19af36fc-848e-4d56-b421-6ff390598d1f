import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FullscreenEnterIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='FullscreenEnter'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      xmlnsXlink='http://www.w3.org/1999/xlink'
    >
      <path
        d='M12.7071068,15.2928932 C13.0675907,15.6533772 13.0953203,16.2206082 12.7902954,16.6128994 L12.7071068,16.7071068 L7.414,22 L11,22 C11.5128358,22 11.9355072,22.3860402 11.9932723,22.8833789 L12,23 C12,23.5128358 11.6139598,23.9355072 11.1166211,23.9932723 L11,24 L4.92476146,23.9972121 L4.92476146,23.9972121 L4.79927424,23.9797599 L4.79927424,23.9797599 L4.68785748,23.9502619 L4.68785748,23.9502619 L4.57677845,23.9063266 L4.57677845,23.9063266 L4.47929125,23.8540045 L4.47929125,23.8540045 L4.38297765,23.7870723 L4.38297765,23.7870723 L4.29285919,23.7070891 L4.29285919,23.7070891 C4.25749917,23.6717127 4.22531295,23.6343256 4.19633458,23.5953066 L4.12467117,23.4840621 L4.12467117,23.4840621 L4.07122549,23.371336 L4.07122549,23.371336 L4.03584514,23.265993 L4.03584514,23.265993 L4.00672773,23.1166211 L4.00672773,23.1166211 L4,23 L4,17 C4,16.4477153 4.44771525,16 5,16 C5.51283584,16 5.93550716,16.3860402 5.99327227,16.8833789 L6,17 L6,20.584 L11.2928932,15.2928932 C11.6834175,14.9023689 12.3165825,14.9023689 12.7071068,15.2928932 Z M16.8833789,4.00672773 L17,4 L23,4 L23.0752385,4.00278786 L23.0752385,4.00278786 L23.2007258,4.02024007 L23.2007258,4.02024007 L23.3121425,4.04973809 L23.3121425,4.04973809 L23.4232215,4.09367336 L23.4232215,4.09367336 L23.5207088,4.14599545 L23.5207088,4.14599545 L23.6170223,4.21292769 L23.6170223,4.21292769 L23.7071408,4.29291093 L23.7071408,4.29291093 C23.7425008,4.32828727 23.774687,4.36567442 23.8036654,4.40469339 L23.8753288,4.5159379 L23.8753288,4.5159379 L23.9287745,4.62866398 L23.9287745,4.62866398 L23.9641549,4.73400703 L23.9641549,4.73400703 L23.9932723,4.88337887 L23.9932723,4.88337887 L24,5 L24,11 C24,11.5522847 23.5522847,12 23,12 C22.4871642,12 22.0644928,11.6139598 22.0067277,11.1166211 L22,11 L22,7.414 L16.7071068,12.7071068 C16.3165825,13.0976311 15.6834175,13.0976311 15.2928932,12.7071068 C14.9324093,12.3466228 14.9046797,11.7793918 15.2097046,11.3871006 L15.2928932,11.2928932 L20.584,6 L17,6 C16.4871642,6 16.0644928,5.61395981 16.0067277,5.11662113 L16,5 C16,4.48716416 16.3860402,4.06449284 16.8833789,4.00672773 Z'
        id='Shape'
        fill='#000000'
        fillRule='nonzero'
      ></path>
    </svg>
  )
}

export default FullscreenEnterIcon