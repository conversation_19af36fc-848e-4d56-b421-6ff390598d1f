import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailOpenSmallIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailOpenSmall'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M9.74275 3.07125C9.90109 2.97625 10.0989 2.97625 10.2572 3.07125L17.029 7.1343C17.6314 7.49575 18 8.14676 18 8.84929V14.5C18 15.8807 16.8807 17 15.5 17H4.5C3.11929 17 2 15.8807 2 14.5V8.84929C2 8.14676 2.3686 7.49575 2.97101 7.1343L9.74275 3.07125ZM10 4.0831L3.4855 7.99179C3.48096 7.99452 3.47644 7.99728 3.47195 8.00007L10 11.9169L16.5281 8.00007C16.5236 7.99728 16.519 7.99452 16.5145 7.99179L10 4.0831ZM17 8.88309L10.2572 12.9287C10.0989 13.0238 9.90109 13.0238 9.74275 12.9287L3 8.88309V14.5C3 15.3284 3.67157 16 4.5 16H15.5C16.3284 16 17 15.3284 17 14.5V8.88309Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default EmailOpenSmallIcon