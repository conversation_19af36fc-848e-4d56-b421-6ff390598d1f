import { FC } from 'react'
import { IconsProps } from '../icons.d'

const StepPassedIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='StepPassed'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <circle cx='10' cy='10' r='10' fill='#53C394' />
      <path
        d='M6 11L8.7 14L15 7'
        stroke='white'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default StepPassedIcon