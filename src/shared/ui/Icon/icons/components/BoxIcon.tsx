import { FC } from 'react'
import { IconsProps } from '../icons.d'

const BoxIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Box'
      width='160'
      height='160'
      viewBox='0 0 160 160'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      className={className}
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M79.9979 40H53.848L32.6531 64.9205H51.0423C55.4606 64.9205 59.0423 68.5022 59.0423 72.9205V79.4815H79.9979H100.953V72.9205C100.953 68.5022 104.535 64.9205 108.953 64.9205H127.343L106.148 40H79.9979ZM79.9979 32H106.148C108.494 32 110.722 33.0299 112.242 34.817L139.043 66.329C139.658 67.0523 139.996 67.9709 139.996 68.9205V119.999C139.996 124.417 136.414 127.999 131.996 127.999H79.9979H28C23.5817 127.999 20 124.417 20 119.999V68.9205C20 67.9709 20.3378 67.0523 20.953 66.329L47.754 34.817C49.274 33.0299 51.5019 32 53.848 32H79.9979ZM79.9979 119.999H131.996V72.9205H108.953V79.4815C108.953 83.8997 105.372 87.4815 100.953 87.4815H79.9979H59.0423C54.624 87.4815 51.0423 83.8997 51.0423 79.4815V72.9205H28V119.999H79.9979Z'
        fill='#C9CEDC'
      />
    </svg>
  )
}

export default BoxIcon
