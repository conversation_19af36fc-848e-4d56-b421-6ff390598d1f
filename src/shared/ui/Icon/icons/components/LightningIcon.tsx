import { FC } from 'react'
import { IconsProps } from '../icons.d'

const LightningIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Lightning'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_469_1419)'>
        <path
          d='M16 2L14 12.56L21 13.2118L10 26L12 16.4L5 15.7055L16 2Z'
          stroke='#343B54'
          strokeWidth='2'
          strokeLinejoin='round'
        />
        <path
          d='M18.704 27H17.156L20.48 18.6H22.136L25.448 27H23.852L23.072 24.936H19.496L18.704 27ZM21.272 20.244L20 23.616H22.568L21.296 20.244H21.272Z'
          fill='#343B54'
        />
      </g>
      <defs>
        <clipPath id='clip0_469_1419'>
          <rect width='28' height='28' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default LightningIcon