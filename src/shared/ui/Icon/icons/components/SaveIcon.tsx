import { FC } from 'react'
import { IconsProps } from '../icons.d'

const SaveIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Save'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M8 2H5C3.89543 2 3 2.89543 3 4V20C3 21.1046 3.89543 22 5 22H8M8 2V7.625H15.125M8 2H15.125H17L21 7V20C21 21.1046 20.1046 22 19 22H16M16 22V14.5H8V22M16 22H8'
        stroke='#343B54'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
    </svg>
  )
}

export default SaveIcon