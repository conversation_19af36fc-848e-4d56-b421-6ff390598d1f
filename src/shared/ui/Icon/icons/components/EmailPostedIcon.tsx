import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailPostedIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailPosted'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M14.5322 8H19.8317C21.1067 8 21.9369 8.0017 22.5653 8.05524C23.1731 8.10703 23.3891 8.19526 23.4878 8.24983C23.6969 8.36547 23.882 8.51675 24.0357 8.69532L19.8622 12.1792C18.977 12.9181 18.4044 13.3941 17.9397 13.7217C17.4942 14.0358 17.2913 14.107 17.1831 14.1317C16.7713 14.2257 16.3403 14.1869 15.952 14.0208C15.8499 13.9772 15.663 13.8709 15.2807 13.4822C14.882 13.0768 14.4037 12.5063 13.6648 11.6211L11.0625 8.50367C11.2057 8.37663 11.3672 8.27014 11.5425 8.1882C11.64 8.14264 11.827 8.07877 12.3128 8.04105C12.8234 8.0014 13.4907 8 14.5322 8ZM10.3001 10.7113C10.2581 11.046 10.2134 11.4368 10.1604 11.9032L9.79674 15.1032C9.65278 16.3701 9.56073 17.1952 9.54297 17.8255C9.5258 18.4353 9.58908 18.6599 9.63216 18.7641C9.80537 19.1831 10.1158 19.5308 10.5125 19.7502C10.6112 19.8047 10.8272 19.893 11.435 19.9448C12.0634 19.9983 12.8936 20 14.1686 20H19.468C20.5096 20 21.1769 19.9986 21.6875 19.959C22.1733 19.9212 22.3603 19.8574 22.4577 19.8118C22.8124 19.646 23.1106 19.3798 23.3153 19.0461C23.3716 18.9544 23.4562 18.7758 23.5485 18.2974C23.6456 17.7946 23.7223 17.1317 23.8399 16.0968L24.2035 12.8968C24.2928 12.1116 24.3621 11.496 24.4061 10.9914L21.1439 13.7145C19.4241 15.1501 18.5642 15.8679 17.628 16.0816C16.8044 16.2695 15.9425 16.1919 15.1658 15.8598C14.2828 15.4824 13.565 14.6225 12.1294 12.9027L12.1294 12.9027L10.3001 10.7113ZM8.17316 11.6774C8.40136 9.66929 8.51545 8.66525 8.98035 7.90774C9.38989 7.24042 9.98627 6.70797 10.6956 6.3764C11.5007 6 12.5112 6 14.5322 6H19.8317C22.3121 6 23.5523 6 24.4557 6.49966C25.2492 6.93847 25.87 7.63382 26.2164 8.47173C26.6108 9.42582 26.4708 10.6581 26.1908 13.1226L25.8271 16.3226C25.5989 18.3307 25.4848 19.3348 25.0199 20.0923C24.6104 20.7596 24.014 21.292 23.3047 21.6236C22.4996 22 21.4891 22 19.468 22H14.1686C11.6882 22 10.448 22 9.54455 21.5003C8.75112 21.0615 8.13031 20.3662 7.78389 19.5283C7.38944 18.5742 7.52947 17.3419 7.80953 14.8774L8.17316 11.6774Z'
        fill='#343B54'
      />
      <rect y='8' width='7' height='2' rx='1' fill='#343B54' />
      <rect y='13' width='6' height='2' rx='1' fill='#343B54' />
      <rect y='18' width='5' height='2' rx='1' fill='#343B54' />
    </svg>
  )
}

export default EmailPostedIcon