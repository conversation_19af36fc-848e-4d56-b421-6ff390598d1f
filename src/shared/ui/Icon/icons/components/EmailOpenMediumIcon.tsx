import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailOpenMediumIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EmailOpenMedium'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12 4.77712L19.8043 9.73518C20.208 9.99161 20.4634 10.4237 20.4964 10.8967L12 15.1449L3.50364 10.8967C3.53664 10.4237 3.79201 9.99161 4.19565 9.73518L3.3913 8.46908L4.19565 9.73518L12 4.77712ZM3.5 12.5719V18C3.5 18.8284 4.17157 19.5 5 19.5H19C19.8284 19.5 20.5 18.8284 20.5 18V12.5719L12.3801 16.6319C12.1408 16.7515 11.8592 16.7515 11.6199 16.6319L3.5 12.5719ZM11.9464 3.03409L3.3913 8.46908C2.52485 9.01953 2 9.97477 2 11.0013V18C2 19.6569 3.34315 21 5 21H19C20.6569 21 22 19.6569 22 18V11.0013C22 9.97477 21.4751 9.01953 20.6087 8.46908L12.0536 3.03409C12.0209 3.0133 11.9791 3.0133 11.9464 3.03409Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default EmailOpenMediumIcon