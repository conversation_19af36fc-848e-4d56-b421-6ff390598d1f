import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Graduation: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Graduation'
      className={className}
      width='34'
      height='34'
      viewBox='0 0 34 34'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14.4599 5.7816C16.0324 4.87879 17.966 4.87879 19.5385 5.7816L31.8724 12.8628C32.1364 13.0144 32.2992 13.2955 32.2992 13.6C32.2992 13.9044 32.1364 14.1855 31.8724 14.3371L27.1992 17.02V24.6499C27.1992 24.8753 27.1097 25.0915 26.9503 25.2509L26.9476 25.2536L26.9436 25.2575L26.9319 25.269L26.8934 25.3059C26.861 25.3365 26.8152 25.3789 26.7562 25.4313C26.6383 25.5361 26.4675 25.6813 26.2453 25.8533C25.8014 26.197 25.1504 26.6493 24.3055 27.0999C22.6148 28.0016 20.1406 28.8999 16.9992 28.8999C13.8578 28.8999 11.3837 28.0016 9.69297 27.0999C8.84803 26.6493 8.19706 26.197 7.75309 25.8533C7.53095 25.6813 7.36012 25.5361 7.24223 25.4313C7.18326 25.3789 7.13748 25.3365 7.10508 25.3059C6.92206 25.1292 6.79922 24.9113 6.79922 24.6499V17.02L3.39922 15.0681L3.39922 22.9499C3.39922 23.4193 3.01866 23.7999 2.54922 23.7999C2.07978 23.7999 1.69922 23.4193 1.69922 22.9499V13.6C1.69922 13.278 1.87821 12.9978 2.1421 12.8536L14.4599 5.7816ZM19.5385 21.4182C17.966 22.3209 16.0324 22.3209 14.46 21.4182L8.49922 17.996V24.2712C8.57888 24.3385 8.6772 24.4188 8.79378 24.509C9.17326 24.8028 9.74415 25.2005 10.493 25.5999C11.9898 26.3982 14.1906 27.1999 16.9992 27.1999C19.8078 27.1999 22.0087 26.3982 23.5055 25.5999C24.2543 25.2005 24.8252 24.8028 25.2047 24.509C25.3212 24.4188 25.4196 24.3385 25.4992 24.2712V17.996L19.5385 21.4182ZM18.6921 7.2559C17.6438 6.65402 16.3547 6.65402 15.3064 7.25589L4.2564 13.5999L15.3064 19.9439C16.3547 20.5457 17.6437 20.5457 18.6921 19.9439L29.742 13.5999L18.6921 7.2559Z'
        fill='#8E97AF'
        stroke='#8E97AF'
        strokeWidth='0.5'
        strokeLinecap='round'
      />
    </svg>
  )
}

export default Graduation