import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FileIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='File'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M15 3H7.25C6.65326 3 6.08097 3.23178 5.65901 3.64437C5.23705 4.05695 5 4.61652 5 5.2V22.8C5 23.3835 5.23705 23.9431 5.65901 24.3556C6.08097 24.7682 6.65326 25 7.25 25H20.75C21.3467 25 21.919 24.7682 22.341 24.3556C22.7629 23.9431 23 23.3835 23 22.8V11L15 3Z'
        stroke='#343B54'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M15 3V11H23'
        stroke='#343B54'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <rect x='9' y='19' width='10' height='2' rx='1' fill='#343B54' />
      <rect x='9' y='15' width='10' height='2' rx='1' fill='#343B54' />
    </svg>
  )
}

export default FileIcon