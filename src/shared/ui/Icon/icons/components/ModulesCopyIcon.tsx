import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ModulesCopyIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='ModulesCopy'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15.4176 9.00182L8.84401 9L8.5788 9.00182C7.3814 9.01894 6.73358 9.15935 6.06132 9.51889C5.39816 9.87355 4.87173 10.4 4.51707 11.0631C4.15754 11.7354 4.01712 12.3832 4 13.5806V18.4194C4.01712 19.6168 4.15754 20.2646 4.51707 20.9369C4.87173 21.6 5.39816 22.1265 6.06132 22.4811C6.7816 22.8663 7.47382 23 8.84401 23L15.4176 22.9982C16.615 22.9811 17.2628 22.8406 17.9351 22.4811C18.5982 22.1265 19.1246 21.6 19.4793 20.9369C19.8645 20.2166 19.9982 19.5244 19.9982 18.1542V13.8458L19.9964 13.5806C19.9792 12.3832 19.8388 11.7354 19.4793 11.0631C19.1246 10.4 18.5982 9.87355 17.9351 9.51889C17.2628 9.15935 16.615 9.01894 15.4176 9.00182ZM21.4235 5.65245C20.5294 5.17431 19.648 5 17.9798 5H14.0166L13.7128 5.00207C12.2382 5.02298 11.4111 5.20419 10.5729 5.65245C9.74677 6.09427 9.09245 6.74859 8.65063 7.57471C8.57514 7.71588 8.50722 7.85673 8.44636 8.00041L10.7725 7.99997C10.9803 7.76488 11.2285 7.56991 11.5161 7.41608C12.0488 7.13116 12.5807 7.01477 13.7698 7.00133L17.9798 7L18.4716 7.00581C19.4917 7.03226 19.9856 7.15151 20.4803 7.41608C20.9579 7.67149 21.3267 8.04032 21.5821 8.51791C21.848 9.01515 21.9671 9.51156 21.9928 10.5424L21.9982 11.0184V12.9816L21.9928 13.4576L21.9852 13.6863C21.948 14.5648 21.8276 15.0231 21.5821 15.4821C21.4283 15.7697 21.2333 16.0179 20.9982 16.2256L20.9982 18.5516C21.1418 18.4908 21.2825 18.423 21.4235 18.3475C22.2496 17.9057 22.9039 17.2514 23.3457 16.4253C23.8239 15.5312 23.9982 14.6498 23.9982 12.9816V11.0184L23.9961 10.7146C23.9752 9.23998 23.794 8.41289 23.3457 7.57471C22.9039 6.74859 22.2496 6.09427 21.4235 5.65245ZM5.99818 18.1542V13.915C5.99818 12.5 6.54575 11.6623 6.54575 11.6623C6.54575 11.6623 6.82862 11.3766 7.00451 11.2825C7.36752 11.0884 7.72137 11.0112 8.62537 11.0011L15.1524 11L15.5701 11.0047C16.3295 11.0238 16.6568 11.1033 16.9919 11.2825C17.1677 11.3766 17.3206 11.4934 17.4506 11.633C17.6331 11.6867 17.9982 12.2179 17.9982 13.914V18.1542L17.9935 18.5719L17.9868 18.7686C17.9591 19.3909 17.8786 19.689 17.7157 19.9937C17.5474 20.3083 17.3065 20.5492 16.9919 20.7175C16.6568 20.8967 16.3295 20.9762 15.5701 20.9953L15.1524 21H8.84401L8.42627 20.9953L8.22954 20.9886C7.6073 20.961 7.30913 20.8804 7.00451 20.7175C6.68989 20.5492 6.44896 20.3083 6.28069 19.9937C6.10149 19.6586 6.02193 19.3313 6.00286 18.5719L5.99818 18.1542Z'
        fill='black'
      ></path>
    </svg>
  )
}

export default ModulesCopyIcon