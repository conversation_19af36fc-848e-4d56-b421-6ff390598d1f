import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EditLandingIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='EditLanding'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <mask id='path-1-inside-1_8548_116648' fill='white'>
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M20 6H8C6.89543 6 6 6.89543 6 8V20C6 21.1046 6.89543 22 8 22H20C21.1046 22 22 21.1046 22 20V8C22 6.89543 21.1046 6 20 6ZM8 4C5.79086 4 4 5.79086 4 8V20C4 22.2091 5.79086 24 8 24H20C22.2091 24 24 22.2091 24 20V8C24 5.79086 22.2091 4 20 4H8ZM18 10H10V14H18V10ZM10 8C8.89543 8 8 8.89543 8 10V14C8 15.1046 8.89543 16 10 16H18C19.1046 16 20 15.1046 20 14V10C20 8.89543 19.1046 8 18 8H10ZM9 18C8.44772 18 8 18.4477 8 19C8 19.5523 8.44771 20 9 20H19C19.5523 20 20 19.5523 20 19C20 18.4477 19.5523 18 19 18H9Z'
        />
      </mask>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M20 6H8C6.89543 6 6 6.89543 6 8V20C6 21.1046 6.89543 22 8 22H20C21.1046 22 22 21.1046 22 20V8C22 6.89543 21.1046 6 20 6ZM8 4C5.79086 4 4 5.79086 4 8V20C4 22.2091 5.79086 24 8 24H20C22.2091 24 24 22.2091 24 20V8C24 5.79086 22.2091 4 20 4H8ZM18 10H10V14H18V10ZM10 8C8.89543 8 8 8.89543 8 10V14C8 15.1046 8.89543 16 10 16H18C19.1046 16 20 15.1046 20 14V10C20 8.89543 19.1046 8 18 8H10ZM9 18C8.44772 18 8 18.4477 8 19C8 19.5523 8.44771 20 9 20H19C19.5523 20 20 19.5523 20 19C20 18.4477 19.5523 18 19 18H9Z'
        fill='#343B54'
      />
      <path
        d='M10 10V9H9V10H10ZM18 10H19V9H18V10ZM10 14H9V15H10V14ZM18 14V15H19V14H18ZM8 7H20V5H8V7ZM7 8C7 7.44772 7.44772 7 8 7V5C6.34315 5 5 6.34315 5 8H7ZM7 20V8H5V20H7ZM8 21C7.44772 21 7 20.5523 7 20H5C5 21.6569 6.34315 23 8 23V21ZM20 21H8V23H20V21ZM21 20C21 20.5523 20.5523 21 20 21V23C21.6569 23 23 21.6569 23 20H21ZM21 8V20H23V8H21ZM20 7C20.5523 7 21 7.44771 21 8H23C23 6.34315 21.6569 5 20 5V7ZM5 8C5 6.34315 6.34315 5 8 5V3C5.23858 3 3 5.23858 3 8H5ZM5 20V8H3V20H5ZM8 23C6.34315 23 5 21.6569 5 20H3C3 22.7614 5.23858 25 8 25V23ZM20 23H8V25H20V23ZM23 20C23 21.6569 21.6569 23 20 23V25C22.7614 25 25 22.7614 25 20H23ZM23 8V20H25V8H23ZM20 5C21.6569 5 23 6.34315 23 8H25C25 5.23858 22.7614 3 20 3V5ZM8 5H20V3H8V5ZM10 11H18V9H10V11ZM11 14V10H9V14H11ZM18 13H10V15H18V13ZM17 10V14H19V10H17ZM9 10C9 9.44772 9.44772 9 10 9V7C8.34315 7 7 8.34315 7 10H9ZM9 14V10H7V14H9ZM10 15C9.44772 15 9 14.5523 9 14H7C7 15.6569 8.34315 17 10 17V15ZM18 15H10V17H18V15ZM19 14C19 14.5523 18.5523 15 18 15V17C19.6569 17 21 15.6569 21 14H19ZM19 10V14H21V10H19ZM18 9C18.5523 9 19 9.44772 19 10H21C21 8.34315 19.6569 7 18 7V9ZM10 9H18V7H10V9ZM9 19V17C7.89543 17 7 17.8954 7 19H9ZM9 19H7C7 20.1046 7.89543 21 9 21V19ZM19 19H9V21H19V19ZM19 19V21C20.1046 21 21 20.1046 21 19H19ZM19 19H21C21 17.8954 20.1046 17 19 17V19ZM9 19H19V17H9V19Z'
        fill='#343B54'
        mask='url(#path-1-inside-1_8548_116648)'
      />
    </svg>
  )
}

export default EditLandingIcon