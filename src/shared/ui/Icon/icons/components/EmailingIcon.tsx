import { FC } from 'react'
import { IconsProps } from '../icons.d'

const EmailingIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Emailing'
      xmlns='http://www.w3.org/2000/svg'
      width='32'
      height='32'
      viewBox='0 0 32 32'
      className={className}
      fill='none'
    >
      <path
        d='M7.82214 12.5714L15.3348 12.5735C16.7033 12.593 17.4436 12.7536 18.212 13.1645C18.9698 13.5697 19.5714 14.1714 19.9768 14.9293C20.3876 15.6976 20.5481 16.438 20.5677 17.8064L20.5698 18.1095V23.0334C20.5698 24.5993 20.417 25.3904 19.9768 26.2136C19.5714 26.9714 18.9698 27.5732 18.212 27.9784C17.4436 28.3893 16.7033 28.5498 15.3348 28.5694L7.82214 28.5714C6.25621 28.5714 5.4651 28.4186 4.64193 27.9784C3.88403 27.5732 3.2824 26.9714 2.87707 26.2136C2.46618 25.4453 2.3057 24.7049 2.28613 23.3365V17.8064C2.3057 16.438 2.46618 15.6976 2.87707 14.9293C3.2824 14.1714 3.88403 13.5697 4.64193 13.1645C5.41022 12.7536 6.15059 12.593 7.51905 12.5735L7.82214 12.5714ZM18.2841 18.1874L15.6502 20.8213C14.2738 22.1533 12.8661 22.8572 11.4269 22.8572C10.0776 22.8572 8.75611 22.2385 7.45102 21.0528L7.19022 20.8081L4.56977 18.1886V23.0334L4.57512 23.5108C4.59691 24.3786 4.68784 24.7527 4.89265 25.1357C5.08494 25.4952 5.36029 25.7705 5.71986 25.9629C6.068 26.149 6.40876 26.2412 7.11989 26.2727L7.34473 26.2804L7.82214 26.2857H15.0317L15.5091 26.2804C16.377 26.2585 16.751 26.1677 17.134 25.9629C17.4936 25.7705 17.7689 25.4952 17.9612 25.1357C18.1474 24.7874 18.2394 24.4468 18.271 23.7356L18.2787 23.5108L18.2841 23.0334V18.1874ZM18.263 8.00002C20.1696 8.00002 21.1769 8.19923 22.1987 8.74567C23.1428 9.25061 23.8906 9.99841 24.3955 10.9425C24.9078 11.9005 25.1149 12.8457 25.1388 14.531L25.1412 14.8782V17.1218C25.1412 19.0284 24.942 20.0357 24.3955 21.0575C23.8906 22.0016 23.1428 22.7494 22.1987 23.2543C22.0376 23.3406 21.8768 23.4181 21.7126 23.4876V20.8293C21.9813 20.5919 22.2042 20.3082 22.38 19.9796C22.6605 19.455 22.7981 18.9312 22.8406 17.9272L22.8493 17.6658L22.8555 17.1218V14.8782L22.8493 14.3342C22.82 13.1561 22.6838 12.5887 22.38 12.0205C22.0881 11.4746 21.6666 11.0531 21.1208 10.7613C20.5554 10.4589 19.9909 10.3226 18.8251 10.2924L18.263 10.2857L13.4516 10.2873C12.0926 10.3026 11.4848 10.4356 10.876 10.7613C10.5472 10.9371 10.2636 11.1599 10.0262 11.4286L7.36769 11.429C7.43724 11.2649 7.51486 11.1039 7.60115 10.9425C8.10608 9.99841 8.85387 9.25061 9.79801 8.74567C10.7559 8.23338 11.7012 8.02628 13.3865 8.00238L13.7337 8.00002H18.263ZM15.0317 14.8572L7.57227 14.8584C6.53913 14.87 6.13473 14.9582 5.71986 15.18C5.51884 15.2876 5.34414 15.421 5.19556 15.5806L8.7931 19.1788C9.77856 20.1325 10.6566 20.5714 11.4269 20.5714C12.1422 20.5714 12.9504 20.1929 13.8405 19.3857L14.0474 19.1919L17.6582 15.5806C17.5097 15.421 17.3349 15.2876 17.134 15.18C16.751 14.9752 16.377 14.8844 15.5091 14.8625L15.0317 14.8572ZM22.8345 3.42859C24.741 3.42859 25.7483 3.6278 26.7701 4.17425C27.7142 4.67918 28.462 5.42698 28.9669 6.37111C29.4793 7.32903 29.6864 8.27428 29.7102 9.95962L29.7126 10.3068V12.5504C29.7126 14.4569 29.5134 15.4642 28.9669 16.4861C28.462 17.4302 27.7142 18.178 26.7701 18.6829C26.609 18.7692 26.4482 18.8466 26.2841 18.9161V16.2578C26.5528 16.0205 26.7756 15.7368 26.9514 15.4081C27.232 14.8836 27.3696 14.3598 27.4121 13.3558L27.4208 13.0944L27.4269 12.5504V10.3068L27.4208 9.76271C27.3914 8.58466 27.2553 8.01733 26.9514 7.44906C26.6595 6.90324 26.238 6.48172 25.6922 6.18982C25.1268 5.88746 24.5624 5.75117 23.3965 5.72094L22.8345 5.7143L18.023 5.71582C16.6641 5.73118 16.0563 5.8642 15.4474 6.18982C15.1187 6.36563 14.835 6.58845 14.5976 6.85713L11.9392 6.85763C12.0086 6.69342 12.0862 6.53245 12.1725 6.37111C12.6774 5.42698 13.4253 4.67918 14.3694 4.17425C15.3274 3.66195 16.2726 3.45485 17.9579 3.43095L18.3051 3.42859H22.8345Z'
        fill='#8E97AF'
      />
    </svg>
  )
}

export default EmailingIcon