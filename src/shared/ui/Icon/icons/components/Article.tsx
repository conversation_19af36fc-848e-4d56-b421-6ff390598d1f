import { FC } from 'react'
import { IconsProps } from '../icons.d'

const Article: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Article'
      className={className}
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <rect width='40' height='40' rx='8' fill='none' />
      <rect x='8' y='12' width='24' height='3' rx='1.5' fill='white' />
      <rect x='8' y='19' width='24' height='3' rx='1.5' fill='white' />
      <rect x='8' y='26' width='16' height='3' rx='1.5' fill='white' />
    </svg>
  )
}

export default Article