import { FC } from 'react'
import { IconsProps } from '../icons.d'

const SortIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Sort'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M12.25 6.5C12.6642 6.5 13 6.16421 13 5.75C13 5.33579 12.6642 5 12.25 5H7.75C7.33579 5 7 5.33579 7 5.75C7 6.16421 7.33579 6.5 7.75 6.5H12.25ZM14.25 10.75C14.6642 10.75 15 10.4142 15 10C15 9.58579 14.6642 9.25 14.25 9.25H5.75C5.33579 9.25 5 9.58579 5 10C5 10.4142 5.33579 10.75 5.75 10.75H14.25ZM16.25 15C16.6642 15 17 14.6642 17 14.25C17 13.8358 16.6642 13.5 16.25 13.5H3.75C3.33579 13.5 3 13.8358 3 14.25C3 14.6642 3.33579 15 3.75 15H16.25Z'
        fill='#343B54'
      />
    </svg>
  )
}

export default SortIcon