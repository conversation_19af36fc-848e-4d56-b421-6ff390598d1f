import { FC } from 'react'
import { IconsProps } from '../icons.d'

const FilterIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Filter'
      className={className}
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M3 5C3 4.72386 3.22386 4.5 3.5 4.5H16.5C16.7761 4.5 17 4.72386 17 5C17 5.27614 16.7761 5.5 16.5 5.5H3.5C3.22386 5.5 3 5.27614 3 5Z'
        fill='#343B54'
      />
      <path
        d='M3 10C3 9.72386 3.22386 9.5 3.5 9.5H16.5C16.7761 9.5 17 9.72386 17 10C17 10.2761 16.7761 10.5 16.5 10.5H3.5C3.22386 10.5 3 10.2761 3 10Z'
        fill='#343B54'
      />
      <path
        d='M3 15C3 14.7239 3.22386 14.5 3.5 14.5H16.5C16.7761 14.5 17 14.7239 17 15C17 15.2761 16.7761 15.5 16.5 15.5H3.5C3.22386 15.5 3 15.2761 3 15Z'
        fill='#343B54'
      />
      <circle cx='7' cy='5' r='2' fill='#343B54' />
      <circle cx='13' cy='10' r='2' fill='#343B54' />
      <circle cx='7' cy='15' r='2' fill='#343B54' />
    </svg>
  )
}

export default FilterIcon