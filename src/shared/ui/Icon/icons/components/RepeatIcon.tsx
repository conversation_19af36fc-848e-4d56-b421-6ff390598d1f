import { FC } from 'react'
import { IconsProps } from '../icons.d'

const RepeatIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='Repeat'
      className={className}
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M18.452 12.7883C18.3104 13.9479 17.858 15.0531 17.136 15.9839C16.2519 17.1237 15.0137 17.937 13.6165 18.2958C12.2193 18.6545 10.7424 18.5383 9.41854 17.9654C8.09465 17.3925 6.99895 16.3955 6.30401 15.1314L4.98955 15.854C5.84486 17.4098 7.19342 18.6369 8.82282 19.342C10.4522 20.0471 12.2699 20.1902 13.9895 19.7487C15.7092 19.3071 17.2331 18.3061 18.3212 16.9033C19.2374 15.7221 19.8009 14.3127 19.9561 12.8371L20.6494 13.5303C20.9423 13.8232 21.4171 13.8232 21.71 13.5303C22.0029 13.2374 22.0029 12.7626 21.71 12.4697L19.71 10.4697C19.4171 10.1768 18.9423 10.1768 18.6494 10.4697L16.6494 12.4697C16.3565 12.7626 16.3565 13.2374 16.6494 13.5303C16.9423 13.8232 17.4171 13.8232 17.71 13.5303L18.452 12.7883ZM4.0439 11.1629L3.35064 10.4697C3.05775 10.1768 2.58288 10.1768 2.28998 10.4697C1.99709 10.7626 1.99709 11.2374 2.28998 11.5303L4.28998 13.5303C4.58288 13.8232 5.05775 13.8232 5.35064 13.5303L7.35064 11.5303C7.64354 11.2374 7.64354 10.7626 7.35064 10.4697C7.05775 10.1768 6.58288 10.1768 6.28998 10.4697L5.54797 11.2117C5.68965 10.0521 6.14204 8.94684 6.86399 8.0161C7.74813 6.87628 8.98631 6.06295 10.3835 5.7042C11.7807 5.34546 13.2576 5.46169 14.5815 6.03459C15.9054 6.60749 17.0011 7.6045 17.696 8.8686L19.0105 8.14596C18.1551 6.59015 16.8066 5.36306 15.1772 4.65796C13.5478 3.95285 11.7301 3.8098 10.0105 4.25133C8.29084 4.69286 6.76693 5.69388 5.67876 7.09674C4.7626 8.27784 4.19915 9.68729 4.0439 11.1629Z'
        fill='#8E97AF'
      />
    </svg>
  )
}

export default RepeatIcon