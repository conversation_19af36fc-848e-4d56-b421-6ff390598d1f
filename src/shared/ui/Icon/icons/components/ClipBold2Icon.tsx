import { FC } from 'react'
import { IconsProps } from '../icons.d'

const ClipBold2Icon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='ClipBold2'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M14.1672 22.3598L22.8598 13.6673C25.0077 11.5194 25.0077 8.03697 22.8598 5.88909V5.88909C20.7119 3.7412 17.2295 3.7412 15.0816 5.88909L4.82857 16.1421C3.26647 17.7042 3.26647 20.2369 4.82857 21.799V21.799C6.39066 23.3611 8.92332 23.3611 10.4854 21.799L17.91 14.3744C18.8863 13.3981 18.8863 11.8152 17.91 10.8389V10.8389C16.9337 9.86257 15.3508 9.86257 14.3745 10.8389L8.85171 16.3616'
        stroke='#343B54'
        strokeWidth='2'
        strokeLinecap='round'
      />
    </svg>
  )
}

export default ClipBold2Icon