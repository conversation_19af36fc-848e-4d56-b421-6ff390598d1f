import { FC } from 'react'
import { IconsProps } from '../icons.d'

const SettingsBoldIcon: FC<IconsProps.Own> = props => {
  const { className } = props

  return (
    <svg
      data-testid='SettingsBold'
      className={className}
      width='28'
      height='28'
      viewBox='0 0 28 28'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <g clipPath='url(#clip0_469_1420)'>
        <path
          d='M14.69 2C16.172 2 17.4221 3.07977 17.6524 4.52605L17.6751 4.70131L17.788 5.833L17.9802 5.92598L18.4596 6.18107L18.8137 6.3937L19.002 6.51586L19.177 6.636L20.2152 6.16798C21.5076 5.58471 23.0132 5.98974 23.8459 7.09765L23.9506 7.24612L24.0473 7.40242L24.7373 8.59758C25.4758 9.87673 25.1703 11.4927 24.0422 12.4169L23.8905 12.5334L22.966 13.197L22.9904 13.5537L22.9989 13.7995L22.9998 14.1732L22.9896 14.4667L22.965 14.801L23.8903 15.4665C25.0375 16.2911 25.4408 17.7896 24.9064 19.0618L24.8277 19.2337L24.7373 19.4024L24.0473 20.5976C23.3075 21.879 21.7511 22.422 20.385 21.9026L20.2153 21.8321L19.177 21.363L18.8789 21.5646L18.6664 21.6971L18.5181 21.7853L18.349 21.8811L18.0906 22.0186L17.788 22.165L17.6751 23.2985C17.5422 24.6276 16.5519 25.6906 15.2446 25.9447L15.0644 25.9743L14.8806 25.9935L14.69 26H13.31C11.9135 26 10.7132 25.0386 10.3877 23.6685L10.3503 23.4875L10.3249 23.2987L10.211 22.166L9.94582 22.037L9.53393 21.8152L9.51126 21.8004L9.38565 21.7288L8.99801 21.4841L8.82199 21.363L7.78482 21.832C6.57576 22.3777 5.1708 22.0599 4.29452 21.0689L4.17301 20.9228L4.05917 20.7676L3.9527 20.5976L3.26268 19.4024C2.57018 18.203 2.79281 16.6969 3.80035 15.7255L3.94259 15.5965L4.10954 15.4666L5.033 14.801L5.01039 14.4665C5.00244 14.3111 4.99853 14.1555 4.99866 14.0017L5.00148 13.7673L5.0104 13.5333L5.034 13.198L4.10968 12.5335C3.03622 11.7618 2.60783 10.3925 3.02231 9.13911L3.08728 8.96092L3.16384 8.78477L3.26268 8.59758L3.9527 7.40242C4.64884 6.19668 6.07539 5.63725 7.42375 6.03476L7.59912 6.09206L7.78466 6.16791L8.82199 6.636L9.23876 6.36164L9.47799 6.218L9.51084 6.19797L9.65489 6.11675L9.90936 5.98144L10.211 5.834L10.3249 4.70149C10.4662 3.28831 11.5731 2.18567 12.952 2.02122L13.1333 2.00514L13.31 2H14.69ZM14.69 4H13.31C12.8357 4 12.4327 4.33171 12.3333 4.78495L12.3149 4.90067L12.1424 6.62326C12.1093 6.95352 11.9151 7.24294 11.6287 7.40014L11.5171 7.45272L11.323 7.52982L11.0936 7.62974L10.8195 7.76234L10.508 7.928L10.1942 8.12295L10.0637 8.20938L9.76088 8.42694L9.57135 8.57608C9.31087 8.78179 8.9678 8.84328 8.65573 8.74657L8.54037 8.70286L6.99866 8.00657L6.88679 7.96048C6.47207 7.81178 6.01101 7.95478 5.75061 8.30256L5.68475 8.40242L5.01493 9.56083L4.95502 9.67354C4.76823 10.0708 4.86532 10.5417 5.18465 10.8344L5.27721 10.9097L6.68273 11.9204C6.95584 12.1168 7.1096 12.4353 7.09834 12.7661L7.08629 12.8905L7.06093 13.0635L7.01986 13.4491L7.00085 13.8177L6.99866 14.0016L7.00082 14.1822L7.0074 14.3574L7.02376 14.5991L7.03881 14.755L7.08842 15.1234C7.13633 15.4518 7.01805 15.7796 6.77826 16.0016L6.68273 16.0796L5.31059 17.0653L5.20962 17.1433C4.87597 17.4286 4.77074 17.8978 4.94133 18.2958L4.99473 18.4024L5.6642 19.5636L5.72814 19.6663C5.97809 20.0291 6.43593 20.1821 6.85049 20.0516L6.96229 20.009L8.54037 19.2971C8.84184 19.1612 9.18826 19.1839 9.46719 19.3519L9.5684 19.4216L9.60788 19.4526L9.64069 19.4821L9.69229 19.5194L9.736 19.555L9.93639 19.7021L10.1883 19.8732L10.3848 19.9959L10.6088 20.1251L10.9533 20.3042L11.2525 20.4405L11.5171 20.5473C11.8255 20.67 12.0502 20.9364 12.1225 21.255L12.1424 21.3767L12.3105 23.062L12.3255 23.176C12.4026 23.6113 12.7576 23.9409 13.1902 23.9929L13.31 24L14.6523 24.0007L14.7672 23.9971C15.208 23.9637 15.5713 23.6433 15.666 23.2178L15.6851 23.0993L15.8576 21.3767C15.8907 21.0465 16.0849 20.7571 16.3713 20.5999L16.4829 20.5473L16.7463 20.441L16.9064 20.3703L17.3047 20.172L17.6151 19.996L17.8058 19.8771L17.8919 19.8205L18.1534 19.6373L18.4253 19.4266C18.6534 19.2449 18.9456 19.1742 19.225 19.2244L19.3438 19.2533L19.4596 19.2971L21.0379 20.0091C21.4701 20.2041 21.9739 20.0676 22.2509 19.6955L22.3152 19.5976L23.0053 18.4024C23.2424 17.9917 23.1566 17.4769 22.8138 17.1641L22.7228 17.0903L21.3173 16.0796C21.0478 15.8858 20.8942 15.5729 20.9013 15.2463L20.9116 15.1234L20.9418 14.9168L20.9801 14.5509L20.9962 14.2716L21 13.863L20.9926 13.6426L20.9758 13.3962L20.9516 13.1607L20.9108 12.8715C20.8649 12.5445 20.9834 12.2187 21.2222 11.998L21.3173 11.9204L22.7229 10.9096C23.108 10.6327 23.2416 10.1282 23.0579 9.70225L23.0053 9.59758L22.3152 8.40242C22.0781 7.99172 21.5893 7.80856 21.1471 7.94908L21.0377 7.99101L19.4596 8.70286C19.1571 8.83934 18.8093 8.81587 18.53 8.64643L18.4286 8.57608L18.2649 8.4466L18.0636 8.29786L17.8117 8.1268L17.6843 8.04675L17.3891 7.8739L17.0467 7.69579L16.7544 7.5624L16.4829 7.45272C16.1745 7.33 15.9497 7.06363 15.8775 6.74501L15.8576 6.62326L15.6851 4.9005C15.6379 4.42862 15.2677 4.06061 14.807 4.00679L14.69 4ZM14 9C16.7614 9 19 11.2386 19 14C19 16.7614 16.7614 19 14 19C11.2386 19 9 16.7614 9 14C9 11.2386 11.2386 9 14 9ZM14 11C12.3431 11 11 12.3431 11 14C11 15.6569 12.3431 17 14 17C15.6568 17 17 15.6569 17 14C17 12.3431 15.6568 11 14 11Z'
          fill='#343B54'
        />
      </g>
      <defs>
        <clipPath id='clip0_469_1420'>
          <rect width='28' height='28' fill='white' />
        </clipPath>
      </defs>
    </svg>
  )
}

export default SettingsBoldIcon