@mixin color($color, $exclude: none) {
  *[fill]:not([fill='none']):not([fill='#{$exclude}']) {
    fill: $color;
  }
  *[stroke]:not([stroke='none']):not([stroke='#{$exclude}']) {
    stroke: $color;
  }
}

.icon {
  transition: var(--transition);
  position: relative;

  .icon-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid var(--color-gray-30);
    border-top-color: var(--color-primary-90);
    animation: icon-spin 1s linear infinite;
  }

  @keyframes icon-spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  &.size {
    &--14 {
      height: 14px;
      width: 14px;
      svg {
        display: block;
        height: 14px;
        width: 14px;
      }
    }
    &--16 {
      height: 16px;
      width: 16px;
      svg {
        display: block;
        height: 16px;
        width: 16px;
      }
    }
    &--20 {
      height: 20px;
      width: 20px;
      svg {
        display: block;
        height: 20px;
        width: 20px;
      }
    }
    &--24 {
      height: 24px;
      width: 24px;
      svg {
        display: block;
        height: 24px;
        width: 24px;
      }
    }
    &--28 {
      height: 28px;
      width: 28px;
      svg {
        display: block;
        height: 28px;
        width: 28px;
      }
    }
    &--32 {
      height: 32px;
      width: 32px;
      svg {
        display: block;
        height: 32px;
        width: 32px;
      }
    }
    &--40 {
      height: 40px;
      width: 40px;
      svg {
        display: block;
        height: 40px;
        width: 40px;
      }
    }
    &--44 {
      height: 44px;
      width: 44px;
      svg {
        display: block;
        height: 44px;
        width: 44px;
      }
    }
    &--62 {
      height: 62px;
      width: 62px;
      svg {
        display: block;
        height: 62px;
        width: 62px;
      }
    }
  }
  &.color {
    &--gray90 {
      @include color(var(--color-gray-90));
    }
    &--gray70 {
      @include color(var(--color-gray-70));
    }
    &--gray80 {
      @include color(var(--color-gray-80));
    }
    &--gray60 {
      @include color(var(--color-gray-60));
    }
    &--primary {
      @include color(var(--color-primary-90));
    }
    &--red {
      @include color(var(--color-warn));
    }
    &--white {
      @include color(#ffffff);
    }
    &--green {
      @include color(#3dbc87);
    }
    &--self {
      fill: inherit;
      stroke: inherit;
    }
  }
  &.direction {
    &--up {
      transform: rotate(0deg);
    }
    &--right {
      transform: rotate(90deg);
    }
    &--down {
      transform: rotate(180deg);
    }
    &--left {
      transform: rotate(270deg);
    }
  }
  &.hoverable {
    cursor: pointer;
  }
  &.disabled {
    pointer-events: none;
    @include color(var(--color-gray-60));
  }
  &.padding {
    &--4 {
      svg {
        padding: 4px;
      }
    }
    &--8 {
      svg {
        padding: 8px;
      }
    }
    &--12 {
      svg {
        padding: 12px;
      }
    }
  }
}
