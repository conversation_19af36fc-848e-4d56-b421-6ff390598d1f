import { FC, ReactNode } from 'react'
import { TIconColor, IIconSize, IIconDirection } from './icon.d'
import styles from './icon.module.scss'
import classNamesBind from 'classnames/bind'

const cx = classNamesBind.bind(styles)

interface IconWrapperProps {
  children: ReactNode
  size?: IIconSize
  color?: TIconColor
  direction?: IIconDirection
  padding?: string
  disabled?: boolean
  hoverable?: boolean
  className?: string
  onClick?: (e: any) => void
  'data-testid'?: string
  [key: string]: any
}

export const IconWrapper: FC<IconWrapperProps> = ({
  children,
  size = '20',
  color = 'gray90',
  direction = 'up',
  padding,
  disabled = false,
  hoverable = false,
  className,
  onClick,
  'data-testid': dataTestid = 'Icon',
  ...otherProps
}) => {
  const handleClick = (e: any) => {
    if (disabled) return
    onClick && onClick(e)
  }

  return (
    <div
      className={cx(
        'icon',
        `size--${size}`,
        `color--${color}`,
        `direction--${direction}`,
        {
          [`padding--${padding}`]: padding,
          hoverable,
          disabled,
        },
        className
      )}
      onClick={handleClick}
      data-testid={dataTestid}
      {...otherProps}
    >
      {children}
    </div>
  )
}
