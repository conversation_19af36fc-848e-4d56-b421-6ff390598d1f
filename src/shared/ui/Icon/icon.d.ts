import { TIcons } from './icons/icons.d'

export const color = [
  'red',
  'primary',
  'gray60',
  'gray70',
  'gray90',
  'white',
  'gray80',
  'green',
  'self',
] as const
export type TIconColor = (typeof color)[number]

export const size = ['14', '16', '20', '24', '28', '32', '40', '44', '62'] as const
export type IIconSize = (typeof size)[number]

export const direction = ['left', 'right', 'up', 'down']
export type IIconDirection = (typeof direction)[number]

export const padding = ['4', '8', '12']
type IIconPadding = (typeof padding)[number]

export declare namespace IconProps {
  interface Own extends DataTest {
    className?: string
    icon?: TIcons
    size?: IIconSize
    direction?: IIconDirection
    padding?: IIconPadding
    disabled?: boolean
    color?: TIconColor
    hoverable?: boolean
  }

  type Props = Own & React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>
}

export {}
