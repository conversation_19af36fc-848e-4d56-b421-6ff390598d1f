import { Tooltip } from './tooltip'
import { renderTestScreen } from '@/shared/helpers/tests/render'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'

const mockedUsedNavigate = jest.fn()

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockedUsedNavigate,
}))

describe('[shared] [ui] Tooltip', () => {
  it('Компонент должен отрендериться', () => {
    const { getByTestId } = renderTestScreen(
      <Tooltip content='Текст подсказки'>Компонент</Tooltip>
    )

    const tooltip = getByTestId('Tooltip')
    const children = getByTestId('Tooltip.Children')

    expect(tooltip).toBeInTheDocument()
    expect(children).toBeInTheDocument()
    expect(children).toHaveTextContent('Компонент')
  })

  it('Компонент должен отобразить подсказку при наведение', async () => {
    const { getByTestId } = renderTestScreen(
      <Tooltip content='Текст подсказки'>Компонент</Tooltip>
    )

    const children = getByTestId('Tooltip.Children')

    await userEvent.hover(children)

    const content = getByTestId('Tooltip.Content')

    expect(children).toBeInTheDocument()
    expect(children).toHaveTextContent('Компонент')
    expect(content).toBeInTheDocument()
  })

  it('Компонент должен убрать подсказку при уходе наведения', async () => {
    const { getByTestId, queryByTestId } = renderTestScreen(
      <Tooltip content='Текст подсказки'>Компонент</Tooltip>
    )

    const children = getByTestId('Tooltip.Children')

    await userEvent.hover(children)

    const content = getByTestId('Tooltip.Content')

    expect(content).toBeInTheDocument()

    await userEvent.unhover(children)

    expect(queryByTestId('Tooltip.Content')).toBeNull()
  })

  it('Компонент должен отобразить подсказку по дефолтку, если initOpen = true', () => {
    const { getByTestId } = renderTestScreen(
      <Tooltip content='Текст подсказки' initOpen>Компонент</Tooltip>
    )

    const content = getByTestId('Tooltip.Content')

    expect(content).toBeInTheDocument()
  })

  describe('Компонент должен отобразиться', () => {
    it('снизу по дефолту', () => {
      const { getByTestId } = renderTestScreen(
        <Tooltip content='Текст подсказки' initOpen>Компонент</Tooltip>
      )

      const content = getByTestId('Tooltip.Content')
      const contentWrapper = getByTestId('Tooltip.ContentWrapper')

      expect(content).toBeInTheDocument()
      expect(contentWrapper).toHaveClass('tooltip-bottom')
    })

    it('снизу если передан position = bottom', () => {
      const { getByTestId } = renderTestScreen(
        <Tooltip content='Текст подсказки' initOpen position='bottom'>Компонент</Tooltip>
      )

      const content = getByTestId('Tooltip.Content')
      const contentWrapper = getByTestId('Tooltip.ContentWrapper')

      expect(content).toBeInTheDocument()
      expect(contentWrapper).toHaveClass('tooltip-bottom')
    })

    it('справа если передан position = right', () => {
      const { getByTestId } = renderTestScreen(
        <Tooltip content='Текст подсказки' initOpen position='right'>Компонент</Tooltip>
      )

      const content = getByTestId('Tooltip.Content')
      const contentWrapper = getByTestId('Tooltip.ContentWrapper')

      expect(content).toBeInTheDocument()
      expect(contentWrapper).toHaveClass('tooltip-right')
    })

    it('сверху если передан position = top', () => {
      const { getByTestId } = renderTestScreen(
        <Tooltip content='Текст подсказки' initOpen position='top'>Компонент</Tooltip>
      )

      const content = getByTestId('Tooltip.Content')
      const contentWrapper = getByTestId('Tooltip.ContentWrapper')

      expect(content).toBeInTheDocument()
      expect(contentWrapper).toHaveClass('tooltip-top')
    })

    it('слева если передан position = left', () => {
      const { getByTestId } = renderTestScreen(
        <Tooltip content='Текст подсказки' initOpen position='left'>Компонент</Tooltip>
      )

      const content = getByTestId('Tooltip.Content')
      const contentWrapper = getByTestId('Tooltip.ContentWrapper')

      expect(content).toBeInTheDocument()
      expect(contentWrapper).toHaveClass('tooltip-left')
    })
  })
})
