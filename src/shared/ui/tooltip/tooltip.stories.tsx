import type { Meta, StoryObj } from '@storybook/react'

import { Tooltip } from './tooltip'
import QuestionIcon from '../Icon/icons/components/QuestionIcon'
import { IconWrapper } from '../Icon/IconWrapper'

const meta = {
  title: 'UI/Tooltip',
  component: Tooltip,
} satisfies Meta<typeof Tooltip>

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    content: 'Текст тултипа',
    children: (
      <IconWrapper size='20'>
        <QuestionIcon />
      </IconWrapper>
    ),
  },
}

export const DefaultOpen: Story = {
  args: {
    initOpen: true,
    content: 'Текст тултипа',
    children: (
      <IconWrapper size='20'>
        <QuestionIcon />
      </IconWrapper>
    ),
  },
}

export const WithTextComponent: Story = {
  args: {
    content: 'Текст тултипа',
    children: 'Текст на который можно навестись',
  },
}

export const OpenWithTextComponent: Story = {
  args: {
    initOpen: true,
    content: 'Текст тултипа',
    children: 'Текст на который можно навестись',
  },
}

export const OpenWithRightPosition: Story = {
  args: {
    initOpen: true,
    content: 'Текст тултипа',
    children: (
      <IconWrapper size='20'>
        <QuestionIcon />
      </IconWrapper>
    ),
    position: 'right',
  },
  decorators: [
    Story => (
      <div style={{ width: 'max-content' }}>
        <Story />
      </div>
    ),
  ],
}

export const OpenWithLeftPosition: Story = {
  args: {
    initOpen: true,
    content: 'Текст тултипа',
    children: (
      <IconWrapper size='20'>
        <QuestionIcon />
      </IconWrapper>
    ),
    position: 'left',
  },
  decorators: [
    Story => (
      <div style={{ width: 'max-content', paddingLeft: '100px' }}>
        <Story />
      </div>
    ),
  ],
}

export const OpenWithTopPosition: Story = {
  args: {
    initOpen: true,
    content: 'Текст тултипа',
    children: (
      <IconWrapper size='20'>
        <QuestionIcon />
      </IconWrapper>
    ),
    position: 'top',
  },
  decorators: [
    Story => (
      <div style={{ width: 'max-content', paddingTop: '50px' }}>
        <Story />
      </div>
    ),
  ],
}
