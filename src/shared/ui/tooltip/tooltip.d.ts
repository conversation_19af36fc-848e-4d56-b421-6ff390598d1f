import { ReactNode } from 'react'

export declare namespace TooltipProps {
  type TooltipPositionsVariants =
    | 'top'
    | 'bottom'
    | 'left'
    | 'right'
    | 'top-left'
    | 'top-right'
    | 'bottom-left'
    | 'bottom-right'

  interface Own {
    tooltipClassname?: string
    content: ReactNode
    position?: TooltipPositionsVariants
    initOpen?: boolean
    className?: string
    /**
     * Задержка в миллисекундах
     * @default 0
     */
    delay?: number
  }

  type Props = Own & DataTest
}

export {}
