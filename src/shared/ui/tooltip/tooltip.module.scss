.tooltip {
  align-items: center;

  background-color: var(--color-gray-100);
  border-radius: 4px;

  display: flex;
  justify-content: center;
  opacity: 0.7;
  padding: 4px 6px;
  position: absolute;

  white-space: normal;
  z-index: 100;
}

.tooltip-wrapper {
  align-items: center;

  display: flex;
  justify-content: center;
  position: relative;
}

.tooltip-content {
  color: var(--color-surface);
  font: var(--font-caption-2-normal);
}

.tooltip.tooltip-top {
  bottom: calc(100% + 6px);
}

.tooltip.tooltip-right {
  left: calc(100% + 12px);
  top: 0;
}

.tooltip.tooltip-bottom {
  top: calc(100% + 6px);
}

.tooltip.tooltip-left {
  right: calc(100% + 12px);
  top: 0;
}

.tooltip.tooltip-bottom-left {
  top: calc(100% + 6px);
  left: 0;
}

.tooltip.tooltip-bottom-right {
  top: calc(100% + 6px);
  right: 0;
}

.tooltip.tooltip-top-left {
  bottom: calc(100% + 6px);
  left: 0;
}

.tooltip.tooltip-top-right {
  bottom: calc(100% + 6px);
  right: 0;
}
