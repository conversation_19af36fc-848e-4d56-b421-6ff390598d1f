import { FC, PropsWithChildren, useEffect, useRef, useState } from 'react'
import styles from './tooltip.module.scss'
import classNamesBind from 'classnames/bind'
import { TooltipProps } from './tooltip.d'

const cx = classNamesBind.bind(styles)

export const Tooltip: FC<PropsWithChildren<TooltipProps.Props>> = props => {
  const {
    children,
    tooltipClassname,
    content,
    position = 'bottom',
    initOpen = false,
    className,
    'data-testid': dataTestid = 'Tooltip',
    delay = 0,
  } = props

  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const [isHovered, setIsHovered] = useState(initOpen)

  const handleMouseEnter = () => {
    if (timerRef.current) clearTimeout(timerRef.current)
    if (delay) {
      timerRef.current = setTimeout(() => {
        setIsHovered(true)
      }, delay)
    } else {
      setIsHovered(true)
    }
  }

  const handleMouseLeave = () => {
    timerRef.current && clearTimeout(timerRef.current)
    setIsHovered(false)
  }

  useEffect(
    () => () => {
      timerRef.current && clearTimeout(timerRef.current)
    },
    [],
  )

  return (
    <div className={cx('tooltip-wrapper') + ` ${className}`} data-testid={dataTestid}>
      <div
        className={cx('tooltip-children')}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        data-testid={`${dataTestid}.Children`}
      >
        {children}
      </div>

      {isHovered && (
        <div
          className={cx('tooltip', `tooltip-${position}`, tooltipClassname)}
          data-testid={`${dataTestid}.ContentWrapper`}
        >
          <div className={cx('tooltip-content')} data-testid={`${dataTestid}.Content`}>
            {content}
          </div>
        </div>
      )}
    </div>
  )
}
