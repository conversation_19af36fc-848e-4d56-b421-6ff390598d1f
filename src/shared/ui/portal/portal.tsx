import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";

type PortalProps = {
  container?: Element | null;
  children?: React.ReactNode;
};

export const Portal: React.FC<PortalProps> = ({ container, children }) => {
  const [portalContainerRef, setPortalContainerRef] = useState<Element | null>(
    null
  );

  useEffect(() => {
    if (!container) {
      const portalContainer = document.createElement("div");
      document.body.appendChild(portalContainer);
      setPortalContainerRef(portalContainer);

      return;
    }

    setPortalContainerRef(container);

    return () => {
      if (!container) {
        setPortalContainerRef(null);
      }
    };
  }, [container]);

  if (!portalContainerRef) return null;

  return createPortal(children, portalContainerRef);
};
