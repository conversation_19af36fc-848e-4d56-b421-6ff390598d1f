.wrapper {
  background-color: var(--color-surface);
  border-radius: 12px;
  bottom: 0;
  box-shadow: var(--shadow-border);
  box-shadow:
    0 30px 84px rgba(19, 10, 46, 0.08),
    0 8px 32px rgba(19, 10, 46, 0.07),
    0 3px 14px rgba(19, 10, 46, 0.03),
    0 1px 3px rgba(19, 10, 46, 0.13);
  position: absolute;
  right: 0;
  transform: translateX(101%);
  z-index: 20;
}

.wrapper {
  display: flex;
  flex-direction: column;
  height: auto;
  padding: 24px;

  pointer-events: all;
  width: 328px;
}

.header {
  align-items: center;
  display: flex;
  height: 36px;
  justify-content: center;
  width: 100%;
}

.submit {
  margin-left: auto;
}

.header-date {
  color: var(--color-primary);
  font: var(--font-text-2-demibold);
  text-align: center;

  user-select: none;
  width: 112px;
}

.row {
  display: flex;
  height: 40px;
  width: 100%;
}

.cell {
  align-items: center;

  border-radius: 12px;

  color: var(--color-gray-100);
  cursor: pointer;
  display: flex;
  flex: 1 0;
  font: var(--font-text-2-normal);
  height: 100%;
  justify-content: center;

  transition: 200ms ease;

  user-select: none;

  &.header-cell {
    font: var(--font-text-2-demibold);
    cursor: default;
  }

  &.is-today {
    color: var(--color-primary);
  }

  &.is-weekends {
    color: #ff586b;
  }

  &:not(.is-current-month):not(.header-cell) {
    color: var(--color-gray-70);
    &.is-weekends {
      color: #ffaaba;
    }

    &.is-selected {
      background: var(--color-primary-40);
      color: var(--color-surface);
    }
  }

  &:not(.header-cell):not(.is-selected) {
    &:hover {
      background: var(--color-gray-40);
    }
    &:active {
      transform: scale(0.95);
    }
  }

  &.is-selected {
    background: var(--color-primary);
    color: var(--color-surface);
    font: var(--font-text-2-medium);
  }

  &.is-disabled {
    color: var(--color-gray-60) !important;

    pointer-events: none !important;
  }
}



.select-text {
  text-overflow: initial !important;
  overflow: initial !important;
}
