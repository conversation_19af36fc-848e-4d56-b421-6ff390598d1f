import { FC, useCallback, useEffect, useRef, useState } from 'react'
import styles from './date-picker.module.scss'
import classNamesBind from 'classnames/bind'
import { DatePickerProps } from './date-picker.d'
import { ButtonIcon, Button } from '@/shared/ui'
import {
  addDays,
  addMonths,
  format,
  isAfter,
  isBefore,
  isMonday,
  isSameDay,
  isSameMonth,
  isWeekend,
  parseISO,
  previousMonday,
  startOfMonth,
  subMonths,
  isToday as isTodayDateFNS,
  setHours,
  setMinutes,
  endOfDay,
} from '@/shared/helpers/date'
import { useEvent } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'
import { useLocale } from '@/shared/hooks/use-locale'

const cx = classNamesBind.bind(styles)

export const transform = (
  value: string | Date | null | undefined,
  formatString: string,
  locale?: string,
): string | null => {
  if (value === null || value === undefined) return null

  const date = typeof value === 'string' ? parseISO(value) : value

  return format(date, formatString, locale)
}

const DAYS_IN_WEEK = 7
const ROWS_COUNT = 6

export const DatePicker: FC<DatePickerProps.Props> = props => {
  const {
    className,
    min = null,
    max = null,
    setActive,
    active,
    selected: selectedValue = null,
    handleSelect,
    bottomSlot,
  } = props
  const locale = useLocale()

  const { t } = useTranslation('components__date-picker')

  const weekDays = [
    t('monday_short'),
    t('tuesday_short'),
    t('wednesday_short'),
    t('thursday_short'),
    t('friday_short'),
    t('saturday_short'),
    t('sunday_short'),
  ]

  const today = new Date()

  const [selected, setSelected] = useState<Date | null>(props.selected)
  const [currentMonth, setCurrentMonth] = useState<Date>(today)

  useEffect(() => {
    setSelected(props.selected)
    if (props.selected) setCurrentMonth(new Date(props.selected))
  }, [props.selected])

  useEffect(() => {
    const now = new Date()
    const defaultValue = min && min > now ? min : now
    const value = selectedValue || defaultValue

    setSelected(value)
    setCurrentMonth(value)
  }, [active])

  const wrapper = useRef<HTMLDivElement>(null)

  const handleOutsideClick = useCallback(
    (e: Event) => {
      if (!wrapper.current) return
      if (!active) return

      if (e.composedPath().indexOf(wrapper.current) === -1) {
        setActive(false)
      }
    },
    [active, setActive],
  )

  useEvent('click', handleOutsideClick, window)

  const onPrev = () => {
    setCurrentMonth(subMonths(currentMonth, 1))
  }

  const onNext = () => {
    setCurrentMonth(addMonths(currentMonth, 1))
  }

  const isSelected = (date: Date): boolean => {
    return !!selected && isSameDay(selected, date)
  }

  const isDisabled = (date: Date) => {
    let disabled = false
    let newDate = endOfDay(new Date(date))

    if (newDate) {
      newDate = setHours(newDate)
      newDate = setMinutes(newDate)
    }

    if (min) {
      disabled = disabled || isBefore(newDate, min)
    }
    if (max) {
      disabled = disabled || isAfter(newDate, max)
    }

    return disabled
  }

  const onSubmit = () => {
    let date = selected

    if (date) {
      date = setHours(date)
      date = setMinutes(date)
    }

    handleSelect(date)
    setActive(false)
  }

  const getRows = (): Date[][] => {
    const firstDayOfMonth = startOfMonth(currentMonth)
    const firstDayOfWeek = getPreviosFirstDayOfWeek(firstDayOfMonth)
    return Array.from({ length: ROWS_COUNT }).map((_, rowIndex) => {
      return Array.from({ length: DAYS_IN_WEEK }).map((__, dayOfWeek) => {
        const daysOffset = rowIndex * DAYS_IN_WEEK + dayOfWeek
        return addDays(firstDayOfWeek, daysOffset)
      })
    })
  }

  const getPreviosFirstDayOfWeek = (date: Date): Date => {
    if (isMonday(date)) return date

    return previousMonday(date)
  }

  const onSelect = (date: Date) => {
    setSelected(date)
    if (!isSameMonth(currentMonth, date)) {
      setCurrentMonth(date)
    }
    handleSelect(date)
  }

  if (!active) {
    return <></>
  }

  const rows = getRows()

  return (
    <div className={cx('wrapper', className)} ref={wrapper}>
      <div className={cx('header')}>
        <ButtonIcon type='button' icon='chevroneMedium' direction='down' onClick={onPrev} />
        <div className={cx('header-date')}>{transform(currentMonth, 'LLLL yyyy', locale)}</div>
        <ButtonIcon type='button' icon='chevroneMedium' onClick={onNext} />
        {!bottomSlot && (
          <Button size='medium' className={cx('submit')} onClick={onSubmit} type='button'>
            {t('submit_button')}
          </Button>
        )}
      </div>
      <div className={cx('row')}>
        {weekDays.map((d, i) => {
          return (
            <div
              key={`week-day-${d}`}
              className={cx('cell', 'header-cell', {
                'is-weekends': i > 4,
              })}
            >
              {d}
            </div>
          )
        })}
      </div>
      {rows.map((r, i) => {
        return (
          <div className={cx('row')} key={`row-${i}`}>
            {r.map(c => {
              const isTodayClass: boolean = isTodayDateFNS(c)
              const isSelectedClass: boolean = isSelected(c)
              const isCurrentMonthClass: boolean = isSameMonth(currentMonth, c)
              const isWeekendsClass: boolean = isWeekend(c)
              const isDisabledClass: boolean = isDisabled(c)

              return (
                <div
                  className={cx('cell', {
                    'is-today': isTodayClass,
                    'is-selected': isSelectedClass,
                    'is-current-month': isCurrentMonthClass,
                    'is-weekends': isWeekendsClass,
                    'is-disabled': isDisabledClass,
                  })}
                  onClick={() => onSelect(c)}
                  key={`cell-${i}-${i}-${c}`}
                >
                  {transform(c, 'd')}
                </div>
              )
            })}
          </div>
        )
      })}
      {bottomSlot}
    </div>
  )
}
