import { ReactNode } from "react";
import { ModalProps } from "react-app/new/components";

export declare namespace DatePickerProps {
  interface Own extends Omit<ModalProps.Own, 'children'> {
    className?: string
    selected: Date | null
    handleSelect: (date: Date | null) => void
    min?: Date | null
    max?: Date | null
    withoutTime?: boolean
    bottomSlot?: ReactNode;
  }

  type Props = Own;
}

export {};
