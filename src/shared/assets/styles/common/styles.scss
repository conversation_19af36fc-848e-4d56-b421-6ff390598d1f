.jodit-react-container {
  height: 100% !important;
}

.cke_inner {
  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  em {
    font-style: italic;
  }

  strong {
    font-weight: bold;
  }

  u {
    text-decoration: underline;
  }

  sup {
    font-size: smaller;
    vertical-align: super;
  }

  sub {
    font-size: smaller;
    vertical-align: bottom;
  }
}

.jodit-container {
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100% !important;

  max-height: 600px;

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  em {
    font-style: italic;
  }

  strong {
    font-weight: bold;
  }

  u {
    text-decoration: underline;
  }

  // pre {
  //   display: block;
  //   font-family: monospace;
  //   white-space: pre;
  //   margin: 1em 0px;
  // }

  sup {
    font-size: smaller;
    vertical-align: super;
  }

  sub {
    font-size: smaller;
    vertical-align: bottom;
  }

  .jodit-workplace {
    overflow-y: scroll;
  }
}

:global(.BrainhubCarousel__container),
:global(.BrainhubCarousel),
:global(.BrainhubCarousel__trackContainer),
:global(.BrainhubCarousel__track),
:global(.BrainhubCarouselItem) {
  height: 100%;
}
:global(.BrainhubCarousel__trackContainer) {
  width: 100%;
}

html {
  background-color: var(--color-background);
  height: 100%;
  min-height: 100%;
}

body {
  display: flex;
  flex-direction: column;

  min-height: 100%;
  width: 100%;
  font-family: 'TT Norms Pro', 'Segoe UI', 'SF Pro Display', 'Roboto', sans-serif;
}

.error-text {
  color: var(--color-statistics-bad, #ff8577);
  font: var(--font-caption-2-normal);
}

.empty-text {
  font: var(--font-caption-1-normal);
}

.scrollbar {
  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: #e1e4eb;
    border-radius: 8px;
  }
}

edu-app-root {
  display: flex;
  flex: 1 1;
  flex-direction: column;
  min-height: 0;
}

* {
  box-sizing: border-box;
}
// ====================================================================================================

a,
a:hover,
a:focus,
a:active {
  color: inherit;

  cursor: pointer;
  text-decoration: none;
}

.react-page {
  display: flex;
  margin: auto 0;
  min-height: 100%;
  //? Should be here?
  padding: 40px 0;
}

.react-404-bg {
  background-image: url('./assets/legacy_images/404.png');
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite; /* Продолжительность, тип анимации и бесконечное воспроизведение */
}
