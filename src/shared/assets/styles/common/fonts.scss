@font-face {
  font-display: block;
  font-family: 'GothamPro-Medium';
  font-style: normal;
  font-weight: normal;
  src: url('../../fonts/GothamPro-Medium.eot');
  src:
    url('../../fonts/GothamPro-Medium.eot?#iefix') format('embedded-opentype'),
    url('../../fonts/GothamPro-Medium.woff') format('woff'),
    url('../../fonts/GothamPro-Medium.ttf') format('truetype');
}

@font-face {
  font-display: block;
  font-family: 'GothamPro-Bold';
  font-style: normal;
  font-weight: normal;
  src: url('../../fonts/GothamPro-Bold.eot');
  src:
    url('../../fonts/GothamPro-Bold.eot?#iefix') format('embedded-opentype'),
    url('../../fonts/GothamPro-Bold.woff') format('woff'),
    url('../../fonts/GothamPro-Bold.ttf') format('truetype');
}

@font-face {
  font-display: block;
  font-family: 'GothamPro';
  font-style: normal;
  font-weight: normal;
  src: url('../../fonts/GothamPro.eot');
  src:
    url('../../fonts/GothamPro.eot?#iefix') format('embedded-opentype'),
    url('../../fonts/GothamPro.woff') format('woff'),
    url('../../fonts/GothamPro.ttf') format('truetype');
}

@font-face {
  font-display: block;
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 600;
  src:
    url('../../fonts/Montserrat-SemiBold.woff2') format('woff2'),
    url('../../fonts/Montserrat-SemiBold.woff') format('woff');
}

@font-face {
  font-display: block;
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: normal;
  src:
    url('../../fonts/Montserrat-Regular.woff2') format('woff2'),
    url('../../fonts/Montserrat-Regular.woff') format('woff');
}

@font-face {
  font-display: block;
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: bold;
  src:
    url('../../fonts/Montserrat-Bold.woff2') format('woff2'),
    url('../../fonts/Montserrat-Bold.woff') format('woff');
}

@font-face {
  font-display: block;
  font-family: 'Montserrat';
  font-style: normal;
  font-weight: 500;
  src:
    url('../../fonts/Montserrat-Medium.woff2') format('woff2'),
    url('../../fonts/Montserrat-Medium.woff') format('woff');
}

/* TT Norms Pro */

@font-face {
  font-display: block;
  font-family: 'TT Norms Pro';
  font-style: normal;
  font-weight: 400;
  src: url('../../fonts/TTNormsPro-Normal.eot');
  src:
    url('../../fonts/TTNormsPro-Normal.eot?#iefix') format('embedded-opentype'),
    url('../../fonts/TTNormsPro-Normal.woff2') format('woff2'),
    url('../../fonts/TTNormsPro-Normal.woff') format('woff'),
    url('../../fonts/TTNormsPro-Normal.ttf') format('truetype');
}

@font-face {
  font-display: fallback;
  font-family: 'TT Norms Pro';
  font-style: normal;
  font-weight: 500;
  src: url('../../fonts/TTNormsPro-Md.eot');
  src:
    url('../../fonts/TTNormsPro-Md.eot?#iefix') format('embedded-opentype'),
    url('../../fonts/TTNormsPro-Md.woff2') format('woff2'),
    url('../../fonts/TTNormsPro-Md.woff') format('woff'),
    url('../../fonts/TTNormsPro-Md.ttf') format('truetype');
}

@font-face {
  font-display: fallback;
  font-family: 'TT Norms Pro';
  font-style: normal;
  font-weight: 600;
  src: url('../../fonts/TTNormsPro-DmBd.eot');
  src:
    url('../../fonts/TTNormsPro-DmBd.eot?#iefix') format('embedded-opentype'),
    url('../../fonts/TTNormsPro-DmBd.woff2') format('woff2'),
    url('../../fonts/TTNormsPro-DmBd.woff') format('woff'),
    url('../../fonts/TTNormsPro-DmBd.ttf') format('truetype');
}

@font-face {
  font-family: 'Consolas';
  src: url('../../fonts/Consolas.ttf') format('truetype');
}
