@mixin mobile {
  @media screen and (max-width: 768px) {
    @content;
  }
}

@mixin mobile_portrait {
  @media screen and (max-width: 768px) and (orientation: portrait) {
    @content;
  }
}

$mq-xl: 1400px;
$mq-l: 1000px;
$mq-m: 767px;
$mq-s: 400px;

@mixin mqS {
  @media (max-width: $mq-s) {
    @content;
  }
}

@mixin mqM {
  @media (max-width: $mq-m) {
    @content;
  }
}

@mixin mqL {
  @media (max-width: $mq-l) {
    @content;
  }
}

@mixin mqXl {
  @media (max-width: $mq-xl) {
    @content;
  }
}
