@mixin color($color, $exclude: none) {
  // *[fill] {
  //     transition: fill 300ms ease;
  // }
  // *[stroke] {
  //     transition: stroke 300ms ease;
  // }
  transition: var(--transition);

  *[fill]:not([fill="none"]):not([fill="#{$exclude}"]) {

    fill: $color;
    transition: var(--transition);
  }
  *[stroke]:not([stroke="none"]):not([stroke="#{$exclude}"]) {

    stroke: $color;
    transition: var(--transition);
  }
}
