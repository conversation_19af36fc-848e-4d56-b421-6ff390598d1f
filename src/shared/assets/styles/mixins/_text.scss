@mixin title($size) {
  font-family: var(--font-title);
  font-size: $size;
  font-weight: bold;
}

@mixin main($size) {
  font-family: var(--font-main);
  font-size: $size;
  font-weight: normal;
}

//! Используется для ограничения текста - максимум $lines строк
@mixin max-lines($lines) {
  -webkit-box-orient: vertical;

  display: -webkit-box;
  -webkit-line-clamp: $lines;

  max-height: calc(1.2em * $lines);
  overflow: hidden;

  @supports (-webkit-line-clamp: 3) {
    max-height: initial;
  }
}
