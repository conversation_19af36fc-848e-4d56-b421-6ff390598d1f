@mixin color {
  &-white,
  &-WHITE {
    @content (var(--white));
  }
  &-red,
  &-RED {
    @content (var(--color-statistics-bad));
  }
  &-green,
  &-GREEN {
    @content (var(--color-statistics-good));
  }
  &-blue,
  &-BLUE {
    @content (var(--color-statistics-complementary));
  }
  &-yellow,
  &-YELLOW {
    @content (var(--color-statistics-warning));
  }
  &-gray,
  &-GRAY {
    @content (var(--grey));
  }
  &-light_gray,
  &-LIGHT_GRAY {
    @content (var(--silver));
  }
  &-dark_gray,
  &-DARK_GRAY {
    @content (var(--dark-opacity));
  }
}
