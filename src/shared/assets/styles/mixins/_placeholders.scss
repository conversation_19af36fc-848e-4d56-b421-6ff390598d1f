@mixin simple {

  animation: skeleton_loading 10s ease-in-out infinite;
  background: none;

  background-image: linear-gradient(
    270deg,
    var(--grey),
    var(--grey),
    var(--silver),
    var(--grey),
    var(--grey)
  );
  background-size: 400% 100%;
  border-radius: 4px;
  opacity: 0.5;
}
@mixin rounded {
  @include simple();
  border-radius: 50%;
}

@keyframes skeleton_loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}
