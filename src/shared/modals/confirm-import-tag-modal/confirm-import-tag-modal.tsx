import { FC } from 'react'
import styles from './confirm-import-tag-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ConfirmImportTagModalProps } from './confirm-import-tag-modal.d'
import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__import-employess'

export const ConfirmImportTagModal: FC<ConfirmImportTagModalProps.Props> = props => {
  const { active, setActive, onSubmit } = props

  const handleClose = () => {
    setActive(false)
  }

  const { t } = useTranslation(TRANSLATION_FILE)

  return (
    <Modal className={cx('wrapper')} active={active} setActive={setActive}>
      <div className={cx('title')}>{t('confirm_title')}</div>
      <div className={cx('buttonWrapper')}>
        <Button color='gray' size='big' fullWidth onClick={handleClose}>
          {t('commons:cancel')}
        </Button>
        <Button size='big' color='green' fullWidth onClick={onSubmit}>
          {t('confirm_button')}
        </Button>
      </div>
    </Modal>
  )
}
