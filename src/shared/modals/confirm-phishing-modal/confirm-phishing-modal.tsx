import { FC } from 'react'
import styles from './confirm-phishing-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ConfirmModalProps } from './confirm-phishing-modal.d'
import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const ConfirmPhishingModal: FC<ConfirmModalProps.Props> = props => {
  const {
    className,
    id,
    onClose,
    active,
    setActive,
    title,
    text,
    onCloseIDs,
    ids,
    onCloseWithoutData,
  } = props

  const handleClose = (isClose?: boolean, id?: UUID | null, ids?: UUID[] | null) => {
    onCloseWithoutData && onCloseWithoutData(!!isClose)
    onCloseIDs && onCloseIDs(ids)
    onClose && onClose(id)
    setActive(false)
  }

  const { t } = useTranslation()

  return (
    <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
      <div className={cx('title')}>{title}</div>
      {text && <div className={cx('subtitle')}>{text}</div>}

      <div className={cx('buttonWrapper')}>
        <Button color='gray' size='big' fullWidth onClick={() => handleClose(true)}>
          {t('commons:cancel')}
        </Button>
        <Button size='big' color='green' fullWidth onClick={() => handleClose(false, id, ids)}>
          {t('commons:save')}
        </Button>
      </div>
    </Modal>
  )
}
