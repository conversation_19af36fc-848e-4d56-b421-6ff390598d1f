import { UUID } from 'react-app/new/types/IUser'

export declare namespace ConfirmModalProps {
  interface Own {
    className?: string

    id?: UUID | null
    ids?: null | UUID[]
    onCloseWithoutData?: (isClose: boolean) => Promise<void>
    onClose?: (id?: UUID | null) => Promise<void>
    onCloseIDs?: (ids?: UUID[] | null) => Promise<void>
    title?: string
    text?: string
    active: boolean
    setActive: React.Dispatch<React.SetStateAction<boolean>>
  }

  type Props = Own
}
