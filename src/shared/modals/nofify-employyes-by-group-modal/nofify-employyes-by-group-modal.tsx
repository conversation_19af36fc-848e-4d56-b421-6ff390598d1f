import { useCallback, useEffect, useMemo, useState } from 'react'
import { useForm } from 'react-hook-form'
import z from 'zod'
import { v4 as uuid } from 'uuid'
import { useTranslation } from 'react-i18next'

import { useNotification } from '@/shared/contexts/notifications'
import { NotifyEmployeesForm } from '@/shared/components/notify-employees-form'
import { NofifyEmployyes } from '@/shared/modals/nofify-employyes'
import { organizationAPI, organizationGatewayAPI } from '@/entities/organization'
import { TGroup } from '@/shared/types/store/groups'
import { Filters, ProgressGroup } from './nofify-employyes-by-group-modal.d'
import { useUserOrganizationId } from '@/entities/employee'

const getNotifySchema = ({ educationMsg }: { educationMsg: string }) =>
  z.object({
    education: z.string().array().min(1, { message: educationMsg }),
  })

type NofifyEmployyesByGroupModalProps = {
  activeProgressGroup: string | null
  setActiveProgressGroup: (group: string | null) => void
  groups_chart: { title: string; color: string; value: number | undefined; group: string }[]
  groups_chart_mini: { normal: number | undefined; behind: number | undefined }
}

const NofifyEmployyesByGroupModal = ({
  activeProgressGroup,
  setActiveProgressGroup,
  groups_chart,
}: NofifyEmployyesByGroupModalProps) => {
  const { t } = useTranslation('pages__learning__assigned-course-detail')
  const { add } = useNotification()
  const userOrganizationId = useUserOrganizationId()
  const [filters, setFilters] = useState<Filters>({
    education: [activeProgressGroup?.toLocaleLowerCase() as ProgressGroup],
  })
  const form = useForm<Filters>({ defaultValues: filters })

  const [notifyEmployees, { isLoading: isLoadingNotify }] =
    organizationAPI.useNotifyEmployeesMutation()

  const [getEmployeesGroup, { isLoading: isLoadingEmployees }] =
    organizationGatewayAPI.useLazyGetEmployeesGroupQuery()

  const [employees, setEmployees] = useState<Set<string>>(new Set())

  const LEARNING_LIST = useMemo(
    () =>
      groups_chart.map(i => ({
        id: i.group,
        title: i.title,
      })),
    [groups_chart],
  )

  const fetchEmployees = useCallback(
    async (filters: string[]) => {
      setEmployees(new Set([]))
      for (const educationElement of filters) {
        const body = {
          group_name: educationElement.toLowerCase() as TGroup,
          limit: Number.MAX_SAFE_INTEGER,
          offset: 0,
          organization_id: userOrganizationId,
        }

        try {
          const response = await getEmployeesGroup(body, true).unwrap()
          const employeeIds = (response.data || []).map(employee => employee.id)
          setEmployees(prev => new Set([...prev, ...employeeIds]))
        } catch (error) {
          // setEmployees()
        }
      }
    },
    [getEmployeesGroup, userOrganizationId],
  )

  useEffect(() => {
    if (filters.education) fetchEmployees(filters.education)
  }, [filters, fetchEmployees])

  const handleSubmit = (data: { text: string }) => {
    const parse = getNotifySchema({
      educationMsg: t('notify.education_required'),
    }).safeParse(form.getValues())

    if (parse.error) {
      form.setError('education', parse.error.issues[0])
      return
    }

    if (employees.size === 0) {
      add({
        id: uuid(),
        status: 'error',
        message: t('notify.no_employees'),
      })
      return
    }

    notifyEmployees({
      employees: [...employees.values()],
      text: data.text,
    })
      .unwrap()
      .then(() => {
        form.reset()
        setActiveProgressGroup(null)
      })
      .catch(() => {
        add({
          id: uuid(),
          status: 'error',
          message: t('notify.error'),
        })
      })
  }

  const onFilterChange = (values: string[]) => {
    const progressGroupValues = values.map(v => v as ProgressGroup)
    form.setValue('education', progressGroupValues)
    setFilters({ education: progressGroupValues })
    fetchEmployees(values)
  }

  return (
    <NofifyEmployyes
      open={!!activeProgressGroup}
      setOpen={(value: boolean) => {
        if (!value) {
          setActiveProgressGroup(null)
        }
      }}
      title={t('notify.title')}
      textPlaceholder={t('notify.textarea_placeholder')}
      count={employees.size || undefined}
      initialForm={{ text: '' }}
      customDisabled={
        !form.watch('education')?.some(Boolean) ||
        !employees.size ||
        isLoadingNotify ||
        isLoadingEmployees
      }
      topSlot={
        <NotifyEmployeesForm
          items={LEARNING_LIST}
          selectedValues={form.watch('education')?.map(String) || []}
          onChange={onFilterChange}
          error={form.formState.errors.education?.message || ''}
        />
      }
      cancel={{
        text: t('notify.cancel'),
      }}
      submit={{
        text: t('notify.send'),
      }}
      onSubmit={handleSubmit}
    />
  )
}

export { NofifyEmployyesByGroupModal }
