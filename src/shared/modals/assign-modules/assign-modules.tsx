import classNamesBind from 'classnames/bind'
import { Dispatch, SetStateAction, useState } from 'react'
import { Button, Card, Input, Textarea } from '@/shared/ui'
import { FileUploader } from '@/shared/components'
import { StoreModuleChooseDialog } from '../store-module-choose-dialog'
import { AssignModulesProps } from './assign-modules.d'
import styles from './assign-modules.module.scss'
import { getDate } from '@/shared/helpers/date'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { useAssignCourseMutation } from '@/store/services/assigned-courses-service'
import { Checkbox } from '@/shared/ui/checkbox'
import { SubmitHandler, useForm, Controller } from 'react-hook-form'
import {
  OrganizationTree,
  OrganizationTreeProvider,
  useUnmountOrganizationTree,
} from '../organization-tree'

const cx = classNamesBind.bind(styles)

const descriptionMaxLength = 400
const titleMaxLength = 255

const AssignModules: React.FC<AssignModulesProps.Props> = props => {
  const { open, onClose, onBack, refetch } = props
  const { t } = useTranslation('modals__assign-modules')

  const [modulesOpen, setModulesOpen] = useState(false)
  const [targetOpen, setTargetOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [newCountOfEmployes, setNewCountOfEmployes] = useState<number | undefined>(undefined)

  const analytics = useAnalytics()

  useUnmountOrganizationTree()
  const [assignCourse] = useAssignCourseMutation()

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    control,
    setError,
  } = useForm<AssignModulesProps.Inputs>({
    mode: 'onBlur',
    defaultValues: {
      title: '',
      description: '',
      need_assigned_messages: false,
      need_notify_messages: false,
      modules: [],
      targets: {
        target_departments: [],
        target_users: [],
        exclude_users_ids: [],
      },
    },
  })

  const onSubmit: SubmitHandler<AssignModulesProps.Inputs> = async data => {
    const {
      description,
      title,
      period,
      need_assigned_messages,
      need_notify_messages,
      start_date,
      picture,
      targets,
      modules,
    } = data

    setLoading(() => true)
    const course = new FormData()

    course.append('title', title)
    course.append('period', period)
    course.append('need_assigned_messages', String(need_assigned_messages))
    course.append('need_notify_messages', String(need_notify_messages))

    if (description) course.append('description', description)
    if (start_date) course.append('start_date', start_date)
    if (picture && picture[0]) course.append('image', picture[0])

    if (targets.target_users.length) {
      targets.target_users.forEach(u => {
        course.append('target_users', u)
      })
    }
    if (targets.exclude_users_ids?.length) {
      targets.exclude_users_ids.forEach(u => {
        course.append('exclude_users_ids', u)
      })
    }

    if (targets.target_departments.length) {
      targets.target_departments.forEach(d => {
        course.append('target_departments', d)
      })
    }

    modules.forEach(m => {
      course.append('modules', m)
    })

    analytics.event('dialogs.assign_modules.assign_course', {
      source: 'modules',
      ...course,
    })

    await assignCourse(course).unwrap()

    refetch()
    setLoading(() => false)
    onClose()
  }

  const handleTrim = (str: string, name: keyof AssignModulesProps.Inputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setError(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  const descriptionLength = watch('description')?.length || 0
  const titleLength = watch('title')?.length || 0

  // const targetValidation = () => {
  //   const targets = getValues('targets')
  //   const { target_departments, target_users } = targets

  //   if (target_departments.length + target_users.length <= 0) {
  //     setError('targets', { type: 'custom', message: t('commons:empty_targets_error') })
  //   } else {
  //     clearErrors('targets')
  //   }
  // }

  return (
    <Modal key='dialog' setActive={onClose} active={open} className={cx('dialog')}>
      <form onSubmit={handleSubmit(onSubmit)} className={cx('form')}>
        <h2 className={cx('title')}>{t('commons:course_creation')}</h2>
        <div className={cx('content')}>
          <Input
            fullWidth
            bordered
            required
            name='title'
            className={cx('field')}
            placeholder={t('commons:course_name')}
            error={errors.title?.message}
            register={register('title', {
              required: t('commons:required_field'),
              onBlur: e => handleTrim(e.target.value, 'title', true),
              maxLength: {
                value: titleMaxLength,
                message: t('commons:max_course_title_length_error', {
                  maxLength: titleMaxLength,
                  length: titleLength,
                }),
              },
            })}
          />
          <Textarea
            rows={3}
            view='bordered'
            name='description'
            className={cx('field', 'description')}
            placeholder={t('commons:course_description')}
            error={errors.description?.message}
            register={register('description', {
              onBlur: e => handleTrim(e.target.value, 'description'),
              maxLength: {
                value: descriptionMaxLength,
                message: t('commons:max_description_length_error', {
                  maxLength: descriptionMaxLength,
                  length: descriptionLength,
                }),
              },
            })}
          />
          <Input
            fullWidth
            bordered
            name='start_date'
            type='date'
            min={getDate()}
            className={cx('field')}
            placeholder={t('commons:course_startdate')}
            error={errors.start_date?.message}
            register={register('start_date')}
          />
          <Input
            fullWidth
            bordered
            required
            min={1}
            name='period'
            type='number'
            className={cx('field')}
            placeholder={t('commons:course_period')}
            error={errors.period?.message}
            register={register('period', {
              required: t('commons:required_field'),
              onBlur: e => handleTrim(e.target.value, 'period', true),
            })}
          />
          <Controller
            control={control}
            name='picture'
            render={({ field }) => (
              <FileUploader
                accept='image/*'
                name='picture'
                value={field.value}
                className={cx('field')}
                placeholder={t('commons:picture')}
                onChange={e => field.onChange(e.target.files)}
              />
            )}
          />
          <label className={cx('label')}>
            <Controller
              control={control}
              name='need_assigned_messages'
              render={({ field }) => (
                <Checkbox
                  variant='center'
                  initialChecked={field.value}
                  onChange={field.onChange}
                  label={<span>{t('commons:need_assigned_messages')}</span>}
                />
              )}
            />
          </label>
          <label className={cx('label')}>
            <Controller
              control={control}
              name='need_notify_messages'
              render={({ field }) => (
                <Checkbox
                  variant='center'
                  initialChecked={field.value}
                  onChange={field.onChange}
                  label={<span>{t('commons:need_notify_messages')}</span>}
                />
              )}
            />
          </label>
          <Controller
            control={control}
            name='modules'
            rules={{
              required: t('commons:required_field'),
              validate: value => value.length > 0,
            }}
            render={({ field }) => (
              <>
                <Card padding='normal' className={cx('block', 'field')}>
                  {t('modules', {
                    count: field.value.length || 0,
                  })}
                  <Button type='button' size='big' onClick={setModulesOpen.bind(null, true)}>
                    {t('commons:choose_modules')}
                  </Button>
                </Card>

                <StoreModuleChooseDialog
                  selected={field.value}
                  setModules={modules => {
                    field.onChange(modules)
                    setModulesOpen(false)
                  }}
                  open={modulesOpen}
                  onClose={(setSelected: Dispatch<SetStateAction<UUID[]>>) => {
                    field.onChange(field.value)
                    setModulesOpen(false)
                    // Для "обнуления" внутреннего состояния модалка до того, как она была открыта
                    setSelected(field.value)
                  }}
                />
              </>
            )}
          />
          <Controller
            control={control}
            name='targets'
            rules={{
              required: t('commons:required_field'),
              validate: value =>
                value.target_users.length > 0 || value.target_departments.length > 0,
            }}
            render={({ field }) => (
              <>
                <Card padding='normal' className={cx('block', 'field')}>
                  {newCountOfEmployes ? (
                    <>
                      {t(`commons:employee`, {
                        count: newCountOfEmployes,
                      })}
                    </>
                  ) : field.value && !!field.value.target_departments.length ? (
                    <>
                      {t(`commons:deps`, {
                        count: field.value.target_departments.length,
                      })}
                    </>
                  ) : (
                    t('commons:unselected')
                  )}
                  <Button type='button' size='big' onClick={setTargetOpen.bind(null, true)}>
                    {t('commons:choose_employees')}
                  </Button>
                </Card>
                {targetOpen && (
                  <OrganizationTreeProvider
                    handleSelect={data => {
                      field.onChange({
                        target_departments: data?.department_ids,
                        target_users: !data?.selectAll ? data?.users_ids : [],
                        exclude_users_ids: data?.selectAll ? data?.users_ids : [],
                      })
                      setNewCountOfEmployes(data?.countOfPeople)
                      setTargetOpen(false)
                    }}
                    open={targetOpen}
                    setOpen={setTargetOpen}
                  >
                    <OrganizationTree />
                  </OrganizationTreeProvider>
                )}
              </>
            )}
          />
        </div>
        {errors.targets?.message && (
          <span className={cx('error-text', 'centered')}>{errors.targets?.message}</span>
        )}
        <div className={cx('footer')}>
          <Button
            size='veryBig'
            view='border'
            className={cx('button', 'dialogFooter')}
            type='reset'
            onClick={onBack}
          >
            {t('commons:back')}
          </Button>
          <Button
            size='veryBig'
            className={cx('button', 'dialogFooter')}
            type='submit'
            loading={loading}
            disabled={!isValid || loading}
          >
            {t('commons:assign')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export default AssignModules
