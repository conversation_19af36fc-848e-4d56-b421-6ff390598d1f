@use "../../../shared/assets/styles/mixins/text";

.content {
  padding: 10px 60px !important;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
}

.label {
  display: flex;
  flex-direction: row;
  gap: 12px;
  align-items: center;
}

.centered {
  display: flex;
  align-items: center;
  justify-content: center;
}
.form {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}

.title {
  @include text.title(30px);
  padding: 25px 0 !important;

  text-align: center;
}

.description {
  min-height: 100px;
}

.field + .field {
  margin-top: 20px;
  width: 100%;
}

.footer {
  display: flex;
  gap: 12px;
  justify-content: center !important;
  padding: 30px 90px 40px !important;
}
.button {
  max-width: 240px;
  width: 100%;
}

.block {
  align-items: center;

  border: 1px solid var(--color-primary);
  flex-direction: row !important;
  justify-content: space-between;
}

.dialog {
  display: grid;
  grid-template-rows: 1fr;
  max-height: 90vh;
  max-width: 800px;
  width: 100%;
}
