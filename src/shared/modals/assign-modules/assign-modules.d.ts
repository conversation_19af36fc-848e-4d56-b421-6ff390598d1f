import { ITargetData } from '@/entities/target'

export declare namespace AssignModulesProps {
  interface Own {
    open: boolean
    onClose: () => void
    onBack: () => void
    refetch: () => void
  }

  interface Inputs {
    title: string
    description?: string
    start_date?: string
    period: string
    picture?: FileList
    need_assigned_messages: boolean
    need_notify_messages: boolean
    modules: UUID[]
    targets: ITargetData
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch
}

export {}
