import React, { useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './delete-confirm.module.scss'

import { DeleteConfirmProps } from './delete-confirm.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const DeleteConfirm: React.FC<DeleteConfirmProps.Props> = props => {
  const { t } = useTranslation('modals__delete-confirm')
  const title = props.title || t('title')

  const [loading, setLoading] = useState(false)

  const _onConfirm = async () => {
    setLoading(true)
    if ('id' in props) {
      await props.onClose(props.id || undefined)
    } else {
      await props.onClose(props.ids || undefined)
    }
    setLoading(false)
  }

  const _onClose = () => props.onClose()

  const active = ('id' in props && !!props.id) || ('ids' in props && !!props.ids)

  return (
    <Modal key='delete-dialog' setActive={_onClose} active={active} className={cx('dialog')}>
      <h2 className={cx('title')}>{title}</h2>

      <div className={cx('footer')}>
        <Button
          size='veryBig'
          onClick={_onClose}
          className={cx('button', 'dialogFooter')}
          type='button'
        >
          {t('commons:cancel')}
        </Button>
        <Button
          size='veryBig'
          className={cx('button', 'dialogFooter')}
          type='button'
          loading={loading}
          onClick={_onConfirm}
          color='red'
        >
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}

export default DeleteConfirm
