import React, { useState, useEffect, useRef, useCallback } from 'react'
import styles from './edit-employee-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { EditEmployeeModalProps } from './edit-employee-modal.d'
import { Modal } from '@/shared/components'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { IListItem, Button, Input, Loader, Select } from '@/shared/ui'
import { useTranslation } from 'react-i18next'
import { ERole, isTagCustom } from '@/shared/types/enums'
import {
  tagsEmployeesApi,
  useAssignTagToEntitiesMutation,
  useRemoveTagFromEmployeeMutation,
} from '@/store/services/tags-employees-service'
import { CustomTagsSelect } from '@/shared/components/custom-tags-select'
import { ITagWithNew } from '@/shared/types/store/tag'
import { IDepartment } from '@/entities/department'
import { useGetDepartmentsTitlesQuery } from '@/entities/department/model/endpoints'
import InfiniteScroll from 'react-infinite-scroll-component'
import { usePrioritizedList } from '@/shared/hooks'

const cx = classNamesBind.bind(styles)

export interface EmployeeEdit {
  email: string
  middle_name: string
  first_name: string
  last_name: string
  position: string
  department: IListItem | null
  role: ERole | null
}

type Inputs = EmployeeEdit

const TRANLSATION_FILE = 'modals__edit-employee'

const DEPARTMENTS_LIMIT = 10
const DEPARTMENTS_LIST_ID = 'edit-employee-departments-list'

export const EditEmployeeModal: React.FC<EditEmployeeModalProps.Props> = props => {
  const { active, className, setActive, data, refetch } = props

  const { t } = useTranslation(TRANLSATION_FILE)

  const [departmentsPage, setDepartmentsPage] = useState(1)
  const [departmentsList, setDepartmentsList] = useState<IDepartment[]>([])
  const [departmentsTotalCount, setDepartmentsTotalCount] = useState(0)
  const [departmentsSearch, setDepartmentsSearch] = useState('')

  const {
    data: departments,
    isLoading: isLoadingDepartments,
    isFetching: isFetchingDepartments,
  } = useGetDepartmentsTitlesQuery(
    {
      limit: DEPARTMENTS_LIMIT,
      offset: (departmentsPage - 1) * DEPARTMENTS_LIMIT,
      search: departmentsSearch || undefined,
    },
    {
      skip: !active,
    },
  )

  useEffect(() => {
    if (!departments) return

    if (departmentsPage === 1) {
      setDepartmentsList(departments.data)
    } else {
      setDepartmentsList(prev => [...prev, ...departments.data])
    }

    setDepartmentsTotalCount(departments.total_count)
  }, [departments])

  const departmentsHasMore = departmentsPage * DEPARTMENTS_LIMIT < departmentsTotalCount

  const prioritizedDepartmentsList = usePrioritizedList<{ id: string; title: string }>(
    departmentsList,
    data?.employee.department,
  )

  const renderListInnerWrapper = useCallback(
    (children: React.ReactNode) => (
      <InfiniteScroll
        dataLength={departmentsList.length}
        next={() => setDepartmentsPage(prev => prev + 1)}
        hasMore={departmentsHasMore}
        loader={<Loader size='28' loading />}
        scrollableTarget={DEPARTMENTS_LIST_ID}
      >
        {children}
      </InfiniteScroll>
    ),
    [departmentsHasMore, departmentsList.length],
  )

  const handleChangeDepartmentSearch = useCallback((search: string) => {
    setDepartmentsPage(1)
    setDepartmentsSearch(search)
  }, [])

  const [selectedTags, setSelectedTags] = useState<ITagWithNew[]>([])
  const initialCustomTagIds = useRef<UUID[] | null>(null)
  const [hasValue, setHasValue] = useState(false)

  useEffect(() => {
    if (!(data && data.employee)) return

    reset()

    if (data.employee.tags && data.employee.tags.length > 0) {
      const selectedCustomTags = data?.employee.tags.filter(tag => isTagCustom(tag))

      if (!initialCustomTagIds.current) {
        initialCustomTagIds.current = selectedCustomTags.map(selectTag => selectTag.id)
      }

      setSelectedTags(
        selectedCustomTags.map(selectTag => ({
          ...selectTag,
          isNew: false,
        })),
      )
    }

    setValue('email', data.employee.email)
    setValue('middle_name', data.employee?.middle_name || '')
    setValue('first_name', data.employee?.first_name || '')
    setValue('last_name', data.employee?.last_name || '')
    setValue('position', data.employee?.position || '')
    setValue('role', data.employee.role)
    setValue(
      'department',
      data?.employee?.department?.id
        ? {
          title: data?.employee?.department?.title,
          id: data?.employee?.department?.id,
        }
        : null,
    )

    setHasValue(() => true)

    return () => reset()
  }, [data])

  const {
    setValue,
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setError: setErrorForm,
    control,
  } = useForm<Inputs>({
    mode: 'all',
    defaultValues: {
      email: '',
      middle_name: '',
      first_name: '',
      last_name: '',
      position: '',
      department: null,
      role: null,
    },
  })

  const [editEmployee, { isLoading }] = tagsEmployeesApi.useEditEmployeeMutation()
  const [assignTagToEntities, { isLoading: isAssignTagLoading }] = useAssignTagToEntitiesMutation()
  const [removeTagFromEntities, { isLoading: isRemoveTagLoading }] =
    useRemoveTagFromEmployeeMutation()

  const isEntityLoading = isLoading || isAssignTagLoading || isRemoveTagLoading

  const [error, setError] = useState('')

  const onSubmit: SubmitHandler<Inputs> = async formData => {
    if (!data) return

    try {
      const newSelectedTagIds = selectedTags?.filter(tag => tag.isNew).map(tag => tag.id)

      if (newSelectedTagIds && newSelectedTagIds.length > 0) {
        newSelectedTagIds.forEach(async newTagId => {
          await assignTagToEntities({
            tagID: newTagId,
            usersIds: [data?.employee.id],
            departmentsIds: [],
            needOnBoarding: true,
          }).catch(error => setError(error.data.message))
        })
      }

      const deletedTagIds = initialCustomTagIds.current?.filter(initialTagId =>
        selectedTags?.every(selectedTag => selectedTag.id !== initialTagId),
      )

      if (deletedTagIds && deletedTagIds.length > 0) {
        await removeTagFromEntities({
          tagIds: deletedTagIds,
          userID: data?.employee.id,
        })
      }
    } catch (error) {
      setError(t('commons:error_unexpected'))
    }
    try {
      const employeeData = Object.entries(formData).reduce((acc, [key, value]) => {
        if (key) acc[key as keyof EmployeeEdit] = value || ''

        return acc
      }, {} as EmployeeEdit)

      await editEmployee({ ...employeeData, id: data.employee.id })
        .unwrap()
        .then(() => {
          reset()
          refetch && refetch()
          setError('')
          setActive(false)
        })
        .catch(error => setError(error.data.message))
    } catch (error) {
      setError(t('commons:error_unexpected'))
    }
  }

  const handleTrim = (str: string, name: keyof Inputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  const roles = [
    {
      id: 'operator',
      title: t('operator'),
    },
    {
      id: 'content_manager',
      title: t('content_manager'),
    },
    {
      id: 'employee',
      title: t('employee'),
    },
  ] as { id: ERole; title: string }[]

  return (
    <Modal setActive={setActive} active={active} className={cx('modal-wrapper', className)}>
      {!data && !hasValue && <Loader className={cx('loader')} size='56' />}
      {data && hasValue && (
        <form onSubmit={handleSubmit(onSubmit)} className={cx('wrapper')}>
          <div className={cx('title')}>{t('title')}</div>
          <div className={cx('grid')}>
            <Input
              placeholder={t('email')}
              label={t('email')}
              className={cx('input', {
                errorInput: errors.email?.message,
              })}
              error={errors.email?.message}
              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
              // @ts-ignore
              register={register('email', {
                validate: value => {
                  return !!value.trim()
                },
                pattern: {
                  value: /^([+A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,10})$/,
                  message: t('email_pattern'),
                },
              })}
              fullWidth
              required
            />
            <Input
              placeholder={t('surname')}
              label={t('surname')}
              className={cx('input', {
                errorInput: errors.last_name?.message,
              })}
              error={errors.last_name?.message}
              fullWidth
              required
              register={register('last_name', {
                required: t('errors.surname_req'),
              })}
              onBlur={e => handleTrim(e.target.value, 'last_name', true)}
            />
            <Input
              placeholder={t('name')}
              label={t('name')}
              className={cx('input', {
                errorInput: errors.first_name?.message,
              })}
              error={errors.first_name?.message}
              fullWidth
              required
              register={register('first_name', {
                required: t('errors.name_req'),
              })}
              onBlur={e => handleTrim(e.target.value, 'first_name', true)}
            />
            <Input
              placeholder={t('middlename')}
              label={t('middlename')}
              className={cx('input', {
                errorInput: errors.middle_name?.message,
              })}
              error={errors.middle_name?.message}
              fullWidth
              register={register('middle_name')}
              onBlur={e => handleTrim(e.target.value, 'middle_name')}
            />

            <Controller
              control={control}
              name='role'
              render={({ field: { onChange, value } }) => (
                <Select
                  label={t('role')}
                  placeholder={t('role')}
                  className={cx('input')}
                  value={value || data.employee.role}
                  list={roles}
                  handleChange={(r: IListItem) => onChange(r.id as ERole)}
                />
              )}
            />

            <Controller
              control={control}
              name='department'
              render={({ field: { onChange, value } }) => (
                <Select
                  remoteSearch
                  loading={isLoadingDepartments || isFetchingDepartments}
                  label={t('department')}
                  placeholder={t('department')}
                  className={cx('input')}
                  value={value?.id || data?.employee?.department?.id}
                  list={
                    prioritizedDepartmentsList?.map(d => ({
                      title: d.title,
                      id: d.id,
                    })) ?? []
                  }
                  handleChange={(d: IListItem) => onChange(d)}
                  renderListInnerWrapper={renderListInnerWrapper}
                  listWrapperId={DEPARTMENTS_LIST_ID}
                  handleChangeSearch={handleChangeDepartmentSearch}
                />
              )}
            />

            <Input
              label={t('position')}
              placeholder={t('position')}
              classNameWrapper={cx('input', 'double')}
              error={errors.position?.message}
              fullWidth
              register={register('position', {
                maxLength: {
                  value: 255,
                  message: t('errors.position_max', { max: 255 }),
                },
              })}
              onBlur={e => handleTrim(e.target.value, 'position')}
            />
            <CustomTagsSelect
              initialSelectedTags={selectedTags}
              handleChangeTags={setSelectedTags}
              className={cx('input', 'double')}
              isListTop
            />
            <Button
              type='submit'
              color='green'
              size='big'
              className={cx('double', 'button')}
              disabled={!isValid}
              loading={isEntityLoading}
            >
              {t('submit')}
            </Button>

            {error && <div className={cx('error-text')}>{error}</div>}
          </div>
        </form>
      )}
    </Modal>
  )
}
