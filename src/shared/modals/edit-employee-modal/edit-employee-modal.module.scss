.modal-wrapper {
  width: 656px;
  max-height: 95vh;
  max-height: 786px;
  overflow-y: auto;
}

.title {
  font-family: "TT Norms Pro";
  font-size: 18px;
  line-height: 24px;
  color: #343b54;
  margin-bottom: 24px;
}

.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 4px 32px;
  .input {
    margin-bottom: 28px;
  }
  .double {
    grid-column: 1/3;
  }
  .errorInput {
    margin-bottom: 0;
  }
  .button {
    justify-self: end;
    grid-column: 2;
  }
}

.error-text {
  grid-column: 1/3;
  font-family: "TT Norms Pro";
  font-style: normal;
  font-weight: 400;
  font-size: 11px;
  line-height: 16px;
  color: #ff8577;
  text-align: center;
}
