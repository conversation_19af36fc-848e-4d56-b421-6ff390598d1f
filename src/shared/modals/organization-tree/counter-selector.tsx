import { useTranslation } from 'react-i18next'
import styles from './tree.module.scss'
import classNamesBind from 'classnames/bind'
import { useAppSelector } from '../../../store'
import { selectSelectAll, selectTree } from './departments-tree-slice'

const cx = classNamesBind.bind(styles)

export const CounterSelector = () => {
  const { t } = useTranslation('modals__organization-tree')
  const isAllSelected = useAppSelector(selectSelectAll)
  const tree = useAppSelector(selectTree)

  if (!isAllSelected) {
    const departmentsLength = Object.keys(tree.checkedDepartments).length
    const usersLength = Object.keys(tree.checkedUsers).length
    const isShow = !!departmentsLength || !!usersLength

    if (!isShow) return null

    return (
      <span className={cx('selected')}>
        {t('selected.pharase')}
        {!!departmentsLength && (
          <>{t('selected.departments.choosed', { count: departmentsLength })}</>
        )}
        {!!departmentsLength && !!usersLength && t('selected.and')}
        {!!usersLength && <>{t('selected.employees.choosed', { count: usersLength })}</>}
      </span>
    )
  }

  const departmentsLength = Object.keys(tree.checkedDepartments).length
  const usersLength = Object.keys(tree.checkedUsers).length
  const isShow = !!departmentsLength || !!usersLength

  if (!isShow) return <span className={cx('selected')}>{t('selected_all')}</span>

  return (
    <span className={cx('selected')}>
      {t('not_selected.pharase')}
      {!!departmentsLength &&
        t('not_selected.departments.choosed', {
          count: departmentsLength,
        })}
      {!!departmentsLength && !!usersLength && t('selected.and')}
      {!!usersLength && t('not_selected.employees.choosed', { count: usersLength })}
    </span>
  )
}
