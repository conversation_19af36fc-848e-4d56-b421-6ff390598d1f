import { useAppDispatch } from 'store'
import { departmentsTreeSlice } from './departments-tree-slice'
import { SearchVariants } from './types'

export const useSetSearchVariants = () => {
  const dispatch = useAppDispatch()

  const onSetSearchVariants = (searchVariants: SearchVariants[]) => {
    dispatch(departmentsTreeSlice.actions.setSearchVariants(searchVariants))
  }

  return {
    onSetSearchVariants,
  }
}
