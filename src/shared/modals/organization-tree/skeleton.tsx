import styles from './tree.module.scss'

import classNamesBind from 'classnames/bind'
import { ButtonIcon, Checkbox } from '../../ui'
import Skeleton from 'react-loading-skeleton'

const cx = classNamesBind.bind(styles)

type Props = {
  withIcon?: boolean
  checkboxValue?: boolean
}

export const ElementSkeleton = (props: Props) => {
  return (
    <>
      {props.withIcon && (
        <ButtonIcon
          className={cx('switcher')}
          icon='chevroneMedium'
          iconSize='20'
          size='24'
          direction={'up'}
          disabled={true}
        />
      )}

      <Checkbox disabled={true} customChecked={props.checkboxValue} />
      <Skeleton width={400} height={24} />
    </>
  )
}
