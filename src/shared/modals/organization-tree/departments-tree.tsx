/* eslint-disable @typescript-eslint/no-explicit-any */
import { useTranslation } from 'react-i18next'
import styles from './tree.module.scss'
import classNamesBind from 'classnames/bind'
import { useAppDispatch, useAppSelector } from 'store'
import {
  departmentsTreeSlice,
  selectFilteredTreeData,
  selectSearch,
  selectSelectAll,
  selectTree,
} from './departments-tree-slice'
import Tree from 'rc-tree'
import { useDebounceValue } from 'usehooks-ts'
import { ButtonIcon, Checkbox, Loader, SearchInput } from '../../ui'
import OrganizationSmallIcon from '../../ui/Icon/icons/components/OrganizationSmallIcon'
import PersonIcon from '../../ui/Icon/icons/components/PersonIcon'
import { IconWrapper } from '../../ui/Icon/IconWrapper'
import { TreeNodeProps } from 'rc-tree/lib/interface'
import { getOnChecked, hasChildrenLength, isUseCustomMinus } from './helpers'
import { useEffect } from 'react'
import { TreeDataType } from './types'
import { loadTreeData } from './thunks'
import { ElementSkeleton } from './skeleton'
import { EmptyPlug } from './empty-plug'
import { useStrictContext } from '@/shared/helpers'
import { OrganizationTreeContext } from './organization-tree.context'
import { useGetModalDepartmentsQuery } from './departments-tree-endpoints'
import { AllSelector } from './all-selector'

const cx = classNamesBind.bind(styles)

export const DepartmentsTree = () => {
  const { t } = useTranslation('modals__organization-tree')
  const search = useAppSelector(selectSearch)
  const tree = useAppSelector(selectTree)
  const filteredTree = useAppSelector(selectFilteredTreeData)
  const [debounceDepartmentsSearch] = useDebounceValue(String(search.departments), 600)
  const dispatch = useAppDispatch()
  const isAllSelect = useAppSelector(selectSelectAll)
  const { data: departments, isLoading } = useGetModalDepartmentsQuery({
    search: debounceDepartmentsSearch,
  })
  const treeContext = useStrictContext(OrganizationTreeContext)

  useEffect(() => {
    if (!departments) return

    dispatch(
      departmentsTreeSlice.actions.setTree({
        loadedKeys: {},
        treeData: departments?.data?.map(department => ({
          ...department,
          users: undefined,
          className: cx('node'),
          key: department?.id,
          type: 'department',
        })),
      }),
    )
  }, [departments, dispatch])

  const onLoad = async (data: TreeDataType) => {
    await dispatch(loadTreeData({ treeNode: data, department_search: debounceDepartmentsSearch }))
  }

  return (
    <>
      <div className={cx('header')}>
        <div className={cx('search-wrapper')}>
          <SearchInput
            placeholder={t('department_search_placeholder')}
            value={search.departments}
            onChange={v => dispatch(departmentsTreeSlice.actions.setSearch({ departments: v }))}
            className={cx('search')}
            classNameWrapper={cx('searchWrapper')}
          />
        </div>
        {/* <CounterSelector /> */}
        {search.departments.length === 0 && <AllSelector />}
      </div>
      {isLoading && (
        <ul className={cx('skeleton__wrapper')}>
          {Array.from({ length: 550 / (24 + 4) }, (_, index) => index).map(v => (
            <li key={v} className={cx('skeleton__item')}>
              <ElementSkeleton />
            </li>
          ))}
        </ul>
      )}
      {!isLoading && departments?.data && departments?.data?.length > 0 && (
        <Tree<TreeDataType>
          rootClassName={cx('root')}
          className={cx('tree')}
          height={550}
          itemHeight={26}
          checkedKeys={Object.keys(tree.checkedKeys)}
          loadData={onLoad}
          virtual={true}
          expandedKeys={Object.keys(tree.expandedKeys)}
          loadedKeys={Object.keys(tree.loadedKeys)}
          titleRender={data => (
            <>
              <div key={data?.id} className={cx('row')}>
                <Checkbox
                  color={treeContext.isDelete ? 'red' : 'primary'}
                  customMinus={isUseCustomMinus({ node: data, tree, selectAll: isAllSelect })}
                  customChecked={getOnChecked({ node: data, tree, selectAll: isAllSelect })}
                  disabled={tree.isLoadingKeys[data?.id]}
                  onClick={() => {
                    const existsInRequiredIds = treeContext.requiredIds?.includes(data?.id)
                    if (existsInRequiredIds) return

                    if (data.type === 'department' && !tree.loadedKeys[data.id]) {
                      onLoad(data).then(() =>
                        dispatch(departmentsTreeSlice.actions.onCheckNode(data)),
                      )
                    } else {
                      dispatch(departmentsTreeSlice.actions.onCheckNode(data))
                    }
                  }}
                />
                {data?.type === 'department' && (
                  <IconWrapper>
                    <OrganizationSmallIcon />
                  </IconWrapper>
                )}
                {data?.type === 'user' && (
                  <IconWrapper>
                    <PersonIcon />
                  </IconWrapper>
                )}
                <span>{data?.title}</span>
              </div>
              {tree?.expandedKeys[data?.id] &&
                data?.type === 'department' &&
                data?.isLeaf &&
                !hasChildrenLength(data) && <div className={cx('empty')}>{t('no_employees')}</div>}
            </>
          )}
          checkable={false}
          treeData={filteredTree}
          switcherIcon={(props: TreeNodeProps<any>) => {
            if (props?.data?.type === 'user') return <div className={cx('indent')}></div>
            const key = props?.data?.id
            const isExpanded = tree?.expandedKeys[key]

            if (tree.isLoadingKeys[key]) return <Loader size='28' className={cx('loader')} />

            return (
              <ButtonIcon
                className={cx('switcher')}
                icon='chevroneMedium'
                iconSize='20'
                size='24'
                onClick={() => {
                  const isLoaded = tree.loadedKeys[key]

                  if (!isLoaded) return

                  dispatch(departmentsTreeSlice.actions.onExpandNode(props.data))
                }}
                direction={isExpanded ? 'right' : 'up'}
              />
            )
          }}
        />
      )}
      {!isLoading && departments?.data?.length === 0 && <EmptyPlug />}
    </>
  )
}
