.modal{
  padding: 20px;
  padding-right: 40px;
  max-height: 90vh;
  max-width: 816px;
  width: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.root{
  overflow: auto;

  &::-webkit-scrollbar {
    width: 0;
    height: 0;
  }
}

.skeleton{
  &__wrapper{
    display: flex;
    flex-direction: column;
    gap: 4px;
    overflow-y: auto
  }
  &__item{
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-items: center;
  }
}

.header {
  align-items: center;
  display: flex;
  gap: 8px;
  justify-content: flex-start;
  width: 100%;

  .selectAll {
    color: var(--color-gray-70, #8e97af);

    cursor: pointer;
    font: var(--font-text-2-medium);
  }
}

.selectAll {
  margin-left: auto;
  color: var(--color-gray-70, #8e97af);

  cursor: pointer;
  font: var(--font-text-2-medium);
}

.row{
  width: 100%;
  display: flex;
  gap: 8px;
  align-items: center;  
  margin-bottom: 2px;

  font: var(--font-text-2-normal);
}

.tree{
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  width: 100%;
}

.node {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.indent{
  width: 24px;
  height: 24px;
}

.empty {
  font: var(--font-caption-2-normal);
  margin: 8px 0 8px 28px;
}

.switcher{
  align-self: flex-start;
  cursor: pointer;
}

.button__wrapper {
  display: flex;
  gap: 16px;
  justify-content: end;
}

.select {
  padding: 0 12px;
  padding-right: 12px;
  margin-right: -8px;
  min-width: 140px;
  background: none;
  border: none;
  display: flex;
  justify-content: start;

  &-list {
    z-index: 100000;
    background: var(--white);
  }

  &-text {
    margin-left: auto;
    display: flex;

  }
}

.search{
  &__help{
    font: var(--font-text-2-medium)
  }
  &__wrapper{
    display: flex;
  }
  &__select{
    display: flex;
    gap: 4px;
    margin-left: auto;
  }
}

.label{
  &__custom{
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

.selected{
  font: var(--font-text-2-medium);
  color: var(--color-primary);
}

.test{
  height: 550px;
}

.employees {
  & > div {
    height: 550px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
  }

  &__item{
    display: flex;
    gap: 4px;
    align-items: center;
    margin-bottom: 4px;

    font: var(--font-text-2-normal);

    &__email{
      color: var(--color-gray-80);
    }
  }

  &::-webkit-scrollbar {
    width: 3px;

  }

  &::-webkit-scrollbar-track {
    background: none; 
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-gray-60); 
    border-radius: 10px; 
    cursor: grab;

    &:hover {
      background: var(--color-gray-80); 
    }
  }
}

.plug {
  &__container {
    align-items: center;

    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 85px;

    text-align: center;

    width: 100%;

    div {
      width: 100%;
    }

    svg {
      margin-left: auto;
      margin-right: auto;
    }
  }
   &__text {
    color: var(--gray-gray-70, #8e97af);
    font: var(--font-title-3-medium);
  }
}

.box_icon {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  width: 100%;
  > div {
    height: 160px !important;
    width: 160px !important;

    > svg {
      height: 100% !important;
      width: 100% !important;
    }
  }
}

.loader{
  width: 24px !important;
  height: 24px !important;
}

.loading{
  opacity: 0.6;
}