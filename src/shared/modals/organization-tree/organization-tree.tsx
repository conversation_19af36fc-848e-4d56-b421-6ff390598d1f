/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Modal } from 'shared/components'
import './tree.css'
import { Button } from 'shared/ui'
import styles from './tree.module.scss'
import classNamesBind from 'classnames/bind'
import { useTranslation } from 'react-i18next'
import 'react-loading-skeleton/dist/skeleton.css'
import { EmployeeList } from './employee-list'
import { useAppSelector } from 'store'
import { selectSearchVariant } from './departments-tree-slice'
import { SearchVariantSelector } from './search-variant-selector'
import { DepartmentsTree } from './departments-tree'
import { OrganizationTreeSaveButton } from './save-button'
import { useSetFiltersFns } from './use-set-filters-fns'
import { useStrictContext } from '@/shared/helpers'
import { OrganizationTreeContext } from './organization-tree.context'

const cx = classNamesBind.bind(styles)

export const OrganizationTree = () => {
  const { t } = useTranslation('modals__organization-tree')
  const selectedSearchVariant = useAppSelector(selectSearchVariant)
  const treeContext = useStrictContext(OrganizationTreeContext)
  useSetFiltersFns(treeContext.filterFns)

  return (
    <Modal
      active={treeContext.open}
      setActive={v => treeContext.setOpen(!!v)}
      className={cx('modal')}
    >
      <div className={cx('title')}>{t('title')}</div>
      <div className={cx('search__wrapper')}>
        <div className={cx('search__help')}>{t('search_by')}</div>
        <div className={cx('search__select')}>
          <SearchVariantSelector />
        </div>
      </div>
      <div className={cx('header')}></div>
      {selectedSearchVariant === 'departments' && <DepartmentsTree />}
      {selectedSearchVariant === 'employees' && <EmployeeList />}
      <div className={cx('button__wrapper')}>
        <Button onClick={() => treeContext.setOpen(false)} color='gray'>
          {t('cancel')}
        </Button>
        <OrganizationTreeSaveButton />
      </div>
    </Modal>
  )
}

export default OrganizationTree
