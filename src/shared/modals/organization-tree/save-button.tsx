import { Button } from '@/shared/ui'
import { getStore, useAppDispatch, useAppSelector } from '@/store'
import { useTranslation } from 'react-i18next'
import { loadUsersCount } from './thunks'
import { useNotification } from '@/shared/contexts/notifications'
import { useState } from 'react'
import { selectIsCanPressSave } from './departments-tree-slice'
import { useStrictContext } from '@/shared/helpers'
import { OrganizationTreeContext } from './organization-tree.context'

export const OrganizationTreeSaveButton = () => {
  const { t } = useTranslation('modals__organization-tree')
  const [isLoading, setIsLoading] = useState(false)
  const dispatch = useAppDispatch()
  const { handleErrorResponse } = useNotification()
  const isCanPress = useAppSelector(selectIsCanPressSave)
  const treeContext = useStrictContext(OrganizationTreeContext)

  return (
    <Button
      onClick={async () => {
        const treeSlice = getStore().getState().departmentsTree
        setIsLoading(true)

        let data: {
          result: number
          department_ids: string[]
          users_ids: string[]
        }
        try {
          data = (await dispatch(
            loadUsersCount({ selectAll: treeSlice.selectAll, treeSlice, treeContext }),
          ).unwrap()) as {
            result: number
            department_ids: string[]
            users_ids: string[]
          }
        } catch (error) {
          return
        } finally {
          setIsLoading(false)
        }
        const getIsError = () => {
          if (treeSlice.selectAll) {
            if (treeContext?.isDelete) return false

            return !data?.department_ids?.length || !data?.users_ids || !data?.result
          }

          return data?.result < 1 && !treeContext.isCanEmpty
        }
        const getUsersIds = () => {
          if (treeContext?.isDelete) {
            return treeSlice.selectAll
              ? treeSlice.forceUsers
                ?.filter(u => !treeSlice.tree.checkedUsers[u?.id])
                .map(u => u.id)
              : Object.keys(treeSlice.tree.checkedUsers)
          }
          return data?.users_ids
        }

        if (getIsError() && !getUsersIds()?.length) {
          handleErrorResponse({
            status: 'error',
            message: t('notfound_error'),
          })
          return
        }

        if (treeContext?.handleSelect)
          treeContext.handleSelect({
            ...treeSlice.tree,
            selectAll: treeSlice.selectAll,
            countOfPeople: data?.result,
            department_ids: data?.department_ids,
            users_ids: getUsersIds() ?? [],
          })

        treeContext.setOpen(false)
      }}
      disabled={treeContext.isCanEmpty ? false : isLoading || !isCanPress}
      loading={isLoading}
      color={treeContext.isDelete ? 'red' : 'green'}
    >
      {treeContext.isDelete ? t('delete') : t('select')}
    </Button>
  )
}
