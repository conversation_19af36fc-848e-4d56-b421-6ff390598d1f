import 'react-loading-skeleton/dist/skeleton.css'
import styles from './tree.module.scss'
import classNamesBind from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  departmentsTreeSlice,
  selectEmployeesSearch,
  selectFilteredForcedUsers,
  selectFilteredUsers,
  selectPage,
  selectSelectAll,
  selectTree,
} from './departments-tree-slice'
import { useLazyGetModalEmployeesQuery } from '@/store/services/tags-employees-service'
import { useEffect, useState } from 'react'
import { useDebounceValue } from 'usehooks-ts'
import { Checkbox, SearchInput } from 'shared/ui'
import PersonIcon from 'shared/ui/Icon/icons/components/PersonIcon'
import { IconWrapper } from 'shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import { InfiniteLoader, List, ListRowProps } from 'react-virtualized'
import { ElementSkeleton } from './skeleton'
import { getOnCheckForUser } from './helpers'
import { EmptyPlug } from './empty-plug'
import { isNumber, useStrictContext } from '@/shared/helpers'
import { OrganizationTreeContext } from './organization-tree.context'
import { AllSelector } from './all-selector'

const cx = classNamesBind.bind(styles)

const LIMIT = 50

export const EmployeeList = () => {
  const { t } = useTranslation('modals__organization-tree')
  const treeContext = useStrictContext(OrganizationTreeContext)
  const users = useAppSelector(selectFilteredUsers)
  const page = useAppSelector(selectPage)
  const tree = useAppSelector(selectTree)
  const isAllSelected = useAppSelector(selectSelectAll)
  const search = useAppSelector(selectEmployeesSearch)
  const [getEmployessTrigger, lazyGetEmployees] = useLazyGetModalEmployeesQuery()
  const dispatch = useAppDispatch()
  const [debounceEmployeesSearch] = useDebounceValue(String(search), 600)
  const forcedUsers = useAppSelector(state =>
    selectFilteredForcedUsers(state, debounceEmployeesSearch),
  )
  const [isFirstMount, setIsFirstMount] = useState(true)
  const usageUsers = forcedUsers ?? users

  useEffect(() => {
    if (users?.length) return

    dispatch(departmentsTreeSlice.actions.setPage(Math.ceil(users?.length / LIMIT)))

    return () => {
      dispatch(departmentsTreeSlice.actions.setPage(0))
    }
  }, [])

  const fetchMore = async (onSuccess?: () => void) => {
    const { data: employees } = await getEmployessTrigger(
      {
        filter: { search: debounceEmployeesSearch },
        page,
        limit: LIMIT,
      },
      true,
    )
    const data = employees?.data
    if (onSuccess) onSuccess()

    if (data) {
      dispatch(departmentsTreeSlice.actions.addUsers(data))
      dispatch(departmentsTreeSlice.actions.setPage(page + 1))
    }
  }

  useEffect(() => {
    if (forcedUsers) return
    if (isFirstMount) {
      setIsFirstMount(false)
      return
    }

    fetchMore(() => {
      dispatch(departmentsTreeSlice.actions.removeAllUsers())
    })
  }, [debounceEmployeesSearch])

  useEffect(() => {
    return () => {
      dispatch(departmentsTreeSlice.actions.removeAllUsers())
    }
  }, [dispatch])

  const rowRender = (params: ListRowProps) => {
    const e = usageUsers.at(params.index)

    if (!e)
      return (
        <li key={params.index} style={params.style} className={cx('employees__item')}>
          <ElementSkeleton checkboxValue={isAllSelected} withIcon={false} />
        </li>
      )

    return (
      <li style={params.style} key={e.id} className={cx('employees__item')}>
        <Checkbox
          color={treeContext.isDelete ? 'red' : 'primary'}
          customChecked={getOnCheckForUser({ tree, user: e, selectAll: isAllSelected })}
          onClick={() => {
            const existsInRequiredIds = treeContext.requiredIds?.includes(e.id)
            if (existsInRequiredIds) return

            dispatch(
              departmentsTreeSlice.actions.onCheckNode({
                ...e,
                title: `${e.first_name} ${e.last_name} ${e.email ? `(${e.email})` : ''}`,
                key: e.id,
                type: 'user',
                parent: e.department?.id
                  ? {
                      id: String(e.department?.id),
                      key: String(e.department?.id),
                      title: String(e?.department?.title),
                      type: 'department',
                    }
                  : undefined,
                email: e.email,
                last_name: String(e.last_name),
                first_name: String(e.first_name),
                middle_name: String(e.middle_name),
                // eslint-disable-next-line i18next/no-literal-string
                variant: 'single',
              }),
            )
          }}
        />
        <IconWrapper>
          <PersonIcon />
        </IconWrapper>
        <span>
          {`${e?.first_name} ${e.last_name}`}{' '}
          {e.email ? <span className={cx('employees__item__email')}>({e.email})</span> : null}
        </span>
      </li>
    )
  }

  const getIsEmpty = () => {
    if (forcedUsers) return !usageUsers?.length

    if (lazyGetEmployees.data?.total_count === 0) return true

    return false
  }

  const getRowCount = () => {
    if (forcedUsers) return usageUsers?.length

    if (typeof lazyGetEmployees?.data?.total_count === 'undefined') return 1000

    if (lazyGetEmployees?.data?.total_count === 0) return 0

    if (usageUsers.length === lazyGetEmployees?.data?.total_count) return usageUsers.length

    if (isNumber(lazyGetEmployees?.data?.total_count)) return usageUsers.length + 3

    return usageUsers.length
  }

  return (
    <>
      <div className={cx('header')}>
        <div className={cx('search-wrapper')}>
          <SearchInput
            placeholder={t('user_search_placeholder')}
            value={search}
            onChange={v => {
              if (lazyGetEmployees.isFetching) return

              dispatch(departmentsTreeSlice.actions.setSearch({ employees: v, page: 0 }))
            }}
            className={cx('search')}
            classNameWrapper={cx('searchWrapper', lazyGetEmployees.isFetching && 'loading')}
          />
        </div>
        {/* <CounterSelector /> */}
        {search.length === 0 && <AllSelector />}
      </div>
      {!getIsEmpty() && (
        <InfiniteLoader
          rowCount={getRowCount()}
          loadMoreRows={async () => {
            if (forcedUsers) return

            fetchMore()
          }}
          isRowLoaded={({ index }) => !!usageUsers.at(index)}
        >
          {({ onRowsRendered, registerChild }) => (
            <List
              containerProps={{ className: cx('employees') }}
              className={cx('employees')}
              ref={registerChild}
              onRowsRendered={onRowsRendered}
              rowRenderer={rowRender}
              rowCount={getRowCount()}
              rowHeight={28}
              width={764}
              height={550}
              overscanRowCount={10}
            ></List>
          )}
        </InfiniteLoader>
      )}
      {getIsEmpty() && <EmptyPlug />}
    </>
  )
}
