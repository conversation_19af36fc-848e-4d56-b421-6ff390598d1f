/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  EntityState,
  PayloadAction,
  createEntityAdapter,
  createSelector,
  createSlice,
} from '@reduxjs/toolkit'
import { IUser } from 'entities/employee'
import {
  CustomTreeState,
  DepartmentNode,
  FilterFns,
  SearchVariants,
  TreeDataType,
  UserNode,
  isDepartmentNode,
  isUserNode,
} from './types'
import { findNodeByKey, replaceNodeByKey, updateChildrenCheckedStatus } from './helpers'
import { loadTreeData, loadUsersCount } from './thunks'
import { ITargetData } from '@/entities/target'
import { IEmployeesStatUser } from '@/entities/employee/model/types'

export type DepartmentsTreeSliceState = {
  search: {
    employees: string
    page: number
    departments: string
  }
  filterFns?: FilterFns
  lastLoadedCountOfPeople?: number
  selectAll: boolean
  tree: CustomTreeState
  searchVariant: 'employees' | 'departments'
  users: EntityState<IUser, string>
  forceUsers?: IUser[]
  searchVariants: SearchVariants[]
}

export type DepatmentTreeState = {
  departmentsTree: DepartmentsTreeSliceState
}

export const usersAdapter = createEntityAdapter({
  selectId: (user: IUser) => user.id,
})

const initialState: DepartmentsTreeSliceState = {
  search: {
    employees: '',
    page: 0,
    departments: '',
  },
  searchVariants: ['departments', 'employees'],
  selectAll: false,
  tree: {
    checkedKeys: {},
    checkedDepartments: {},
    checkedUsers: {},
    treeData: [],
    halfChecked: {},
    expandedKeys: {},
    isLoadingKeys: {},
    loadedKeys: {},
  },
  searchVariant: 'departments',
  users: usersAdapter.getInitialState(),
}

export const departmentsTreeSlice = createSlice({
  name: 'departments-tree',
  initialState,
  reducers: {
    addUsers: (state, action: PayloadAction<IUser[]>) => {
      usersAdapter.addMany(state.users, action.payload)
    },
    removeUsers: (state, action: PayloadAction<IUser['id'][]>) => {
      usersAdapter.removeMany(state.users, action.payload)
    },
    removeAllUsers: state => {
      usersAdapter.removeAll(state.users)
    },
    setPage: (state, action: PayloadAction<DepartmentsTreeSliceState['search']['page']>) => {
      state.search.page = action.payload
    },
    setSearch: (state, action: PayloadAction<Partial<DepartmentsTreeSliceState['search']>>) => {
      state.search = { ...state.search, ...action.payload }
    },
    setSearchVariant: (
      state,
      action: PayloadAction<DepartmentsTreeSliceState['searchVariant']>,
    ) => {
      state.searchVariant = action.payload
    },
    setFiltersFns: (state, action: PayloadAction<DepartmentsTreeSliceState['filterFns']>) => {
      state.filterFns = { ...state.filterFns, ...action.payload }
    },
    setSearchVariants: (
      state,
      action: PayloadAction<DepartmentsTreeSliceState['searchVariants']>,
    ) => {
      state.searchVariants = action.payload
    },
    setForcedUsers: (state, action: PayloadAction<IUser[] | undefined>) => {
      state.forceUsers = action.payload
    },
    onConsistencyWithTargetData: (
      state,
      action: PayloadAction<ITargetData & { half_checked?: string[]; selectAll?: boolean }>,
    ) => {
      const { target_departments, target_users, half_checked, selectAll } = action.payload

      state.tree.checkedDepartments = target_departments?.reduce<Record<string, DepartmentNode>>(
        (acc, department) => {
          acc[department] = department as unknown as DepartmentNode
          return acc
        },
        {},
      )
      state.tree.checkedKeys = [...target_departments, ...target_users].reduce<
        Record<string, boolean>
      >((acc, department) => {
        acc[department] = true
        return acc
      }, {})
      state.tree.checkedUsers = target_users?.reduce<Record<string, UserNode>>(
        (acc, department) => {
          acc[department] = department as unknown as UserNode
          return acc
        },
        {},
      )
      state.tree.halfChecked = (half_checked ?? [])?.reduce<Record<string, DepartmentNode>>(
        (acc, department) => {
          acc[department] = department as unknown as DepartmentNode
          return acc
        },
        {},
      )

      state.selectAll = !!selectAll
    },
    onExpandNode: (state, action: PayloadAction<TreeDataType>) => {
      const treeData = state.tree.treeData
      const expandData = action.payload
      const isExpanded = !state.tree.expandedKeys[expandData.key]
      const updatedExpandedKeys = { ...state.tree.expandedKeys }
      // TODO: Добавить сюда также вариативность isAllSelected
      if (isExpanded) {
        updatedExpandedKeys[expandData.key] = true
      } else {
        delete updatedExpandedKeys[expandData.key]
      }

      const collapseChildren = (node: TreeDataType) => {
        node.children?.forEach(child => {
          delete updatedExpandedKeys[child.key]
          collapseChildren(child)
        })
      }

      const findAndCollapseChildren = (nodes: TreeDataType[]) => {
        for (const node of nodes) {
          if (node.key === expandData.key) {
            if (!isExpanded) {
              collapseChildren(node)
            }
            break
          } else if (node.children) {
            findAndCollapseChildren(node.children)
          }
        }
      }

      findAndCollapseChildren(treeData)

      const isEveryChildActive =
        !!expandData?.children &&
        expandData?.children.length > 0 &&
        expandData?.children?.every(
          ch => state.tree.checkedDepartments[ch.id] || state.tree.checkedUsers[ch.id],
        )

      const isSomeActive =
        !!expandData?.children &&
        expandData?.children?.some(
          ch => state.tree.checkedDepartments[ch.id] || state.tree.checkedUsers[ch.id],
        )

      const isAllNotActive = !isEveryChildActive && !isSomeActive

      if (isEveryChildActive) {
        state.tree.checkedDepartments = {
          ...state.tree.checkedDepartments,
          [expandData.key]: expandData,
        }
        if (state.tree.halfChecked[expandData.key]) delete state.tree.halfChecked[expandData.key]

        if (expandData.children) {
          expandData.children.forEach(child => {
            if (isUserNode(child) && state.tree.checkedUsers[child.id]) {
              state.tree.checkedUsers[child.id] = {
                ...state.tree.checkedUsers[child.id],
                parent: expandData,
              }
            }
          })
        }
      } else if (isSomeActive) {
        state.tree.halfChecked = {
          ...state.tree.halfChecked,
          [expandData.key]: expandData,
        }
        if (state.tree.checkedDepartments[expandData.key])
          delete state.tree.checkedDepartments[expandData.key]

        if (state.tree.checkedKeys[expandData.key]) delete state.tree.checkedKeys[expandData.key]
      } else if (isAllNotActive) {
        if (state.tree.halfChecked[expandData.key]) delete state.tree.halfChecked[expandData.key]
        // if (state.tree.checkedDepartments[expandData.key])
        //   delete state.tree.checkedDepartments[expandData.key]
      }

      state.tree.expandedKeys = updatedExpandedKeys
    },
    setTree: (state, action: PayloadAction<Partial<CustomTreeState>>) => {
      state.tree = { ...state.tree, ...action.payload }
    },
    setInitialTree: () => {
      return { ...initialState }
    },
    setSelectAll: (state, action: PayloadAction<boolean>) => {
      state.selectAll = action.payload

      state.tree.checkedDepartments = {}
      state.tree.checkedKeys = {}
      state.tree.checkedUsers = {}
      state.tree.expandedKeys = {}
      state.tree.halfChecked = {}
      state.tree.isLoadingKeys = {}
    },
    onCheckNode: (state, action: PayloadAction<TreeDataType>) => {
      const checkData = action.payload
      const isChecked = !state.tree.checkedKeys[checkData.key]
      const treeNodeData = findNodeByKey(state.tree.treeData, checkData.id) ?? checkData

      if (!treeNodeData) return state

      if (isChecked) {
        state.tree.checkedKeys[treeNodeData.key] = true
        if (isDepartmentNode(treeNodeData))
          state.tree.checkedDepartments[treeNodeData.key] = treeNodeData
        if (isUserNode(treeNodeData)) {
          state.tree.checkedUsers[treeNodeData.key] = treeNodeData
        }

        updateChildrenCheckedStatus(treeNodeData, isChecked, state.tree)

        let parent = treeNodeData?.parent

        while (parent) {
          const isEveryActive =
            !!parent.children && parent.children?.every(child => state.tree.checkedKeys[child.key])
          const isSomeNotActive =
            !!parent.children && parent.children?.some(child => !state.tree.checkedKeys[child.key])

          if (isEveryActive) {
            if (state.tree.halfChecked[parent.key]) delete state.tree.halfChecked[parent.key]
            state.tree.checkedKeys[parent.key] = true
            state.tree.checkedDepartments[parent.key] = parent

            // parent.children?.map(child => {
            //   if (state.tree.checkedUsers[child.key]) delete state.tree.checkedUsers[child.key]
            // })
          } else if (isSomeNotActive && isDepartmentNode(parent)) {
            state.tree.halfChecked[parent.key] = parent
            if (state.tree.checkedKeys[parent.key]) delete state.tree.checkedKeys[parent.key]
          } else {
            if (state.tree.halfChecked[parent.key]) delete state.tree.halfChecked[parent.key]
            if (state.tree.checkedKeys[parent.key]) delete state.tree.checkedKeys[parent.key]
          }

          parent = parent?.parent
        }

        if (
          isUserNode(treeNodeData) &&
          treeNodeData.variant === 'single' &&
          treeNodeData.parent?.id &&
          !state.tree.halfChecked[treeNodeData.parent?.id]
        ) {
          state.tree.halfChecked[treeNodeData.parent?.id] = treeNodeData.parent
        }
      } else {
        if (state.tree.checkedKeys[treeNodeData.key])
          delete state.tree.checkedKeys[treeNodeData.key]
        if (state.tree.halfChecked[treeNodeData.key])
          delete state.tree.halfChecked[treeNodeData.key]

        if (isDepartmentNode(treeNodeData) && state.tree.checkedDepartments[treeNodeData.key])
          delete state.tree.checkedDepartments[treeNodeData.key]
        if (isUserNode(treeNodeData) && state.tree.checkedUsers[treeNodeData.key])
          delete state.tree.checkedUsers[treeNodeData.key]

        updateChildrenCheckedStatus(treeNodeData, isChecked, state.tree)

        let parent = treeNodeData.parent

        while (parent) {
          const isEveryNotActive = parent.children?.every(
            child => !state.tree.checkedKeys[child.key] && !state.tree.halfChecked[child.key],
          )
          const isSomeActive = parent.children?.some(
            child => state.tree.checkedKeys[child.key] || state.tree.halfChecked[child.key],
          )
          const isEveryActive = parent.children?.every(child => state.tree.checkedKeys[child.key])

          if (isEveryActive) {
            if (state.tree.halfChecked[parent.key]) delete state.tree.halfChecked[parent.key]
            state.tree.checkedKeys[parent.key] = true
            state.tree.checkedDepartments[parent.key] = parent

            parent.children?.map(child => {
              if (state.tree.checkedUsers[child.key]) delete state.tree.checkedUsers[child.key]
              if (state.tree.checkedKeys[child.key]) delete state.tree.checkedKeys[child.key]
            })
          } else if (isEveryNotActive) {
            if (state.tree.halfChecked[parent.key]) delete state.tree.halfChecked[parent.key]
            if (state.tree.checkedDepartments[parent.key])
              delete state.tree.checkedDepartments[parent.key]
            if (state.tree.checkedKeys[parent.key]) delete state.tree.checkedKeys[parent.key]
          } else if (isSomeActive && isDepartmentNode(parent)) {
            state.tree.halfChecked[parent.key] = parent
            if (state.tree.checkedKeys[parent.key]) delete state.tree.checkedKeys[parent.key]
            if (state.tree.checkedDepartments[parent.key])
              delete state.tree.checkedDepartments[parent.key]
          }

          parent = parent.parent
        }
      }
    },
  },
  extraReducers: builder => {
    builder
      .addCase(loadTreeData.pending, (state, action) => {
        state.tree.isLoadingKeys[action.meta.arg.treeNode.id] = true
      })
      .addCase(loadTreeData.fulfilled, (state, action) => {
        const key = action.meta.arg.treeNode.id
        const treeNode = action.meta.arg.treeNode
        const data = action.payload

        const searchableTreeNode = findNodeByKey(state.tree.treeData, key)

        if (state.tree.isLoadingKeys[key]) {
          delete state.tree.isLoadingKeys[key]
        }

        if (!searchableTreeNode) return state

        if (!searchableTreeNode.children) searchableTreeNode.children = []

        const first = data?.[0]
        searchableTreeNode.isLeaf = !(data?.length > 1 || first?.id !== key)

        data.forEach(department => {
          if (department.id === searchableTreeNode.id && searchableTreeNode?.children) {
            // users
            searchableTreeNode.children.push(
              ...((department.users?.map<TreeDataType>(user => {
                const newUser = user as IEmployeesStatUser
                return {
                  ...newUser,
                  id: newUser.id,
                  key: newUser.id,
                  title: `${newUser.first_name} ${newUser.last_name} ${newUser.email ? `(${newUser.email})` : ''}`,
                  type: 'user',
                  users_count: 0,
                  isLeaf: true,
                  parent: searchableTreeNode as DepartmentNode,
                }
              }) ?? []) as any[]),
            )
          } else if (searchableTreeNode?.children) {
            searchableTreeNode.children.push({
              ...department,
              type: 'department',
              key: department.id,
              isLeaf: false,
              parent: searchableTreeNode,
              id: department?.id,
              title: department?.title,
              users_count: 0,
            } as any)
          }
        })

        if (state.tree.checkedKeys[searchableTreeNode.id] && searchableTreeNode?.children) {
          searchableTreeNode?.children?.forEach(child => {
            state.tree.checkedKeys[child.id] = true
            if (isDepartmentNode(child)) state.tree.checkedDepartments[child.id] = { ...child }
            if (isUserNode(child) && !state.tree.checkedUsers[child.id]) {
              state.tree.checkedUsers[child.id] = { ...child }
            }
          })
        }
        state.tree.loadedKeys[treeNode.id] = true
        state.tree.treeData = replaceNodeByKey(state.tree.treeData, treeNode.id, searchableTreeNode)
        departmentsTreeSlice.caseReducers.onExpandNode(state, {
          type: 'departments-tree/onExpandNode',
          payload: searchableTreeNode,
        })
      })
      .addCase(loadTreeData.rejected, (state, action) => {
        delete state.tree.isLoadingKeys[action.meta.arg.treeNode.id]
        delete state.tree.loadedKeys[action.meta.arg.treeNode.id]
      })
      .addCase(loadUsersCount.fulfilled, (state, action) => {
        state.lastLoadedCountOfPeople = action.payload?.result
      })
  },
})

export const selectAllUsers = (state: DepatmentTreeState) =>
  usersAdapter.getSelectors(() => state.departmentsTree.users).selectAll(state)

export const selectSearch = (state: DepatmentTreeState) => state.departmentsTree.search

export const selectPage = (state: DepatmentTreeState) => state.departmentsTree.search.page

export const selectSearchVariant = (state: DepatmentTreeState) =>
  state.departmentsTree.searchVariant

export const selectTree = (state: DepatmentTreeState) => state.departmentsTree.tree

export const selectSelectAll = (state: DepatmentTreeState) => state.departmentsTree.selectAll

export const selectEmployeesSearch = (state: DepatmentTreeState) =>
  state.departmentsTree.search.employees

export const selectDepartmentsSearch = (state: DepatmentTreeState) =>
  state.departmentsTree.search.employees

export const selectLastLoadedCountOfPeople = (state: DepatmentTreeState) =>
  state.departmentsTree.lastLoadedCountOfPeople

export const selectIsCanPressSave = (state: DepatmentTreeState) =>
  Object.keys(state.departmentsTree.tree.checkedKeys).length > 0 || state.departmentsTree.selectAll

export const selectFilteredTreeData = createSelector(
  [(state: DepatmentTreeState) => state],
  state => {
    // const tree: TreeDataType[] = state.departmentsTree.tree.treeData;
    return state.departmentsTree.tree.treeData.filter(v => {
      if (isDepartmentNode(v)) {
        return state.departmentsTree?.filterFns?.filterDepartments?.(v) ?? true
      }
      if (isUserNode(v)) {
        return state.departmentsTree?.filterFns?.filterUsers?.(v) ?? true
      }

      return true
    })
  },
)

export const selectFilteredUsers = createSelector(
  [(state: DepatmentTreeState) => state, selectAllUsers],
  (state, users) => {
    return users.filter(
      e =>
        state.departmentsTree?.filterFns?.filterUsers?.({
          ...e,
          title: `${e.first_name} ${e.last_name} ${e.email ? `(${e.email})` : ''}`,
          key: e.id,
          type: 'user',
          parent: {
            id: String(e.department?.id),
            key: String(e.department?.id),
            title: String(e?.department?.title),
            type: 'department',
          },
          email: e.email,
          last_name: String(e.last_name),
          first_name: String(e.first_name),
          middle_name: String(e.middle_name),
          // eslint-disable-next-line i18next/no-literal-string
          variant: 'single',
        }) ?? true,
    )
  },
)

export const selectCurrentSelectedUsersCount = createSelector(
  [
    (state: DepatmentTreeState) => state.departmentsTree.selectAll,
    (state: DepatmentTreeState) => state.departmentsTree.tree,
    (state: DepatmentTreeState) => state.departmentsTree.lastLoadedCountOfPeople,
    (state: DepatmentTreeState) => state.departmentsTree.forceUsers,
  ],
  (selectAll, tree, lastLoadedCountOfPeople, forceUsers) => {
    if (selectAll) {
      if (forceUsers) {
        return forceUsers.filter(u => !tree.checkedUsers[u.id])?.length
      } else {
        const excludedDepartmentsCount = Object.keys(tree.checkedDepartments).length
        const excludedUsersCount = Object.keys(tree.checkedUsers).length

        if (!lastLoadedCountOfPeople) {
          return undefined
        }

        if (excludedDepartmentsCount === 0 && excludedUsersCount === 0) {
          return lastLoadedCountOfPeople
        }

        if (excludedDepartmentsCount > 0) {
          const individuallyExcludedUsers = Object.entries(tree.checkedUsers).filter(([, user]) => {
            if (typeof user === 'string') return true

            const parentId = user?.parent?.id
            if (parentId && tree.checkedDepartments[parentId]) {
              return false
            }
            return true
          }).length

          const result = lastLoadedCountOfPeople - individuallyExcludedUsers
          return result > 0 ? result : undefined
        } else {
          const result = lastLoadedCountOfPeople - excludedUsersCount
          return result > 0 ? result : undefined
        }
      }
    } else {
      const checkedDepartmentsCount = Object.keys(tree.checkedDepartments).length
      const individualUsersCount = Object.entries(tree.checkedUsers).reduce((count, [, user]) => {
        if (typeof user === 'string') return count + 1

        if (user && typeof user === 'object' && 'parent' in user) {
          const parentId = user.parent?.id
          if (parentId && tree.checkedDepartments[parentId]) {
            return count
          }
        }
        return count + 1
      }, 0)

      if (checkedDepartmentsCount > 0) {
        return undefined
      }

      return individualUsersCount > 0 ? individualUsersCount : undefined
    }
  },
)

export const selectSearchVariants = createSelector(
  [(state: DepatmentTreeState) => state],
  state => state.departmentsTree.searchVariants,
)

export const selectForcedUsers = createSelector(
  (state: DepatmentTreeState) => state,
  state => state.departmentsTree.forceUsers,
)

export const selectFilteredForcedUsers = createSelector(
  [(_: DepatmentTreeState, search: string) => search, selectForcedUsers],
  (search, users) => {
    return users?.filter(u => {
      const searchableString =
        (u.first_name?.toLocaleLowerCase() ?? '') +
        (u.last_name?.toLocaleLowerCase() ?? '') +
        (u.email?.toLocaleLowerCase() ?? '')

      const searchWords = search
        .split(' ')
        .filter(Boolean)
        .map(w => w.toLocaleLowerCase())

      return searchWords.every(word => searchableString.includes(word.toLocaleLowerCase()))
    })
  },
)

export const departmentsTreeSliceActions = departmentsTreeSlice.actions
