import styles from './tree.module.scss'
import classNamesBind from 'classnames/bind'
import BoxIcon from '../../ui/Icon/icons/components/BoxIcon'
import { IconWrapper } from '../../ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const EmptyPlug = () => {
  const { t } = useTranslation('modals__organization-tree')

  return (
    <div className={cx('plug__container')}>
      <div className={cx('plug__icon', 'box_icon')}>
        <IconWrapper color='gray60'>
          <BoxIcon />
        </IconWrapper>
      </div>
      <h2 className={cx('plug__text')}>{t('commons:nothing_found')}</h2>
    </div>
  )
}
