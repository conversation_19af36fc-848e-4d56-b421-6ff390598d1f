import { Department } from './types'
import { globalBaseApi, GlobalEndpointBuilder } from '@/store/services/endpoints/base'

type DataResponse = {
  data: Department[]
  total_count: number
  limit: null
  offset: null
}

const USER_PREFIX_URL = '/users/api/v1/'

export const createDepartmentsTreeEndpoints = (builder: GlobalEndpointBuilder) => ({
  getModalDepartments: builder.query<
    DataResponse,
    {
      department_id?: string
      search?: string
      user_search?: string
      sort_order?: 'asc' | 'desc'
      limit?: number
      offset?: number
    } | void
  >({
    query: params => {
      return {
        url: `${USER_PREFIX_URL}departments/modal/v1/tree`,
        params: {
          limit: 10000,
          ...(params ?? {}),
        },
      }
    },
  }),
  getModalUsersCount: builder.mutation<
    {
      users_count: number
    },
    {
      department_ids: string[]
      exclude_user_ids?: string[]
    }
  >({
    query: ({ department_ids, exclude_user_ids }) => {
      return {
        url: `${USER_PREFIX_URL}departments/modal/v1/tree/users-count`,
        body: {
          departments_ids: department_ids,
          exclude_user_ids,
        },
        method: 'POST',
      }
    },
  }),
})

export const departmentsTreeApiWithEndpoints = globalBaseApi.injectEndpoints({
  endpoints: createDepartmentsTreeEndpoints,
  overrideExisting: true,
})

export const { useGetModalDepartmentsQuery } = departmentsTreeApiWithEndpoints
