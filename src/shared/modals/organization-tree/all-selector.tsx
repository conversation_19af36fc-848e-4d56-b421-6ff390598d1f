import { useTranslation } from 'react-i18next'
import { useAppDispatch, useAppSelector } from '@/store'
import { departmentsTreeSlice, selectSelectAll } from './departments-tree-slice'
import styles from './tree.module.scss'
import classNamesBind from 'classnames/bind'
import { OrganizationTreeContext } from './organization-tree.context'
import { useStrictContext } from '@/shared/helpers'
const cx = classNamesBind.bind(styles)

export const AllSelector = () => {
  const { t } = useTranslation('modals__organization-tree')

  const isAllSelect = useAppSelector(selectSelectAll)
  const dispatch = useAppDispatch()
  const treeContext = useStrictContext(OrganizationTreeContext)

  if (!treeContext?.showSelectAll) return false

  return (
    <span
      className={cx('selectAll')}
      onClick={() => dispatch(departmentsTreeSlice.actions.setSelectAll(!isAllSelect))}
    >
      {isAllSelect ? t('cancel_selection') : t('select_all')}
    </span>
  )
}
