import { createAsyncThunk } from '@reduxjs/toolkit'
import { DepartmentUser, isUserNode, TreeDataType, UserNode } from './types'
import { departmentsTreeApiWithEndpoints } from './departments-tree-endpoints'
import { filteredNodesByIds, getAllDepartmentsNodes } from './helpers'
import { DepartmentsTreeSliceState, DepatmentTreeState } from './departments-tree-slice'
import { OrganizationTreeContextProps } from './organization-tree.context'
import { arrayToRecord } from '@/shared/helpers'
import { departmentAPI } from '@/entities/department'

export const loadTreeData = createAsyncThunk(
  'departments-tree/loadData',
  async (
    { treeNode, department_search }: { treeNode: TreeDataType; department_search: string },
    thunkAPI,
  ) => {
    try {
      const dispatch = thunkAPI.dispatch

      const { data: departmentsData } = await dispatch(
        departmentsTreeApiWithEndpoints.endpoints.getModalDepartments.initiate({
          department_id: treeNode.id,
          search: department_search || undefined,
        }),
      )

      const updatedTreeNode = {
        ...treeNode,
      } as TreeDataType & { users?: DepartmentUser[] }

      if (treeNode.type === 'department' && treeNode.exists_users) {
        const { data: users } = await dispatch(
          departmentAPI.endpoints.getDepartmentsModalUsers.initiate({
            department_id: treeNode.id,
          }),
        )

        const usersInDepartment = users ? ([...users] as DepartmentUser[]) : undefined
        updatedTreeNode.users = usersInDepartment
      }

      if (!departmentsData) throw new Error('No data received')

      return [...departmentsData.data, updatedTreeNode]
    } catch (error) {
      console.error(error)
      return thunkAPI.rejectWithValue('error')
    }
  },
)

export const loadUsersCount = createAsyncThunk(
  'departments-tree/loadUsersCount',
  async (
    {
      treeSlice,
      selectAll,
      treeContext,
    }: {
      treeSlice: DepartmentsTreeSliceState
      selectAll: boolean
      treeContext?: OrganizationTreeContextProps
    },
    thunkAPI,
  ) => {
    try {
      const tree = treeSlice.tree
      const dispatch = thunkAPI.dispatch
      const store = thunkAPI.getState() as DepatmentTreeState

      let department_ids: string[] = []
      let exclude_user_ids: string[] = []
      let checkedTreeNodes: TreeDataType[] = []
      let unCheckedTreeNodes: TreeDataType[] = []
      if (selectAll) {
        const { checkedIds, unCheckedIds } = filteredNodesByIds(
          getAllDepartmentsNodes(tree),
          store.departmentsTree.tree.checkedDepartments,
        )
        checkedTreeNodes = checkedIds
        unCheckedTreeNodes = unCheckedIds
        exclude_user_ids = Object.keys(tree.checkedUsers ?? {})

        department_ids = unCheckedIds?.map(d => d.id)
      } else {
        department_ids = Object.keys(tree.checkedDepartments ?? {})
      }
      let users_count = 0
      if (department_ids.length) {
        const { data } = await dispatch(
          departmentsTreeApiWithEndpoints.endpoints.getModalUsersCount.initiate({
            department_ids: department_ids,
            exclude_user_ids: exclude_user_ids?.length > 0 ? exclude_user_ids : undefined,
          }),
        )
        users_count = data?.users_count ?? 0
      }
      if (selectAll && treeSlice.forceUsers) {
        users_count = treeSlice.forceUsers.filter(u => tree.checkedUsers[u.id])?.length
      }
      if (!selectAll) {
        const users = Object.entries(tree.checkedUsers ?? {}).reduce<UserNode[]>((prev, user) => {
          const value = user[1]
          const getExistsInDepartments = () => {
            if (typeof value === 'string') return department_ids.includes(value)

            if (isUserNode(value) && value.parent?.id)
              return department_ids.includes(value.parent?.id)

            return false
          }

          if (!getExistsInDepartments()) {
            prev.push(value)
          }
          return prev
        }, [])
        users_count += users?.length
      }

      const getDepartmentsIds = () => {
        if (treeContext?.isDelete) {
          return selectAll ? checkedTreeNodes?.map(n => n.id) : unCheckedTreeNodes?.map(n => n.id)
        }

        const allDepartments = getAllDepartmentsNodes(tree)
        const idsRecord = arrayToRecord(department_ids)

        if (selectAll) return allDepartments.filter(d => idsRecord[d.id]).map(d => d.id)

        return department_ids
      }

      const getUsersIds = () => {
        if (selectAll && treeSlice.forceUsers) {
          return treeSlice.forceUsers.filter(u => !tree.checkedUsers[u.id])
        }

        return Object.keys(tree.checkedUsers ?? {})
      }

      return {
        result: users_count,
        department_ids: getDepartmentsIds() ?? [],
        users_ids: getUsersIds(),
      }
    } catch (error) {
      return thunkAPI.rejectWithValue('error')
    }
  },
)
