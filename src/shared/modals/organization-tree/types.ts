export type BaseNode = {
  title: string
  key: string
  className?: string
  isLeaf?: boolean
  checked?: boolean
}

export type DepartmentNode = BaseNode &
  Department & {
    type: 'department'
    children?: DepartmentNode[] | UserNode[]
    parent?: DepartmentNode
  }

export type UserNode = BaseNode &
  DepartmentUser & {
    type: 'user'
    parent?: DepartmentNode
    children?: never
    variant?: 'department' | 'single'
  }

export type TreeDataType = DepartmentNode | UserNode

export const isUserNode = (node: TreeDataType): node is UserNode => {
  return node?.type === 'user'
}

export const isDepartmentNode = (node: TreeDataType): node is DepartmentNode => {
  return node?.type === 'department'
}

export type CustomTreeState = {
  treeData: TreeDataType[]
  checkedKeys: Record<string, boolean>
  expandedKeys: Record<string, boolean>
  checkedDepartments: Record<string, DepartmentNode>
  halfChecked: Record<string, DepartmentNode>
  checkedUsers: Record<string, UserNode>
  isLoadingKeys: Record<string, boolean>
  loadedKeys: Record<string, boolean>
}

export type Department = {
  id: string
  title: string
  exists_users?: boolean
  departments_ids?: UUID[]
  users?: DepartmentUser[]
}

export type DepartmentUser = {
  id: string
  first_name: string
  last_name: string
  middle_name: string
  email: string
}

export type FilterFns = {
  filterDepartments?: (department: DepartmentNode) => boolean
  filterUsers?: (user: UserNode) => boolean
}
export type SearchVariants = 'employees' | 'departments'

export type HandleSelectValue = CustomTreeState & {
  selectAll?: boolean
  countOfPeople: number
  department_ids: string[]
  users_ids: string[]
}
