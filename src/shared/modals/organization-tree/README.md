selectAll = false

кликаю на элемент

  если я его выбираю(isChecked = true)
    1) делаем его активным
    2) смотрим всех его дочерних элементов, если такие есть -> ставим их полностью активными
    3) смотрим родительские элементы, если такие есть -> ставим их активными
        смотрим в родителе, если все дочерние активные - ставим полностью активным
        смотрим в родителе, если какой либо дочерний не активный - ставим полуактивным

  

  если я его отжимаю(isChecked = false)
    1) делаем его неактивным
    2) смотрим всех его дочерних элементов, если такие есть -> ставим их неактивными
    3) смотрим родительские элементы, если такие есть -> ставим их неактивными в связи со следующим правилом:
      - если все дочерние неактивные - ставим полностью неактивным
      - если какой-либо активный - ставим наполовину активным 


selectAll = true

  кликаю на элемент

  если я его выбираю(isExcluded = true)
    1) делаем его активным   delete state.tree.exclude...[key]
    2) смотрим всех его дочерних элементов, если такие есть -> ставим их полностью активными     delete state.tree.exclude...[key]
    3) смотрим родительские элементы, если такие есть -> ставим их активными
        смотрим в родителе, если все дочерние активные - ставим полностью активным
        смотрим в родителе, если какой либо дочерний не активный - ставим полуактивным

  

  если я его отжимаю(isExcluded = false)
    1) делаем его неактивным exclude...[id] = true
    2) смотрим всех его дочерних элементов, если такие есть -> ставим их неактивными
    3) смотрим родительские элементы, если такие есть -> ставим их неактивными в связи со следующим правилом:
      - если все дочерние неактивные - ставим полностью неактивным -> everyExcluded
      - если какой-либо активный и какой либо неактивный - ставим наполовину активным  -> someNotExcluded


План:
 Доработать onExpand при selectAll = true
 Доработать onCheck при selectAll = true