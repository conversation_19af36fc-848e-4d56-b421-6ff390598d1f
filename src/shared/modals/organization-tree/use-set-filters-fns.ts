import { useAppDispatch } from 'store'
import { departmentsTreeSlice } from './departments-tree-slice'
import { FilterFns } from './types'
import { useEffect } from 'react'

type Props = FilterFns | undefined

export const useSetFiltersFns = (props: Props) => {
  const dispatch = useAppDispatch()

  useEffect(() => {
    dispatch(departmentsTreeSlice.actions.setFiltersFns(props))

    return () => {
      dispatch(departmentsTreeSlice.actions.setFiltersFns(undefined))
    }
  }, [dispatch, props])
}

export const useOnSetFiltersFns = () => {
  const dispatch = useAppDispatch()

  const onSetFilters = (props?: FilterFns) => {
    dispatch(departmentsTreeSlice.actions.setFiltersFns(props))
  }

  return { onSetFilters }
}
