import { IUser } from 'entities/employee'
import {
  CustomTreeState,
  DepartmentNode,
  TreeDataType,
  isDepartmentNode,
  isUserNode,
} from './types'

export const updateDescendantsCheckedStatus = (
  node: TreeDataType,
  isChecked: boolean,
): TreeDataType => {
  node.checked = isChecked

  node.children?.forEach(child => {
    updateDescendantsCheckedStatus(child, isChecked)
  })

  return node
}

export function findNodeByKey(treeData: TreeDataType[], key: string): TreeDataType | undefined {
  for (const node of treeData) {
    if (node.key === key) {
      return node
    }
    if (node.children) {
      const found = findNodeByKey(node.children, key)
      if (found) {
        return found
      }
    }
  }
  return undefined
}

export function replaceNodeByKey(
  treeData: TreeDataType[],
  key: string,
  newNode: TreeDataType,
): TreeDataType[] {
  return treeData.map(node => {
    if (node.key === key) {
      return newNode
    }
    if (node.children) {
      return { ...node, children: replaceN<PERSON><PERSON>y<PERSON><PERSON>(node.children, key, newNode) }
    }
    return node
  }) as TreeDataType[]
}

export function hasChildrenLength(node: TreeDataType): boolean {
  if (!node.children) return true

  return node.children && node.children.length > 0
}

export const findParent = (treeData: TreeDataType[], nodeKey: string): TreeDataType | null => {
  for (const node of treeData) {
    if (node.children && node.children.some(child => child.key === nodeKey)) {
      return node
    }
    const foundParent = findParent(node.children || [], nodeKey)
    if (foundParent) {
      return foundParent
    }
  }
  return null
}

export const getParentNodes = (currentNode: TreeDataType): TreeDataType[] => {
  const parents: TreeDataType[] = []

  let node: TreeDataType | undefined = currentNode

  while (node && node.parent) {
    parents.push(node.parent)

    node = node.parent
  }

  return parents.reverse()
}

export const updateParent = (state: CustomTreeState, parent?: TreeDataType) => {
  if (!parent) return

  const hasAllCheckedChildren = parent?.children?.every(child => state.checkedKeys[child.id])
  const hasSomeCheckedChildren = parent?.children?.some(child => state.checkedKeys[child.id])

  if (hasAllCheckedChildren) {
    state.checkedKeys[parent.id] = true
    state.checkedDepartments[parent.id] = parent as DepartmentNode

    updateParent(state, parent?.parent)
  } else if (hasSomeCheckedChildren) {
    state.halfChecked[parent.id] = parent as DepartmentNode
    delete state.checkedDepartments[parent.id]
  } else {
    delete state.checkedDepartments[parent.id]
    delete state.halfChecked[parent.id]
    delete state.checkedKeys[parent.id]
  }
}

export const getUsersWithoutCheckedDepartments = (
  state: Pick<CustomTreeState, 'checkedKeys' | 'checkedUsers' | 'checkedDepartments'>,
) =>
  Object.entries(state.checkedUsers).reduce<number>((prev, userField) => {
    const user = userField[1]
    if (state.checkedUsers[user.id]) {
      if (user?.parent && !state.checkedDepartments[user?.parent?.id]) {
        prev += 1
      }
    }

    return prev
  }, 0)

type GetOnCheckedProps = {
  tree: CustomTreeState
  node: TreeDataType
  selectAll?: boolean
}

export const getOnChecked = ({ node, tree, selectAll }: GetOnCheckedProps): boolean => {
  const isDepartment = isDepartmentNode(node)
  const isUser = isUserNode(node)

  const getResult = () => {
    if (isDepartment) {
      return (
        !!tree.checkedKeys[node.id] ||
        !!tree.halfChecked[node.id] ||
        !!tree.checkedDepartments[node.id]
      )
    }

    if (isUser) {
      return !!tree.checkedUsers[node.id] || !!tree.checkedKeys[node.id]
    }

    return false
  }

  const result = getResult()

  return selectAll ? !result : result
}

export const isUseCustomMinus = ({ node, tree }: GetOnCheckedProps): boolean => {
  if (tree.halfChecked[node?.id]) return true

  return false
}

type getOnCheckForUserProps = {
  tree: CustomTreeState
  selectAll?: boolean
  user: Pick<IUser, 'id' | 'department'>
}
export const getOnCheckForUser = ({ tree, user, selectAll }: getOnCheckForUserProps) => {
  const getResult = () => {
    const existsInKeys = !!tree.checkedKeys[user.id] || !!tree.checkedUsers[user.id]
    const isDepartmentChecked =
      !!user?.department?.id && !!tree.checkedDepartments[user?.department?.id]
    // const isExludeParameter = selectAll ? !!tree.checkedKeys[user.id] : !tree.checkedKeys[user.id]

    return existsInKeys || !!isDepartmentChecked
  }

  const result = getResult()

  return selectAll ? !result : result
}

type Predicate<T> = (item: T) => boolean

export function createArrayCheckers<T>(predicate: Predicate<T>) {
  return {
    some: (array: T[]): boolean => array?.some(predicate),
    every: (array: T[]): boolean => array?.every(predicate),
  }
}

export const updateChildrenCheckedStatus = (
  node: TreeDataType,
  isChecked: boolean,
  tree: CustomTreeState,
) => {
  if (isChecked) {
    tree.checkedKeys[node.key] = true
    if (isDepartmentNode(node)) tree.checkedDepartments[node.key] = node
    if (isUserNode(node)) tree.checkedUsers[node.id] = node
  } else {
    delete tree.checkedKeys[node.key]
    if (isDepartmentNode(node)) {
      if (tree.checkedDepartments[node.key]) delete tree.checkedDepartments[node.key]
      if (tree.halfChecked[node.key]) delete tree.halfChecked[node.key]
    }

    if (isUserNode(node) && tree.checkedUsers[node.key]) delete tree.checkedUsers[node.key]
  }

  node.children?.forEach(child => {
    if (isChecked) {
      tree.checkedKeys[child.key] = true
      if (isDepartmentNode(child)) tree.checkedDepartments[child.key] = child
      if (isUserNode(child)) tree.checkedUsers[child.id] = child
    } else {
      delete tree.checkedKeys[child.key]
      if (isDepartmentNode(child) && tree.checkedDepartments[child.key])
        delete tree.checkedDepartments[child.key]
      if (isUserNode(child) && tree.checkedUsers[child.key]) delete tree.checkedUsers[child.key]
    }
    updateChildrenCheckedStatus(child, isChecked, tree)
  })
}
export const findAndToggleNode = (
  nodes: TreeDataType[],
  isChecked: boolean,
  checkDataId: string,
  tree: CustomTreeState,
) => {
  for (const node of nodes) {
    if (node.key === checkDataId) {
      updateChildrenCheckedStatus(node, isChecked, tree)
      break
    } else if (node.children) {
      findAndToggleNode(node.children, isChecked, checkDataId, tree)
    }
  }
}

export const getAllDepartmentsNodes = (tree: CustomTreeState) => {
  const nodes: TreeDataType[] = []

  const updateChildren = (children: TreeDataType[]) => {
    children?.forEach(child => {
      if (isDepartmentNode(child)) nodes.push(child)
      if (child.children) updateChildren(child.children)
    })
  }
  if (tree) updateChildren(tree.treeData)

  return nodes
}

export const filteredNodesByIds = (nodes: TreeDataType[], ids: Record<string, DepartmentNode>) => {
  const checkedIds: TreeDataType[] = []
  const unCheckedIds: TreeDataType[] = []

  const handleNode = (node: TreeDataType) => {
    if (ids[node.key]) {
      checkedIds.push(node)
    } else {
      unCheckedIds.push(node)
    }
    if (node.children) node.children.map(handleNode)
  }

  nodes.map(handleNode)

  return {
    checkedIds,
    unCheckedIds,
  }
}

export const getCountOfEmployyesInNodes = (nodes: TreeDataType[]) => {
  let count = 0

  const updateChildren = (children: TreeDataType[]) => {
    children?.forEach(child => {
      if (isUserNode(child)) count++
      if (child.children) updateChildren(child.children)
    })
  }
  if (nodes) updateChildren(nodes)

  return count
}
