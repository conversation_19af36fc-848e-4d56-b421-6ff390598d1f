import { createStrictContext } from '@/shared/helpers'
import { FilterFns, HandleSelectValue } from './types'
import { PropsWithChildren } from 'react'

export type OrganizationTreeContextProps = {
  handleSelect?: (state: HandleSelectValue) => void
  open: boolean
  setOpen: (v: boolean) => void
  isDelete?: boolean
  isCanEmpty?: boolean
  filterFns?: FilterFns
  disableUncheck?: boolean
  requiredIds?: string[]
  showSelectAll?: boolean
}

export const OrganizationTreeContext = createStrictContext<OrganizationTreeContextProps>()

export const OrganizationTreeProvider = ({
  children,
  ...value
}: PropsWithChildren<OrganizationTreeContextProps>) => {
  return (
    <OrganizationTreeContext.Provider
      value={{
        ...value,
        showSelectAll: value?.showSelectAll ?? true,
      }}
    >
      {children}
    </OrganizationTreeContext.Provider>
  )
}
