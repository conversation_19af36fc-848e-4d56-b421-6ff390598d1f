import { departmentsTreeApiWithEndpoints } from './departments-tree-endpoints'

export { LazyOrganizationModal as OrganizationTree } from './lazy-organization-tree'
export {
  departmentsTreeSlice,
  selectTree,
  selectLastLoadedCountOfPeople,
  selectCurrentSelectedUsersCount,
  selectSelectAll,
  departmentsTreeSliceActions
} from './departments-tree-slice'
export { type CustomTreeState } from './types'
export * from './helpers'
export const {
  useGetModalDepartmentsQuery,
  useGetModalUsersCountMutation,
  useLazyGetModalDepartmentsQuery,
} = departmentsTreeApiWithEndpoints

export { useRestoreTreeByTargetData } from './use-restore-tree'
export { useResetTreeCheck } from './use-reset-tree-check'
export { useUnmountOrganizationTree } from './use-unmount-organization-tree'
export { useSetUsers } from './use-set-users'
export { useOnSetFiltersFns, useSetFiltersFns } from './use-set-filters-fns'
export { useSetSearchVariants } from './use-set-search-variants'
export { OrganizationTreeProvider } from './organization-tree.context'
export { useResetModalSearch } from './use-reset-modal-search'
