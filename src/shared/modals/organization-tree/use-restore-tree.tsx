import { useAppDispatch } from '@/store'
import { ITargetData } from '@/entities/target'
import { departmentsTreeSlice } from './departments-tree-slice'

export const useRestoreTreeByTargetData = () => {
  const dispatch = useAppDispatch()

  const onRestore = (data: ITargetData & { half_checked?: string[]; selectAll?: boolean }) => {
    dispatch(departmentsTreeSlice.actions.onConsistencyWithTargetData(data))
  }

  return onRestore
}

export const useRestoreTreeByEntities = () => {
  const dispatch = useAppDispatch()

  const onRestore = (data: ITargetData) => {
    dispatch(departmentsTreeSlice.actions.onConsistencyWithTargetData(data))
  }

  return onRestore
}
