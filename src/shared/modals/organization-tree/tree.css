.rc-tree-indent-unit {
  display: inline-block;
  width: 16px;
}

.rc-tree-list-scrollbar-thumb{
  background: var(--color-gray-60) !important;
  width: 3px !important;
}

.rc-tree-list{
  width: 100%;
}

.rc-tree-list-holder{
  height: 550px;
}

.rc-tree-node-content-wrapper{
  display: flex;
  flex-direction: row;
}

.rc-tree-indent {
  display: inline-block;
  height: 0;
  vertical-align: bottom;
}

.rc-tree-switcher{
  align-self: flex-start;
  margin-right: 8px;
}

.rc-tree-treenode-loading{
  cursor: wait;
}

.rc-tree-treenode{
  display: flex;
  align-items: center;
}

.row {
  display: flex;
  align-items: center;
  gap: 8px;
}
