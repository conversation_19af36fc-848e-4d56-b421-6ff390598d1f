import { FC } from 'react'
import styles from './delete-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { DeleteModalProps } from './delete-modal.d'
import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const DeleteModal: FC<DeleteModalProps.Props> = props => {
  const { t } = useTranslation('modals__delete-modal')
  const {
    className,
    id,
    onClose,
    active,
    setActive,
    title,
    text,
    onCloseIDs,
    ids,
    onCloseWithoutData,
  } = props

  const handleClose = (isClose?: boolean, id?: UUID | null, ids?: UUID[] | null) => {
    onCloseWithoutData && onCloseWithoutData(!!isClose)
    onCloseIDs && onCloseIDs(ids)
    onClose && onClose(id)
    setActive(false)
  }

  return (
    <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
      <div className={cx('title')}>{title || t('title')}</div>
      <div className={cx('subtitle')}>{text || t('subtitle')}</div>

      <div className={cx('buttonWrapper')}>
        <Button color='gray' size='big' fullWidth onClick={() => handleClose(true)}>
          {t('commons:cancel')}
        </Button>
        <Button size='big' color='red' fullWidth onClick={() => handleClose(false, id, ids)}>
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}
