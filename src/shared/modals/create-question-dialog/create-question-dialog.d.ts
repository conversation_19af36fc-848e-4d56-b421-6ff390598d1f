import { IAnswer } from '@/shared/types/store/modules'

export declare namespace CreateQuestionDialogProps {
  interface Own {
    open: boolean
    onClose: () => void
    moduleId: UUID
    refetch?: () => void
    refetch2?: () => void
  }

  interface Inputs {
    text: string
    isMulti: boolean
    answers: IAnswer[]
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch
}

export {}
