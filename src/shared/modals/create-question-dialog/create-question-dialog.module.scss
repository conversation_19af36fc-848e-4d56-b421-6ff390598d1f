@use "../../../shared/assets/styles/mixins/text";

.dialog {
  padding-bottom: 40px;
}
.content {
  overflow-y: auto;
  padding: 5px 40px !important;
}

.feilds {
  display: flex;
  flex-direction: column;
}

.description {
  height: 98px;
  min-height: 90px;
  max-height: 98px;
}

.dialog {
  gap: 12px;
  max-height: calc(100% - 64px);
  max-width: 600px;
  overflow-y: auto;
  width: calc(100vw - 64px);
}

.content {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  gap: 12px;
  max-height: calc(100dvh - 300px);
  width: 100%;
}

.field {
  align-items: center;
  display: flex;
  gap: 8px;
}

.form {
  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: hidden;
}

.title {
  @include text.title(30px);
  display: flex;
  flex: 0 0 auto;
  padding: 30px 0 !important;

  text-align: center;
}

.error-text {
  color: #ff8577;
  font-family: "TT Norms Pro";
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-top: 12px;
  text-align: center;
}

.field + .field {
  margin-top: 20px;
  width: 100%;
}

.footer {
  align-items: center;
  display: flex;
  flex: 0 0 auto;
  justify-content: center;
  width: 100%;
}

.answers {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.header {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.answersTitle {
  @include text.title(20px);
  margin-top: 30px;
}

.addAnswer {
  @include text.main(16px);

  color: var(--color-primary);

  cursor: pointer;
  display: flex;
  justify-content: center;
  text-decoration: underline;
  width: 100%;
}

.answerItem {
  display: flex;
  flex-direction: column;
  margin: 10px 0;
  .answerTitle {
    align-items: center;
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
  }
}

.answerField {
  + .answerField {
    margin-top: 10px;
  }
}

.button {
  max-width: 350px;
  width: 100%;
}

.answer__card {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.answer__checkbox {
  margin-top: 5px;
}

.errorTip {
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-top: 8px;
  color: var(--color-warn)
}