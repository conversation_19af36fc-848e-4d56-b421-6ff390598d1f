import React, { FC, useEffect, useState } from 'react'
import styles from './create-question-dialog.module.scss'
import classNamesBind from 'classnames/bind'
import { Card, Input } from '@/shared/ui'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useDebounce } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'
import { IAnswer } from '@/shared/types/store/modules'
import { v4 as uuid } from 'uuid'
import { Checkbox } from '@/shared/ui/checkbox'

const cx = classNamesBind.bind(styles)

type AnswersChangeType = 'add' | 'delete' | 'change'

export const Answers: React.FC<{
  handleChange: React.Dispatch<React.SetStateAction<IAnswer[]>>
  setIsDisabled: React.Dispatch<React.SetStateAction<boolean>>
  answers: IAnswer[]
  multi?: boolean
}> = ({ answers, handleChange, setIsDisabled, multi = false }) => {
  const handleChangeAnswers = (
    type: AnswersChangeType = 'add',
    indexNewAnswer?: number,
    newAnswer?: IAnswer,
  ) => {
    if (!answers) return

    if (type === 'add') {
      handleChange([...answers, { id: uuid(), text: '', tip: '', is_correct: false }])
    }

    if (type === 'delete' && typeof indexNewAnswer === 'number') {
      handleChange(answers?.filter((__, i) => i !== indexNewAnswer))
    }

    if (type === 'change' && typeof indexNewAnswer === 'number' && newAnswer) {
      const isOnlyOneAnswer = !multi && newAnswer.is_correct

      const resultAnswers = isOnlyOneAnswer
        ? answers.map((a, i) => (i === indexNewAnswer ? newAnswer : { ...a, is_correct: false }))
        : answers?.map((a, i) => (i === indexNewAnswer ? newAnswer : a))

      handleChange(resultAnswers)
    }
  }

  useEffect(() => {
    const isCorrectMoreThenOne = answers.filter(a => a.is_correct).length > 1

    if (!multi && isCorrectMoreThenOne) {
      handleChange(
        answers.map(a => ({
          ...a,
          is_correct: false,
        })),
      )
    }
  }, [multi])

  const { t } = useTranslation()

  return (
    <div className={cx('answers')}>
      <div className={cx('header')}>
        {answers &&
          answers.map((f, index) => (
            <Answer
              key={`answer-${f.id ? f.id : index}`}
              index={index}
              answer={f}
              setIsDisabled={setIsDisabled}
              handleChangeAnswers={handleChangeAnswers}
            />
          ))}
        <div className={cx('addAnswer')} onClick={() => handleChangeAnswers('add')}>
          {t('dialogs.answers.add_answer')}
        </div>
      </div>
    </div>
  )
}

const Answer: FC<{
  index: number
  answer: IAnswer
  handleChangeAnswers: (type?: AnswersChangeType, index?: number, newAnswer?: IAnswer) => void
  setIsDisabled: React.Dispatch<React.SetStateAction<boolean>>
}> = ({ index, handleChangeAnswers, answer, setIsDisabled }) => {
  const [text, setText] = useState(answer.text)
  const [errorText, setErrorText] = useState<string>('')
  const [tip, setTip] = useState(answer.tip)
  const [errorTip, setErrorTip] = useState<string>('')
  const [isCorrect, setIsCorrect] = useState(answer.is_correct)

  const lazyText = useDebounce(text, 300)
  const lazyTip = useDebounce(tip, 300)

  useEffect(() => {
    if (!errorText && !errorTip) {
      setIsDisabled(false)
      handleChangeAnswers('change', index, {
        ...answer,
        text,
        tip,
        is_correct: isCorrect,
      })
    } else {
      setIsDisabled(true)
    }
  }, [lazyText, lazyTip, isCorrect])

  useEffect(() => {
    setIsCorrect(answer.is_correct)
  }, [answer.is_correct])

  const handleChangeText = (value: string) => {
    setText(value)
    setErrorText(
      value.length > 500
        ? t('commons:input_length_limit', {
            limit: 500,
            count: value.length,
          })
        : '',
    )
  }

  const handleChangeTip = (value: string) => {
    setTip(value)
    setErrorTip(
      value.length > 500
        ? t('commons:input_length_limit', {
            limit: 500,
            count: value.length,
          })
        : '',
    )
  }

  const handleChangeIsCorrect = (value: boolean) => {
    setIsCorrect(value)
  }

  const { t } = useTranslation()

  return (
    <div className={cx('answerItem')}>
      <Card className={cx('answer__card')} padding='small'>
        <div className={cx('answerTitle')}>
          {t('dialogs.answers.answer')} {index + 1}
          <IconWrapper hoverable onClick={() => handleChangeAnswers('delete', index)}>
            <CloseBoldIcon />
          </IconWrapper>
        </div>
        <Input
          fullWidth
          className={cx('answerField', 'answerText')}
          placeholder={t('dialogs.answers.text')}
          name={`${'answers'}[${index}].text`}
          value={text}
          onChange={handleChangeText}
        />
        {errorText && <div className={cx('errorTip')}>{errorText}</div>}
        <Input
          fullWidth
          className={cx('answerField', 'answerTip')}
          placeholder={t('dialogs.answers.tip')}
          name={`${'answers'}[${index}].tip`}
          value={tip}
          onChange={handleChangeTip}
        />
        {errorTip && <div className={cx('errorTip')}>{errorTip}</div>}
        <label className={cx('field', 'answer__checkbox')}>
          <Checkbox
            onChange={handleChangeIsCorrect}
            customChecked={isCorrect}
            variant='center'
            label={<span>{t('commons:correct_answer')}</span>}
          />
        </label>
      </Card>
    </div>
  )
}
