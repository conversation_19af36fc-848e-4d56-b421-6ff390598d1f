import classNamesBind from 'classnames/bind'
import React, { useState } from 'react'
import { Button, Textarea } from '@/shared/ui'
import { Answers } from './answers'
import { CreateQuestionDialogProps } from './create-question-dialog.d'
import styles from './create-question-dialog.module.scss'
import { useCreateQuestionMutation } from '@/store/services/module-service'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { Checkbox } from '@/shared/ui/checkbox'
import { checkAnswers, hasCorrectAnswer } from './helpers'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'

const cx = classNamesBind.bind(styles)

const CreateQuestionDialog: React.FC<CreateQuestionDialogProps.Props> = props => {
  const { onClose, open, moduleId: module_id, refetch, refetch2 } = props

  const [createQuestion, { isLoading }] = useCreateQuestionMutation()
  const [isDisabled, setIsDisabled] = useState<boolean>(false)

  const { t } = useTranslation('modals__create-question-dialog')

  const {
    control,
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    setError,
    clearErrors,
  } = useForm<CreateQuestionDialogProps.Inputs>({
    mode: 'onBlur',
    defaultValues: {
      text: '',
      isMulti: false,
      answers: [],
    },
  })

  const onSubmit: SubmitHandler<CreateQuestionDialogProps.Inputs> = async data => {
    const { text, isMulti, answers } = data

    const isAnswersCorrect = checkAnswers(answers)

    if (!isAnswersCorrect) {
      setError('answers', {
        type: 'custom',
        message: t('commons:question_empty_answer_error'),
      })
      return
    }

    if (!hasCorrectAnswer(answers)) {
      setError('answers', {
        type: 'custom',
        message: t('commons:question_no_correct_answer_error'),
      })
      return
    }

    clearErrors()

    createQuestion({
      moduleId: module_id,
      body: {
        multiple_answers: isMulti,
        text: text,
        answers: answers,
      },
    })
      .unwrap()
      .then(() => {
        refetch && refetch()
        refetch2 && refetch2()
        onClose()
      })
  }

  const handleTrim = (
    str: string,
    name: keyof CreateQuestionDialogProps.Inputs,
    isRequired = false,
  ) => {
    if (!str.trim().length && isRequired) {
      setError(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  const isMulti = watch('isMulti')

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <form onSubmit={handleSubmit(onSubmit)} className={cx('form')}>
        <h3 className={cx('title')}>{t('title')}</h3>
        <div className={cx('content')}>
          <Textarea
            required
            view='bordered'
            name='text'
            className={cx('description')}
            placeholder={t('text')}
            error={errors.text?.message}
            register={register('text', {
              required: t('commons:required_field'),
              onBlur: e => handleTrim(e.target.value, 'text', true),
            })}
          />
          <Controller
            control={control}
            name='isMulti'
            render={({ field }) => (
              <label className={cx('field')}>
                <Checkbox
                  customChecked={field.value}
                  onChange={field.onChange}
                  variant='center'
                  label={<span>{t('multiple')}</span>}
                />
              </label>
            )}
          />

          <div className={cx('answersTitle')}>{t('commons:answers')}</div>

          <Controller
            control={control}
            name='answers'
            rules={{ validate: value => value.length > 0 }}
            render={({ field }) => (
              <Answers
                answers={field.value}
                handleChange={field.onChange}
                multi={isMulti}
                setIsDisabled={setIsDisabled}
              />
            )}
          />
        </div>

        {errors.answers?.message && (
          <div className={cx('error-text')}>{errors.answers?.message}</div>
        )}

        <div className={cx('footer')}>
          <Button
            size='big'
            className={cx('button', 'dialogFooter')}
            type='submit'
            loading={isLoading}
            disabled={!isValid || isLoading || isDisabled}
          >
            {t('commons:create')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export { CreateQuestionDialog }
