import classNamesBind from 'classnames/bind'
import React, { Dispatch, SetStateAction, useCallback, useEffect, useState } from 'react'
import { Card } from '@/shared/ui'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { StoreModuleChooseDialog } from '@/shared/modals/store-module-choose-dialog'
import styles from './create-course-dialog.module.scss'
import { useTranslation } from 'react-i18next'
import { useGetModulesQuery } from '@/store/services/module-service'

const cx = classNamesBind.bind(styles)

export interface IModule {
  id: UUID
  old_id: number
  title: string
  description: string
}

export const ModulesSelector: React.FC<{
  name: string
  setDisabled: Dispatch<SetStateAction<boolean>>
  required?: boolean
  disabled?: boolean
  value?: UUID[]
  onChange: (v: UUID[]) => void
  error?: string
}> = ({ disabled, onChange, value = [], error }) => {
  const [moduleChooseOpen, setModuleChooseOpen] = useState(false)
  const [modules, setModules] = useState<UUID[]>(value)

  const { data: fetchedModules } = useGetModulesQuery()

  const onModuleChooseClose = useCallback(
    (setSelected: Dispatch<SetStateAction<UUID[]>>) => {
      setModuleChooseOpen(false)
      if (modules) setSelected(modules)
    },
    [modules],
  )

  useEffect(() => {
    setModules(value)
  }, [value])

  const onModuleDelete = (deleteId: UUID) => {
    const filteredModules = modules.filter(v => v !== deleteId)
    setModules(filteredModules)
    onChange?.(filteredModules)
  }

  const onSetModules = (modules: UUID[]) => {
    const m = [...modules]
    setModules(m)
    onChange?.(m)
    setModuleChooseOpen(false)
  }

  const { t } = useTranslation()

  const filteredModules = fetchedModules?.filter(m => modules?.includes(m.id))

  if (disabled && (!filteredModules || filteredModules?.length === 0))
    return <span className={cx('empty')}>{t('commons:modules_not_exist')}</span>

  return (
    <>
      <div className={cx('modulesWrapper')}>
        {filteredModules?.map(m => (
          <Card padding='small' className={cx('module')} key={m.id}>
            {m.title}
            {!disabled && (
              <IconWrapper
                className={cx('delete')}
                onClick={() => onModuleDelete(m.id)}
                disabled={disabled}
                //  || m.id)}
              >
                <CloseBoldIcon />
              </IconWrapper>
            )}
          </Card>
        ))}
        {/* TODO hide button if there are no more modules */}
        {!disabled && (
          <div className={cx('addModule')} onClick={setModuleChooseOpen.bind(null, true)}>
            {t('dialogs.modules_selector.add_modules')}
          </div>
        )}
      </div>
      {error && <div className={cx('error-text', 'errorText')}>{error}</div>}

      {moduleChooseOpen && (
        <StoreModuleChooseDialog
          open={moduleChooseOpen}
          selected={modules || []}
          onClose={onModuleChooseClose}
          setModules={onSetModules}
        />
      )}
    </>
  )
}
