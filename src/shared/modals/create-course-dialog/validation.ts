import { z } from 'zod'
type CreateCourseSchemaMessages = {
  title: string
  title_max: string
  description_max: string
  file: string
}

const descriptionMaxLength = 400
const titleMaxLength = 255

export const createCourseSchema = ({
  title,
  title_max,
  description_max,
  file,
}: CreateCourseSchemaMessages) =>
  z.object({
    title: z.string().max(titleMaxLength, title_max).min(1, title),
    description: z.string().max(descriptionMaxLength, description_max),
    picture: z.any().optional(),
    file: z.any().refine(files => files && files.length > 0, file),
  })
