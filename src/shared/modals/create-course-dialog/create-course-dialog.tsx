import classNamesBind from 'classnames/bind'
import { useCallback, useState } from 'react'
import { useForm, Controller } from 'react-hook-form'
import { Button, Input, Textarea } from '@/shared/ui'
import { CreateCourseDialogProps } from './create-course-dialog.d'
import styles from './create-course-dialog.module.scss'
import { ModulesSelector } from './modules-selector'
import { FileUploader, Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { usePostCourseMutation } from '@/store/services/course-service'

const cx = classNamesBind.bind(styles)

const descriptionMaxLength = 400
const titleMaxLength = 255

type FormInputs = {
  title: string
  description: string
  picture?: FileList
  modules: UUID[]
}

const CreateCourseDialog: React.FC<CreateCourseDialogProps.Props> = props => {
  const { onClose, open } = props

  const { t } = useTranslation('modals__create-course-dialog')

  const [loading, setLoading] = useState(false)
  const [isSubmitDisabled, setIsSubmitDisabled] = useState<boolean>(false)
  const [error, setError] = useState('')

  const [createCourse] = usePostCourseMutation()

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<FormInputs>({
    defaultValues: {
      title: '',
      description: '',
      modules: [],
    },
  })

  const handleClose = useCallback(() => {
    setError('')
    onClose()
    reset()
  }, [onClose, reset])

  const onFormSubmit = useCallback(
    async (data: FormInputs) => {
      if (!data?.modules?.length) return setError(t('error_modules_length'))

      if (data.title && data.title?.length > titleMaxLength) {
        return setError(
          t('error_course_name_length', {
            titleMaxLength: titleMaxLength,
            currentLength: data.title?.length,
          }),
        )
      }

      if (data.description && data.description?.length > descriptionMaxLength) {
        return setError(
          t('error_desc_length', {
            descMaxLength: descriptionMaxLength,
            currentLength: data.description.length,
          }),
        )
      }

      if (!data.modules?.length || !data.title) return

      setLoading(true)

      const formData = new FormData()

      formData.append('title', data.title)
      data.modules.forEach(module => {
        formData.append('modules_ids', '' + module)
      })

      if (data.picture && data.picture[0]) formData.append('image', data.picture[0])
      if (data.description) formData.append('description', data.description)

      await createCourse(formData)

      setLoading(false)
      handleClose()
      reset()
    },
    [handleClose, createCourse, reset],
  )

  return (
    <Modal setActive={handleClose} active={open} className={cx('dialog')}>
      <form className={cx('form')} onSubmit={handleSubmit(onFormSubmit)}>
        <h2 className={cx('title')}>{t('title')}</h2>
        <div className={cx('content')}>
          <Controller
            name='title'
            control={control}
            rules={{ required: t('name_required') }}
            render={({ field }) => (
              <Input
                {...field}
                bordered
                fullWidth
                className={cx('field')}
                placeholder={t('commons:course_name')}
                error={errors.title?.message}
              />
            )}
          />
          <Controller
            name='description'
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                view='bordered'
                rows={3}
                className={cx('field', 'description')}
                placeholder={t('commons:description')}
                error={errors.description?.message}
              />
            )}
          />
          <Controller
            name='picture'
            control={control}
            render={({ field }) => (
              <FileUploader
                {...field}
                accept='image/*'
                className={cx('field')}
                placeholder={t('commons:picture')}
                onChange={e => {
                  const files = e?.target?.files

                  field.onChange(files || undefined)
                }}
              />
            )}
          />
          <div className={cx('modulesTitle')}>{t('add_modules')}</div>
          <Controller
            name='modules'
            control={control}
            render={({ field }) => (
              <ModulesSelector {...field} setDisabled={setIsSubmitDisabled} required />
            )}
          />
        </div>
        {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
        <div className={cx('footer')}>
          <Button
            size='veryBig'
            className={cx('button', 'dialogFooter')}
            type='submit'
            loading={loading}
            disabled={isSubmitDisabled}
          >
            {t('commons:create')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export { CreateCourseDialog }
