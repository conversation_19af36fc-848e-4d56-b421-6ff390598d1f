@use '../../../shared/assets/styles/mixins/text';

.dialog {
  max-width: 800px;
  width: calc(100% - 64px);
  max-height: calc(100dvh - 64px);
  display: flex;
}

.empty {
  margin: 0 auto;
  width: auto;
  display: inline-block;
  color: var(--color-gray-70);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  overflow-y: auto;
  padding: 5px 40px;
}

.button {
  max-width: 240px;
  width: 100%;
}

.description {
  max-height: 140px;
}

.form {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  width: 100%;
}

.title {
  @include text.title(30px);
  padding: 20px 0;
  margin-bottom: 10px;

  text-align: center;
}

.errorText {
  margin-top: 12px;
  padding: 0 60px;

  text-align: center;
}

.modulesTitle {
  @include text.title(16px);

  color: var(--dark);
  text-align: center;
}

.modulesWrapper {
  border: 1px solid var(--color-primary);
  border-radius: 10px;
  margin-top: 10px;
  padding: 15px 25px;
  width: 100%;
}

.module {
  @include text.main(16px);
  align-items: center;

  border: 1px solid var(--grey);
  display: flex;
  flex-direction: row !important;
  justify-content: space-between;
  margin: 10px 0;

  .delete {
    margin-left: 10px;
  }
  + .addModule {
    margin: 25px 0 10px;
  }
}

.addModule {
  @include text.main(16px);

  color: var(--color-primary);

  cursor: pointer;
  display: flex;
  justify-content: center;
  text-decoration: underline;
  width: 100%;
}

.footer {
  display: flex;
  justify-content: center !important;
  padding: 20px !important;
}

.errorText {
  margin-top: 12px;
}

.error,
.error:-webkit-autofill {
  border: 1px solid #fff;
  box-shadow:
    0 0 0 2px #ff586b,
    0 0 0 50px #fff inset !important;
  box-shadow:
    0 0 0 2px #ff586b,
    0 0 0 50px #fff inset !important;

  transition:
    ease 0.15s,
    margin 0s;
}
