import classNamesBind from 'classnames/bind'
import { useCallback, useEffect, useMemo } from 'react'
import { useForm, Controller, SubmitHandler } from 'react-hook-form'
import { Button, Input, Textarea } from '@/shared/ui'
import { useNotification } from '@/shared/contexts/notifications'
import { useTranslation } from 'react-i18next'
import { Modal, FileUploader } from '@/shared/components'
import { CreateCourseDialogProps } from './create-course-dialog.d'
import styles from './create-course-dialog.module.scss'
import { usePostScormCourseMutation } from '@/store/services/course-service'
import { zodResolver } from '@hookform/resolvers/zod'
import { createCourseSchema } from './validation'

const cx = classNamesBind.bind(styles)

type FormInput = {
  title: string
  description: string
  picture?: FileList
  file?: FileList
}
const TRANSLATION_FILE = 'modals__create-course-scorm'

const CreateCourseDialogSCORM: React.FC<CreateCourseDialogProps.Props> = props => {
  const { onClose, open, refetch } = props

  const { fetchPPTXEvent } = useNotification()
  const [postScormCourse, { isLoading, data: scormCourseData }] = usePostScormCourseMutation()

  const { t } = useTranslation(TRANSLATION_FILE)

  const resolver = useMemo(
    () =>
      zodResolver(
        createCourseSchema({
          title: t('title'),
          description_max: t('description_max', { max: 400 }),
          file: t('file'),
          title_max: t('title_max', { max: 255 }),
        }),
      ),
    [t],
  )

  const { control, reset, handleSubmit } = useForm<FormInput>({
    defaultValues: {
      title: '',
      description: '',
    },
    resolver,
  })

  const onHandleClose = useCallback(() => {
    onClose()
    reset()
  }, [onClose, reset])

  const onFormSubmit: SubmitHandler<FormInput> = useCallback(
    data => {
      const formData = new FormData()

      formData.append('title', data.title)

      if (data.picture && data.picture[0]) formData.append('picture', data.picture[0])
      if (data.description) formData.append('description', data.description)
      if (data.file) formData.append('file', data.file[0])

      postScormCourse(formData).unwrap().then(onHandleClose)
    },
    [onHandleClose, postScormCourse],
  )

  useEffect(() => {
    if (scormCourseData?.event_id) {
      fetchPPTXEvent(scormCourseData.event_id, refetch, {
        success: t('event_success'),
        error: t('event_error'),
      })
    }
  }, [scormCourseData, t])

  return (
    <Modal setActive={onHandleClose} active={open} className={cx('dialog')}>
      <form className={cx('form')} onSubmit={handleSubmit(onFormSubmit)}>
        <h2 className={cx('title')}>{t('form_title')}</h2>

        <div className={cx('content')}>
          <Controller
            name='title'
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                error={error?.message}
                {...field}
                bordered
                fullWidth
                className={cx('field')}
                placeholder={t('form_name')}
              />
            )}
          />
          <Controller
            name='description'
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Textarea
                {...field}
                view='bordered'
                error={error?.message}
                rows={3}
                className={cx('field', 'description')}
                placeholder={t('form_description')}
              />
            )}
          />
          <Controller
            name='picture'
            control={control}
            render={({ field, fieldState: { error } }) => (
              <FileUploader
                {...field}
                error={error?.message}
                accept='image/*'
                className={cx('field')}
                placeholder={t('form_image')}
                onChange={e => {
                  const files = e?.target?.files

                  field.onChange(files || undefined)
                }}
              />
            )}
          />
          <div className={cx('field')}>
            <Controller
              name='file'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <FileUploader
                  {...field}
                  error={error?.message}
                  accept='application/zip'
                  className={cx('field')}
                  placeholder={t('form_course')}
                  onChange={e => {
                    const files = e?.target?.files

                    field.onChange(files || undefined)
                  }}
                />
              )}
            />
          </div>
        </div>
        <div className={cx('footer')}>
          <Button
            size='veryBig'
            className={cx('button', 'dialogFooter')}
            type='submit'
            loading={isLoading}
          >
            {t('create')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export { CreateCourseDialogSCORM }
