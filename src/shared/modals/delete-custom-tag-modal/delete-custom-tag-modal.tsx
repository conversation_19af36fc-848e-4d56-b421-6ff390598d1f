import React, { useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './delete-custom-tag-modal.module.scss'

import { DeleteCustomTagModalProps } from './delete-custom-tag-modal.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const DeleteCustomTagModal: React.FC<DeleteCustomTagModalProps.Props> = props => {
  const { t } = useTranslation('modals__delete-custom-tag-modal')
  const [loading, setLoading] = useState(false)

  const _onConfirm = async () => {
    setLoading(true)
    try {
      await props.onConfirm()
    } catch (error) {
      throw new Error('Failed to delete custom tag')
    } finally {
      setLoading(false)
    }
  }

  const _onClose = () => {
    props.onClose()
    setLoading(false)
  }

  return (
    <Modal
      key='delete-dialog'
      setActive={_onClose}
      active={!!props.active}
      className={cx('dialog')}
    >
      <h2 className={cx('title')}>{t('title')}</h2>

      <div className={cx('footer')}>
        <Button
          size='veryBig'
          onClick={_onClose}
          className={cx('button', 'dialogFooter')}
          type='button'
          color='green'
        >
          {t('commons:cancel')}
        </Button>
        <Button
          size='veryBig'
          className={cx('button', 'dialogFooter')}
          type='button'
          loading={loading}
          onClick={_onConfirm}
          color='red'
        >
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}

export default DeleteCustomTagModal
