import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './theme-steps-warning.module.scss'

import { ThemeStepsWarningModalProps } from './theme-steps-warning.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__theme-steps-warning-modal'

const ThemeStepsWarningModal: React.FC<ThemeStepsWarningModalProps.Props> = props => {
  const { open, setOpen, onConfirm } = props
  const { t } = useTranslation(TRANSLATION_FILE)

  return (
    <Modal setActive={v => setOpen(Boolean(v))} active={open} className={cx('dialog')}>
      <h2 className={cx('title')}>{t('title')}</h2>
      <p className={cx('description')}>{t('description')}</p>

      <div className={cx('footer')}>
        <Button
          size='veryBig'
          onClick={() => {
            setOpen(false)
          }}
          className={cx('button', 'dialogFooter')}
          type='button'
          color='gray'
        >
          {t('commons:cancel')}
        </Button>
        <Button
          size='veryBig'
          className={cx('button', 'dialogFooter')}
          type='button'
          onClick={() => {
            setOpen(false)
            onConfirm?.()
          }}
          color='red'
        >
          {t('submit')}
        </Button>
      </div>
    </Modal>
  )
}

export default ThemeStepsWarningModal
