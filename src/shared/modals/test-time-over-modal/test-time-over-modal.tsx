import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './test-time-over-modal.module.scss'

import { TestTimeOverModalProps } from './test-time-over-modal.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__test-time-over'

export const TestTimeOverModal: React.FC<TestTimeOverModalProps.Props> = props => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const { attempts, active, setActive, onClose, onSubmit } = props

  return (
    <Modal className={cx('wrapper')} active={active} setActive={setActive}>
      <div className={cx('title')}>{t('title')}</div>
      <div className={cx('subtitle')}>{t('hint')}</div>
      {attempts !== undefined && (
        <div className={cx('attempts')}>
          {t('attempts', {
            count: attempts,
          })}
        </div>
      )}
      <div className={cx('buttonWrapper')}>
        <Button color='green' size='big' fullWidth onClick={onSubmit}>
          {t('new_attempt')}
        </Button>
        <Button size='big' color='darkGray' fullWidth onClick={onClose}>
          {t('finish')}
        </Button>
      </div>
    </Modal>
  )
}
