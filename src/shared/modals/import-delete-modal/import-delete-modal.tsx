import { FC } from 'react'
import styles from './import-delete-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ImportDeleteModalProps } from './import-delete-modal.d'
import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)
const TRANLSATION_FILE = 'modals__import-delete'

export const ImportDeleteModal: FC<ImportDeleteModalProps.Props> = props => {
  const { className, onClose, active, setActive, title, employees, departments } = props
  const { t } = useTranslation(TRANLSATION_FILE)

  const handleClose = (close: boolean = true) => {
    onClose && onClose(close)
    setActive(false)
  }

  const employeesCount = employees
    ? t('employees_interval', { postProcess: 'interval', count: employees })
    : ''

  const departmentsCount = departments
    ? t('departments_interval', { postProcess: 'interval', count: departments })
    : ''

  return (
    <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
      <div className={cx('title')}>{title || t('title') || t('commons:confirmation')}</div>
      <div className={cx('subtitle')}>
        {t('subtitle', {
          employeesCount,
          departmentsCount,
        })}
      </div>
      <div className={cx('buttonWrapper')}>
        <Button color='gray' size='big' fullWidth onClick={() => handleClose()}>
          {t('cancel_button')}
        </Button>
        <Button size='big' color='red' fullWidth onClick={() => handleClose(false)}>
          {t('confirm_button')}
        </Button>
      </div>
    </Modal>
  )
}
