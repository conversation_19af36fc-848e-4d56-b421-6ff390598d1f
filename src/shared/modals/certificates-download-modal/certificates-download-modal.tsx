import { Loader } from '@/shared/ui'
import { Modal } from '@/shared/components'
import styles from './certificates-download-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ReactNode } from 'react'

const cx = classNamesBind.bind(styles)

type Props = {
  data: undefined | boolean
  open: boolean
  setOpen: (v: boolean) => void
  title: string
  error: boolean
  isLoading: boolean
  loaderTitle: ReactNode
}

export const CertificatesDownloadModal = ({
  open,
  setOpen,
  title,
  error,
  isLoading,
  data,
  loaderTitle,
}: Props) => {
  const isSuccess = !isLoading && !error && data !== undefined

  return (
    <Modal active={open} setActive={v => setOpen(!!v)} className={cx('wrapper')}>
      <div className={cx('title')}>{title}</div>
      <div className={cx('loaderWrapper')}>
        <Loader size='56' error={!!error} success={isSuccess} loading={isLoading} />
        <div
          className={cx('loaderTitle', {
            success: isSuccess,
            error: error,
          })}
        >
          {loaderTitle}
        </div>
      </div>
    </Modal>
  )
}
