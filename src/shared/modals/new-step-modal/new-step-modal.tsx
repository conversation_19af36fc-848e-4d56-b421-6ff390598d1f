import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './new-step-modal.module.scss'

import { NewStepProps } from './new-step-modal.d'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import Article from '@/shared/ui/Icon/icons/components/Article'
import Slide from '@/shared/ui/Icon/icons/components/Slide'
import Quiz from '@/shared/ui/Icon/icons/components/Quiz'
import Video from '@/shared/ui/Icon/icons/components/Video'
import Import from '@/shared/ui/Icon/icons/components/Import'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

const cx = classNamesBind.bind(styles)

const types: ('article' | 'slide' | 'quiz' | 'video' | 'import')[] = [
  'article',
  'slide',
  'quiz',
  'video',
  'import',
]

const renderIcon = (iconType: string) => {
  switch (iconType) {
    case 'article':
      return <Article />
    case 'slide':
      return <Slide />
    case 'quiz':
      return <Quiz />
    case 'video':
      return <Video />
    case 'import':
      return <Import />
    default:
      return null
  }
}

const NewStepModal: React.FC<NewStepProps.Props> = props => {
  const { t } = useTranslation('pages__create-theme')
  const { open, setOpen, onChoose } = props

  return (
    <Modal setActive={v => setOpen(Boolean(v))} active={open} className={cx('dialog')}>
      <h2 className={cx('title')}>{t('step_type')}</h2>
      <div className={cx('list')}>
        {types.map(item => (
          <div className={cx('type')} onClick={() => onChoose(item)} key={item}>
            <IconWrapper size='40' className={cx('icon')} color='white'>
              {renderIcon(item)}
            </IconWrapper>
            <div className={cx('info')}>
              <div className={cx('name')}>{t(item)}</div>
              <div className={cx('hint')}>{t(`${item}_hint`)}</div>
            </div>
          </div>
        ))}
      </div>
    </Modal>
  )
}

export default NewStepModal
