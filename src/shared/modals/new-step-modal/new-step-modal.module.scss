@use '../../../shared/assets/styles/mixins/text';

.dialog {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 450px;
  padding: 18px;
  width: 100%;
}

.title {
  font: var(--font-title-4-normal);
}

.type {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;

  &:hover {
    .icon {
      background-color: var(--color-primary);
    }
  }
}

.icon {
  background-color: var(--color-gray-60);
  border-radius: 8px;
}

.list {
  display: grid;
  gap: 16px;
}

.name {
  font: var(--font-text-1-normal);
}

.hint {
  font-size: 14px;
  line-height: 18px;
  color: var(--color-gray-80, #5c6585);
}

.info {
  display: grid;
  justify-content: space-between;
}