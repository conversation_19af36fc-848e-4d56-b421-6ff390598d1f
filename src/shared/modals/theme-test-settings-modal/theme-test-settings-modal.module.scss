.wrapper {
  display: grid;
  grid-gap: 32px;
  max-height: fit-content;
  max-width: 450px;
  width: 100%;

  input[type="number"] {
    appearance: textfield;
    text-align: right;
  }
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-4-normal);
}

.block {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  &.disabled {
    color: var(--color-gray-70);

    .hint {
      color: var(--color-gray-70)
    }
  }
}

.input {
  width: 56px;
  height: 40px;
  border-radius: 8px;
  border-width: 2px;

  &::-webkit-outer-spin-button,
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  &:disabled {
    border-color: var(--color-gray-60);
    background-color: var(--color-gray-40);

    &:hover {
      border-width: 2px;
    }
  }
}

.text {
  font: var(--font-text-1-normal);
  margin-bottom: 4px;
}

.hint {
  font: var(--font-text-2-normal);
  color: var(--color-gray-80)
}

.button {
  margin-left: auto;
  margin-top: 32px;
}

.error {
  color: var(--color-error);
}