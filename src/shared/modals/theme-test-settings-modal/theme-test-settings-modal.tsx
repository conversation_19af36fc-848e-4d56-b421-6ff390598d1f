/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react'
import styles from './theme-test-settings-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ThemeTestSettingsModalProps } from './theme-test-settings-modal.d'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { Button, Input, SwitchNew } from '@/shared/ui'
import { TestSettings } from '@/entities/test'
import { zodResolver } from '@hookform/resolvers/zod'
import { TestSettingsSchema } from '@/features/manage-questions/model/validation'

const cx = classNamesBind.bind(styles)

export const ThemeTestSettingsModal: FC<ThemeTestSettingsModalProps.Props> = props => {
  const { className, active, setActive, initialState, saveSettings, questions } = props
  const { t } = useTranslation('modals__theme-test-settings-modal')

  const {
    control,
    formState: { errors, isValid, isDirty },
    register,
    handleSubmit,
    watch,
  } = useForm<TestSettings>({
    defaultValues: initialState
      ? {
          explanations: initialState.need_explain,
          right_answers: initialState.correct_answers,
          questions_amount: initialState.questions_count,
          duration: initialState.testing_time_duration,
          allow_continue: !initialState.learning_block,
          limit_attempts: !!initialState.attempts_limit,
          attempts_amount: initialState.attempts_limit,
        }
      : {
          explanations: false,
          allow_continue: false,
          limit_attempts: false,
        },
    resolver: zodResolver(TestSettingsSchema),
  })

  const onSubmit: SubmitHandler<TestSettings> = async data => {
    saveSettings(data)
  }

  const isLimit = watch('limit_attempts')
  const questionsAmount = watch('questions_amount')

  const durationError = !watch('duration') || watch('duration') < 1
  const rightAnswersError = !watch('right_answers') || watch('right_answers') < 1
  const questionsAmountError = !watch('questions_amount') || watch('questions_amount') < 1

  return (
    <>
      <Modal active={active} setActive={setActive} className={cx('wrapper', className)}>
        <div className={cx('title')}>{t('title')}</div>
        <form onSubmit={handleSubmit(onSubmit)}>
          <Controller
            name={`explanations`}
            control={control}
            render={({ field, fieldState }) => {
              return (
                <div className={cx('block')}>
                  <div className={cx('text_wrapper')}>
                    <div className={cx('text')}>{t('explanations')}</div>
                    <div className={cx('hint')}>{t('explanations_hint')}</div>
                  </div>
                  <SwitchNew onChange={v => field.onChange(v)} value={!!field.value} />
                  {fieldState.error?.message}
                </div>
              )
            }}
          />

          <div className={cx('block')}>
            <div className={cx('text_wrapper')}>
              <div className={cx('text')}>{t('questions_amount')}</div>
              <div className={cx('hint')}>{t('questions_amount_hint')}</div>
              {questionsAmountError && (
                <div className={cx('hint', 'error')}>{t('question_ammount_error')}</div>
              )}
            </div>
            <Input
              bordered
              register={register('questions_amount', {
                valueAsNumber: true,
              })}
              type='number'
              error={errors.questions_amount?.message || questionsAmountError}
              className={cx('input')}
              min={1}
              max={questions?.length}
              readOnly={false}
            />
          </div>
          <div className={cx('block')}>
            <div className={cx('text_wrapper')}>
              <div className={cx('text')}>{t('right_answers')}</div>
              <div className={cx('hint')}>{t('right_answers_hint')}</div>
              {rightAnswersError && (
                <div className={cx('hint', 'error')}>{t('question_ammount_error')}</div>
              )}
            </div>
            <Input
              bordered
              register={register('right_answers', {
                valueAsNumber: true,
              })}
              type='number'
              error={errors.right_answers?.message || rightAnswersError}
              className={cx('input')}
              min={1}
              max={questionsAmount}
              readOnly={false}
            />
          </div>
          <div className={cx('block')}>
            <div className={cx('text_wrapper')}>
              <div className={cx('text')}>{t('duration')}</div>
              <div className={cx('hint')}>{t('duration_hint')}</div>
              {durationError && <div className={cx('hint', 'error')}>{t('duration_error')}</div>}
            </div>
            <Input
              bordered
              register={register('duration', {
                valueAsNumber: true,
              })}
              type='number'
              error={errors.duration?.message || durationError}
              className={cx('input')}
              min={1}
              readOnly={false}
            />
          </div>
          <Controller
            name={`allow_continue`}
            control={control}
            render={({ field, fieldState }) => {
              return (
                <div className={cx('block')}>
                  <div className={cx('text_wrapper')}>
                    <div className={cx('text')}>{t('continue_learning')}</div>
                    <div className={cx('hint')}>{t('continue_learning_hint')}</div>
                  </div>
                  <SwitchNew onChange={v => field.onChange(v)} value={!!field.value} />
                  {fieldState.error?.message}
                </div>
              )
            }}
          />
          <Controller
            name={`limit_attempts`}
            control={control}
            render={({ field, fieldState }) => {
              return (
                <div className={cx('block')}>
                  <div className={cx('text_wrapper')}>
                    <div className={cx('text')}>{t('attempts_limit')}</div>
                    <div className={cx('hint')}>{t('attempts_limit_hint')}</div>
                  </div>
                  <SwitchNew onChange={v => field.onChange(v)} value={!!field.value} />
                  {fieldState.error?.message}
                </div>
              )
            }}
          />
          <div
            className={cx('block', {
              disabled: !isLimit,
            })}
          >
            <div className={cx('text_wrapper')}>
              <div className={cx('text')}>{t('attempts_amount')}</div>
              <div className={cx('hint')}>{t('attempts_amount_hint')}</div>
            </div>
            <Input
              bordered
              register={register('attempts_amount', {
                valueAsNumber: true,
              })}
              disabled={!isLimit}
              type='number'
              error={errors.attempts_amount?.message}
              className={cx('input')}
              min={0}
              readOnly={false}
            />
          </div>
          <Button
            type='submit'
            color='green'
            size='big'
            className={cx('button')}
            disabled={!isValid || !isDirty}
          >
            {t('commons:save')}
          </Button>
        </form>
      </Modal>
    </>
  )
}
