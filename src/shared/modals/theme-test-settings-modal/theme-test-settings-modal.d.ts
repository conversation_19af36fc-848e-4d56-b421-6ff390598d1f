import { TestSettings } from '@/entities/test'
import { TQuestion, TSettings } from '@/entities/themeCourse/model/types';
import { ModalProps } from 'react-app/new/components'

export declare namespace ThemeTestSettingsModalProps {
  interface Own extends Omit<ModalProps.Own, 'children'> {
    className?: string
    initialState?: TSettings
    saveSettings: (data: TestSettings) => void;
    questions: TQuestion[];
  }

  type Props = Own
}

export {}
