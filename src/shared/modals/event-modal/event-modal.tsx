import { FC } from 'react'
import styles from './event-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { EventModalProps } from './event-modal.d'
import { Button, Loader } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const TRANLSATION_FILE = 'modals__event-modal'

export const EventModal: FC<EventModalProps.Props> = props => {
  const { open, setOpen } = props
  const { t } = useTranslation(TRANLSATION_FILE)

  return (
    <Modal active={open} setActive={setOpen} className={cx('wrapper')}>
      <div className={cx('title')}>{t('title')}</div>
      <div className={cx('loaderWrapper')}>
        <Loader size='56' success loading={false} />
        <div className={cx('loaderTitle', 'success')}>
          {t('loader_message_first')} <br /> {t('loader_message_second')}
        </div>
      </div>
      <div className={cx('buttonWrapper', 'full')}>
        <Button color='gray' size='big' fullWidth onClick={() => setOpen(false)}>
          {t('close_button')}
        </Button>
      </div>
    </Modal>
  )
}
