import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './course-progress-modal.module.scss'

import { CourseProgressProps } from './course-progress-modal.d'
import { Logo, Modal } from '@/shared/components'

const cx = classNamesBind.bind(styles)

const CourseProgressModal: React.FC<CourseProgressProps.Props> = props => {
  const { open, setOpen, content } = props

  return (
    <Modal setActive={v => setOpen(Boolean(v))} active={open} className={cx('dialog')}>
      <div className={cx('logo-wrapper')}>
        <Logo />
      </div>
      {content}
    </Modal>
  )
}

export default CourseProgressModal
