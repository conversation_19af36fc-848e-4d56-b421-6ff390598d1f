import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './replace-modal.module.scss'

import { ReplaceModalProps } from './replace-modal.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const ReplaceModal: React.FC<ReplaceModalProps.Props> = props => {
  const { t } = useTranslation()
  const { open, setOpen, onConfirm, title, submitText } = props

  return (
    <Modal setActive={v => setOpen(Boolean(v))} active={open} className={cx('dialog')}>
      <h2 className={cx('title')}>{title}</h2>

      <div className={cx('footer')}>
        <Button
          size='veryBig'
          onClick={() => {
            setOpen(false)
          }}
          className={cx('button', 'dialogFooter')}
          type='button'
          color='gray'
        >
          {t('commons:cancel')}
        </Button>
        <Button
          size='veryBig'
          className={cx('button', 'dialogFooter')}
          type='button'
          onClick={() => {
            setOpen(false)
            onConfirm?.()
          }}
          color='green'
        >
          {submitText ?? t('commons:reload')}
        </Button>
      </div>
    </Modal>
  )
}

export default ReplaceModal
