/* eslint-disable @typescript-eslint/no-explicit-any */
import classNamesBind from 'classnames/bind'
import React, { useEffect, useState } from 'react'
import { OldText } from '@/shared/components'
import { Button } from '@/shared/ui/button'
import { List } from '@/shared/components/list'
import { IChartData, ILegend, ILine } from '@/shared/types/charts'
import { NotifyEmployeesDialog } from '../notify-employees-modal'
import styles from './employees-in-group-modal.module.scss'
import { Modal } from '@/shared/components/modal'
import { useTranslation } from 'react-i18next'
import { IGroupsEmployee, IGroupsNew, TGroup } from '@/shared/types/store/groups'
import { IEmployeesGroupRequest, organizationGatewayAPI } from 'entities/organization'

const cx = classNamesBind.bind(styles)

export interface IEmployeesInGroupDialogProps {
  id?: UUID
  organization_id?: UUID
  needRequest?: boolean
  type: 'phishing' | 'organization' | 'course'
  groups: Array<{
    title: string
    color: string
    group: string
    employees?: IGroupsEmployee[]
  }>
  activeGroup: string | null
  onGroupChange(group: string | null): void
  title?: string
}

const TRANLSATION_FILE = 'modals__employees-in-group'

export const EmployeesInGroupDialogNewRequest: React.FC<IEmployeesInGroupDialogProps> = props => {
  const {
    activeGroup,
    onGroupChange,
    groups,
    type,
    id,
    needRequest = false,
    organization_id,
  } = props

  const group = activeGroup?.toUpperCase() as TGroup

  const { t } = useTranslation(TRANLSATION_FILE)

  const title = props.title || t('title')

  const [opened, setOpened] = useState(false)

  const onClose = () => onGroupChange(null)

  const limit = 50

  const [isFetched, setIsFetched] = useState(false)
  const [groups1, setGroups] = useState<IGroupsNew>({
    NORMAL: {
      offset: 0,
      total_count: 0,
      data: [],
      title: t('commons:normal_plan'),
      color: 'green',
    },
    BEHIND: {
      offset: 0,
      total_count: 0,
      data: [],
      title: t('commons:behind_plan'),
      color: 'red',
    },
  })

  const [getEmployeesGroup, { isLoading }] = organizationGatewayAPI.useLazyGetEmployeesGroupQuery()

  const fetchData = async () => {
    const body: IEmployeesGroupRequest = {
      group_name: group?.toLocaleLowerCase() as TGroup,
      limit,
      offset: groups1[group].offset,
    }

    if (organization_id) {
      body.organization_id = organization_id
    }

    const { data, total_count = 0 } = await getEmployeesGroup(body).unwrap()

    if (data.length) {
      setGroups({
        ...groups1,
        [group]: {
          ...groups1[group],
          total_count,
          data: [...groups1[group].data, ...data],
          offset: (groups1[group].offset || 0) + limit,
        },
      })
    }

    setIsFetched(() => true)
  }

  useEffect(() => {
    if (!needRequest || isLoading || isFetched) return

    fetchData()
  }, [needRequest, isLoading, isFetched])

  useEffect(() => {
    if (isLoading || !needRequest) return

    fetchData()
  }, [activeGroup])

  const canLoadMore =
    needRequest && groups1 && groups1[group]?.total_count > groups1[group]?.data?.length

  const employees = groups1[group]?.data

  const renderEmployee = (profile: undefined | any, index: number) => {
    return (
      <div key={profile ? 'id_' + profile.id : 'i_' + index} className={cx('user')}>
        <div className={cx('userAvatar')}>
          {profile?.picture && <img className={cx('image')} src={profile.picture} alt={''} />}
        </div>
        <div className={cx('userInfo')}>
          {profile?.full_name?.trim() || profile?.email} <br />
          {[profile?.department_title, profile?.position].filter(p => !!p).join(' / ')}
        </div>
      </div>
    )
  }

  const legends = Object.keys(groups1).map(k => {
    const key = k as TGroup

    return {
      title: groups1[key].title,
      color: groups1[key].color,
      group: key.toLocaleLowerCase(),
    }
  })

  return (
    <>
      <Modal setActive={onClose} active={!!activeGroup} className={cx('dialog')}>
        <h2 className={cx('title')}>{title}</h2>
        <div className={cx('header')}>
          <div className={cx('to')}>{t('group')}</div>
          <OldLegend
            className={cx('groups')}
            labels={legends.map(g => ({ ...g, label: g.title })) || []}
            active={activeGroup || undefined}
            onClick={onGroupChange}
            horizontal
            collapsible
          />
        </div>
        <div id='scrollable-employees-modal' className={cx('content')}>
          <List
            className={cx('employees', 'items')}
            data={employees}
            renderItem={renderEmployee}
            loadMore={fetchData}
            canLoadMore={canLoadMore}
            scrollableTarget='scrollable-employees-modal'
            paginated
          />
        </div>
        <div className={cx('footer')}>
          <Button
            size='veryBig'
            className={cx('button', 'dialogFooter')}
            onClick={() => setOpened(true)}
            disabled={!employees?.length}
          >
            {t('notify')}
          </Button>
        </div>
      </Modal>
      {opened && (
        <NotifyEmployeesDialog
          activeGroup={activeGroup}
          onGroupChange={onGroupChange}
          groups={groups}
          type={type}
          onClose={() => setOpened(false)}
          id={id}
          employees={employees.map(e => e.id)}
        />
      )}
    </>
  )
}

type Item = ILegend | ILine | IChartData

interface LegendProps {
  className?: string
  labels: Item[]
  horizontal?: boolean
  onClick?(value: any): void // TODO FIX TYPE
  size?: 'l' | 'm'
  active?: string
  collapsible?: boolean
}

type LegendItemProps = {
  className?: string
  data: Item
  size?: 'm' | 'l'
  hideLabel?: boolean
  active?: boolean
  onClick?(): void
}

const LegendItem: React.FC<LegendItemProps> = props => {
  const { className, data, size = 'm', hideLabel, active, ...otherProps } = props

  return (
    <div className={cx('legendItem', className)} {...otherProps}>
      <div className={cx({ active })}>
        <div className={cx('label', 'color-' + data.color!, 'size-' + size)} />
      </div>
      {!hideLabel && (
        <OldText variant={size === 'l' ? 'h3' : 'h4'} placeholder={{ width: 100 }}>
          {data.label}
        </OldText>
      )}
    </div>
  )
}

const OldLegend: React.FC<LegendProps> = props => {
  const { className, labels, collapsible, onClick = () => undefined, active, size } = props

  const horizontal = props.horizontal || collapsible

  const isActive = (item: Item): boolean => {
    if (!active) return false

    return 'group' in item && active === item.group
  }

  return (
    <div className={cx('legend', className, { horizontal, collapsible })}>
      <div className={styles.wrapper}>
        {labels.map((label, i) => (
          <LegendItem
            className={styles.legendItem}
            data={label as any}
            key={i}
            size={size}
            active={collapsible && isActive(label)}
            hideLabel={collapsible && !isActive(label)}
            onClick={onClick.bind(null, label.group)}
          />
        ))}
      </div>
    </div>
  )
}
