@use '../../../shared/assets/styles/mixins/text';

.dialog {
  max-height: calc(100dvh - 64px);
  max-width: 800px;
  display: flex;
  flex-direction: column;
}

.content {
  display: flex;
  flex-direction: column;
  padding: 0 60px !important;
  max-height: calc(100dvh - 300px);
  overflow-y: auto;
}

.header {
  align-items: center;
  display: flex;
  margin-bottom: 10px;
  padding: 10px 60px 0;
  .to {
    margin-right: 20px;
  }
}

.title {
  @include text.title(30px);
  padding: 40px 0 20px !important;

  text-align: center;
}

.button {
  max-width: 240px;
  width: 100%;
}

.field + .field {
  margin-top: 20px;
  width: 100%;
}

.footer {
  display: flex;
  justify-content: center !important;
  padding: 30px 90px 40px !important;
}

.load-more-button {
  margin-top: 20px;
  max-width: 200px;
}

.user {
  align-items: center;
  display: flex;
  width: 100%;

  .userAvatar {
    flex-shrink: 0;
    height: 40px;
    position: relative;
    width: 40px;
    .image {
      background-color: var(--brown-grey);
      border: 3px solid var(--brown-grey);
      border-radius: 50%;
      box-sizing: border-box;
      height: 100%;
      object-fit: cover; // TODO not work in ie
      object-position: center;
      width: 100%;
    }
  }

  .userInfo {
    font-size: 14px;

    font-weight: normal;
    line-height: 120%;
    margin-left: 20px;
  }

  + .user {
    margin-top: 15px;
  }
}

.legend {
  display: flex;

  .wrapper {
    display: flex;
    flex: 1 0;
    flex-direction: column;
    flex-wrap: wrap;
    margin: -8px -12px;

    .legendItem {
      margin: 8px 12px;

      @media (max-width: 480px) {
        margin: 3px 7px;
      }
    }
  }

  &.horizontal .wrapper {
    flex-direction: row;
    justify-content: center;

    @media (max-width: 480px) {
      flex-direction: column;
      justify-content: flex-start;
    }
  }

  &.collapsible .wrapper {
    flex-wrap: nowrap;
  }
}

.legendItem {
  align-items: center;

  cursor: pointer;
  display: flex;
}

.active {
  align-items: center;

  background-color: var(--white);
  border-radius: 40px;
  box-shadow: 0 0 10px 0 #dcdcdc;
  display: flex;
  height: 40px;
  justify-content: center;
  margin-right: 10px;
  width: 40px;
  .label {
    margin: 0;
  }
}

.label {
  border: solid 2px;
  border-radius: 6px;
  display: inline-block;
  height: 6px;
  margin-right: 10px;
  width: 6px;

  &.size {
    &-l {
      border: solid 3px;
      border-radius: 8px;
      height: 8px;
      width: 8px;
    }

    &-m {
      border: solid 3px;
      border-radius: 100%;
      height: 12px;
      width: 12px;
    }
  }

  &.color {
    &-red {
      border-color: var(--color-statistics-bad);
    }

    &-green {
      border-color: var(--color-statistics-good);
    }

    &-blue {
      border-color: var(--color-statistics-complementary);
    }

    &-yellow {
      border-color: var(--color-statistics-warning);
    }
    &-gray {
      border-color: var(--grey);
    }
    &-light_gray {
      border-color: var(--silver);
    }
  }
}
@media (min-width: 320px) and (max-width: 768px) {
  .legendItem {
    margin: 0;
    padding-left: 10px;
  }

  .label {
    border: solid 2px;
    border-radius: 6px;
    display: inline-block;
    height: 3px;
    margin-right: 10px;
    width: 3px;
  }
}
