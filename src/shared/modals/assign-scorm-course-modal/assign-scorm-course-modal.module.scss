.wrapper {
  background: var(--surface, #fff);
  border-radius: 16px;
  box-shadow:
    0 32px 88px -4px rgba(24, 39, 75, 0.12),
    0 12px 28px -6px rgba(24, 39, 75, 0.12);

  display: grid;
  grid-template-rows: auto 1fr auto;
  max-height: 90vh;
  max-width: 982px;
  width: 100%;
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-4-normal);
  margin-bottom: 16px;
}

.inner {
  display: grid;
  grid-gap: 32px 16px;
  grid-template-columns: repeat(3, 1fr);
  height: max-content;
}

.innerWrapper {
  margin-bottom: 20px;
  overflow-y: scroll;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.buttonWrapper {
  align-items: center;
  display: flex;
  gap: 16px;
  justify-content: end;
}

.emptyText {
  font: var(--font-caption-1-normal);
}
