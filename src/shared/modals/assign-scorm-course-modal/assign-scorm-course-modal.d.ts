import { ModalProps } from 'react-app/new/components'
import { ICourse } from '../../types/store/course'

export declare namespace AssignCourseModalProps {
  interface Own extends Omit<ModalProps.Own, 'children'> {
    className?: string
    selected: UUID[]
    actionID?: UUID
    handleSelect: (id: UUID, courses: UUID[]) => void
    multi?: boolean
    courses?: ICourse[]
  }

  type Props = Own
}

export {}
