import { FC, useMemo, useState } from 'react'
import styles from './assign-scorm-course-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { AssignCourseModalProps } from './assign-scorm-course-modal.d'
import { CourseCard, Modal } from '@/shared/components'
import { useGetScormCoursesQuery } from '@/store/services/course-service'
import { Button, Loader } from '@/shared/ui'
import { useTranslation } from 'react-i18next'
import { ICourse } from '../../types/store/course'

const cx = classNamesBind.bind(styles)

export const AssignScormCourseModal: FC<AssignCourseModalProps.Props> = props => {
  const {
    className,
    active,
    setActive,
    selected = [],
    handleSelect,
    actionID,
    multi = true,
    courses: propsedCourses,
  } = props

  const { data, isLoading, error } = useGetScormCoursesQuery(true)

  const coursesByType = useMemo(() => {
    const result: Record<'archived' | 'not_acrhived', ICourse[]> = {
      archived: [],
      not_acrhived: [],
    }
    const coursesData = propsedCourses ?? data
    if (!coursesData) return result

    data?.map(v => (v?.archived ? result.archived.push(v) : result.not_acrhived.push(v)))

    return result
  }, [data, propsedCourses])

  const [courses, setCourses] = useState<UUID[]>(selected)

  const handleSelectCourse = (id: UUID) => {
    const position = courses.indexOf(id)

    if (multi) {
      if (position === -1) {
        setCourses(prev => [...prev, id])
      } else {
        setCourses(prev => [...prev.slice(0, position), ...prev.slice(position + 1)])
      }
    } else {
      if (position === -1) {
        setCourses([id])
      } else {
        setCourses([])
      }
    }
  }
  const handleClick = () => {
    handleSelect(actionID!, courses)
    setActive(() => false)
  }
  const handleClose = () => {
    if (!actionID) return

    handleSelect(actionID, selected)
    setActive(() => false)
  }

  const { t } = useTranslation('modals__assign-scorm-course-modal')

  return (
    <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
      <div className={cx('title')}>{t('title')}</div>
      <div className={cx('innerWrapper')}>
        {isLoading && <Loader size='56' loading />}
        {!isLoading && error && (
          <div className='error-text'>{t('commons:error_unexpected_with_dots')}</div>
        )}
        {!isLoading &&
          !error &&
          data &&
          !data.length &&
          coursesByType?.archived &&
          !coursesByType?.archived?.length && <div className='emptyText'>{t('no_courses')}</div>}
        {!isLoading && !error && data && !!data.length && (
          <div className={cx('inner')}>
            {data.map(c => {
              const isSelected = courses.indexOf(c.id) !== -1

              return (
                <CourseCard
                  key={c.id}
                  info={c}
                  onClick={handleSelectCourse}
                  isSelected={isSelected}
                  multi={false}
                />
              )
            })}
          </div>
        )}
        {coursesByType?.archived?.length > 0 && (
          <>
            <h3>{t('commons:archive_courses')}</h3>
            <div className={cx('inner')}>
              {coursesByType?.archived.map(c => {
                const isSelected = courses.indexOf(c.id) !== -1

                return (
                  <CourseCard
                    key={c.id}
                    info={c}
                    onClick={handleSelectCourse}
                    isSelected={isSelected}
                    multi={false}
                  />
                )
              })}
            </div>
          </>
        )}
      </div>
      <div className={cx('buttonWrapper')}>
        <Button color='gray' onClick={handleClose}>
          {t('commons:cancel')}
        </Button>
        <Button onClick={handleClick} disabled={!courses.length}>
          {t('commons:choose')}
        </Button>
      </div>
    </Modal>
  )
}
