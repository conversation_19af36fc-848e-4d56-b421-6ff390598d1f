import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './continue-incompleted-quiz.module.scss'

import { СontinueIncompletedQuizProps } from './continue-incompleted-quiz.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const СontinueIncompletedQuiz: React.FC<СontinueIncompletedQuizProps.Props> = props => {
  const { show } = props

  const _onContinue = () => props.onClose(true)
  const _onNew = () => props.onClose(false)
  const _onClose = () => props.onClose()

  const { t } = useTranslation('modals__continue-incompleted-quiz')

  return (
    <Modal key='dialog' setActive={_onClose} active={show} className={cx('dialog')}>
      <h3 className={cx('title')}>{t('title')}</h3>

      <div className={cx('footer')}>
        <Button
          size='veryBig'
          onClick={_onNew}
          className={cx('button', 'dialogFooter')}
          type='button'
          view='border'
        >
          {t('start_new')}
        </Button>
        <Button
          size='veryBig'
          className={cx('button', 'dialogFooter')}
          type='button'
          onClick={_onContinue}
        >
          {t('commons:continue')}
        </Button>
      </div>
    </Modal>
  )
}

export default СontinueIncompletedQuiz
