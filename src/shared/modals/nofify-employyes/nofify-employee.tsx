import { Modal } from '@/shared/components'
import { Button, ButtonProps, Textarea } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './nofify-employee.module.scss'
import { FC, ReactNode } from 'react'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { isNumber } from '@/shared/helpers'

const cx = classNamesBind.bind(styles)

type FormValues = {
  text: string
}

type Props = {
  title: string
  textPlaceholder?: string
  count?: number
  initialForm?: FormValues
  open: boolean
  setOpen: (v: boolean) => void
  onSubmit?: (data: FormValues) => void
  onReset?: (data: FormValues) => void
  cancel: Partial<ButtonProps.Own> & {
    text: string
  }
  submit: Partial<ButtonProps.Own> & {
    text: string
  }
  topSlot?: ReactNode
  customDisabled?: boolean
}

export const NofifyEmployyes: FC<Props> = ({
  title,
  textPlaceholder,
  initialForm,
  open,
  setOpen,
  count,
  topSlot,
  customDisabled,
  ...props
}) => {
  const { t } = useTranslation()
  const form = useForm<FormValues>({ defaultValues: initialForm })

  const onSubmit: SubmitHandler<FormValues> = data => {
    props?.onSubmit?.(data)
  }

  const onClose = () => {
    props?.onReset?.(form.watch())
    form.reset()
    setOpen(false)
  }

  return (
    <Modal setActive={v => setOpen(!!v)} active={open} className={cx('modal')}>
      <form className={cx('wrapper')} onSubmit={form.handleSubmit(onSubmit)}>
        <h4>{title}</h4>
        {topSlot}
        {isNumber(count) && (
          <p className={cx('count')}>{t('commons:employees_choosed', { count: count })}</p>
        )}
        <Controller
          control={form.control}
          name='text'
          render={({ field }) => (
            <Textarea
              resize={false}
              classNameWrapper={cx('textarea')}
              fullWidth
              onChange={field.onChange}
              value={field.value}
              placeholder={textPlaceholder}
            />
          )}
        />
        <div className={cx('actions')}>
          <Button
            onClick={() => {
              onClose()
            }}
            {...props.cancel}
            type='button'
            fullWidth
            color='gray'
          >
            {props.cancel.text}
          </Button>
          <Button
            {...props.submit}
            disabled={!!customDisabled || !form.watch('text')}
            type='submit'
            fullWidth
          >
            {props.submit.text}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
