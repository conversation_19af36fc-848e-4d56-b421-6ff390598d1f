import React, { FC, useEffect, useState } from 'react'
import styles from './assign-template-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { AssignTemplateModalProps } from './assign-template-modal.d'
import { Modal, TemplateLogo } from '@/shared/components'
import { <PERSON><PERSON>, Loader, SearchInput } from '@/shared/ui'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useDebounce } from '@/shared/hooks'
import { toggleArrayValue, groupByCategory } from '@/shared/helpers'
import { Checkbox } from '@/shared/ui/checkbox'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '@/entities/phishing'

const cx = classNamesBind.bind(styles)

export const AssignTemplateModal: FC<AssignTemplateModalProps.Props> = props => {
  const { className, active, setActive, selected = [], handleSelect } = props

  const [search, setSearch] = useState('')
  const debouncedSearch = useDebounce(search, 500)

  const [activeEmail, setActiveEmail] = useState<UUID | null>(null)
  const [selectedEmails, setSelectedEmails] = useState<UUID[]>(() => selected)

  const { data, isLoading, error, isFetching } = phishingQueries.useGetTemplatesQuery({
    search: debouncedSearch,
  })
  const {
    data: email,
    isLoading: isLoadingEmail,
    error: errorEmail,
  } = phishingQueries.useGetEmailQuery(activeEmail, {
    skip: !activeEmail,
  })

  const { t } = useTranslation('modals__assign-template-modal')

  const groupData = data && groupByCategory(data)

  const handleActiveEmail = (id: UUID) => setActiveEmail(id)
  const handleSelectEmail = (id: UUID) => {
    toggleArrayValue<UUID>(id, selectedEmails, setSelectedEmails)
  }

  const emailsCount = selectedEmails.length
  // emailsCount
  const counterTitle = t('commons:templates_choosed', { count: emailsCount })

  const handleClose = () => {
    handleSelect(selected)
    setActive(false)
  }
  const handleClick = () => {
    handleSelect(selectedEmails)
    setActive(false)
  }

  useEffect(() => {
    if (activeEmail) return
    if (!groupData || !groupData[0]?.templates[0] || !groupData[0]?.templates[0]?.emails) return

    setActiveEmail(groupData[0]?.templates[0]?.emails[0]?.id)
  }, [groupData])

  return (
    <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
      <div className={cx('title')}>{t('title')}</div>
      <div className={cx('inner')}>
        <SearchInput
          placeholder={t('search_placeholder')}
          value={search}
          onChange={setSearch}
          className={cx('searchInput')}
          isLoading={isFetching}
        />
        <div className={cx('counter')}>{counterTitle}</div>
        <div className={cx('list')}>
          {isLoading && <Loader size='56' loading />}
          {!isLoading && error && (
            <div className='error-text'>{t('commons:error_unexpected_with_dots')}</div>
          )}
          {!isLoading && !error && groupData && !groupData.length && (
            <div className='empty-text'>{t('no_templates')}</div>
          )}
          {!isLoading && !error && groupData && !!groupData.length && (
            <div className={cx('listInner')}>
              {groupData.map((group, index) => {
                return (
                  <React.Fragment key={group?.id || index}>
                    <div className={cx('listName')}>{group.category?.name}</div>
                    {group?.templates?.map((t, ind) => {
                      if (!t?.emails?.length) return null
                      return (
                        <React.Fragment key={`${group.id}-${t.id}` + ind}>
                          <div key={`${t.id}-logo`} className={cx('listCategoryName')}>
                            <TemplateLogo
                              src={t.logo}
                              // eslint-disable-next-line i18next/no-literal-string, i18n/no-russian-character
                              alt={`Логотип - ${t.name}`}
                              className={cx('listCategoryLogo')}
                              name={t.name}
                            />
                            <span>{t.name}</span>
                          </div>
                          {t.emails.map(e => {
                            const isSelected = selectedEmails.indexOf(e.id) !== -1

                            return (
                              <div
                                key={e.id}
                                className={cx('listEmail', {
                                  active: activeEmail === e.id,
                                  'listEmail_with-mail': e?.attachments?.length > 0,
                                })}
                                onClick={() => handleActiveEmail(e.id)}
                              >
                                <Checkbox
                                  onChange={() => handleSelectEmail(e.id)}
                                  customChecked={isSelected}
                                />
                                <span>{e.name}</span>
                                {e?.attachments?.length > 0 && (
                                  <IconWrapper size='20' color='gray80'>
                                    <ClipBoldIcon />
                                  </IconWrapper>
                                )}
                              </div>
                            )
                          })}
                        </React.Fragment>
                      )
                    })}
                  </React.Fragment>
                )
              })}
            </div>
          )}
        </div>
        <div className={cx('email')}>
          {isLoadingEmail && <Loader size='56' loading />}
          {!isLoadingEmail && errorEmail && (
            <div className='error-text'>{t('commons:error_unexpected_with_dots')}</div>
          )}

          {!isLoading && !error && email && (
            <>
              <div className={cx('emailTitle')}>{email.name}</div>
              <iframe allowFullScreen srcDoc={email.html} />
            </>
          )}
        </div>
      </div>

      <div className={cx('buttonWrapper')}>
        <Button color='gray' onClick={handleClose}>
          {t('commons:cancel')}
        </Button>
        <Button onClick={handleClick} disabled={!selectedEmails.length}>
          {t('commons:choose')}
        </Button>
      </div>
    </Modal>
  )
}
