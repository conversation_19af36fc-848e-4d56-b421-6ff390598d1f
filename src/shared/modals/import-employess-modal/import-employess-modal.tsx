/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { FC, useEffect, useRef, useState } from 'react'
import styles from './import-employess-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ImportEmployeesModalProps } from './import-employess-modal.d'
import { Modal } from '@/shared/components'
import { ImportDeleteModal } from '@/shared/modals/import-delete-modal'
import { Button, Loader } from '@/shared/ui'
import DownloadIcon from '@/shared/ui/Icon/icons/components/DownloadIcon'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { IEmployeesImportWithFileInfo } from 'entities/employee'
import { useConfig, useDownload } from '@/shared/hooks'
import { combineURLs } from '@/shared/helpers'
import {
  useImportEmployeesWithFileInfoMutation,
  useImportEmployeesWithFileMutation,
} from '@/store/services/tags-employees-service'
import { Checkbox } from '@/shared/ui/checkbox'
import { useTranslation } from 'react-i18next'
import { useEmployeesInfoEvent } from '@/shared/hooks/use-employees-info-event'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__import-employess'
const LOCAL_STORAGE_KEY = 'employeeEventId'

export const ImportEmployeesModal: FC<ImportEmployeesModalProps.Props> = props => {
  const { className, active, setActive, refetchDepartments } = props

  const {
    status,
    errorLoading,
    info,
    uploading,
    eventId,
    fileName,
    isEventLoading,
    errorMessage,
    sectionErrors,
    handleEventId,
    handleClear,
    handleUploading,
    setUploading,
  } = useEmployeesInfoEvent()

  const [infoLoading, setInfoLoading] = useState<boolean>(false)

  const onClose = () => {
    if (step === 2 && uploading === 'success') {
      setUploading('')
    }
    setActive(false)
  }

  const onCancel = (stayOpen: boolean = false) => {
    handleClear()
    !stayOpen && onClose()
  }

  useEffect(() => {
    if (info && eventId && status === 'completed') {
      setStep(1)
    }

    if (!info && eventId && status === 'created') {
      setStep(1)
    }

    if (eventId && (uploading === 'in_process' || uploading === 'success')) {
      setStep(2)
    }
  }, [info, eventId, status])

  useEffect(() => {
    if (errorMessage) {
      setStep(2)
    }
  }, [errorMessage])

  const { t } = useTranslation(TRANSLATION_FILE)
  const csvInput = useRef<HTMLInputElement | null>(null)

  const [csvFile, setCsvFile] = useState<File | null>(null)

  const onCsvInputClick = () => {
    csvInput.current?.click()
  }

  const handleAdd = (needOpenModal = false) => {
    if (step === 0 && csvFile) {
      setStep(() => 1)
      getInfoaAboutFile()
    }

    if (step === 1) {
      if (needOpenModal) {
        setOpenDelete(() => true)
      } else {
        handleImport()
      }
    }
  }
  const handleReset = () => {
    onCancel(true)
    setStep(() => 0)
    setCsvFile(() => null)
    setGetInfoError(() => '')
    if (csvInput?.current?.value) csvInput.current.value = ''
  }

  const config = useConfig()
  const [download] = useDownload()

  const onCsvTemplateClick = () => {
    download(
      combineURLs(
        config?.url?.startsWith(`https://`) ? config?.url || '' : `https://` + config?.url || '',
        '/lk/assets/legacy_files/users_template.xlsx',
      ),
      'users_template.xlsx',
    )
  }

  const onCsvFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files ? e.target.files[0] : undefined

    if (!file) return

    setCsvFile(file)
  }

  const [step, setStep] = useState(0)

  const [getInfo, { isLoading: isLoadingGetInfo }] = useImportEmployeesWithFileInfoMutation()
  const [importFile, { isLoading: isLoadingImportFile, error: errorImportFile }] =
    useImportEmployeesWithFileMutation()

  const [getInfoError, setGetInfoError] = useState('')
  const [getInfoInfo, setGetInfoInfo] = useState<IEmployeesImportWithFileInfo | null>(null)

  const getInfoaAboutFile = async () => {
    if (!csvFile) return
    setInfoLoading(true)
    const formData = new FormData()

    formData.append('upload_file', csvFile)

    try {
      await getInfo(formData)
        .unwrap()
        .then(response => {
          setGetInfoInfo(response)
          localStorage.setItem(LOCAL_STORAGE_KEY, response.id)
          handleEventId()
        })
        .catch(e => {
          setGetInfoError(e.message || e?.data?.message || e.error || t('commons:error_unexpected'))
        })
    } catch (e: any) {
      setGetInfoError(e.message || e?.data?.message || e.error || t('commons:error_unexpected'))
    }
    setInfoLoading(false)
  }

  const [isDelete, setIsDelete] = useState(false)
  const [isUpdate, setIsUpdate] = useState(false)
  const [isEmailing, setIsEmailing] = useState(false)

  const currentInfoType =
    !isDelete && !isUpdate
      ? 'basic'
      : isDelete && !isUpdate
        ? 'delete'
        : !isDelete && isUpdate
          ? 'update'
          : isDelete && isUpdate
            ? 'full'
            : 'basic'
  const currentInfoTypeForDepartment =
    !isDelete && !isUpdate
      ? 'basic'
      : isDelete && !isUpdate
        ? 'full'
        : !isDelete && isUpdate
          ? 'basic'
          : isDelete && isUpdate
            ? 'full'
            : 'basic'

  const currentInfo = {
    users: getInfoInfo?.metadata?.content?.info?.users?.[currentInfoType] ??
      info?.users?.[currentInfoType] ?? {
        created: 0,
        deleted: 0,
        updated: 0,
        domain_skipped: 0,
      },
    departments: getInfoInfo?.metadata?.content?.info?.departments?.[
      currentInfoTypeForDepartment
    ] ??
      info?.departments?.[currentInfoTypeForDepartment] ?? {
        created: 0,
        deleted: 0,
        updated: 0,
      },
  }

  const { users, departments } = currentInfo

  const [openDelete, setOpenDelete] = useState(false)

  const handleImport = async (close?: boolean) => {
    handleUploading()
    const id = localStorage.getItem(LOCAL_STORAGE_KEY)
    if (close || !id) return

    setStep(() => 2)

    try {
      await importFile({
        event_id: id,
        type: currentInfoType,
        need_message: isEmailing,
      }).unwrap()
      refetchDepartments && refetchDepartments()
    } catch (e: any) {
      setGetInfoError(e.message || e?.data?.message || e.error || t('commons:error_unexpected'))
    }
  }

  const mailingText =
    typeof users?.created === 'number' && users?.created > 0
      ? t('mail_interval', { postProcess: 'interval', count: Number(users?.created) ?? 0 })
      : ''

  const onModalClose = (val: boolean) => {
    setActive(val)
    if (step === 2 && uploading === 'success') {
      setUploading('')
    }
  }

  return (
    <>
      <Modal
        active={active}
        setActive={val => onModalClose(!!val)}
        className={cx('wrapper', className)}
      >
        <div className={cx('title')}>{t('import_from_file')}</div>
        <div className={cx('inner')}>
          {(step === 0 || (step === 1 && csvFile?.name && isLoadingGetInfo)) && (
            <svg
              className={cx('bg')}
              width='194'
              height='246'
              viewBox='0 0 194 246'
              fill='none'
              xmlns='http://www.w3.org/2000/svg'
            >
              <path
                d='M16 246H178C186.837 246 194 238.837 194 230V59.359C194 55.1053 192.306 51.0267 189.293 48.0244L145.774 4.66544C142.775 1.67762 138.714 0 134.481 0H16C7.16344 0 0 7.16344 0 16V230C0 238.837 7.16345 246 16 246Z'
                fill='#F0F3F7'
              />
              <path
                fillRule='evenodd'
                clipRule='evenodd'
                d='M138.165 0H128V54.0004C128 60.6278 133.373 66.0004 140 66.0004H194V57.8156C194 54.624 192.729 51.5639 190.467 49.312L144.204 3.24811C142.537 1.58894 140.434 0.468927 138.165 0Z'
                fill='#E1E4EB'
              />
            </svg>
          )}
          {step === 0 && (
            <div className={cx('info', 'first')}>
              <Button
                size='small'
                onClick={() => {
                  onCsvInputClick()
                }}
                className={cx('addButton')}
              >
                {csvFile ? csvFile.name : t('get_file')}
              </Button>
              <span className={cx('help')}>({t('file_format')})</span>

              <div className={cx('customButton')} onClick={onCsvTemplateClick}>
                {t('dowload_template')}
                <IconWrapper>
                  <DownloadIcon />
                </IconWrapper>
              </div>
            </div>
          )}
          {step === 1 && csvFile?.name && (isLoadingGetInfo || status === 'created') && (
            <div className={cx('info', 'second')}>
              <Loader size='56' className={cx('loader')} loading />
              <div className={cx('loaderTitle')}>{t('loading')}</div>
              <span className={cx('help')}>{csvFile.name}</span>
            </div>
          )}
          {step === 1 && !csvFile?.name && status === 'created' && !info && eventId && (
            <div className={cx('info', 'second')}>
              <Loader size='56' className={cx('loader')} loading />
              <div className={cx('loaderTitle')}>{t('loading')}</div>
            </div>
          )}
          {step === 1 && csvFile?.name && ((!!getInfoError && !getInfoInfo) || errorLoading) && (
            <div className={cx('info', 'error')}>
              <div>
                <div className={cx('bgWrapper')}>
                  <svg
                    className={cx('bg')}
                    width='154'
                    height='195'
                    viewBox='0 0 154 195'
                    fill='none'
                    xmlns='http://www.w3.org/2000/svg'
                  >
                    <path
                      d='M16 195H138C146.837 195 154 187.837 154 179V48.4371C154 44.1795 152.303 40.0974 149.285 37.0945L116.681 4.65739C113.683 1.67449 109.626 0 105.397 0H16C7.16344 0 0 7.16344 0 16V179C0 187.837 7.16344 195 16 195Z'
                      fill='#F0F3F7'
                    />
                    <path
                      fillRule='evenodd'
                      clipRule='evenodd'
                      d='M109.203 0H102V40C102 46.6274 107.373 52 114 52H154V46.609C154 43.4174 152.728 40.3573 150.467 38.1054L115.511 3.30078C113.779 1.57586 111.575 0.43368 109.203 0Z'
                      fill='#E1E4EB'
                    />
                  </svg>
                  <div className={cx('bgText')}>
                    <Loader size='56' className={cx('loader')} error loading={false} />
                    <span>{t('error.plain')}</span>
                    <span className={cx('help')}>{csvFile.name}</span>
                  </div>
                </div>
                <div className={cx('customButton')} onClick={handleReset}>
                  <IconWrapper>
                    <ClipBoldIcon />
                  </IconWrapper>
                  {t('change_file')}
                </div>
              </div>
              <div>
                <span className={cx('errorTitle')}>{t('error.table_loading')}</span>
              </div>
            </div>
          )}
          {step === 1 &&
            (csvFile?.name || fileName) &&
            !!currentInfo &&
            status === 'completed' &&
            uploading !== '' && (
              <div className={cx('info', 'success')}>
                <div>
                  <div className={cx('bgWrapper')}>
                    <svg
                      className={cx('bg')}
                      width='154'
                      height='195'
                      viewBox='0 0 154 195'
                      fill='none'
                      xmlns='http://www.w3.org/2000/svg'
                    >
                      <path
                        d='M16 195H138C146.837 195 154 187.837 154 179V48.4371C154 44.1795 152.303 40.0974 149.285 37.0945L116.681 4.65739C113.683 1.67449 109.626 0 105.397 0H16C7.16344 0 0 7.16344 0 16V179C0 187.837 7.16344 195 16 195Z'
                        fill='#F0F3F7'
                      />
                      <path
                        fillRule='evenodd'
                        clipRule='evenodd'
                        d='M109.203 0H102V40C102 46.6274 107.373 52 114 52H154V46.609C154 43.4174 152.728 40.3573 150.467 38.1054L115.511 3.30078C113.779 1.57586 111.575 0.43368 109.203 0Z'
                        fill='#E1E4EB'
                      />
                    </svg>
                    <div className={cx('bgText')}>
                      <Loader
                        size='56'
                        className={cx('loader')}
                        success
                        error={false}
                        loading={false}
                      />
                      <span>{t('loaded')}</span>
                      <span className={cx('help')}>{csvFile?.name ?? fileName}</span>
                    </div>
                  </div>
                  <div className={cx('customButton')} onClick={handleReset}>
                    <IconWrapper>
                      <ClipBoldIcon />
                    </IconWrapper>
                    {t('change_file')}
                  </div>
                </div>
                <div>
                  <span className={cx('title')}>{t('employees_data')}</span>

                  <div className={cx('items')}>
                    {typeof users?.created === 'number' && (
                      <div className={cx('item')}>
                        {t('employees_create', {
                          count:
                            Number(users?.created) -
                            Number(info?.users?.domain_skipped?.domain_skipped ?? 0),
                        })}
                      </div>
                    )}
                    {typeof users?.updated === 'number' && (
                      <div className={cx('item')}>
                        {t('employees_update', { count: users?.updated })}
                      </div>
                    )}
                    {typeof users?.deleted === 'number' && (
                      <div className={cx('item')}>
                        {t('employees_delete', { count: users?.deleted })}
                      </div>
                    )}
                    {typeof users?.created === 'number' && (
                      <div className={cx('item')}>
                        {t('employees_skip', {
                          count: Number(info?.users?.domain_skipped?.domain_skipped ?? 0),
                        })}
                      </div>
                    )}
                  </div>
                  <div className={cx('items')}>
                    {typeof departments?.created === 'number' && (
                      <div className={cx('item')}>
                        {t('departments_add', { count: departments?.created })}
                      </div>
                    )}
                    {typeof departments?.deleted === 'number' && (
                      <div className={cx('item')}>
                        {t('departments_delete', { count: departments?.deleted })}
                      </div>
                    )}
                  </div>
                </div>

                <div className={cx('checkboxWrapper')}>
                  <div className={cx('checkboxInner')}>
                    <Checkbox
                      onChange={setIsDelete}
                      customChecked={isDelete}
                      label={<span>{t('delete_employess_and_departments')}</span>}
                    />
                  </div>
                  <div className={cx('checkboxInner')}>
                    <Checkbox
                      onChange={setIsUpdate}
                      customChecked={isUpdate}
                      label={<span> {t('update_employess_and_departments')}</span>}
                    />
                  </div>
                  {mailingText && (
                    <div className={cx('checkboxInner')}>
                      <Checkbox
                        onChange={setIsEmailing}
                        customChecked={isEmailing}
                        label={<span>{mailingText}</span>}
                      />
                    </div>
                  )}
                </div>
              </div>
            )}
          {step === 2 && (
            <div className={cx('info', 'import')}>
              <Loader
                size='56'
                loading={
                  isLoadingImportFile ||
                  isEventLoading ||
                  uploading === 'in_process' ||
                  uploading === 'not_started'
                }
                error={!!errorImportFile || !!errorMessage}
                success={
                  !errorImportFile &&
                  !isLoadingImportFile &&
                  uploading === 'success' &&
                  !errorMessage
                }
              />
              <div
                className={cx('text', {
                  bad: (!isLoadingImportFile && !!errorImportFile) || errorMessage,
                })}
              >
                {uploading === 'in_process' && t('import_process')}
                {((!isLoadingImportFile && !!errorImportFile) || errorMessage) && (
                  <div>
                    <div className={cx('error')}>{errorMessage ?? t('error.import')}</div>
                    {sectionErrors.length > 0 && (
                      <div className={cx('section-errors')}>
                        {sectionErrors.map((error, index) => (
                          <div key={index} className={cx('section-error')}>
                            {error}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                )}
                {uploading === 'success' && <p>{t('import_success')}</p>}
              </div>
            </div>
          )}
        </div>
        {step !== 2 && !isLoadingImportFile && !errorImportFile && (
          <div className={cx('hint')}>
            {t('info.departments_name')}{' '}
            <span className={cx('hint_underline')}>{t('info.departments_name_uniq')}</span>
          </div>
        )}
        <div className={cx('buttonWrapper')}>
          {step === 2 && !isLoadingImportFile && !errorImportFile && (
            <>
              {uploading === 'in_process' && (
                <Button onClick={() => onCancel(false)} color='red'>
                  {t('commons:cancel')}
                </Button>
              )}
              <Button onClick={onClose}>{t('end')}</Button>
            </>
          )}
          {step !== 2 && (
            <>
              <Button color='gray' onClick={handleReset}>
                {t('reset')}
              </Button>
              {!getInfoError && (
                <Button
                  disabled={
                    ((!csvFile || infoLoading) && step === 0) ||
                    (step === 1 && status === 'created')
                  }
                  onClick={() => handleAdd(!!users?.deleted || !!departments?.deleted)}
                >
                  {step === 1 && csvFile?.name && !!getInfoInfo ? t('agree') : t('add')}
                </Button>
              )}
            </>
          )}
        </div>
        <input
          //TODO move to hook
          className={cx('file')}
          ref={csvInput}
          type={'file'}
          onChange={onCsvFileChange}
          accept={'.xlsx'}
        />
      </Modal>
      <ImportDeleteModal
        active={openDelete}
        setActive={setOpenDelete}
        onClose={handleImport}
        employees={users?.deleted}
        departments={departments?.deleted}
      />
    </>
  )
}
