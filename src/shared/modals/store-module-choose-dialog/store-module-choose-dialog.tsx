import classNamesBind from 'classnames/bind'
import React, { useCallback, useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON> } from '@/shared/ui'
import { StoreModuleChooseDialogProps } from './store-module-choose-dialog.d'
import styles from './store-module-choose-dialog.module.scss'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { useGetModulesQuery } from '@/store/services/module-service'
import { Checkbox } from '@/shared/ui/checkbox'

const cx = classNamesBind.bind(styles)

export interface IModule {
  can_delete: boolean
  can_edit: boolean
  description: string
  id: UUID
  is_available: boolean
  old_id: number
  slide_count: number
  title: string
}

const TRANSLATION_FILE = 'modals__store-module-choose'

const StoreModuleChooseDialog: React.FC<StoreModuleChooseDialogProps.Props> = props => {
  const { onClose, open, selected, setDisabled, setModules } = props

  const [selected_modules, setSelected] = useState<UUID[]>(selected)

  useEffect(() => {
    setDisabled && setDisabled(!selected_modules?.length)
  }, [selected_modules, setDisabled])

  useEffect(() => {
    setSelected(selected)
  }, [selected])

  const { data: modules } = useGetModulesQuery()

  const onModuleClick = useCallback((id: UUID) => {
    setSelected(sl => (sl.includes(id) ? sl.filter(s => s !== id) : [...sl, id]))
  }, [])

  const _onClose = useCallback(() => setModules(selected_modules), [setModules, selected_modules])

  const { t } = useTranslation(TRANSLATION_FILE)

  return (
    <Modal setActive={onClose.bind(null, setSelected)} active={open} className={cx('dialog')}>
      <h2 className={cx('title')}>{t('title')}</h2>
      <div className={cx('content')}>
        {modules?.map(({ id, title, slide_count, is_available }) => (
          <Card
            key={'m' + id}
            padding='small'
            width='wide'
            className={cx('module', {
              active: selected_modules.includes(id),
              disabled: !is_available,
            })}
            title={slide_count === 0 ? t('no_slides') : ''}
            onClick={onModuleClick.bind(null, id)}
          >
            <Checkbox
              className={cx('checkbox')}
              customChecked={selected_modules.includes(id)}
              label={<div className={cx('moduleTitle')}>{title}</div>}
            />
          </Card>
        ))}
      </div>
      <div className={cx('footer')}>
        <Button
          size='big'
          className={cx('button', 'dialogFooter')}
          onClick={_onClose}
          disabled={!selected_modules.length}
        >
          {t('choose')}
        </Button>
      </div>
    </Modal>
  )
}

export { StoreModuleChooseDialog }
