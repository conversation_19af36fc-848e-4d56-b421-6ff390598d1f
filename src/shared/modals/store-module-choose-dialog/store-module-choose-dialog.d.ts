import { UUID } from 'core/models'

export type BaseModule = {
  // TODO FIX
  id: number
  title: string
}

export declare namespace StoreModuleChooseDialogProps {
  interface Own {
    open: boolean
    selected: UUID[]
    onClose: (setSelected: Dispatch<SetStateAction<UUID[]>>) => void
    setDisabled?: Dispatch<SetStateAction<boolean>>
    setModules: (modules: UUID[]) => void
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch
}

export {}
