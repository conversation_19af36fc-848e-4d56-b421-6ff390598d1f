@use '../../../shared/assets/styles/mixins/text';

.title {
  @include text.title(20px);
  padding: 40px 0 30px !important;

  text-align: center;
}

.content {
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  padding: 0 50px 40px !important;
}

.footer {
  display: flex;
  justify-content: center !important;
  padding: 30px 90px 40px !important;
}

.module {
  border: 1px solid var(--grey);

  cursor: pointer;
  flex-direction: row !important;
  flex-shrink: 0;
  padding: 15px;
  width: 100%;

  + .module {
    margin-top: 10px;
  }
  &.active {
    border-color: var(--color-primary);
  }
  &.disabled {
    opacity: 0.5;

    pointer-events: none;
  }
}

.checkbox {
  margin-right: 10px;

  pointer-events: none;
}

.moduleTitle {
  @include text.main(18px);
  box-sizing: border-box; /* 2 */
  max-width: calc(100% - 30px);
}

.button {
  max-width: 240px;
  width: 100%;
}

.dialog {
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  max-width: 600px;
  width: 100%;
}
