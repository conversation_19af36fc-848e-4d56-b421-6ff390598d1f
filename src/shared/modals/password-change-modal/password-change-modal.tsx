import { FC } from 'react'
import { PasswordChangeModalProps } from './password-change-modal.d'

import classNamesBind from 'classnames/bind'
import styles from './password-change-modal.module.scss'
import { Modal } from '@/shared/components'
import { Button, Input, FormTitle, PasswordGenerator } from '@/shared/ui'
import { usePasswordChangeModal } from './use-password-change-modal'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__password-change'

export const PasswordChangeModal: FC<PasswordChangeModalProps.Props> = props => {
  const { active, className, setActive } = props
  const { t } = useTranslation(TRANSLATION_FILE)

  const {
    isLoading,
    isValid,
    onSubmit,
    handleSubmit,
    register,
    errors,
    error,
    organization_id,
    setValue,
    userRole,
  } = usePasswordChangeModal({ setActive })

  return (
    <Modal setActive={setActive} active={active} className={cx('modal-wrapper', className)}>
      <form onSubmit={handleSubmit(onSubmit)} className={cx('wrapper')}>
        <FormTitle>{t('title')}</FormTitle>

        <Input
          type='password'
          label={t('password_label')}
          placeholder={t('password_placeholder')}
          classNameWrapper={cx('input')}
          error={errors.currentPassword?.message}
          fullWidth
          register={register('currentPassword')}
        />
        <Input
          type='password'
          label={t('new_password_label')}
          placeholder={t('new_password_placeholder')}
          classNameWrapper={cx('input')}
          error={errors.password?.message}
          fullWidth
          register={register('password')}
        />
        <Input
          type='password'
          label={t('confirm_password_label')}
          placeholder={t('confirm_password_placeholder')}
          classNameWrapper={cx('input')}
          error={errors.passwordConfirm?.message}
          fullWidth
          register={register('passwordConfirm')}
        />

        <PasswordGenerator
          organizationId={organization_id ?? ''}
          roles={userRole ? [userRole] : []}
          onPasswordChange={password => {
            setValue('password', password, {
              shouldValidate: true,
            })
            setValue('passwordConfirm', password, {
              shouldValidate: true,
            })
          }}
        />

        <div className={cx('button-wrapper')}>
          <Button type='button' fullWidth color='gray' onClick={() => setActive(false)}>
            {t('cancel')}
          </Button>
          <Button fullWidth type='submit' loading={isLoading} disabled={!isValid}>
            {t('submit')}
          </Button>
        </div>
        {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
      </form>
    </Modal>
  )
}
