import { useMemo, useState } from 'react'
import { authAPI } from 'entities/auth'
import { SubmitHandler, useForm } from 'react-hook-form'
import { handleErrorResponseAndTranslate } from '@/shared/contexts/notifications/helper'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useTranslation } from 'react-i18next'
import { useUserOrganizationId, userAPI } from '@/entities/employee'

interface IPasswordChangeInputs {
  currentPassword: string
  password: string
  passwordConfirm: string
}
const { useUpdatePasswordMutation } = authAPI
const TRANSLATION_FILE = 'modals__password-change'

export const usePasswordChangeModal = ({
  setActive,
}: {
  setActive: React.Dispatch<React.SetStateAction<boolean>>
}) => {
  const [updatePassword, { isLoading }] = useUpdatePasswordMutation()
  const organization_id = useUserOrganizationId()
  const { data: userInfo } = userAPI.useGetUserInfoQuery()

  const [error, setError] = useState('')

  const { t } = useTranslation(TRANSLATION_FILE)

  const resolver = useMemo(
    () =>
      zodResolver(
        z
          .object({
            currentPassword: z.string().min(1, {
              message: t('commons:required_field'),
            }),
            password: z.string().min(1, {
              message: t('commons:required_field'),
            }),
            passwordConfirm: z.string().min(1, {
              message: t('commons:required_field'),
            }),
          })
          .refine(
            data => {
              return data.passwordConfirm === data.password
            },
            {
              message: t('passwords_equal_error'),
              path: ['passwordConfirm'],
            },
          ),
      ),
    [t],
  )

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setValue,
  } = useForm<IPasswordChangeInputs>({
    mode: 'onBlur',
    defaultValues: {
      currentPassword: '',
      password: '',
      passwordConfirm: '',
    },
    resolver,
  })

  const onSubmit: SubmitHandler<IPasswordChangeInputs> = async data => {
    const { password, currentPassword } = data

    await updatePassword({ password: currentPassword, new_password: password })
      .unwrap()
      .then(() => {
        reset()
        setError('')
        setActive(false)
      })
      .catch(e => setError(handleErrorResponseAndTranslate(e)))
  }

  return {
    isLoading,
    isValid,
    onSubmit,
    handleSubmit,
    register,
    errors,
    error,
    organization_id,
    setValue,
    userRole: userInfo?.role,
  }
}
