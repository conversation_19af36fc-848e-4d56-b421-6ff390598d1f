import classNamesBind from 'classnames/bind'
import styles from './styles.module.scss'

import { Button } from '@/shared/ui'
import { useTranslation } from 'react-i18next'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  handleNextStep,
  selectDisableNext,
  selectModal,
  setDisableNext,
  setModal,
} from '@/store/slices/user-course-slice'
import { Modal } from '@/shared/components'
import { useNavigate } from 'react-router-dom'
import { URLS } from '../../../configs/urls'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { GOALS } from '@/shared/constants'

const cx = classNamesBind.bind(styles)

export const CompleteModal = ({ isUserSide }: { isUserSide?: boolean }) => {
  const { t } = useTranslation()
  const dispatch = useAppDispatch()
  const disableNext = useAppSelector(selectDisableNext)
  const modalState = useAppSelector(selectModal)
  const navigate = useNavigate()
  const analytics = useAnalytics()

  const handleComplete = async () => {
    analytics.event(GOALS['course-move-to-courses-after-quiz'].name)
    await dispatch(handleNextStep())
    navigate(URLS.USER_MY_COURSES_PAGE)
  }

  const hasButtons = modalState?.agreeeBtnText || modalState?.disagreeBtnText

  return (
    <>
      <Modal
        active={!!modalState}
        setActive={() => {
          dispatch(setModal(undefined))
          dispatch(setDisableNext(false))
        }}
        className={cx('modal')}
      >
        <h2 className={cx('title')}>{modalState?.title}</h2>
        {modalState?.description && <p className={cx('description')}>{modalState?.description}</p>}
        {hasButtons && (
          <div className={cx('actions')}>
            {modalState?.disagreeBtnText && (
              <Button
                onClick={() => modalState.onDisagree?.()}
                color='darkGray'
                fullWidth={!!modalState?.agreeeBtnText}
              >
                {modalState?.disagreeBtnText}
              </Button>
            )}
            {modalState?.agreeeBtnText && (
              <Button
                onClick={() => modalState.onAgree?.()}
                fullWidth={!!modalState?.disagreeBtnText}
              >
                {modalState?.agreeeBtnText}
              </Button>
            )}
          </div>
        )}
      </Modal>
      <Button
        disabled={isUserSide && disableNext}
        onClick={handleComplete}
        size='big'
        color='green'
        className={cx('button')}
      >
        {t('commons:return_to_courses')}
      </Button>
    </>
  )
}
