/* eslint-disable no-unsafe-optional-chaining */
import { FC, RefObject, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import styles from './add-department-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { AddDepartmentModalProps } from './add-department-modal.d'
import { Modal } from '@/shared/components'
import { Button, Input, Loader, Select, IListItem } from '@/shared/ui'
import { departmentAPI } from 'entities/department'
import { SubmitHandler, useForm } from 'react-hook-form'
import { useDepartments } from '@/entities/department'
import { IDepartment } from '@/entities/department'
import { useTranslation } from 'react-i18next'
import { useGetDepartmentsTitlesQuery } from '@/entities/department/model/endpoints'
import InfiniteScroll from 'react-infinite-scroll-component'
import { usePrioritizedList } from '@/shared/hooks'

const cx = classNamesBind.bind(styles)

interface Inputs {
  title: string
  department: UUID | null
}

const DEPARTMENTS_LIMIT = 10
const DEPARTMENTS_LIST_ID = 'create-employee-departments-list'

export const AddDepartmentModal: FC<AddDepartmentModalProps.Props> = props => {
  const { className, active, setActive } = props
  const { t } = useTranslation('modals__add-department-modal')

  const { activeDepartment } = useDepartments({ isSkipDepartmentsReq: true })

  const [departmentsPage, setDepartmentsPage] = useState(1)
  const [departmentsList, setDepartmentsList] = useState<IDepartment[]>([])
  const [departmentsTotalCount, setDepartmentsTotalCount] = useState(0)
  const [departmentsSearch, setDepartmentsSearch] = useState('')

  const {
    data: departments,
    error: errorDepartments,
    isLoading: isLoadingDepartments,
    isFetching: isFetchingDepartments,
  } = useGetDepartmentsTitlesQuery(
    {
      limit: DEPARTMENTS_LIMIT,
      offset: (departmentsPage - 1) * DEPARTMENTS_LIMIT,
      search: departmentsSearch || undefined,
    },
    {
      skip: !active,
    },
  )

  useEffect(() => {
    if (!departments) return

    if (departmentsPage === 1) {
      setDepartmentsList(departments.data)
    } else {
      setDepartmentsList(prev => [...prev, ...departments.data])
    }

    setDepartmentsTotalCount(departments.total_count)
  }, [departments])

  const departmentsHasMore = departmentsPage * DEPARTMENTS_LIMIT < departmentsTotalCount

  const renderListInnerWrapper = useCallback(
    (children: React.ReactNode) => (
      <InfiniteScroll
        dataLength={departmentsList.length}
        next={() => setDepartmentsPage(prev => prev + 1)}
        hasMore={departmentsHasMore}
        loader={<Loader size='28' loading />}
        scrollableTarget={DEPARTMENTS_LIST_ID}
      >
        {children}
      </InfiniteScroll>
    ),
    [departmentsHasMore, departmentsList.length],
  )

  const handleChangeDepartmentSearch = useCallback((search: string) => {
    setDepartmentsPage(1)
    setDepartmentsSearch(search)
  }, [])

  const [createDepartments, { isLoading: isLoadingCreate }] =
    departmentAPI.useCreateDepartmentMutation()
  const buttonRef: RefObject<HTMLButtonElement> = useRef(null)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    setError: setErrorForm,
  } = useForm<Inputs>({
    mode: 'onBlur',
    defaultValues: {
      title: '',
      department: null,
    },
  })

  const [error, setError] = useState('')

  const onSubmit: SubmitHandler<Inputs> = async data => {
    const { department: parent_department_uuid, title } = data

    const body: { title: string; parentID?: UUID } = {
      title,
    }

    if (parent_department_uuid) body['parentID'] = parent_department_uuid

    await createDepartments(body)
      .unwrap()
      .then(() => {
        setError('')
        setActive(false)
      })
      .catch(e => {
        setError(
          e.message ||
            e?.data?.message ||
            e.error ||
            e?.data?.detail ||
            t('commons:error_unexpected'),
        )
      })
  }

  const handleClose = () => setActive(false)
  const handleTrim = (str: string, name: keyof Inputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  const handleChangeDepartment = (item: IListItem) => setValue('department', item.id)

  const nestingListItems = useMemo<IListItem[]>(() => {
    const nestingDepartments = departmentsList ? departmentsList : ([] as IDepartment[])

    return [
      { title: t('commons:absent'), id: '', hasPriority: true },
      ...nestingDepartments?.map(d => ({
        title: d?.title,
        id: d?.id,
      })),
    ]
  }, [departmentsList, t])

  const prioritizedDepartmentsList = usePrioritizedList<{ id: string; title: string }>(
    nestingListItems,
    activeDepartment,
  )

  const handleEnterPress = (e: KeyboardEvent) => {
    if (e.code === 'Enter' && buttonRef.current) {
      buttonRef.current.click()
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', handleEnterPress)

    return () => document.removeEventListener('keyup', handleEnterPress)
  }, [])

  return (
    <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
      <div className={cx('title')}>{t('department_creation')}</div>

      {isLoadingDepartments && <Loader loading size='56' />}
      {!isLoadingDepartments && errorDepartments && (
        <div className='error-text'>{t('error_unexpected_with_dots')}</div>
      )}
      {!isLoadingDepartments && !errorDepartments && (
        <form onSubmit={handleSubmit(onSubmit)} className={cx('wrapper')}>
          <Input
            label={t('department_name')}
            placeholder={t('department_name_input')}
            classNameWrapper={cx('input')}
            error={errors.title?.message}
            fullWidth
            required
            register={register('title', {
              required: t('commons:required_field'),
              onBlur: e => handleTrim(e.target.value, 'title', true),
            })}
          />
          <Select
            remoteSearch
            loading={isLoadingDepartments || isFetchingDepartments}
            label={t('enclosure')}
            placeholder={t('choose_parent_department')}
            className={cx('input')}
            required
            list={prioritizedDepartmentsList}
            value={activeDepartment?.id || ''}
            handleChange={handleChangeDepartment}
            help={t('department_select_help')}
            renderListInnerWrapper={renderListInnerWrapper}
            listWrapperId={DEPARTMENTS_LIST_ID}
            handleChangeSearch={handleChangeDepartmentSearch}
          />
          <div className={cx('buttonWrapper')}>
            <Button type='button' color='gray' onClick={handleClose}>
              {t('commons:cancel')}
            </Button>
            <Button
              ref={buttonRef}
              type='submit'
              color='green'
              disabled={!isValid}
              loading={isLoadingCreate}
            >
              {t('commons:create')}
            </Button>
          </div>

          {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
        </form>
      )}
    </Modal>
  )
}
