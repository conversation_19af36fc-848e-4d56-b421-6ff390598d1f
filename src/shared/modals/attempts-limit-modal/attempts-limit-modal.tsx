import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './attempts-limit-modal.module.scss'

import { AttemptsLimitModalProps } from './attempts-limit-modal.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { useAppDispatch, useAppSelector } from '@/store'
import { handleNextStep, selectLastStep } from '@/store/slices/user-course-slice'
import { CompleteModal } from '../editors/complete'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { GOALS } from '@/shared/constants'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__attempts-limit'

export const AttemptsLimitModal: React.FC<AttemptsLimitModalProps.Props> = props => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const { active, setActive } = props
  const dispatch = useAppDispatch()
  const isLastStep = useAppSelector(selectLastStep)
  const analytics = useAnalytics()

  const handleNext = () => {
    analytics.event(GOALS['course-next-theme-item-after-quiz'].name)
    dispatch(handleNextStep())
    setActive(false)
  }

  return (
    <Modal className={cx('wrapper')} active={active} setActive={setActive}>
      <div className={cx('title')}>{t('title')}</div>
      <div className={cx('subtitle')}>{t('subtitle')}</div>

      {isLastStep ? (
        <CompleteModal />
      ) : (
        <Button color='green' size='big' className={cx('button')} onClick={handleNext}>
          {t('next_step')}
        </Button>
      )}
    </Modal>
  )
}
