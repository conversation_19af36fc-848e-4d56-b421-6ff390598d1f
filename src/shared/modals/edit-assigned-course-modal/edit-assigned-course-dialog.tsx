import { useCallback, useMemo } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './edit-assigned-course-dialog.module.scss'
import { EditAssignedCourseDialogProps } from './edit-assigned-course-dialog.d'
import { Button, Input, Textarea } from '@/shared/ui'
import { FileUploader } from '@/shared/components'
import {
  DASH_DATE_FORMAT,
  getDate,
  addDays,
  differenceInDays,
  format,
  max,
} from '@/shared/helpers/date'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { ICourseInfo } from '@/shared/types/store/course'
import { INewCourseStatus } from '@/shared/types/enums'
import {
  useToggleAssignedCourseMutation,
  useUpdateAssignedCourseMutation,
} from '@/store/services/assigned-course-service'
import { Checkbox } from '@/shared/ui/checkbox'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

const cx = classNamesBind.bind(styles)

type FormInput = {
  title: string
  description: string
  start_date: string
  end_date: string
  need_assigned_messages: boolean
  need_notify_messages: boolean
  picture?: FileList
}

const TRANSLATION_FILE = 'modals__edit-assigned-course'

export const EditAssignedCourseDialog: React.FC<EditAssignedCourseDialogProps.Props> = props => {
  const { onClose, open, id, info, isScormPage = false } = props
  const { t } = useTranslation(TRANSLATION_FILE)

  const [updateCourse, { isLoading: isUpdateLoading }] = useUpdateAssignedCourseMutation()

  const [toggleCourse, { isLoading: isToggleLoading }] = useToggleAssignedCourseMutation()

  const isLoading = isUpdateLoading || isToggleLoading

  const resolver = useMemo(
    () =>
      zodResolver(
        z
          .object({
            title: z
              .string()
              .max(255, {
                message: t('errors.title_max_len', { max: 255 }),
              })
              .min(1, {
                message: t('errors.title_required'),
              }),
            description: z
              .string()
              .max(400, {
                message: t('errors.descr_max_len', { max: 400 }),
              })
              .optional(),
            start_date: z.string(),
            end_date: z.string(),
            need_assigned_messages: z.boolean(),
            need_notify_messages: z.boolean(),
            picture: z.any().optional(),
          })
          .refine(data => new Date(data.end_date) >= new Date(Date.now()), {
            message: t('errors.end_date_lt_today'),
            path: ['end_date'],
          })
          .refine(
            data => {
              if (info?.status !== INewCourseStatus.Planned) return true
              return new Date(data.start_date) <= new Date(data.end_date)
            },
            {
              message: t('errors.start_date_gt_end'),
              path: ['end_date'],
            },
          )
          .refine(
            data => {
              if (info?.status !== INewCourseStatus.Planned) return true

              return differenceInDays(new Date(data?.start_date), new Date(data?.end_date))
            },
            {
              message: t('errors.start_date_equals_end_date'),
              path: ['end_date'],
            },
          ),
      ),
    [t, info],
  )

  const { control, handleSubmit, setValue, watch } = useForm<FormInput>({
    defaultValues: {
      title: info?.title ?? '',
      description: info?.description ?? '',
      start_date: info?.start_date,
      end_date: info?.end_date,
      need_assigned_messages: info?.need_assigned_messages,
      need_notify_messages: info?.need_notify_messages,
    },
    resolver,
  })

  const onFormSubmit = useCallback(
    async (data: {
      title?: string
      description?: string
      picture?: FileList
      start_date: string
      end_date: string
      need_assigned_messages: boolean
      need_notify_messages: boolean
    }) => {
      const {
        title,
        description,
        picture,
        start_date,
        end_date,
        need_assigned_messages,
        need_notify_messages,
      } = data

      const course = new FormData()

      if (title) course.append('title', title)
      if (start_date) course.append('start_date', start_date)
      if (end_date) course.append('end_date', end_date)
      course.append('description', description ?? '')

      if (picture && picture[0]) course.append(`${isScormPage ? 'picture' : 'image'}`, picture[0])

      const dataRequestPromise = updateCourse({
        body: course,
        id,
        isScorm: isScormPage,
      })
      const notifyRequestPromise = toggleCourse({
        id,
        params: {
          activate_assigned_messages: need_assigned_messages,
          activate_notify_messages: need_notify_messages,
        },
        isScorm: isScormPage,
      })

      await Promise.all([notifyRequestPromise, dataRequestPromise])

      const updateInfo: ICourseInfo = {}

      if (title) updateInfo.title = title
      updateInfo.description = description ?? ''
      if (start_date) updateInfo.start_date = start_date
      if (end_date) updateInfo.end_date = end_date
      updateInfo.need_assigned_messages = need_assigned_messages
      updateInfo.need_notify_messages = need_notify_messages

      onClose()
    },
    [onClose, id, isScormPage, toggleCourse, updateCourse],
  )

  const getImageName = (imageSrc: string): string => {
    const lastSlashPosition = imageSrc.lastIndexOf('/')

    return imageSrc.slice(lastSlashPosition + 1)
  }

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <form className={cx('form')} onSubmit={handleSubmit(onFormSubmit)}>
        <h3 className={cx('title')}>{t('dialogs.edit_assigned_course.title')}</h3>
        <div className={cx('content')}>
          <Controller
            name='title'
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                bordered
                fullWidth
                className={cx('field')}
                placeholder={t('dialogs.edit_assigned_course.name')}
                error={error?.message}
                {...field}
              />
            )}
          />
          <Controller
            name='description'
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Textarea
                view='bordered'
                rows={3}
                className={cx('field', 'text-area')}
                error={error?.message}
                placeholder={t('dialogs.edit_assigned_course.description')}
                {...field}
              />
            )}
          />
          <Controller
            name='picture'
            control={control}
            render={({ field, fieldState: { error } }) => {
              const getPlaceholder = () => {
                if (typeof field?.value === 'string') return field

                if (field?.value instanceof FileList) return field?.value?.[0]?.name

                return (
                  (info?.picture && getImageName(info?.picture)) ||
                  t('dialogs.edit_assigned_course.image')
                )
              }

              return (
                <FileUploader
                  accept='image/*'
                  className={cx('field')}
                  placeholder={getPlaceholder()}
                  error={error?.message}
                  onChange={e => {
                    const files = e.target.files
                    if (!files) {
                      setValue('picture', undefined)
                      return
                    }
                    setValue('picture', files)
                  }}
                />
              )
            }}
          />
          <Controller
            name='need_assigned_messages'
            control={control}
            render={({ field }) => (
              <Checkbox
                initialChecked={field.value}
                onChange={field.onChange}
                variant='center'
                label={<span>{t('dialogs.edit_assigned_course.send_assign_notif')}</span>}
              />
            )}
          />
          <Controller
            name='need_notify_messages'
            control={control}
            render={({ field }) => (
              <Checkbox
                initialChecked={field.value}
                onChange={field.onChange}
                variant='center'
                label={<span>{t('dialogs.edit_assigned_course.send_lagging_notif')}</span>}
              />
            )}
          />
          {info?.status === INewCourseStatus.Planned && (
            <div className={cx('field__wrapper')}>
              <span>{t('dialogs.edit_assigned_course.start_date')}</span>
              <Controller
                name='start_date'
                control={control}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    bordered
                    fullWidth
                    type='date'
                    error={error?.message}
                    className={cx('field')}
                    placeholder={t('dialogs.edit_assigned_course.start_date')}
                    min={getDate()}
                    {...field}
                    readOnly={false}
                  />
                )}
              />
            </div>
          )}
          <div className={cx('field__wrapper')}>
            <span>{t('dialogs.edit_assigned_course.end_date')}</span>
            <Controller
              name='end_date'
              control={control}
              render={({ field, fieldState: { error } }) => (
                <Input
                  bordered
                  fullWidth
                  type='date'
                  error={error?.message}
                  className={cx('field')}
                  placeholder={t('dialogs.edit_assigned_course.end_date')}
                  {...field}
                  readOnly={false}
                  min={format(
                    max([new Date(watch('start_date')), addDays(new Date(), 1)]),
                    DASH_DATE_FORMAT,
                  ).toString()}
                />
              )}
            />
          </div>
          <div className={cx('footer')}>
            <Button
              size='veryBig'
              view='border'
              className={cx('button', 'dialogFooter')}
              type='reset'
              onClick={onClose}
            >
              {t('dialogs.edit_assigned_course.cancel')}
            </Button>
            <Button
              size='veryBig'
              className={cx('button', 'dialogFooter')}
              disabled={isLoading}
              type='submit'
              loading={isLoading}
            >
              {t('dialogs.edit_assigned_course.save')}
            </Button>
          </div>
        </div>
      </form>
    </Modal>
  )
}

export default EditAssignedCourseDialog
