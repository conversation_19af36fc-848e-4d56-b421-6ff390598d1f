@use '../../../shared/assets/styles/mixins/text';

.content {
  padding: 0 60px !important;
}

.dialog {
  max-width: 800px;
  width: calc(100vw - 64px);
}

.field__wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.label {
  align-items: center;
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.text-area {
  max-height: 100px;
}

.errorText {
  margin-top: 12px;
  padding: 0 60px;

  text-align: center;
}

.form {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.title {
  @include text.title(30px);
  padding: 40px 0 !important;

  text-align: center;
}

.field + .field {
  margin-top: 20px;
  width: 100%;
}

.modulesTitle {
  @include text.title(16px);

  color: rgb(0, 0, 0);
  margin-top: 30px;
  text-align: center;
}

.modulesWrapper {
  border: 1px solid var(--color-primary);
  border-radius: 10px;
  margin-top: 10px;
  padding: 15px 25px;
}

.module {
  @include text.main(16px);
  align-items: center;

  border: 1px solid var(--grey);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 10px 0;
  .delete {
    margin-left: 10px;
  }
  + .addModule {
    margin: 25px 0 10px;
  }
}

.addModule {
  @include text.main(16px);

  color: var(--color-primary);

  cursor: pointer;
  display: flex;
  justify-content: center;
  text-decoration: underline;
  width: 100%;
}

.footer {
  display: flex;
  gap: 12px;
  justify-content: center !important;
  padding: 30px 90px 40px !important;
}

.button {
  max-width: 240px;
  width: 100%;
}
