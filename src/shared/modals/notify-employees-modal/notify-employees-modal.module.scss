@use "../../../shared/assets/styles/mixins/text";

.content {
  padding: 0 20px !important;
}

.dialog {
  max-width: 600px;
  width: calc(100vw - 64px);
}

.form {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
  gap: 24px;
}

.header {
  align-items: center;
  display: flex;
  margin-bottom: 30px;
  padding-top: 10px;
  .to {
    margin-right: 20px;
  }
}

.title {
  @include text.title(30px);

  text-align: center;
}

.field + .field {
  margin-top: 20px;
  width: 100%;
}

.footer {
  display: flex;
  justify-content: center !important;
  gap: 24px;
}

.legend {
  display: flex;

  .wrapper {
    display: flex;
    flex: 1 0;
    flex-direction: column;
    flex-wrap: wrap;
    margin: -8px -12px;

    .legendItem {
      margin: 8px 12px;

      @media (max-width: 480px) {
        margin: 3px 7px;
      }
    }
  }

  &.horizontal .wrapper {
    flex-direction: row;
    justify-content: center;

    @media (max-width: 480px) {
      flex-direction: column;
      justify-content: flex-start;
    }
  }

  &.collapsible .wrapper {
    flex-wrap: nowrap;
  }
}

.button {
  width: 100%;
  max-width: 200px;
}
