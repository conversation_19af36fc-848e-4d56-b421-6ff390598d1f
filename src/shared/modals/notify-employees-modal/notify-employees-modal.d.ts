/* eslint-disable @typescript-eslint/no-explicit-any */
import { UUID } from "core/models/base.model";
import { IGroupsEmployee } from "react-app/modules/admin/learning/pages";
export declare namespace NotifyEmployeesDialogProps {
  interface Own {
    id?: UUID;
    type: "phishing" | "organization" | "course";
    activeGroup: string | null;
    onClose?(): void;
    onGroupChange(g: any): void;
    groups: Array<{
      title: string;
      color: string;
      group: string;
      employees?: IGroupsEmployee[];
    }>;
    employees?: UUID[];
  }

  interface Inputs {
    text: string
  }

  interface Store {}

  interface Dispatch {}

  type Props = Own & Store & Dispatch;
}

export {};
