import classNamesBind from 'classnames/bind'
import React from 'react'
import { Button, Textarea } from '@/shared/ui'
import { useNotification } from '@/shared/contexts/notifications'
import { NotifyEmployeesDialogProps } from './notify-employees-modal.d'
import styles from './notify-employees-modal.module.scss'
import { Modal } from '@/shared/components/modal'
import { useTranslation } from 'react-i18next'
import { organizationAPI } from 'entities/organization'
import { SubmitHandler, useForm } from 'react-hook-form'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__notify-employees'

const NotifyEmployeesDialog: React.FC<NotifyEmployeesDialogProps.Props> = props => {
  const { activeGroup, onClose, groups, employees: employeesProps } = props

  const [notifyEmployees, { isLoading }] = organizationAPI.useNotifyEmployeesMutation()
  const { handleErrorResponse } = useNotification()

  const _onClose = () => onClose && onClose()

  const { t } = useTranslation(TRANSLATION_FILE)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    setError: setErrorForm,
  } = useForm<NotifyEmployeesDialogProps.Inputs>({
    mode: 'onBlur',
    defaultValues: {
      text: '',
    },
  })

  const onSubmit: SubmitHandler<NotifyEmployeesDialogProps.Inputs> = async data => {
    const { text } = data

    if (!activeGroup) return

    const employees = employeesProps?.length
      ? employeesProps
      : groups.filter(g => g.group === activeGroup)[0].employees?.map(e => e.id)

    if (!employees?.length) {
      handleErrorResponse({
        status: 'error',
        message: t('empty'),
      })
      return
    }

    await notifyEmployees({ employees, text }).unwrap()
    _onClose()
  }

  const handleTrim = (
    str: string,
    name: keyof NotifyEmployeesDialogProps.Inputs,
    isRequired = false,
  ) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  return (
    <Modal setActive={_onClose} active={!!activeGroup} className={cx('dialog')}>
      <form onSubmit={handleSubmit(onSubmit)} className={cx('form')}>
        <h2 className={cx('title')}>{t('title')}</h2>

        <div className={cx('content')}>
          <Textarea
            fullWidth
            required
            rows={5}
            name='text'
            className={cx('field')}
            placeholder={t('text')}
            error={errors.text?.message}
            register={register('text', {
              required: t('commons:required_field'),
              onBlur: e => handleTrim(e.target.value, 'text', true),
            })}
          />
        </div>
        <div className={cx('footer')}>
          <Button
            size='veryBig'
            view='border'
            className={cx('button', 'dialogFooter')}
            type='reset'
            onClick={onClose}
          >
            {t('cancel')}
          </Button>
          <Button
            size='veryBig'
            className={cx('button', 'dialogFooter')}
            type='submit'
            loading={isLoading}
            disabled={isLoading || !isValid}
          >
            {t('send')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export default NotifyEmployeesDialog
