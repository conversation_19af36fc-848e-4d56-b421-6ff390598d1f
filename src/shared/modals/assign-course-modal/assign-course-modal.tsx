import { FC, useMemo, useState } from 'react'
import styles from './assign-course-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { AssignCourseModalProps } from './assign-course-modal.d'
import { Modal } from '@/shared/components'
import { Button, Checkbox, Loader } from '@/shared/ui'
import { useTranslation } from 'react-i18next'
import { Course, coursesApi, CourseCard } from '@/entities/courses'
import { coursesByTagApi } from '@/store/services/endpoints/new-tags-endpoint'
import { useUserOrganizationId } from '@/entities/employee'

const cx = classNamesBind.bind(styles)

export const AssignCourseModal: FC<AssignCourseModalProps.Props> = props => {
  const {
    className,
    active,
    setActive,
    selected = [],
    handleSelect,
    actionID,
    multi = true,
    tagId,
  } = props
  const { t } = useTranslation('modals__assign-course-modal')
  const userOrganizationId = useUserOrganizationId()

  const {
    data: allCoursesData,
    error,
    isLoading,
  } = coursesApi.useGetCoursesByOrganizationIdQuery({
    organization_id: userOrganizationId ?? '',
    limit: Number.MAX_SAFE_INTEGER,
  })

  const { data: coursesByTag } = coursesByTagApi.useGetCoursesByTagQuery(
    {
      tag_id: String(tagId),
    },
    { skip: !tagId },
  )

  const coursesByType = useMemo(() => {
    const result: Record<'archived' | 'not_acrhived', Course[]> = {
      archived: [],
      not_acrhived: [],
    }
    if (!allCoursesData?.data?.length && !coursesByTag?.length) {
      return result
    }

    const seenIds = new Set<string>()
    const allCourses = []

    if (allCoursesData?.data) {
      for (const course of allCoursesData.data) {
        if (!seenIds.has(course.id)) {
          seenIds.add(course.id)
          allCourses.push(course)
        }
      }
    }

    if (coursesByTag) {
      for (const course of coursesByTag) {
        if (!seenIds.has(course.id)) {
          seenIds.add(course.id)
          allCourses.push(course)
        }
      }
    }

    for (const course of allCourses) {
      const pushedCourse = course as Course
      if (course.archived) {
        result.archived.push(pushedCourse)
      } else {
        result.not_acrhived.push(pushedCourse)
      }
    }

    return result
  }, [allCoursesData, coursesByTag])

  const [courses, setCourses] = useState<UUID[]>(selected)

  const handleSelectCourse = (id: UUID) => {
    const position = courses.indexOf(id)

    if (multi) {
      if (position === -1) {
        setCourses(prev => [...prev, id])
      } else {
        setCourses(prev => [...prev.slice(0, position), ...prev.slice(position + 1)])
      }
    } else {
      if (position === -1) {
        setCourses([id])
      } else {
        setCourses([])
      }
    }
  }
  const handleClick = () => {
    handleSelect(actionID!, courses)
    setActive(() => false)
  }
  const handleClose = () => {
    if (!actionID) return

    handleSelect(actionID, selected)
    setActive(() => false)
  }

  return (
    <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
      <div className={cx('title')}>{t('commons:course_choose')}</div>
      <div className={cx('innerWrapper')}>
        {isLoading && <Loader size='56' loading />}
        {!isLoading && error && <div className='error-text'>{t('error_unexpected_with_dots')}</div>}
        {!isLoading &&
          !error &&
          allCoursesData?.data &&
          !allCoursesData?.data.length &&
          coursesByType?.archived &&
          !coursesByType?.archived?.length && <div className='emptyText'>{t('no_courses')}</div>}
        {!isLoading &&
          !error &&
          coursesByType?.not_acrhived &&
          coursesByType?.not_acrhived?.length > 0 && (
            <div className={cx('inner')}>
              {coursesByType?.not_acrhived.map(c => {
                const isSelected = courses.indexOf(c.id) !== -1
                return (
                  <CourseCard
                    className={cx('card')}
                    key={c.id}
                    imageUrl={c.image_path}
                    title={c.title}
                    TopAdornment={
                      <Checkbox
                        type='circle'
                        className={cx('checkbox')}
                        customChecked={isSelected}
                        onChange={() => handleSelectCourse(c.id)}
                      />
                    }
                  />
                )
              })}
            </div>
          )}
        {coursesByType?.archived?.length > 0 && (
          <>
            <h3>{t('commons:archive_courses')}</h3>
            <div className={cx('inner')}>
              {coursesByType?.archived.map(c => {
                const isSelected = courses.indexOf(c.id) !== -1

                return (
                  <CourseCard
                    className={cx('card')}
                    key={c.id}
                    imageUrl={c.image_path}
                    title={c.title}
                    TopAdornment={
                      <Checkbox
                        type='circle'
                        className={cx('checkbox')}
                        customChecked={isSelected}
                        onChange={() => handleSelectCourse(c.id)}
                      />
                    }
                  />
                )
              })}
            </div>
          </>
        )}
      </div>
      <div className={cx('buttonWrapper')}>
        <Button color='gray' onClick={handleClose}>
          {t('commons:cancel')}
        </Button>
        <Button onClick={handleClick} disabled={!courses.length}>
          {t('commons:choose')}
        </Button>
      </div>
    </Modal>
  )
}
