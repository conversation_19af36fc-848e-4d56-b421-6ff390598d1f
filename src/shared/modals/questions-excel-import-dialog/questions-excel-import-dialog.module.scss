@use "../../../shared/assets/styles/mixins/text";

.content {
  display: flex;
  flex-direction: column;
  padding: 10px 30px;
}

.dialog {
  max-width: 400px;
  width: 100%;
}

.form {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  overflow-y: hidden;
}

.title {
  @include text.title(30px);
  padding: 40px 0 !important;

  text-align: center;
}

.error-text {

  color: #ff8577;
  font-family: "TT Norms Pro";
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-top: 12px;
  text-align: center;
}

.field {
  margin-top: 20px;
  width: 100%;
}

.footer {
  display: flex;
  justify-content: center !important;
  padding: 30px 90px 40px !important;
}
