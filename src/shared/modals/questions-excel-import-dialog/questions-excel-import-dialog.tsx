import React, { useCallback } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './questions-excel-import-dialog.module.scss'

import { QuestionsExcelImportDialogProps } from './questions-excel-import-dialog.d'
import { Button } from '@/shared/ui'
import { FileUploader, Modal } from '@/shared/components'
import { useCreateQuestuinFromExcelMutation } from '@/store/services/module-service'
import { useTranslation } from 'react-i18next'
import { combineURLs } from '@/shared/helpers'
import { useConfig, useDownload } from '@/shared/hooks'
import {  } from '@/shared/hooks'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { handleErrorResponseAndTranslate } from '@/shared/contexts/notifications/helper'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__questions-excel-import'

const QuestionsExcelImportDialog: React.FC<QuestionsExcelImportDialogProps.Props> = props => {
  const { open, onClose, moduleId } = props

  const [download] = useDownload()
  const config = useConfig()

  const onTemplateClick = useCallback(
    // TODO use bucket
    () =>
      download(
        combineURLs(
          config?.url?.startsWith(`https://`) ? config?.url || '' : `https://` + config?.url || '',
          '/assets/legacy_files/quiz_template.xlsx',
        ),
        'quiz_template.xlsx',
      ),
    [config, download],
  )

  const [createQuestionFromExcel, { isLoading }] = useCreateQuestuinFromExcelMutation()

  const { t } = useTranslation(TRANSLATION_FILE)

  const {
    handleSubmit,
    formState: { errors, isValid },
    control,
    setError: setErrorForm,
    clearErrors,
  } = useForm<QuestionsExcelImportDialogProps.Inputs>({
    mode: 'onBlur',
  })

  const onSubmit: SubmitHandler<QuestionsExcelImportDialogProps.Inputs> = async data => {
    const formData = new FormData()

    formData.append('file', data.file[0])

    createQuestionFromExcel({
      id: moduleId,
      body: formData,
    })
      .unwrap()
      .then(() => {
        clearErrors()
        onClose()
      })
      .catch(e => {
        setErrorForm('file', { type: 'custom', message: handleErrorResponseAndTranslate(e) })
      })
  }

  return (
    <Modal key='questions-dialog' setActive={onClose} active={open} className={cx('dialog')}>
      <form onSubmit={handleSubmit(onSubmit)} className={cx('form')}>
        <h2 className={cx('title')}>{t('title')}</h2>
        <div className={cx('content')}>
          <Button size='veryBig' className={cx('button')} type='button' onClick={onTemplateClick}>
            {t('download_template')}
          </Button>

          <Controller
            control={control}
            name='file'
            rules={{ validate: value => value?.length > 0 }}
            render={({ field }) => (
              <FileUploader
                accept='application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                name='file'
                value={field.value}
                className={cx('field')}
                placeholder={t('file')}
                onChange={e => field.onChange(e.target.files)}
              />
            )}
          />
        </div>
        {errors.file?.message && <div className={cx('error-text')}>{errors.file?.message}</div>}
        <div className={cx('footer')}>
          <Button
            size='veryBig'
            className={cx('button', 'dialogFooter')}
            type='submit'
            loading={isLoading}
            disabled={!isValid || isLoading}
          >
            {t('import')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export default QuestionsExcelImportDialog
