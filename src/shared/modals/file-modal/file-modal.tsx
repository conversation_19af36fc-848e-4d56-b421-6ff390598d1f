/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react'
import styles from './file-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { FileModalProps } from './file-modal.d'
import { Modal } from '@/shared/components'
import { Button, Input, Select } from '@/shared/ui'
import { Controller, SubmitHandler } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const SELECT_LIST = [
  { id: 'PDF', title: 'pdf' },
  { id: 'DOCX', title: 'docx' },
  { id: 'XLSX', title: 'xlsx' },
]

const TRANSLATION_FILE = 'modals__file-modal'

export const FileModal: FC<FileModalProps.Props> = props => {
  const { className, form, isOpen, setIsOpen, onSubmitForm } = props
  const { t } = useTranslation(TRANSLATION_FILE)

  const onCancel = () => {
    form.reset()
    setIsOpen(() => false)
  }

  const onSubmit: SubmitHandler<FileModalProps.FileModalForm> = data => {
    if (onSubmitForm) onSubmitForm(data)

    form.reset()
    setIsOpen(() => false)
  }

  return (
    <Modal active={isOpen} setActive={v => setIsOpen(v)} className={cx('wrapper', className)}>
      <h2 className={cx('title')}>{t('title')}</h2>
      <section className={cx('form')}>
        <div>
          <Input
            error={form.formState.errors.name?.message && t('name_error')}
            register={form.register('name', {
              required: t('name_required'),
              pattern: {
                value: /\S+/,
                message: t('name_error'),
              },
              validate: (v: any) => !!v.trim(),
              minLength: 1,
            })}
            fullWidth
            label={t('name_label')}
            placeholder={t('name_placeholder')}
          />
        </div>
        <Controller
          name='type'
          control={form.control}
          render={({ field }) => (
            <Select
              placeholder=''
              label={t('type_label')}
              handleChange={v => {
                field.onChange(v.id)
              }}
              value={field.value}
              list={SELECT_LIST}
            />
          )}
        />
        <div className={cx('actions')}>
          <Button fullWidth color='gray' onClick={onCancel}>
            {t('cancel_button')}
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={!form.formState.isValid}
            fullWidth
            type='button'
            color='green'
          >
            {t('submit_button')}
          </Button>
        </div>
      </section>
    </Modal>
  )
}
