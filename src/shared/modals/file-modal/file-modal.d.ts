/* eslint-disable @typescript-eslint/no-explicit-any */
import { EAttachments } from "@/shared/types/store/phishing";

export declare namespace FileModalProps {
  interface FileModalForm {
    name: string;
    type: EAttachments;
  }

  interface Own {
    className?: string;
    isOpen: boolean;
    setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
    onSubmitForm?: (data: FileModalForm) => void;
    form: UseFormReturn<FileModalProps.FileModalForm, any>;
  }

  type Props = Own;
}

export {};
