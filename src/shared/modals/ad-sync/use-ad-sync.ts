import { useEffect, useState } from 'react'
import { adApi } from '@/entities/ad-service'
import { settingsApi } from '@/entities/settings'
import { useAppDispatch } from '@/store'

export type StepVariants = 2 | 1 | 3

const LOCAL_STORAGE_KEY = 'ADSyncId'

export const useADSyncModal = () => {
  const [step, setStep] = useState<StepVariants>(1)
  const [eventId, setEventId] = useState<string | null>()
  const [errorMessage, setErrorMessage] = useState<string | undefined>()
  const [uploading, setUploading] = useState<string | undefined>()
  const {
    data,
    error,
    isLoading: isEventLoading,
  } = adApi.useADSyncStatusQuery(eventId || '', {
    skip: !eventId || uploading === 'not_started' || uploading === 'presync_completed',
    pollingInterval: 5000,
  })

  const handleEventId = () => {
    const id = localStorage.getItem(LOCAL_STORAGE_KEY)
    setEventId(id)
  }

  const handleUploading = () => {
    setUploading('in_process')
  }

  const dispatch = useAppDispatch()

  useEffect(() => {
    handleEventId()
  }, [])

  const handleClear = (shoulClearUploading: boolean = true) => {
    dispatch(
      adApi.util.upsertQueryData('ADSyncStatus', eventId || '', {
        content: undefined,
        event_id: '',
        status: '',
      }),
    )
    localStorage.removeItem(LOCAL_STORAGE_KEY)
    shoulClearUploading && setUploading('')
    setEventId(null)
  }

  useEffect(() => {
    if (data?.content?.error && data?.content?.details?.info) {
      setErrorMessage(data?.content?.details?.info)
      handleClear()
    }
  }, [data?.content?.error])

  useEffect(() => {
    if (!!data?.content?.details?.info) {
      setUploading(data?.content?.details?.info)
    }

    if (data?.content?.details?.info === 'sync_completed') {
      handleClear(false)
    }
  }, [data?.status, data?.content?.details?.info])

  const { data: ldapData } = settingsApi.useGetLDAPSettingsQuery()

  const [adSync, { isLoading: adSyncFirstIsLoading, isError: adSyncFirstIsError }] =
    adApi.usePostADSyncMutation()

  const [adSyncApplyTarget, { isLoading: adSyncApplyIsLoading, isError: adSyncApplyIsError }] =
    adApi.useADSyncApplyMutation()

  const [isDeleteEmployees, setIsDeleteEmployees] = useState(false)
  const [isUpdateEmployees, setIsUpdateEmployees] = useState(true)

  const adSyncFirst = async () => {
    await adSync()
      .unwrap()
      .then(({ event_id }) => {
        setEventId(event_id)
        localStorage.setItem(LOCAL_STORAGE_KEY, event_id)
      })
  }

  const [openDeleteEmployeesId, setOpenDeleteEmployeesId] = useState(false)

  const confirmDelete = () => {
    if (
      (data?.content?.details?.departments.delete_in_db ||
        data?.content?.details?.employees.delete_count) &&
      isDeleteEmployees
    ) {
      setOpenDeleteEmployeesId(true)
    } else {
      adSyncApply()
    }
  }

  const adSyncApply = async () => {
    if (!data) return

    setStep(() => 3)

    await adSyncApplyTarget({
      params: {
        event_id: data?.event_id,
        delete: isDeleteEmployees,
        update: isUpdateEmployees,
      },
    })
      .unwrap()
      .then(() => {
        handleClear()
      })
  }

  return {
    eventId,
    uploading,
    adSyncFirst,
    adSyncFirstIsLoading: adSyncFirstIsLoading || isEventLoading || data?.status === 'in_process',
    adSyncFirstIsError: adSyncFirstIsError || !!error || !!errorMessage,
    handleUploading,
    setUploading,
    handleClear,
    step,
    isDeleteEmployees,
    setIsDeleteEmployees,
    isUpdateEmployees,
    setIsUpdateEmployees,
    adSyncApply,
    adSyncApplyIsLoading,
    adSyncApplyIsError,
    ldapData,
    setStep,
    adSyncData: data,
    openDeleteEmployeesId,
    setOpenDeleteEmployeesId,
    confirmDelete,
    errorMessage,
  }
}
