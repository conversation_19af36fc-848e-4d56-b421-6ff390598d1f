.first-step-wrapper {
  display: flex;
  flex-direction: column;

  align-items: center;

  width: 100%;

  .img {
    margin-bottom: 27px;
  }

  .title {
    color: var(--color-gray-90);
    font: var(--font-text-2-medium);

    margin: 0 auto 12px auto;

    text-align: center;
  }

  .text {
    margin-bottom: 27px;

    text-align: center;
  }

  .sync-btn {
    max-width: max-content;
    margin: 0 auto 25px auto;
  }

  .btns {
    margin-top: 25px;
  }
}

.modal-wrapper {
  height: auto;
  max-height: calc(100dvh - 64px);
  min-height: 500px;

  min-width: 624px;
  max-width: calc(100vw - 64px);
  width: 100%;

  overflow-y: auto;

  &.grid {
    display: grid;
    grid-template-rows: auto 1fr;
  }
}

.third-step-wrapper {
  display: grid;
  grid-template-rows: 1fr auto;

  align-content: space-between;
}

.status {
  color: var(--color-primary);
  font: var(--font-text-2-normal);
}

.modal {
  // align-items: center;
  // display: flex;
  // flex-direction: column;
  // gap: 28px;
  // justify-content: center;
  // max-height: calc(100% - 64px);
  // width: 624px;
  // max-width: calc(100vw - 64px);
  //
}

.success {
  display: flex;
  flex-direction: column;
  gap: 40px;
  height: 100%;
  padding: 20px;
}

.description {
  color: var(--color-gray-80);
  font: var(--font-text-2-normal);
  max-width: 240px;
  text-align: center;
  &__wrapper {
    margin-top: -12px;
  }
}

.filters {
  color: var(--color-gray-90);
  font: var(--font-text-2-medium);
}

.title {
  color: var(--color-gray-90);
  font: var(--font-title-4-medium);

  margin-bottom: 50px;

  &.third {
    margin-bottom: 0;
  }
}

.status-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
}

.img {
  height: 194px;

  vertical-align: middle;
}

.btns {
  display: flex;
  flex-direction: row;
  gap: 28px;
  margin-top: auto;
  width: 100%;

  gap: 16px;
  justify-content: end;
}

.second-step-wrapper {
  width: 100%;

  display: grid;
  grid-template-rows: repeat(2, auto);

  align-content: space-between;

  .btns {
    margin-top: 80px;
  }
}

.preview {
  display: grid;
  gap: 20px;
  grid-template-columns: 226px 1fr;
  align-items: flex-start;

  width: 100%;

  min-height: 250px;
  margin-bottom: 30px;

  padding: 0 24px;

  &__left {
    display: flex;
    flex-direction: column;
    gap: 12px;
    justify-content: center;
  }

  &__right {
    display: flex;
    flex-direction: column;
    gap: 20px;

    &__title {
      color: var(--color-gray-90);
      font: var(--font-text-2-medium);
      margin-bottom: 12px;
    }

    &__text {
      color: var(--color-gray-80);
      font: var(--font-text-2-medium);
      font-weight: 400;
      > span {
        margin-left: 2px;
      }
    }
  }
}

.sync {
  &__btn {
    align-items: center;
    display: flex;
    gap: 8px;
    justify-content: flex-start;
    padding: 2px;
  }

  &__text {
    color: var(--color-gray-80);
    font: var(--font-text-2-medium);
  }
}

.checkbox {
  &__wrapper {
    display: flex;
    gap: 8px;
  }

  &__text {
    color: var(--color-gray-80);
    font: var(--font-text-2-normal);
  }
}
.checkboxes {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 0 52px;
}

.error {
  color: #ff4b60;
  font: var(--font-text-2-medium);
  font-weight: 400;
  margin-bottom: 8px;
}

.strings {
  margin-top: 12px;
}
