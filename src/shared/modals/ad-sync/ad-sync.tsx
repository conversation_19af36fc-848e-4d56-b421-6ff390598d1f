/* eslint-disable react-refresh/only-export-components */
import { Dispatch, FC, ReactElement, SetStateAction, useEffect } from 'react'
import styles from './ad-sync.module.scss'
import classNamesBind from 'classnames/bind'
import { ADSyncModalProps } from './ad-sync.d'
import { StepVariants, useADSyncModal } from './use-ad-sync'
import { Modal } from '@/shared/components'
import { Button, Loader } from '@/shared/ui'
import RefreshIcon from '@/shared/ui/Icon/icons/components/RefreshIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import imgUrl from './ad.svg'
import { DeleteModal } from '../delete-modal'
import { Checkbox } from '@/shared/ui/checkbox'
import { useTranslation } from 'react-i18next'
import { TFunction } from 'i18next'

const cx = classNamesBind.bind(styles)

interface IStepProps {
  setActive?: Dispatch<SetStateAction<boolean>>
  context: ReturnType<typeof useADSyncModal>
  t: TFunction<'translation', undefined>
  onCancel?: (stayOpen: boolean) => void
}

const FirstStep = ({ context, t, onCancel }: IStepProps) => {
  const { adSyncFirst, adSyncFirstIsLoading, ldapData } = context

  return (
    <>
      <h3 className={cx('title')}>{t('active-directory')}</h3>
      <div className={cx('first-step-wrapper')}>
        <img src={imgUrl} className={cx('img')} />
        <div className={cx('title')}>{t('active-directory-filters')}</div>
        <div className={cx('text')}>
          <p className={cx('preview__right__text')}>
            {t('search-dn')}
            {ldapData?.search_dn ? ldapData.search_dn : t('commons:loading_with_dots')}
          </p>
          <p className={cx('preview__right__text')}>
            {t('user-search')}
            {ldapData?.filter_str ? ldapData.filter_str : t('commons:loading_with_dots')}
          </p>
        </div>
        <Button className={cx('sync-btn')} onClick={adSyncFirst}>
          {t('commons:synchronize')}
        </Button>

        {adSyncFirstIsLoading && <Loader loading size='56' />}

        <div className={cx('btns')}>
          <Button color='gray' onClick={() => onCancel && onCancel(false)}>
            {t('commons:cancel')}
          </Button>
          <Button disabled>{t('commons:apply')}</Button>
        </div>
      </div>
    </>
  )
}

const SecondStep = ({ context, t, onCancel }: IStepProps) => {
  const {
    adSyncFirst,
    adSyncFirstIsLoading,
    adSyncFirstIsError,
    isDeleteEmployees,
    isUpdateEmployees,
    setIsDeleteEmployees,
    setIsUpdateEmployees,
    ldapData,
    adSyncData,
    adSyncApply,
    openDeleteEmployeesId,
    setOpenDeleteEmployeesId,
    confirmDelete,
    errorMessage,
  } = context

  const { content } = adSyncData || {}
  const { employees, departments } = content?.details || {}
  const { create_count = 0, update_count = 0, delete_count = 0 } = employees || {}
  const { add_to_db = 0, delete_in_db = 0 } = departments || {}

  const employeesCount = delete_count
    ? t('commons:employee', {
        count: delete_count,
      })
    : ''

  const departmentsCount = delete_in_db
    ? t('commons:deps', {
        count: delete_in_db,
      })
    : ''

  return (
    <>
      <h3 className={cx('title')}>{t('active-directory')}</h3>
      <div className={cx('second-step-wrapper')}>
        <div>
          <div className={cx('preview')}>
            <div className={cx('preview__left')}>
              <img src={imgUrl} className={cx('img')} />
              {adSyncFirstIsLoading ? (
                <Loader loading />
              ) : (
                <Button
                  onClick={() => adSyncFirst()}
                  size='small'
                  className={cx('sync__btn')}
                  color='gray'
                >
                  <IconWrapper size='16'>
                    <RefreshIcon />
                  </IconWrapper>
                  <p className={cx('sync__text')}>{t('commons:re-syncing')}</p>
                </Button>
              )}
            </div>

            <div className={cx('preview__right')}>
              <div>
                <h3 className={cx('preview__right__title')}>{t('active-directory-filters')}</h3>
                <p className={cx('preview__right__text')}>
                  {t('search-dn')}
                  {ldapData?.search_dn ? ldapData.search_dn : t('commons:loading_with_dots')}
                </p>
                <p className={cx('preview__right__text')}>
                  {t('user-search')}
                  {ldapData?.filter_str ? ldapData.filter_str : t('commons:loading_with_dots')}
                </p>
              </div>

              {adSyncFirstIsError ? (
                <div>
                  <p className={`error-text ${cx('error')}`}>{t('synchronize-error')}</p>
                  {errorMessage && <p className={`error-text ${cx('error')}`}>{errorMessage}</p>}
                </div>
              ) : (
                <>
                  <div>
                    <h3 className={cx('preview__right__title')}>{t('employees-data')}</h3>
                    <div className={cx('preview__right__text')}>
                      {t('employees_added')}
                      <span>{create_count}</span>
                    </div>
                    {isUpdateEmployees && (
                      <div className={cx('preview__right__text')}>
                        {t('employees_updated')}
                        <span>{update_count}</span>
                      </div>
                    )}
                    {isDeleteEmployees && (
                      <div className={cx('preview__right__text', 'error')}>
                        {t('employees_deleted')}
                        <span>{delete_count}</span>
                      </div>
                    )}
                  </div>
                  <div>
                    <div className={cx('preview__right__text')}>
                      {t('departments_added')}
                      <span>{add_to_db}</span>
                    </div>
                    {isDeleteEmployees && (
                      <div className={cx('preview__right__text', 'error')}>
                        {t('departments_deleted')}
                        <span>{delete_in_db}</span>
                      </div>
                    )}
                  </div>
                </>
              )}
            </div>
          </div>

          {!adSyncFirstIsError && (
            <div className={cx('checkboxes')}>
              <div className={cx('checkbox__wrapper')}>
                <Checkbox
                  initialChecked={isDeleteEmployees}
                  onChange={v => setIsDeleteEmployees(v)}
                  label={
                    <span className={cx('checkbox__text')}>
                      {t('delete_employees_departments')}
                    </span>
                  }
                />
              </div>
              <div className={cx('checkbox__wrapper')}>
                <Checkbox
                  initialChecked={isUpdateEmployees}
                  onChange={v => setIsUpdateEmployees(v)}
                  label={<span className={cx('checkbox__text')}>{t('data_update')}</span>}
                />
              </div>
            </div>
          )}
        </div>

        <div className={cx('btns')}>
          <Button color='gray' onClick={() => onCancel && onCancel(false)}>
            {t('commons:cancel')}
          </Button>
          <Button disabled={adSyncFirstIsError || adSyncFirstIsLoading} onClick={confirmDelete}>
            {t('commons:apply')}
          </Button>
        </div>

        <DeleteModal
          title={t('commons:confirmation')}
          text={
            !!employeesCount && !!departmentsCount
              ? t('delete_employees_and_deps_after_add', {
                  employeesCount: employeesCount,
                  departmentsCount: departmentsCount,
                })
              : t('delete_employees_after_add', {
                  employeesCount: employeesCount,
                })
          }
          onClose={adSyncApply}
          active={openDeleteEmployeesId}
          setActive={setOpenDeleteEmployeesId}
        />
      </div>
    </>
  )
}

const ThirdStep = ({ setActive, context, t }: IStepProps) => {
  const { adSyncApplyIsLoading, adSyncApplyIsError } = context

  const statusText = adSyncApplyIsError
    ? t('commons:error_without_dots')
    : adSyncApplyIsLoading
      ? t('commons:syncing')
      : t('commons:syncing_finished')

  return (
    <>
      <h3 className={cx('title', 'third')}>{t('active-directory')}</h3>
      <div className={cx('third-step-wrapper')}>
        <div className={cx('status-wrapper')}>
          <div>
            <Loader
              size='56'
              loading={adSyncApplyIsLoading}
              error={adSyncApplyIsError}
              success={!adSyncApplyIsLoading && !adSyncApplyIsError}
            />
            <div
              className={cx('status', {
                error: adSyncApplyIsError,
              })}
            >
              {statusText}
            </div>
          </div>
        </div>

        <div className={cx('btns')}>
          <Button disabled={adSyncApplyIsLoading} onClick={() => setActive && setActive(false)}>
            {t('commons:finish')}
          </Button>
        </div>
      </div>
    </>
  )
}

export const STEPS_MAP: Record<StepVariants, ({ setActive, context }: IStepProps) => ReactElement> =
  {
    1: FirstStep,
    2: SecondStep,
    3: ThirdStep,
  }

export const ADSyncModal: FC<ADSyncModalProps.Props> = props => {
  const { active, setActive } = props
  const context = useADSyncModal()
  const { t } = useTranslation('modals__ad-sync')
  const {
    step,
    setStep,
    eventId,
    uploading,
    adSyncData,
    adSyncFirstIsError,
    setUploading,
    handleClear,
  } = context
  const onClose = () => {
    if (step === 2 && uploading === 'presync_completed') {
      setUploading('')
    }
    setActive(false)
  }

  const onCancel = (stayOpen: boolean = false) => {
    handleClear()
    !stayOpen && onClose()
  }
  const stepProps = step == 2 ? { context, t, onCancel } : { setActive, context, t, onCancel }

  useEffect(() => {
    if (adSyncData && eventId && adSyncData.status === 'success') {
      setStep(1)
    }

    if (!adSyncData && eventId) {
      setStep(1)
    }

    if (eventId && (uploading === 'in_process' || uploading === 'presync_completed')) {
      setStep(2)
    }
  }, [adSyncData, eventId, adSyncData?.status, uploading])

  useEffect(() => {
    if (!!adSyncFirstIsError) {
      setStep(2)
    }
  }, [adSyncFirstIsError])

  return (
    <Modal key={step} className={cx('modal')} active={active} setActive={setActive}>
      <div
        className={cx('modal-wrapper', {
          grid: step == 3,
        })}
      >
        {STEPS_MAP[step](stepProps)}
      </div>
    </Modal>
  )
}
