@use '../../../shared/assets/styles/mixins/text';

.dialog {
  display: flex;
  flex-direction: column;
  max-width: 630px;
  min-height: 600px;
  padding: 24px;
  padding-bottom: 20px;
  width: 100%;
}

.title {
  font: var(--font-title-3-medium);
  margin-bottom: 12px;
}

.buttons {
  color: var(--color-gray-80);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.button {
  height: 24px;
  padding: 4px 8px;
  color: var(--color-gray-80);
  font: var(--font-caption-1-normal);
  border-radius: 24px;

  &.active {
    color: var(--white);

    &.riskLevelButton {
      background-color: #a259ff;
    }

    &.phishingButton {
      background-color: #ffc700;
    }

    &.learningButton {
      background-color: var(--color-statistics-good);
    }
  }
}

.riskLevelButton {
  border: 1px solid #a259ff;
}

.phishingButton {
  border: 1px solid #ffc700;
}

.learningButton {
  border: 1px solid var(--color-statistics-good);
}

.formula {
  margin-bottom: 16px;
  font: var(--font-title-3-medium);
}

.green {
  color: var(--color-statistics-good);
}

.yellow {
  color: #ffc700;
}

.purple {
  color: #a259ff;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item {
  font: var(--font-text-2-normal);

  span {
    font-weight: 500;
  }
}

.subtitle {
  margin-top: 24px;
  font: var(--font-text-2-normal);
}
