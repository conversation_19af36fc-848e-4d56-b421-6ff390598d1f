/* eslint-disable i18n/no-russian-character */
/* eslint-disable i18next/no-literal-string */
import React, { useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './risk-level-modal.module.scss'

import { RiskLevelModalProps } from './risk-level-modal.d'
import { Modal } from '@/shared/components'
import { Trans, useTranslation } from 'react-i18next'
import { TFunction } from 'i18next'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__risk-level-modal'

type Tabs = 'riskLevel' | 'phishing' | 'learning'

type Props = {
  t: TFunction
}

const RiskLevelContent: React.FC<Props> = ({ t }) => {
  return (
    <div className={cx('wrapper')}>
      <div className={cx('formula')}>
        <span className={cx('purple')}>R</span> = 10 * ( 1 -- ( a *{' '}
        <span className={cx('yellow')}>Ф</span> + ( 1 -- a ) *{' '}
        <span className={cx('green')}>Е</span> ))
      </div>
      <div className={cx('list')}>
        <div className={cx('item')}>
          <span>a</span> = 0,7 --- {t('risk_level_points.first')}
        </div>
        <div className={cx('item')}>
          <span className={cx('yellow')}>Ф</span> --- {t('risk_level_points.second')}
        </div>
        <div className={cx('item')}>
          <span className={cx('green')}>Е</span> --- {t('risk_level_points.third')}
        </div>
      </div>
      <div className={cx('subtitle')}>
        <Trans
          i18nKey='risk_level_subtitle'
          t={t}
          components={{
            green: <span className={cx('green')} />,
            yellow: <span className={cx('yellow')} />,
          }}
        />
      </div>
    </div>
  )
}

const PhishingContent: React.FC<Props> = ({ t }) => {
  return (
    <div className={cx('wrapper')}>
      <div className={cx('formula')}>
        <span className={cx('yellow')}>Ф</span> = max (0,( V_1 / V -- b * ( F_2 / F ) -- c * ( F_3 /
        F ) -- d *( W_1 / W ) )
      </div>
      <div className={cx('list')}>
        <div className={cx('item')}>
          <span>b</span> = 0,5 --- {t('phishing_points.first')}
        </div>
        <div className={cx('item')}>
          <span>c</span> = 0,75 --- {t('phishing_points.second')}
        </div>
        <div className={cx('item')}>
          <span>d</span> = 0,8 --- {t('phishing_points.third')}
        </div>
        <div className={cx('item')}>
          <span>V_1</span> --- {t('phishing_points.fourth')}
        </div>
        <div className={cx('item')}>
          <span>V</span> --- {t('phishing_points.fifth')}
        </div>
        <div className={cx('item')}>
          <span>F</span> --- {t('phishing_points.sixth')}
        </div>
        <div className={cx('item')}>
          <span>F_2</span> --- {t('phishing_points.seventh')}
        </div>
        <div className={cx('item')}>
          <span>F_3</span> --- {t('phishing_points.eighth')}
        </div>
        <div className={cx('item')}>
          <span>W</span> --- {t('phishing_points.ninth')}
        </div>
        <div className={cx('item')}>
          <span>W_1</span> --- {t('phishing_points.tenth')}
        </div>
      </div>
    </div>
  )
}

const LearningContent: React.FC<Props> = ({ t }) => {
  return (
    <div className={cx('wrapper')}>
      <div className={cx('formula')}>
        <span className={cx('green')}>E</span> = SUM (T_i) / T_max
      </div>
      <div className={cx('formula')}>T_max = SUM (y_i)</div>
      <div className={cx('formula')}>T_i = d * max ( ( 1 - k * m ), 0,5 ) * y_i</div>
      <div className={cx('list')}>
        <div className={cx('item')}>
          <span>d</span> --- {t('learning_points.first')}
        </div>
        <div className={cx('item')}>
          <span>k</span> --- {t('learning_points.second')}
        </div>
        <div className={cx('item')}>
          <span>m</span> = 0,1 --- {t('learning_points.third')}
        </div>
        <div className={cx('item')}>
          <span>y_i</span> = 1 --- {t('learning_points.fourth')}
        </div>
      </div>
    </div>
  )
}

const RiskLevelModal: React.FC<RiskLevelModalProps.Props> = props => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const { open, setOpen } = props
  const [activeTab, setActiveTab] = useState<Tabs>('riskLevel')

  const content = {
    riskLevel: <RiskLevelContent t={t} />,
    phishing: <PhishingContent t={t} />,
    learning: <LearningContent t={t} />,
  }

  return (
    <Modal setActive={v => setOpen(Boolean(v))} active={open} className={cx('dialog')}>
      <h2 className={cx('title')}>{t('title')}</h2>
      <div className={cx('buttons')}>
        <button
          className={cx('riskLevelButton', 'button', {
            active: activeTab === 'riskLevel',
          })}
          onClick={() => setActiveTab('riskLevel')}
        >
          {t('risk_level')}
        </button>
        =
        <button
          className={cx('phishingButton', 'button', {
            active: activeTab === 'phishing',
          })}
          onClick={() => setActiveTab('phishing')}
        >
          {t('phishing')}
        </button>
        +
        <button
          className={cx('learningButton', 'button', {
            active: activeTab === 'learning',
          })}
          onClick={() => setActiveTab('learning')}
        >
          {t('learning')}
        </button>
      </div>
      {content[activeTab]}
    </Modal>
  )
}

export default RiskLevelModal
