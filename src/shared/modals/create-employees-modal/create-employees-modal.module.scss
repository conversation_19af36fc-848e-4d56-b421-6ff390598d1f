.modal-wrapper {
  width: 656px;
}

.title {

  color: #343b54;
  font-family: "TT Norms Pro";
  font-size: 18px;
  line-height: 24px;
  margin-bottom: 24px;
}

.grid {
  display: grid;
  grid-gap: 4px 32px;
  grid-template-columns: repeat(2, 1fr);
  .input {
    margin-bottom: 28px;
  }
  .double {
    grid-column: 1/3;
  }
  .errorInput {
    margin-bottom: 0;
  }
  .button {
    grid-column: 2;
    justify-self: end;
  }
}

.error-text {

  color: #ff8577;
  font-family: "TT Norms Pro";
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  grid-column: 1/3;
  line-height: 16px;
  text-align: center;
}

.checkboxInner {
  align-items: center;
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  &:last-child {
    margin-bottom: 0;
  }
  span {
    color: var(--color-gray-80, #5c6585);
    font: var(--font-text-2-normal);
  }
}
