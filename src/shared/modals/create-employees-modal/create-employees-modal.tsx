/* eslint-disable no-useless-escape */
import React, { RefObject, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { Submit<PERSON><PERSON><PERSON>, useForm, Controller } from 'react-hook-form'
import classNamesBind from 'classnames/bind'

import { useEmployees } from '@/entities/employee'
import { Modal } from '@/shared/components'
import { IDepartment } from 'entities/department'
import { tagsEmployeesApi } from '@/store/services/tags-employees-service'
import { IListItem, Button, Input, Loader, Select, Checkbox } from '@/shared/ui'
import { ERole } from '@/shared/types/enums'

import styles from './create-employees-modal.module.scss'
import { CreateEmployeeModalProps } from './create-employees-modal.d'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useGetDepartmentsTitlesQuery } from '@/entities/department/model/endpoints'

const cx = classNamesBind.bind(styles)

export interface EmployeeCreate {
  email: string
  middle_name: string
  first_name: string
  last_name: string
  position: string
  department: IListItem | null
  role: ERole | null
  need_message?: boolean
}

type Inputs = EmployeeCreate

const DEPARTMENTS_LIMIT = 10
const DEPARTMENTS_LIST_ID = 'add-employee-departments-list'

export const CreateEmployeeModal: React.FC<CreateEmployeeModalProps.Props> = props => {
  const { active, className, setActive, refetch } = props

  const [departmentsPage, setDepartmentsPage] = useState(1)
  const [departmentsList, setDepartmentsList] = useState<IDepartment[]>([])
  const [departmentsTotalCount, setDepartmentsTotalCount] = useState(0)
  const [departmentsSearch, setDepartmentsSearch] = useState('')

  const {
    data: departments,
    isLoading: isLoadingDepartments,
    isFetching: isFetchingDepartments,
  } = useGetDepartmentsTitlesQuery(
    {
      limit: DEPARTMENTS_LIMIT,
      offset: (departmentsPage - 1) * DEPARTMENTS_LIMIT,
      search: departmentsSearch || undefined,
    },
    {
      skip: !active,
    },
  )

  useEffect(() => {
    if (!departments) return

    if (departmentsPage === 1) {
      setDepartmentsList(departments.data)
    } else {
      setDepartmentsList(prev => [...prev, ...departments.data])
    }

    setDepartmentsTotalCount(departments.total_count)
  }, [departments])

  const departmentsHasMore = departmentsPage * DEPARTMENTS_LIMIT < departmentsTotalCount

  const renderListInnerWrapper = useCallback(
    (children: React.ReactNode) => (
      <InfiniteScroll
        dataLength={departmentsList.length}
        next={() => setDepartmentsPage(prev => prev + 1)}
        hasMore={departmentsHasMore}
        loader={<Loader size='28' loading />}
        scrollableTarget={DEPARTMENTS_LIST_ID}
      >
        {children}
      </InfiniteScroll>
    ),
    [departmentsHasMore, departmentsList.length],
  )

  const handleChangeDepartmentSearch = useCallback((search: string) => {
    setDepartmentsPage(1)
    setDepartmentsSearch(search)
  }, [])

  const { setPage } = useEmployees()

  const buttonRef: RefObject<HTMLButtonElement> = useRef(null)

  const { t } = useTranslation('modals__create-employees-modal')

  const {
    setValue,
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    control,
    setError: setErrorForm,
  } = useForm<Inputs>({
    mode: 'all',
    defaultValues: {
      email: '',
      middle_name: '',
      first_name: '',
      last_name: '',
      position: '',
      department: null,
      role: null,
      need_message: false,
    },
  })

  const [createEmployee, { isLoading }] = tagsEmployeesApi.useCreateEmployeeMutation()

  const [error, setError] = useState('')

  const onSubmit: SubmitHandler<Inputs> = async formData => {
    if (!formData) return

    try {
      await createEmployee(formData)
        .unwrap()
        .then(() => {
          reset()
          refetch && refetch()
          setError('')
          setPage(0)
          setActive(false)
        })
        .catch(error => setError(error.data.message))
    } catch (error) {
      setError(t('commons:error_unexpected'))
    }
  }

  const handleChangeDepartment = (d: IListItem) => setValue('department', d)
  const handleChangeRole = (r: IListItem) => setValue('role', r.id as ERole)

  const handleTrim = (str: string, name: keyof Inputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  const handleEnterPress = (e: KeyboardEvent) => {
    if (e.code === 'Enter' && buttonRef.current) {
      buttonRef.current.click()
    }
  }

  useEffect(() => {
    document.addEventListener('keyup', handleEnterPress)

    return () => document.removeEventListener('keyup', handleEnterPress)
  }, [])

  const employeeList = useMemo(() => {
    return [
      {
        id: 'operator',
        title: t('commons:operator'),
      },
      {
        id: 'content_manager',
        title: t('commons:content_manager'),
      },
      {
        id: 'employee',
        title: t('commons:employee_role'),
      },
    ] as { id: ERole; title: string }[]
  }, [t])

  const departmentsSelectList = useMemo(
    () =>
      departmentsList?.map(d => ({
        title: d.title,
        id: d.id,
      })) ?? [],
    [departmentsList],
  )

  return (
    <Modal setActive={setActive} active={active} className={cx('modal-wrapper', className)}>
      {!departments && <Loader className={cx('loader')} size='56' />}
      {departments && (
        <form onSubmit={handleSubmit(onSubmit)} className={cx('wrapper')}>
          <div className={cx('title')}>{t('title')}</div>
          <div className={cx('grid')}>
            <Input
              placeholder={t('commons:email')}
              label={t('commons:email')}
              className={cx('input', {
                errorInput: errors.email?.message,
              })}
              error={errors.email?.message}
              register={register('email', {
                required: t('commons:required_field'),
                pattern: {
                  value: /^([+A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,10})$/,
                  message: t('email_pattern'),
                },
              })}
              fullWidth
              required
              onBlur={e => handleTrim(e.target.value, 'email')}
            />
            <Input
              placeholder={t('commons:lastname')}
              label={t('commons:lastname')}
              className={cx('input', {
                errorInput: errors.last_name?.message,
              })}
              error={errors.last_name?.message}
              fullWidth
              register={register('last_name')}
              onBlur={e => handleTrim(e.target.value, 'last_name')}
            />
            <Input
              placeholder={t('commons:firstname')}
              label={t('commons:firstname')}
              className={cx('input', {
                errorInput: errors.first_name?.message,
              })}
              error={errors.first_name?.message}
              fullWidth
              register={register('first_name')}
              onBlur={e => handleTrim(e.target.value, 'first_name')}
            />
            <Input
              placeholder={t('commons:middlename')}
              label={t('commons:middlename')}
              className={cx('input', {
                errorInput: errors.middle_name?.message,
              })}
              error={errors.middle_name?.message}
              fullWidth
              register={register('middle_name')}
              onBlur={e => handleTrim(e.target.value, 'middle_name')}
            />
            <Select
              label={t('commons:role')}
              placeholder={t('commons:role')}
              className={cx('input')}
              // eslint-disable-next-line i18next/no-literal-string
              value='employee'
              list={employeeList}
              handleChange={handleChangeRole}
            />
            {departmentsList && (
              <Select
                remoteSearch
                loading={isLoadingDepartments || isFetchingDepartments}
                label={t('commons:department')}
                placeholder={t('commons:department')}
                className={cx('input')}
                list={departmentsSelectList}
                handleChange={handleChangeDepartment}
                renderListInnerWrapper={renderListInnerWrapper}
                listWrapperId={DEPARTMENTS_LIST_ID}
                handleChangeSearch={handleChangeDepartmentSearch}
              />
            )}
            <Input
              label={t('commons:position')}
              placeholder={t('commons:position')}
              classNameWrapper={cx('input', 'double')}
              error={errors.position?.message}
              fullWidth
              register={register('position', {
                maxLength: {
                  value: 255,
                  message: t('position_length_limit'),
                },
              })}
              onBlur={e => handleTrim(e.target.value, 'position')}
            />
            <div className={cx('checkboxInner', 'double')}>
              <Controller
                control={control}
                name='need_message'
                render={({ field: { value, onChange } }) => (
                  <Checkbox
                    initialChecked={value}
                    onChange={onChange}
                    label={<span>{t('send_registration_letter')}</span>}
                  />
                )}
              />
            </div>
            <Button
              ref={buttonRef}
              type='submit'
              color='green'
              size='big'
              className={cx('double', 'button')}
              disabled={!isValid}
              loading={isLoading}
            >
              {t('commons:add')}
            </Button>
            {error && <div className={cx('error-text')}>{error}</div>}
          </div>
        </form>
      )}
    </Modal>
  )
}
