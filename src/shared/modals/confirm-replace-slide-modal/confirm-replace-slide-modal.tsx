import React, { useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './confirm-replace-slide-modal.module.scss'

import { ConfirmReplaceSlideProps } from './confirm-replace-slide-modal.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const ConfirmReplaceSlideModal: React.FC<ConfirmReplaceSlideProps.Props> = props => {
  const { t } = useTranslation('modals__confirm-replace-slide-modal')
  const [loading, setLoading] = useState(false)

  const _onConfirm = async () => {
    setLoading(true)
    await props.onConfirm()
    setLoading(false)
  }

  const _onClose = () => props.onClose()

  return (
    <Modal
      key='delete-dialog'
      setActive={_onClose}
      active={!!props.active}
      className={cx('dialog')}
    >
      <h2 className={cx('title')}>{t('title')}</h2>

      <div className={cx('footer')}>
        <Button
          size='veryBig'
          onClick={_onClose}
          className={cx('button', 'dialogFooter')}
          type='button'
          color='red'
        >
          {t('commons:cancel')}
        </Button>
        <Button
          size='veryBig'
          className={cx('button', 'dialogFooter')}
          type='button'
          loading={loading}
          onClick={_onConfirm}
          color='green'
        >
          {t('confirm')}
        </Button>
      </div>
    </Modal>
  )
}

export default ConfirmReplaceSlideModal
