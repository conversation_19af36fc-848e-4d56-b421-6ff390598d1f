import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './delete-confirm-modal.module.scss'

import { DeleteConfirmModalProps } from './delete-confirm-modal.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const DeleteConfirmModal: React.FC<DeleteConfirmModalProps.Props> = props => {
  const { open, setOpen, onConfirm, title, description } = props
  const { t } = useTranslation()

  return (
    <Modal setActive={v => setOpen(Boolean(v))} active={open} className={cx('dialog')}>
      <h2 className={cx('title')}>{title}</h2>
      <p className={cx('description')}>{description}</p>

      <div className={cx('footer')}>
        <Button
          size='veryBig'
          onClick={() => {
            setOpen(false)
          }}
          className={cx('button', 'dialogFooter')}
          type='button'
          color='gray'
        >
          {t('commons:cancel')}
        </Button>
        <Button
          size='veryBig'
          className={cx('button', 'dialogFooter')}
          type='button'
          onClick={() => {
            setOpen(false)
            onConfirm?.()
          }}
          color='red'
        >
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}

export default DeleteConfirmModal
