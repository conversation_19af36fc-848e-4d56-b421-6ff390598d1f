import { ButtonProps } from '../../ui'

export declare namespace DeleteConfirmProps {
  interface Own {
    title: string
    description?: string
    onClose?: () => void
    onConfirm?: () => void
    open: boolean
    setOpen: (v: boolean) => void
    closeText?: string
    confirmText?: string
    wrapperClassname?: string
    confirmProps?: Partial<ButtonProps.Own>
    closeProps?: Partial<ButtonProps.Own>
    footerClassname?: string
  }

  type Props = Own
}

export {}
