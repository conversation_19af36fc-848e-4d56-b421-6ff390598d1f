import React from 'react'
import classNamesBind from 'classnames/bind'
import styles from './confirm-modal.module.scss'

import { DeleteConfirmProps } from './confirm-modal.d'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const ConfirmModal: React.FC<DeleteConfirmProps.Props> = props => {
  const { t } = useTranslation()
  const {
    title,
    open,
    setOpen,
    onClose,
    onConfirm,
    closeText,
    description,
    confirmText,
    wrapperClassname,
    confirmProps,
    closeProps,
    footerClassname,
  } = props

  return (
    <Modal
      setActive={v => setOpen(Boolean(v))}
      active={open}
      className={cx('dialog', wrapperClassname)}
    >
      <h2 className={cx('title')}>{title}</h2>
      {description && <p className={cx('description')}>{description}</p>}

      <div className={cx('footer', footerClassname)}>
        <Button
          size='veryBig'
          onClick={() => {
            setOpen(false)
            onClose?.()
          }}
          type='button'
          {...confirmProps}
          className={cx('button', 'dialogFooter', confirmProps?.className)}
        >
          {confirmText ?? t('commons:cancel')}
        </Button>
        <Button
          size='veryBig'
          type='button'
          onClick={() => {
            setOpen(false)
            onConfirm?.()
          }}
          color='red'
          {...closeProps}
          className={cx('button', 'dialogFooter', closeProps?.className)}
        >
          {closeText ?? t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}

export default ConfirmModal
