import { Loader } from '@/shared/ui'
import { Modal } from '@/shared/components'
import styles from './report-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ReactNode } from 'react'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  setOpen: (v: boolean) => void
  title: string
  error: boolean
  isLoading: boolean
  data: unknown
  loaderTitle: ReactNode
}

export const ReportViewModal = ({
  open,
  setOpen,
  data,
  error,
  isLoading,
  loaderTitle,
  title,
}: Props) => {
  return (
    <Modal active={open} setActive={v => setOpen(!!v)} className={cx('wrapper')}>
      <div className={cx('title')}>{title}</div>
      <div className={cx('loaderWrapper')}>
        <Loader
          size='56'
          error={!!error}
          success={!!(!isLoading && !error && data !== undefined)}
          loading={isLoading}
        />
        <div
          className={cx('loaderTitle', {
            success: !error && !isLoading && data !== undefined,
            error: error,
          })}
        >
          {loaderTitle}
        </div>
      </div>
    </Modal>
  )
}
