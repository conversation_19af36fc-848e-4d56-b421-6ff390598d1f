/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useEffect, useRef, useState } from 'react'
import styles from './report-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { ReportModalProps, TReportType } from './report-modal.d'
import { <PERSON><PERSON>, <PERSON>ader } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { IReportStatus } from '@/shared/types/store/reports'
import { delay } from '@/shared/helpers/delay'
import { reportServiceApi } from '@/entities/reports'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'modals__report-modal'

export const ReportModal: FC<ReportModalProps.Props> = props => {
  const { title = 'Export', type, open, setOpen, objectID, customSuccessText } = props
  const scormReport = ['scorm', 'certificateScorm']
  const [isLoading, setIsLoading] = useState(false)
  const [isFetched, setIsFetched] = useState(false)
  const isUnmounted = useRef(false)
  const { t } = useTranslation(TRANSLATION_FILE)

  const urlPost: Record<TReportType, (id?: UUID) => string> = {
    PDF: id => `reports/pdf${id ? `?organization_id=${id}&` : ''}`,
    subOrganizationPDF: id => `reports/pdf${id ? `?organization_id=${id}&` : ''}`,
    progress: id => `reports/progress${id ? `?organization_id=${id}&` : ''}`,
    attempt: id => `reports/attempt/${id}`,
    attemptScorm: id => `reports/attempt/${id}/scorm`,
    course: id => `reports/course/${id}`,
    scorm: id => `reports/course/${id}`,
    courses: id => `reports/courses/${id}`,
    phishing: id => `reports/phishing/${id}`,
    certificate: id => `reports/pdf/certificate/${id}`,
    certificateScorm: id => `reports/pdf/certificate/${id}`,
    certificateCourseV2: id => `reports/pdf/certificate/${id}/v2`,
  }

  const isCertificate = type === 'certificate' || type === 'certificateScorm'
  const isComeToMail = type === 'PDF' || isCertificate

  const urlGet = (id: UUID) => (type === 'PDF' ? `reports/pdf/${id}` : `reports/${id}`)

  const [getReportTrigger] = reportServiceApi.useLazyGetReportWithReportModalQuery()
  const [postReportTrigger] = reportServiceApi.usePostReportWithReportModalMutation()

  const fetchReport = async () => {
    try {
      setIsLoading(_ => true)
      const data = await postReportTrigger({
        url: urlPost[type](objectID ? objectID : ''),
        params: scormReport.includes(type) ? { type: 'scorm' } : null,
      }).unwrap()

      if (data === null) {
        return setData(
          _ =>
            ({
              withoutUrl: true,
            }) as IReportStatus,
        )
      }

      const { id: reportID, status, url } = data || {}

      if (isCertificate) {
        return setData(_ => ({}) as IReportStatus)
      }

      if (type === 'PDF' && (status === 'in_progress' || status === 'complete')) {
        return setData(_ => data)
      }

      if (url && status === 'complete') {
        setData(_ => data)
      } else {
        let delayCount = 5
        let data: IReportStatus
        const TICKS = 1_000

        for (let i = 0; i < TICKS; i++) {
          data = await getReportTrigger(urlGet(reportID)).unwrap()

          if (isUnmounted?.current) {
            i = Number.MAX_VALUE
            break
          }

          const { id: _, status, url } = data

          if (status === 'complete' && url) {
            setError(_ => '')
            setData(_ => data)
            break
          }

          if (status === 'error') {
            setError(_ => t('unknown_error'))
            break
          }

          await delay(delayCount * 1000)

          if (i === 3) {
            delayCount = 4
          } else if (i === 8) {
            delayCount = 3
          } else if (i === 15) {
            delayCount = 2
          }
        }

        setError(_ => {
          if ((!data || data.status === 'in_progress') && !_) {
            return t('time_error')
          }

          return _
        })
      }
    } catch (error) {
      setError(_ => t('unknown_error'))
    } finally {
      setIsFetched(_ => true)
      setIsLoading(_ => false)
    }
  }

  useEffect(() => {
    if (isLoading || isFetched) return

    fetchReport()
  }, [isLoading])

  useEffect(() => {
    return () => {
      isUnmounted.current = true
    }
  }, [])

  const [error, setError] = useState('')
  const [data, setData] = useState<IReportStatus | null>(null)

  const getLoaderTitle = () => {
    if (isLoading) {
      return t('report_generate')
    }

    if (data) {
      if (customSuccessText) return customSuccessText

      if (isComeToMail) {
        return `${isCertificate ? t('certificate') : t('report')} ${t('will_be_sent')}`
      }
      return t('report_generated')
    }

    return error || t('error')
  }

  const loaderTitle = getLoaderTitle()

  return (
    <Modal active={open} setActive={setOpen} className={cx('wrapper')}>
      <div className={cx('title')}>{title}</div>
      <div className={cx('loaderWrapper')}>
        <Loader size='56' error={!!error} success={!!(!isLoading && data)} loading={isLoading} />
        <div
          className={cx('loaderTitle', {
            success: data,
            error: !data && error,
          })}
        >
          {loaderTitle}
        </div>
      </div>
      <div
        className={cx('buttonWrapper', {
          full: isComeToMail || data?.withoutUrl,
        })}
      >
        <Button color='gray' size='big' fullWidth onClick={() => setOpen(false)}>
          {isComeToMail ? t('close') : t('cancel')}
        </Button>
        {type !== 'PDF' &&
          type !== 'certificate' &&
          type !== 'certificateScorm' &&
          !data?.withoutUrl && (
            <a href={data?.url || ''} target='_blank' rel='noreferrer'>
              <Button size='big' fullWidth disabled={isLoading || !data}>
                {t('download')}
              </Button>
            </a>
          )}
      </div>
    </Modal>
  )
}
