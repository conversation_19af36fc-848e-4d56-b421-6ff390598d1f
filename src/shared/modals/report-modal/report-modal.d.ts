export type TReportType =
  | 'PDF'
  | 'subOrganizationPDF'
  | 'progress'
  | 'attempt'
  | 'attemptScorm'
  | 'course'
  | 'courses'
  | 'phishing'
  | 'certificate'
  | 'certificateScorm'
  | 'scorm'
  | 'certificateCourseV2'

type UUID = string;

export declare namespace ReportModalProps {
  interface Own {
    className?: string;
    customSuccessText?: string;
    title?: string;
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;

    type: TReportType;
    objectID?: UUID;
  }

  type Props = Own;
}

export {};
