/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useEffect, useState } from 'react'
import classNamesBind from 'classnames/bind'

import { ReportModalProps, TReportType } from './report-modal.d'
import { reportServiceApi } from '@/entities/reports'
import { useTranslation } from 'react-i18next'
import { ReportViewModal } from './report-view-modal'
import styles from './hint-report-modal.module.scss'
import { SelectDateCardWithInput } from '@/shared/components/SelectDateCard/select-date-card-with-input'
import { addDays, endOfDay, startOfDay } from '@/shared/helpers/date'
import { Button } from '@/shared/ui'
import { Modal } from '@/shared/components'

const cx = classNamesBind.bind(styles)

export const HintReportModal: FC<ReportModalProps.Props> = props => {
  const { title = 'Export', type, open, setOpen, objectID } = props
  const scormReport = ['scorm', 'certificateScorm']
  const intervalReports = ['PDF', 'subOrganizationPDF', 'phishing']
  const [isLoading, setIsLoading] = useState(false)
  const [step, setStep] = useState<number>(intervalReports.includes(type) ? 0 : 1)
  const [startDate, setStartDate] = useState<Date | null>()
  const [endDate, setEndDate] = useState<Date | null>()
  const { t } = useTranslation()

  const urlPost: Record<TReportType, (id?: UUID) => string> = {
    PDF: id => `reports/pdf${id ? `?organization_id=${id}&` : ''}`,
    subOrganizationPDF: id => `reports/pdf${id ? `?organization_id=${id}&` : ''}`,
    progress: id => `reports/progress${id ? `?organization_id=${id}&` : ''}`,
    attempt: id => `reports/attempt/${id}`,
    attemptScorm: id => `reports/attempt/${id}/scorm`,
    course: id => `reports/course/${id}`,
    courses: id => `reports/courses/${id}`,
    scorm: id => `reports/course/${id}`,
    phishing: () => `reports/phishing`,
    certificate: id => `reports/pdf/certificate/${id}`,
    certificateScorm: id => `reports/pdf/certificate/${id}`,
    certificateCourseV2: id => `reports/pdf/certificate/${id}/v2`,
  }

  const [postReportTrigger, { data }] = reportServiceApi.usePostReportWithReportModalMutation()

  const fetchReport = async () => {
    try {
      setIsLoading(_ => true)
      await postReportTrigger({
        url: urlPost[type](objectID ? objectID : ''),
        params: scormReport.includes(type)
          ? {
              type: 'scorm',
            }
          : type === 'phishing' && objectID
            ? {
                organization_id: objectID,
                date_from: startDate
                  ? addDays(startDate, 1)?.toISOString().slice(0, 10)
                  : undefined,
                date_to: endDate ? addDays(endDate, 1)?.toISOString().slice(0, 10) : undefined,
              }
            : {
                date_from: startDate
                  ? addDays(startDate, 1)?.toISOString().slice(0, 10)
                  : undefined,
                date_to: endDate ? addDays(endDate, 1)?.toISOString().slice(0, 10) : undefined,
              },
      }).unwrap()
    } catch (e) {
      setError(t('commons:error_try_again'))
    } finally {
      setIsLoading(_ => false)
    }
  }

  useEffect(() => {
    if (!intervalReports.includes(type)) {
      fetchReport()
    }
  }, [])

  const [error, setError] = useState('')

  const loaderTitle = () => {
    if (isLoading) return t('dialogs.report_modal.loader_title')
    if (error) return error

    if (type === 'subOrganizationPDF') {
      return t('commons:report_sending_by_email')
    }

    return (
      <>
        {t('commons:report_in_progress')}
        <br />
        {t('commons:report_description')}
      </>
    )
  }

  const onDownload = () => {
    setStep(1)
    fetchReport()
  }

  // При генерации PDF отчета в ответе приходит null
  const verifiedData =
    (type === 'subOrganizationPDF' || type === 'PDF') && data === null ? {} : data

  return (
    <>
      {step === 0 && (
        <Modal active={open} setActive={setOpen} className={cx('modal')}>
          <h2 className={cx('modal__title')}>{t('commons:export_report')}</h2>
          <div className={cx('modal__inner')}>
            <div className={cx('modal__intervals')}>
              <SelectDateCardWithInput
                label={t('commons:date_start')}
                wrapperClassName={cx('modal__interval')}
                datePickerClassName={cx('modal__datepicker')}
                classNameLabel={cx('modal__interval-label')}
                onChange={v => {
                  const date_start = v ?? null
                  setStartDate(date_start)
                }}
                withoutTime
                selected={startDate ?? null}
                text={t('commons:choose')}
                max={
                  endDate ? endOfDay(addDays(new Date(endDate as Date), -1)) : endOfDay(new Date())
                }
                min={null}
              />
              <div className={cx('modal__separator')}>:</div>
              <SelectDateCardWithInput
                label={t('commons:date_end')}
                wrapperClassName={cx('modal__interval')}
                datePickerClassName={cx('modal__datepicker')}
                classNameLabel={cx('modal__interval-label')}
                onChange={v => {
                  const date_end = v ?? null
                  setEndDate(date_end)
                }}
                withoutTime
                selected={endDate ?? null}
                text={t('commons:choose')}
                max={endOfDay(new Date())}
                min={startDate ? startOfDay(addDays(new Date(startDate), 1)) : null}
              />
            </div>
            <div className={cx('modal__buttons')}>
              <Button size='big' color='gray' onClick={() => setOpen(false)}>
                {t('commons:cancel')}
              </Button>
              <Button size='big' color='green' onClick={onDownload}>
                {t('commons:download')}
              </Button>
            </div>
          </div>
        </Modal>
      )}
      {step === 1 && (
        <ReportViewModal
          data={verifiedData}
          error={!!error}
          isLoading={isLoading}
          loaderTitle={loaderTitle()}
          open={open}
          setOpen={setOpen}
          title={title}
        />
      )}
    </>
  )
}
