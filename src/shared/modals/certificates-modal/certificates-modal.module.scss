.wrapper {
  display: grid;
  grid-template-rows: auto 1fr;
  height: 100%;
  max-height: 90vh;
  max-width: 624px;
  width: 100%;
}

.inner {
  height: 100%;
  margin: 0 -24px;
  overflow-x: hidden;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    width: 0;
  }

  .item {

    cursor: pointer;
    display: grid;
    grid-template-columns: auto 1fr auto auto;

    padding: 12px 24px 12px 36px;

    transition: var(--transition);

    > span {

      color: var(--gray-gray-90, #343b54);

      display: block;
      font: var(--font-text-1-normal);
      margin-right: 16px;
    }

    .itemTitle {

      color: var(--gray-gray-90, #343b54);
      font: var(--font-text-1-normal);
      overflow: hidden;

      padding-right: 10px;
      text-overflow: ellipsis;

      white-space: nowrap;
    }
    &:hover {
      background: var(--gray-gray-40, #f0f3f7);

      transition: var(--transition);
    }
  }
}

.empty {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;

  > img {
    display: block;
    margin: 0 auto;
  }
  &-title {
    color: var(--gray-gray-70, #8e97af);
    font: var(--font-title-3-medium);

    text-align: center;
  }
}

.title {

  color: var(--gray-gray-90, #343b54);
  font: var(--font-text-2-normal);
  margin-bottom: 28px;

  max-width: max-content;
  padding-bottom: 12px;

  position: relative;

  &::after {

    background: var(--color-primary, #3dbc87);
    bottom: -2px;

    content: "";

    display: block;
    height: 2px;
    left: 0;

    position: absolute;
    width: 100%;
  }
}

.tabs {
  &__content{
    height: calc(90vh - 80px);
  }
  
  &__item{
    max-height: 100%;
    margin: 0px;
    margin-top: 12px;
    overflow-x: hidden;
    overflow-y: scroll;

    &_active{
      border-bottom: 2px solid var(--color-primary);
    }

    &::-webkit-scrollbar {
      width: 3px;
    }

    &::-webkit-scrollbar-track {
      background: none; 
    }

    &::-webkit-scrollbar-thumb {
      background: var(--color-gray-60); 
      border-radius: 10px; 
      cursor: grab;

      &:hover {
        background: var(--color-gray-80); 
      }
    }
  }
}