import { globalBaseApi } from '@/store/services/endpoints/base'

export const coursesReports = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getReports: builder.query<unknown, { assigned_course_id: string }>({
      query: ({ assigned_course_id }) => {
        return {
          url: `/lk/api/v2/reports/pdf/certificate/${assigned_course_id}/v2`,
        }
      },
    }),
  }),
  overrideExisting: true,
})
