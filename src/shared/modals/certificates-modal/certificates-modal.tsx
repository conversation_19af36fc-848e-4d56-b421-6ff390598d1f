/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react'
import styles from './certificates-modal.module.scss'
import classNamesBind from 'classnames/bind'
import { CertificatesModalProps } from './certificates-modal.d'
import { Modal } from '@/shared/components'
import { TabItem, TabsNew } from '@/shared/ui'
import { useCertificatesModal } from './use-certificates-modal'
import { ReportModal } from '@/shared/modals/report-modal'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const CertificatesModal: FC<CertificatesModalProps.Props> = props => {
  const { className, setActive, active } = props

  const { TABS_CONFIG, tab, setTab, getReportModalType, openSuccessModal, setOpenSuccesModal } =
    useCertificatesModal()

  const { t } = useTranslation('modals__certificates-modal')

  return (
    <>
      <Modal className={cx('wrapper', className)} active={active} setActive={setActive}>
        <TabsNew
          activeTab={tab?.value}
          tabContentClassname={cx('tabs__content')}
          tabContentItemClassname={cx('tabs__item')}
          activeTabClassname={cx('tabs__item_active')}
          tabsClassname={cx('tabs')}
          className={cx('tabs__container')}
          onChange={v => setTab(TABS_CONFIG.find(t => t.value === v) ?? TABS_CONFIG?.[0])}
        >
          {TABS_CONFIG.map(tab => {
            return (
              <TabItem key={tab.value} label={tab.label} value={tab.value}>
                <div>{tab.content}</div>
              </TabItem>
            )
          })}
        </TabsNew>
      </Modal>
      {openSuccessModal && (
        <ReportModal
          customSuccessText={t('commons:certificates_success')}
          type={getReportModalType(openSuccessModal.type)}
          open={!!openSuccessModal}
          setOpen={setOpenSuccesModal as any}
          title={t('commons:certificates')}
          objectID={openSuccessModal.id}
        />
      )}
    </>
  )
}
