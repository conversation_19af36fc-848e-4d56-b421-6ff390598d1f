import path from 'path'

export const primaryPaletteRatios: Record<string, number> = {
  primary: 100,
  'primary-90': 88.9,
  'primary-80': 77.9,
  'primary-70': 66.7,
  'primary-60': 55.9,
  'primary-50': 44.5,
  'primary-40': 33.5,
  'primary-30': 22.4,
  'primary-20': 11.1,
}

// export const frontendBaseUrl = "/lk";
export const frontendBaseUrl = '.'
export const apiUrl = '/'

export const frontendDir = path.resolve(__dirname, '..', 'dist')
export const indexFileName = 'index.html'

export const port = process.env.PORT || 8080

export const apiHost = process.env.API_HOST!
export const apiToken = process.env.API_TOKEN!
export const needPrivacyPolicyPage = process.env.NEED_PRIVACY_POLICY_PAGE === 'true'
export const needAgreementPage = process.env.NEED_AGREEMENT_PAGE === 'true'

export const isDev = false || process.env.NODE_ENV !== 'production'

export const analyticsEnabled = process.env.ANALYTICS_ENABLED === 'true'
export const analyticsYMId = process.env.ANALYTICS_YANDEX_METRICA_ID || null
export const analyticsGID = process.env.ANALYTICS_GOOGLE_ANALYTICS_ID || null

export const useSSO = process.env.USE_SSO === 'true'
