import axios, { AxiosInstance } from 'axios'

import { apiHost } from './config'

export interface Branding {
  id: string
  url: string
  title: string
  description: string
  favicon: string | null
  logo: {
    light: string
    dark: string
  }
  theme: {
    id: string
    colors: {
      primary: string
    }
  }
}

interface CacheEntry {
  timestamp: number
  data: Branding
}

export class API {
  private api: AxiosInstance | undefined
  private cache: { [key: string]: CacheEntry } = {}
  private cacheDuration = 5000
  constructor() {
    this.api = axios.create({
      baseURL: apiHost,
    })
  }

  async getBranding(domain: string, secondDomain?: string): Promise<Branding> {
    let cacheKey = `branding-${domain}`
    const now = Date.now()

    if (this.cache[cacheKey] && now - this.cache[cacheKey].timestamp < this.cacheDuration) {
      return Promise.resolve(this.cache[cacheKey].data)
    }

    const brandingRequest = await this.api?.get<Branding>(`/lk/api/v2/internal/configs/`, {
      params: { domain },
    })

    let branding = brandingRequest?.data as Branding

    if (!branding && secondDomain) {
      branding = (
        await this.api?.get<Branding>(`/lk/api/v2/internal/configs/`, {
          params: { domain: secondDomain },
        })
      )?.data as Branding

      cacheKey = `branding-${secondDomain}`
    }

    this.cache[cacheKey] = { timestamp: now, data: branding }
    return branding
  }
}

export const brandingApi = new API()
