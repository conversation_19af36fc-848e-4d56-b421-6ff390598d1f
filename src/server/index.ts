/* eslint-disable no-empty */
/* eslint-disable no-useless-escape */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { AxiosError } from 'axios'
import express, { RequestHandler } from 'express'
import { engine } from 'express-handlebars'
import path from 'path'

import { API } from './api'
import {
  analyticsEnabled,
  analyticsGID,
  analyticsYMId,
  apiUrl,
  frontendBaseUrl,
  frontendDir,
  indexFileName,
  port,
  useSSO,
  needPrivacyPolicyPage,
  needAgreementPage,
  apiHost,
} from './config'
import { getClientHost } from './utils'
import { generatePrimaryPalette } from '../shared/utils'

function isAxiosError(err: unknown): err is AxiosError {
  return typeof err === 'object' && err !== null && 'response' in err
}

const api = new API()

const defaultHandler: RequestHandler = async (req, res) => {
  const host = getClientHost(req)
  // Говнокод от backend'еров для предотвращения xss
  const lang = req.query.lang || 'ru'
  const langStr = lang.toString()
  const sanitized = langStr.replace(/[^a-z0-9áéíóúñü \.,_-]/gim, '')
  // X-USE-SSO передается из caddy для теста оффлайн версии
  const useSSOHeader = req.headers['x-use-sso'] === 'true'

  const hostURL = process.env.API_HOST || host

  try {
    const branding = await api.getBranding(host, hostURL)

    if (!branding) {
      throw new Error()
    }

    const baseData = {
      analytics_enabled: analyticsEnabled,
      yandex_metrika_id: analyticsYMId,
      google_analytics_id: analyticsGID,
      sanitized,
      apiUrl,
      apiHost,
      useSSO: useSSOHeader || useSSO,
      needPrivacyPolicyPage: needPrivacyPolicyPage,
      needAgreementPage: needAgreementPage,
    }

    const templateData = {
      layout: false,
      ...baseData,
      ...branding,
      theme: {
        ...branding,
        colors: {
          ...(branding?.theme?.colors ?? {}),
          ...generatePrimaryPalette(branding?.theme?.colors?.primary),
        },
      },
      eventKeeperUrl: '/api/v2/webhooks/events',
    }

    const preparedConfig: Record<string, unknown> = {
      ...templateData,
      config_json: JSON.stringify(templateData, null, 4),
    }

    if (templateData?.theme?.favicon) {
      preparedConfig.fav_icon = templateData?.theme?.favicon
    }

    res.render(path.parse(indexFileName).name, preparedConfig)
  } catch (err: unknown) {
    let message = 'Произошла неизвестная ошибка. Повторите запрос позже.'
    console.log(
      `[${new Date()}]: Request error. ${(err as any).message}` +
        ((isAxiosError(err) && err.response?.data ? err.response?.data.toString() : '') as string),
    )

    if (isAxiosError(err)) {
      const status = err.response?.status
      if (status) {
        if (status === 404) {
          message = 'Неизвестный домен. Проверьте правильность написания ссылки'
        } else if (status === 500) {
          // use default unknown message
          // TODO 500 error page
          message = 'Внутренняя ошибка сервера. Повторите запрос позже'
        } else if ([502, 504].includes(status)) {
          // use default unknown message
          // TODO Gateway Timeout error page
          message = 'Сервер временно недоступен. Повторите запрос позже'
        }
      }
    }
    res.send(message)
  }
}

async function main() {
  const app = express()

  app.get('/health', (_, res) => {
    res.status(200).send('OK')
  })

  app.engine('html', engine({ extname: path.parse(indexFileName).ext }))
  app.set('view engine', 'html')
  app.set('views', frontendDir)
  app.set('view cache', false)

  const oldRegExp = /^[\/a-zA-Z0-9\-_]+(index\.html)?$/

  app.use(oldRegExp, defaultHandler)
  app.use(express.static(frontendDir))

  app.use(frontendBaseUrl, express.static(frontendDir))

  await new Promise(res =>
    app.listen(port, () => {
      console.log(`🚀 start server at port ${port}`)
      res(null)
    }),
  )
}

void main()
