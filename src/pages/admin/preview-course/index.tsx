/* eslint-disable react-hooks/exhaustive-deps */
import styles from './preview-course.module.scss'
import classNamesBind from 'classnames/bind'
import { useAppDispatch, useAppSelector } from '@/store'
import { ReactNode, useEffect, useLayoutEffect } from 'react'
import { setCreateContent } from '@/store/slices/sidebar-slice'
import { DefaultSidebarContent } from '@/shared/components'
import { ThemePreview } from '@/entities/themeCourse/ui/theme-preview'
import { themeApi } from '@/entities/themeCourse/model/api'
import { CourseSection, CourseTheme, coursesApi } from '@/entities/courses'
import {
  selectViewedCourseTheme,
  setViewedCourse,
  setViewedCourseTheme,
} from '../learning/assign/create/slice'
import { useNavigate, useParams } from 'react-router-dom'
import { Loader } from '@/shared/ui'
import ChevroneBoldIcon from '@/shared/ui/Icon/icons/components/ChevroneBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useTranslation } from 'react-i18next'
import { URLS } from '@/shared/configs/urls'
import { useNotification } from '@/shared/contexts/notifications'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'pages__preview-course'

const CoursePreview = () => {
  const { course_id = '' } = useParams()
  const { t } = useTranslation(TRANSLATION_FILE)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { add: addNotification } = useNotification()
  const [getThemeData, { isLoading: isLoadingThemeData }] = themeApi.useLazyGetThemeByIdQuery()
  const { data: courseData } = coursesApi.useGetCourseByIdQuery(
    { course_id: course_id },
    {
      skip: !course_id,
    },
  )
  const viewedTheme = useAppSelector(selectViewedCourseTheme)

  useEffect(() => {
    dispatch(setViewedCourse(courseData))

    return () => {
      dispatch(setViewedCourse(undefined))
    }
  }, [courseData])

  const pickTheme = async (theme: CourseTheme | undefined) => {
    if (!theme) return
    const data = await getThemeData(theme.id).unwrap()
    await dispatch(setViewedCourseTheme(data))
  }

  useEffect(() => {
    if (courseData && !courseData?.sections?.length) {
      navigate(URLS.ADMIN_LEARNING_COURSES_PAGE)
      addNotification({
        message: t('no_themes'),
        status: 'error',
        id: crypto.randomUUID(),
        withoutAdditionalInfo: true,
      })
      return
    }

    if (courseData) {
      pickTheme(courseData.sections?.[0]?.themes?.[0])
    }
  }, [courseData])

  useEffect(() => {
    return () => {
      dispatch(setCreateContent(() => <DefaultSidebarContent />))
    }
  }, [])

  useLayoutEffect(() => {
    dispatch(
      setCreateContent(() => (
        <CourseSidebar
          courseName={courseData?.title}
          sections={courseData?.sections ?? []}
          currentTheme={viewedTheme?.id}
          onThemeClick={pickTheme}
        />
      )),
    )
  }, [dispatch, courseData, viewedTheme, pickTheme])

  const onClick = () => {
    navigate(-1)
  }

  if (!courseData) return null

  return (
    <div className={cx('wrapper')} key={courseData.id}>
      {isLoadingThemeData ? (
        <Loader />
      ) : (
        <div className={cx('previewWrapper')}>
          <div className={cx('backButton')} onClick={onClick}>
            <IconWrapper color='gray70' direction='down' size='20' className={cx('icon')}>
              <ChevroneBoldIcon />
            </IconWrapper>
            {t('commons:back')}
          </div>
          <div className={cx('title__wrapper')}>
            <h1 className={cx('title')}>{viewedTheme?.title}</h1>
            <div className={cx('title__hint')}>{t('hint')}</div>
          </div>
          <ThemePreview themeData={viewedTheme} key={viewedTheme?.id} />
        </div>
      )}
    </div>
  )
}
type CourseSidebarProps = {
  courseName: ReactNode
  sections: CourseSection[]
  currentTheme?: UUID
  onThemeClick: (theme: CourseTheme) => void
}

const CourseSidebar = ({
  courseName,
  sections,
  currentTheme,
  onThemeClick,
}: CourseSidebarProps) => {
  return (
    <div className={cx('sidebar')}>
      <div className={cx('title__wrapper')}>
        <h3 className={cx('title')}>{courseName}</h3>
      </div>
      <ul className={cx('sections')}>
        {sections.map((section, sectIndex) => {
          const sectionIndex = sectIndex + 1

          return (
            <li className={cx('sections__item')} key={section.title}>
              <h4 className={cx('sections__item__title')}>
                <span>{sectionIndex} </span>
                <span className={cx('sections__item__name')}>{section.title} </span>
              </h4>
              <ul className={cx('themes')}>
                {section.themes.map((theme, thmIndex) => {
                  const themeIndex = thmIndex + 1
                  return (
                    <li
                      key={theme.id}
                      className={cx('themes__item', {
                        active: theme.id === currentTheme,
                      })}
                      onClick={() => {
                        onThemeClick(theme)
                      }}
                    >
                      <span>
                        {sectionIndex}.{themeIndex}
                      </span>
                      <span className={cx('themes__item__title')}>{theme.title}</span>
                    </li>
                  )
                })}
              </ul>
            </li>
          )
        })}
      </ul>
    </div>
  )
}

export default CoursePreview
