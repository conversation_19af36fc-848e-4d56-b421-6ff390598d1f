import React, { FC, useMemo } from 'react'
import { QueryStatus } from '@reduxjs/toolkit/query'

import { ButtonIcon, Loader, RoundedButton, PageTitle } from '@/shared/ui'
import { usePhishingTemplates } from './use-phishing-templates'

import styles from './phishing-templates.module.scss'
import classNamesBind from 'classnames/bind'
import { sortTemplatesByCategoryName } from './helpers'
import { PhishingTemplatesProps } from './phishing-templates.d'
import { TemplateLogo } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const TemplatesList: React.FC<PhishingTemplatesProps.TemplateList> = ({
  copyArgs,
  isItemCopiesLoading,
  data,
  onItemClick,
  onItemCopy,
  onItemEdit,
}) => {
  const mappedData = useMemo(() => sortTemplatesByCategoryName(data), [data])
  const { t } = useTranslation()

  return (
    <ul className={cx('list')}>
      {mappedData.map(i => (
        <li className={cx('list__item')} title={i.category?.name} key={i.id}>
          <h3 className={cx('list__item__title')}>
            {i.category?.name || t('commons:no_category')}
          </h3>
          <ul className={cx('template-list')}>
            {i.templates.map(i => (
              <li
                onClick={() => onItemClick(i.id)}
                className={cx('template-list__item')}
                title={i.name}
                key={i.id}
              >
                <TemplateLogo src={i.logo} alt={i.name} />
                <p className={cx('template-list__item__name')}>{i.name}</p>
                <div className={cx('template-list__item__actions')}>
                  {i.can_edit && (
                    <div title={`${t('commons:edit')} ${i.name}`} role='button'>
                      <ButtonIcon
                        onClick={(e: { stopPropagation: () => void }) => {
                          e.stopPropagation()

                          onItemEdit(i.id)
                        }}
                        title={`${t('commons:edit')} ${i.name}`}
                        className={cx('template-list__item__actions__btn')}
                        color='gray70'
                        icon='editBold'
                      />
                    </div>
                  )}
                  {i.can_read && (
                    <div
                      role='button'
                      className={cx('template-list__item__actions__btn')}
                      title={`${t('commons:copy')} ${i.name}`}
                      color='gray'
                      onClick={e => {
                        e.stopPropagation()

                        onItemCopy(i.id)
                      }}
                    >
                      {isItemCopiesLoading && copyArgs?.id === i.id ? (
                        <ButtonIcon
                          className={cx('template-list__item__actions__btn')}
                          classNameForIcon={cx('animate-spin')}
                          color='gray70'
                          size='28'
                          icon='load'
                        />
                      ) : (
                        <ButtonIcon
                          className={cx('template-list__item__actions__btn')}
                          color='gray70'
                          size='28'
                          icon='copy'
                        />
                      )}
                    </div>
                  )}
                </div>
              </li>
            ))}
          </ul>
        </li>
      ))}
    </ul>
  )
}

export const PhishingTemplates: FC<PhishingTemplatesProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const {
    data,
    isItemCopiesLoading,
    onCreateItem,
    onItemClick,
    onItemCopyClick,
    onItemEdit,
    originalArgs,
    dataFetchStatus,
  } = usePhishingTemplates()

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('templates__wrapper')}>
        <PageTitle>{t('commons:templates')}</PageTitle>
        <RoundedButton size='40' onClick={() => onCreateItem()} />
      </div>
      {!data && dataFetchStatus === QueryStatus.pending && (
        <div className={cx('plug__wrapper')}>
          <Loader size='56' />
        </div>
      )}
      {!data && dataFetchStatus === QueryStatus.rejected && (
        <div className={cx('plug__wrapper')}>
          <Loader error={true} loading={false} />
          <h2>{t('commons:loading_error')}...</h2>
        </div>
      )}
      {data && (
        <TemplatesList
          copyArgs={originalArgs}
          onItemClick={onItemClick}
          data={data}
          isItemCopiesLoading={isItemCopiesLoading}
          onItemCopy={onItemCopyClick}
          onItemEdit={onItemEdit}
        />
      )}
    </div>
  )
}

export default PhishingTemplates
