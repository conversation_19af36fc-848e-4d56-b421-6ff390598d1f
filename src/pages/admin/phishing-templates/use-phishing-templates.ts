import { useMemo } from 'react'
import { groupByCategory } from '@/shared/helpers'

import { useLocation, useNavigate } from 'react-router-dom'
import {
  type IPhishingTemplateByCategory,
  phishingQueries,
  phishingMutations,
} from '@/entities/phishing'

export const usePhishingTemplates = () => {
  const { data, status } = phishingQueries.useGetTemplatesQuery({ search: '' })
  const [trigger, { isLoading: isItemCopiesLoading, originalArgs }] =
    phishingMutations.useCopyTemplateByIdMutation()

  const { pathname } = useLocation()
  const navigate = useNavigate()

  const onItemClick = (id: string) => {
    navigate(`${pathname}/${id}`)
  }

  const onItemEdit = (id: string) => {
    navigate(`/lk/admin/phishing/templates/${id}/edit`)
  }

  const onCreateItem = () => {
    navigate(`${pathname}/new`)
  }

  const onItemCopyClick = async (id: string) => {
    await trigger({ id }).unwrap()
  }

  const preparedData = useMemo(
    () => (data ? groupByCategory(data) : undefined),
    [data],
  ) as IPhishingTemplateByCategory[]

  return {
    data: preparedData,
    onItemCopyClick,
    onCreateItem,
    onItemEdit,
    onItemClick,
    isItemCopiesLoading,
    originalArgs,
    dataFetchStatus: status,
  }
}
