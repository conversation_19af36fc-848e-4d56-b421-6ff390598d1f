@use "../../../shared/assets/styles/mixins/icons";
@use "../../../shared/assets/styles/mixins/text";
@use "../../../shared/assets/styles/mixins/media";

.templates__wrapper {
  align-items: center;
  display: flex;
  justify-content: space-between;
}

.plug__wrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100dvh;

  text-align: center;
  width: 100%;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 24px;

  &__item {
    display: flex;
    flex-direction: column;
    gap: 16px;

    &__title {
      color: var(--gray-gray-90);
      font: var(--font-title-4-medium);
    }
  }
}

.template-list {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(4, 1fr);

  @include media.mqXl() {
    grid-template-columns: repeat(3, 1fr);
  }

  @include media.mqL() {
    gap: 8px;
    grid-template-columns: repeat(2, 1fr);
  }

  @include media.mqS() {
    grid-template-columns: repeat(1, 1fr);
  }

  &__item {
    align-items: center;

    background-color: var(--white);
    border: 1px solid var(--gray-gray-30);

    border-radius: 12px;

    cursor: pointer;

    display: grid;
    gap: 12px;
    grid-template-columns: auto 1fr auto;
    height: 48px;

    padding: 10px 12px;

    transition: var(--transition);
    width: 100%;

    @include media.mqL() {
      padding: 8px;
    }

    &__name {
      @include text.max-lines(1);
      color: var(--color-gray-100);
      font: var(--font-text-2-normal);
    }

    img {
      height: 24px;
      width: 24px;
    }

    &__actions {
      display: none;
      margin-left: auto;

      &__btn {
        height: 28px !important;
        padding: 0 !important;
        width: 28px !important;
      }
    }

    &:hover {
      background: var(--color-gray-20, #f9fafc);

      transition: var(--transition);

      > .template-list__item__actions {
        display: flex;
      }
    }
  }
}
