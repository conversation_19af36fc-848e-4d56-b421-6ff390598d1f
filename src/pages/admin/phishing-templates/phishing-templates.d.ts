import { IPhishingTemplateByCategory } from "@/shared/types/store/phishing";

export declare namespace PhishingTemplatesProps {
  interface Own {
    className?: string;
  }

  type Props = Own;

  interface TemplateList {
    data: IPhishingTemplateByCategory[];
    onItemClick: (id: string) => void;
    onItemEdit: (id: string) => void;
    onItemCopy: (id: string) => void;
    isItemCopiesLoading: boolean;
    copyArgs?: { id: string };
  }
}

export {};
