import { skipToken } from '@reduxjs/toolkit/query'
import { useMemo, useState } from 'react'
import { organizationAPI } from 'entities/organization'
import { SubmitHandler, useForm } from 'react-hook-form'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { handleErrorResponseAndTranslate } from '@/shared/contexts/notifications/helper'
import { useTranslation } from 'react-i18next'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

interface IEditOrganizationInputs {
  title: string
  employees_limit: string
}

export const useOrganizationEdit = () => {
  const { organization_id = '' } = useParams()
  const [currentQueryParameters] = useSearchParams()
  const from = currentQueryParameters.get('from') || ''
  const navigate = useNavigate()
  const { t } = useTranslation()

  const [error, setError] = useState<string | null>(null)

  const { data: organization, isLoading } = organizationAPI.useGetOrganizationQuery(
    organization_id ?? skipToken,
  )

  const resolver = useMemo(
    () =>
      zodResolver(
        z
          .object({
            title: z
              .string()
              .min(1, {
                message: t('commons:required_field'),
              })
              .refine(value => !!value.trim(), {
                message: t('commons:required_field'),
              }),
            employees_limit: z.string().regex(/^\d+$/, {
              message: t('commons:enter_number'),
            }),
          })
          .refine(() => !!organization?.parent?.id),
      ),
    [t, organization?.parent?.id],
  )

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setValue,
    setError: setErrorForm,
  } = useForm<IEditOrganizationInputs>({
    mode: 'all',
    values: {
      title: organization?.title || '',
      employees_limit: '' + organization?.license?.users_limit || '',
    },
    resolver,
  })

  const [editOrganization, { isLoading: isEditLoading }] =
    organizationAPI.useEditOrganizationMutation()

  const onSubmit: SubmitHandler<IEditOrganizationInputs> = async data => {
    if (!organization?.parent?.id) return

    const body = {
      title: data.title,
      employees_limit: +data.employees_limit,
      parent_id: organization?.parent?.id,
      id: organization_id,
    }

    await editOrganization(body)
      .unwrap()
      .then(({ id }) => {
        if (!id) {
          return setError(t('commons:error_occurred_data'))
        }

        setError('')
        reset()
        navigate(`/lk/admin/organization/${organization_id}`)
      })
      .catch(e => setError(handleErrorResponseAndTranslate(e)))
  }

  const handleTrim = (str: string, name: keyof IEditOrganizationInputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  return {
    handleSubmit,
    onSubmit,
    errors,
    register,
    handleTrim,
    isLoading,
    isValid,
    error,
    from,
    isEditLoading,
  }
}
