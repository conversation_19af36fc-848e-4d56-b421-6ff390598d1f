import { FC } from 'react'
import styles from './organization-edit.module.scss'
import classNamesBind from 'classnames/bind'
import { OrganizationEditProps } from './organization-edit.d'
import { BackButton, Button, Input, Loader, PageTitle } from '@/shared/ui'
import { useOrganizationEdit } from './use-organization-edit'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const OrganizationEdit: FC<OrganizationEditProps.Props> = props => {
  const { className } = props

  const { t } = useTranslation()

  const {
    handleSubmit,
    onSubmit,
    errors,
    register,
    handleTrim,
    isLoading,
    isValid,
    error,
    from,
    isEditLoading,
  } = useOrganizationEdit()

  return (
    <div className={cx('wrapper', className)}>
      {/* TEMPTODO - надо будет на хлебные крошки переделать наверное */}
      <BackButton route={from ? `/lk/admin/organization/${from}` : '/lk/admin/organization/'} />
      <PageTitle>{t('commons:organization_edit')}</PageTitle>

      {isLoading && <Loader size='56' />}
      {!isLoading && (
        <form onSubmit={handleSubmit(onSubmit)} className={cx('form')}>
          <Input
            label={t('commons:name')}
            placeholder={t('commons:name')}
            classNameWrapper={cx('formInput')}
            error={errors.title?.message}
            fullWidth
            required
            register={register('title')}
            onBlur={e => handleTrim(e.target.value, 'title', true)}
          />
          <Input
            label={t('commons:license_amount')}
            placeholder={t('commons:license_amount')}
            classNameWrapper={cx('formInput')}
            error={errors.employees_limit?.message}
            type='number'
            fullWidth
            required
            register={register('employees_limit')}
          />
          <Button type='submit' loading={isEditLoading} fullWidth disabled={!isValid}>
            {t('commons:edit')}
          </Button>
          {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
        </form>
      )}
    </div>
  )
}
