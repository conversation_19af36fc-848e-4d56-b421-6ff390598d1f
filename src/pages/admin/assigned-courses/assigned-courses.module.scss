@use '../../../shared/assets/styles/mixins/text';
@use '../../../shared/assets/styles/mixins/colors';
@use '../../../shared/assets/styles/mixins/media';

.page {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 40px;
}

.tags {
  display: flex;
  gap: 16px;
}

.courses {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  align-items: flex-end;

  @include media.mqXl {
    grid-template-columns: repeat(2, 1fr);
  }

  &__item {
    padding: 20px 20px 16px 20px;
    background-color: white;
    min-height: 180px;
    border: 1px solid color(--color-gray-30);
    display: flex;
    flex-direction: column;
    border-radius: 16px;
    gap: 12px;

    &__img {
      width: 48px;
      height: 48px;
      object-fit: cover;
      border-radius: 16px;
      border: 1px solid var(--color-gray-30);
      max-width: 100%;
      max-height: 100%;
      height: 100%;

      &__wrapper {
        background-color: var(--color-gray-30);
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 16px;
      }
    }

    &__actions {
      display: flex;
      justify-content: space-between;

      &__chevrone {
        display: flex;
        align-items: center;
        gap: 4px;

        &__text {
          display: none;
          font: var(--font-caption-1-medium);
          color: var(--color-primary);
        }
      }
    }

    &:hover {
      .courses__item__actions__chevrone__text {
        display: block;
      }
      * {
        stroke: var(--color-primary) !important;
      }
    }

    &__title {
      color: var(--color-gray-90);
      font: var(--font-text-2-demibold);
      word-break: break-word;
      @include text.max-lines(3);
    }

    &__additional {
      &__wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: auto;
        gap: 8px;
      }
      text-align: right;
      color: var(--color-gray-80);
      font: var(--font-caption-1-medium);
      font-weight: 500;
    }
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filterTags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.title {
  font: var(--font-title-2-medium);
  color: var(--color-gray-90);
}

.actions {
  display: flex;
  justify-content: space-between;
}

.menu-wrapper {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  background: var(--color-surface);
  border: 1px solid var(--color-gray-30);
  border-radius: 8px;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
  padding: 6px 0;
  min-width: 200px;
  z-index: 10;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  cursor: pointer;
  color: var(--color-gray-80);
  font: var(--font-text-2-medium);
  transition: var(--transition);

  &:hover {
    background: var(--color-gray-20);
    transition: var(--transition);
  }

  span {
    flex: 1;
  }
}

.tooltip {
  transform: translateX(-270px) translateY(-50%);
}
