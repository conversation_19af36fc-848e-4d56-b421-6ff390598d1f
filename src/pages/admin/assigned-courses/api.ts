import { globalBaseApi } from '@/store/services/endpoints/base'

export type CourseTabVariants = 'active' | 'completed' | 'planned' | 'shared'

type CoursesByOrganizationResponse = ResponseWithPagination & {
  data: Course[]
}

type CoursesByOrganizationRequest = RequestWithPagination & {
  search?: string
  organization_id: string
  tags?: string[]
  status?: CourseTabVariants
}

export type Course = {
  id: string
  title: string
  description?: string
  organization_id: string
  picture: string
  created_at: string
  start_date: string
  end_date: string
  status: string
  employees_count: number
  need_assigned_message: boolean
  need_notify_message: boolean
  tags?: Array<string | null>
}

export const assignedCoursesApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getAssignedCoursesByOrganization: builder.query<
      CoursesByOrganizationResponse,
      CoursesByOrganizationRequest
    >({
      query: ({ organization_id, ...params }) => ({
        url: `/learning/api/learning/assigned-courses/organizations/${organization_id}`,
        params,
      }),
      providesTags: ['assigned-courses'],
    }),
  }),
  overrideExisting: true,
})
