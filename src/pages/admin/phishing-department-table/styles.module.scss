@use '../../../shared/assets/styles/mixins/text';

.table {
  background-color: var(--white);
  border-radius: 16px;
  padding: 12px 16px;
  width: 100%;
  &__wrapper {
    margin: 12px 0;
  }

  tbody {
    color: var(--gray-gray-90);
    font: var(--font-caption-1-medium);
    position: relative;
    .name-cell {
      width: 20%;
    }
    .work-post-cell {
      width: 15%;
    }
    .department-cell {
      width: 20%;
    }
    .system-cell {
      width: 15%;
    }
    .employee-cell {
      width: 30%;
    }
    .dep-cell {
      width: 40%;
    }
    .popular-cell {
      width: 25%;
    }
    .caught-cell {
      width: 25%;
    }
    .date-cell {
      width: 15%;
      overflow: visible;
    }
    .date-cell-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
    }
    tr {
      border-top: 1px solid var(--color-gray-30);
      padding: 12px 16px;
    }
    td {
      align-items: center;
      min-height: inherit;
      overflow: hidden;
      padding: 12px 16px;

      vertical-align: middle;
      word-wrap: break-word;

      p,
      span {
        @include text.max-lines(2);
      }
    }
  }
  thead {
    color: var(--color-gray-70);

    font: var(--font-caption-1-medium);
    padding: 12px 16px;

    th {
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      padding: 12px 16px;

      text-align: start;
    }
  }

  &__opacity {
    opacity: 0.7;
  }
}

.plug {
  &__container {
    align-items: center;

    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 85px;

    text-align: center;

    width: 100%;
  }

  &__loader {
    align-items: center;

    display: flex;
    flex-direction: column;
    justify-content: center;
    left: 50%;
    position: absolute;
    top: 50%;

    transform: translate(-50%, -50%);
    &__container {
      align-items: center;
      height: 100%;
      justify-content: center;
      min-height: 200px;
      width: 100%;
    }
  }

  &__text {
    color: var(--gray-gray-70, #8e97af);
    font: var(--font-title-3-medium);
  }
}

.incident-level {
  &-good {
    color: var(--color-statistics-good);
  }
  &-warning {
    color: var(--color-statistics-warning);
  }
  &-bad {
    color: var(--color-statistics-bad);
  }
}

.box_icon {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  width: 100%;
  > div {
    height: 160px !important;
    width: 160px !important;

    > svg {
      height: 100% !important;
      width: 100% !important;
    }
  }
}
