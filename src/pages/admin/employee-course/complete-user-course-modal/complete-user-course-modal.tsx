import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { v4 as uuid } from 'uuid'

import { useAppDispatch, useAppSelector } from '@/store'
import {
  selectCompleteUserCourseModalOpen,
  setCompleteUserCourseModalOpen,
  selectSelectedUsers,
  selectExcludedUsers,
  selectIsAllSelected,
} from '@/pages/admin/employee-course/slice'
import { ConfirmModal } from '@/shared/modals/confirm-modal'
import { useNotification } from '@/shared/contexts/notifications'
import { coursesApi } from '@/entities/courses'

export const CompleteUserCourseModal = () => {
  const dispatch = useAppDispatch()
  const { t } = useTranslation('pages__assigned-course-detail-statistics')
  const { add } = useNotification()
  const { course_id = '' } = useParams()

  const isAllSelected = useAppSelector(selectIsAllSelected)
  const selectedUsers = useAppSelector(selectSelectedUsers)
  const excludedUsers = useAppSelector(selectExcludedUsers)
  const isCompleteUserCourseModalOpen = useAppSelector(selectCompleteUserCourseModalOpen)
  const [completeCourseForUsers, { isLoading }] = coursesApi.useCompleteCourseForUsersMutation()

  return (
    <ConfirmModal
      open={isCompleteUserCourseModalOpen}
      setOpen={v => dispatch(setCompleteUserCourseModalOpen(v))}
      title={t('actions.complete_course.action')}
      description={t('actions.complete_course.description')}
      /**
       * слева кнопка "Отменить" -> confirmText, confirmProps, а экшн onClose
       */
      confirmText={t('notify.cancel')}
      confirmProps={{ color: 'gray' }}
      onClose={() => {
        dispatch(setCompleteUserCourseModalOpen(false))
      }}
      /**
       * справа кнопка "Завершить" -> closeText, closeProps, а экшн onConfirm
       */
      closeText={t('actions.complete_course.action')}
      closeProps={{
        color: 'green',
        loading: isLoading,
      }}
      onConfirm={() => {
        completeCourseForUsers({
          assigned_course_id: course_id,
          users_ids: isAllSelected ? [] : selectedUsers,
          need_all: isAllSelected,
          exclude_users_ids: isAllSelected ? excludedUsers : [],
        }).then(() => {
          add({ id: uuid(), message: t('actions.complete_course.success'), status: 'success' })
          dispatch(setCompleteUserCourseModalOpen(false))
        })
      }}
    />
  )
}
