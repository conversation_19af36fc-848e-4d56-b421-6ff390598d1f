.card{
  padding: 12px 16px;

  display: flex;
  flex-direction: row;
  align-items: center;
}

.tags {
  position: relative;
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -4px !important;

  margin-right: -4px;

  .tag {
    max-width: 170px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: var(--stat-neutral-light, #e1e4eb);

    border-radius: 8px;

    color: var(--stat-neutral, #8e97af);
    color: var(--color-surface, #fff);
    font: var(--font-caption-2-medium);
    margin-bottom: 4px;

    margin-right: 4px;
    padding: 2px 6px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.description {
  &__employee {
    margin-left: 12px;
    width: 100%;
    max-width: 210px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    * {
      word-break: break-all;
    }
    &__name {
      color: var(--color-gray-90);
      font: var(--font-caption-1-medium);
    }
    &__email {
      color: var(--color-gray-80);
      font: var(--font-caption-2-normal);
    }
    &__progress {
      color: var(--color-gray-80);
      font: var(--font-caption-2-medium);
      font-size: 12px;
      display: flex;
      gap: 12px;
      align-items: center;
    }
  }
  &__general {
    margin-left: 24px;
    max-width: 480px;
    min-width: 200px;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 8px;

    &__name{
      font: var(--font-caption-1-normal);
      color: var(--color-gray-90);
    }
    &__position{
      font: var(--font-caption-2-normal);
      color: var(--color-gray-80);
    }
  }
}

.status{
  font: var(--font-caption-2-medium);
  font-size: 12px;
  margin-left: 24px;

  &_normal {
    color: var(--text-good-text);
  }
  &_behind {
    color: var(--color-error);
  }
  &_not-started {
    color: var(--color-gray-70);
  }
}

.go-to-employee {
  margin-left: auto;
}

