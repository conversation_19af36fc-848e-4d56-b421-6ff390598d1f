export type ContentCourse = {
  id: string
  title: string
  description: string
  organization_id: string
  picture: string
  created_at: string
  start_date: string
  end_date: string
  status: string
  employees_count: number
  need_assigned_message: boolean
  need_notify_message: boolean
}

export type EmployeesByCourseResponse = ResponseWithPagination & {
  data: AssignedUser[]
}

export type ProcessGroup = 'completed' | 'in_process' | 'not_started'
export type ProgressGroup = 'behind' | 'normal'
export type EmployeesByCourseSortOrder = 'asc' | 'desc'
export type EmployeesByCourseSortBy = 'full_name' | 'theory' | 'quiz'

export type EmployeesByCourseFilters = {
  progress_group?: ProgressGroup[]
  process_group?: ProcessGroup[]
  is_not_registered?: boolean
  newbie?: boolean
}

export type EmployeesByCourseRequest = RequestWithPagination &
  EmployeesByCourseFilters & {
    assigned_course_id: string
    sort_by?: EmployeesByCourseSortBy
    sort_order?: EmployeesByCourseSortOrder
  }

export type AssignedUser = {
  user: User
  assigned_course_id: string
  assigned_at: string
  progress: Progress
  statistics: Statistics
  group: ProgressGroup
  passed: boolean
  finished_at: string
}

export type User = {
  id: string
  full_name: string
  email: string
  department_title: string
  position: string
  tags: AssignedUserTag[]
}

export type AssignedUserTag = {
  title: string
  color: string
}

export type Progress = {
  passed_themes_count: number
  total_themes_count: number
}

export type Statistics = {
  theory: number
  quiz: number
  overall: number
}

export type AddEmployeesRequest = {
  id: string
  body: {
    users: string[]
    departments: string[]
    exclude_users_ids: string[]
  }
}

export type DeleteEmployeesRequest = {
  id: string
  params?: EmployeesByCourseFilters
  body: {
    users: string[]
    exclude_users_ids: string[]
    need_all: boolean
  }
}

export type NotifyEmployeesRequest = {
  id: string
  params?: EmployeesByCourseFilters
  body: {
    users: string[]
    exclude_users_ids: string[]
    need_all: boolean
    message: string
  }
}

export type Filters = {
  status?: ProcessGroup[] | undefined
  education?: ProgressGroup[] | undefined
}
