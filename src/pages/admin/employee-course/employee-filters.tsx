import { Modal } from '@/shared/components'
import { But<PERSON>, Checkbox } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './employee-filters.module.scss'
import { FC, useMemo } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { Filters, ProcessGroup, ProgressGroup } from './types'

const cx = classNamesBind.bind(styles)

type FormValues = Filters

type Props = {
  initialForm?: FormValues
  open: boolean
  setOpen: (v: boolean) => void
  onSubmit?: (v: FormValues) => void
  onReset?: (v: FormValues) => void
}

export const EmployyesFiltersWithModal: FC<Props> = ({ initialForm, open, setOpen, ...props }) => {
  const { t } = useTranslation('pages__assigned-course-detail-statistics')
  const form = useForm<FormValues>({ values: initialForm })

  const LEARNING_LIST = useMemo(
    () => [
      { id: 'normal', title: t('commons:normal_plan') },
      { id: 'behind', title: t('commons:behind_plan') },
    ],
    [t],
  )

  const STATUS_LIST = useMemo(
    () => [
      { id: 'completed', title: t('filters.status.end_course') },
      { id: 'in_process', title: t('filters.status.not-end_course') },
      { id: 'not_started', title: t('filters.status.not-start_course') },
    ],
    [t],
  )

  const onSubmit: SubmitHandler<FormValues> = data => {
    props?.onSubmit?.(data)
  }

  const onClose = () => {
    setOpen(false)
  }

  return (
    <>
      <Modal
        setActive={v => {
          onClose()
          setOpen(!!v)
        }}
        active={open}
        className={cx('modal')}
      >
        <form className={cx('wrapper')} onSubmit={form.handleSubmit(onSubmit)}>
          <h3 className={cx('title')}>{t('filters.modal_title')}</h3>
          <div className={cx('filters')}>
            <div className={cx('filter__item')}>
              <h4 className={cx('title_small')}>{t('filters.status.title')}</h4>

              <ul className={cx('filter__item__list')}>
                {STATUS_LIST.map(i => {
                  const isChecked = form.watch('status')?.includes(i.id as ProcessGroup)
                  return (
                    <li key={i.id}>
                      <Checkbox
                        className={cx('checkbox')}
                        customChecked={isChecked}
                        onChange={() => {
                          if (isChecked) {
                            form.setValue(
                              'status',
                              form.watch('status')?.filter(v => v !== i.id),
                            )
                          } else {
                            form.setValue('status', [
                              ...(form.watch().status ?? []),
                              i.id as ProcessGroup,
                            ])
                          }
                        }}
                        label={<span className={cx('checkbox__text')}>{i.title}</span>}
                      />
                    </li>
                  )
                })}
              </ul>
            </div>
            <div className={cx('filter__item')}>
              <h4 className={cx('title_small')}>{t('education')}</h4>

              <ul className={cx('filter__item__list')}>
                {LEARNING_LIST.map(i => {
                  const isChecked = form.watch('education')?.includes(i.id as ProgressGroup)

                  return (
                    <li key={i.id}>
                      <Checkbox
                        className={cx('checkbox')}
                        customChecked={isChecked}
                        onChange={() => {
                          if (isChecked) {
                            form.setValue(
                              'education',
                              form.watch('education')?.filter(v => v !== i.id),
                            )
                          } else {
                            form.setValue('education', [
                              ...(form.watch().education ?? []),
                              i.id as ProgressGroup,
                            ])
                          }
                        }}
                        label={<span className={cx('checkbox__text')}>{i.title}</span>}
                      />
                    </li>
                  )
                })}
              </ul>
            </div>
          </div>
          <div className={cx('actions')}>
            <Button
              onClick={() => {
                form.reset({ education: [], status: [] })
                props?.onReset?.(form.watch())
              }}
              type='button'
              fullWidth
              color='gray'
            >
              {t('filters.reset')}
            </Button>
            <Button
              disabled={!Object.values(form.watch() ?? {}).some(filter => filter?.find(Boolean))}
              type='submit'
              fullWidth
            >
              {t('filters.apply')}
            </Button>
          </div>
        </form>
      </Modal>
    </>
  )
}
