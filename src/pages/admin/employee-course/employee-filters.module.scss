.wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: flex-start;
  width: 100%;
  height: 100%;
}

.title{
  color: var(--color-gray-70);

  &_small{
    font: var(--font-text-2-medium);
    color: var(--color-gray-100);
  }
}

.checkbox{
  display: flex;
  gap: 12px;
  &__text{
    font: var(--font-text-2-normal);
    color: var(--color-gray-100);
  }
}

.filters{
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  justify-content: space-between;
  width: 100%;

  &__actions{
    margin-left: auto;
    font: var(--font-text-2-medium);
    color: var(--color-gray-80);
    text-transform: capitalize;
    display: flex;
    align-items: center;
    gap: 4px;
    width: auto;
    margin-bottom: 12px;
  }
}

.filter__item{
  display: flex;
  flex-direction: column;
  gap: 12px;

  &__list{
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.modal{
  max-width: 500px;
  width: 100%;
  max-height: 336px;
  height: 100%;
}

.actions{
  width: 100%;
  display: flex;
  gap: 16px;
  margin-top: auto;
}

.textarea {
  width: 100%;
}