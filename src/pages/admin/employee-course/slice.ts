import { createSlice, PayloadAction, WithSlice } from '@reduxjs/toolkit'
import { Filters, EmployeesByCourseSortBy, EmployeesByCourseSortOrder } from './types'
import { rootReducer } from '@/store/reducer'
import { ITargetData } from '@/entities/target'

type InitialState = {
  filters: Filters
  selectedUsers: string[]
  excludedUsers: string[]
  completeUserCourseModalOpen: boolean
  filtersModalOpen: boolean
  deleteModalOpen: boolean
  notificationModalOpen: boolean
  assignNotificationModalOpen: boolean
  isAllSelected: boolean
  organizationTreeOpen: boolean
  organizationTreeTargets: ITargetData
  sort: {
    by: EmployeesByCourseSortBy
    order?: EmployeesByCourseSortOrder
  }
  totalCount: number
}

const initialState: InitialState = {
  filters: {
    education: undefined,
    status: undefined,
  },
  selectedUsers: [],
  excludedUsers: [],
  totalCount: 0,
  filtersModalOpen: false,
  isAllSelected: false,
  organizationTreeOpen: false,
  organizationTreeTargets: {
    target_departments: [],
    target_users: [],
    all_users_ids: [],
    exclude_users_ids: [],
  },
  completeUserCourseModalOpen: false,
  assignNotificationModalOpen: false,
  notificationModalOpen: false,
  deleteModalOpen: false,
  sort: {
    by: 'full_name',
    order: undefined,
  },
}

export const detailStatisticsSlice = createSlice({
  name: 'detailStatistics',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Filters>) => {
      state.filters = action.payload
    },
    setSelectedUsers: (state, action: PayloadAction<UUID[]>) => {
      state.selectedUsers = action.payload
    },
    setExcludedUsers: (state, action: PayloadAction<UUID[]>) => {
      state.excludedUsers = action.payload
    },
    resetSelected: state => {
      state.selectedUsers = []
      state.excludedUsers = []
    },
    setTotalCount: (state, action: PayloadAction<number>) => {
      state.totalCount = action.payload ?? 0
    },
    handleUserSelection(state, action: PayloadAction<{ employeeId: string; selected: boolean }>) {
      const { employeeId, selected } = action.payload
      if (state.isAllSelected) {
        const excludeIndex = state.excludedUsers.indexOf(employeeId)
        if (selected && excludeIndex !== -1) {
          state.excludedUsers.splice(excludeIndex, 1)
        } else if (!selected && excludeIndex === -1) {
          state.excludedUsers.push(employeeId)
        }
      } else {
        const checkedIndex = state.selectedUsers.indexOf(employeeId)
        if (selected && checkedIndex === -1) {
          state.selectedUsers.push(employeeId)
        } else if (!selected && checkedIndex !== -1) {
          state.selectedUsers.splice(checkedIndex, 1)
        }
      }
    },
    setFiltersModalOpen: (state, action: PayloadAction<boolean>) => {
      state.filtersModalOpen = action.payload
    },
    setIsAllSelected: (state, action: PayloadAction<boolean>) => {
      state.isAllSelected = action.payload
    },
    setOrganizationTreeOpen: (state, action: PayloadAction<boolean>) => {
      state.organizationTreeOpen = action.payload
    },
    setOrganizationTreeTargets: (state, action: PayloadAction<ITargetData>) => {
      state.organizationTreeTargets = action.payload
    },
    setCompleteUserCourseModalOpen: (state, action: PayloadAction<boolean>) => {
      state.completeUserCourseModalOpen = action.payload
    },
    setAssignNotificationModalOpen: (state, action: PayloadAction<boolean>) => {
      state.assignNotificationModalOpen = action.payload
    },
    setNotificationModalOpen: (state, action: PayloadAction<boolean>) => {
      state.notificationModalOpen = action.payload
    },
    setDeleteModalOpen: (state, action: PayloadAction<boolean>) => {
      state.deleteModalOpen = action.payload
    },
    setSortBy: (state, action: PayloadAction<EmployeesByCourseSortBy>) => {
      state.sort.by = action.payload
    },
    setSortOrder: (state, action: PayloadAction<EmployeesByCourseSortOrder>) => {
      state.sort.order = action.payload
    },
    resetSlice: () => initialState,
  },
  selectors: {
    selectSelectedUsers: state => state.selectedUsers,
    selectExcludedUsers: state => state.excludedUsers,
    selectFilters: state => state.filters,
    selectFiltersModalOpen: state => state.filtersModalOpen,
    selectIsAllSelected: state => state.isAllSelected,
    selectOrganizationTreeOpen: state => state.organizationTreeOpen,
    selectOrganizationTreeTargets: state => state.organizationTreeTargets,
    selectActionsVisible: state => {
      if (state.isAllSelected) return true
      if (state.selectedUsers.length > 0) return true
      return false
    },
    selectCompleteUserCourseModalOpen: state => state.completeUserCourseModalOpen,
    selectNotificationModalOpen: state => state.notificationModalOpen,
    selectAssignNotificationModalOpen: state => state.assignNotificationModalOpen,
    selectDeleteModalOpen: state => state.deleteModalOpen,
    selectSortBy: state => state.sort.by,
    selectSortOrder: state => state.sort.order,
    selectSelectedUsersCount: state => {
      if (!state.totalCount) return 0

      if (state.isAllSelected) {
        return state.totalCount - state.excludedUsers?.length
      }

      return state.selectedUsers?.length
    },
    selectFiltersExists: state =>
      Object.values(state.filters).some(filter => filter?.find(Boolean)),
  },
})

declare module '@/store/reducer' {
  export interface LazyLoadedSlices extends WithSlice<typeof detailStatisticsSlice> {}
}

const injectedDetailStatisticsSlice = detailStatisticsSlice.injectInto(rootReducer)

export const {
  handleUserSelection,
  setFilters,
  setFiltersModalOpen,
  setIsAllSelected,
  setOrganizationTreeOpen,
  setOrganizationTreeTargets,
  resetSelected,
  setNotificationModalOpen,
  setCompleteUserCourseModalOpen,
  setAssignNotificationModalOpen,
  setDeleteModalOpen,
  setSortBy,
  setSortOrder,
  setTotalCount,
  resetSlice,
  setExcludedUsers,
  setSelectedUsers,
} = injectedDetailStatisticsSlice.actions
export const {
  selectFilters,
  selectSelectedUsers,
  selectExcludedUsers,
  selectFiltersModalOpen,
  selectIsAllSelected,
  selectOrganizationTreeOpen,
  selectOrganizationTreeTargets,
  selectActionsVisible,
  selectNotificationModalOpen,
  selectCompleteUserCourseModalOpen,
  selectAssignNotificationModalOpen,
  selectDeleteModalOpen,
  selectSortBy,
  selectSortOrder,
  selectSelectedUsersCount,
  selectFiltersExists,
} = injectedDetailStatisticsSlice.selectors
