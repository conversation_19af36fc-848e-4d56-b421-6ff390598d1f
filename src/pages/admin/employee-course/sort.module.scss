.wrapper {
  cursor: pointer;

  padding: 4px;
  position: relative;
  .text {
    align-items: center;

    color: var(--color-gray-80, #5c6585);
    display: flex;
    font: var(--font-text-2-medium);
    letter-spacing: 0.13px;
  }
}

.listWrapper {
  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 8px;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
  max-width: max-content;
  overflow: -moz-scrollbars-none;
  -ms-overflow-style: none;
  overflow-y: scroll;
  padding: 8px 0;
  position: absolute;
  right: 0;
  top: 100%;
  transform: translateY(4px);

  width: max-content;
  z-index: 10;

  &::-webkit-scrollbar {
    width: 0;
  }

  .listInner {
    height: max-content;
  }
  .listItem {
    color: var(--color-gray-80, #5c6585);

    cursor: pointer;

    display: block;
    font: var(--font-text-2-normal);
    padding: 8px 16px;

    transition: var(--transition);
    &:hover {
      background: var(--color-gray-40, #f0f3f7);

      transition: var(--transition);
    }
    &.active {
      background: var(--color-gray-40, #f0f3f7);

      transition: var(--transition);
    }
  }
}
