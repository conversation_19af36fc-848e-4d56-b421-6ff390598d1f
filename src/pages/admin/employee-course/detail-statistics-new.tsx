import { useEffect, useMemo, useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Checkbox, Pagination, Select } from '@/shared/ui'
import StopCircleIcon from '@/shared/ui/Icon/icons/components/StopCircleIcon'
import EmailPostedIcon from '@/shared/ui/Icon/icons/components/EmailPostedIcon'
import EmailCloseMediumIcon from '@/shared/ui/Icon/icons/components/EmailCloseMediumIcon'
import TrashSmallIcon from '@/shared/ui/Icon/icons/components/TrashSmallIcon'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import MoreIcon from '@/shared/ui/Icon/icons/components/MoreIcon'
import FilterIcon from '@/shared/ui/Icon/icons/components/FilterIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import {
  getAssignedCoursesDetailStatisticsByIdUrl,
  getEmployeeDetailsByIdUrl,
  URLS,
} from '@/shared/configs/urls'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './detail-statistics-new.module.scss'
import { EmployeeCard } from './employee-card'
import { EmployyesFiltersWithModal } from './employee-filters'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  resetSlice,
  selectActionsVisible,
  selectDeleteModalOpen,
  selectFilters,
  selectFiltersExists,
  selectFiltersModalOpen,
  selectIsAllSelected,
  selectNotificationModalOpen,
  selectOrganizationTreeOpen,
  selectSelectedUsersCount,
  selectSortBy,
  selectSortOrder,
  setDeleteModalOpen,
  setFilters,
  setFiltersModalOpen,
  setIsAllSelected,
  setNotificationModalOpen,
  setAssignNotificationModalOpen,
  setOrganizationTreeOpen,
  setOrganizationTreeTargets,
  resetSelected,
  setCompleteUserCourseModalOpen,
  handleUserSelection,
  selectSelectedUsers,
  selectExcludedUsers,
  setTotalCount,
} from './slice'
import { OrganizationTree, OrganizationTreeProvider } from '@/shared/modals/organization-tree'
import { OverflowMenu, OverflowProvider } from '@/shared/components/overflow'
import { assignedCoursesEmployyesApi } from './api'
import { useNavigate, useParams } from 'react-router-dom'
import Skeleton from 'react-loading-skeleton'
import { NofifyEmployyes } from '@/shared/modals/nofify-employyes'
import { ConfirmModal } from '@/shared/modals/confirm-modal'
import { useNotification } from '@/shared/contexts/notifications'
import { v4 as uuid } from 'uuid'
import { EmployeesSort } from './sort'
import { AssignNotifyModal } from '@/entities/courses/ui/assign-notify-modal/assign-notify-modal'
import { CompleteUserCourseModal } from './complete-user-course-modal/complete-user-course-modal'

const cx = classNamesBind.bind(styles)

export const SendEmployeesNotifyModal = () => {
  const { t } = useTranslation('pages__assigned-course-detail-statistics')
  const dispatch = useAppDispatch()
  const selectedUsers = useAppSelector(selectSelectedUsers)
  const excludedUsers = useAppSelector(selectExcludedUsers)
  const isAllSelected = useAppSelector(selectIsAllSelected)
  const selectedUsersCount = useAppSelector(selectSelectedUsersCount)
  const [notifyMutation, { isLoading }] =
    assignedCoursesEmployyesApi.useNotifyEmployyesFromTableMutation()
  const { course_id = '' } = useParams()
  const { add } = useNotification()
  const filters = useAppSelector(selectFilters)

  const notificationModalOpen = useAppSelector(selectNotificationModalOpen)

  if (!notificationModalOpen) return null

  return (
    <NofifyEmployyes
      title={t('notify.title')}
      textPlaceholder={t('notify.textarea_placeholder')}
      count={selectedUsersCount}
      setOpen={v => dispatch(setNotificationModalOpen(v))}
      open={notificationModalOpen}
      cancel={{
        text: t('notify.cancel'),
      }}
      submit={{
        text: t('notify.send'),
        loading: isLoading,
      }}
      onSubmit={data => {
        notifyMutation({
          id: course_id,
          params: {
            process_group: filters.status,
            progress_group: filters.education,
          },
          body: {
            users: isAllSelected ? [] : selectedUsers,
            need_all: isAllSelected,
            exclude_users_ids: isAllSelected ? excludedUsers : [],
            message: data?.text ?? '',
          },
        }).then(() => {
          add({ id: uuid(), message: t('actions.send_msg.success'), status: 'success' })
          dispatch(setNotificationModalOpen(false))
        })
      }}
    />
  )
}

const Actions = () => {
  const { t } = useTranslation('pages__assigned-course-detail-statistics')
  const actionsVisible = useAppSelector(selectActionsVisible)
  const [deleteEmployyesMutation] = assignedCoursesEmployyesApi.useDeleteEmployeesMutation()
  const selectedUsers = useAppSelector(selectSelectedUsers)
  const excludedUsers = useAppSelector(selectExcludedUsers)
  const { course_id = '' } = useParams()
  const deleteModalOpen = useAppSelector(selectDeleteModalOpen)
  const { add } = useNotification()
  const isAllSelected = useAppSelector(selectIsAllSelected)
  const selectedUsersCount = useAppSelector(selectSelectedUsersCount)
  const dispatch = useAppDispatch()
  const filters = useAppSelector(selectFilters)

  if (!actionsVisible) return null

  return (
    <>
      <CompleteUserCourseModal />
      <SendEmployeesNotifyModal />
      <AssignNotifyModal />
      <ConfirmModal
        wrapperClassname={cx('actions__delete__wrapper')}
        footerClassname={cx('actions__delete__footer')}
        open={deleteModalOpen}
        setOpen={v => dispatch(setDeleteModalOpen(v))}
        title={t('actions.delete.title')}
        description={t('actions.delete.description')}
        onClose={() => {
          dispatch(setDeleteModalOpen(false))
        }}
        onConfirm={() => {
          deleteEmployyesMutation({
            id: course_id,
            params: {
              process_group: filters.status,
              progress_group: filters.education,
            },
            body: {
              users: isAllSelected ? [] : selectedUsers,
              need_all: isAllSelected,
              exclude_users_ids: isAllSelected ? excludedUsers : [],
            },
          }).then(() => {
            add({ id: uuid(), message: t('actions.delete.msg'), status: 'success' })
            dispatch(setDeleteModalOpen(false))
            dispatch(setIsAllSelected(false))
          })
        }}
        closeProps={{
          color: 'red',
          className: cx('actions__padding'),
        }}
        confirmProps={{
          color: 'gray',
          className: cx('actions__padding'),
        }}
      />
      <OverflowProvider
        gapSize={24}
        additionalWidth={168}
        className={cx('actions')}
        dropdownDataId='detail-statistics-actions-dropdown'
        items={[
          {
            id: 'selected-counter',
            content: (
              <div className={cx('actions__counter', 'no-wrap')} key='counter'>
                <span>{t('commons:employees_choosed', { count: selectedUsersCount })}</span>
              </div>
            ),
          },
          {
            id: 'complete-course-by-users',
            content: (
              <button
                onClick={() => dispatch(setCompleteUserCourseModalOpen(true))}
                className={cx('actions__elements__element', 'no-wrap')}
                key='complete'
              >
                <IconWrapper size='20'>
                  <StopCircleIcon />
                </IconWrapper>
                <span>{t('actions.complete_course.action')}</span>
              </button>
            ),
          },
          {
            id: 'notify-action',
            content: (
              <button
                onClick={() => dispatch(setAssignNotificationModalOpen(true))}
                className={cx('actions__elements__element', 'no-wrap')}
                key='notify'
              >
                <IconWrapper size='20'>
                  <EmailPostedIcon />
                </IconWrapper>
                <span>{t('actions.notify.action')}</span>
              </button>
            ),
          },
          {
            id: 'send-message-action',
            content: (
              <button
                onClick={() => dispatch(setNotificationModalOpen(true))}
                className={cx('actions__elements__element', 'no-wrap')}
                key='email'
              >
                <IconWrapper size='20'>
                  <EmailCloseMediumIcon />
                </IconWrapper>
                <span>{t('actions.send_msg.action')}</span>
              </button>
            ),
          },
          {
            id: 'remove-employees-action',
            content: (
              <button
                onClick={() => {
                  dispatch(setDeleteModalOpen(true))
                }}
                className={cx('actions__elements__element', 'actions__delete__text', 'no-wrap')}
                key='delete'
              >
                <IconWrapper size='20'>
                  <TrashSmallIcon />
                </IconWrapper>
                <span>{t('actions.delete.action')}</span>
              </button>
            ),
          },
          {
            id: 'cancel-action',
            content: (
              <button
                onClick={() => {
                  dispatch(resetSelected())
                  dispatch(setIsAllSelected(false))
                }}
                className={cx('actions__elements__element', 'actions__cancel__text', 'no-wrap')}
                key='cancel'
              >
                <IconWrapper color='red' size='20'>
                  <CloseBoldIcon />
                </IconWrapper>
                <span className={cx('warn')}>{t('actions.cancel')}</span>
              </button>
            ),
          },
        ]}
      >
        <OverflowMenu
          renderDropdown={items => (
            <>
              <Select
                disableActiveValue={true}
                listClassName={cx('select__list')}
                renderLabel={() => (
                  <button
                    data-id='detail-statistics-actions-dropdown'
                    className={cx('actions__elements__element', 'no-wrap')}
                  >
                    <span>{t('actions.more')}</span>
                    <IconWrapper size='20'>
                      <MoreIcon />
                    </IconWrapper>
                  </button>
                )}
                wrapperClassName={cx('select')}
                withoutIcon={true}
                list={items?.map(i => ({ id: String(i.id), title: '', content: i.content }))}
              />
            </>
          )}
        />
      </OverflowProvider>
    </>
  )
}

const LIMIT = 20

const DetailStatistics = () => {
  const { t } = useTranslation('pages__assigned-course-detail-statistics')
  const { course_id = '' } = useParams()
  const [page, setPage] = useState(1)
  const dispatch = useAppDispatch()
  const filters = useAppSelector(selectFilters)
  const filtersExists = useAppSelector(selectFiltersExists)
  const filtersModalOpen = useAppSelector(selectFiltersModalOpen)
  const selectedUsers = useAppSelector(selectSelectedUsers)
  const excludedUsers = useAppSelector(selectExcludedUsers)
  const isAllSelected = useAppSelector(selectIsAllSelected)
  const organizationTreeOpen = useAppSelector(selectOrganizationTreeOpen)
  const { data: course } = assignedCoursesEmployyesApi.useGetAssignedCourseQuery(course_id)
  const activeSortOrder = useAppSelector(selectSortOrder)
  const activeSortBy = useAppSelector(selectSortBy)
  const navigate = useNavigate()

  const {
    data: employees,
    isLoading,
    isFetching,
  } = assignedCoursesEmployyesApi.useGetAssignedEmployeesByCourseIdQuery({
    assigned_course_id: course_id,
    limit: LIMIT,
    offset: (page - 1) * LIMIT,
    process_group: filters?.status,
    progress_group: filters?.education,
    sort_by: activeSortBy,
    sort_order: activeSortOrder,
  })

  const { add } = useNotification()
  const [addEmployeesMutation] = assignedCoursesEmployyesApi.useAddEmployeesMutation()

  useEffect(() => {
    dispatch(setTotalCount(employees?.total_count ?? 0))
  }, [dispatch, employees?.total_count])

  useEffect(() => {
    return () => {
      dispatch(resetSlice())
    }
  }, [dispatch])

  const breadcrumbItems = useMemo(
    () => [
      {
        id: URLS.ADMIN_LEARNING_PAGE,
        text: t('breadcrumbs.assigned-courses'),
        clickable: true,
      },
      {
        id: course?.id
          ? getAssignedCoursesDetailStatisticsByIdUrl(course?.id)
          : URLS.ADMIN_LEARNING_COURSES_PAGE,
        text: course?.title ?? '',
        clickable: true,
        isLoading: !course?.title,
      },
      {
        id: URLS.ADMIN_CREATE_COURSE_PAGE,
        text: t('breadcrumbs.employees'),
        clickable: false,
      },
    ],
    [t, course],
  )

  return (
    <div className={cx('page')}>
      <Actions />
      <div className={cx('header')}>
        <div>
          <Breadcrumbs items={breadcrumbItems} />
          {course?.title ? (
            <>
              <h2>{t('title.first')}</h2>
              <p>
                {t('title.second')} {`«${course?.title}»`}
              </p>
            </>
          ) : (
            <Skeleton height={30} count={2} width={380} />
          )}
        </div>
        <OrganizationTreeProvider
          handleSelect={data => {
            dispatch(
              setOrganizationTreeTargets({
                target_departments: data?.department_ids,
                target_users: !data?.selectAll ? data?.users_ids : [],
                exclude_users_ids: data?.selectAll ? data?.users_ids : [],
              }),
            )
            addEmployeesMutation({
              id: course_id,
              body: {
                departments: data?.department_ids ?? [],
                users: !data?.selectAll ? data?.users_ids : [],
                exclude_users_ids: data?.selectAll ? data?.users_ids : [],
              },
            }).then(() => add({ id: uuid(), message: t('add_employyes_msg'), status: 'success' }))
          }}
          open={organizationTreeOpen}
          setOpen={v => dispatch(setOrganizationTreeOpen(v))}
          requiredIds={employees?.data?.map(u => u.user?.id)}
        >
          <OrganizationTree />
        </OrganizationTreeProvider>
        <Button
          onClick={() => {
            dispatch(setOrganizationTreeOpen(true))
          }}
          size='big'
        >
          {t('commons:add_employee')}
        </Button>
      </div>
      {filtersModalOpen && (
        <EmployyesFiltersWithModal
          open={filtersModalOpen}
          setOpen={v => dispatch(setFiltersModalOpen(v))}
          onSubmit={v => {
            dispatch(setFilters(v))
            dispatch(setFiltersModalOpen(false))
            setPage(1)
          }}
          initialForm={filters}
          onReset={() => dispatch(setFilters({ education: undefined, status: undefined }))}
        />
      )}
      {employees && (
        <section className={cx('content')}>
          <div className={cx('filters__wrapper')}>
            {filtersExists && (
              <Button
                size='small'
                color='gray'
                onClick={() => {
                  dispatch(setFilters({}))
                }}
              >
                {t('filters.reset')}
              </Button>
            )}
            <Button
              size='small'
              color='gray'
              onClick={() => {
                dispatch(setFiltersModalOpen(true))
              }}
              className={cx('filters__actions', filtersExists ? 'filters__actions__primary' : '')}
            >
              <span>{t('filters.phrase')}</span>
              <IconWrapper size='20' color={'gray80'}>
                <FilterIcon />
              </IconWrapper>
            </Button>
          </div>
          {employees.data.length === 0 && (
            <div className={cx('empty')}>{t('commons:not_find_such_employees')} :(</div>
          )}
          {employees.data.length > 0 && (
            <div className={cx('employees__wrapper')}>
              <div className={cx('employees__header')}>
                <div className={cx('selectAll')}>
                  <Checkbox
                    onChange={checked => {
                      dispatch(setIsAllSelected(checked))
                      if (checked) {
                        dispatch(resetSelected())
                      }
                    }}
                    customChecked={isAllSelected}
                    label={
                      <span className={cx('employees__header__checkbox__text')}>
                        {t('commons:select_all')}
                      </span>
                    }
                    variant='center'
                  />
                </div>
                <EmployeesSort />
              </div>

              {employees?.data?.map(e => (
                <EmployeeCard
                  key={e.user.id}
                  onChecked={v =>
                    dispatch(handleUserSelection({ selected: v, employeeId: e.user.id }))
                  }
                  isChecked={
                    selectedUsers.includes(e.user.id) ||
                    (isAllSelected && !excludedUsers.includes(e.user.id))
                  }
                  department={e.user?.department_title}
                  email={e?.user?.email}
                  fullname={e?.user?.full_name}
                  theory={t('progress.theory', { count: Number(e?.statistics.theory.toFixed()) })}
                  testing={t('progress.testing', { count: Number(e?.statistics.quiz.toFixed()) })}
                  position={e?.user?.position}
                  status={e.group}
                  statusText={t(`progress.${e.group}`)}
                  tags={e.user.tags}
                  goToTheEmployee={() => navigate(getEmployeeDetailsByIdUrl(e.user.id))}
                />
              ))}
            </div>
          )}
          {employees && (
            <Pagination
              limit={LIMIT}
              currentPage={page - 1}
              total={employees?.total_count ?? 0}
              onChange={p => setPage(p + 1)}
              withEmptySpace
              isLoading={isLoading || isFetching}
            />
          )}
        </section>
      )}
    </div>
  )
}

export default DetailStatistics
