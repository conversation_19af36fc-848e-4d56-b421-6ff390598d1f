import { globalBaseApi } from '@/store/services/endpoints/base'
import {
  AddEmployeesRequest,
  ContentCourse,
  DeleteEmployeesRequest,
  EmployeesByCourseRequest,
  EmployeesByCourseResponse,
  NotifyEmployeesRequest,
} from './types'
import { isNumber } from '@/shared/helpers'

export const assignedCoursesEmployyesApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getAssignedEmployeesByCourseId: builder.query<
      EmployeesByCourseResponse,
      EmployeesByCourseRequest
    >({
      query: ({ assigned_course_id, ...queries }) => {
        const params = new URLSearchParams()
        if (queries.is_not_registered)
          params.append('is_not_registered', String(queries.is_not_registered))

        if (queries.newbie) params.append('newbie', String(queries.newbie))
        if (queries.limit) params.append('limit', String(queries.limit))
        if (isNumber(queries.offset)) params.append('offset', String(queries.offset))
        if (queries.sort_by) params.append('sort_by', String(queries.sort_by))
        if (queries.sort_order) params.append('sort_order', String(queries.sort_order))

        if (queries.progress_group && queries.progress_group.length > 0) {
          for (const group of queries.progress_group) {
            params.append('progress_groups', group)
          }
        }
        if (queries.process_group && queries.process_group.length > 0) {
          for (const group of queries.process_group) {
            params.append('process_groups', group)
          }
        }

        return {
          url: `/learning/api/learning/assigned-courses/${assigned_course_id}/employees`,
          params,
        }
      },
      providesTags: ['assigned-courses-employees'],
    }),
    getAssignedCourse: builder.query<ContentCourse, string>({
      query: id => ({
        url: '/learning/api/learning/assigned-courses/' + id,
      }),
    }),
    addEmployees: builder.mutation<unknown, AddEmployeesRequest>({
      query: ({ id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${id}/employees`,
        body,
        method: 'POST',
      }),
    }),
    deleteEmployees: builder.mutation<unknown, DeleteEmployeesRequest>({
      query: ({ id, params: queries, body }) => {
        const params = new URLSearchParams()
        if (queries?.is_not_registered)
          params.append('is_not_registered', String(queries.is_not_registered))

        if (queries?.newbie) params.append('newbie', String(queries.newbie))

        if (queries?.progress_group && queries.progress_group.length > 0) {
          for (const group of queries.progress_group) {
            params.append('progress_groups', group)
          }
        }
        if (queries?.process_group && queries.process_group.length > 0) {
          for (const group of queries.process_group) {
            params.append('process_groups', group)
          }
        }

        return {
          url: `/learning/api/learning/assigned-courses/${id}/employees/delete`,
          body,
          params,
          method: 'POST',
        }
      },
    }),
    notifyEmployyesFromTable: builder.mutation<unknown, NotifyEmployeesRequest>({
      query: ({ id, params: queries, body }) => {
        const params = new URLSearchParams()
        if (queries?.is_not_registered)
          params.append('is_not_registered', String(queries.is_not_registered))

        if (queries?.newbie) params.append('newbie', String(queries.newbie))

        if (queries?.progress_group && queries.progress_group.length > 0) {
          for (const group of queries.progress_group) {
            params.append('progress_groups', group)
          }
        }
        if (queries?.process_group && queries.process_group.length > 0) {
          for (const group of queries.process_group) {
            params.append('process_groups', group)
          }
        }

        return {
          url: `/learning/api/learning/assigned-courses/${id}/employees/message`,
          body,
          params,
          method: 'POST',
        }
      },
    }),
  }),
  overrideExisting: true,
})
