import { useCallback, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { EmployeesByCourseSortBy } from './types'
import { useEvent } from '@/shared/hooks'
import classNamesBind from 'classnames/bind'
import styles from './sort.module.scss'
import { useAppDispatch, useAppSelector } from '@/store'
import { selectSortBy, selectSortOrder, setSortBy, setSortOrder } from './slice'
import ChevroneSmallIcon from '@/shared/ui/Icon/icons/components/ChevroneSmallIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { IconSortDirection } from '@/shared/components/icon-sort-direction'

const cx = classNamesBind.bind(styles)

export const EmployeesSort = () => {
  const { t } = useTranslation('pages__assigned-course-detail-statistics')
  const [open, setOpen] = useState(false)
  const wrapper = useRef<HTMLDivElement>(null)
  const dispatch = useAppDispatch()
  const activeSortOrder = useAppSelector(selectSortOrder)
  const activeSortBy = useAppSelector(selectSortBy)
  const SORT_LABELS: Record<EmployeesByCourseSortBy, string> = useMemo(
    () => ({
      full_name: t('filters.sort_by.full_name'),
      quiz: t('filters.sort_by.quiz'),
      theory: t('filters.sort_by.theory'),
    }),
    [t],
  )

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) setOpen(false)
  }, [])

  useEvent('click', handleOutsideClick, window)

  return (
    <div className={cx('wrapper')} ref={wrapper} onClick={() => setOpen(!open)}>
      <div className={cx('text')}>
        {activeSortBy && SORT_LABELS[activeSortBy]}
        <IconWrapper color='gray80' className={cx('icon')} direction={!open ? 'right' : 'left'}>
          <ChevroneSmallIcon />
        </IconWrapper>
        <IconSortDirection
          startDirection={'asc'}
          direction={activeSortOrder}
          onChange={(dir, e) => {
            dispatch(setSortOrder(dir))
            e?.stopPropagation()
          }}
        />
      </div>
      {open && (
        <div className={cx('listWrapper')} style={styles} onClick={e => e.stopPropagation()}>
          <div className={cx('listInner')}>
            {Object.keys(SORT_LABELS).map(s => {
              const sort = s as EmployeesByCourseSortBy
              const isActive = sort === activeSortBy

              return (
                <span
                  key={`list-item-${s}`}
                  className={cx('listItem', { active: isActive })}
                  onClick={() => {
                    dispatch(setSortBy(sort))
                    setOpen(false)
                  }}
                >
                  {SORT_LABELS[sort]}
                </span>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
