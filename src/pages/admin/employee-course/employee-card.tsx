import classNamesBind from 'classnames/bind'
import styles from './employees.module.scss'
import { ButtonIcon, Checkbox } from '../../../shared/ui'
import { FC } from 'react'
import { ProgressGroup } from './types'

const cx = classNamesBind.bind(styles)

type Props = {
  className?: string
  goToTheEmployee?: () => void
  isChecked?: boolean
  onChecked?: (v: boolean) => void
  fullname: string
  email: string
  theory: string
  testing: string
  department: string
  position: string
  status: ProgressGroup
  statusText: string
  tags?: { color: string; title: string }[]
}

export const EmployeeCard: FC<Props> = ({
  className,
  goToTheEmployee,
  isChecked,
  onChecked,
  department,
  email,
  fullname,
  position,
  status,
  statusText,
  testing,
  theory,
  tags,
}) => {
  const checked = isChecked ?? false

  return (
    <div className={cx('card', className)}>
      <Checkbox customChecked={checked} onChange={() => onChecked?.(!checked)} />
      <div className={cx('description__employee')}>
        <p className={cx('description__employee__name')}>{fullname}</p>
        <p className={cx('description__employee__email')}>{email}</p>
        <div className={cx('description__employee__progress')}>
          <span>{theory}</span>
          <span>{testing}</span>
        </div>
      </div>
      <div className={cx('description__general')}>
        <p className={cx('description__general__name')}>{department}</p>
        <p className={cx('description__general__position')}>{position}</p>
        {tags && tags.length > 0 && (
          <div className={cx('tags')}>
            {tags.map(tag => (
              <span key={tag.title} className={cx('tag')} style={{ background: tag.color }}>
                {tag.title}
              </span>
            ))}
          </div>
        )}
      </div>
      <p className={cx('status', `status_${status}`)}>{statusText}</p>
      <ButtonIcon
        onClick={goToTheEmployee}
        iconSize='16'
        className={cx('go-to-employee')}
        icon='chevroneBold'
        color='gray70'
      />
    </div>
  )
}
