import { useTranslation } from 'react-i18next'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Button,
  HelpIcon,
  IListItem,
  Input,
  MultiSelect,
  PageTitle,
  Switch,
  TabSelector,
  Textarea,
} from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './create-campaign.module.scss'
import { useCallback, useEffect, useMemo } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  CreateCampaignByEndingForm,
  selectCreateCampaignByEnding,
  selectCreateCampaignByEndingForm,
  // selectCreateCampaignForm,
  setCreateCampaign,
  setCreateCampaignByEnding,
  setPostCampaignForm,
} from '@/store/slices/phishing'
import { SelectCourseCard, SelectTemplateCard } from '@/shared/components'
import { CustomTagsSelect } from '@/shared/components/custom-tags-select'
import { useCampaign } from './use-campaign'
// import { getPrettyDate, subtractOneDay } from '@/shared/helpers/date'
import { TDateType, TEmailSendEndType, TTargetAutoCourseType } from './config'
import { useNotification } from '@/shared/contexts/notifications'
import { v4 as uuid } from 'uuid'
import { URLS } from '@/shared/configs/urls'
import { SelectRedirectPage } from '@/shared/components/select-redirect-page'
import { useNavigate } from 'react-router-dom'
import { SelectDateCardWithInput } from '@/shared/components/SelectDateCard/select-date-card-with-input'

const cx = classNamesBind.bind(styles)

export const SettingsCampaignByEnding = () => {
  const { t } = useTranslation('pages__phishing')

  const { DATE_EMAIL_SEND_END_TYPES, DATE_TYPES, TARGET_AUTO_COURSE_LIST } = useCampaign({
    onlyAuto: true,
  })

  const dispatch = useAppDispatch()
  const campaignByEnding = useAppSelector(selectCreateCampaignByEndingForm)
  const campaignByEndingHelpers = useAppSelector(selectCreateCampaignByEnding)
  const { add } = useNotification()
  const navigate = useNavigate()

  const {
    watch,
    setValue,
    handleSubmit,
    register,
    setError,
    formState: { errors, isValid },
  } = useForm<CreateCampaignByEndingForm>({
    mode: 'all',
    reValidateMode: 'onChange',
    defaultValues: campaignByEnding,
  })

  useEffect(() => {
    const subscription = watch(value => {
      const changedValue = value as unknown as CreateCampaignByEndingForm
      dispatch(setPostCampaignForm({ ...changedValue }))
    })
    return () => subscription.unsubscribe()
  }, [dispatch, watch])

  const breadcrumbItems = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/campaigns',
        text: t('commons:campaigns'),
        clickable: true,
      },
      {
        id: URLS.ADMIN_PHISHING_CAMPAIGNS_CREATE_PAGE,
        text: t('creating_new_campaign'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/campaigns',
        text: t('creating_new_campaign_by_ending_breadcrumb'),
        clickable: false,
      },
    ],
    [t],
  )

  const targetsMessage = watch('targetsMessage')
  const targetsAutoCourse = watch('targetsAutoCourse')

  const renderMessageTargets = useCallback(() => {
    const renderedTargets = TARGET_AUTO_COURSE_LIST.filter(target =>
      targetsMessage?.includes(target.id),
    )

    return renderedTargets.map(
      (target, index) => `${target.title}${index + 1 < renderedTargets.length ? ', ' : ''}`,
    )
  }, [TARGET_AUTO_COURSE_LIST, targetsMessage])

  const renderAutoCourseTargets = useCallback(() => {
    const renderedTargets = TARGET_AUTO_COURSE_LIST.filter(target =>
      targetsAutoCourse?.includes(target.id),
    )

    return renderedTargets.map(
      (target, index) => `${target.title}${index + 1 < renderedTargets.length ? ', ' : ''}`,
    )
  }, [TARGET_AUTO_COURSE_LIST, targetsAutoCourse])

  const handleTrim = (str: string, name: keyof CreateCampaignByEndingForm, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setError(name, { type: 'custom', message: t('commons:required_field') })
    }
  }
  const name = watch('name')
  const dateDelay = watch('dateDelay')
  const dateEnd = watch('dateEnd')
  const hasMessage = watch('hasMessage')
  const isDelay = !!watch('isDelay')
  const hasAutoCourse = watch('hasAutoCourse')
  const course = watch('course')
  const isTesting = watch('isTesting')
  const templates = watch('templates')
  const theme = watch('theme')
  const text = watch('text')

  const handleChangeTargetsMessage = useCallback(
    (i: IListItem) => {
      if (!targetsMessage) return

      const isIdAlreadyExists = targetsMessage.includes(i.id as TTargetAutoCourseType)

      if (isIdAlreadyExists && targetsMessage.length === 1) return

      if (isIdAlreadyExists) {
        setValue(
          'targetsMessage',
          targetsMessage?.filter(targetId => targetId !== i.id),
        )
      } else {
        setValue('targetsMessage', [...targetsMessage, i.id as TTargetAutoCourseType])
      }
    },
    [setValue, targetsMessage],
  )

  const handleChangeTargetsAutoCourse = useCallback(
    (i: IListItem) => {
      if (!targetsAutoCourse) return

      const isIdAlreadyExists = targetsAutoCourse.includes(i.id as TTargetAutoCourseType)

      if (isIdAlreadyExists && targetsAutoCourse.length === 1) return

      if (isIdAlreadyExists) {
        setValue(
          'targetsAutoCourse',
          targetsAutoCourse?.filter(targetId => targetId !== i.id),
        )
      } else {
        setValue('targetsAutoCourse', [...targetsAutoCourse, i.id as TTargetAutoCourseType])
      }
    },
    [targetsAutoCourse],
  )

  const handleChangeNumber = (value: string, name: keyof CreateCampaignByEndingForm) => {
    if (!isNaN(+value)) setValue(name, `${+value}`)
  }

  const onSubmit: SubmitHandler<CreateCampaignByEndingForm> = () => {
    navigate(URLS.ADMIN_PHISHING_CAMPAIGNS_CREATE_PAGE)
    dispatch(setCreateCampaign({ enableCreateCampaignByEndingSubmit: true }))
  }

  const enableRedirectPage = campaignByEndingHelpers?.enableRedirectPage
  const enableRedirect = watch('enableRedirect')

  const isDisabled =
    !isValid ||
    !templates?.length ||
    (hasAutoCourse && !course) ||
    (hasMessage && (!theme || !text)) ||
    (campaignByEndingHelpers.enableRedirectPage && !watch('redirect_page'))

  const autoGenerate = () => {
    const now = new Date()

    const day = String(now.getDate()).padStart(2, '0')
    const month = String(now.getMonth() + 1).padStart(2, '0') // Месяцы начинаются с 0
    const year = now.getFullYear()
    const hours = String(now.getHours()).padStart(2, '0')
    const minutes = String(now.getMinutes()).padStart(2, '0')

    const currentDate = `${day}.${month}.${year}`
    const currentTime = `${hours}:${minutes}`

    const value = `${t('phishing_campaign')} ${currentDate} ${currentTime}`

    setValue('name', value, { shouldValidate: true })
  }

  return (
    <div className={cx('wrapper')}>
      <Breadcrumbs items={breadcrumbItems} className={cx('breadcrumb')} />
      <div className={cx('inner')}>
        <PageTitle>{t('creating_new_campaign')}</PageTitle>
        <form onSubmit={handleSubmit(onSubmit)} className={cx('formWrapper')}>
          <div className={cx('inputWrapper', 'itemWrapper', 'half')}>
            <Input
              fullWidth
              name='name'
              label={t('campaign_name')}
              placeholder={t('input_new_campaign')}
              error={errors.name?.message}
              register={register('name', {
                required: t('commons:required_field'),
                onBlur: e => handleTrim(e.target.value, 'name', true),
              })}
            />
            {!name && (
              <button onClick={autoGenerate} className={cx('autoGenerate')}>
                {t('auto')}
                <HelpIcon text={t('phishing_campaign_hint')} />
              </button>
            )}
          </div>
          <div className={cx('itemWrapper', 'half')}>
            <CustomTagsSelect
              label={t('exclude_tag')}
              handleChangeTags={tags => setValue('excludedTags', tags)}
              initialSelectedTags={watch('excludedTags') ?? []}
              inputBaseClassName={cx('inputBase')}
            />
          </div>
          <div className={cx('itemWrapper', 'half')}>
            <TabSelector
              label={t('links_ttl')}
              help={t('links_ttl_help')}
              items={DATE_TYPES}
              active={campaignByEndingHelpers.dateType}
              onChange={item => dispatch(setCreateCampaign({ dateType: item.name as TDateType }))}
            />
            {campaignByEndingHelpers.dateType === 'choose' && (
              <SelectDateCardWithInput
                text={t('date_choose')}
                selected={dateEnd || null}
                onChange={(date: Date | null) => {
                  setValue('dateEnd', date)

                  const emailEndDate = watch('email_send_end_date')
                  if (emailEndDate && date && emailEndDate > date) {
                    setValue('email_send_end_date', date)
                    add({
                      message: t('end_date_msg'),
                      status: 'error',
                      withoutAdditionalInfo: true,
                      id: uuid(),
                    })
                  }
                }}
                min={isDelay ? dateDelay : new Date()}
              />
            )}
          </div>
          <div className={cx('itemWrapper', 'half')}>
            <TabSelector
              label={t('emails_period')}
              help={t('emails_period_help')}
              items={DATE_EMAIL_SEND_END_TYPES}
              active={campaignByEndingHelpers.emailSendType}
              onChange={i => {
                dispatch(setCreateCampaignByEnding({ emailSendType: i.name as TEmailSendEndType }))

                if (i?.name === 'to_end') {
                  setValue('email_send_end_date', undefined)
                }
              }}
            />
          </div>
          <SelectTemplateCard
            label={t('commons:templates')}
            text={
              templates?.length == 0 || !templates
                ? t('campaign_choose')
                : t('commons:templates_choosed', { count: templates?.length })
            }
            selected={templates || []}
            onChange={ids => setValue('templates', ids)}
            className={cx('itemWrapper', 'half')}
          />
          {!enableRedirectPage && (
            <div className={cx('switchWrapper', 'switchInner')}>
              <span>{t('redirect_to_orig_resource')}</span>
              <Switch
                onChange={() => setValue('enableRedirect', !watch('enableRedirect'))}
                customValue={enableRedirect}
              />
              <HelpIcon text={t('redirect_to_orig_resource_help')} />
            </div>
          )}
          {!enableRedirect && (
            <>
              <div className={cx('switchWrapper', 'switchInner', 'half')}>
                <span>{t('redirect_to_chosen_page')}</span>
                <Switch
                  onChange={() => {
                    if (enableRedirectPage) {
                      setValue('redirect_page', undefined)
                    }

                    dispatch(
                      setCreateCampaignByEnding({
                        enableRedirectPage: !campaignByEndingHelpers.enableRedirectPage,
                      }),
                    )
                  }}
                  customValue={enableRedirectPage}
                />
                <HelpIcon text={t('redirect_to_chosen_page_help')} />
              </div>
              {campaignByEndingHelpers.enableRedirectPage && (
                <div className={cx('selected__page__wrapper', 'half')}>
                  <SelectRedirectPage
                    onSelect={v => setValue('redirect_page', v)}
                    selected={watch('redirect_page') ?? undefined}
                  />
                </div>
              )}
            </>
          )}
          <div className={cx('switchWrapper', 'switchInner', 'half')}>
            <span>{t('testing_campaign')}</span>
            <Switch onChange={() => setValue('isTesting', !isTesting)} customValue={isTesting} />
            <HelpIcon text={t('testing_campaign_help')} />
          </div>
          <div className={cx('switchWrapper', 'half')}>
            <div className={cx('switchInner')}>
              <span>{t('assign_course_after_campaign')} </span>
              <Switch
                onChange={() => setValue('hasAutoCourse', !hasAutoCourse)}
                customValue={hasAutoCourse}
              />
              <HelpIcon text={t('assign_course_after_campaign_help')} />
            </div>
            {hasAutoCourse && (
              <div className={cx('autoCourseInner', 'half')}>
                <SelectCourseCard
                  text={t('select_course')}
                  onChange={id => setValue('course', id)}
                  className={cx('itemWrapper')}
                  selected={typeof course === 'string' ? [course] : []}
                />
                <MultiSelect
                  label={t('for_those_who')}
                  onChange={handleChangeTargetsAutoCourse}
                  customValue={targetsAutoCourse}
                  list={TARGET_AUTO_COURSE_LIST}
                  className={cx('multiselectWrap')}
                  inputBaseClassName={cx('multiselect')}
                  renderValue={renderAutoCourseTargets}
                />
                <Input
                  fullWidth
                  name='period'
                  label={t('select_course_period')}
                  placeholder={t('commons:enter_course_duration')}
                  classNameWrapper={cx('itemWrapper')}
                  error={errors.period?.message}
                  register={register('period', {
                    pattern: {
                      value: /^\d+$/,
                      message: t('commons:enter_number'),
                    },
                    onBlur: e => handleTrim(e.target.value, 'period', true),
                    onChange: e => handleChangeNumber(e.target.value, 'period'),
                  })}
                />
              </div>
            )}
          </div>
          <div className={cx('switchWrapper', 'half')}>
            <div className={cx('switchInner')}>
              <span>{t('send_msg_after_campaign')}</span>
              <Switch
                onChange={() => setValue('hasMessage', !hasMessage)}
                customValue={hasMessage}
              />
              <HelpIcon text={t('send_msg_after_campaign_help')} />
            </div>
            {hasMessage && (
              <div className={cx('autoCourseInner', 'half')}>
                <MultiSelect
                  label={t('for_those_who')}
                  onChange={handleChangeTargetsMessage}
                  customValue={targetsMessage}
                  list={TARGET_AUTO_COURSE_LIST}
                  className={cx('multiselectWrap')}
                  inputBaseClassName={cx('multiselect')}
                  renderValue={renderMessageTargets}
                />
                <Input
                  fullWidth
                  name='theme'
                  label={t('commons:mail_theme')}
                  placeholder={t('commons:mail_theme')}
                  classNameWrapper={cx('itemWrapper')}
                  error={errors.theme?.message}
                  register={register('theme', {
                    onBlur: e => handleTrim(e.target.value, 'theme', true),
                  })}
                />
                <Textarea
                  fullWidth
                  name='text'
                  label={t('email_text')}
                  placeholder={t('send_msg_after_campaign_text_placeholder')}
                  classNameWrapper={cx('itemWrapper')}
                  error={errors.text?.message}
                  register={register('text', {
                    onBlur: e => handleTrim(e.target.value, 'text', true),
                  })}
                />
              </div>
            )}
          </div>
          <Button
            type='submit'
            fullWidth
            className={cx('itemWrapper', 'half')}
            disabled={isDisabled}
          >
            {t('save_settings')}
          </Button>
          <div className={cx('tip', 'half')}>{t('info')}</div>
        </form>
      </div>
    </div>
  )
}

export default SettingsCampaignByEnding
