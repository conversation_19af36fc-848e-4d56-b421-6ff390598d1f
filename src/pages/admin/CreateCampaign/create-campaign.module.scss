.wrapper {
  margin: 0 auto;
  max-width: var(--page-container);
  position: relative;

  width: 100%;
}

.primary-text {
  color: var(--color-primary);

  &:hover {
    color: var(--color-primary);
  }
}

.inner {
  max-width: 450px;
  width: 100%;
}

.new_campaign_by_ending {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.half {
  max-width: 450px;
}

.underline {
  text-decoration: underline;
}

.primary {
  color: var(--color-primary);
}

.mt-10 {
  margin-top: 10px;
}

.target {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;

  &__item {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.breadcrumb {
  margin-bottom: 8px;
}

.itemWrapper {
  margin-bottom: 32px;
}

.ttl {
  &__info {
    margin-top: 12px;
    &_loading {
      opacity: 0.3;
    }
  }
  &__date {
    color: var(--color-primary);
  }
}

.multiselectWrap {
  .multiselect {
    margin-bottom: 32px;
    padding: 13px 16px;

    background: #fff;
    border: 1px solid #ebeff2;
    border-radius: 12px;
  }

  &__withoutMargin {
    .multiselect {
      margin-bottom: 0;
      padding: 13px 16px;

      background: #fff;
      border: 1px solid #ebeff2;
      border-radius: 12px;
    }
  }
}

.selected__page__wrapper {
  margin: 24px 0;
}

.itemWrapperTop {
  margin-top: 12px;
}

.errorText {
  margin-bottom: 12px;
}

.tip {
  color: var(--color-gray-70);
  font: var(--font-caption-2-medium);
  text-align: center;
}

.switchWrapper {
  margin-bottom: 24px;
}

.switchInner {
  align-items: center;
  display: flex;
  gap: 12px;

  span {
    color: var(--color-gray-90);
    font: var(--font-text-2-medium);
  }
}

.autoCourseInner {
  margin-top: 12px;

  .itemWrapper {
    margin-bottom: 12px;
  }
}

.inputBase {
  background: var(--color-surface);
  box-shadow: var(--shadow-border);
  border-radius: 12px;
  border: 1px solid var(--color-border);
  cursor: pointer;

  span {
    color: var(--color-gray-80);
    font: var(--font-text-2-medium);
    line-height: 1.858;
  }
}

.inputWrapper {
  position: relative;
}

.autoGenerate {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  border-radius: 6px;
  background-color: var(--color-gray-30);
  font: var(--font-text-2-normal);
  color: var(--color-gray-90);
  padding: 6px 8px;
  right: 12px;
  top: 35px;
}
