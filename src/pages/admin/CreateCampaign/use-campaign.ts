import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { ITabSelector } from '@/shared/ui'
import { TTargetAutoCourseType } from '@/store/slices/phishing'
import { useEmployees } from '@/entities/employee'

type Props = {
  onlyAuto: boolean
}

export const useCampaign = (props: Props | void) => {
  const { t } = useTranslation('pages__phishing')

  const { useInPhishingCampaign } = useEmployees()

  const DATE_TYPES = useMemo<ITabSelector[]>(() => {
    if (props && props?.onlyAuto) {
      return [
        {
          name: 'auto',
          text: t('commons:auto'),
        },
      ]
    }

    return [
      {
        name: 'auto',
        text: t('commons:auto'),
      },
      {
        name: 'choose',
        text: t('to_selected_date'),
      },
    ]
  }, [props, t])

  const DATE_EMAIL_SEND_END_TYPES = useMemo<ITabSelector[]>(() => {
    if (props && props?.onlyAuto) {
      return [
        {
          name: 'to_end',
          text: t('commons:auto'),
        },
      ]
    }

    return [
      {
        name: 'to_end',
        text: t('commons:auto'),
      },
      {
        name: 'choose',
        text: t('to_selected_date'),
      },
    ]
  }, [t])

  const TARGET_AUTO_COURSE_LIST = useMemo<{ id: TTargetAutoCourseType; title: string }[]>(
    () => [
      { id: 'opened', title: t('commons:letter_opened') },
      { id: 'clicked', title: t('commons:followed_link') },
      { id: 'entered_data', title: t('commons:entered_data') },
      { id: 'opened_attachment', title: t('commons:opened_attachment') },
    ],
    [t],
  )

  const TARGET_TYPES = useMemo(() => {
    if (useInPhishingCampaign) {
      return [
        {
          name: 'filtered',
          text: t('filtered_users'),
        },
      ]
    }

    return [
      {
        name: 'custom',
        text: t('selectively'),
      },
      {
        name: 'all',
        text: t('commons:all'),
      },
    ]
  }, [useInPhishingCampaign, t])

  return {
    DATE_TYPES,
    DATE_EMAIL_SEND_END_TYPES,
    TARGET_AUTO_COURSE_LIST,
    TARGET_TYPES,
  }
}
