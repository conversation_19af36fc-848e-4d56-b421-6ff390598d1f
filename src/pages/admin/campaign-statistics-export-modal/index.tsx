import React from 'react'
import { useTranslation } from 'react-i18next'
import { Modal } from '@/shared/components'
import { Loader } from '@/shared/ui'
import styles from './styles.module.scss'
import classNamesBind from 'classnames/bind'
import { useCampaignStatisticsExportModal } from '../campaign-statictics/hooks/use-campaign-statistics-export-modal'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}

export const CampaignStatisticsExportModal = ({ open, setOpen }: Props) => {
  const { isError, isLoading, isSuccess } = useCampaignStatisticsExportModal()
  const { t } = useTranslation()

  return (
    <Modal active={open} setActive={setOpen} className={cx('modal')}>
      <h2 className={cx('modal__title')}>{t('dialogs.report_modal.export_report')}</h2>
      <div className={cx('modal__content')}>
        {isLoading && (
          <>
            <Loader size='56' />
            <h3 className={cx('modal__loading')}>{t('dialogs.report_modal.loader_title')}</h3>
          </>
        )}
        {isSuccess && (
          <>
            <Loader size='56' success loading={false} />
            <h3 className={cx('modal__success')}>
              {t('commons:report_in_progress')}
              <br />
              {t('commons:report_description')}
            </h3>
          </>
        )}
        {isError && (
          <>
            <Loader size='56' error loading={false} />
            <h3 className={cx('modal__error')}>{t('dialogs.report_modal.error')}</h3>
          </>
        )}
      </div>
    </Modal>
  )
}
