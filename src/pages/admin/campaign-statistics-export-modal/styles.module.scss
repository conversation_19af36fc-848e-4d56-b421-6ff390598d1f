@use '../../../shared/assets/styles/mixins/text';

.modal {
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 360px;
  max-width: 360px;
  text-align: center;

  &__title {
    color: var(--gray-gray-90);
    font: var(--font-title-4-medium);
    text-align: left;
  }

  &__content {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px;
  }

  &__success {
    color: var(--color-primary-90);
    font: var(--font-text-2-normal);
  }

  &__error {
    color: var(--color-error);
    font: var(--font-text-2-normal);
  }

  &__loading {
    color: var(--color-gray-90);
    font: var(--font-text-2-normal);
  }

  &__actions {
    align-items: center;
    display: flex;
    gap: 16px;
    justify-content: space-between;

    * {
      width: 100%;
    }
  }
}
