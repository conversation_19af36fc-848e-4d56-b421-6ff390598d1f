import { globalBaseApi } from '@/store/services/endpoints/base'

export const generalStatisticsApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getCoursesCounts: builder.query<
      {
        active: number
        completed: number
      },
      { organization_id: string }
    >({
      query: ({ organization_id }) => {
        return {
          url: `/learning/api/statistics/organizations/${organization_id}/courses-counts`,
        }
      },
    }),
  }),
  overrideExisting: true,
})
