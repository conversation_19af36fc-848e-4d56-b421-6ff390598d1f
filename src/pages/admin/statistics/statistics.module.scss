@use '../../../shared/assets/styles/mixins/text';

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 32px;
  width: 100%;
}

.general {
  display: flex;
  flex-direction: column;
  gap: 20px;

  &__title {
    margin-bottom: -4px;
  }

  &__widgets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    &__sm {
      grid-column: 1;
    }

    &_xl {
      grid-column: 2 / 4;
    }
  }
}

.education {
  display: flex;
  flex-direction: column;
  gap: 20px;

  &__title {
    margin-bottom: -4px;
  }

  &__widgets {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;

    &_xl {
      grid-column: 1 / 3;
    }
  }
}

.phishing {
  display: flex;
  flex-direction: column;
  gap: 20px;

  &__title {
    margin-bottom: -4px;
  }

  &__widgets {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

.block {
  margin: 0;
  padding: 20px 24px 34px 24px;
  width: 100%;
}

.loading {
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-70) 100%);
  height: 5px;
  left: 0;
  position: fixed;
  top: 0;

  width: 0;
  z-index: 100;
  &.loadingActive {
    animation: loading 60s ease-out;
  }
}

.header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  width: 100%;
  .headerTitle {
    @include text.title(30px);
  }
  .generateOverallButton {
    margin-left: 30px;
    max-width: 250px;
  }
}

.mainMetricks {
  flex-direction: row !important;
  justify-content: space-between;
  margin: 20px 20px 0;
  overflow: hidden;

  .mainMetricksDataItem {
    flex: 1 0;
    + .mainMetricksDataItem {
      border-left: 1px solid var(--gray);
    }
  }
}

.row {
  display: flex;
  flex-shrink: 0;
  margin: 0 -20px 40px;
  // @include media.mobile {
  // 	flex-direction: column;
  // 	margin: 0;
  // }
}

.title {
  @include text.title(20px);
  margin-bottom: 15px;
}

.statisticsTable {
  margin: 0 20px;
}

.excelExport {
  margin-top: 10px;
}

@keyframes loading {
  0% {
    width: 0;
  }
  20% {
    width: 65%;
  }

  100% {
    width: 80%;
  }
}

.riskLevelChart,
.testingChart,
.educationChart {
  min-height: 316px;
}
