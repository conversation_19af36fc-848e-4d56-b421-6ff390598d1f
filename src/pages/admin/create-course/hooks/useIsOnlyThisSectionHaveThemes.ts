import { useAppSelector } from '@/store'
import { newCourseSectionsSelectors } from '@/store/slices/new-course'
import { useCallback } from 'react'

export const useIsOnlyThisSectionHaveThemes = () => {
  const allSectionsCourse = useAppSelector(newCourseSectionsSelectors.selectAll)

  return useCallback(
    (sectionId: string) =>
      allSectionsCourse
        .filter(section => section.id !== sectionId)
        .every(section => section.themes.length === 0),
    [allSectionsCourse],
  )
}
