import { useAppSelector } from '@/store'
import { selectCourseImage, selectNewCourse } from '@/store/slices/new-course'
import { useMemo } from 'react'

export const useGetCourseFormData = () => {
  const newCourse = useAppSelector(selectNewCourse)
  const courseImage = useAppSelector(selectCourseImage)

  const courseFormData = useMemo(() => {
    const formData = new FormData()

    formData.append('title', newCourse.title)
    formData.append('lock_sequence', String(newCourse.lockSequence))
    formData.append('description', newCourse.description)
    formData.append('image', courseImage || '')

    return formData
  }, [newCourse.description, courseImage, newCourse.lockSequence, newCourse.title])

  return courseFormData
}
