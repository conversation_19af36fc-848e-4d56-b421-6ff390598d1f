.wrapper {
  margin: 0 auto;
  padding-bottom: 30px;
  max-width: var(--page-container);
  position: relative;

  width: 100%;
}

.trash {
  margin-left: 12px;
}

.inner {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-top: 10px;
  max-width: 524px;
}

.title {
  margin-bottom: 0;
}

.formWrapper {
  max-width: 524px;
  margin-top: 32px;
}

.itemWrapper {
  margin-top: 16px;
}

.itemWrapperBig {
  margin-top: 32px;
}
.imagePreviewWrapper {
  max-width: 206px;
}
.switchInner {
  align-items: center;
  display: flex;
  gap: 12px;

  span {
    color: var(--color-gray-90);
    font: var(--font-text-2-medium);
  }
}

.addSectionBtn {
  font: var(--font-text-2-demibold);
  margin-top: 24px;
}

.btns {
  position: sticky;
  right: 0;
  bottom: 36px;
  display: flex;
  flex-direction: row-reverse;
  gap: 16px;
  margin-top: 64px;
}

.wrapperWithLoader {
  position: absolute;
  top: 50%;
}
