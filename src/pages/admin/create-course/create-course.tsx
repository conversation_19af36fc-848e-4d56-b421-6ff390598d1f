import { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import { v4 as uuid } from 'uuid'
import { useTranslation } from 'react-i18next'
import { useNavigate, useParams } from 'react-router-dom'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Button, ButtonIcon, Input, Loader, PageTitle, Switch } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './create-course.module.scss'
import { URLS } from '@/shared/configs/urls'
import { ThemeCourseList } from './compose/theme-course-list/theme-course-list'
import { SectionsCourseList } from './compose/sections-course-list/sections-course-list'
import { TagsCollection } from './compose/tags-collection/tags-collection'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  addNewCourseSection,
  deleteAllSectionsExceptFirst,
  deleteNewCourseSection,
  downCourseSectionOrder,
  newCourseSectionsSelectors,
  resetNewCourseStateToInitial,
  selectDescriptionNewCourse,
  selectImagePreview,
  selectInitialNewCourse,
  selectIsNewCourseHaveSections,
  selectIsNewCourseHaveTags,
  selectIsTitleValidationError,
  selectNewCourse,
  selectNewCourseTags,
  selectTitleNewCourse,
  setIsNewCourseHaveSections,
  setIsNewCourseHaveTags,
  setNewCourseByCourseResponse,
  setNewCourseDescription,
  setNewCourseImage,
  setNewCourseTags,
  setNewCourseTitle,
  setNewCourseTitleValidationError,
  upCourseSectionOrder,
  updateNewCourseSection,
} from '@/store/slices/new-course'
import { CourseSettingsModal } from './compose/modals/course-settings-modal/course-settings-modal'

import { useNotification } from '@/shared/contexts/notifications'
import { coursesApi } from '@/entities/courses/model/api/courses'
import ReplaceModal from '@/shared/modals/replace-modal/replace-modal'
import { SectionDeleteModal } from './compose/modals/section-delete-modal/section-delete-modal'
import { ResetModal } from './compose/modals/reset-modal/reset-modal'
import { DeleteTagsModal } from './compose/modals/delete-tags-modal/delete-tags-modal'
import { DeleteSectionsModal } from './compose/modals/delete-sections-modal/delete-sections-modal'
import { ImageUpload } from '@/shared/components/image-upload/image-upload'
import { truncateString } from '@/shared/helpers'
import { useGetCourseFormData } from './hooks/useGetCourseFormData'
import { useIsOnlyThisSectionHaveThemes } from './hooks/useIsOnlyThisSectionHaveThemes'

const cx = classNamesBind.bind(styles)

export const CreateCourse = () => {
  const dispatch = useAppDispatch()

  const { course_id = '' } = useParams()
  const { t } = useTranslation('pages__create-course')

  const [openCourseSettings, setOpenCourseSettings] = useState(false)
  const [isOpenReplace, setIsOpenReplace] = useState<boolean>(false)
  const [isOpenDeleteSection, setIsOpenDeleteSection] = useState(false)
  const [isResetModalOpen, setIsResetModalOpen] = useState(false)
  const [isDeleteTagsModalOpen, setIsDeleteTagsModalOpen] = useState(false)
  const [isDeleteSectionsModalOpen, setIsDeleteSectionsModalOpen] = useState(false)

  const changedTitleSectionIdRef = useRef<string | null>(null)
  const deleteSectionIdRef = useRef<string | null>(null)

  const [createCourse, { isLoading: isCreateCourseLoading }] = coursesApi.useCreateCourseMutation()
  const [updateCourse, { isLoading: isUpdateCourseLoading }] = coursesApi.useUpdateCourseMutation()
  const [swapSectionsInCourse, { isLoading: isSwapSectionsInCourseLoading }] =
    coursesApi.useSwapSectionsInCourseMutation()
  const [deleteCourseSections, { isLoading: isDeleteCourseSectionsLoading }] =
    coursesApi.useDeleteCourseSectionsMutation()
  const [updateCourseSections, { isLoading: isUpdateCourseSectionLoading }] =
    coursesApi.useUpdateCourseSectionsMutation()
  const [updateTags, { isLoading: isUpdateTagsLoading }] = coursesApi.useUpdateTagsMutation()
  const [createSections, { isLoading: isCreateSectionsLoading }] =
    coursesApi.useCreateSectionsMutation()
  const [appendThemesInSection, { isLoading: isAppendThemesInSectionLoading }] =
    coursesApi.useAppendThemesInSectionMutation()
  const [getCourseById, { isLoading: isGetCourseByIdLoading }] =
    coursesApi.useLazyGetCourseByIdQuery()
  const { data: courseInfo, isLoading: isCourseInfoLoading } = coursesApi.useGetCourseByIdQuery(
    { course_id: course_id },
    { skip: !course_id },
  )
  const checkIsOnlyThisSectionHaveThemes = useIsOnlyThisSectionHaveThemes()

  const navigate = useNavigate()

  const breadcrumbItems = useMemo(() => {
    if (course_id) {
      return [
        {
          id: URLS.ADMIN_LEARNING_COURSES_PAGE,
          text: t('commons:courses'),
          clickable: true,
        },
        {
          id: uuid(),
          text: courseInfo ? truncateString(courseInfo.title, 28) : '',
          clickable: false,
          isLoading: isCourseInfoLoading,
        },
        {
          id: URLS.ADMIN_CREATE_COURSE_PAGE,
          text: t('commons:editing'),
          clickable: false,
        },
      ]
    } else {
      return [
        {
          id: URLS.ADMIN_LEARNING_COURSES_PAGE,
          text: t('commons:courses'),
          clickable: true,
        },
        {
          id: URLS.ADMIN_CREATE_COURSE_PAGE,
          text: t('commons:course_creation'),
          clickable: true,
        },
      ]
    }
  }, [courseInfo, course_id, isCourseInfoLoading, t])

  const { add: addNotification } = useNotification()

  const newCourse = useAppSelector(selectNewCourse)
  const titleNewCourse = useAppSelector(selectTitleNewCourse)
  const courseTags = useAppSelector(selectNewCourseTags)
  const descriptionNewCourse = useAppSelector(selectDescriptionNewCourse)
  const newCourseSections = useAppSelector(newCourseSectionsSelectors.selectAll)
  const isNewCourseHaveTags = useAppSelector(selectIsNewCourseHaveTags)
  const isNewCourseHaveSections = useAppSelector(selectIsNewCourseHaveSections)
  const imagePreview = useAppSelector(selectImagePreview)
  const initialNewCourse = useAppSelector(selectInitialNewCourse)
  const isCourseTitleValidationError = useAppSelector(selectIsTitleValidationError)

  const courseFormData = useGetCourseFormData()

  const handleCreateCourseSection = useCallback(async () => {
    const sectionsCreateResp = await createSections({
      courseId: course_id,
      sections: [{ title: `${t('section')} ${newCourseSections.length + 1}` }],
    })

    if (sectionsCreateResp.error) {
      addNotification({
        id: uuid(),
        status: 'error',
        message: t('tips:course.sections_creation_error'),
      })

      return
    }

    addNotification({
      id: uuid(),
      status: 'success',
      message: t('tips:course.section_creation_success'),
    })
  }, [addNotification, course_id, createSections, newCourseSections.length, t])

  useEffect(() => {
    if (!courseInfo) return

    if (courseInfo && !courseInfo.can_edit) navigate(URLS.ADMIN_LEARNING_COURSES_PAGE)

    dispatch(setNewCourseByCourseResponse(courseInfo))

    if (courseInfo.sections.length === 1) {
      dispatch(setIsNewCourseHaveSections(false))
    }

    if (courseInfo.sections.length === 0) {
      handleCreateCourseSection()
    }
  }, [courseInfo])

  const checkValidation = useCallback(
    (params: { withNotifications?: boolean } | void) => {
      const withNotifications = params?.withNotifications ?? true
      let isCourseTitleValidate = false
      let isValidateSections = true
      let isValidateThemes = true

      if (!course_id) {
        newCourseSections.forEach(section => {
          if (section.title.length === 0 && isNewCourseHaveSections) {
            if (withNotifications)
              dispatch(
                updateNewCourseSection({
                  sectionId: section.id,
                  newValue: { isTitleValidationError: true },
                }),
              )

            isValidateSections = false
          }

          if (section.themes.length === 0) {
            if (withNotifications)
              dispatch(
                updateNewCourseSection({
                  sectionId: section.id,
                  newValue: { isThemesValidationError: true },
                }),
              )

            isValidateThemes = false
          }
        })
      }

      if (newCourse.title.length > 0) {
        isCourseTitleValidate = true
      }

      if (withNotifications) dispatch(setNewCourseTitleValidationError(!isCourseTitleValidate))

      if (!isCourseTitleValidate && withNotifications) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('validation_title_error'),
          withoutAdditionalInfo: true,
        })
      }

      if (!isValidateSections && withNotifications) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('validation_section_title_error'),
          withoutAdditionalInfo: true,
        })
      }

      if (!isValidateThemes && isNewCourseHaveSections && withNotifications) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('validation_theme_in_section_error'),
          withoutAdditionalInfo: true,
        })
      }

      if (!isValidateThemes && !isNewCourseHaveSections && withNotifications) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('validation_theme_error'),
          withoutAdditionalInfo: true,
        })
      }

      return isCourseTitleValidate && isValidateSections && isValidateThemes
    },
    [
      addNotification,
      dispatch,
      isNewCourseHaveSections,
      newCourse.title.length,
      newCourseSections,
      t,
    ],
  )

  const handleSubmit = useCallback(
    async (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault()

      if (!checkValidation()) {
        return
      }

      if (course_id) {
        const updateCourseResponse = await updateCourse({
          courseId: course_id,
          body: courseFormData,
        })

        if (updateCourseResponse.error) {
          addNotification({
            id: uuid(),
            status: 'error',
            message: t('tips:course.update_error'),
          })

          return
        }

        if (isInitialTagsChanged) {
          await updateTags({ courseId: course_id, tags: newCourse.tags || [] })
        }

        addNotification({ id: uuid(), status: 'success', message: t('tips:course.update') })
      } else {
        const createCourseResponse = await createCourse(courseFormData)

        if (createCourseResponse.error) {
          addNotification({
            id: uuid(),
            status: 'error',
            message: t('tips:course.create_error'),
          })

          return
        }

        const newCourseId = createCourseResponse.data.id

        if (isNewCourseHaveTags && newCourse.tags && newCourse.tags.length > 0) {
          await updateTags({ courseId: newCourseId, tags: newCourse.tags })
        }

        const courseByIdResp = await getCourseById({ course_id: newCourseId })

        if (courseByIdResp.isError) {
          addNotification({
            id: uuid(),
            status: 'error',
            message: t('tips:course.get_error'),
          })

          return
        }

        const firstSectionId = courseByIdResp.data?.sections[0].id

        const sectionsIdsForAppendThemes = [firstSectionId]

        await updateCourseSections({
          courseId: newCourseId,
          sectionId: firstSectionId || '',
          title: newCourseSections[0].title,
        })

        if (isNewCourseHaveSections) {
          const sectionsCreateResp = await createSections({
            courseId: newCourseId,
            sections: newCourseSections.slice(1, newCourseSections.length).map(course => ({
              title: course.title,
            })),
          })

          if (sectionsCreateResp.error) {
            addNotification({
              id: uuid(),
              status: 'error',
              message: t('tips:course.sections_creation_error'),
            })

            return
          }

          const sortedByOrderSectionsCreate = [...sectionsCreateResp.data].sort(
            (a, b) => a.order_id - b.order_id,
          )

          sectionsIdsForAppendThemes.push(
            ...sortedByOrderSectionsCreate.map(newCreateSection => newCreateSection.id),
          )
        }

        const appendThemesPromises = sectionsIdsForAppendThemes.map((sectionId, sectionIndex) =>
          appendThemesInSection({
            courseId: newCourseId,
            sectionId: sectionId || '',
            themes: newCourseSections[sectionIndex]
              ? newCourseSections[sectionIndex].themes.map(theme => theme.id)
              : [],
          }).unwrap(),
        )

        await Promise.all(appendThemesPromises)
          .then(() => {
            addNotification({ id: uuid(), status: 'success', message: t('tips:course.create') })
            navigate(URLS.ADMIN_LEARNING_COURSES_PAGE)
          })
          .catch(error => {
            addNotification({
              message: t('commons:error_occurred') + error,
              status: 'error',
              id: uuid(),
            })
          })
      }
    },
    [
      addNotification,
      appendThemesInSection,
      checkValidation,
      course_id,
      createCourse,
      createSections,
      getCourseById,
      isNewCourseHaveTags,
      isNewCourseHaveSections,
      navigate,
      newCourse.tags,
      newCourseSections,
      t,
      updateCourse,
      updateTags,
      courseFormData,
    ],
  )

  const handleInputSectionNameBlur = useCallback(
    (event: React.FocusEvent<HTMLInputElement>, sectionId: string) => {
      const sectionTitleForCheck = initialNewCourse.sections[sectionId]

      if (!event.target.value.trim()) return

      if (sectionTitleForCheck.trim() !== event.target.value.trim()) {
        changedTitleSectionIdRef.current = sectionId
        setIsOpenReplace(true)
      }
    },
    [initialNewCourse],
  )

  const handleChangeSectionTitleConfirm = useCallback(async () => {
    if (!changedTitleSectionIdRef.current) return

    const updateResp = await updateCourseSections({
      courseId: course_id,
      sectionId: changedTitleSectionIdRef.current,
      title: newCourse.sections.entities[changedTitleSectionIdRef.current].title,
    })

    if (updateResp.error) {
      addNotification({
        id: uuid(),
        status: 'error',
        message: t('tips:course.section_update_error'),
      })

      changedTitleSectionIdRef.current = null

      return
    }

    changedTitleSectionIdRef.current = null

    addNotification({
      id: uuid(),
      status: 'success',
      message: t('tips:course.section_title_update_success'),
    })
  }, [addNotification, course_id, newCourse.sections.entities, t, updateCourseSections])

  const handleAddSectionClick = async () => {
    if (course_id) {
      const sectionsCreateResp = await createSections({
        courseId: course_id,
        sections: [{ title: `${t('section')} ${newCourseSections.length + 1}` }],
      })

      if (sectionsCreateResp.error) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('tips:course.section_creation_error'),
        })

        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.section_creation_success'),
      })
    } else {
      dispatch(
        addNewCourseSection({
          id: uuid(),
          title: `${t('section')} ${newCourseSections.length + 1}`,
          order: newCourseSections.length + 1,
          index: newCourseSections.length,
          themes: [],
        }),
      )
    }
  }

  const handleTitleChange = (value: string) => {
    dispatch(setNewCourseTitle(value))
  }

  const handleDescriptionChange = (value: string) => {
    dispatch(setNewCourseDescription(value))
  }

  const handleSettingClick = () => {
    setOpenCourseSettings(true)
  }

  const handleSettingClose = () => {
    setOpenCourseSettings(false)
  }

  const handleResetModalConfirm = () => {
    dispatch(resetNewCourseStateToInitial())
    setIsResetModalOpen(false)
    navigate(URLS.ADMIN_LEARNING_COURSES_PAGE)
  }

  const handleDeleteTagsModalConfirm = () => {
    dispatch(setNewCourseTags([]))
    dispatch(setIsNewCourseHaveTags(!isNewCourseHaveTags))
    setIsDeleteTagsModalOpen(false)
  }

  const handleDeleteSectionsConfirm = async () => {
    const firstSectionWithThemes = newCourseSections.filter(section => section.themes.length > 0)[0]

    const sectionsForDelete = firstSectionWithThemes
      ? newCourseSections.filter(section => section.id !== firstSectionWithThemes.id)
      : newCourseSections.slice(1)

    const deleteSectionsResp = await deleteCourseSections({
      courseId: course_id,
      sectionIds: sectionsForDelete.map(section => section.id),
    })

    if (deleteSectionsResp.error) {
      addNotification({
        id: uuid(),
        status: 'error',
        message: t('tips:course.sections_delete_error'),
      })

      setIsDeleteSectionsModalOpen(false)

      return
    }

    setIsDeleteSectionsModalOpen(false)

    addNotification({
      id: uuid(),
      status: 'success',
      message: t('tips:course.sections_delete_success'),
    })
  }

  const handleCancelBtnClick = () => {
    setIsResetModalOpen(true)
  }

  const handleChangeTagSwitcher = useCallback(() => {
    if (course_id && courseTags && courseTags.length > 0) {
      setIsDeleteTagsModalOpen(true)
    } else {
      dispatch(setIsNewCourseHaveTags(!isNewCourseHaveTags))
    }
  }, [courseTags, dispatch, isNewCourseHaveTags])

  const handleSectionsChange = useCallback(async () => {
    if (course_id) {
      if (isNewCourseHaveSections) {
        setIsDeleteSectionsModalOpen(true)
      } else {
        handleCreateCourseSection()
      }
    } else {
      if (isNewCourseHaveSections) {
        dispatch(deleteAllSectionsExceptFirst())
      }

      dispatch(setIsNewCourseHaveSections(!isNewCourseHaveSections))

      if (newCourseSections.length === 1) {
        dispatch(
          addNewCourseSection({
            id: uuid(),
            title: `${t('section')} ${newCourseSections.length + 1}`,
            order: newCourseSections.length + 1,
            index: newCourseSections.length,
            themes: [],
          }),
        )
      }
    }
  }, [
    addNotification,
    createSections,
    dispatch,
    isNewCourseHaveSections,
    newCourseSections.length,
    t,
    handleCreateCourseSection,
  ])

  const handleImageChange = (newFile?: File) => {
    dispatch(setNewCourseImage(newFile))
  }

  const handleDeleteSectionClick = useCallback(
    (sectionDeleteId: string) => {
      if (
        course_id &&
        newCourse.sections.entities[sectionDeleteId].themes.length > 0 &&
        checkIsOnlyThisSectionHaveThemes(sectionDeleteId)
      ) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('delete_section_with_last_themes'),
          withoutAdditionalInfo: true,
        })

        return
      }

      if (course_id) {
        deleteSectionIdRef.current = sectionDeleteId
        setIsOpenDeleteSection(true)
      } else {
        if (newCourseSections.length === 2) {
          dispatch(setIsNewCourseHaveSections(false))
        }

        dispatch(deleteNewCourseSection({ sectionDeleteId }))
      }
    },
    [
      checkIsOnlyThisSectionHaveThemes,
      course_id,
      dispatch,
      newCourse.sections.entities,
      newCourseSections.length,
      t,
    ],
  )

  const handleDeleteSectionClose = () => {
    setIsOpenDeleteSection(false)
    deleteSectionIdRef.current = null
  }

  const handleDeleteSectionConfirm = async () => {
    const deleteSectionResp = await deleteCourseSections({
      courseId: course_id,
      sectionIds: [deleteSectionIdRef.current || ''],
    })

    if (deleteSectionResp.error) {
      addNotification({
        id: uuid(),
        status: 'error',
        message: t('tips:course.section_delete_error'),
      })

      deleteSectionIdRef.current = null
      setIsOpenDeleteSection(false)

      return
    }

    deleteSectionIdRef.current = null
    setIsOpenDeleteSection(false)

    addNotification({
      id: uuid(),
      status: 'success',
      message: t('tips:course.section_delete_success'),
    })
  }

  const handleUpOrderClick = async (activeSectionId: UUID, passiveSectionId: UUID) => {
    if (course_id) {
      const activePrevOrder = newCourse.sections.entities[activeSectionId].order
      const passivePrevOrder = newCourse.sections.entities[passiveSectionId].order

      const swapResp = await swapSectionsInCourse({
        courseId: course_id,
        body: {
          section_from: { object_id: activeSectionId, order_id: activePrevOrder },
          section_to: { object_id: passiveSectionId, order_id: passivePrevOrder },
        },
      })

      if (swapResp.error) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('tips:course.section_order_error'),
        })

        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.section_order_success'),
      })
    } else {
      dispatch(upCourseSectionOrder({ activeSectionId, passiveSectionId }))
    }
  }

  const handleDownOrderClick = async (activeSectionId: UUID, passiveSectionId: UUID) => {
    if (course_id) {
      const activePrevOrder = newCourse.sections.entities[activeSectionId].order
      const passivePrevOrder = newCourse.sections.entities[passiveSectionId].order

      const swapResp = await swapSectionsInCourse({
        courseId: course_id,
        body: {
          section_from: { object_id: activeSectionId, order_id: activePrevOrder },
          section_to: { object_id: passiveSectionId, order_id: passivePrevOrder },
        },
      })

      if (swapResp.error) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('tips:course.section_order_error'),
        })

        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.section_order_success'),
      })
    } else {
      dispatch(downCourseSectionOrder({ activeSectionId, passiveSectionId }))
    }
  }

  const isInitialTagsChanged =
    JSON.stringify(initialNewCourse.tags) !== JSON.stringify(newCourse.tags || [])

  const isLoadingSaveBtn =
    isGetCourseByIdLoading ||
    isCreateCourseLoading ||
    isUpdateTagsLoading ||
    isCreateSectionsLoading ||
    isAppendThemesInSectionLoading ||
    isUpdateCourseSectionLoading ||
    isDeleteCourseSectionsLoading ||
    isSwapSectionsInCourseLoading ||
    isUpdateCourseLoading

  return (
    <div className={cx('wrapper', { wrapperWithLoader: isCourseInfoLoading })}>
      {isCourseInfoLoading ? (
        <Loader size='56' />
      ) : (
        <>
          {openCourseSettings && (
            <CourseSettingsModal
              open={openCourseSettings}
              onClose={handleSettingClose}
              courseId={course_id}
            />
          )}
          {isOpenReplace && (
            <ReplaceModal
              open={isOpenReplace}
              setOpen={setIsOpenReplace}
              title={t('tips:course.section_title_update')}
              onConfirm={handleChangeSectionTitleConfirm}
              submitText={t('commons:replace')}
            />
          )}
          {isResetModalOpen && (
            <ResetModal
              open={isResetModalOpen}
              onClose={() => setIsResetModalOpen(false)}
              onConfirm={handleResetModalConfirm}
            />
          )}
          {isDeleteTagsModalOpen && (
            <DeleteTagsModal
              open={isDeleteTagsModalOpen}
              onClose={() => setIsDeleteTagsModalOpen(false)}
              onConfirm={handleDeleteTagsModalConfirm}
            />
          )}
          {isOpenDeleteSection && (
            <SectionDeleteModal
              open={isOpenDeleteSection}
              onClose={handleDeleteSectionClose}
              onConfirmClick={handleDeleteSectionConfirm}
              sectionTitle={`${t('section').toLocaleLowerCase()} ${deleteSectionIdRef.current ? newCourse.sections.entities[deleteSectionIdRef.current].index + 1 : ''}`}
            />
          )}
          {isDeleteSectionsModalOpen && (
            <DeleteSectionsModal
              open={isDeleteSectionsModalOpen}
              onClose={() => setIsDeleteSectionsModalOpen(false)}
              onConfirmClick={handleDeleteSectionsConfirm}
              isLoading={isDeleteCourseSectionsLoading}
            />
          )}
          <Breadcrumbs items={breadcrumbItems} className={cx('breadcrumb')} />
          <div className={cx('inner')}>
            <PageTitle className={cx('title')}>
              {course_id ? t('commons:course_edit') : t('commons:course_creation')}
            </PageTitle>
            <ButtonIcon icon='settingsBold' onClick={handleSettingClick} />
          </div>
          <form id='createForm' onSubmit={handleSubmit} className={cx('formWrapper')}>
            <Input
              fullWidth
              name='title'
              value={titleNewCourse}
              onChange={handleTitleChange}
              label={t('commons:course_name')}
              placeholder={t('commons:enter_title')}
              error={isCourseTitleValidationError}
              required
            />
            <Input
              fullWidth
              name='description'
              value={descriptionNewCourse}
              onChange={handleDescriptionChange}
              label={t('commons:description')}
              placeholder={t('enter_description')}
              classNameWrapper={cx('itemWrapper', 'half')}
            />

            <div className={cx('switchInner', 'itemWrapperBig')}>
              <span>{t('commons:tags')}</span>
              <Switch onChange={handleChangeTagSwitcher} customValue={isNewCourseHaveTags} />
            </div>

            {isNewCourseHaveTags && (
              <div className={cx('itemWrapper')}>
                <TagsCollection />
              </div>
            )}

            <div className={cx('itemWrapperBig', 'imagePreviewWrapper')}>
              <ImageUpload
                defaultPreview={imagePreview}
                onSelect={handleImageChange}
                hasDelete={false}
              />
            </div>

            <div className={cx('switchInner', 'itemWrapperBig')}>
              <span>{t('commons:sections')}</span>
              <Switch
                onChange={handleSectionsChange}
                customValue={isNewCourseHaveSections}
                disabled={isCreateSectionsLoading}
              />
            </div>

            {isNewCourseHaveSections ? (
              <div className={cx('itemWrapper')}>
                <SectionsCourseList
                  sectionList={newCourseSections}
                  onSectionUpOrderClick={handleUpOrderClick}
                  onSectionDownOrderClick={handleDownOrderClick}
                  onSectionNameBlur={course_id ? handleInputSectionNameBlur : undefined}
                  onSectionDeleteClick={handleDeleteSectionClick}
                />
                <Button
                  type='button'
                  leftIcon='plus'
                  size='big'
                  color='darkGray'
                  onClick={handleAddSectionClick}
                  className={cx('addSectionBtn')}
                  loading={isCreateSectionsLoading}
                >
                  {t('add_section')}
                </Button>
              </div>
            ) : (
              <div className={cx('itemWrapper')}>
                {newCourseSections[0]?.id && (
                  <ThemeCourseList sectionId={newCourseSections[0].id} />
                )}
              </div>
            )}
          </form>
          <div className={cx('btns')}>
            <Button
              form='createForm'
              disabled={!checkValidation({ withNotifications: false })}
              type='submit'
              loading={isLoadingSaveBtn}
            >
              {t('commons:save')}
            </Button>
            <Button size='big' type='button' color='gray' onClick={handleCancelBtnClick}>
              {t('commons:cancel')}
            </Button>
          </div>
        </>
      )}
    </div>
  )
}
