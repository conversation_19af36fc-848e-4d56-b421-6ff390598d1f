import { ButtonIcon } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import { TThemeCourse } from '@/store/slices/new-course'
import styles from './theme-course.module.scss'

const cx = classNamesBind.bind(styles)

type Props = {
  themeCourse: TThemeCourse
  sectionOrder: number
  onThemeClick: () => void
  isDisabled?: boolean
}

export const ThemeCourse = ({
  themeCourse,
  sectionOrder,
  onThemeClick,
  isDisabled = false,
}: Props) => {
  return (
    <div className={cx('themeCourse', { cardDisabled: isDisabled })}>
      <p className={cx('order')}>
        {sectionOrder}.{themeCourse.index + 1}
      </p>
      <button type='button' className={cx('main')} onClick={onThemeClick}>
        {themeCourse.title}
      </button>
      <ButtonIcon
        type='button'
        icon='arrow'
        className={cx('navigateIcon')}
        onClick={onThemeClick}
      />
    </div>
  )
}
