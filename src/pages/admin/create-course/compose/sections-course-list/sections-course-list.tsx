import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'

import { useAppDispatch } from '@/store'
import { ButtonIcon, Card, Input } from '@/shared/ui'
import { setCourseSectionTitle, TSectionCourse } from '@/store/slices/new-course'
import { ThemeCourseList } from '../theme-course-list/theme-course-list'
import styles from './sections-course-list.module.scss'

const cx = classNamesBind.bind(styles)

type Props = {
  sectionList: TSectionCourse[]
  onSectionNameBlur?: (event: React.FocusEvent<HTMLInputElement>, sectionId: string) => void
  onSectionDeleteClick?: (sectionDeleteId: string) => void
  onSectionUpOrderClick?: (activeSectionId: UUID, passiveSectionId: UUID) => Promise<void>
  onSectionDownOrderClick?: (activeSectionId: UUID, passiveSectionId: UUID) => Promise<void>
}

export const SectionsCourseList = ({
  sectionList,
  onSectionNameBlur,
  onSectionDeleteClick,
  onSectionUpOrderClick,
  onSectionDownOrderClick,
}: Props) => {
  const { t } = useTranslation('pages__create-course')

  const dispatch = useAppDispatch()

  const handleSectionTitleChange = (sectionId: UUID, value: string) => {
    dispatch(setCourseSectionTitle({ sectionId, title: value }))
  }

  return (
    <div className={cx('sections')}>
      {sectionList.map((section, sectionIndex) => (
        <Card key={section.id} className={cx('wrapper')}>
          <div className={cx('headerWrap')}>
            <h2 className={cx('headerTitle')}>
              {t('section')} {section.index + 1}
            </h2>
            <div className={cx('headerActionsWrap')}>
              <ButtonIcon
                type='button'
                className={cx('chevroneDown')}
                icon={'chevroneMedium'}
                disabled={sectionIndex === sectionList.length - 1}
                onClick={
                  onSectionUpOrderClick
                    ? () => onSectionUpOrderClick(section.id, sectionList[sectionIndex + 1].id)
                    : undefined
                }
              />
              <ButtonIcon
                type='button'
                className={cx('chevroneUp')}
                icon={'chevroneMedium'}
                disabled={sectionIndex === 0}
                onClick={
                  onSectionDownOrderClick
                    ? () => onSectionDownOrderClick(section.id, sectionList[sectionIndex - 1].id)
                    : undefined
                }
              />
              <ButtonIcon
                type='button'
                icon={'trashBold'}
                disabled={sectionList.length === 1}
                onClick={onSectionDeleteClick ? () => onSectionDeleteClick(section.id) : undefined}
              />
            </div>
          </div>
          <Input
            value={section.title}
            onChange={value => handleSectionTitleChange(section.id, value)}
            fullWidth
            name={`section-name-${section.id}`}
            label={t('commons:name')}
            placeholder={t('section_name')}
            onBlur={onSectionNameBlur ? event => onSectionNameBlur(event, section.id) : undefined}
            required
            error={section.isTitleValidationError}
          />
          <ThemeCourseList sectionId={section.id} className={cx('contentThemeListWrapper')} />
        </Card>
      ))}
    </div>
  )
}
