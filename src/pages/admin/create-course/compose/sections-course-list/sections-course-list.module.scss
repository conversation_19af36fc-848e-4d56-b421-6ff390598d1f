.wrapper {
  padding: 20px 24px;
  border: 1px solid transparent;
}

.headerWrap {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.headerTitle {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-3-medium);
}

.headerActionsWrap {
  display: flex;
}

.contentThemeListWrapper {
  margin-top: 24px;
  background-color: var(--color-gray-40);

  h2 {
    font: var(--font-title-4-medium);
  }
}

.chevroneDown {
  transform: rotate(90deg);
  margin-right: 5px;
}

.chevroneUp {
  margin-right: 10px;
  transform: rotate(-90deg);
}

.sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}
