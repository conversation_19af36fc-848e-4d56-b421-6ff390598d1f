import { useCallback, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { v4 as uuid } from 'uuid'

import { useAppDispatch, useAppSelector } from '@/store'
import { useNotification } from '@/shared/contexts/notifications'
import { Button, TextAreaAutocomplete } from '@/shared/ui'
import { Tag } from '@/shared/components'

import classNamesBind from 'classnames/bind'
import styles from './tags-collection.module.scss'
import { selectNewCourseTags, setNewCourseTags } from '@/store/slices/new-course'
import { COURSE_TAG_COLOR } from '@/shared/constants'
const cx = classNamesBind.bind(styles)

export const TagsCollection = () => {
  const [newTagTitle, setNewTagTitle] = useState<string>()
  const { add: addNotification } = useNotification()
  const dispatch = useAppDispatch()
  const courseTags = useAppSelector(selectNewCourseTags)

  const { t } = useTranslation('pages__create-course')

  const handleAddNewTagClick = useCallback(() => {
    if (!newTagTitle) return
    if (courseTags && courseTags.includes(newTagTitle)) {
      addNotification({
        id: uuid(),
        status: 'error',
        message: t('tag_already_exist'),
      })
      return
    }

    const prevTags = courseTags ?? []

    dispatch(setNewCourseTags([...prevTags, newTagTitle]))
    setNewTagTitle('')
  }, [addNotification, courseTags, dispatch, newTagTitle, t])

  const handleDeleteTagClick = useCallback(
    (deleteTag: string) => {
      const prevTags = courseTags ?? []
      dispatch(setNewCourseTags(prevTags.filter(prevTag => prevTag !== deleteTag)))
    },
    [courseTags, dispatch],
  )

  const renderCourseTags = useMemo(() => {
    if (!courseTags) return []
    return courseTags.map(tag => ({ id: uuid(), color: COURSE_TAG_COLOR, title: tag }))
  }, [courseTags])

  const displayNewTagInfo = useMemo(
    () => ({
      id: uuid(),
      title: newTagTitle,
      color: COURSE_TAG_COLOR,
    }),
    [newTagTitle],
  )

  const isDisabledAddBtn = courseTags && courseTags.length >= 3

  return (
    <div>
      {renderCourseTags && (
        <TextAreaAutocomplete
          value={newTagTitle}
          onChange={value => setNewTagTitle(value)}
          inputPlaceholder={renderCourseTags.length === 0 ? t('enter_tag_name') : ''}
          leftAdornment={
            <>
              {renderCourseTags.map(tag => (
                <div key={tag.id} className={cx('tag')}>
                  <Tag
                    info={{
                      id: tag.id,
                      title: tag.title,
                      color: tag.color,
                    }}
                    onDelete={() => handleDeleteTagClick(tag.title)}
                    isActive={true}
                  />
                </div>
              ))}
            </>
          }
        />
      )}

      <Button
        type='button'
        leftIcon='plus'
        size='small'
        color='darkGray'
        onClick={handleAddNewTagClick}
        className={cx('btn')}
        disabled={isDisabledAddBtn}
      >
        {t('commons:add')}{' '}
        {newTagTitle && !isDisabledAddBtn && (
          <Tag info={displayNewTagInfo} isActive={true} className={cx('tagDisplay')} />
        )}
      </Button>
    </div>
  )
}
