import { Trans, useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './delete-all-themes-modal.module.scss'

import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'
import { useAppSelector } from '@/store'
import { useIsOnlyThisSectionHaveThemes } from '../../../hooks/useIsOnlyThisSectionHaveThemes'
import { selectSection } from '@/store/slices/new-course'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  onClose: () => void
  onConfirmClick: () => void
  sectionId: string
  isLoading?: boolean
}

export const DeleteAllThemesModal = ({
  open,
  onClose,
  onConfirmClick,
  sectionId,
  isLoading = false,
}: Props) => {
  const { t } = useTranslation('pages__create-course')
  const currentSection = useAppSelector(state => selectSection(state, sectionId))
  const checkIsOnlyThisSectionHaveThemes = useIsOnlyThisSectionHaveThemes()

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <div className={cx('title')}>
        {t('delete_all_theme')}{' '}
        {currentSection.title
          ? currentSection.title
          : `${t('section')} ${currentSection.index + 1}`}
      </div>
      <p className={cx('description')}>
        {checkIsOnlyThisSectionHaveThemes(sectionId) ? (
          <Trans t={t}>delete_last_theme_description</Trans>
        ) : (
          t('delete_all_theme_description')
        )}
      </p>
      <div className={cx('btns')}>
        <Button size='big' className={cx('btn')} type='button' color='gray' onClick={onClose}>
          {t('commons:cancel')}
        </Button>
        <Button
          size='big'
          className={cx('btn')}
          type='button'
          color='red'
          onClick={onConfirmClick}
          loading={isLoading}
        >
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}
