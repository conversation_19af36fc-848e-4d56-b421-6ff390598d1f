import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './section-delete-modal.module.scss'

import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  onClose: () => void
  onConfirmClick: () => void
  sectionTitle: string
}

export const SectionDeleteModal = ({ open, onClose, onConfirmClick, sectionTitle }: Props) => {
  const { t } = useTranslation('pages__create-course')

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <div className={cx('title')}>
        {t('commons:delete')} {sectionTitle}
      </div>
      <p className={cx('description')}>
        {t('delete_section_description')} {sectionTitle}
      </p>
      <div className={cx('btns')}>
        <Button size='big' className={cx('btn')} type='button' color='gray' onClick={onClose}>
          {t('commons:cancel')}
        </Button>
        <Button size='big' className={cx('btn')} type='button' color='red' onClick={onConfirmClick}>
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}
