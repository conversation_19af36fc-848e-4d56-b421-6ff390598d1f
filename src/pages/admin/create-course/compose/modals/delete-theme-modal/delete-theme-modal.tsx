import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './delete-theme-modal.module.scss'

import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  onClose: () => void
  onConfirmClick: () => void
  isLoading?: boolean
}

export const DeleteThemeModal = ({ open, onClose, onConfirmClick, isLoading = false }: Props) => {
  const { t } = useTranslation('pages__create-course')

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <div className={cx('title')}>{t('delete_theme')}</div>
      <p className={cx('description')}>{t('delete_theme_description')}</p>
      <div className={cx('btns')}>
        <Button size='big' className={cx('btn')} type='button' color='gray' onClick={onClose}>
          {t('commons:cancel')}
        </Button>
        <Button
          size='big'
          className={cx('btn')}
          type='button'
          color='red'
          onClick={onConfirmClick}
          loading={isLoading}
        >
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}
