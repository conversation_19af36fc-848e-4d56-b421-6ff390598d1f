import { Modal } from '@/shared/components'
import classNamesBind from 'classnames/bind'
import styles from './course-settings-modal.module.scss'
import { useTranslation } from 'react-i18next'
import { Button, Switch } from '@/shared/ui'
import { useAppDispatch, useAppSelector } from '@/store'
import { selectLockSequenceCourse, setNewCourseSequence } from '@/store/slices/new-course'
import { v4 as uuid } from 'uuid'
import { useState } from 'react'
import { useNotification } from '@/shared/contexts/notifications'
import { coursesApi } from '@/entities/courses'
import { useGetCourseFormData } from '../../../hooks/useGetCourseFormData'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  onClose: () => void
  courseId?: string
}

export const CourseSettingsModal = ({ onClose, open, courseId }: Props) => {
  const { t } = useTranslation('pages__create-course')

  const dispatch = useAppDispatch()
  const courseFormData = useGetCourseFormData()

  const [updateCourse, { isLoading: isUpdateCourseLoading }] = coursesApi.useUpdateCourseMutation()
  const isLockSequence = useAppSelector(selectLockSequenceCourse)
  const { add: addNotification } = useNotification()
  const [isLockSequenceLocale, setIsLockSequenceLocale] = useState(isLockSequence)

  const handleSequenceChange = () => {
    setIsLockSequenceLocale(prev => !prev)
  }

  const handleSaveClick = async () => {
    if (courseId) {
      courseFormData.set('lock_sequence', String(isLockSequenceLocale))

      const updateCourseResponse = await updateCourse({ courseId, body: courseFormData })

      if (updateCourseResponse.error) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('tips:course.update_error'),
        })

        return
      }

      addNotification({ id: uuid(), status: 'success', message: t('tips:course.update') })
      dispatch(setNewCourseSequence(isLockSequenceLocale))
      onClose()
      return
    }

    dispatch(setNewCourseSequence(isLockSequenceLocale))
    onClose()
  }

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <div className={cx('title')}>{t('settings_title')}</div>
      <div className={cx('main')}>
        <div className={cx('switchInner', 'itemWrapperBig')}>
          <div className={cx('switchInnerTitleWrap')}>
            <p className={cx('switchInnerTitle')}>{t('sequence_course')}</p>
            <p className={cx('switchInnerDescription')}>{t('sequence_description')}</p>
          </div>
          <Switch onChange={handleSequenceChange} customValue={isLockSequenceLocale} />
        </div>
      </div>
      <Button
        size='big'
        className={cx('saveBtn')}
        type='button'
        onClick={handleSaveClick}
        loading={isUpdateCourseLoading}
      >
        {t('commons:save')}
      </Button>
    </Modal>
  )
}
