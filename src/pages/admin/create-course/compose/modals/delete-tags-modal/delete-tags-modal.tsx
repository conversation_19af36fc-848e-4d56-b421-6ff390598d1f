import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './delete-tags-modal.module.scss'

import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  onClose: () => void
  onConfirm: () => void
}

export const DeleteTagsModal = ({ open, onClose, onConfirm }: Props) => {
  const { t } = useTranslation('pages__create-course')

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <div className={cx('title')}>{t('tags_delete_title')}</div>
      <div className={cx('btns')}>
        <Button size='big' className={cx('btn')} type='button' color='gray' onClick={onClose}>
          {t('commons:cancel')}
        </Button>
        <Button size='big' className={cx('btn')} type='button' color='red' onClick={onConfirm}>
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}
