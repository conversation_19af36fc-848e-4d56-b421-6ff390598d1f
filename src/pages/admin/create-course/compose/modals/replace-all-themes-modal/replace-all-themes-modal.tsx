import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './replace-all-themes-modal.module.scss'

import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  onClose: () => void
  onConfirmClick: () => void
  sectionTitle: string
  isLoading: boolean
}

export const ReplaceAllThemesModal = ({
  open,
  onClose,
  onConfirmClick,
  sectionTitle,
  isLoading = false,
}: Props) => {
  const { t } = useTranslation('pages__create-course')

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <div className={cx('title')}>
        {t('replace_all_theme')} {sectionTitle}
      </div>
      <p className={cx('description')}>{t('description_replace_all_theme')}</p>
      <div className={cx('btns')}>
        <Button size='big' className={cx('btn')} type='button' color='gray' onClick={onClose}>
          {t('commons:cancel')}
        </Button>
        <Button
          size='big'
          className={cx('btn')}
          type='button'
          color='green'
          onClick={onConfirmClick}
          loading={isLoading}
        >
          {t('replace')}
        </Button>
      </div>
    </Modal>
  )
}
