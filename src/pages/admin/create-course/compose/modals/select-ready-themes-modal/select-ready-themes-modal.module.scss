@use '../../../../../../shared/assets/styles/mixins/text';

.wrapper {
  background: var(--surface, #fff);
  border-radius: 16px;
  box-shadow:
    0 32px 88px -4px rgba(24, 39, 75, 0.12),
    0 12px 28px -6px rgba(24, 39, 75, 0.12);
  display: grid;
  grid-gap: 16px;
  grid-template-rows: auto 1fr auto;
  height: 100%;
  max-height: 76vh;
  max-width: 1120px;
  width: 100%;
}

.list {
  height: 100%;
  overflow-y: auto;
}

.title {
  font: var(--font-title-4-medium);
  color: var(--color-gray-90);
  margin-bottom: 16px;
}

.inner {
  display: grid;
  grid-gap: 16px 20px;
  grid-template-columns: minmax(250px, 315px) 1fr;
  grid-template-rows: auto 1fr;
  overflow: hidden;
  padding: 2px;
}

.searchInput {
  width: 100%;
}

.counter {
  align-self: center;

  color: var(--color-primary);
  font: var(--font-text-2-normal);
}

.buttonWrapper {
  align-self: center;
  display: flex;
  justify-content: end;
  gap: 16px;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 14px;
  padding-right: 5px;
}

.listInner {
  padding: 4px 0;
  padding-left: 12px;
  padding-left: 4px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  gap: 8px;
  cursor: pointer;

  &:hover {
    background: var(--color-gray-40);

    span,p {
      @include text.max-lines(100);
    }
  }

  span,p {
    @include text.max-lines(1);
  }
}

.selected {
  background: var(--color-gray-40);
}

.preview {
  overflow-y: auto;
  padding-right: 5px;
}

.noThemesTooltip {
  margin-left: auto;
  margin-right: 5px;
}
