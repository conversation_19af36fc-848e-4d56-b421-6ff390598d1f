import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './reset-modal.module.scss'

import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  onClose: () => void
  onConfirm: () => void
}

export const ResetModal = ({ open, onClose, onConfirm }: Props) => {
  const { t } = useTranslation('pages__create-course')

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <div className={cx('title')}>{t('cancel_title')}</div>
      <p className={cx('description')}>{t('cancel_description')}</p>
      <div className={cx('btns')}>
        <Button size='big' className={cx('btn')} type='button' color='gray' onClick={onClose}>
          {t('commons:cancel')}
        </Button>
        <Button size='big' className={cx('btn')} type='button' color='green' onClick={onConfirm}>
          {t('reset')}
        </Button>
      </div>
    </Modal>
  )
}
