import { useCallback, useEffect, useRef, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
  closestCenter,
  DndContext,
  DragEndEvent,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core'
import {
  rectSortingStrategy,
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
} from '@dnd-kit/sortable'
import { CSS } from '@dnd-kit/utilities'
import { v4 as uuid } from 'uuid'

import {
  addThemesCourseInSection,
  changeThemeCourseOrderInSection,
  deleteNewCourseSection,
  deleteThemeCourseFromSection,
  newCourseSectionsSelectors,
  replaceAllThemeFromSection,
  replaceThemeFromSection,
  selectIsNewCourseHaveSections,
  selectThemesByCourseId,
  TThemeCourse,
} from '@/store/slices/new-course'
import { But<PERSON>, ButtonIcon, Card, Loader, Tooltip } from '@/shared/ui'
import ChevroneMediumIcon from '@/shared/ui/Icon/icons/components/ChevroneMediumIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useEvent } from '@/shared/hooks'
import {
  getAdminEditThemeForCourseSection,
  getAdminEditThemeWithSection,
  URLS,
} from '@/shared/configs/urls'

import { ThemeCourse } from '../theme-course/theme-course'

import classNamesBind from 'classnames/bind'
import styles from './theme-course-list.module.scss'
import { useAppDispatch, useAppSelector } from '@/store'
import { DeleteAllThemesModal } from '../modals/delete-all-themes-modal/delete-all-themes-modal'
import { ReplaceAllThemesModal } from '../modals/replace-all-themes-modal/replace-all-themes-modal'
import { SelectReadyThemesModal } from '../modals/select-ready-themes-modal/select-ready-themes-modal'
import { setThemePageBreadcrumbs } from '@/store/slices/theme-page-slice'
import { coursesApi } from '@/entities/courses/model/api/courses'
import { useNotification } from '@/shared/contexts/notifications'
import { DeleteThemeModal } from '../modals/delete-theme-modal/delete-theme-modal'
import { useIsOnlyThisSectionHaveThemes } from '../../hooks/useIsOnlyThisSectionHaveThemes'

const cx = classNamesBind.bind(styles)

type Props = {
  className?: string
  sectionId: UUID
}

export const ThemeCourseList = ({ className, sectionId }: Props) => {
  const { t } = useTranslation('pages__create-course')

  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { course_id = '' } = useParams()
  const { add: addNotification } = useNotification()

  const [transportThemeInDiffSection, { isLoading: isTransportThemeInDiffSectionLoading }] =
    coursesApi.useTransportCourseThemeInDiffSectionMutation()
  const [swapThemeInSection, { isLoading: isSwapThemeInSectionLoading }] =
    coursesApi.useSwapThemeInSectionMutation()
  const [deleteSectionThemes, { isLoading: isDeleteSectionThemesLoading }] =
    coursesApi.useDeleteSectionThemesMutation()
  const [appendThemesInSection, { isLoading: isAppendThemesInSectionLoading }] =
    coursesApi.useAppendThemesInSectionMutation()
  const [deleteCourseSections, { isLoading: isDeleteCourseSectionsLoading }] =
    coursesApi.useDeleteCourseSectionsMutation()

  const allSectionsCourse = useAppSelector(newCourseSectionsSelectors.selectAll)
  const currentSection = useAppSelector(state =>
    newCourseSectionsSelectors.selectById(state, sectionId),
  )
  const themeList = useAppSelector(state => selectThemesByCourseId(state, sectionId))
  const isNewCourseHaveSections = useAppSelector(selectIsNewCourseHaveSections)

  const [renderThemes, setRenderThemes] = useState<TThemeCourse[]>(themeList)
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false)
  const [isMoveAllThemeMenuOpen, setIsMoveAllThemeMenuOpen] = useState(false)
  const [openDeleteAllThemes, setOpenDeleteAllThemes] = useState(false)
  const [openReplaceAllThemes, setOpenReplaceAllThemes] = useState(false)
  const [readyThemeModalOpen, setReadyThemeModalOpen] = useState(false)
  const [toSectionId, setToSectionId] = useState<UUID>('')

  const toSection = useAppSelector(state =>
    newCourseSectionsSelectors.selectById(state, toSectionId),
  )

  const checkIsOnlyThisSectionHaveThemes = useIsOnlyThisSectionHaveThemes()

  const contextMenuListWrapper = useRef<HTMLDivElement>(null)
  const themeMenuListWrapper = useRef<HTMLLIElement>(null)

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        delay: 130,
        tolerance: 0,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  )

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event

    if (!over) return

    if (active.id !== over.id && renderThemes) {
      const oldIndex = renderThemes.findIndex(themeCourse => themeCourse.id === active.id) || 0
      const newIndex = renderThemes.findIndex(themeCourse => themeCourse.id === over.id) || 0

      if (course_id) {
        dispatch(
          changeThemeCourseOrderInSection({
            sectionId,
            oldThemeIndex: oldIndex,
            newThemeIndex: newIndex,
          }),
        )

        const swapThemeResp = await swapThemeInSection({
          courseId: course_id,
          sectionId: currentSection.id,
          body: {
            theme_from: {
              object_id: renderThemes[oldIndex].id,
              order_id: renderThemes[oldIndex].order,
            },
            theme_to: {
              object_id: renderThemes[newIndex].id,
              order_id: renderThemes[newIndex].order,
            },
          },
        })

        if (swapThemeResp.error) {
          addNotification({
            message: t('tips:course.swap_theme_error'),
            status: 'error',
            id: uuid(),
          })

          dispatch(
            changeThemeCourseOrderInSection({
              sectionId,
              oldThemeIndex: newIndex,
              newThemeIndex: oldIndex,
            }),
          )

          return
        }

        addNotification({
          message: t('tips:course.swap_theme_success'),
          status: 'success',
          id: uuid(),
        })
      } else {
        dispatch(
          changeThemeCourseOrderInSection({
            sectionId,
            oldThemeIndex: oldIndex,
            newThemeIndex: newIndex,
          }),
        )
      }
    }
  }

  const handleMoveThemeButtonClick = () => {
    setIsContextMenuOpen(prev => !prev)
  }

  const handleOutsideContextMenuClick = useCallback((e: Event) => {
    if (!contextMenuListWrapper.current) return

    if (e.composedPath().indexOf(contextMenuListWrapper.current) === -1) {
      setIsContextMenuOpen(false)
    }
  }, [])

  const handleOutsideThemeListClick = useCallback((e: Event) => {
    if (!themeMenuListWrapper.current) return

    if (e.composedPath().indexOf(themeMenuListWrapper.current) === -1) {
      setIsMoveAllThemeMenuOpen(false)
    }
  }, [])

  const handleReadyThemeClick = () => {
    setReadyThemeModalOpen(true)
  }

  const handleNewThemeClick = () => {
    dispatch(
      setThemePageBreadcrumbs([
        {
          id: course_id
            ? `${URLS.ADMIN_EDIT_COURSE_PAGE}${course_id}`
            : URLS.ADMIN_CREATE_COURSE_PAGE,
          text: course_id ? 'commons:course_edit' : 'commons:course_creation',
          clickable: true,
        },
        { id: '', text: 'commons:theme_creation', clickable: false },
      ]),
    )
    navigate(
      course_id
        ? URLS.ADMIN_CREATE_THEME_PAGE + `/course/${course_id}/section/${sectionId}`
        : URLS.ADMIN_CREATE_THEME_PAGE + `/section/${sectionId}`,
    )
  }

  const handleConfirmAddReadyThemeClick = async (newThemeList: TThemeCourse[]) => {
    if (course_id) {
      const appendThemeInSectionResp = await appendThemesInSection({
        courseId: course_id,
        sectionId: sectionId,
        themes: newThemeList.map(theme => theme.id),
      })

      if (appendThemeInSectionResp.error) {
        addNotification({
          message: t('tips:course.themes_append_error'),
          status: 'error',
          id: uuid(),
        })

        return
      }

      addNotification({
        message: t('tips:course.themes_append_success'),
        status: 'success',
        id: uuid(),
      })
      setReadyThemeModalOpen(false)
    } else {
      dispatch(addThemesCourseInSection({ sectionId: sectionId, newThemesCourse: newThemeList }))
      setReadyThemeModalOpen(false)
    }
  }

  const handleAllDeleteThemeClose = () => {
    setOpenDeleteAllThemes(false)
  }

  const handleAllReplaceThemeClose = () => {
    setOpenReplaceAllThemes(false)
  }

  const handleConfirmDeleteAllThemesClick = async () => {
    if (course_id && checkIsOnlyThisSectionHaveThemes(currentSection.id)) {
      const deleteResp = await deleteSectionThemes({
        courseId: course_id,
        sectionId,
        themesIds: themeList.slice(1).map(theme => theme.id),
      })

      if (deleteResp.error) {
        addNotification({
          message: t('tips:course.themes_delete_error'),
          status: 'error',
          id: uuid(),
        })

        setOpenDeleteAllThemes(false)

        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.themes_delete_success'),
      })

      setOpenDeleteAllThemes(false)
      return
    }

    if (course_id) {
      const deleteSectionResp = await deleteCourseSections({
        courseId: course_id,
        sectionIds: [currentSection.id],
      })

      if (deleteSectionResp.error) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('tips:course.section_delete_error'),
        })

        setOpenDeleteAllThemes(false)
        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.section_delete_success'),
      })
    } else {
      dispatch(deleteNewCourseSection({ sectionDeleteId: sectionId }))
    }

    setOpenDeleteAllThemes(false)
  }

  const handleConfirmReplaceAllThemesClick = async () => {
    if (course_id) {
      const themesIds = themeList.map(transportTheme => transportTheme.id)

      const transportResp = await transportThemeInDiffSection({
        courseId: course_id,
        sectionId: sectionId,
        body: { themes_ids: themesIds, target_section_id: toSectionId },
      })

      if (transportResp.error) {
        addNotification({
          message: t('tips:course.transport_themes_error'),
          status: 'error',
          id: uuid(),
        })
        setOpenReplaceAllThemes(false)
        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.transport_themes_success'),
      })
      setOpenReplaceAllThemes(false)
    } else {
      dispatch(
        replaceAllThemeFromSection({
          fromSectionId: sectionId,
          toSectionId: toSectionId,
        }),
      )
      setOpenReplaceAllThemes(false)
    }
  }

  useEffect(() => {
    setRenderThemes(themeList)
  }, [themeList])

  useEffect(() => {
    if (themeList?.length === 0 && isContextMenuOpen) {
      setIsContextMenuOpen(false)
    }
  }, [isContextMenuOpen, themeList?.length])

  useEvent('click', handleOutsideContextMenuClick, window)
  useEvent('click', handleOutsideThemeListClick, window)

  return (
    <Card
      className={cx('cardWrapper', className, {
        validationError: currentSection?.isThemesValidationError,
      })}
    >
      {openDeleteAllThemes && (
        <DeleteAllThemesModal
          open={openDeleteAllThemes}
          onClose={handleAllDeleteThemeClose}
          onConfirmClick={handleConfirmDeleteAllThemesClick}
          sectionId={currentSection.id}
          isLoading={isDeleteSectionThemesLoading || isDeleteCourseSectionsLoading}
        />
      )}

      {openReplaceAllThemes && (
        <ReplaceAllThemesModal
          open={openReplaceAllThemes}
          onClose={handleAllReplaceThemeClose}
          onConfirmClick={handleConfirmReplaceAllThemesClick}
          sectionTitle={`${t('section').toLocaleLowerCase()} ${toSection.order}`}
          isLoading={isTransportThemeInDiffSectionLoading}
        />
      )}
      {readyThemeModalOpen && (
        <SelectReadyThemesModal
          open={readyThemeModalOpen}
          sectionId={sectionId}
          onClose={() => {
            setReadyThemeModalOpen(false)
          }}
          onConfirm={handleConfirmAddReadyThemeClick}
          isLoading={isAppendThemesInSectionLoading}
        />
      )}
      <div className={cx('cardTitleWrap')}>
        <h2 className={cx('cardTitle')}>
          {t('themes')} <span>*</span>
        </h2>
        <div ref={contextMenuListWrapper} className={cx('moveThemeMenuWrapper')}>
          <ButtonIcon
            type='button'
            icon='verticalDotted'
            iconSize='14'
            disabled={themeList?.length === 0 || isSwapThemeInSectionLoading}
            onClick={handleMoveThemeButtonClick}
          />
          {isContextMenuOpen && (
            <Card className={cx('contextMenu')}>
              <ul>
                <li ref={themeMenuListWrapper} className={cx('contextMenuItem')}>
                  <button
                    type='button'
                    className={cx('contextMenuItemBtn')}
                    disabled={!isNewCourseHaveSections}
                    onClick={() => {
                      setIsMoveAllThemeMenuOpen(prev => !prev)
                    }}
                  >
                    {t('replace_all_themes')}{' '}
                    <IconWrapper>
                      <ChevroneMediumIcon />
                    </IconWrapper>
                  </button>

                  {isMoveAllThemeMenuOpen && (
                    <Card className={cx('moveAllThemeMenu')}>
                      <ul>
                        <li
                          className={cx(
                            'moveThemeMenuItem',
                            'moveThemeMenuItemBold',
                            'moveThemeMenuItemTitle',
                          )}
                        >
                          {t('section_name')} {isTransportThemeInDiffSectionLoading && <Loader />}
                        </li>
                        {allSectionsCourse.map(section => (
                          <li key={section.id} className={cx('moveThemeMenuItem')}>
                            <button
                              type='button'
                              className={cx('moveThemeMenuItemBtn')}
                              disabled={section.id === sectionId}
                              onClick={() => {
                                setToSectionId(section.id)
                                setOpenReplaceAllThemes(true)
                                setIsContextMenuOpen(false)
                              }}
                            >
                              <IconWrapper disabled={section.id === sectionId}>
                                <ChevroneMediumIcon />
                              </IconWrapper>
                              <span>{section.title || `${t('section')} ${section.index + 1}`}</span>
                            </button>
                          </li>
                        ))}
                      </ul>
                    </Card>
                  )}
                </li>
                <li className={cx('moveThemeMenuItem')}>
                  <button
                    className={cx('deleteThemeMenuItemBtn')}
                    type='button'
                    onClick={() => {
                      if (
                        checkIsOnlyThisSectionHaveThemes(sectionId) &&
                        currentSection.themes.length === 1
                      ) {
                        addNotification({
                          message: t('delete_theme_not_possible_description'),
                          status: 'error',
                          id: uuid(),
                          withoutAdditionalInfo: true,
                        })
                      } else {
                        setOpenDeleteAllThemes(true)
                      }
                      setIsContextMenuOpen(false)
                    }}
                  >
                    {t('commons:delete')}
                  </button>
                </li>
              </ul>
            </Card>
          )}
        </div>
      </div>
      {renderThemes && renderThemes.length > 0 && (
        <div className={cx('cardItems')}>
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={renderThemes} strategy={rectSortingStrategy}>
              {renderThemes.map(renderTheme => (
                <SortableThemeCourse
                  key={renderTheme.id}
                  themeCourse={renderTheme}
                  sectionId={sectionId}
                  isLoading={isSwapThemeInSectionLoading}
                />
              ))}
            </SortableContext>
          </DndContext>
        </div>
      )}

      <div className={cx('actionWrap')}>
        {course_id && (
          <Button
            type='button'
            leftIcon='plus'
            size='small'
            color='darkGray'
            onClick={handleNewThemeClick}
            disabled={isSwapThemeInSectionLoading}
          >
            {t('new_theme')}
          </Button>
        )}

        <Button
          type='button'
          leftIcon='plus'
          size='small'
          color='darkGray'
          onClick={handleReadyThemeClick}
          disabled={isSwapThemeInSectionLoading}
        >
          {t('ready_theme')}
        </Button>
      </div>
    </Card>
  )
}

type SortableThemeCourseProps = {
  themeCourse: TThemeCourse
  sectionId: UUID
  isLoading: boolean
}

const SortableThemeCourse = ({ themeCourse, sectionId, isLoading }: SortableThemeCourseProps) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: themeCourse.id,
  })
  const { t } = useTranslation('pages__create-course')
  const { course_id = '' } = useParams()

  const [transportThemeInDiffSection, { isLoading: isTransportThemeInDiffSectionLoading }] =
    coursesApi.useTransportCourseThemeInDiffSectionMutation()
  const [deleteSectionThemes, { isLoading: isDeleteSectionThemesLoading }] =
    coursesApi.useDeleteSectionThemesMutation()
  const [deleteCourseSections, { isLoading: isDeleteCourseSectionsLoading }] =
    coursesApi.useDeleteCourseSectionsMutation()

  const { add: addNotification } = useNotification()

  const contextMenuRef = useRef<HTMLDivElement>(null)
  const themeMenuListRef = useRef<HTMLLIElement>(null)

  const [openDeleteTheme, setOpenDeleteTheme] = useState(false)
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false)
  const [isMoveAllThemeMenuOpen, setIsMoveAllThemeMenuOpen] = useState(false)

  const isNewCourseHaveSections = useAppSelector(selectIsNewCourseHaveSections)
  const allSectionsCourse = useAppSelector(newCourseSectionsSelectors.selectAll)
  const sectionCourse = useAppSelector(state =>
    newCourseSectionsSelectors.selectById(state, sectionId),
  )
  const dispatch = useAppDispatch()
  const navigate = useNavigate()

  const checkIsOnlyThisSectionHaveThemes = useIsOnlyThisSectionHaveThemes()

  const handleDragBtnClick = () => {
    setIsContextMenuOpen(prev => !prev)
  }

  const dndStyle = {
    transform: CSS.Transform.toString(transform),
    transition,
    zIndex: isDragging ? '100' : 'auto',
    opacity: isDragging ? 0.3 : 1,
  }

  const handleOutsideContextMenuClick = useCallback((e: Event) => {
    if (!contextMenuRef.current) return

    if (e.composedPath().indexOf(contextMenuRef.current) === -1) {
      setIsContextMenuOpen(false)
    }
  }, [])

  const handleOutsideThemeMenuClick = useCallback((e: Event) => {
    if (!themeMenuListRef.current) return

    if (e.composedPath().indexOf(themeMenuListRef.current) === -1) {
      setIsMoveAllThemeMenuOpen(false)
    }
  }, [])

  const handleDeleteThemeClose = () => {
    setOpenDeleteTheme(false)
  }
  const handleThemeClick = () => {
    if (course_id) {
      dispatch(
        setThemePageBreadcrumbs([
          {
            id: URLS.ADMIN_EDIT_COURSE_PAGE + `${course_id}`,
            text: t('commons:course_edit'),
            clickable: true,
          },
          { id: URLS.ADMIN_CREATE_COURSE_PAGE, text: 'commons:course_creation', clickable: true },
        ]),
      )
      navigate(
        getAdminEditThemeForCourseSection({
          course_id,
          section_id: sectionId,
          theme_id: themeCourse.id,
        }),
      )
    } else {
      dispatch(
        setThemePageBreadcrumbs([
          { id: URLS.ADMIN_CREATE_COURSE_PAGE, text: 'commons:course_creation', clickable: true },
          { id: '', text: 'commons:theme_creation', clickable: false },
        ]),
      )

      navigate(
        getAdminEditThemeWithSection({
          section_id: sectionId,
          theme_id: themeCourse.id,
        }),
      )
    }
  }

  const handleReplaceThemeClick = async (
    fromSectionId: string,
    toSectionId: string,
    movingTheme: TThemeCourse,
  ) => {
    if (course_id) {
      const transportResp = await transportThemeInDiffSection({
        courseId: course_id,
        sectionId: fromSectionId,
        body: { target_section_id: toSectionId, themes_ids: [movingTheme.id] },
      })

      if (transportResp.error) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('tips:course.transport_theme_error'),
        })

        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.transport_theme_success'),
      })
    } else {
      dispatch(
        replaceThemeFromSection({
          fromSectionId,
          toSectionId,
          movingTheme,
        }),
      )
    }
  }

  const handleDeleteThemeClick = () => {
    if (checkIsOnlyThisSectionHaveThemes(sectionId) && sectionCourse.themes.length === 1) {
      addNotification({
        message: t('delete_theme_not_possible_description'),
        status: 'error',
        id: uuid(),
        withoutAdditionalInfo: true,
      })
    } else {
      setOpenDeleteTheme(true)
    }
  }

  const handleConfirmDeleteThemeClick = async () => {
    if (course_id && sectionCourse.themes.length === 1) {
      const deleteSectionsResp = await deleteCourseSections({
        courseId: course_id,
        sectionIds: [sectionCourse.id],
      })

      if (deleteSectionsResp.error) {
        addNotification({
          id: uuid(),
          status: 'error',
          message: t('tips:course.section_delete_error'),
        })

        setIsContextMenuOpen(false)
        setOpenDeleteTheme(false)

        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.section_delete_success'),
      })

      setIsContextMenuOpen(false)
      setOpenDeleteTheme(false)

      return
    }

    if (course_id) {
      const deleteResp = await deleteSectionThemes({
        courseId: course_id,
        sectionId,
        themesIds: [themeCourse.id],
      })

      if (deleteResp.error) {
        addNotification({
          message: t('tips:course.theme_delete_error'),
          status: 'error',
          id: uuid(),
        })

        setIsContextMenuOpen(false)
        setOpenDeleteTheme(false)

        return
      }

      addNotification({
        id: uuid(),
        status: 'success',
        message: t('tips:course.theme_delete_success'),
      })

      setIsContextMenuOpen(false)
      setOpenDeleteTheme(false)
    } else {
      dispatch(
        deleteThemeCourseFromSection({
          sectionId: sectionId,
          themeIdForDelete: themeCourse.id,
        }),
      )
    }
  }

  useEvent('click', handleOutsideContextMenuClick, window)
  useEvent('click', handleOutsideThemeMenuClick, window)

  return (
    <div ref={setNodeRef} style={dndStyle} className={cx('sortableWrap')}>
      {openDeleteTheme && (
        <DeleteThemeModal
          open={openDeleteTheme}
          onClose={handleDeleteThemeClose}
          onConfirmClick={handleConfirmDeleteThemeClick}
          isLoading={isDeleteSectionThemesLoading || isDeleteCourseSectionsLoading}
        />
      )}
      <div ref={contextMenuRef} className={cx('dragMenuWrap')}>
        <Tooltip
          content={t('hold_to_move')}
          tooltipClassname={cx('themeTooltip', isDragging && 'themeTooltip_hidden')}
          // eslint-disable-next-line i18next/no-literal-string
          position='bottom-left'
          delay={400}
        >
          <ButtonIcon
            icon='dragDotted'
            type='button'
            iconSize='14'
            onClick={handleDragBtnClick}
            dndListeners={listeners}
            dndAttributes={attributes}
            className={cx('themeDragBtn')}
            color='gray70'
            disabled={isLoading}
          />
        </Tooltip>
        {isContextMenuOpen && (
          <Card className={cx('contextMenu')}>
            <ul>
              <li ref={themeMenuListRef} className={cx('contextMenuItem')}>
                <button
                  type='button'
                  className={cx('contextMenuItemBtn')}
                  disabled={!isNewCourseHaveSections}
                  onClick={() => {
                    setIsMoveAllThemeMenuOpen(prev => !prev)
                  }}
                >
                  {t('move_another_section')}{' '}
                  <IconWrapper>
                    <ChevroneMediumIcon />
                  </IconWrapper>
                </button>

                {isMoveAllThemeMenuOpen && (
                  <Card className={cx('moveAllThemeMenu')}>
                    <ul>
                      <li
                        className={cx(
                          'moveThemeMenuItem',
                          'moveThemeMenuItemBold',
                          'moveThemeMenuItemTitle',
                        )}
                      >
                        {t('section_name')} {isTransportThemeInDiffSectionLoading && <Loader />}
                      </li>
                      {allSectionsCourse.map(section => (
                        <li key={section.id} className={cx('moveThemeMenuItem')}>
                          <button
                            type='button'
                            className={cx('moveThemeMenuItemBtn')}
                            disabled={
                              section.id === sectionId || isTransportThemeInDiffSectionLoading
                            }
                            onClick={() => {
                              handleReplaceThemeClick(sectionId, section.id, themeCourse)
                            }}
                          >
                            <IconWrapper
                              disabled={
                                section.id === sectionId || isTransportThemeInDiffSectionLoading
                              }
                            >
                              <ChevroneMediumIcon />
                            </IconWrapper>
                            <span>{section.title || `${t('section')} ${section.index + 1}`}</span>
                          </button>
                        </li>
                      ))}
                    </ul>
                  </Card>
                )}
              </li>
              <li className={cx('moveThemeMenuItem')}>
                <button
                  className={cx('deleteThemeMenuItemBtn')}
                  type='button'
                  onClick={handleDeleteThemeClick}
                  disabled={isDeleteSectionThemesLoading || isDeleteCourseSectionsLoading}
                >
                  {isDeleteSectionThemesLoading || isDeleteCourseSectionsLoading
                    ? t('commons:loading') + '...'
                    : t('commons:delete')}
                </button>
              </li>
            </ul>
          </Card>
        )}
      </div>
      <ThemeCourse
        themeCourse={themeCourse}
        sectionOrder={sectionCourse.index + 1}
        onThemeClick={handleThemeClick}
        isDisabled={isLoading}
      />
    </div>
  )
}
