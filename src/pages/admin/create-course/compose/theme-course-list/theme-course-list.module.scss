.cardWrapper {
  padding: 20px 24px 24px;
  border: 2px solid transparent;
}

.cardTitle {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-3-medium);

  span {
    color: var(--color-error);
  }
}

.validationError {
  border: 2px solid var(--color-error);
}

.cardTitleWrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.cardItems {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
  // max-height: 430px;
  // overflow-y: scroll;
}

.actionWrap {
  display: flex;
  gap: 12px;
}

.sortableWrap {
  display: flex;
  align-items: center;
}

.themeDragBtn {
  margin-right: 5px;
  cursor: grab;
}

.dragMenuWrap {
  position: relative;
}

.dragMenu {
  position: absolute;
  padding: 16px 20px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.1019607843);

  z-index: 1;
}

.dragMenuItem {
  font: var(--font-text-1-normal);

  &:hover {
    opacity: 0.6;
  }
}

.fontRed {
  color: #ff586b;
}

.moveThemeMenuWrapper {
  position: relative;
}

.contextMenu {
  position: absolute;
  padding: 16px;
  width: max-content;
  z-index: 1;
  box-shadow: 0px 8px 16px 0px #0000001a;
}

.contextMenuItem {
  font: var(--font-text-2-normal);
  color: var(--color-gray-80);
}

.moveThemeMenu {
  position: absolute;
  padding: 16px;
  width: max-content;
  z-index: 1;
  box-shadow: 0px 8px 16px 0px #0000001a;
}

.moveThemeMenuTitle {
  font: var(--font-text-2-medium);
  margin-bottom: 10px;
}

.moveThemeMenuItem {
  font: var(--font-text-2-normal);
  color: var(--color-gray-80);
}

.contextMenuItemBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  width: 100%;
  font: var(--font-text-2-normal);
  color: inherit;
  border-radius: 8px;

  &:hover {
    background-color: var(--color-gray-40);
  }

  &:disabled {
    opacity: 0.4;
    background-color: transparent;
  }
}

.moveThemeMenuItemBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  width: 100%;
  font: var(--font-text-2-normal);
  color: inherit;
  border-radius: 8px;

  &:hover {
    background-color: var(--color-gray-40);
  }

  span {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  &:disabled {
    color: var(--color-gray-60);
    background: transparent;
  }
}

.deleteThemeMenuItemBtn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  width: 100%;
  font: var(--font-text-2-normal);
  color: var(--color-error);
  border-radius: 8px;

  &:hover {
    background-color: var(--color-gray-40);
  }
}

.dragTooltip {
  width: max-content;
  text-align: center;
}

.moveAllThemeMenu {
  position: absolute;
  top: 50px;
  left: 82px;
  width: 100%;
  max-width: 906px;
  padding: 16px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.1);
}

.moveThemeMenuItemBold {
  font: var(--font-text-2-medium);
  color: var(--color-gray-90, #343b54);
}

.moveThemeMenuItemTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.themeTooltip {
  width: max-content;

  &_hidden {
    display: none;
  }
}
