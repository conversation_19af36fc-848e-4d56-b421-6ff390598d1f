/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useMemo } from 'react'

import { PAGE_ITEMS_LIMIT } from './config'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '@/entities/phishing'
import { type IPhishingStatus } from '@/entities/phishing'
import { useAppDispatch, useAppSelector } from '@/store'
import { phishingCampaignsByTagActions, phishingCampaignsByTagSelectors } from './slice'
import { URLS } from '@/shared/configs/urls'
import { useDebounceValue } from 'usehooks-ts'

export const usePhishingByTags = () => {
  const [currentQueryParameters, setSearchParams] = useSearchParams()
  const { t } = useTranslation()
  const dispatch = useAppDispatch()
  const { filters, limit, offset } = useAppSelector(
    phishingCampaignsByTagSelectors.selectPhishingCampaigns,
  )
  const {
    data: phishingStatuses,
    isLoading: isStatusesLoading,
    isFetching: isStatusesFetching,
    isError: isStatusesError,
    status: campaignStatus,
  } = phishingQueries.useGetPhishingCampaignsStatusesQuery({ params: { by_tag: true } })
  const [debouncedSearch] = useDebounceValue(filters.search, 500)
  const navigate = useNavigate()
  const page = useAppSelector(phishingCampaignsByTagSelectors.selectPagination)
  const onAddClick = () => {
    navigate(URLS.ADMIN_STAFF_TAGS_PAGE)
  }

  const onCardClick = (id: UUID) => {
    if (id) navigate('/lk/admin/phishing/phishing-by-tags/campaigns/' + id)
  }

  const TABS_CONFIG = useMemo(
    () => [
      {
        label: t('commons:active'),
        value: 'active',
        count: 0,
      },
      {
        label: t('commons:postponed'),
        value: 'planned',
        count: 0,
      },
      {
        label: t('commons:completed'),
        value: 'completed',
        count: 0,
      },
    ],
    [t],
  )

  const filtredTabs = useMemo(() => {
    if (phishingStatuses) {
      return TABS_CONFIG.filter(t => !!phishingStatuses[t.value as IPhishingStatus]).map(
        (t: any) => {
          t.count = phishingStatuses[t.value as IPhishingStatus]

          return t
        },
      )
    }

    return []
  }, [phishingStatuses, TABS_CONFIG])

  const status = filters?.status
  const { data, isLoading, isError } = phishingQueries.useGetPhishingCampaignsQuery(
    {
      status,
      limit,
      offset,
      filters: { ...filters, search: debouncedSearch },
    },
    {
      skip: !status,
    },
  )

  const onTabClick = (value: string) => {
    if (value) {
      const newQueryParameters: URLSearchParams = new URLSearchParams()

      newQueryParameters.set('tab', value)
      newQueryParameters.set('page', '1')
      setSearchParams(newQueryParameters)

      dispatch(phishingCampaignsByTagActions.setFilters({ status: value as IPhishingStatus }))
      dispatch(phishingCampaignsByTagActions.setPage(0))
    }
  }

  useEffect(() => {
    const status = currentQueryParameters.get('tab')
    const page = currentQueryParameters.get('page')
    const search = currentQueryParameters.get('search')
    dispatch(
      phishingCampaignsByTagActions.setFilters({
        status: (status as IPhishingStatus) ?? 'active',
        search: search ?? null,
      }),
    )
    dispatch(phishingCampaignsByTagActions.setPage(page ? Number(page) - 1 : 0))
  }, [])

  useEffect(() => {
    if (!filtredTabs || !filtredTabs.length) return

    const currentTab = filtredTabs.find(tab => tab.value === status)

    if (filtredTabs?.length > 0 || currentTab) {
      onTabClick(currentTab?.value ?? filtredTabs[0].value)
    }
  }, [filtredTabs])

  const handleClickPagination = (newPage: string) => {
    if (newPage) {
      const newQueryParameters: URLSearchParams = new URLSearchParams()

      newQueryParameters.set('page', newPage)
      newQueryParameters.set('tab', filters.status ?? 'active')
      setSearchParams(newQueryParameters)
    }
  }

  const onSetPage = (page: number) => {
    dispatch(phishingCampaignsByTagActions.setPage(page))
    handleClickPagination(String(page + 1))
  }

  const isDataLoading = isStatusesLoading || isLoading
  const phishingCampaignsCount =
    phishingStatuses &&
    !isDataLoading &&
    phishingStatuses?.active + phishingStatuses?.completed + phishingStatuses?.planned
  const hasError = isError && isStatusesError

  const COUNT_PAGE = Math.ceil((data?.total_count || 0) / PAGE_ITEMS_LIMIT)
  const isPaginationVisible = data?.data && COUNT_PAGE > 1

  const hasFilters = useAppSelector(phishingCampaignsByTagSelectors.selectHasFilters)
  const filterModalOpen = useAppSelector(phishingCampaignsByTagSelectors.selectFilterModalOpen)

  return {
    isLoading: isDataLoading,
    isFetching: isStatusesFetching,
    onAddClick,
    phishingCampaignsCount,
    tabs: filtredTabs,
    onTabClick,
    tab: status,
    data,
    ACTUAL_PAGE: page.offset / page.limit,
    PAGE_ITEMS_LIMIT,
    isPaginationVisible,
    onSetPage,
    onCardClick,
    hasError,
    campaignStatus,
    filters,
    setFilters: (f: Partial<typeof filters>) =>
      dispatch(phishingCampaignsByTagActions.setFilters(f)),
    hasFilter: hasFilters,
    isModalOpen: filterModalOpen,
  }
}
