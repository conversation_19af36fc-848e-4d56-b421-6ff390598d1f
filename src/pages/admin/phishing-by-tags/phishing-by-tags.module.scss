.wrapper {
  display: flex;
  flex-direction: column;

  min-height: 100%;
}

.title-wrapper {
  align-items: center;
  display: flex;
  justify-content: space-between;
  gap: 24px;
  margin-bottom: 24px;

  .title {
    margin-bottom: 0;
    width: fit-content;
    flex-shrink: 0;
  }
}

.autophish-wrapper {
  align-items: center;
  display: flex;
  gap: 32px;

  .autophish {
    align-items: center;
    display: flex;
    gap: 12px;

    span {
      color: var(--gray-gray-70, #8e97af);
      font: var(--font-text-1-normal);
    }
  }
}

.loader-wrapper {
  height: 100%;
}

.empty-wrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 24px;

  height: 100%;
  justify-content: center;

  span {
    color: var(--gray-gray-70, #8e97af);
    display: block;
    font: var(--font-title-3-medium);
  }
}

.tabs {
  &__container {
    margin-bottom: 16px;
  }
}

.list-wrapper {
  background: var(--stroke, #ebeff2);
  border: 1px solid var(--stroke, #ebeff2);

  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.list {
  &__item {
    border-bottom: 1px solid var(--stroke, #ebeff2);
    &:last-child {
      border-bottom-right-radius: 16px;
      border-bottom-left-radius: 16px;
    }
  }
}

.tabs-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.header {
  background: var(--color-surface, #fff);
  border-bottom: 1px solid var(--stroke, #ebeff2);
  border-radius: 16px 16px 0 0;

  display: flex;
  justify-content: flex-end;
  flex-direction: row;
  padding: 10px 24px 10px 16px;
}

.searchWrapper {
  width: 100%;
  .search {
    background: #fff;
    width: 100%;
  }
}

.data-loading__wrapper {
  margin: 120px 0;
}