/* eslint-disable i18next/no-literal-string */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { usePhishingByTags } from './use-phishing-by-tags'
import styles from './phishing-by-tags.module.scss'
import classNamesBind from 'classnames/bind'
import { Loader, PageTitle, Pagination, SearchInput, TabItem, TabsNew } from '@/shared/ui'
import { PhishingCampaignsListSort, PhishingCampaignsProps } from '../phishing-campaigns'
import { FC } from 'react'
import { PhishingCampaignCard } from '@/shared/components/phishing-campaign-card'
import { useTranslation } from 'react-i18next'
import { removeLastSegmentAfterDelimiter } from '@/shared/helpers'
import { PhishingCampaignsFilters } from '../phishing-campaigns/phishing-campaigns-filters'
import { phishingCampaignsByTagActions, phishingCampaignsByTagSelectors } from './slice'
import { useAppDispatch, useAppSelector } from '@/store'
import { LoadingDataStatus } from '@/shared/components/loading-data-status'
import { mapFormToApiParams } from '../phishing-campaigns/helpers'
import { format } from '@/shared/helpers/date'

const TRANSLATION_FILE = 'pages__phishing-campaigns'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignsList: FC<PhishingCampaignsProps.ListProps> = props => {
  const { className, list, onCardClick } = props
  const activeSort = useAppSelector(phishingCampaignsByTagSelectors.selectSorting)
  const dispatch = useAppDispatch()

  return (
    <div className={cx('list-wrapper', className)}>
      <div className={cx('header')}>
        <PhishingCampaignsListSort
          activeSort={activeSort.sort_by}
          onChange={s => dispatch(phishingCampaignsByTagActions.setFilters({ sort_by: s }))}
          onChangeSortDirection={dir =>
            dispatch(phishingCampaignsByTagActions.setFilters({ sort_order: dir }))
          }
          activeSortDirection={activeSort.sort_order}
        />
      </div>
      <div>
        {list.map(item => (
          <PhishingCampaignCard
            key={`campaign-card-${item.id}`}
            className={cx('list__item')}
            data={item}
            withEndDate={false}
            onClick={onCardClick}
          />
        ))}
      </div>
    </div>
  )
}

export const PhishingByTags = () => {
  const {
    isLoading,
    isFetching,
    phishingCampaignsCount,
    tabs,
    onTabClick,
    tab,
    data,
    ACTUAL_PAGE,
    PAGE_ITEMS_LIMIT,
    isPaginationVisible,
    onSetPage,
    onCardClick,
    hasError,
    onAddClick,
    campaignStatus,
    filters,
    setFilters,
    hasFilter,
    isModalOpen,
  } = usePhishingByTags()
  const dispatch = useAppDispatch()
  const { t } = useTranslation(TRANSLATION_FILE)

  return (
    <div className={cx('wrapper')}>
      <div className={cx('title-wrapper')}>
        <PageTitle className={cx('title')}>{t('commons:phishing_by_tags')}</PageTitle>
        <SearchInput
          className={cx('search')}
          classNameWrapper={cx('searchWrapper')}
          placeholder={t('search')}
          onChange={search => setFilters({ search })}
          value={filters.search ?? ''}
        />
      </div>
      {isLoading && (
        <div className={cx('loader-wrapper')}>
          <Loader size='56' className='loader_centered' />
        </div>
      )}
      {!isLoading && hasError && (
        <div className={cx('error-text')}>
          {t('commons:error_occurred_while_receiving_data')} :(
        </div>
      )}
      {!isLoading && !hasError && !phishingCampaignsCount && (
        <div className={cx('empty-wrapper')}>
          <span>{t('commons:have_not_mailings')}</span>
        </div>
      )}
      {!isLoading && !hasError && !!phishingCampaignsCount && (
        <>
          <div className={cx('tabs-wrapper')}>
            {tab && tabs && (
              <TabsNew
                activeTab={tab}
                tabClassname={cx('tabs__item')}
                activeTabClassname={cx('tabs__item_active')}
                tabsClassname={cx('tabs')}
                className={cx('tabs__container')}
                onChange={v => onTabClick(v)}
              >
                {tabs.map((tab: any) => {
                  return (
                    <TabItem key={tab.value} label={tab.label} value={tab.value} count={tab.count}>
                      <></>
                    </TabItem>
                  )
                })}
              </TabsNew>
            )}
            <PhishingCampaignsFilters
              hasFilter={!!hasFilter}
              isModalOpen={isModalOpen}
              filters={filters}
              onModalOpen={v => dispatch(phishingCampaignsByTagActions.setFilterModalOpen(v))}
              resetFilters={() => dispatch(phishingCampaignsByTagActions.resetFilters())}
              onSubmit={data => {
                dispatch(
                  phishingCampaignsByTagActions.setFilters(
                    mapFormToApiParams(data, date => format(date, 'yyyy-MM-dd')),
                  ),
                )
                dispatch(phishingCampaignsByTagActions.setFilterModalOpen(false))
                dispatch(phishingCampaignsByTagActions.setPage(0))
              }}
            />
          </div>
          {!!data?.data.length && (
            <PhishingCampaignsList
              list={data?.data.map(v => {
                const newItem = {
                  ...v,
                  name: removeLastSegmentAfterDelimiter(v.name),
                }

                return newItem
              })}
              onCardClick={onCardClick}
            />
          )}

          {!data?.data?.length && (
            <LoadingDataStatus
              data={data?.data}
              fetchStatus={campaignStatus}
              dataLength={data?.data?.length}
              createHandler={onAddClick}
              texts={{ empty: t('empty') }}
              wrapperClass={cx('data-loading__wrapper')}
            />
          )}
          {isPaginationVisible && (
            <Pagination
              currentPage={ACTUAL_PAGE}
              limit={PAGE_ITEMS_LIMIT}
              onChange={onSetPage}
              total={data?.total_count || 0}
              isLoading={isLoading || isFetching}
            />
          )}
        </>
      )}
    </div>
  )
}
