import { useNavigate } from 'react-router-dom'
import {
  URLS,
  getPhishingRedirectDetailUrl,
  getPhishingRedirectEditUrl,
} from '@/shared/configs/urls'
import { type RedirectPage, phishingQueries, phishingMutations } from '@/entities/phishing'

export const usePhishingRedirectPages = () => {
  const { data, status, originalArgs } = phishingQueries.useGetRedirectPagesQuery()
  const [createRedirectPage, { isLoading: isLoadingCreate, originalArgs: createArgs }] =
    phishingMutations.useCreateRedirectPageMutation()
  const [deleteRedirectPage] = phishingMutations.useDeleteRedirectPageMutation()
  const [getPageInfo] = phishingQueries.useLazyGetRedirectPageQuery()
  const navigate = useNavigate()

  const onItemClick = (id: number | string) => {
    navigate(getPhishingRedirectDetailUrl(id))
  }

  const onItemEdit = (id: string) => {
    navigate(getPhishingRedirectEditUrl(id))
  }

  const onCreateItem = () => {
    navigate(URLS.ADMIN_PHISHING_REDIRECT_CREATE_PAGE)
  }
  const onItemCopyClick = async (data: RedirectPage) => {
    if (!data?.id) return

    const page = await getPageInfo({ id: data?.id }).unwrap()

    createRedirectPage({ html: page?.html, name: page?.name + '(copy)' })
  }

  const onItemDelete = (id: string) => {
    deleteRedirectPage({ id })
  }
  const isItemCopiesLoading = false

  return {
    data,
    onCreateItem,
    onItemEdit,
    onItemClick,
    dataFetchStatus: status,
    originalArgs,
    onItemCopyClick,
    isItemCopiesLoading,
    isLoadingCreate,
    createArgs,
    onItemDelete,
  }
}
