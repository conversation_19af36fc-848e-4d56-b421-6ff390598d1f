import { useNavigate, useParams } from 'react-router-dom'
import { Form } from './redirect-pages'
import { useNotification } from '@/shared/contexts/notifications'
import { SubmitHandler, useForm } from 'react-hook-form'
import { getPhishingRedirectDetailUrl } from '@/shared/configs/urls'
import { prepareReplacedMapForLogos, replaceLogoUrls } from '@/shared/helpers'
import { useConfig } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'
import { phishingQueries, phishingMutations } from '@/entities/phishing'

export const useRedirectTemplatesForms = () => {
  const { id = '' } = useParams()
  const { t } = useTranslation()
  const { handleErrorResponse } = useNotification()
  const navigate = useNavigate()

  const [createRedirectPage, { isLoading: isCreateLoading }] =
    phishingMutations.useCreateRedirectPageMutation()
  const [editRedirectPage, { isLoading: isEditLoading }] =
    phishingMutations.useEditRedirectPageMutation()

  const isDisabled = isCreateLoading || isEditLoading
  const isEdit = id

  const { data: redirectData, isLoading } = phishingQueries.useGetRedirectPageQuery(
    { id },
    { skip: !id },
  )

  const config = useConfig()

  const form = useForm<Form>({
    values: {
      ...redirectData,
      html: replaceLogoUrls(redirectData?.html || '', prepareReplacedMapForLogos(config)),
    } as Form,
  })

  const title = isEdit
    ? `${t('commons:editing')} ${redirectData?.name ?? ''}`
    : t('commons:creating_redirect_page')

  const onSubmit: SubmitHandler<Form> = async data => {
    if (isDisabled) return

    if (!data.html) {
      handleErrorResponse({
        status: 'error',
        message: t('commons:fill_html_template'),
      })
      return
    }

    if (isEdit && id) {
      const editedData = await editRedirectPage({ id, data }).unwrap()

      if (editedData?.id) navigate(getPhishingRedirectDetailUrl(editedData?.id))

      return
    }

    const createdEmail = await createRedirectPage(data).unwrap()

    if (createdEmail?.id) navigate(getPhishingRedirectDetailUrl(createdEmail?.id))
  }

  const ButtonText = isEdit ? t('commons:save_changes') : t('commons:create_page')

  return {
    isDisabled,
    title,
    onSubmit,
    form,
    isLoading,
    ButtonText,
  }
}
