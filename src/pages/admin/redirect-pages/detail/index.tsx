import { useNavigate, useParams } from 'react-router-dom'
import { phishingQueries } from '@/entities/phishing'
import styles from '../redirect-pages.module.scss'
import classNamesBind from 'classnames/bind'
import { BackButton, ButtonIcon, Card, Loader, PageTitle } from '@/shared/ui'
import { getPhishingRedirectEditUrl } from '@/shared/configs/urls'
import { PagePreview } from '@/shared/components'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const RedirectPageDetail = () => {
  const { id = '' } = useParams()
  const { t } = useTranslation()
  const { data, isLoading, isError, isSuccess } = phishingQueries.useGetRedirectPageQuery(
    { id },
    { skip: !id },
  )
  const navigate = useNavigate()

  return (
    <section className={cx('demo-page__wrapper')}>
      <BackButton className={cx('back')} />
      <PageTitle className={cx('title')}>{data?.name}</PageTitle>
      <div className={cx('demo-page__title-wrapper')}>
        <h3>{t('commons:redirect_page')}</h3>
        {data?.can_edit && data && data?.html !== null && (
          <ButtonIcon
            onClick={() => {
              navigate(getPhishingRedirectEditUrl(id))
            }}
            className={cx('demo-page__edit')}
            size='28'
            iconSize='20'
            title={`${t('commons:edit_phishing_page')} ${data?.name}`}
            icon='editBold'
          />
        )}
      </div>
      <Card className={cx('demo-page__card')}>
        <div className={cx('demo-page__content', !data?.html && 'empty')}>
          {isLoading && <Loader size='56' />}
          {isError && <Loader size='56' loading={false} error />}
          {isSuccess && data?.html && <PagePreview html={data?.html} viewportWidth={1280} />}
        </div>
      </Card>
    </section>
  )
}

export default RedirectPageDetail
