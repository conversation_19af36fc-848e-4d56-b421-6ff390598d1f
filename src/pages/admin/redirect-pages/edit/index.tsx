import { FC } from 'react'
import styles from '../redirect-pages.module.scss'
import classNamesBind from 'classnames/bind'
import { EditPhishingTemplateProps } from '../redirect-pages'
import { useRedirectTemplatesForms } from '../use-redirect-templates-forms'
import { BackButton, Button, Input, PageTitle } from '@/shared/ui'
import { TabsPage } from '../tabs-page'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const CreateRedirectTemplate: FC<EditPhishingTemplateProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const { title, form, isLoading, onSubmit, ButtonText } = useRedirectTemplatesForms()

  return (
    <div className={cx('forms__wrapper', className)}>
      <BackButton className={cx('back')} />
      <div className={cx('rowed')}>
        <PageTitle className={cx('title')}>{title}</PageTitle>
        <Button
          disabled={isLoading || !(form.formState.isValid && form.formState.isDirty)}
          onClick={form.handleSubmit(onSubmit)}
          type='submit'
          loading={isLoading}
        >
          {ButtonText}
        </Button>
      </div>
      <Input
        register={form.register('name', {
          required: t('commons:required_field'),
        })}
        placeholder={t('commons:name_for_redirect_page')}
        label={t('commons:name_page')}
        className={cx('form__field')}
      />
      <TabsPage form={form} />
    </div>
  )
}

export default CreateRedirectTemplate
