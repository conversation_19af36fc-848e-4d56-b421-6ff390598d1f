import React, { FC } from 'react'
import { QueryStatus } from '@reduxjs/toolkit/query'

import { Loader, RoundedButton, PageTitle, ButtonIcon, Button } from '@/shared/ui'
import { usePhishingRedirectPages } from './use-redirect-pages'

import styles from './redirect-pages.module.scss'
import classNamesBind from 'classnames/bind'
// import { sortTemplatesByCategoryName } from './helpers'
import { PhishingTemplatesProps } from './redirect-pages'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const TemplatesList: React.FC<PhishingTemplatesProps.TemplateList> = ({
  copyArgs,
  isItemCopiesLoading,
  data,
  onItemClick,
  onItemCopy,
  onItemEdit,
  onItemDelete,
}) => {
  const { t } = useTranslation()

  return (
    <ul className={cx('list')}>
      {data?.map(i => (
        <li
          onClick={() => {
            if (i?.id) onItemClick(i?.id ?? '')
          }}
          className={cx('template-list__item')}
          title={i.name}
          key={i.id}
        >
          <p className={cx('template-list__item__name')}>{i.name}</p>
          <div className={cx('template-list__item__actions')}>
            {i.can_edit && (
              <div title={`${t('commons:edit')} ${i.name}`} role='button'>
                <ButtonIcon
                  onClick={(e: { stopPropagation: () => void }) => {
                    e.stopPropagation()

                    if (i?.id) onItemEdit(i?.id)
                  }}
                  className={cx('template-list__item__actions__btn')}
                  color='gray70'
                  icon='editBold'
                />
              </div>
            )}

            {i?.can_read && (
              <div
                role='button'
                className={cx('template-list__item__actions__btn')}
                title={`${t('commons:copy')} ${i.name}`}
                color='gray'
                onClick={e => {
                  e.stopPropagation()
                  onItemCopy(i)
                }}
              >
                {isItemCopiesLoading && copyArgs?.id === i?.id ? (
                  <ButtonIcon
                    className={cx('template-list__item__actions__btn')}
                    classNameForIcon={cx('animate-spin')}
                    color='gray70'
                    size='28'
                    icon='load'
                  />
                ) : (
                  <ButtonIcon
                    className={cx('template-list__item__actions__btn')}
                    color='gray70'
                    size='28'
                    icon='copy'
                  />
                )}
              </div>
            )}
            {i?.can_delete && (
              <ButtonIcon
                title={`${t('commons:delete')} ${i.name}`}
                className={cx('template-list__item__actions__btn')}
                color='red'
                size='28'
                icon='trashBold'
                onClick={e => {
                  e.stopPropagation()
                  if (i?.id) onItemDelete(i?.id)
                }}
              />
            )}
          </div>
        </li>
      ))}
    </ul>
  )
}
export const TRANSLATION_FILE = 'pages__redirect-pages'

export const RedirectTemplates: FC<PhishingTemplatesProps.Props> = props => {
  const { className } = props
  const {
    data,
    onCreateItem,
    dataFetchStatus,
    onItemClick,
    onItemEdit,
    // originalArgs,
    createArgs,
    onItemDelete,
    onItemCopyClick,
    isItemCopiesLoading,
  } = usePhishingRedirectPages()

  const { t } = useTranslation(TRANSLATION_FILE)

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('templates__wrapper')}>
        <PageTitle className={cx('title')}>{t('commons:redirect')}</PageTitle>
        <RoundedButton size='40' onClick={() => onCreateItem()} />
      </div>
      {!data && dataFetchStatus === QueryStatus.pending && (
        <div className={cx('plug__wrapper')}>
          <Loader size='56' />
        </div>
      )}
      {!data && dataFetchStatus === QueryStatus.rejected && (
        <div className={cx('plug__wrapper')}>
          <Loader error={true} loading={false} />
          <h2>{t('commons:loading_error')}...</h2>
        </div>
      )}
      {data && data?.data?.length === 0 && dataFetchStatus === QueryStatus.fulfilled && (
        <div className={cx('empty-wrapper')}>
          <span>{t('empty_text')}</span>
          <Button onClick={onCreateItem}>{t('create_new_text')}</Button>
        </div>
      )}
      {data && (
        <TemplatesList
          onItemDelete={onItemDelete}
          copyArgs={{ id: createArgs?.id ?? '' }}
          onItemClick={onItemClick}
          data={data?.data}
          isItemCopiesLoading={isItemCopiesLoading}
          onItemCopy={onItemCopyClick}
          onItemEdit={onItemEdit}
        />
      )}
    </div>
  )
}

export default RedirectTemplates
