@use '../../../shared/assets/styles/mixins/icons';
@use '../../../shared/assets/styles/mixins/text';
@use '../../../shared/assets/styles/mixins/media';

.forms__wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.empty-wrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 24px;

  height: 100%;
  justify-content: center;

  span {
    color: var(--gray-gray-70, #8e97af);
    display: block;
    font: var(--font-title-3-medium);
  }
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.title {
  margin-bottom: 12px;
}

.form__field {
  max-width: 450px;
  width: 100%;
}

.rowed {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.templates__wrapper {
  align-items: center;
  display: flex;
  justify-content: space-between;
}

.plug__wrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: 100dvh;

  text-align: center;
  width: 100%;
}

.list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;

  &__item {
    display: flex;
    flex-direction: column;
    gap: 16px;

    &__title {
      color: var(--gray-gray-90);
      font: var(--font-title-4-medium);
    }
  }
}

.template-list {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(4, 1fr);

  @include media.mqXl() {
    grid-template-columns: repeat(3, 1fr);
  }

  @include media.mqL() {
    gap: 8px;
    grid-template-columns: repeat(2, 1fr);
  }

  @include media.mqS() {
    grid-template-columns: repeat(1, 1fr);
  }

  &__item {
    align-items: center;

    background-color: var(--white);
    border: 1px solid var(--gray-gray-30);

    border-radius: 12px;

    cursor: pointer;

    display: grid;
    gap: 12px;
    grid-template-columns: 1fr auto;
    height: 48px;

    padding: 10px 12px;

    transition: var(--transition);
    width: 100%;

    @include media.mqL() {
      padding: 8px;
    }

    &__name {
      @include text.max-lines(1);
      color: var(--color-gray-100);
      font: var(--font-text-2-normal);
      width: 100%;
    }

    img {
      height: 24px;
      width: 24px;
    }

    &__actions {
      display: flex;
      margin-left: auto;

      &__btn {
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
    .template-list__item__actions {
      display: none;
    }

    &:hover {
      background: var(--color-gray-20, #f9fafc);

      transition: var(--transition);

      > .template-list__item__actions {
        display: flex;
      }
    }
  }
}

.back {
  margin-bottom: 0;
}

.actions {
  align-items: center;
  display: flex;
  gap: 24px;
  justify-content: space-between;
}

.ckeditor-wrapper {
  height: 100%;
  > *:last-child {
    height: 100%;
    > *:last-child {
      display: grid;
      grid-template-rows: auto 1fr auto;

      height: 100%;
    }
  }
}

.tab {
  &__wrapper {
    background-color: var(--white);
    border-radius: 0 12px 12px;
    height: 600px;
    width: 100%;
  }
}

.title {
  margin-bottom: 0;
}

.tabs {
  gap: 8px;
  &__tab {
    background: var(--white);
    border-radius: 12px 12px 0 0;
    display: flex;

    font: var(--font-text-2-medium);
    opacity: 0.7;
    padding: 8px 16px 6px;

    transition: 0.2s ease;

    &_active {
      opacity: 1;
    }

    &:not(&_active):hover {
      opacity: 0.85;
    }
    &:nth-child(1) {
      background: var(--white);
    }
    &:nth-child(2) {
      background: #1a1a1a;
      color: var(--white);
    }
  }
}

.tabs__page {
  position: relative;
}

.format__btn {
  position: absolute;
  right: 0;
  top: 4px;

  * {
    font: var(--font-text-2-medium);
  }

  &:hover {
    path {
      stroke: var(--color-gray-90) !important;
    }
  }
}

.preview {
  overflow: hidden;
  overflow-y: scroll;
}

.white-underlay {
  background-color: var(--white);
  border-radius: 0 12px 12px;
  height: 100%;
  width: 100%;
}

.autoformatting {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
}

.demo-page {
  &__wrapper {
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__title-wrapper {
    align-items: center;
    display: flex;
    gap: 8px;
  }

  &__card {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  &__header {
    align-items: center;

    background: var(--color-gray-50);
    border-top-left-radius: 16px;

    border-top-right-radius: 16px;

    display: flex;
    justify-content: center;
    padding: 4px 8px;
    position: relative;
  }

  &__badge {
    background-color: var(--color-gray-40);
    border-radius: 4px;

    color: var(--color-gray-70);
    font: var(--font-text-2-normal);
    padding: 0 12px;
    text-align: center;
    width: 50%;
  }
  &__controls {
    border-radius: 100%;
    height: 8px;
    width: 8px;
    &__wrapper {
      align-items: center;

      display: flex;
      gap: 4px;
      left: 16px;
      position: absolute;
      top: 12px;
    }
  }

  &__controls:nth-child(1) {
    background: #ff5f57;
  }

  &__controls:nth-child(2) {
    background: #febc2e;
  }

  &__controls:nth-child(3) {
    background: #28c840;
  }

  &__content {
    border-bottom-left-radius: 16px;

    border-bottom-right-radius: 16px;
    flex-grow: 1;
    height: 480px;
    overflow-y: auto;
    width: 100%;

    &.empty {
      align-items: center;
      display: flex;
      height: 100%;
      justify-content: center;
      width: 100%;
    }
  }
}
