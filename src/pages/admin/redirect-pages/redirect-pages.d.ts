import { IPhishingTemplateByCategory, RedirectPage } from '@/shared/types/store/phishing'

export declare namespace PhishingTemplatesProps {
  interface Own {
    className?: string
  }

  type Props = Own

  interface TemplateList {
    data: RedirectPage[]
    onItemClick: (id: string) => void
    onItemEdit: (id: string) => void
    onItemCopy: (data: RedirectPage) => void
    onItemDelete: (id: string) => void
    isItemCopiesLoading: boolean
    copyArgs?: { id: string }
  }
}

export declare namespace CreatePhishingTemplateProps {
  interface Own {
    className?: string
  }

  type Props = Own

  interface TemplateList {
    data: IPhishingTemplateByCategory[]
    onItemClick: (id: string) => void
    onItemEdit: (id: string) => void
    onItemCopy: (data: RedirectPage) => void
    isItemCopiesLoading: boolean
    copyArgs?: { id: string }
  }
}

export declare namespace EditPhishingTemplateProps {
  interface Own {
    className?: string
  }

  type Props = Own

  interface TemplateList {
    data: IPhishingTemplateByCategory[]
    onItemClick: (id: string) => void
    onItemEdit: (id: string) => void
    onItemCopy: (data: RedirectPage) => void
    isItemCopiesLoading: boolean
    copyArgs?: { id: string }
  }
}

export type Form = {
  id?: string
  name: string
  html: string
}

export {}
