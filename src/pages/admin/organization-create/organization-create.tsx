/* eslint-disable no-useless-escape */
import { FC } from 'react'
import styles from './organization-create.module.scss'
import classNamesBind from 'classnames/bind'
import { OrganizationCreateProps } from './organization-create.d'
import { BackButton, Button, Input, PageTitle } from '@/shared/ui'
import { useOrganizationCreate } from './use-organization-create'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'pages__organization-create'

export const OrganizationCreate: FC<OrganizationCreateProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation(TRANSLATION_FILE)

  const { handleSubmit, onSubmit, errors, register, handleTrim, isLoading, isValid, error } =
    useOrganizationCreate()

  return (
    <div className={cx('wrapper', className)}>
      {/* TEMPTODO - надо будет на хлебные крошки переделать наверное */}
      <BackButton />
      <PageTitle>{t('commons:organization_create')}</PageTitle>
      <form onSubmit={handleSubmit(onSubmit)} className={cx('form')}>
        <Input
          label={t('commons:name')}
          placeholder={t('commons:name')}
          classNameWrapper={cx('formInput')}
          error={errors.title?.message}
          fullWidth
          required
          register={register('title', {
            onBlur: (e: { target: { value: string } }) => handleTrim(e.target.value, 'title', true),
          })}
        />
        <Input
          // eslint-disable-next-line i18next/no-literal-string
          label='Email'
          type='email'
          placeholder={t('email_placeholder')}
          classNameWrapper={cx('formInput')}
          error={errors.operator_email?.message}
          fullWidth
          required
          register={register('operator_email', {
            onBlur: (e: { target: { value: string } }) =>
              handleTrim(e.target.value, 'operator_email', true),
          })}
        />
        <Input
          label={t('commons:license_amount')}
          placeholder={t('commons:license_amount')}
          classNameWrapper={cx('formInput')}
          error={errors.employees_limit?.message}
          type='number'
          fullWidth
          required
          register={register('employees_limit', {
            onBlur: (e: { target: { value: string } }) =>
              handleTrim(e.target.value, 'employees_limit', true),
          })}
        />
        <Button type='submit' loading={isLoading} fullWidth disabled={!isValid}>
          {t('commons:create')}
        </Button>
        {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
      </form>
    </div>
  )
}
