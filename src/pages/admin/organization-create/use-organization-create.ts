import { useMemo, useState } from 'react'
import { organizationAPI } from 'entities/organization'
import { SubmitHandler, useForm } from 'react-hook-form'
import { useNavigate, useParams } from 'react-router-dom'
import { handleErrorResponseAndTranslate } from '@/shared/contexts/notifications/helper'
import { useTranslation } from 'react-i18next'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useUserOrganizationId } from '../../../entities/employee'

interface ICreateOrganizationInputs {
  title: string
  employees_limit: string
  operator_email: string
}

const TRANSLATION_FILE = 'pages__organization-create'

export const useOrganizationCreate = () => {
  const { organization_id = '' } = useParams()
  const { t } = useTranslation(TRANSLATION_FILE)
  const userOrganizationId = useUserOrganizationId()

  const parentOrgID = organization_id ? organization_id : userOrganizationId

  const [error, setError] = useState<string | null>(null)

  const [createOrganization, { isLoading }] = organizationAPI.useCreateOrganizationMutation()
  const navigate = useNavigate()

  const resolver = useMemo(
    () =>
      zodResolver(
        z.object({
          title: z
            .string()
            .min(1, {
              message: t('commons:required_field'),
            })
            .refine(value => !!value.trim(), {
              message: t('commons:required_field'),
            }),
          employees_limit: z.string().regex(/^\d+$/, {
            message: t('commons:enter_number'),
          }),
          operator_email: z
            .string()
            .min(1, {
              message: t('commons:required_field'),
            })
            .email({ message: t('email_valid') }),
        }),
      ),
    [t],
  )

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setValue,
    setError: setErrorForm,
  } = useForm<ICreateOrganizationInputs>({
    mode: 'all',
    defaultValues: {
      title: '',
      employees_limit: '',
      operator_email: '',
    },
    resolver,
  })

  const onSubmit: SubmitHandler<ICreateOrganizationInputs> = async data => {
    if (!parentOrgID) return

    const body = {
      title: data.title,
      employees_limit: +data.employees_limit,
      operator_email: data.operator_email,
      parent_id: parentOrgID,
    }

    await createOrganization(body)
      .unwrap()
      .then(({ id }) => {
        if (!id) {
          return setError(t('commons:error_occurred_data'))
        }

        setError('')
        reset()
        navigate(`/lk/admin/organization/${id}`)
      })
      .catch(e => setError(handleErrorResponseAndTranslate(e)))
  }

  const handleTrim = (str: string, name: keyof ICreateOrganizationInputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  return {
    handleSubmit,
    onSubmit,
    errors,
    register,
    handleTrim,
    isLoading,
    isValid,
    error,
  }
}
