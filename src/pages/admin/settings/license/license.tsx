import { FC } from 'react'
import styles from './license.module.scss'
import classNamesBind from 'classnames/bind'
import { LicenseProps } from './license.d'
import { PageTitle, Loader } from '@/shared/ui'
import { useLicense } from './use-license'
import { useTranslation } from 'react-i18next'
import { useGetBeautifulDateWithMonth } from '@/shared/hooks'

const cx = classNamesBind.bind(styles)

export const License: FC<LicenseProps.Props> = props => {
  const { className } = props
  const { data, isLoading, isError } = useLicense()
  const { t } = useTranslation()

  return (
    <div className={cx('wrapper', className)}>
      <PageTitle className={cx('title')}>{t('commons:license')}</PageTitle>

      {isLoading && <Loader size='56' loading className='loader_centered' />}
      {!isLoading && isError && (
        <div className={cx('error-text')}>{t('commons:error_unexpected')} :(</div>
      )}
      {!isLoading && !isError && data && (
        <>
          <div className={cx('card')}>
            <div className={cx('card-title')}>{data.title}</div>
            <div className={cx('card-row')}>
              <div className={cx('row-name')}>{t('commons:status')}:</div>
              <div className={cx('row-value', 'green')}>{data.license_type}</div>
            </div>
            <div className={cx('card-row')}>
              <div className={cx('row-name')}>{t('commons:uses')}:</div>
              <div className={cx('row-value')}>{`${data.users_count} / ${data.users_limit}`}</div>
            </div>
            <div className={cx('card-row')}>
              <div className={cx('row-name')}>{t('commons:date_of_completion')}:</div>
              <div className={cx('row-value')}>
                <BeautifulDate date={data.end_date} />
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  )
}

const BeautifulDate = (props: { date: string }) => {
  const beautifulDate = useGetBeautifulDateWithMonth(props.date)
  return beautifulDate
}

export default License
