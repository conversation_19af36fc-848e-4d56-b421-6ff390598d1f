.title {
  margin-bottom: 24px;
}

.card {

  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);

  border-radius: 12px;
  max-width: max-content;
  min-width: 315px;
  padding: 20px 24px 24px;

  &-title {

    color: var(--color-gray-90, #343b54);
    font: var(--font-title-4-medium);

    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &-row {
    display: flex;
    justify-content: space-between;

    margin-bottom: 8px;

    .row-name {
      color: var(--color-gray-80, #5c6585);
      font: var(--font-text-1-normal);
    }
    .row-value {
      color: var(--color-gray-90, #343b54);
      font: var(--font-text-1-medium);

      &.green {
        color: var(--text-good-text, #29a873);
        color: var(--color-primary, #3dbc87);
      }
    }
  }
}
