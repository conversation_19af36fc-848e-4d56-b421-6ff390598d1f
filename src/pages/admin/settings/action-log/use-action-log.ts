import { useLayoutEffect, useRef, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { QueryStatus } from '@reduxjs/toolkit/query'

import { actionLogApi } from '@/entities/action-log'
import { IActionLogRequest, IActionLogRequestTypes } from '@/shared/types/store/settings'
import { PAGE_ITEMS_LIMIT } from '@/shared/constants'

import { useGetTabs } from './data'
import { useNotification } from '@/shared/contexts/notifications'
import { useCopyToClipboard } from 'usehooks-ts'
import { useTranslation } from 'react-i18next'

const ACTION_LOG_NAMESPACE = 'pages__action-log'

export const useActionLog = () => {
  const { t: tLog } = useTranslation(ACTION_LOG_NAMESPACE)
  const [currentQueryParams, setSearchParams] = useSearchParams()
  const type =
    (currentQueryParams.get('type') as IActionLogRequestTypes) ||
    ('learning' as IActionLogRequestTypes)
  const page = currentQueryParams.get('page') || 1
  const { add } = useNotification()

  const TABS = useGetTabs()
  const [_, onCopy] = useCopyToClipboard()

  const ACTUAL_TAB = TABS.find(tab => tab.name === type)

  const [modalOpen, setModalOpen] = useState(false)
  const [wasError, setWasError] = useState(false)

  const ACTUAL_PAGE = +page - 1

  const tableHeaderRef = useRef<HTMLTableSectionElement>(null)

  const { data, status, isFetching, isSuccess, isError, currentData, isLoading } =
    actionLogApi.useGetActiosByTypeQuery({
      type: type as IActionLogRequest['type'],
      limit: PAGE_ITEMS_LIMIT,
      offset: ACTUAL_PAGE * PAGE_ITEMS_LIMIT,
    })

  useLayoutEffect(() => {
    if (isError) setWasError(true)
  }, [isError])

  useLayoutEffect(() => {
    if (isSuccess && !!currentData) setWasError(false)
  }, [isSuccess, currentData])

  const onTabChange = (newType: string) => {
    if (!newType) return

    const queryParams = new URLSearchParams()
    queryParams.set('type', newType)
    queryParams.set('page', '1')
    setSearchParams(queryParams)
  }

  const handleClickPagination = (newPage: string) => {
    if (!newPage) return

    const queryParams = new URLSearchParams()
    queryParams.set('page', newPage)
    queryParams.set('type', type)
    setSearchParams(queryParams)
  }

  const onSetPage = (page: number) => {
    handleClickPagination(String(page + 1))

    if (tableHeaderRef.current) tableHeaderRef.current.scrollIntoView()
  }

  const isEmptyData = status === QueryStatus.fulfilled && data?.data.length === 0

  const COUNT_PAGE = Math.ceil((data?.total_count || 0) / PAGE_ITEMS_LIMIT)
  const isPaginationVisible = data?.data && COUNT_PAGE > 1

  const isExportDisabled = isEmptyData || !data?.total_count || status !== QueryStatus.fulfilled

  return {
    isExportDisabled,
    isEmptyData,
    isPaginationVisible,
    onTabChange,
    onSetPage,
    data,
    status,
    ACTUAL_TAB,
    type,
    ACTUAL_PAGE,
    tableHeaderRef,
    modalOpen,
    setModalOpen,
    isFetching,
    wasError,
    isLoading,
    onCopy: (text: string) => {
      onCopy(text)
      add({ message: tLog('success_copy'), status: 'success', id: crypto.randomUUID() })
    },
  }
}
