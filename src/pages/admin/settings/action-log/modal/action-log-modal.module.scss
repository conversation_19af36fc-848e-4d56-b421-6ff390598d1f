.modal {
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 360px;
  max-width: 360px;
  text-align: center;

  &__title {
    color: var(--gray-gray-90);
    font: var(--font-title-4-medium);
    text-align: left;
  }

  &__content {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px;
  }

  &__success {
    color: var(--color-primary-90);
    font: var(--font-text-2-normal);
  }

  &__error {
    color: var(--color-error);
    font: var(--font-text-2-normal);
  }

  &__loading {
    color: var(--color-gray-90);
    font: var(--font-text-2-normal);
  }

  &__actions {
    align-items: center;
    display: flex;
    gap: 16px;
    justify-content: space-between;

    * {
      width: 100%;
    }
  }

  &__intervals {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
  }

  &__interval {
    flex: 1;

    &-label {
      color: var(--color-gray-70);
      text-align: start;
    }
  }

  &__inner {
    border-top: 1px solid #ebeff2;
    padding-top: 16px;
  }

  &__separator {
    margin-top: 24px;
    color: var(--color-gray-70);
    font: var(--font-text-2-demibold);
  }

  &__buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    gap: 16px;

    button {
      flex: 1;
    }
  }

  &__datepicker {
    z-index: 1310;
    bottom: -200px;
  }
}
