import { TabsProps } from '@/shared/components'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'

export const useGetTabs = () => {
  const { t } = useTranslation()

  const TABS: TabsProps.TabItem[] = useMemo(
    () => [
      { name: 'learning', value: t('commons:learning') },
      { name: 'phishing', value: t('commons:phishing') },
      { name: 'auth', value: t('commons:authorization') },
      { name: 'admin', value: t('commons:administration') },
    ],
    [t],
  )

  return TABS
}
