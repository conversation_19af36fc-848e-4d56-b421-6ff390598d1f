.form {
  display: flex;
  flex-direction: column;

  gap: 24px;
  width: 450px;

  input {
    width: 100%;
  }

  &__actions {
    align-items: center;
    display: flex;

    gap: 16px;
    justify-content: flex-start;

    button {
      flex-grow: 1;
    }
  }
}

.switch {
  align-self: flex-start;
  display: flex;
  flex-direction: row-reverse;
}

.loader {
  height: 100%;
  &__wrapper {
    align-items: center;
    display: flex;
    justify-content: center;
    min-height: 400px;
    width: 100%;
  }
}
