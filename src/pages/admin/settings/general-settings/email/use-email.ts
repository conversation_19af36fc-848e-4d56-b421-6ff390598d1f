import { useLayoutEffect, useState } from 'react'
import { settingsApi } from '@/entities/settings'
import { SubmitHandler, useForm } from 'react-hook-form'
import { ISMTPResponse } from '@/shared/types/store/settings'

export const useEmail = () => {
  const { data, isError, isFetching } = settingsApi.useGetSMTPSettingsQuery()
  const [patchTrigger, { error: isPatchError, isLoading: isPatchLoading }] =
    settingsApi.useSMTPPatchMutation()
  const [isAnon, setIsAnon] = useState(!data?.user)
  const form = useForm<ISMTPResponse>({
    values: {
      ...(data as ISMTPResponse),
      password: '',
    },
  })

  useLayoutEffect(() => {
    if (!data?.user) {
      setIsAnon(true)
      return
    }
    setIsAnon(false)
  }, [data?.user])

  const onSubmit: SubmitHandler<ISMTPResponse> = async data => {
    let customData = { ...data }
    if (isAnon) {
      customData = {
        ...customData,
        password: null,
        user: null,
      }
    }

    await patchTrigger(customData).unwrap()
  }

  const isSubmitDisabled = !isAnon && !form.watch('user')
  const isFieldsDisabled = isPatchLoading || isFetching

  return {
    form,
    onSubmit,
    isAnon,
    setIsAnon,
    isSubmitDisabled,
    isFieldsDisabled,
    isError: isError || isPatchError,
  }
}
