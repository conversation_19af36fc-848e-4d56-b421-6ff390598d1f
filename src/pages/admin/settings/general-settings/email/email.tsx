import { Controller } from 'react-hook-form'

import classNamesBind from 'classnames/bind'
import styles from './email.module.scss'

import { Button, Input, Switch } from '@/shared/ui'
import { useEmail } from './use-email'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const Email = () => {
  const { form, onSubmit, isAnon, isSubmitDisabled, setIsAnon, isError, isFieldsDisabled } =
    useEmail()

  const { t } = useTranslation()

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className={cx('form')}>
      <Input
        disabled={isFieldsDisabled}
        placeholder='********:25'
        // eslint-disable-next-line i18next/no-literal-string
        label='Host'
        register={form.register('host')}
      />
      <Input
        disabled={isFieldsDisabled}
        // eslint-disable-next-line i18next/no-literal-string
        placeholder='<EMAIL>'
        // eslint-disable-next-line i18next/no-literal-string
        label='Email from'
        register={form.register('email_from')}
      />
      <Controller
        control={form.control}
        name='use_tls'
        render={({ field }) => (
          <Switch
            disabled={isFieldsDisabled}
            className={cx('switch')}
            customValue={field.value}
            text={t('commons:use_tls')}
            onChange={() => {
              field.onChange(!field.value)
            }}
          />
        )}
      />
      <Switch
        disabled={isFieldsDisabled}
        className={cx('switch')}
        customValue={isAnon}
        text={t('commons:anonym_auth')}
        onChange={() => setIsAnon(v => !v)}
      />
      {!isAnon && (
        <>
          <Input
            disabled={isFieldsDisabled}
            // eslint-disable-next-line i18next/no-literal-string
            placeholder='<EMAIL>'
            label={t('commons:login')}
            register={form.register('user')}
          />
          <Input
            disabled={isFieldsDisabled}
            type='password'
            placeholder='********'
            label={t('commons:password')}
            register={form.register('password')}
          />
        </>
      )}
      {isError && <p className='error-text'>{`${t('commons:error_try_again')}...`}</p>}
      <div className={cx('form__actions')}>
        <Button
          disabled={!form.formState.isDirty || isFieldsDisabled}
          onClick={() => {
            form.reset()
            setIsAnon(!form.getValues('user'))
          }}
          type='button'
          color='gray'
        >
          {t('commons:cancel_changes')}
        </Button>
        <Button disabled={isSubmitDisabled || isFieldsDisabled} type='submit'>
          {isFieldsDisabled ? t('commons:loading_with_dots') : t('commons:save_changes')}
        </Button>
      </div>
    </form>
  )
}
