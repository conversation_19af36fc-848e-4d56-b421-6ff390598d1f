import { SubmitHand<PERSON>, useForm } from 'react-hook-form'
import { settingsApi } from '@/entities/settings'
import { IADFSSettingsFields } from '@/shared/types/store/settings'

import { mocValue, mocValueWithAuth } from './config'

export const useADFS = () => {
  const { data, isError, isFetching } = settingsApi.useGetADFSSettingsQuery()
  const [updateSettings, { error: isUpdateError, isLoading: isUpdateLoading }] =
    settingsApi.useUpdateSettingsMutation()

  const form = useForm<IADFSSettingsFields>({
    mode: 'onBlur',
    values: {
      sp_entity_id: mocValue,
      sp_assertion_consumer_service_url: mocValueWithAuth,
      sp_single_logout_service_url: mocValueWithAuth,
      idp_entity_id: data?.idp_entity_id || '',
      idp_sso_service_url: data?.idp_sso_service_url || '',
      idp_single_logout_service_url: data?.idp_single_logout_service_url || '',
      x509_cert: data?.x509_cert || '',
    },
  })

  const onSubmit: SubmitHandler<IADFSSettingsFields> = async data => {
    await updateSettings({
      x509_cert: data.x509_cert,
      idp_entity_id: data.idp_entity_id,
      idp_sso_service_url: data.idp_sso_service_url,
      idp_single_logout_service_url: data.idp_single_logout_service_url,
    }).unwrap()
  }

  const isSubmitDisabled = !form.formState.isValid
  const isFieldsDisabled = isUpdateLoading || isFetching

  return {
    form,
    onSubmit,
    isSubmitDisabled,
    isFieldsDisabled,
    isError: isError || isUpdateError,
  }
}
