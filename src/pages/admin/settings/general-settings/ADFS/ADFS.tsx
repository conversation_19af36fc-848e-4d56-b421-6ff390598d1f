import classNamesBind from 'classnames/bind'
import styles from './ADFS.module.scss'

import { Button, Input } from '@/shared/ui'
import { useADFS } from './use-adfs'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const ADFS = () => {
  const { form, onSubmit, isError, isFieldsDisabled, isSubmitDisabled } = useADFS()
  const { register, handleSubmit, formState } = form
  const { isDirty, errors } = formState
  const { t } = useTranslation('pages__settings__adfs')

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={cx('form')}>
      <Input
        required
        error={errors.sp_entity_id?.message}
        placeholder='sp entity id'
        label='sp entity id'
        register={register('sp_entity_id', {
          required: t('commons:required_field'),
        })}
        help={t('sp_entity_id')}
        disabled
      />
      <Input
        required
        error={errors.sp_assertion_consumer_service_url?.message}
        placeholder='sp assertion consumer service url'
        label='sp assertion consumer service url'
        register={register('sp_assertion_consumer_service_url', {
          required: t('commons:required_field'),
        })}
        help={t('sp_assertion_consumer_service_url')}
        disabled
      />
      <Input
        required
        error={errors.sp_single_logout_service_url?.message}
        placeholder='sp single logout service url'
        label='sp single logout service url'
        register={register('sp_single_logout_service_url', {
          required: t('commons:required_field'),
        })}
        help={t('sp_single_logout_service_url')}
        disabled
      />
      <Input
        required
        error={errors.x509_cert?.message}
        disabled={isFieldsDisabled}
        placeholder='x509 cert'
        label='x509 cert'
        register={register('x509_cert', {
          required: t('commons:required_field'),
        })}
        help={t('x509_cert')}
      />
      <Input
        required
        error={errors.idp_entity_id?.message}
        disabled={isFieldsDisabled}
        placeholder='idp entity id'
        label='idp entity id'
        register={register('idp_entity_id', {
          required: t('commons:required_field'),
        })}
        help={t('idp_entity_id')}
      />
      <Input
        required
        error={errors.idp_sso_service_url?.message}
        disabled={isFieldsDisabled}
        placeholder='idp sso service url'
        label='idp sso service url'
        register={register('idp_sso_service_url', {
          required: t('commons:required_field'),
        })}
        help={t('idp_sso_service_url')}
      />
      <Input
        required
        error={errors.idp_single_logout_service_url?.message}
        disabled={isFieldsDisabled}
        placeholder='idp single logout service url'
        label='idp single logout service url'
        register={register('idp_single_logout_service_url', {
          required: t('commons:required_field'),
        })}
        help={t('idp_single_logout_service_url')}
      />
      {isError && <p className='error-text'>{`${t('commons:error_try_again')}...`}</p>}
      <div className={cx('form__actions')}>
        <Button
          disabled={!isDirty || isFieldsDisabled}
          onClick={() => {
            form.reset()
          }}
          type='button'
          color='gray'
        >
          {t('commons:cancel_changes')}
        </Button>
        <Button
          disabled={isSubmitDisabled || isFieldsDisabled}
          loading={isFieldsDisabled}
          type='submit'
        >
          {t('commons:save_changes')}
        </Button>
      </div>
    </form>
  )
}
