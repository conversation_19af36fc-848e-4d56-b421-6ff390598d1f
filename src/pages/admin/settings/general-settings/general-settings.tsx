import { FC, useCallback, useEffect, useMemo, useState } from 'react'

import styles from './general-settings.module.scss'
import classNamesBind from 'classnames/bind'

import { GeneralSettingsProps } from './general-settings.d'
import SettingsSmallIcon from '@/shared/ui/Icon/icons/components/SettingsSmallIcon'
import OrganizationSmallIcon from '@/shared/ui/Icon/icons/components/OrganizationSmallIcon'
import SobakaIcon from '@/shared/ui/Icon/icons/components/SobakaIcon'
import KeyIcon from '@/shared/ui/Icon/icons/components/KeyIcon'
import Lock2Icon from '@/shared/ui/Icon/icons/components/Lock2Icon'
import PeopleBoldIcon from '@/shared/ui/Icon/icons/components/PeopleBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { DefaultSettings } from './default-settings/default-settings'
import { ActiveDirectory } from './active-directory/active-directory'
import { Email } from './email/email'
import { ADFS } from './ADFS/ADFS'
import { TwoFA } from './twofa'

import { PageTitle, Loader, TabItem, TabsNew } from '@/shared/ui'
import { useSearchParams } from 'react-router-dom'
import { useConfig } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'
import { organizationAPI } from 'entities/organization'
import { Superusers } from './superusers/superusers'
import { useUserOrganizationId, useUserPermissions } from '@/entities/employee'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'pages__settings__settings'

export const GeneralSettings: FC<GeneralSettingsProps.Props> = props => {
  const { className } = props
  const [currentQueryParams, setSearchParams] = useSearchParams()
  const { t } = useTranslation(TRANSLATION_FILE)
  const type = currentQueryParams?.get('type')
  const userOrganizationId = useUserOrganizationId()

  const { data: organization, isLoading: isLoadingOrganization } =
    organizationAPI.useGetOrganizationQuery(userOrganizationId ?? '', {
      skip: !userOrganizationId,
    })

  const isPageLoading = isLoadingOrganization

  const config = useConfig()

  const permissions = useUserPermissions()

  const settingsPermissions = useMemo(() => {
    const settingsPermissions = []
    const isWithAd = Boolean(config?.use_admin_ad || config?.useSSO)

    if (isWithAd) settingsPermissions.push('ad')

    settingsPermissions.push(
      ...(permissions?.admin?.filter(i => i.name === 'settings')[0].pages?.map(i => i.name) ?? []),
    )

    return settingsPermissions
  }, [permissions, config])

  const TABS_CONFIG = useMemo(
    () => [
      {
        label: (
          <>
            <IconWrapper>
              <SettingsSmallIcon />
            </IconWrapper>
            <p>{t('commons:basic')}</p>
          </>
        ),
        value: 'general',
        children: <DefaultSettings />,
        permissions: ['settings'],
      },
      {
        label: (
          <>
            <IconWrapper>
              <OrganizationSmallIcon />
            </IconWrapper>
            <p>Active Directory</p>
          </>
        ),
        value: 'active_directory',
        children: <ActiveDirectory />,
        // permissions: ['settings', 'ad'],
        permissions: [],
      },
      {
        label: (
          <>
            <IconWrapper>
              <SobakaIcon />
            </IconWrapper>
            <p>Email</p>
          </>
        ),
        value: 'email',
        children: <Email />,
        permissions: ['settings'],
      },
      {
        label: (
          <>
            <IconWrapper>
              <KeyIcon />
            </IconWrapper>
            <p>SAML</p>
          </>
        ),
        value: 'adfs',
        children: <ADFS />,
        permissions: ['adfs'],
      },
      {
        label: (
          <>
            <IconWrapper>
              <Lock2Icon />
            </IconWrapper>
            <p>{t('commons:auth')}</p>
          </>
        ),
        value: t('commons:auth'),
        children: <TwoFA />,
        permissions: [],
      },
      {
        label: (
          <>
            <IconWrapper>
              <PeopleBoldIcon />
            </IconWrapper>
            <p>{t('commons:superusers')}</p>
          </>
        ),
        value: t('commons:superusers'),
        children: <Superusers org={organization} />,
        permissions: organization?.children.length ? [] : ['disable'],
      },
    ],
    [t, organization],
  )

  const [active, setActive] = useState<Nullable<(typeof TABS_CONFIG)[0]>>(
    TABS_CONFIG.find(tab => tab.value === type) ?? null,
  )

  const onTabClick = useCallback(
    (v: string) => {
      const activeTab = TABS_CONFIG.find(tab => tab.value === v) ?? TABS_CONFIG[0]

      setActive(() => activeTab)

      if (!activeTab.value) return
      const queryParams = new URLSearchParams()

      queryParams.set('type', activeTab.value)
      setSearchParams(queryParams)
    },
    [setSearchParams],
  )

  useEffect(() => {
    if (type) return

    const filtredTabs = TABS_CONFIG.filter(tab => settingsPermissions?.includes(tab.value))

    filtredTabs[0] && onTabClick(filtredTabs[0].value)
  }, [onTabClick, settingsPermissions, type])

  return (
    <div className={cx('wrapper', className)}>
      <PageTitle>{t('generalSettings')}</PageTitle>
      {isPageLoading && <Loader size='56' />}
      {!isPageLoading && active && (
        <TabsNew
          activeTab={active.value}
          tabClassname={cx('tabs__item')}
          activeTabClassname={cx('tabs__item_active')}
          tabsClassname={cx('tabs')}
          className={cx('tabs__container')}
          onChange={(v: string) => onTabClick(v)}
        >
          {TABS_CONFIG.map(tab => {
            if (
              tab.permissions &&
              tab?.permissions?.length > 0 &&
              !settingsPermissions?.find(permission => tab.permissions.includes(permission))
            )
              return

            return (
              <TabItem key={String(tab.value)} label={tab.label} value={tab.value}>
                {tab.children}
              </TabItem>
            )
          })}
        </TabsNew>
      )}
    </div>
  )
}

export default GeneralSettings
