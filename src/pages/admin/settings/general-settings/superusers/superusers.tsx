import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './superusers.module.scss'
import { IOrganizationWithChildren } from 'entities/organization'
import { ButtonIcon } from '@/shared/ui'
import { Link } from 'react-router-dom'

const cx = classNamesBind.bind(styles)

type Props = {
  org?: IOrganizationWithChildren
}

export const Superusers = ({ org }: Props) => {
  const { t } = useTranslation()

  if (!org) return

  return (
    <>
      <div className={cx('title')}>{t('commons:child_orgs')}</div>
      <div className={cx('list')}>
        {org.children.length === 0 ? (
          <div className={cx('notChildren')}>{t('commons:no_child_orgs')}</div>
        ) : (
          org.children.map(childOrg => (
            <Link
              key={childOrg.id}
              to={`/lk/admin/settings/adfs/${childOrg.id}/superusers`}
              className={cx('listItem')}
            >
              <div>
                <div className={cx('title')}>{childOrg.title}</div>
              </div>
              <div className={cx('icons')}>
                <ButtonIcon icon='chevroneBold' color='gray70' size='28' />
              </div>
            </Link>
          ))
        )}
      </div>
    </>
  )
}
