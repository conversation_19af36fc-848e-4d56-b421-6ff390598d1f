import { Controller } from 'react-hook-form'

import classNamesBind from 'classnames/bind'
import styles from './active-directory.module.scss'

import { Button, Checkbox, Input, Switch } from '@/shared/ui'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { FileUpload } from '@/shared/components'
import { useActiveDirectory } from './use-active-directory'
import { ConfirmModal } from '@/shared/modals/confirm-modal'
import { truncateString } from '@/shared/helpers'
import { getFileNameFromUrl } from '@/shared/helpers/files'

const cx = classNamesBind.bind(styles)

export const ActiveDirectory = () => {
  const {
    isError,
    isDisabled,
    form,
    isSSO,
    onSubmit,
    onReset,
    isAutoSyncEnabled,
    setIsAutoSyncEnabled,
    autoSyncTimer,
    setAutoSyncTimer,
    optionDelete,
    optionUpdate,
    setOptionDelete,
    setOptionUpdate,
    isLoading,
    data,
    setOpen,
    open,
    t,
  } = useActiveDirectory()

  return (
    <form className={cx('form')}>
      <ConfirmModal
        open={open}
        setOpen={setOpen}
        onConfirm={() => {
          onSubmit(form.getValues())
        }}
        title={t('areYouSureToDelete')}
      />
      <Input
        disabled={isLoading}
        placeholder={t('serverUrlPlaceholder')}
        label={t('serverUrl')}
        register={form.register('url')}
      />
      <Controller
        name='use_tls'
        control={form.control}
        render={({ field }) => (
          <Switch
            disabled={isLoading}
            className={cx('switch')}
            customValue={field.value}
            text={t('useTLS')}
            onChange={() => {
              field.onChange(!field.value)
            }}
          />
        )}
      />
      <Input
        disabled={isLoading}
        placeholder={t('bindDNPlaceholder')}
        label={t('bindDN')}
        register={form.register('bind_dn')}
      />
      <Input
        disabled={isLoading}
        type='password'
        placeholder={t('passwordPlaceholder')}
        label={t('password')}
        register={form.register('password', { required: !data?.password })}
      />
      <Input
        disabled={isLoading}
        placeholder={t('searchDNPlaceholder')}
        label={t('searchDN')}
        register={form.register('search_dn')}
      />
      <Input
        disabled={isLoading}
        placeholder={t('filterStrPlaceholder')}
        label={t('filterStr')}
        register={form.register('filter_str')}
      />

      <Controller
        name='use_sso'
        control={form.control}
        render={({ field }) => (
          <Switch
            disabled={isLoading}
            className={cx('switch')}
            customValue={field.value}
            text={t('ssoTitle')}
            onChange={() => {
              const switchValue = !field.value
              field.onChange(switchValue)
              if (!switchValue) {
                form.setValue('krb_file', undefined)
              }
            }}
          />
        )}
      />

      {isSSO && (
        <div>
          <h4 className={cx('form__keytab')}>{t('keyTabFileTitle')}</h4>
          <Controller
            name={'krb_file'}
            control={form.control}
            render={({ field }) => (
              <div className={cx('upload__container')}>
                <FileUpload disabled={isLoading} accept='.keytab' onFileSelect={field.onChange}>
                  <Button
                    disabled={isLoading}
                    size='small'
                    color='darkGray'
                    type='button'
                    leftIcon={'clipBold'}
                  >
                    {field?.value ? t('replaceFile') : t('chooseFile')}
                  </Button>
                </FileUpload>
                {field.value && (
                  <div className={cx('upload__content')}>
                    <IconWrapper onClick={() => form.resetField('krb_file')}>
                      <CloseBoldIcon />
                    </IconWrapper>
                    <p>
                      {field?.value instanceof File
                        ? truncateString(field.value?.name, 32)
                        : truncateString(getFileNameFromUrl(field?.value) ?? '', 32)}
                    </p>
                  </div>
                )}
              </div>
            )}
          />
        </div>
      )}

      <>
        <Switch
          disabled={isLoading}
          className={cx('switch')}
          customValue={isAutoSyncEnabled}
          text={t('autoSyncADTitle')}
          onChange={() => {
            setIsAutoSyncEnabled(v => !v)
          }}
        />
        {isAutoSyncEnabled && (
          <>
            <div className={cx('rules__wrapper')}>
              <div className={cx('sync-hours')}>
                {t('syncEachHour')}
                <Input
                  value={autoSyncTimer}
                  onChange={v => {
                    if (v !== undefined) setAutoSyncTimer(v ? String(parseInt(v)) : v)
                  }}
                  min={1}
                  step={1}
                  type='number'
                  domain={t('syncHoursLabel')}
                  className={cx('input__sync-hours')}
                />
              </div>
            </div>
            <Checkbox
              onChange={setOptionDelete}
              className={cx('label__wrapper')}
              label={<span>{t('deleteEmployeesAndDepartments')}</span>}
              customChecked={optionDelete}
            />
            <Checkbox
              className={cx('label__wrapper')}
              onChange={setOptionUpdate}
              label={<span>{t('updateExistingEmployees')}</span>}
              customChecked={optionUpdate}
            />
          </>
        )}
      </>

      {isError && <p className='error-text'>{t('retryAfterError')}</p>}
      <div className={cx('form__actions')}>
        <Button
          disabled={!form.formState.isDirty || isDisabled}
          onClick={onReset}
          type='button'
          color='gray'
        >
          {t('cancelChanges')}
        </Button>
        <Button
          disabled={isDisabled}
          type='button'
          onClick={() => {
            if (isAutoSyncEnabled && optionDelete) {
              setOpen(true)
              return
            }
            onSubmit(form.getValues())
          }}
        >
          {t('applySettings')}
        </Button>
      </div>
    </form>
  )
}
