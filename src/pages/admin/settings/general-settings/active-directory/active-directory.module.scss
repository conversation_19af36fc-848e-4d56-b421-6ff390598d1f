.form {
  display: flex;
  flex-direction: column;

  gap: 24px;
  width: 450px;

  input {
    width: 100%;
  }

  &__actions {
    align-items: center;
    display: flex;

    gap: 16px;
    justify-content: flex-start;

    button {
      flex-grow: 1;
    }
  }

  &__keytab {
    color: var(--color-gray-70);
    font: var(--font-text-2-medium);
    margin-bottom: 8px;
  }
}

.label__wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch {
  align-self: flex-start;
  display: flex;
  flex-direction: row-reverse;
}

.upload {
  &__container {
    align-items: center;
    display: flex;

    gap: 16px;
    justify-content: flex-start;
  }

  &__content {
    align-items: center;
    display: flex;
    gap: 4px;
    justify-content: flex-start;

    svg,
    path {
      cursor: pointer;
    }
    p {
      color: var(--color-gray-90);
      font: var(--font-caption-1-normal);
      max-width: 100%;
    }
  }
}

.loader {
  height: 100%;
  &__wrapper {
    align-items: center;
    display: flex;
    justify-content: center;
    min-height: 400px;
    width: 100%;
  }
}

.rules__wrapper {
  .sync-hours,
  .riskLevel,
  .riskLevel > div {
    justify-content: flex-start;
    align-items: center;

    color: var(--color-gray-90, #343b54);
    display: flex;
    font: var(--font-text-2-normal);
    gap: 8px;
  }

  .riskLevel {
    > div {
      justify-content: center;
    }
  }
}

.input__sync-hours {
  border-radius: 8px;
  width: 120px !important;
  padding: 8px 65px 8px 8px !important;
}
