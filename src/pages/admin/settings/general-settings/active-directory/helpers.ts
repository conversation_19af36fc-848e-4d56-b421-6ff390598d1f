import { IActiveDirectoryResponse } from '@/shared/types/store/settings'

export const prepareLDAPData = (
  data: IActiveDirectoryResponse & {
    krb_file?: File
  },
): FormData | null => {
  const formData = new FormData()

  formData.append('bind_dn', data.bind_dn)
  formData.append('filter_str', data.filter_str)
  formData.append('id', data.id)

  if (data.use_sso && data.krb_file && data?.krb_file instanceof File) {
    formData.append('krb_file', new Blob([data.krb_file], { type: data.krb_file.type }))
    formData.append('krb_file_exist', String(true))
  } else {
    formData.append('krb_file_exist', String(false))
  }

  if (data?.password) formData.append('password', data.password)
  formData.append('search_dn', data.search_dn)
  formData.append('url', data.url)
  formData.append('use_tls', String(Boolean(data.use_tls)))

  return formData
}
