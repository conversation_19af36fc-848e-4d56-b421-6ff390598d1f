import { useEffect, useState } from 'react'
import { settingsApi } from '@/entities/settings'
import { SubmitHandler, useForm } from 'react-hook-form'
import { prepareLDAPData } from './helpers'
import { ADAutoSyncSettings, IActiveDirectoryResponse } from '@/shared/types/store/settings'
import { useTranslation } from 'react-i18next'

const TRANSLATION_FILE = 'pages__settings__active_directory'

export const useActiveDirectory = () => {
  const { data, isError, isFetching } = settingsApi.useGetLDAPSettingsQuery()
  const [patchTrigger, { data: patchData, isLoading: isPatchLoading }] =
    settingsApi.useLDAPPatchMutation()

  const [open, setOpen] = useState(false)
  const [isAutoSyncEnabled, setIsAutoSyncEnabled] = useState(false)
  const [autoSyncTimer, setAutoSyncTimer] = useState<string>('24')

  const { data: autoSyncSettingsData } = settingsApi.useGetAutoSyncSettingsQuery()
  const [optionDelete, setOptionDelete] = useState(
    () => autoSyncSettingsData?.auto_sync_option_delete,
  )
  const [optionUpdate, setOptionUpdate] = useState(
    () => autoSyncSettingsData?.auto_sync_option_update,
  )
  const [patchAutoSync] = settingsApi.usePatchAutoSyncSettingsMutation()
  const form = useForm<
    IActiveDirectoryResponse & {
      krb_file?: File
    }
  >({
    values: { ...(patchData || data)!, password: '' },
  })

  const { t } = useTranslation(TRANSLATION_FILE)

  const onSubmit: SubmitHandler<IActiveDirectoryResponse> = async submitData => {
    const preparedData = prepareLDAPData(submitData)
    preparedData?.append('use_sso', String(Boolean(submitData.use_sso)))
    if (!preparedData) return

    await patchTrigger(preparedData).unwrap()

    const newAdSyncData: ADAutoSyncSettings = {
      auto_sync_interval: Number(autoSyncTimer) ?? autoSyncSettingsData?.auto_sync_interval ?? 0,
      auto_sync_enabled: isAutoSyncEnabled ?? autoSyncSettingsData?.auto_sync_enabled ?? false,
      auto_sync_option_delete:
        optionDelete ?? autoSyncSettingsData?.auto_sync_option_delete ?? false,
      auto_sync_option_update:
        optionUpdate ?? autoSyncSettingsData?.auto_sync_option_update ?? false,
    }

    await patchAutoSync(newAdSyncData).unwrap()
  }

  const onReset = () => form.reset()

  useEffect(() => {
    if (!autoSyncSettingsData) return
    setOptionDelete(autoSyncSettingsData?.auto_sync_option_delete)
    setOptionUpdate(autoSyncSettingsData?.auto_sync_option_update)
  }, [autoSyncSettingsData])

  useEffect(() => {
    if (!autoSyncSettingsData) return

    setIsAutoSyncEnabled(() => autoSyncSettingsData.auto_sync_enabled)
    setAutoSyncTimer(() => String(autoSyncSettingsData.auto_sync_interval))
  }, [autoSyncSettingsData])

  const isLoading = isPatchLoading || isFetching

  const getIsDisabled = () => {
    if (isLoading) return true

    if (!form.formState.isValid) return true

    if (form.watch('use_sso')) {
      if (!form.watch('krb_file') && !form.watch('krb_file_exist')) return true
    }

    if (!autoSyncTimer) {
      if (Number(autoSyncTimer) < 1) return true
    }

    return false
  }

  const submitBtnText = isLoading ? t('commons:loading') : t('commons:save')

  return {
    isSSO: form.watch('use_sso'),
    form,
    onSubmit,
    isDisabled: getIsDisabled(),
    isError,
    onReset,
    isAutoSyncEnabled,
    setIsAutoSyncEnabled,
    autoSyncTimer,
    setAutoSyncTimer,
    optionDelete,
    setOptionDelete,
    optionUpdate,
    setOptionUpdate,
    isFetching,
    isPatchLoading,
    submitBtnText,
    isLoading,
    data,
    open,
    t,
    setOpen,
  }
}
