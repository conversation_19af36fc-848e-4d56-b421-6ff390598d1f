import { Suspense, useCallback, useEffect, useState } from 'react'
import { HelpIcon, Switch } from '@/shared/ui'
import { organizationAPI } from 'entities/organization'

import { useNotification } from '@/shared/contexts/notifications'
import { debounce } from '@/shared/helpers/delay'
import { Modal, TwoFaAuth, LazyQrCode } from '@/shared/components'

import styles from './twofa.module.scss'
import classNamesBind from 'classnames/bind'

import { userAPI, useUserOrganizationId } from 'entities/employee'

import { TwoFATestingRequest } from '@/shared/types/store/twofa'
import { useTranslation } from 'react-i18next'
import { PasswordPolicy } from '../password-policy'

const cx = classNamesBind.bind(styles)

const E2EAuthMsg = () => {
  const { t } = useTranslation()

  return (
    <span className={cx('underline')}>
      {t('commons:functional_does_not_work_with_end_to_end_auth')}
    </span>
  )
}

const HINT = () => {
  const { t } = useTranslation()

  return (
    <span>
      {t('commons:e2e_hint')} <br />
      {E2EAuthMsg()}
    </span>
  )
}

const DELAY = 1000
export const TwoFA = () => {
  const { t } = useTranslation()
  const { data: userInfo } = userAPI.useGetUserInfoQuery()
  const userOrganizationId = useUserOrganizationId()

  const { data, isLoading: isOrganizationInfoLoading } = organizationAPI.useGetOrganizationQuery(
    userOrganizationId ?? '',
    { skip: !userOrganizationId },
  )

  const [isTwoFAActive, setIsTwoFAActive] = useState(data?.need_twofa)

  useEffect(() => {
    if (data) setIsTwoFAActive(data.need_twofa)
  }, [data])

  const { handleErrorResponse } = useNotification()

  const [triggerTwoFa, { isLoading }] = organizationAPI.useOrganizationTwoFaMutation()

  const onUpdateTwoFa = useCallback(
    debounce(async (v: boolean, values: TwoFATestingRequest) => {
      await triggerTwoFa({ active: v, code: values?.code })
        .unwrap()
        .catch(() => {
          handleErrorResponse({
            status: 'error',
            message: t('commons:error_twofa_confirm'),
          })
        })

      setIsQrCodeModalOpen(false)
      setIsTwoFAActive(v)
    }, DELAY),
    [triggerTwoFa, debounce],
  )

  const [isQrCodeModalOpen, setIsQrCodeModalOpen] = useState(false)

  return (
    <div className={cx('twofa__wrapper')}>
      <div className={cx('twofa__switch__wrapper')}>
        <Switch
          onChange={() => {
            setIsQrCodeModalOpen(true)
          }}
          className={cx('twofa__switch')}
          customValue={isTwoFAActive}
          disabled={isLoading || isOrganizationInfoLoading}
          text={t('commons:enable_tf_authentication_for_all')}
        />
        <HelpIcon className={cx('twofa__switch__info')} text={E2EAuthMsg()} />
      </div>

      <Modal
        className={cx(styles.modal)}
        active={isQrCodeModalOpen}
        setActive={setIsQrCodeModalOpen}
        key={'qrcode-modal'}
      >
        <h3>{t('commons:two_factor_authentication')}</h3>
        <Suspense>
          <LazyQrCode />
        </Suspense>
        <div className={cx('hint')}>
          <p>{t('commons:find_more')}:</p>
          <HelpIcon text={HINT()} />
        </div>
        <TwoFaAuth
          isError={false}
          inputProps={{ disabled: false }}
          className={cx(styles.twofa__input)}
          animationDuration={8000}
          codeLength={6}
          onSuccess={v => {
            if (userInfo)
              onUpdateTwoFa(!isTwoFAActive, {
                code: v,
                email: userInfo?.email,
              })
          }}
        />
      </Modal>

      <PasswordPolicy />
    </div>
  )
}
