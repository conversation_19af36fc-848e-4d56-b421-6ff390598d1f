.modal {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}

.hint {
  display: flex;
  flex-direction: row;
  gap: 12px;
  color: var(--color-gray-70);
  font: var(--font-text-2-normal);
  margin-top: -12px;
  margin-bottom: -12px;
  margin-left: auto;
}

.underline {
  text-decoration: underline;
}

.bold {
  font-weight: 600;
}

.underline {
  text-decoration: underline;
}

.twofa {
  &__switch {
    width: fit-content;
    cursor: pointer;
    &__wrapper {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    &__info {
      margin-left: auto;
      stroke: var(--color-primary-80);
    }
  }
  &__wrapper {
    max-width: 540px;
    display: flex;
    flex-direction: column;
    gap: 32px;
  }
}
