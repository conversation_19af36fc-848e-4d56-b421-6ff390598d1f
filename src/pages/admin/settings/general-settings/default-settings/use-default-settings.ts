import { settingsApi } from '@/entities/settings'
import { SubmitHandler, useForm } from 'react-hook-form'
import { prepareArchive, prepareCrtKey } from './helpers'
import {
  IDefaultSettingsKeyAndCertificateRequest,
  IDefaultSettingsResponse,
  IDefaultSettingsUploadArchiveRequest,
} from '@/shared/types/store/settings'

type SSLVariants = 'certificate' | 'archive'

export type DefaultSettingsFormProps = IDefaultSettingsResponse &
  IDefaultSettingsUploadArchiveRequest &
  IDefaultSettingsKeyAndCertificateRequest & {
    ssl_type: SSLVariants
    pkcs12_file: File
    key_file: File
    cert_file: File
  }

export const useDefaultSettings = () => {
  const { data, isError: isDataError } = settingsApi.useGetOrganizationsSettingsQuery()
  const [patchTrigger, { isLoading, isError: isPatchError, data: patchData }] =
    settingsApi.useOrganizationPatchMutation()
  const [crtKeyTrigger, { isLoading: isCrtKeyLoading, isError: isCrtKeyError }] =
    settingsApi.useOrganizationUploadCrtFileMutation()
  const [archiveTrigger, { isLoading: isAcrhiveLoading, isError: isArchiveTriggerError }] =
    settingsApi.useOrganizationUploadArchiveMutation()
  const form = useForm<DefaultSettingsFormProps>({
    values: {
      ...((patchData || data) as DefaultSettingsFormProps),
      ssl_type: 'certificate',
    },
  })

  const isCertificate = form.watch('ssl_type') === 'certificate'
  const isArchive = form.watch('ssl_type') === 'archive'

  const onCancelChanges = () => {
    if (!data) return

    form.reset()

    form.setValue('title', data.title)
    form.setValue('url', data.url)
  }

  const isCancelChangesAvailable =
    form.watch('cert_file') ||
    form.watch('key_file') ||
    form.watch('pkcs12_file') ||
    form.watch('password')

  const onSubmit: SubmitHandler<DefaultSettingsFormProps> = async data => {
    if (data.ssl_type === 'archive') {
      const acrhiveData = prepareArchive(data)

      let archiveId = ''
      if (acrhiveData) {
        const acrhiveResponse = await archiveTrigger(acrhiveData).unwrap()
        archiveId = acrhiveResponse?.id
      }

      patchTrigger({
        certificate_id: archiveId,
        logo: data.logo,
        title: data.title,
        url: data.url,
      })
    }
    if (data.ssl_type === 'certificate') {
      const crtKeyData = prepareCrtKey(data)

      let crtKeyId = ''
      if (crtKeyData) {
        const crtKeyResponse = await crtKeyTrigger(crtKeyData).unwrap()
        crtKeyId = crtKeyResponse?.id
      }

      patchTrigger({
        certificate_id: crtKeyId,
        logo: data.logo,
        title: data.title,
        url: data.url,
      })
    }
  }

  const isSubmitBtnLoading = isLoading || isCrtKeyLoading || isAcrhiveLoading
  const isError = isDataError || isPatchError
  const isCertificateViewAvailable =
    !isError && !isPatchError && !isArchiveTriggerError && !isCrtKeyError

  return {
    isSubmitBtnLoading,
    form,
    onSubmit,
    isCertificate,
    isArchive,
    onCancelChanges,
    isCancelChangesAvailable,
    isCrtKeyError,
    isArchiveTriggerError,
    isError,
    isCertificateViewAvailable,
  }
}
