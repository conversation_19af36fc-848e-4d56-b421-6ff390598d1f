@use "../../../../../shared/assets/styles/mixins/icons";
@use "../../../../../shared/assets/styles/mixins/text";

.form {
  display: flex;
  flex-direction: column;

  gap: 24px;
  width: 450px;

  input {
    width: 100%;
  }

  &__actions {
    align-items: center;
    display: flex;

    gap: 16px;
    justify-content: flex-start;

    button {
      flex-grow: 1;
    }
  }

  &__choise {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__btn {
    align-self: start;
    flex-grow: 0;
    width: auto;

    svg,
    path {
      fill: var(--color-gray-70) !important;
      stroke: var(--color-gray-70) !important;
    }
  }
}

.tooltip {

  border-radius: 8px;
  left: 20px;

  padding: 8px;

  &__icon {
    svg,
    path {
      fill: var(--color-gray-70) !important;
      stroke: var(--color-gray-70) !important;
    }
  }
}

.upload {
  &__container {
    align-items: center;
    display: flex;

    gap: 16px;
    justify-content: flex-start;
  }

  &__content {
    align-items: center;
    display: flex;
    gap: 4px;
    justify-content: flex-start;

    svg,
    path {
      cursor: pointer;
    }
    p {

      color: var(--color-gray-90);
      font: var(--font-caption-1-normal);
      max-width: 100%;
    }
  }
}

.expired {
  color: var(--color-gray-70);
  font: var(--font-text-2-medium);
}

.loader {
  height: 100%;
  &__wrapper {
    align-items: center;
    display: flex;
    justify-content: center;
    min-height: 400px;
    width: 100%;
  }
}
