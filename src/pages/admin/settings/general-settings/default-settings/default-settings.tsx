import React, { useMemo } from 'react'
import { Controller } from 'react-hook-form'

import classNamesBind from 'classnames/bind'
import styles from './default-settings.module.scss'

import { Button, HelpIcon, Input, ITabSelector, TabSelector } from '@/shared/ui'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import CloseSmallIcon from '@/shared/ui/Icon/icons/components/CloseSmallIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { FileUpload } from '@/shared/components'
import { truncateString } from '@/shared/helpers'
import { getDateThroughDot } from '@/shared/helpers/date'

import { useDefaultSettings } from './use-default-settings'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const DefaultSettings = () => {
  const {
    form,
    isArchive,
    isCancelChangesAvailable,
    isCertificate,
    isSubmitBtnLoading,
    onCancelChanges,
    onSubmit,
    isArchiveTriggerError,
    isCrtKeyError,
    isError,
    isCertificateViewAvailable,
  } = useDefaultSettings()

  const { t } = useTranslation('pages__settings__settings')

  const TABS_SELECTOR: ITabSelector[] = useMemo(
    () => [
      { name: 'certificate', text: t('commons:cert_key') },
      { name: 'archive', text: t('commons:archive') },
    ],
    [t],
  )

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className={cx('form')}>
      <Input
        disabled={isSubmitBtnLoading}
        // eslint-disable-next-line i18next/no-literal-string
        placeholder='Example'
        label={t('organization_name')}
        register={form.register('title')}
      />
      <Input
        disabled={isSubmitBtnLoading}
        // eslint-disable-next-line i18next/no-literal-string
        placeholder='edu.example.local'
        label={t('domain_name')}
        register={form.register('url')}
      />
      <Controller
        name='ssl_type'
        control={form.control}
        render={({ field }) => (
          <div ref={field.ref}>
            <TabSelector
              items={TABS_SELECTOR}
              label={t('ssl_certificate')}
              active={field.value}
              onChange={v => {
                //! во время загрузки не менять выбор
                if (isSubmitBtnLoading) return

                if (v.name === 'archive') {
                  form.resetField('cert_file')
                  form.resetField('key_file')
                }
                if (v.name === 'certificate') {
                  form.resetField('pkcs12_file')
                  form.resetField('password')
                }

                field.onChange(v.name)
              }}
            />
          </div>
        )}
      />
      {isCertificate && (
        <div className={cx('form__choise')} key={'keys block'}>
          <Controller
            name='cert_file'
            control={form.control}
            render={({ field }) => (
              <div className={cx('upload__container')}>
                <FileUpload
                  disabled={isSubmitBtnLoading}
                  accept='.pem,.der,.crt'
                  onFileSelect={field.onChange}
                >
                  <Button
                    disabled={isSubmitBtnLoading}
                    type='button'
                    className={cx('form__btn')}
                    size='small'
                    color='darkGray'
                    leftIcon={'clipSmall'}
                  >
                    {field.value?.name ? t('replace_certificate') : t('select_certificate')}
                    <HelpIcon
                      className={cx('tooltip__icon')}
                      tooltipClassname={cx('tooltip')}
                      text={t('cert_extension')}
                    />
                  </Button>
                </FileUpload>
                {field.value && (
                  <div className={cx('upload__content')}>
                    <IconWrapper onClick={() => form.resetField('cert_file')}>
                      <CloseBoldIcon />
                    </IconWrapper>
                    <p>{`${truncateString(field.value.name, 18)}`}</p>
                  </div>
                )}
              </div>
            )}
          />
          <Controller
            name='key_file'
            control={form.control}
            render={({ field }) => (
              <div className={cx('upload__container')}>
                <FileUpload
                  disabled={isSubmitBtnLoading}
                  accept='.pem,.key,.der'
                  onFileSelect={field.onChange}
                >
                  <Button
                    disabled={isSubmitBtnLoading}
                    type='button'
                    className={cx('form__btn')}
                    size='small'
                    color='darkGray'
                    leftIcon={'key'}
                  >
                    {field.value?.name ? t('replace_key') : t('select_key')}
                    <HelpIcon
                      className={cx('tooltip__icon')}
                      tooltipClassname={cx('tooltip')}
                      text={t('replace_key')}
                    />
                  </Button>
                </FileUpload>
                {field.value && (
                  <div className={cx('upload__content')}>
                    <IconWrapper onClick={() => form.resetField('key_file')}>
                      <CloseSmallIcon />
                    </IconWrapper>
                    <p>{`${truncateString(field.value.name, 18)}`}</p>
                  </div>
                )}
              </div>
            )}
          />
        </div>
      )}
      {isArchive && (
        <React.Fragment key={'archive block'}>
          <Controller
            name='pkcs12_file'
            control={form.control}
            render={({ field }) => (
              <div className={cx('upload__container')}>
                <FileUpload
                  disabled={isSubmitBtnLoading}
                  accept='.pkcs12,.pfx,.p12'
                  onFileSelect={field.onChange}
                >
                  <Button
                    disabled={isSubmitBtnLoading}
                    type='button'
                    className={cx('form__btn')}
                    size='small'
                    color='darkGray'
                    leftIcon={'clipBold'}
                  >
                    {field.value?.name ? t('replace_archive') : t('select_archive')}
                    <HelpIcon
                      className={cx('tooltip__icon')}
                      tooltipClassname={cx('tooltip')}
                      text={t('archive_extension')}
                    />
                  </Button>
                </FileUpload>
                {field.value && (
                  <div className={cx('upload__content')}>
                    <IconWrapper onClick={() => form.resetField('pkcs12_file')}>
                      <CloseBoldIcon />
                    </IconWrapper>
                    <p>{`${truncateString(field.value.name, 18)}`}</p>
                  </div>
                )}
              </div>
            )}
          />
          <Input label={t('password_if_need')} register={form.register('password')} />
        </React.Fragment>
      )}
      {isCertificateViewAvailable && form.getValues('certificate.not_after') && (
        <p className={cx('expired')}>
          {t('cert_valid_until')}{' '}
          {getDateThroughDot(new Date(form.getValues('certificate.not_after')))}
        </p>
      )}
      {isCrtKeyError && <p className='error-text'>{t('error_loading_cert_and_key')}</p>}
      {isArchiveTriggerError && <p className='error-text'>{t('error_loading_archive')}</p>}
      {isError && <p className='error-text'>{t('error')}</p>}
      <div className={cx('form__actions')}>
        <Button
          onClick={onCancelChanges}
          disabled={(!form.formState.isDirty && !isCancelChangesAvailable) || isSubmitBtnLoading}
          type='button'
          color='gray'
        >
          {t('commons:cancel_changes')}
        </Button>
        <Button disabled={isSubmitBtnLoading} type='submit'>
          {isSubmitBtnLoading ? t('commons:loading_with_dots') : t('commons:save_changes')}
        </Button>
      </div>
    </form>
  )
}
