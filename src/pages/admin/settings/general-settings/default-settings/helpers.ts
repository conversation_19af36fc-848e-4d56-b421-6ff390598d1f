import {
  IDefaultSettingsKeyAndCertificateResponse,
  IDefaultSettingsPatchRequest,
  IDefaultSettingsUploadArchiveResponse,
} from "@/shared/types/store/settings";
import { DefaultSettingsFormProps } from "./use-default-settings";

type DefaultOrganizationWithCertKeyFnProps = {
  data: DefaultSettingsFormProps;
  keysData: IDefaultSettingsKeyAndCertificateResponse;
};

export const getDefaultOrganizationWithCertKey = (
  props: DefaultOrganizationWithCertKeyFnProps
) => {
  const { data, keysData } = props;

  const preparedData: IDefaultSettingsPatchRequest = {
    certificate_id: keysData.id,
    logo: data.logo,
    title: data.title,
    url: data.url,
  };

  return preparedData;
};

type DefaultOrganizationWithArchiveProps = {
  data: DefaultSettingsFormProps;
  archiveData: IDefaultSettingsUploadArchiveResponse;
};

export const getDefaultOrganizationWithArchive = (
  props: DefaultOrganizationWithArchiveProps
): IDefaultSettingsPatchRequest => {
  const { data, archiveData } = props;

  const preparedData: IDefaultSettingsPatchRequest = {
    certificate_id: archiveData.id,
    logo: data.logo,
    title: data.title,
    url: data.url,
  };

  return preparedData;
};

export const prepareArchive = (
  data: DefaultSettingsFormProps
): FormData | null => {
  if (!data.pkcs12_file) return null;
  const formData = new FormData();
  if (data.password) {
    formData.append("password", data.password);
  }

  const blob = new Blob([data.pkcs12_file], { type: data.pkcs12_file?.type });
  formData.append("pkcs12_file", blob);

  return formData;
};

export const prepareCrtKey = (
  data: DefaultSettingsFormProps
): FormData | null => {
  if (!data.cert_file || !data.key_file) return null;

  const formData = new FormData();

  const certFileBlob = new Blob([data.cert_file], {
    type: data.cert_file?.type,
  });
  const keyFileBlob = new Blob([data.key_file], { type: data.key_file?.type });

  formData.append("cert_file", certFileBlob);
  formData.append("key_file", keyFileBlob);

  return formData;
};
