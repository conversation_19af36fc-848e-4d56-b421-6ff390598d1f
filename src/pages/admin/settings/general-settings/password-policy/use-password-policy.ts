import { useCallback, useEffect, useMemo, useState, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

import { settingsApi } from '@/entities/settings'
import {
  PasswordPolicyRequest,
  PasswordPolicyValidationErrors,
  PasswordPolicy,
  UserRole,
} from '@/shared/types/store/settings'
import { useNotification } from '@/shared/contexts/notifications'

const TRANSLATION_FILE = 'pages__settings__settings'

interface PasswordPolicyFormData extends PasswordPolicyRequest {
  max_age_enabled: boolean
}

const DEFAULT_FORM_VALUES: PasswordPolicyFormData = {
  role: 'operator',
  min_length: 8,
  require_uppercase: true,
  require_lowercase: true,
  require_numbers: true,
  require_special_chars: true,
  max_age_days: 60,
  max_age_enabled: false,
  forbidden_words: false,
  prevent_sequences: false,
}

const MIN_PASSWORD_LENGTH = 8
const MAX_PASSWORD_AGE = 365
const FOCUS_DELAY = 20

export const usePasswordPolicy = () => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const { handleResponse } = useNotification()

  const [activePolicy, setActivePolicy] = useState<UserRole>('operator')
  const [originalData, setOriginalData] = useState<Record<UserRole, PasswordPolicyFormData | null>>(
    {
      operator: null,
      employee: null,
    },
  )

  const maxAgeInputRef = useRef<HTMLInputElement>(null)

  const { data: policies, isLoading } = settingsApi.useGetPasswordPoliciesQuery()
  const [updatePolicy, { isLoading: isUpdating }] = settingsApi.useUpdatePasswordPolicyMutation()

  const form = useForm<PasswordPolicyFormData>({
    mode: 'onChange',
    defaultValues: DEFAULT_FORM_VALUES,
  })

  const watchedValues = form.watch()

  const [minLength, requireNumbers, requireSpecialChars, maxAgeEnabled, maxAgeDays] = form.watch([
    'min_length',
    'require_numbers',
    'require_special_chars',
    'max_age_enabled',
    'max_age_days',
  ])

  const validationErrors = useMemo(() => {
    const errors: PasswordPolicyValidationErrors = {}

    if (minLength < MIN_PASSWORD_LENGTH) {
      errors.min_length = t('password_length_error')
    }

    if (!requireNumbers && !requireSpecialChars) {
      errors.require_special_chars = t('password_digits_or_special_error')
    }

    if (maxAgeEnabled && maxAgeDays && maxAgeDays > MAX_PASSWORD_AGE) {
      errors.max_age_days = t('password_max_age_error')
    }

    return errors
  }, [minLength, requireNumbers, requireSpecialChars, maxAgeDays, maxAgeEnabled, t])

  const currentPolicy = useMemo(() => {
    if (!policies) return null
    return activePolicy === 'operator' ? policies.operator_policy : policies.employee_policy
  }, [policies, activePolicy])

  const isFormValid = Object.keys(validationErrors).length === 0
  const isSubmitDisabled = !isFormValid || isUpdating || isLoading

  const createFormDataFromPolicy = useCallback(
    (policy: PasswordPolicy): PasswordPolicyFormData => ({
      role: policy.role,
      min_length: policy.min_length,
      require_uppercase: policy.require_uppercase,
      require_lowercase: policy.require_lowercase,
      require_numbers: policy.require_numbers,
      require_special_chars: policy.require_special_chars,
      max_age_days: policy.max_age_days || 60,
      max_age_enabled: policy.max_age_days !== null && policy.max_age_days > 0,
      forbidden_words: policy.forbidden_words,
      prevent_sequences: policy.prevent_sequences,
    }),
    [],
  )

  const createPolicyDataFromForm = useCallback(
    (data: PasswordPolicyFormData): PasswordPolicyRequest => ({
      role: data.role,
      min_length: data.min_length,
      require_uppercase: data.require_uppercase,
      require_lowercase: data.require_lowercase,
      require_numbers: data.require_numbers,
      require_special_chars: data.require_special_chars,
      max_age_days: data.max_age_enabled ? data.max_age_days : null,
      forbidden_words: data.forbidden_words,
      prevent_sequences: data.prevent_sequences,
    }),
    [],
  )

  const isFormDirty = useMemo(() => {
    const originalDataForCurrentPolicy = originalData[activePolicy]
    if (!originalDataForCurrentPolicy) return false

    return (
      watchedValues.min_length !== originalDataForCurrentPolicy.min_length ||
      watchedValues.require_uppercase !== originalDataForCurrentPolicy.require_uppercase ||
      watchedValues.require_lowercase !== originalDataForCurrentPolicy.require_lowercase ||
      watchedValues.require_numbers !== originalDataForCurrentPolicy.require_numbers ||
      watchedValues.require_special_chars !== originalDataForCurrentPolicy.require_special_chars ||
      watchedValues.max_age_days !== originalDataForCurrentPolicy.max_age_days ||
      watchedValues.max_age_enabled !== originalDataForCurrentPolicy.max_age_enabled ||
      watchedValues.forbidden_words !== originalDataForCurrentPolicy.forbidden_words ||
      watchedValues.prevent_sequences !== originalDataForCurrentPolicy.prevent_sequences
    )
  }, [originalData, activePolicy, watchedValues])

  useEffect(() => {
    if (currentPolicy) {
      const formData = createFormDataFromPolicy(currentPolicy)

      if (!originalData[activePolicy]) {
        setOriginalData(prev => ({
          ...prev,
          [activePolicy]: formData,
        }))
      }

      form.reset(formData)
    }
  }, [currentPolicy, activePolicy, originalData, form, createFormDataFromPolicy])

  const handlePolicyChange = useCallback(
    (item: { name: string }) => {
      const newPolicy = item.name as UserRole
      setActivePolicy(newPolicy)
      form.setValue('role', newPolicy)
    },
    [form],
  )

  const handleMaxAgeSwitchChange = useCallback(
    (enabled: boolean) => {
      form.setValue('max_age_enabled', enabled)
      if (enabled) {
        setTimeout(() => {
          if (maxAgeInputRef.current && !maxAgeInputRef.current.disabled) {
            maxAgeInputRef.current.focus()
          }
        }, FOCUS_DELAY)
      }
    },
    [form],
  )

  const onSubmit = useCallback(
    async (data: PasswordPolicyFormData) => {
      try {
        const policyData = createPolicyDataFromForm(data)
        await updatePolicy(policyData).unwrap()

        const updatedFormData: PasswordPolicyFormData = { ...data }
        setOriginalData(prev => ({
          ...prev,
          [data.role]: updatedFormData,
        }))

        const roleText = data.role === 'operator' ? t('for_operator') : t('for_employee')
        handleResponse(t('password_policy_saved_successfully', { role: roleText }))
      } catch (error) {
        console.error('Failed to update password policy:', error)
      }
    },
    [updatePolicy, handleResponse, t, createPolicyDataFromForm],
  )

  const onReset = useCallback(() => {
    const currentOriginalData = originalData[activePolicy]
    if (currentOriginalData) {
      form.reset(currentOriginalData)
    }
  }, [originalData, activePolicy, form])

  const TABS_SELECTOR = useMemo(
    () => [
      { name: 'operator', text: t('for_operators') },
      { name: 'employee', text: t('for_employees') },
    ],
    [t],
  )

  return {
    form,
    activePolicy,
    validationErrors,
    maxAgeInputRef,
    handleMaxAgeSwitchChange,
    watchedValues: {
      min_length: minLength,
      require_numbers: requireNumbers,
      require_special_chars: requireSpecialChars,
      max_age_enabled: maxAgeEnabled,
      max_age_days: maxAgeDays,
    },
    isLoading,
    isUpdating,
    isSubmitDisabled,
    TABS_SELECTOR,
    handlePolicyChange,
    onSubmit,
    onReset,
    t,
    isFormDirty,
  }
}
