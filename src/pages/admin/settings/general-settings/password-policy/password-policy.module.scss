.password-policy {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font: var(--font-text-1-normal);
  color: var(--color-gray-80);
}

.header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title {
  font: var(--font-title-4-normal);
  color: var(--color-gray-90);
  margin: 0;
}

.policy-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.section {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.field {
  display: flex;
  align-items: center;
  gap: 16px;
}

.label {
  font: var(--font-text-1-normal);
  color: var(--color-gray-90);
  font-weight: 500;
}

.input-max-age {
  padding-right: 50px !important;
  width: 100px;
}

.checkboxes {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.checkbox-wrapper {
  padding: 12px;
  background-color: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--color-gray-30);
}

.error-text {
  margin-top: 8px;
  color: var(--color-error);
  font: var(--font-caption-2-normal);
}

.actions {
  display: flex;
  gap: 16px;
}
