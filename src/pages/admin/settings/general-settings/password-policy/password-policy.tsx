import { FC } from 'react'
import { Controller } from 'react-hook-form'
import classNamesBind from 'classnames/bind'

import { Button, Input, Switch, Checkbox, HelpIcon, TabSelector } from '@/shared/ui'
import { usePasswordPolicy } from './use-password-policy'

import styles from './password-policy.module.scss'

const cx = classNamesBind.bind(styles)

export const PasswordPolicy: FC = () => {
  const {
    form,
    activePolicy,
    validationErrors,
    maxAgeInputRef,
    handleMaxAgeSwitchChange,
    watchedValues,
    isLoading,
    isUpdating,
    isSubmitDisabled,
    TABS_SELECTOR,
    handlePolicyChange,
    onSubmit,
    onReset,
    t,
    isFormDirty,
  } = usePasswordPolicy()

  return (
    <div className={cx('password-policy')}>
      <div className={cx('header')}>
        <h3 className={cx('title')}>{t('password_policy_title')}</h3>
        <HelpIcon text={t('password_policy_hint')} />
      </div>

      <div className={cx('policy-container')}>
        <TabSelector items={TABS_SELECTOR} active={activePolicy} onChange={handlePolicyChange} />

        <form onSubmit={form.handleSubmit(onSubmit)} className={cx('form')}>
          <div className={cx('section')}>
            <div>
              <div className={cx('field')}>
                <label className={cx('label')}>{t('min_password_length')}</label>
                <Input
                  disabled={isLoading}
                  type='number'
                  min={1}
                  max={50}
                  register={form.register('min_length', { valueAsNumber: true })}
                  className={cx('input')}
                />
              </div>
              {validationErrors.min_length && (
                <div className={cx('error-text')}>{validationErrors.min_length}</div>
              )}
            </div>

            <div className={cx('checkboxes')}>
              <Controller
                control={form.control}
                name='require_numbers'
                render={({ field }) => (
                  <Checkbox
                    label={t('require_digits')}
                    customChecked={field.value}
                    onChange={field.onChange}
                    disabled={isLoading}
                    className={cx('checkbox-wrapper')}
                  />
                )}
              />

              <Controller
                control={form.control}
                name='require_uppercase'
                render={({ field }) => (
                  <Checkbox
                    label={t('require_uppercase')}
                    customChecked={field.value}
                    onChange={field.onChange}
                    disabled
                    className={cx('checkbox-wrapper')}
                  />
                )}
              />

              <Controller
                control={form.control}
                name='require_lowercase'
                render={({ field }) => (
                  <Checkbox
                    label={t('require_lowercase')}
                    customChecked={field.value}
                    onChange={field.onChange}
                    disabled
                    className={cx('checkbox-wrapper')}
                  />
                )}
              />

              <Controller
                control={form.control}
                name='require_special_chars'
                render={({ field }) => (
                  <Checkbox
                    label={t('require_special_chars')}
                    customChecked={field.value}
                    onChange={field.onChange}
                    disabled={isLoading}
                    className={cx('checkbox-wrapper')}
                  />
                )}
              />
              {validationErrors.require_special_chars && (
                <div className={cx('error-text')}>{validationErrors.require_special_chars}</div>
              )}
            </div>

            <div>
              <div className={cx('field')}>
                <Controller
                  control={form.control}
                  name='max_age_enabled'
                  render={({ field }) => (
                    <Switch
                      disabled={isLoading}
                      customValue={field.value}
                      onChange={() => {
                        field.onChange(!field.value)
                        handleMaxAgeSwitchChange(!field.value)
                      }}
                      text=''
                    />
                  )}
                />
                <label className={cx('label')}>{t('max_password_age')}</label>
                <Input
                  disabled={isLoading || !watchedValues.max_age_enabled}
                  value={String(watchedValues.max_age_days)}
                  onChange={value => {
                    const numberedValue = Number(value)
                    form.setValue('max_age_days', numberedValue)
                  }}
                  name='max_age_days'
                  domain={t('commons:days_count_many', { count: watchedValues.max_age_days || 0 })}
                  className={cx('input-max-age')}
                  ref={maxAgeInputRef}
                />
              </div>
              {validationErrors.max_age_days && (
                <div className={cx('error-text')}>{validationErrors.max_age_days}</div>
              )}
            </div>

            <div className={cx('field')}>
              <Controller
                control={form.control}
                name='forbidden_words'
                render={({ field }) => (
                  <Switch
                    disabled={isLoading}
                    customValue={field.value}
                    onChange={() => field.onChange(!field.value)}
                    text=''
                  />
                )}
              />
              <label className={cx('label')}>{t('forbidden_words')}</label>
              <HelpIcon text={t('forbidden_words_hint')} />
            </div>

            <div className={cx('field')}>
              <Controller
                control={form.control}
                name='prevent_sequences'
                render={({ field }) => (
                  <Switch
                    disabled={isLoading}
                    customValue={field.value}
                    onChange={() => field.onChange(!field.value)}
                    text=''
                  />
                )}
              />
              <label className={cx('label')}>{t('prevent_sequences')}</label>
              <HelpIcon text={t('prevent_sequences_hint')} />
            </div>
          </div>

          <div className={cx('actions')}>
            <Button type='button' color='gray' onClick={onReset} disabled={!isFormDirty}>
              {t('commons:cancel_changes')}
            </Button>
            <Button type='submit' disabled={isSubmitDisabled || !isFormDirty} loading={isUpdating}>
              {t('commons:save_changes')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
