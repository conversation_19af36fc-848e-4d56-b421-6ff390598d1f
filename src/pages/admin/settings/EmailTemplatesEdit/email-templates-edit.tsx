import { FC, useState } from 'react'
import styles from './email-templates-edit.module.scss'
import classNamesBind from 'classnames/bind'
import { EmailTemplatesEditProps } from './email-templates-edit.d'
import {
  PageTitle,
  Button,
  Loader,
  Select,
  TabItem,
  TabsNew,
  IListItem,
  HelpIcon,
} from '@/shared/ui'
import { useEmailTemplatesEdit } from './use-email-templates-edit'
import { PagePreview } from '@/shared/components'
import { DeleteModal } from '@/shared/modals/delete-modal'
import { useTranslation } from 'react-i18next'
import { replaceLogoUrls } from '@/shared/helpers'

const cx = classNamesBind.bind(styles)

export const EmailTemplatesEdit: FC<EmailTemplatesEditProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation('pages__settings__email-templates-edit')

  const {
    templateList,
    onTemplateChange,
    activeTemplate,
    isLoading,
    openDeleteModal,
    setOpenDeleteModal,
    onDelete,
    isActionLoading,
    viewTabLabel,
    editorTabLabel,
    form,
    onSubmit,
    html,
    element,
    replacedMap,
  } = useEmailTemplatesEdit()

  // !UNDO AND REDO DOESN'T WORK WHEN CHANGE TAB
  // !USE OFFSCREEN FOR FIX IT OR DISPLAY NONE

  type TTabs = typeof viewTabLabel | typeof editorTabLabel

  const [activeTab, setActiveTab] = useState<TTabs>()

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('title-wrapper')}>
        <PageTitle className={cx('title')}>{t('commons:emails')}</PageTitle>
        <div className={cx('action-wrapper')}>
          {activeTemplate?.id && (
            <>
              <Button type='button' onClick={() => setOpenDeleteModal(true)} fullWidth color='red'>
                {t('return_to_default')}
              </Button>
              <DeleteModal
                id={activeTemplate.id}
                onClose={onDelete}
                active={openDeleteModal}
                setActive={setOpenDeleteModal}
                text={`${t('commons:confirm_delete_template')}?`}
              />
            </>
          )}

          <Button fullWidth loading={isActionLoading} onClick={() => onSubmit(form.getValues())}>
            {t('commons:save_changes')}
          </Button>
        </div>
      </div>

      {isLoading && <Loader size='56' className='loader_centered' />}
      {!isLoading && (
        <>
          <div className={cx('select__wrapper')}>
            <Select
              searchable
              label={t('select_template')}
              placeholder={t('select_template')}
              className={cx('template-select')}
              list={templateList}
              handleChange={(item: IListItem) => {
                onTemplateChange(item)
              }}
              value={activeTemplate?.type}
              key={
                !activeTemplate
                  ? 'emptyList'
                  : `notEmptyList-${activeTemplate?.type}-${activeTemplate?.id}`
              }
            />
            <HelpIcon text={t('hint')} />
          </div>
          <div className={cx('tabs__page')}>
            <TabsNew
              tabsClassname={cx('tabs')}
              activeTabClassname={cx('tabs__tab_active')}
              tabClassname={cx('tabs__tab')}
              defaultTab={viewTabLabel}
              hiddenType='display'
              onChange={(activeTab: TTabs) => {
                setActiveTab(activeTab)
              }}
            >
              <TabItem label={viewTabLabel} value={viewTabLabel} key={viewTabLabel}>
                <div className={cx('tab__wrapper', 'preview')}>
                  <PagePreview
                    // чтобы при изменении таба preview перерендеривался
                    // т.к. был баг что не перерендеривается
                    key={activeTab}
                    viewportWidth={600}
                    viewportHeight={580}
                    html={replaceLogoUrls(html, replacedMap)}
                  />
                </div>
              </TabItem>
              <TabItem label={editorTabLabel} value={editorTabLabel} key={editorTabLabel}>
                <div className={cx('tab__wrapper')}>
                  <div className={cx('ckeditor-wrapper')}>
                    <div ref={element} />
                  </div>
                </div>
              </TabItem>
            </TabsNew>
          </div>
        </>
      )}
    </div>
  )
}
