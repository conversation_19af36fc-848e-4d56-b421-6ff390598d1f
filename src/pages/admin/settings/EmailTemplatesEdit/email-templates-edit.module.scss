.wrapper {
  margin: 0 auto;
  max-width: var(--page-container);
  width: 100%;
}

.title-wrapper {
  align-items: center;
  display: flex;
  gap: 8px;
  justify-content: space-between;

  margin-bottom: 32px;

  .title {
    margin-bottom: 0;
  }

  .action-wrapper {
    align-items: center;
    display: grid;
    grid-gap: 8px;
    grid-template-columns: auto auto;
  }
}

.ckeditor-wrapper {
  height: 100%;
  > *:last-child {
    height: 100%;
    > *:last-child {
      display: grid;
      grid-template-rows: auto 1fr auto;

      height: 100%;
    }
  }
}
.template-select {
  margin-bottom: 24px;
  width: 100%;
  max-width: 460px;
  min-width: 225px;
}

.select__wrapper {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
  align-items: center;
}

.tabs__page {
  margin-bottom: 16px;
  position: relative;
}

.tabs {
  gap: 8px;

  &__tab {
    background: var(--color-surface, #fff);

    border-radius: 12px 12px 0 0;

    color: var(--color-gray-80, #5c6585) !important;
    font: var(--font-text-2-medium);

    padding: 8px 16px;

    transition: var(--transition);

    &:nth-child(1) {
      margin-bottom: -17px;
      padding-bottom: 25px;
    }

    &:nth-child(n + 2) {
      background: var(--gray-gray-70, #8e97af);
      box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.03);
      color: var(--color-surface, #fff) !important;
    }

    &_active {
      transition: var(--transition);

      &:nth-child(1) {
        box-shadow: 0 8px 25px 0 rgba(0, 0, 0, 0.03);
        color: var(--gray-gray-90, #343b54) !important;
      }

      &:nth-child(n + 2) {
        background: #1a1a1a;
      }
    }
  }
}

.tab {
  &__wrapper {
    background-color: var(--white, #fff);
    border-radius: 0 12px 12px;
    height: 600px;
    position: relative;

    width: 100%;
    z-index: 2;

    &.dark {
      border-radius: 12px;
      overflow: hidden;
    }
  }
}

.preview {
  overflow: hidden;
  overflow-y: auto;
}

.demo-wrapper {
  display: grid !important;
  grid-template-rows: auto 1fr;
  height: 600px !important;

  width: 100% !important;
}
.demo-editor {
  border: 1px solid #f1f1f1 !important;
  border-radius: 2px !important;
  padding: 5px !important;
}

.demo-custom-option {
  border: 1px solid #f1f1f1;
  cursor: pointer;
  height: 26px;
  padding-bottom: 2px;
  padding-right: 5px;

  text-align: center;
  &:hover {
    box-shadow: 1px 1px 0 #bfbdbd;
  }
}
