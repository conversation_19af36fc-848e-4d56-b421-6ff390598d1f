/* eslint-disable @typescript-eslint/no-explicit-any */
import { useEffect, useMemo, useRef, useState } from 'react'
import { IListItem } from '@/shared/ui'
import { SubmitHandler, useForm } from 'react-hook-form'
import { useNotification } from '@/shared/contexts/notifications'
import { prepareReplacedMapForUrls, replaceLogoUrls } from '@/shared/helpers'
import { useConfig } from '@/shared/hooks'
import {
  CKEditorEventAction,
  registerEditorEventHandler,
  useCKEditor,
} from '@sn.secure-t-org/ckeditor4-react'
import { config } from '@/shared/configs/editor/config'
import { useTranslation } from 'react-i18next'
import {
  EmailTemplateTypes,
  mutationEmailTemplatesApi,
  queryEmailTemplatesApi,
} from '@/entities/email-templates'

interface IEmailTemplateForm {
  html: string
}

interface IActiveTemplate {
  type: EmailTemplateTypes
  id?: Nullable<UUID>
  htmlStatic: string
}

export const useEmailTemplatesEdit = () => {
  const { t } = useTranslation('pages__settings__email-templates-edit')

  const viewTabLabel = t('commons:view')
  const editorTabLabel = t('commons:html_editor')

  const templateNamesList: IListItem[] = useMemo(() => {
    const EMAIL_TYPES: Record<EmailTemplateTypes, string> = {
      reset: t('password_recovery'),
      pass_changed: t('change_password'),
      slow_progress: t('slow_progress'),
      completion: t('completing_registration'),
      invite: t('invite'),
      notify: t('notice_to_employee'),
      course_assignment: t('course_assign'),
      expired_license: t('license_expires'),
      tag_message: t('message_by_tag'),
      pdf_report: t('message_about_PDF_report'),
      learning_certificate: t('message_about_PDF_cert'),
      tag_action_learning_report: t('message_about_EXCEL_department'),
    }

    return (Object.keys(EMAIL_TYPES) as Array<keyof typeof EMAIL_TYPES>).map(key => ({
      id: key,
      title: EMAIL_TYPES[key],
    }))
  }, [t])

  // Активный шаблон (тут же по идее будет происходить редактирование и сохранение изменений)
  const [activeTemplate, setActiveTemplate] = useState<Nullable<IActiveTemplate>>(null)

  // Список дефолтных шаблонов
  const { data: defaultTemplatesData, isLoading: isDefaultTemplatesLoading } =
    queryEmailTemplatesApi.useGetDefaultEmailTemplatesQuery()
  // Список созданных шаблонов без html
  const { data: templatesData, isLoading } = queryEmailTemplatesApi.useGetEmailTemplatesQuery()
  // Получение шаблона по id
  const [getTemplate] = queryEmailTemplatesApi.useLazyGetEmailTemplateQuery()

  // Объект, который будет в себе хранить html дефолтного и не дефолтного состояния с бека + if
  const templates = useMemo(() => {
    if (!defaultTemplatesData || !templatesData) return null

    const list: Partial<
      Record<
        EmailTemplateTypes,
        {
          id?: string
          html?: string
          defaultHtml: string
        }
      >
    > = {}

    templateNamesList.forEach(item => {
      const id = item.id as EmailTemplateTypes

      list[id] = { defaultHtml: '' }

      const defaultValue = defaultTemplatesData.find(t => t.notification_type === id)
      const notDefaultValue = templatesData.find(t => t.notification_type === id)

      list[id].defaultHtml = defaultValue?.html_data || ''
      if (notDefaultValue?.id) {
        list[id].id = notDefaultValue.id
      }
    })

    return list
  }, [templateNamesList, defaultTemplatesData, templatesData])

  // Для редактирования текущего шаблона
  const form = useForm<IEmailTemplateForm>({
    mode: 'onBlur',
    values: { html: '' },
  })

  const appConfig = useConfig()

  const replacedMap = useMemo(
    () => ({
      '{{dark_logo_url}}': appConfig?.darkLogoUrl || appConfig?.logoUrl || '{{dark_logo_url}}',
      '{{light_logo_url}}': appConfig?.lightLogoUrl || appConfig?.logoUrl || '{{light_logo_url}}',
      '{{logo_url}}': appConfig?.logoUrl || '{{logo_url}}',
    }),
    [appConfig],
  )

  // Типы всех кастомных шаблонов, для проверки в списке выбора
  const templateNamesWithDefaultList: IListItem[] = useMemo(() => {
    if (!templates) return []

    return templateNamesList.map(item => {
      const isDefault = !templates[item.id as EmailTemplateTypes]?.id

      return {
        ...item,
        title: `${item.title}${isDefault ? ` ${t('by_default')}` : ` ${t('custom')}`}`,
      }
    })
  }, [templateNamesList, templates, t])

  const [updateEmailTemplate, { isLoading: isUpdateLoading }] =
    mutationEmailTemplatesApi.useUpdateEmailTemplateMutation()
  const [createEmailTemplate, { isLoading: isCreateLoading }] =
    mutationEmailTemplatesApi.useCreateEmailTemplateMutation()
  const [deleteEmailTemplate, { isLoading: isDeleteLoading }] =
    mutationEmailTemplatesApi.useDeleteEmailTemplateMutation()

  const isActionLoading = isUpdateLoading || isCreateLoading || isDeleteLoading

  const { handleErrorResponse } = useNotification()

  const onSubmit: SubmitHandler<IEmailTemplateForm> = async data => {
    if (!data.html) return handleErrorResponse(t('no_template'))
    if (!activeTemplate) {
      return handleErrorResponse(t('template_not_select'))
    }

    const replacedMap = prepareReplacedMapForUrls(appConfig)

    // Удаление добавлений, который помогали убирать работу формы в редакторе
    const parser = new DOMParser()
    const parsedDocument = parser.parseFromString(
      data?.html.replace(/<noscript.*?<\/noscript>/g, ''),
      'text/html',
    )
    const inputs = parsedDocument?.querySelectorAll<HTMLInputElement>('input')

    inputs.forEach(input => {
      input.removeAttribute('readonly')
      input.removeAttribute('onfocus')
    })

    const html = parsedDocument.documentElement.outerHTML

    const htmlValue = replaceLogoUrls(html || '', replacedMap)

    if (!activeTemplate.id) {
      // Добавляем
      createEmailTemplate({
        template_data: htmlValue,
        template_type: activeTemplate.type,
      })
        .unwrap()
        .then(({ id, html_data }) => {
          setActiveTemplate(prev => prev && { id, type: prev?.type, htmlStatic: html_data })
        })
      return
    }

    // Обновляем
    if (activeTemplate.id) {
      updateEmailTemplate({
        template_data: htmlValue,
        template_id: activeTemplate.id,
      }).unwrap()
    }
  }

  const onDelete = async (id?: Nullable<UUID>) => {
    if (!id) return
    if (!templates) return

    const type = activeTemplate?.type || 'reset'
    const html = templates[type]?.defaultHtml || ''

    await deleteEmailTemplate(id)
      .unwrap()
      .then(() => {
        form.setValue('html', html)
        editor && editor.setData(html)
        setActiveTemplate(prev =>
          prev
            ? {
                type: prev?.type,
                htmlStatic: html,
              }
            : null,
        )
      })
  }

  const [openDeleteModal, setOpenDeleteModal] = useState(false)

  const html = form.watch('html')

  const initData = form.getValues('html')
  const element = useRef<HTMLDivElement>(null)
  const isInit = useRef(false)

  const { editor } = useCKEditor({
    config,
    element: element.current,
    type: 'classic',
    dispatchEvent: ({ type, payload }) => {
      if (type === CKEditorEventAction.instanceReady) {
        initData && payload.editor.setData(initData)
      } else if (type === CKEditorEventAction.blur) {
        onChange(payload.editor.getData().trim())
      } else if (type === CKEditorEventAction.change) {
        onChange(payload.editor.getData().trim())
      }
    },
    subscribeTo: ['blur', 'instanceReady', 'change'],
    editorUrl: '/ckeditor/ckeditor.js',
  })

  const onTemplateChange = async (item: Nullable<IListItem>) => {
    if (!item || !templates) return

    const type = item.id as EmailTemplateTypes

    if (activeTemplate && activeTemplate.type === type) return

    let html = ''
    const id = templates[type]?.id

    if (!id) {
      html = templates[type]?.defaultHtml || ''
    } else {
      const { html_data } = await getTemplate(id).unwrap()

      html = html_data
    }

    setActiveTemplate(() => {
      return {
        htmlStatic: html,
        type,
        id,
      }
    })
    form.setValue('html', html)
    editor && editor.setData(html)
  }

  // Для заполнения редактора при первом рендере, когда еще editor не имеет информации о блоке
  useEffect(() => {
    if (!editor || !initData || isInit.current) {
      return
    }

    isInit.current = true
    editor.setData(initData)
  }, [initData, editor])

  const onChange = (newValue: string) => {
    form.setValue('html', newValue, { shouldDirty: true })
  }

  const listners = useRef<HTMLElement[]>([])

  const clearButton = (e: MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
  }

  useEffect(() => {
    if (!editor) return

    const cleanup = registerEditorEventHandler({
      editor,
      evtName: 'change',
      handler: () => {
        if (editor.document && editor.document['$']) {
          const iframe = editor.document['$'] as HTMLElement
          const submitButtons = iframe?.querySelectorAll<HTMLButtonElement>('button')
          const inputs = iframe?.querySelectorAll<HTMLInputElement>('input')

          submitButtons.forEach(button => {
            listners.current.forEach(button => {
              button?.removeEventListener('click', clearButton)
            })

            listners.current = []
            listners.current.push(button)

            button?.addEventListener('click', clearButton)
          })

          inputs.forEach(input => {
            input.setAttribute('readonly', 'readonly')
            input.setAttribute('onfocus', 'this.removeAttribute("readonly");')
          })
        }
      },
      priority: 0,
    })

    return cleanup
  }, [editor])

  return {
    onSubmit,
    templateList: templateNamesWithDefaultList,
    onTemplateChange,
    activeTemplate,
    isLoading: isLoading || isDefaultTemplatesLoading,
    openDeleteModal,
    setOpenDeleteModal,
    onDelete,
    isActionLoading,
    form,
    viewTabLabel,
    editorTabLabel,
    html,
    element,
    replacedMap,
  }
}
