import { useSearchParams } from 'react-router-dom'
import { IReportsRequestTypes } from '@/shared/types/store/settings/reports'
import { reportServiceApi } from '@/entities/reports'

export const PAGE_ITEMS_LIMIT = 12

export const useReportsPage = () => {
  const [currentQueryParams, setSearchParams] = useSearchParams()

  const type =
    (currentQueryParams.get('type') as IReportsRequestTypes) || ('general' as IReportsRequestTypes)

  const page = currentQueryParams.get('page') || 1

  const ACTUAL_PAGE = +page - 1

  const { data, isFetching, isLoading, isError } = reportServiceApi.useGetReportsQuery({
    category: type,
    limit: PAGE_ITEMS_LIMIT,
    offset: ACTUAL_PAGE * PAGE_ITEMS_LIMIT,
  })

  const onTabChange = (newType: string) => {
    if (!newType) return

    const queryParams = new URLSearchParams()
    queryParams.set('type', newType)
    queryParams.set('page', '1')
    setSearchParams(queryParams)
  }

  const handleClickPagination = (newPage: string) => {
    if (!newPage) return

    const queryParams = new URLSearchParams()
    queryParams.set('page', newPage)
    queryParams.set('type', type)
    setSearchParams(queryParams)
  }

  const onSetPage = (page: number) => {
    handleClickPagination(String(page + 1))
  }

  const COUNT_PAGE = Math.ceil((data?.total_count || 0) / PAGE_ITEMS_LIMIT)
  const isPaginationVisible = data?.data && COUNT_PAGE > 1

  return {
    ACTUAL_PAGE,
    onSetPage,
    isPaginationVisible,
    total: data?.total_count || 0,
    data,
    isLoading: isFetching || isLoading,
    isError,
    type,
    onTabChange,
  }
}
