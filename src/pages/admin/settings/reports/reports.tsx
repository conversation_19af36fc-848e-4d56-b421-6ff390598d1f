import { ReportsTable } from '@/shared/components/reports-table/reports-table'
import { Help<PERSON><PERSON>, PageTitle, Pagination } from '@/shared/ui'
import { PAGE_ITEMS_LIMIT, useReportsPage } from './use-reports-page'
import styles from './reports.module.scss'
import classNamesBind from 'classnames/bind'
import { useTranslation } from 'react-i18next'
import { TabsProps } from '@/shared/components'
import { Tabs } from '@/shared/components'
import { userAPI } from 'entities/employee'
import { useEffect, useMemo } from 'react'
import { ERole } from '@/shared/types/enums'
import { useAppSelector } from '@/store'

const cx = classNamesBind.bind(styles)

export const ReportsPage = () => {
  const {
    ACTUAL_PAGE,
    isLoading,
    isPaginationVisible,
    onSetPage,
    total,
    data,
    isError,
    type,
    onTabChange,
  } = useReportsPage()
  const { t } = useTranslation('pages__reports')
  const { data: info } = useAppSelector(userAPI.endpoints.getUserInfo.select())

  const ACTUAL_TABS = useMemo(() => {
    const TABS: TabsProps.TabItem[] = [
      { name: 'general', value: t('commons:total') },
      { name: 'learning', value: t('commons:learning') },
      { name: 'phishing', value: t('commons:phishing') },
      { name: 'archive', value: t('commons:archival') },
    ]

    return info?.role !== ERole.content_manager
      ? TABS
      : [...TABS].filter(item => item.name !== 'phishing')
  }, [info?.role, t])

  useEffect(() => {
    if (type === 'phishing' && info?.role === ERole.content_manager) {
      onTabChange('general')
    }
  }, [type, info?.role])

  return (
    <>
      <div className={cx('title__wrapper')}>
        <PageTitle className={cx('title')}>{t('commons:reports')}</PageTitle>
        <HelpIcon className={cx('icon')} text={t('hint')} />
      </div>
      <Tabs className={cx('tabs')} tabs={ACTUAL_TABS} active={type} onClick={onTabChange} />
      <ReportsTable isError={isError} isLoading={isLoading} data={data?.data} />
      {isPaginationVisible && (
        <Pagination
          currentPage={ACTUAL_PAGE}
          limit={PAGE_ITEMS_LIMIT}
          total={total}
          onChange={onSetPage}
          isLoading={isLoading}
        />
      )}
    </>
  )
}
