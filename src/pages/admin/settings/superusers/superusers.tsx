import { BackButton, ButtonIcon, PageT<PERSON>le, RoundedButton, Tooltip } from '@/shared/ui'
import { settingsApi } from '@/entities/settings'
import { useCallback, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import classNamesBind from 'classnames/bind'
import styles from './superusers.module.scss'
import { getDateThroughDot } from '@/shared/helpers/date'
import { truncateStringInMiddle } from '@/shared/helpers'
import { DeleteConfirm } from '@/shared/modals/delete-confirm'
import { CreateSuperUserResponse } from '@/shared/types/store/settings/superusers'
import { SuperUserInfoModal } from './components/superuser-info-modal'
import { CopyButton } from '@/shared/components/copy-button/copy-button'

const cx = classNamesBind.bind(styles)

export const SuperusersPage = () => {
  const [openDeleteSlideId, setOpenDeleteSlideId] = useState<null | UUID>(null)
  const { organization_id = '' } = useParams()
  const { t } = useTranslation('pages__settings__superusers')
  const [superUserInfo, setSuperUserInfo] = useState<CreateSuperUserResponse | null>(null)

  const { data: superusers, isLoading } = settingsApi.useGetSuperusersQuery(organization_id)
  const [createSuperusers] = settingsApi.useCreateSuperuserMutation()
  const [deleteSuperusers] = settingsApi.useDeleteSuperuserMutation()

  const handleDeleteClick = useCallback(
    async (id: string | UUID) => {
      if (id) await deleteSuperusers(id)

      setOpenDeleteSlideId(null)
    },
    [deleteSuperusers],
  )

  const handleCreateClick = useCallback(() => {
    createSuperusers(organization_id)
      .unwrap()
      .then(data => setSuperUserInfo(data))
  }, [createSuperusers, organization_id])

  const handleSuperUserInfoModalClose = useCallback(() => {
    setSuperUserInfo(null)
  }, [])

  return (
    <div>
      <BackButton />
      <div className={cx('titleWrap')}>
        <PageTitle className={cx('title')}>{t('organization_superusers')}</PageTitle>
        <Tooltip content={t('create_superuser')}>
          <RoundedButton size='40' onClick={handleCreateClick} />
        </Tooltip>
      </div>
      {isLoading ? (
        <p>{t('commons:loading_with_dots')}</p>
      ) : superusers && superusers.length > 0 ? (
        <div className={cx('card-list')}>
          {superusers.map((superuser, index) => (
            <div key={superuser.id} className={cx('card')}>
              <div className={cx('card-title-wrapper')}>
                <h2 className={cx('card-title')}>
                  {/* eslint-disable-next-line i18next/no-literal-string */}
                  {t('superuser')} №{index + 1}{' '}
                </h2>
                <Tooltip content={t('commons:delete')}>
                  <ButtonIcon
                    onClick={setOpenDeleteSlideId.bind(null, superuser.id)}
                    icon='closeBold'
                  />
                </Tooltip>
              </div>
              <div className={cx('card-row')}>
                <div className={cx('row-name')}>{t('date_of_creation')}:</div>
                <div className={cx('row-value')}>
                  {getDateThroughDot(new Date(superuser.created_at))}
                </div>
              </div>
              <div className={cx('card-row')}>
                {/* eslint-disable-next-line i18next/no-literal-string */}
                <div className={cx('row-name')}>email:</div>
                <div className={cx('row-value', 'green')}>
                  <Tooltip content={superuser.email}>
                    <p>{truncateStringInMiddle(superuser.email)}</p>
                  </Tooltip>
                  <CopyButton
                    copyValue={superuser.email}
                    tooltipText={t('copy_email')}
                    notifyText={t('copy_email_success')}
                  />
                </div>
              </div>
            </div>
          ))}
          <SuperUserInfoModal
            superUserInfo={superUserInfo}
            onCloseModal={handleSuperUserInfoModalClose}
          />
          <DeleteConfirm
            id={openDeleteSlideId}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onClose={handleDeleteClick as any}
            title={t('confirm_delete_superuser')}
          />
        </div>
      ) : (
        <p>{t('no_superusers')}</p>
      )}
    </div>
  )
}
