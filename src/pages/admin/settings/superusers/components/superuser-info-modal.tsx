import { useCallback, useState } from 'react'
import { Modal } from '@/shared/components'
import { CreateSuperUserResponse } from '@/shared/types/store/settings/superusers'
import { Button, Checkbox, Tooltip } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './superuser-info-modal.module.scss'
import { truncateStringInMiddle } from '@/shared/helpers'
import { CopyButton } from '@/shared/components/copy-button/copy-button'
import { useTranslation } from 'react-i18next'

type Props = {
  superUserInfo: CreateSuperUserResponse | null
  onCloseModal: () => void
}

const cx = classNamesBind.bind(styles)

export const SuperUserInfoModal = ({ superUserInfo, onCloseModal }: Props) => {
  const { t } = useTranslation('pages__settings__superusers')
  const [isRememberPassword, setIsRememberPassword] = useState(false)

  const handleRememberPasswordChange = useCallback(() => {
    setIsRememberPassword(prev => !prev)
  }, [])

  const handleCloseModal = useCallback(() => {
    onCloseModal()
    setIsRememberPassword(false)
  }, [onCloseModal])

  return (
    <Modal
      active={Boolean(superUserInfo)}
      onClose={isRememberPassword ? handleCloseModal : undefined}
      setActive={() => { }}
    >
      {superUserInfo && (
        <div className={cx('wrapper')}>
          <h2 className={cx('title')}>{t('superuser_success_create')}</h2>
          <div className={cx('card')}>
            <div className={cx('card-row')}>
              {/* eslint-disable-next-line i18next/no-literal-string */}
              <div className={cx('row-name')}>Email:</div>
              <div className={cx('row-value', 'green')}>
                <Tooltip content={superUserInfo.email}>
                  <p>{truncateStringInMiddle(superUserInfo.email)}</p>
                </Tooltip>
                <CopyButton
                  copyValue={superUserInfo.email}
                  tooltipText={t('copy_email')}
                  notifyText={t('copy_email_success')}
                />
              </div>
            </div>
            <div className={cx('card-row')}>
              <div className={cx('row-name')}>{t('password')}:</div>
              <div className={cx('row-value')}>
                <p>{superUserInfo.password}</p>
                <CopyButton
                  copyValue={superUserInfo.password}
                  tooltipText={t('copy_password')}
                  notifyText={t('copy_password_success')}
                />
              </div>
            </div>
            <p className={cx('alert')}>{t('password_alert')}</p>
          </div>
          <Checkbox
            onChange={handleRememberPasswordChange}
            label={t('password_saved')}
            customChecked={isRememberPassword}
            className={cx('checkbox')}
          />
          <Button disabled={!isRememberPassword} onClick={handleCloseModal}>
            {t('close')}
          </Button>
        </div>
      )}
    </Modal>
  )
}
