.title {
  font-family: var(--font-title);
  font-size: 25px;
  font-weight: bold;
  text-align: center;
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 28px;
  max-width: 450px;
  padding: 10px 36px;
  padding-bottom: 20px;
  width: 100%;
}

.alert {
  margin-top: 20px;
  color: var(--color-gray-80, #5c6585);
  font: var(--font-text-1-normal);
}

.checkbox {
  align-items: center;
}

.card {
  &-title {
    color: var(--color-gray-90, #343b54);
    font: var(--font-title-4-medium);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    margin-bottom: 8px;

    .row-name {
      color: var(--color-gray-80, #5c6585);
      font: var(--font-text-1-normal);
    }
    .row-value {
      display: flex;
      align-items: center;
      color: var(--color-gray-90, #343b54);
      font: var(--font-text-1-medium);
      gap: 5px;

      &.green {
        color: var(--text-good-text, #29a873);
        color: var(--color-primary, #3dbc87);
      }
    }
  }
}
