.card-title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.title {
  margin-bottom: 0;
}

.titleWrap {
  display: flex;
  justify-content: space-between;
  margin-bottom: 42px;
}

.card {
  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);

  border-radius: 12px;
  max-width: max-content;
  min-width: 365px;
  padding: 20px 24px 24px;

  &-title {
    color: var(--color-gray-90, #343b54);
    font: var(--font-title-4-medium);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &-row {
    display: flex;
    justify-content: space-between;
    align-items: center;

    margin-bottom: 8px;

    .row-name {
      color: var(--color-gray-80, #5c6585);
      font: var(--font-text-1-normal);
    }
    .row-value {
      display: flex;
      align-items: center;
      color: var(--color-gray-90, #343b54);
      font: var(--font-text-1-medium);
      gap: 5px;

      &.green {
        color: var(--text-good-text, #29a873);
        color: var(--color-primary, #3dbc87);
      }
    }
  }
}

.card-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
