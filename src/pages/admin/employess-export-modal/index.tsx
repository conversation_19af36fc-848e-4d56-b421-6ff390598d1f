import React, { useEffect } from 'react'
import { Report } from '@/shared/types/store/settings'
import { ReportViewModal } from '../../../shared/modals/report-modal'
import { useTranslation } from 'react-i18next'

type Props = {
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  generateReport: () => Promise<void>
  data?: Report
  reset: () => void
  isError: boolean
  isReportLoading: boolean
}

export const EmployessExportModal = ({
  open,
  setOpen,
  generateReport,
  isError,
  isReportLoading,
  data,
}: Props) => {
  const { t } = useTranslation('pages__employees')

  useEffect(() => {
    generateReport()
  }, [])

  const loaderTitle = () => {
    if (isReportLoading) return t('commons:report_generation')
    if (isError) return t('commons:error_unexpected')

    return (
      <p className={'success'}>
        {t('commons:report_in_progress')}
        <br />
        {t('commons:report_description')}
      </p>
    )
  }

  return (
    <ReportViewModal
      data={data}
      error={!!isError}
      isLoading={isReportLoading}
      loaderTitle={loaderTitle()}
      open={open}
      setOpen={setOpen}
      title={t('user_report')}
    />
  )
}
