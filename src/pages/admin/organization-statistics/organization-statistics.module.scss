@use '../../../shared/assets/styles/mixins/media';
@use '../../../shared/assets/styles/mixins/text';

.wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.error-text {
  margin-top: 12px;
}

.loading {
  background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-primary-70) 100%);
  height: 5px;
  left: 0;
  position: fixed;
  top: 0;

  width: 0;
  z-index: 100;
  &.loadingActive {
    animation: loading 60s ease-out;
  }
}

.header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16px;

  .headerTitle {
    @include text.title(30px);
  }
  .generateOverallButton {
    margin-left: 30px;
    max-width: 250px;
  }
}

.title {
  @include text.title(30px);
  margin: 20px 0 30px;
}

.mainMetricks {
  flex-direction: row;
  justify-content: space-between;
  margin: 20px 20px 0;
  overflow: hidden;
  .mainMetricksDataItem {
    flex: 1 0;
    + .mainMetricksDataItem {
      border-left: 1px solid var(--gray);
    }
  }
}

.row {
  display: flex;
  flex-shrink: 0;
  margin: 0 -20px 20px;
  padding: 0 20px;
  gap: 16px;

  @include media.mobile {
    flex-direction: column;
    margin: 0;
  }
}

.education {
  display: flex;
  flex-direction: column;
  &__title {
    margin-bottom: -4px;
  }
  margin-bottom: 20px;
  gap: 20px;

  &__widgets {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;

    &_xl {
      grid-column: 1 / 3;
    }
  }
}

.general {
  display: flex;
  flex-direction: column;
  &__title {
    margin-bottom: -4px;
  }
  gap: 20px;
  margin-bottom: 20px;

  &__widgets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;

    &__sm {
      grid-column: 1;
    }

    &_xl {
      grid-column: 2 / 4;
    }
  }
}

.phishing {
  display: flex;
  flex-direction: column;
  &__title {
    margin-bottom: -4px;
  }
  margin-bottom: 20px;
  gap: 20px;

  &__widgets {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

.block {
  margin: 0 20px;
  @include media.mobile {
    margin: 0 0 20px;
  }
}
.block-title {
  @include text.title(20px);
  margin-bottom: 15px;
}

.excelExport {
  margin-top: 10px;
}

@keyframes loading {
  0% {
    width: 0;
  }
  20% {
    width: 65%;
  }

  100% {
    width: 80%;
  }
}

.subtitle {
  margin-bottom: 16px;
}
