import classNamesBind from 'classnames/bind'
import React, { useEffect, useMemo, useState } from 'react'
import { DepartmentsTable, EmployeesTable } from '@/shared/components'
import { <PERSON><PERSON>, Tab, <PERSON>bView, BackButton } from '@/shared/ui'
import Skeleton from 'react-loading-skeleton'
import { StatisticsProps } from './organization-statistics.d'
import styles from './organization-statistics.module.scss'
import { ReportModal } from '@/shared/modals/report-modal'
import { calculateCompanyDateSummary } from '@/shared/helpers/statictics'
import { useParams, useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { GeneralInformation } from '@/shared/components/general-information'
import { CoursesProgress, CoursesProgressTopline } from '@/shared/components/courses-progress'
import {
  DataPoint,
  EducationChartWidget,
  RangeItem,
  RiskLevelChartWidget,
  TestingChartWidget,
} from '@/features/graphics'
import { getRiskLevelColor } from '../statistics/helper'
import { RiskDangerousWidget } from '@/shared/components/risk-dangerous-widget'
import { PhishingStatistics } from '@/shared/components/phishing-statistics'
import { LastNewsletter } from '@/shared/components/last-newsletter'
import { HintReportModal } from '@/shared/modals/report-modal/hint-report-modal'
import { DEFAULT_TIME_RANGE } from 'features/graphics/model/constants'
import { phishingQueries, type IPhishingCapmaignsStatusesResponse } from '@/entities/phishing'
import { calculateDays } from '@/shared/helpers/date'
import { DateCompanySummaryHashItem } from '@/shared/helpers/statistics'
import { organizationGatewayStatisticAPI, organizationStatisticAPI } from 'entities/statistic'
const {
  useGetRiskStatisticsQuery,
  useGetProgressQuery,
  useLazyGetProgressChangeQuery,
  useGetGroupQuery,
  useGetActiveCourseQuery,
  useGetPhishingStatisticsQuery,
  useGetRiskLevelChangeQuery,
  useGetRiskGroupsQuery,
} = organizationGatewayStatisticAPI

const cx = classNamesBind.bind(styles)

export const Statistics: React.FC<StatisticsProps.Props> = props => {
  const { organization_id = '' } = useParams()
  const { className } = props
  const { t } = useTranslation()

  const [currentQueryParameters] = useSearchParams()
  const from = currentQueryParameters?.get('from')

  const [testingData, setTestingData] = useState<DataPoint[]>([])
  const [educationData, setEducationData] = useState<DataPoint[]>([])

  const [testingTimeRangeItem, setTestingTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )
  const [educationTimeRangeItem, setEducationTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )
  const [riskLevelTimeRangeItem, setRiskLevelTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )

  const [campaignsStatus, setCampaignsStatus] =
    useState<keyof IPhishingCapmaignsStatusesResponse>('active')

  const { data: riskStatistics, isLoading: isLoadingRiskStatistics } =
    useGetRiskStatisticsQuery(organization_id)

  const { data: progress, isLoading: isLoadingProgress } = useGetProgressQuery({
    organization_id,
  })
  const { data: groups, isLoading: isLoadingProgressGroups } = useGetGroupQuery(organization_id)
  const { data: activeCourses, isLoading: isLoadingActiveCourses } = useGetActiveCourseQuery(
    organization_id,
    {
      skip: !organization_id,
    },
  )
  const { data: phishingStatistics, isLoading: isLoadingPhishingStatistics } =
    useGetPhishingStatisticsQuery(organization_id)
  const { data: riskLevelChange, isLoading: isLoadingRiskLevelChange } = useGetRiskLevelChangeQuery(
    {
      organization_id: organization_id,
      days_period: String(
        calculateDays(riskLevelTimeRangeItem?.value ?? DEFAULT_TIME_RANGE[0]?.value),
      ),
    },
  )
  const { data: organization, isLoading: isLoadingOrganization } =
    organizationStatisticAPI.useGetOrganizationQuery(organization_id)

  const { data: riskGroups, isLoading: isLoadingRiskGroups } =
    useGetRiskGroupsQuery(organization_id)

  const [triggerLazyProgress] = useLazyGetProgressChangeQuery()

  useEffect(() => {
    const getDataFn = async () => {
      const promises = [
        triggerLazyProgress(
          {
            organization_id: organization_id,
            days_period: testingTimeRangeItem?.value
              ? String(calculateDays(testingTimeRangeItem?.value))
              : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
          },
          true,
        ),
      ]
      const [{ data: progressData }] = await Promise.all(promises)

      if (!progressData) return

      const { testingData } = calculateCompanyDateSummary(
        progressData.progress as DateCompanySummaryHashItem[],
      )
      setTestingData(testingData)
    }
    getDataFn()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [testingTimeRangeItem, triggerLazyProgress])

  useEffect(() => {
    const getDataFn = async () => {
      const promises = [
        triggerLazyProgress(
          {
            organization_id: organization_id,
            days_period: educationTimeRangeItem?.value
              ? String(calculateDays(educationTimeRangeItem?.value))
              : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
          },
          true,
        ),
      ]
      const [{ data: progressData }] = await Promise.all(promises)

      if (!progressData) return

      const { learningData } = calculateCompanyDateSummary(
        progressData.progress as DateCompanySummaryHashItem[],
      )

      setEducationData(learningData)
    }
    getDataFn()

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [educationTimeRangeItem, triggerLazyProgress])

  const { data: lastPhishingCampaign, isLoading: isLoadingPhishingCampaings } =
    phishingQueries.useGetPhishingLastCampaignQuery(organization_id)

  useEffect(() => {
    if (!lastPhishingCampaign && campaignsStatus !== 'completed') setCampaignsStatus('completed')
  }, [campaignsStatus, lastPhishingCampaign])

  const isFetched = !(
    isLoadingProgress &&
    isLoadingPhishingStatistics &&
    isLoadingRiskStatistics &&
    isLoadingRiskLevelChange &&
    isLoadingProgressGroups &&
    isLoadingActiveCourses &&
    isLoadingOrganization
  )

  const risk_level_chart = useMemo(
    () => [
      {
        color: 'YELLOW',
        data: riskLevelChange?.risk_levels.map(i => ({ ...i, color: 'YELLOW' })),
      },
    ],
    [riskLevelChange],
  )

  const phishing_chart = useMemo(
    () => ({
      sent: phishingStatistics?.sent || undefined,
      opened: phishingStatistics?.opened_letters || undefined,
      followed: phishingStatistics?.link_clicks || undefined,
      entered: phishingStatistics?.entered_data || undefined,
      attachments: phishingStatistics?.opened_attachments || undefined,
    }),
    [phishingStatistics],
  )

  const groups_chart_mini = useMemo(
    () => ({
      normal: groups?.normal,
      behind: groups?.behind,
    }),
    [groups],
  )

  const [openGenerateReport, setOpenGenerateReport] = useState(false)
  const analytics = useAnalytics()

  const [openPhishingExcelReport, setOpenPhishingExcelReport] = useState(false)

  const onPhishingExcelClick = async () => {
    analytics.event('modules.organization_statistics.report', {
      type: 'organization_phishing',
      title: organization?.title,
    })
    setOpenPhishingExcelReport(true)
  }

  const [openOverallReport, setOpenOverallReport] = useState(false)

  const onOverallClick = async () => {
    analytics.event('modules.organization_statistics.report', {
      type: 'organization_overall',
      title: organization?.title,
    })
    setOpenOverallReport(true)
  }

  const items = [
    {
      id: 'learning_progress',
      title: t('commons:learning_progress'),
      // TODO: Rewrite this piece of shit
      value: Number(progress?.overall?.toFixed(0)) ?? undefined,
      suffix: '%',
      color: 'null',
      loading: isLoadingProgress,
      hasActiveCourse: !!activeCourses?.active,
    },
    {
      id: 'active_courses',
      title: t('commons:active_courses'),
      value: activeCourses?.active,
      loading: isLoadingActiveCourses,
    },
    {
      id: 'probability_of_incident',
      title: t('commons:probability_of_incident'),
      value: phishingStatistics?.value?.toFixed(),
      color: phishingStatistics?.color,
      loading: isLoadingPhishingStatistics,
      suffix: '%',
    },
    {
      id: 'risk_level',
      title: t('commons:risk_level'),
      value: riskStatistics?.risk_level?.toFixed(1),
      color: getRiskLevelColor(Number(riskStatistics?.risk_level?.toFixed(1)) ?? 0),
      loading: isLoadingRiskStatistics,
    },
  ]

  return (
    <div className={cx('wrapper', className)}>
      <BackButton route={from ? `/lk/admin/organization/${from}` : '/lk/admin/organization/'} />
      {openOverallReport && (
        <HintReportModal
          type='subOrganizationPDF'
          open={openOverallReport}
          setOpen={setOpenOverallReport}
          title={t('commons:organization_report')}
          objectID={organization_id}
        />
      )}
      {openGenerateReport && (
        <ReportModal
          type='progress'
          open={openGenerateReport}
          setOpen={setOpenGenerateReport}
          title={t('commons:who_needs_attention')}
          objectID={organization_id}
        />
      )}
      <div className={cx('header')}>
        <div className={cx('headerTitle')}>{organization?.title || <Skeleton width='20pc' />}</div>
        <Button
          className={cx('generateOverallButton')}
          onClick={onOverallClick}
          disabled={isLoadingOrganization || !organization?.license?.users_count}
        >
          {t('commons:organization_report')}
        </Button>
      </div>
      <div className={cx('general')}>
        <h3 className={cx('general__title')}>{t('commons:total_information')}</h3>
        <GeneralInformation items={items} />
        <div className={cx('general__widgets')}>
          <div className={cx('general__widgets_sm')}>
            <RiskDangerousWidget {...riskGroups} showLink={false} isLoading={isLoadingRiskGroups} />
          </div>
          <div className={cx('general__widgets_xl')}>
            <RiskLevelChartWidget
              isLoading={!risk_level_chart?.[0]?.data}
              chartProps={{
                data: risk_level_chart?.[0]?.data ?? [],
                type: 'redToGreen',
                customMax: 10,
                customMin: 0,
                dimension: '',
              }}
              defaultItem={riskLevelTimeRangeItem}
              onTimeRangeChange={v => setRiskLevelTimeRangeItem(v)}
              descriptionProps={{ risk: Number(riskStatistics?.risk_level?.toFixed(1)) ?? 0 }}
            />
          </div>
        </div>
      </div>
      <div className={cx('education')}>
        <h3 className={cx('education__title')}>{t('commons:testing_training_statistics')}</h3>
        <div className={cx('education__widgets')}>
          <CoursesProgress
            {...groups_chart_mini}
            showArrow={false}
            active={activeCourses?.active}
            finished={activeCourses?.completed}
            isLoading={isLoadingActiveCourses || isLoadingProgressGroups}
            topSlot={
              <CoursesProgressTopline
                showArrow={false}
                active={activeCourses?.active}
                finished={activeCourses?.completed}
              />
            }
          />
          <TestingChartWidget
            isLoading={!testingData}
            chartProps={{
              data: testingData,
              customMin: 0,
            }}
            descriptionProps={{ precent: progress?.quiz?.toFixed(1) ?? 0 }}
            onTimeRangeChange={v => setTestingTimeRangeItem(v)}
            defaultItem={testingTimeRangeItem}
          />
          <div className={cx('education__widgets_xl')}>
            <EducationChartWidget
              isLoading={!educationData}
              chartProps={{
                data: educationData,
                customMin: 0,
              }}
              descriptionProps={{ percent: progress?.overall?.toFixed(1) ?? 0 }}
              onTimeRangeChange={v => setEducationTimeRangeItem(v)}
              defaultItem={educationTimeRangeItem}
            />
          </div>
        </div>
      </div>
      <div className={cx('phishing')}>
        <h3 className={cx('phishing__title')}>{t('commons:phishing_statistics')}</h3>
        <div className={cx('phishing__widgets')}>
          {/* <AreaChartWidget
            chartProps={{ data: MOCK_DATA, type: 'redToGreen' }}
            riskLevelProps={{ risk: 5.2 }}
          /> */}
          <PhishingStatistics {...phishing_chart} onClick={onPhishingExcelClick} />
          <LastNewsletter
            showLink={false}
            start_date={lastPhishingCampaign?.start_date}
            id={lastPhishingCampaign?.id}
            isLoading={isLoadingPhishingCampaings}
            {...lastPhishingCampaign?.statistic}
          />
          {openPhishingExcelReport && (
            <HintReportModal
              type='phishing'
              open={openPhishingExcelReport}
              setOpen={setOpenPhishingExcelReport}
              title={t('commons:phishing_statistics')}
              objectID={organization_id}
            />
          )}
        </div>
      </div>
      {organization && isFetched && (
        <div className={cx('row')}>
          <TabView
            tabs={[
              <Tab id='departments' title={t('commons:departments')}>
                <DepartmentsTable organization_id={organization_id} />
              </Tab>,
              <Tab id='employees' title={t('commons:employees')}>
                <EmployeesTable organization_id={organization_id} />
              </Tab>,
            ]}
          ></TabView>
        </div>
      )}
    </div>
  )
}

export default Statistics
