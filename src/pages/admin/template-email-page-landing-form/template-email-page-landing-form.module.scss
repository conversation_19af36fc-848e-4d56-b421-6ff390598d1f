@use '../../../shared/assets/styles/mixins/icons';

.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.jodit-workplace {
  overflow-y: auto;
  min-height: 100%;
  iframe {
    height: 100% !important;
  }
}

.actions {
  align-items: center;
  display: flex;
  gap: 24px;
  justify-content: space-between;
}

.ckeditor-wrapper {
  height: 100%;
  > *:last-child {
    height: 100%;
    > *:last-child {
      display: grid;
      grid-template-rows: auto 1fr auto;

      height: 100%;
    }
  }
}

.tab {
  &__wrapper {
    background-color: var(--white);
    border-radius: 0 12px 12px;
    height: 600px;
    width: 100%;
  }
}

.title {
  margin-bottom: 0;
  margin-top: -16px;
}

.tabs {
  gap: 8px;
  &__tab {
    background: var(--white);
    border-radius: 12px 12px 0 0;
    display: flex;

    font: var(--font-text-2-medium);
    opacity: 0.7;
    padding: 8px 16px 6px;

    transition: 0.2s ease;

    &_active {
      opacity: 1;
    }

    &:not(&_active):hover {
      opacity: 0.85;
    }
    &:nth-child(1) {
      background: var(--white);
    }
    &:nth-child(2) {
      background: #1a1a1a;
      color: var(--white);
    }
  }
}

.tabs__page {
  position: relative;
}

.format__btn {
  position: absolute;
  right: 0;
  top: 4px;

  * {
    font: var(--font-text-2-medium);
  }

  &:hover {
    path {
      stroke: var(--color-gray-90) !important;
    }
  }
}

.preview {
  overflow: hidden;
  overflow-y: scroll;
}

.white-underlay {
  background-color: var(--white);
  border-radius: 0 12px 12px;
  height: 100%;
  width: 100%;
}

.autoformatting {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 24px;
}
