import { FC } from 'react'
import styles from './template-email-page-landing-form.module.scss'
import classNamesBind from 'classnames/bind'
import { TemplateEmailPageLandingFormProps } from './template-email-page-landing-form.d'
import { TabsPage } from './compose/tabs-page'
import { useTemplateEmailPageLandingForm } from './use-template-email-page-landing-form'
import { Breadcrumbs, Button, PageTitle } from '@/shared/ui'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const TemplateEmailPageLandingForm: FC<TemplateEmailPageLandingFormProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const { BREADCRUMBS, form, isEdit, onSubmit, ButtonText, templateData } =
    useTemplateEmailPageLandingForm()

  return (
    <div className={cx('container', className)}>
      <div className={cx('actions')}>
        <Breadcrumbs items={BREADCRUMBS} />
        <Button
          disabled={!form.formState.isDirty || !form.watch('html')}
          onClick={form.handleSubmit(onSubmit)}
          type='submit'
        >
          {ButtonText}
        </Button>
      </div>
      <PageTitle className={cx('title')}>
        {
          isEdit
            ? `${t('commons:editing_page')} ${templateData?.name || ''}`
            : `${t('commons:creating_page')} ${templateData?.name || ''}`
        }
      </PageTitle>
      <TabsPage form={form} />
    </div>
  )
}
