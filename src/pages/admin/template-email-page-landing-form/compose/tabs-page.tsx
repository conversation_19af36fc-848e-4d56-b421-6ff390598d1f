/* eslint-disable @typescript-eslint/ban-ts-comment */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Controller, UseFormReturn } from 'react-hook-form'
import { v4 as uuid } from 'uuid'
import { PagePreview } from '@/shared/components'
import { type IEmail } from '@/entities/phishing'
import { Button, HelpIcon, TabItem, TabsNew } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from '../template-email-page-landing-form.module.scss'
import {
  CKEditorEventAction,
  registerEditorEventHandler,
  useCKEditor,
} from '@sn.secure-t-org/ckeditor4-react'
import { useCallback, useEffect, useRef, useState } from 'react'
import { config } from '@/shared/configs/editor/config'
import { useNotification } from '@/shared/contexts/notifications'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

type TabsPageProps = {
  form: UseFormReturn<IEmail, any>
}

const parser = new DOMParser()

const getSubmitButtons = (forms: HTMLFormElement[]) =>
  forms.map(
    form =>
      [
        ...Array.from(form.querySelectorAll('button')),
        ...Array.from(form.querySelectorAll('input[type="submit"]')),
      ] as (HTMLButtonElement | HTMLInputElement)[],
  )

export const TabsPage = ({ form }: TabsPageProps) => {
  const [isAutoFormattingDisabled, setIsAutoFormattingDisabled] = useState(true)
  const { t } = useTranslation()

  const viewTabLabel = t('commons:view')
  const editTabLabel = t('commons:html_editor')

  const onChange = (newValue: string) => {
    form.setValue('html', newValue, { shouldDirty: true })
  }

  const isInit = useRef(false)
  const data = form.watch('html')
  const element = useRef<HTMLDivElement>(null)

  const { add } = useNotification()

  const { editor } = useCKEditor({
    config,
    element: element.current,
    type: 'classic',
    dispatchEvent: ({ type, payload }) => {
      if (type === CKEditorEventAction.instanceReady) {
        data && payload.editor.setData(data)
      } else if (type === CKEditorEventAction.blur) {
        onChange(payload.editor.getData().trim())
      } else if (type === CKEditorEventAction.change) {
        onChange(payload.editor.getData().trim())
      }
    },
    subscribeTo: ['blur', 'instanceReady', 'change'],
    editorUrl: '/ckeditor/ckeditor.js',
  })

  const checkIfDisabled = useCallback(() => {
    let disabled = true

    if (editor) {
      const html = parser.parseFromString(
        editor.getData().replace(/<noscript.*?<\/noscript>/g, ''),
        'text/html',
      )

      const forms = Array.from(html.querySelectorAll('form'))

      const buttonsInnerForms = getSubmitButtons(forms)
      const isHaveSubmitButtons =
        buttonsInnerForms.length > 0 && buttonsInnerForms.some(buttons => !!buttons.length)

      if (forms.length > 0 && isHaveSubmitButtons) {
        const isFormsHaveRequiredAttributes = forms.every(
          innerForm =>
            innerForm.hasAttribute('method') &&
            innerForm.method.toLocaleLowerCase() === 'post' &&
            innerForm.action === window.location.href,
        )

        const isButtonsHaveRequiredAttributes = buttonsInnerForms.every(buttons =>
          buttons.every(btn => btn.type === 'submit'),
        )

        if (!isFormsHaveRequiredAttributes || !isButtonsHaveRequiredAttributes) disabled = false
      }
    }

    setIsAutoFormattingDisabled(disabled)
  }, [editor])

  const handleAutoFormattingClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()

    if (editor) {
      const html = parser.parseFromString(
        editor.getData().replace(/<noscript.*?<\/noscript>/g, ''),
        'text/html',
      )

      const forms = Array.from(html.querySelectorAll('form'))
      const buttonsInnerForms = getSubmitButtons(forms)

      forms.forEach(form => {
        form.setAttribute('method', 'post')
        form.setAttribute('action', '')
      })

      buttonsInnerForms.forEach(buttons =>
        buttons.forEach(btn => btn.setAttribute('type', 'submit')),
      )

      const head = html.head.innerHTML
      const value = html.body.innerHTML
      form.setValue('html', value, { shouldDirty: true })
      editor.setData(head + value)

      add({
        message: t('commons:format_done'),
        status: 'success',
        id: uuid(),
      })
    }
  }

  const listeners = useRef<HTMLElement[]>([])

  const clearButton = (e: MouseEvent) => {
    e.stopPropagation()
    e.preventDefault()
  }

  useEffect(() => {
    if (!editor) return

    const cleanup = registerEditorEventHandler({
      editor,
      evtName: 'change',
      handler: () => {
        if (editor.document && editor.document['$']) {
          const iframe = editor.document['$'] as HTMLElement
          const submitButtons = iframe?.querySelectorAll<HTMLButtonElement>('button')
          const inputs = iframe?.querySelectorAll<HTMLInputElement>('input')

          submitButtons.forEach(button => {
            listeners.current.forEach(button => {
              button?.removeEventListener('click', clearButton)
            })

            listeners.current = []
            listeners.current.push(button)

            button?.addEventListener('click', clearButton)
          })

          inputs.forEach(input => {
            input.setAttribute('readonly', 'readonly')
            input.setAttribute('onfocus', 'this.removeAttribute("readonly");')
          })
        }
      },
      priority: 0,
    })

    return cleanup
  }, [editor])

  // На всякий случай, а то в dispatchEvent дата может быть пустая
  useEffect(() => {
    if (!editor || !data || isInit.current) {
      return
    }

    isInit.current = true
    editor.setData(data)
  }, [data, editor])

  const [activeTab, setActiveTab] = useState<string>()

  return (
    <div className={cx('tabs__page')}>
      <div className={cx('autoformatting')}>
        <Button disabled={isAutoFormattingDisabled} onClick={handleAutoFormattingClick}>
          {t('commons:format_page')}
        </Button>
        <HelpIcon text={t('commons:add_necessary_attr')} />
      </div>
      <TabsNew
        tabsClassname={cx('tabs')}
        activeTabClassname={cx('tabs__tab_active')}
        tabClassname={cx('tabs__tab')}
        defaultTab={viewTabLabel}
        hiddenType='display'
        onChange={(activeTab: string) => {
          setActiveTab(activeTab)
        }}
      >
        <TabItem label={viewTabLabel} value={viewTabLabel} key={viewTabLabel}>
          <div className={cx('tab__wrapper', 'preview')}>
            <Controller
              name='html'
              control={form.control}
              render={({ field }) => {
                return (
                  <PagePreview
                    // чтобы при изменении таба preview перерендеривался
                    // т.к. был баг что не перерендеривается
                    key={activeTab}
                    viewportWidth={600}
                    viewportHeight={580}
                    html={field?.value || form.getValues('html')}
                    checkIfDisabled={checkIfDisabled}
                  />
                )
              }}
            />
          </div>
        </TabItem>
        <TabItem label={editTabLabel} value={editTabLabel} key={editTabLabel}>
          <div className={cx('tab__wrapper', 'dark')}>
            <div className={cx('white-underlay')} itemScope>
              <div className={cx('ckeditor-wrapper')}>
                <div ref={element} />
              </div>
            </div>
          </div>
        </TabItem>
      </TabsNew>
    </div>
  )
}
