/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react'
import { SubmitHandler, useForm } from 'react-hook-form'
import { useNotification } from '@/shared/contexts/notifications'
import { type IEmail, phishingMutations, phishingQueries } from '@/entities/phishing'
import { useNavigate, useParams } from 'react-router-dom'
import { useConfig } from '@/shared/hooks'
import {
  prepareReplacedMapForLogos,
  prepareReplacedMapForUrls,
  replaceLogoUrls,
} from '@/shared/helpers'
import { useTranslation } from 'react-i18next'

export const useTemplateEmailPageLandingForm = () => {
  const { template_id = '', page_id = '' } = useParams()
  const { t } = useTranslation()
  const isEdit = !!page_id
  const { data: pageData } = phishingQueries.useGetPageByIdQuery(page_id, { skip: !isEdit })
  const { data: templateData } = phishingQueries.useGetTemplateByIdQuery(template_id)
  const [patchPage, { isLoading: isPatchLoading }] = phishingMutations.usePatchPageByIdMutation()
  const [createPage, { isLoading: isCreateLoading }] = phishingMutations.useCreatePageByIdMutation()

  const config = useConfig()

  const form = useForm<IEmail>({
    values: {
      ...pageData,
      html: replaceLogoUrls(pageData?.html || '', prepareReplacedMapForLogos(config)),
    } as IEmail,
  })

  const editButtonText = isPatchLoading
    ? `${t('commons:saving_changes')}...`
    : t('commons:save_changes')

  const createButtonText = isCreateLoading
    ? `${t('commons:saving_changes')}...`
    : t('commons:save_changes')

  const ButtonText = isEdit ? editButtonText : createButtonText

  const { handleErrorResponse } = useNotification()
  const navigate = useNavigate()

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/templates',
        text: t('commons:templates'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/templates/' + template_id,
        text: templateData?.name || t('commons:current_template'),
        clickable: true,
        isLoading: !templateData,
      },
      {
        id: '1',
        text: isEdit
          ? `${t('commons:editing_page')}  ${templateData?.name || ''}`
          : `${t('commons:creating_page')} ${templateData?.name || ''}`,
        clickable: false,
      },
    ],
    [templateData, t],
  )

  const onSubmit: SubmitHandler<any> = async data => {
    if (!data.html) {
      handleErrorResponse({
        status: 'error',
        message: t('commons:enter_html_page'),
      })

      return
    }

    const replacedMap = prepareReplacedMapForUrls(config)

    const parser = new DOMParser()
    const parsedDocument = parser.parseFromString(
      data?.html.replace(/<noscript.*?<\/noscript>/g, ''),
      'text/html',
    )
    const inputs = parsedDocument?.querySelectorAll<HTMLInputElement>('input')

    inputs.forEach(input => {
      input.removeAttribute('readonly')
      input.removeAttribute('onfocus')
    })

    const html = parsedDocument.documentElement.outerHTML

    const htmlValue = replaceLogoUrls(html || '', replacedMap)

    if (isEdit) {
      await patchPage({ ...data, html: htmlValue }).unwrap()
      navigate(`/lk/admin/phishing/templates/${templateData?.id}`)

      return
    }

    await createPage({ html: htmlValue, template_id }).unwrap()
    navigate(`/lk/admin/phishing/templates/${templateData?.id}`)
  }

  return {
    BREADCRUMBS,
    form,
    isEdit,
    onSubmit,
    ButtonText,
    templateData,
  }
}
