export function areArraysEqual(array1: string[], array2: string[]) {
  if (array1.length !== array2.length) {
    return false
  }

  const set1 = new Set(array1)
  const set2 = new Set(array2)

  // Сравниваем размеры множеств, чтобы убедиться, что содержат одинаковое количество уникальных элементов
  if (set1.size !== set2.size) {
    return false
  }

  // Теперь можем просто сравнить множества
  for (const value of set1) {
    if (!set2.has(value)) {
      return false
    }
  }

  return true
}

type EntityWithId = {
  id: string
}

export function mergeUniqueItems<T extends EntityWithId>(...arrays: T[][]): T[] {
  const combinedArray: T[] = arrays.flat()

  const uniqueIds: { [key: string]: boolean } = {}

  const uniqueArray: T[] = combinedArray.filter(item => {
    if (uniqueIds[item.id]) {
      return false
    } else {
      uniqueIds[item.id] = true
      return true
    }
  })

  return uniqueArray
}
