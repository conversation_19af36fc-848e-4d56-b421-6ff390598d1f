.wrapper {
  height: 100%;
  margin: 0 auto;
  // padding: var(--page-container-padding);
  max-width: var(--page-container);
  position: relative;
}

.innerWrapper {
  max-width: 768px;
}

.pageTitle {
  margin-bottom: 24px;
}

.pageSubtitle {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-4-medium);
  margin-bottom: 16px;
}

.inner {
  align-items: start;
  display: grid;
  grid-gap: 12px;
  grid-template-columns: minmax(200px, 320px) minmax(200px, 1fr);
}
