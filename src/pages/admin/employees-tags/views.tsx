import { ETagType } from '@/shared/types/enums'
import { Input } from '@/shared/ui'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  selectRiskLevelMax,
  selectRiskLevelMin,
  selectTagLifeDays,
  selectTagsTurnOff,
  setRiskLevel,
  setTagLifeDays,
} from '@/store/slices/tags'
import styles from './tags-settings.module.scss'
import classNamesBind from 'classnames/bind'
import { AssignTagEmployees } from '@/shared/components/assign-tag-employees'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const NewbieView = () => {
  const dispatch = useAppDispatch()
  const { t } = useTranslation('pages__employees')

  const date = useAppSelector(selectTagLifeDays)

  return (
    <>
      <div className={cx('title')}>{t('assign_rule')}</div>
      <div className={cx('rulesWrapper')}>
        <div className={cx('date')}>
          {t('registered_less')}
          <Input
            value={String(date)}
            onChange={value => {
              const numberedValue = Number(value)

              dispatch(setTagLifeDays(numberedValue))
            }}
            name='date'
            domain={t('commons:days')}
            className={cx('inputDate')}
          />
        </div>
      </div>
    </>
  )
}

const ArbitraryView = () => {
  const turnOff = useAppSelector(selectTagsTurnOff)
  return turnOff && <AssignTagEmployees />
}

const NotRegisteredView = () => {
  return <></>
}

const RiskGroupView = () => {
  const { t } = useTranslation('pages__employees')
  const dispatch = useAppDispatch()

  const riskLevelMax = useAppSelector(selectRiskLevelMax)
  const riskLevelMin = useAppSelector(selectRiskLevelMin)

  return (
    <>
      <div className={cx('title')}>{t('assign_rule')}</div>
      <div className={cx('rulesWrapper')}>
        <div className={cx('riskLevel')}>
          {t('risk_level')}
          <div>
            {t('commons:from').toLocaleLowerCase()}
            <Input
              value={String(riskLevelMin || 0)}
              onChange={(value, name) => {
                const numberedValue = Number(value)
                dispatch(
                  setRiskLevel({
                    value: numberedValue,
                    name,
                  }),
                )
              }}
              name='min'
              className={cx('inputRisk')}
            />
            {t('commons:before').toLocaleLowerCase()}
            <Input
              value={String(riskLevelMax || 0)}
              onChange={(value, name) => {
                const numberedValue = Number(value)
                dispatch(
                  setRiskLevel({
                    value: numberedValue,
                    name,
                  }),
                )
              }}
              name='max'
              className={cx('inputRisk')}
            />
          </div>
        </div>
      </div>
    </>
  )
}

export const SETTINGS_BY_ACTION: Record<ETagType, JSX.Element> = {
  arbitrary: <ArbitraryView />,
  newbie: <NewbieView />,
  not_registered: <NotRegisteredView />,
  risk_group: <RiskGroupView />,
}
