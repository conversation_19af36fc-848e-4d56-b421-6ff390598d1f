.wrapper {
  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 16px;
  padding: 28px 32px;
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-1-medium);
  margin-bottom: 16px;
}

.tags {
  --tags-height: 400px;
  margin-bottom: 16px;
  max-height: var(--tags-height);
  padding-right: 5px;
  overflow-y: auto;
  &:last-child {
    margin-bottom: 0;
  }
}

.tagWrapper {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  &:last-child {
    margin-bottom: 0;
  }
  span {
    color: var(--color-primary, #3dbc87);
    font: var(--font-text-2-normal);
  }
}
