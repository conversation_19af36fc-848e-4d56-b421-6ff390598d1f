import { FC } from 'react'
import styles from './employees-tags.module.scss'
import classNamesBind from 'classnames/bind'
import { EmployeesTagsProps } from './employees-tags.d'
import { PageTitle } from '@/shared/ui'
import { NewTagsList } from './new-tag-list'
import { NewTagsSettings } from './new-tags-settings'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const EmployeesTags: FC<EmployeesTagsProps.Props> = props => {
  return <EmployeesTagsInner {...props} />
}

export const EmployeesTagsInner: FC<EmployeesTagsProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation('pages__employees')

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('innerWrapper')}>
        <PageTitle className={cx('pageTitle')}>{t('commons:tags')}</PageTitle>
        <div className={cx('pageSubtitle')}>{t('select_tag_to_customize')}</div>
        <div className={cx('inner')}>
          <NewTagsList />
          <NewTagsSettings />
        </div>
      </div>
    </div>
  )
}

export default EmployeesTags
