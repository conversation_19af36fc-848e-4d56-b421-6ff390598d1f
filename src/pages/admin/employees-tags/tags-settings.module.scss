.wrapper {
  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 16px;
  padding: 28px 32px;
}

.loader__wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;

  .button {
    margin-left: auto;
    width: 30%;
  }
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-text-1-medium);
  margin-bottom: 12px;
}

.text {
  color: var(--color-gray-90);
  font: var(--font-text-2-normal);
}

.inputTitle {
  padding-right: 50px;
}

.colorTooltip {
  width: max-content;
}

.inputTitleWrapper {
  margin-bottom: 24px;
  position: relative;
  .color {
    position: absolute;
    right: 16px;
    top: 10px;
  }
}

.titleWithHelp {
  align-items: center;
  display: flex;
  gap: 8px;
}

.turnOffWrapper {
  margin-bottom: 24px;
}

.errorText {
  margin-top: 12px;
}

.inputDate {
  border-radius: 8px;
  padding: 8px 50px 8px 12px !important;
  width: 88px;
}

.inputRisk {
  max-width: 50px;
  padding: 8px 13px 8px 12px !important;

  text-align: center;
}

.help {
  color: var(--color-primary, #3dbc87);
  font: var(--font-text-2-medium);
  margin-top: 12px;
  text-align: right;
}

.rulesWrapper {
  margin-bottom: 24px;

  .date,
  .riskLevel,
  .riskLevel > div {
    align-items: center;

    color: var(--color-gray-90, #343b54);
    display: flex;
    font: var(--font-text-2-normal);
    gap: 8px;
    justify-content: space-between;
  }

  .riskLevel {
    > div {
      justify-content: center;
    }
  }
}

.actionsWrapper {
  margin-bottom: 16px;
}

.saveButton {
  margin-left: auto;
}

.arbitraryAssignWrapper {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  gap: 10px;
}
