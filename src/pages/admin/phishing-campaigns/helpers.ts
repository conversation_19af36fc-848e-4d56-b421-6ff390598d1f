import { PhishingCampaignsFilters } from '@/entities/phishing/model/types'
import { PhishingCampaignsStateForm } from './phishing-campaigns-filters'

export const getRangeValue = ({ min, max, value }: { min: number; max: number; value: number }) => {
  if (value < min) return min
  if (value > max) return max
  return value
}

export function mapFormToApiParams(
  filters: PhishingCampaignsStateForm,
  formatDate: (date: Date) => string,
): PhishingCampaignsFilters {
  return {
    search: filters.search ?? null,
    status: filters.status ?? null,
    organization_id: filters.organization_id ?? null,
    by_tag: filters.by_tag,
    sort_by: filters.sort_by ?? 'START_DATE',
    sort_order: filters.sort_order ?? 'desc',

    start_date_from: filters.start_date_from ? formatDate(filters.start_date_from) : null,
    start_date_to: filters.start_date_to ? formatDate(filters.start_date_to) : null,
    end_date_from: filters.end_date_from ? formatDate(filters.end_date_from) : null,
    end_date_to: filters.end_date_to ? formatDate(filters.end_date_to) : null,

    incident_percent_from: filters.incident_percent_from ?? null,
    incident_percent_to: filters.incident_percent_to ?? null,

    targets_from: filters.targets_from ?? null,
    targets_to: filters.targets_to ?? null,

    templates_ids: filters.templates_ids ?? [],
    phishing_events: filters.phishing_events ?? [],
  }
}
