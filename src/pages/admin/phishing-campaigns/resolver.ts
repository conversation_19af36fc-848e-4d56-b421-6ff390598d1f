import { isNumber } from '@/shared/helpers'
import { z } from 'zod'

export const getPhishingCampaignsFiltersSchema = ({
  end_date_to_message,
  incident_percent_to_message,
  start_date_to_message,
  targets_to_message,
}: {
  start_date_to_message?: string
  end_date_to_message?: string
  incident_percent_to_message?: string
  targets_to_message?: string
}) =>
  z
    .object({
      search: z.string().nullable().optional(),
      status: z.string().nullable().optional(),
      organization_id: z.string().nullable().optional(),
      by_tag: z.boolean(),
      sort_by: z.string()?.optional(),
      sort_order: z.enum(['asc', 'desc'])?.optional(),

      start_date_from: z.preprocess(
        v => (v instanceof Date ? v : v ? new Date(v as string) : null),
        z.date().nullable(),
      ),
      start_date_to: z.preprocess(
        v => (v instanceof Date ? v : v ? new Date(v as string) : null),
        z.date().nullable(),
      ),
      end_date_from: z.preprocess(
        v => (v instanceof Date ? v : v ? new Date(v as string) : null),
        z.date().nullable(),
      ),
      end_date_to: z.preprocess(
        v => (v instanceof Date ? v : v ? new Date(v as string) : null),
        z.date().nullable(),
      ),

      incident_percent_from: z.preprocess(
        v => (isNumber(v) ? +v : null),
        z.number().min(0).max(100).nullable(),
      ),
      incident_percent_to: z.preprocess(
        v => (isNumber(v) ? +v : null),
        z.number().min(0).max(100).nullable(),
      ),

      targets_from: z.preprocess(
        v => (isNumber(v) ? +v : null),
        z.number().int().min(0).nullable(),
      ),
      targets_to: z.preprocess(v => (isNumber(v) ? +v : null), z.number().int().min(0).nullable()),

      templates_ids: z.array(z.string()),
      phishing_events: z.array(z.string()),
    })
    .superRefine((data, ctx) => {
      if (data.start_date_from && data.start_date_to && data.start_date_from > data.start_date_to) {
        ctx.addIssue({
          path: ['start_date_to'],
          code: 'custom',
          message: start_date_to_message,
        })
      }

      if (data.end_date_from && data.end_date_to && data.end_date_from > data.end_date_to) {
        ctx.addIssue({
          path: ['end_date_to'],
          code: 'custom',
          message: end_date_to_message,
        })
      }
      // проценты
      if (
        data.incident_percent_from !== null &&
        data.incident_percent_to !== null &&
        data.incident_percent_from > data.incident_percent_to
      ) {
        ctx.addIssue({
          path: ['incident_percent_to'],
          code: 'custom',
          message: incident_percent_to_message,
        })
      }

      // таргеты
      if (
        data.targets_from !== null &&
        data.targets_to !== null &&
        data.targets_from > data.targets_to
      ) {
        ctx.addIssue({
          path: ['targets_to'],
          code: 'custom',
          message: targets_to_message,
        })
      }
    })
