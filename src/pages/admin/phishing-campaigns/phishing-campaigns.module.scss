.wrapper {
  display: flex;
  flex-direction: column;

  min-height: 100%;
}

.filters-wrapper {
  align-items: center;
  display: flex;
  gap: 4px;
}

.tabs-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.searchWrapper {
  width: 100%;
  .search {
    background: #fff;
    width: 100%;
  }
}

.title-wrapper {
  align-items: center;
  display: flex;
  justify-content: space-between;
  gap: 24px;
  margin-bottom: 24px;

  &__block {
    display: flex;
    gap: 12px;
    width: 100%;
  }

  .title {
    margin-bottom: 0;
  }
}

.confirm-modal {
  &__wrapper {
    gap: 12px;
  }
}

.autophish-title {
  white-space: nowrap;
  text-align: center;
  @media (max-width: 1024px) {
    white-space: inherit;
  }
}


.autophish-wrapper {
  align-items: center;
  display: flex;
  gap: 32px;

  .autophish {
    align-items: center;
    display: flex;
    gap: 12px;

    span {
      color: var(--gray-gray-70, #8e97af);
      font: var(--font-text-1-normal);
    }
  }
}

.loader-wrapper {
  height: 100%;
}

.empty-wrapper {
  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 24px;

  height: 100%;
  justify-content: center;

  span {
    color: var(--gray-gray-70, #8e97af);
    display: block;
    font: var(--font-title-3-medium);
  }
}

.tabs__container {
  margin-bottom: 16px;
}

.list-wrapper {
  background: var(--stroke, #ebeff2);
  border: 1px solid var(--stroke, #ebeff2);

  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.separator {
  width: 100%;
  height: 1px;
  background-color: var(--stroke);
  margin-top: 12px;
}

.modal {
  width: calc(100% - 64px);
  max-width: 460px;
  margin: 0 auto;

  &__form {
    display: flex;
    flex-direction: column;
    gap: 12px;
    grid-template-columns: 1fr 1fr;
  }

  &__title {
    color: var(--gray-gray-90);
    font: var(--font-title-4-medium);
    text-align: left;
  }

  &__content {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px;
  }

  &__success {
    color: var(--color-primary-90);
    font: var(--font-text-2-normal);
  }

  &__error {
    color: var(--color-error);
    font: var(--font-text-2-normal);
  }

  &__loading {
    color: var(--color-gray-90);
    font: var(--font-text-2-normal);
  }

  &__actions {
    align-items: center;
    display: flex;
    gap: 16px;
    justify-content: space-between;

    * {
      width: 100%;
    }
  }

  &__intervals {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;

    &__content {
      gap: 4px;
      span {
        color: var(--color-gray-90);
      }
    }
  }

  &__interval {
    flex: 1;

    &-label {
      color: var(--color-gray-90) !important;
      text-align: start;
    }
  }

  &__title {
    color: var(--color-gray-70, #8e97af);
    font: var(--font-title-3-medium);
    margin-bottom: 20px;
  }

  &__separator {
    margin-top: 24px;
    color: var(--color-gray-80);
    &_dot {
      color: var(--color-gray-60);
    }
    font: var(--font-text-2-demibold);
  }

  &__buttons {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 16px;
    gap: 16px;
    grid-area: btns;
    margin-left: auto;

    button {
      flex: 1;
    }
  }

  &__datepicker {
    z-index: 1310;
    bottom: -200px;
  }
}

.fullWidth {
  width: 100%;
}

.multiselectLoader {
  &Wrapper{
    width: 100%;
    display: flex;
    align-items: start;
    flex-direction: column;
    gap: 12px;
  }
  &Label {
    color: var(--color-gray-70, #8e97af);
    font: var(--font-text-2-normal);
  }
  
  margin: 0 auto;
}

.error {
  color: var(--color-statistics-bad);
  font: var(--font-text-2-normal);
  font-size: 12px;
  margin-top: 12px;
}

.data-loading__wrapper {
  margin: 120px 0;
}

.active {
  color: var(--color-primary-90);

  &:hover {
    color: var(--color-primary-90);
  }
}

.header {
  background: var(--color-surface, #fff);
  border-bottom: 1px solid var(--stroke, #ebeff2);
  border-radius: 16px 16px 0 0;

  display: flex;
  justify-content: flex-end;
  flex-direction: row;
  padding: 10px 24px 10px 16px;
}

.wrapper__sort {
  cursor: pointer;

  padding: 4px;
  position: relative;
  .text {
    align-items: center;

    color: var(--color-gray-80, #5c6585);
    display: flex;
    font: var(--font-text-2-medium);
    letter-spacing: 0.13px;
  }
}

.listWrapper {
  background: var(--color-surface, #fff);
  border: 1px solid var(--stroke, #ebeff2);
  border-radius: 8px;
  box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
  max-width: max-content;
  overflow: -moz-scrollbars-none;
  -ms-overflow-style: none;
  overflow-y: scroll;
  padding: 8px 0;
  position: absolute;
  right: 0;
  top: 100%;
  transform: translateY(4px);

  width: max-content;
  z-index: 10;

  &::-webkit-scrollbar {
    width: 0;
  }

  .listInner {
    height: max-content;
  }
  .listItem {
    color: var(--color-gray-80, #5c6585);

    cursor: pointer;

    display: block;
    font: var(--font-text-2-normal);
    padding: 8px 16px;

    transition: var(--transition);
    &:hover {
      background: var(--color-gray-40, #f0f3f7);

      transition: var(--transition);
    }
    &.active {
      background: var(--color-gray-40, #f0f3f7);

      transition: var(--transition);
    }
  }
}

.list {
  &__item {
    border-bottom: 1px solid var(--stroke, #ebeff2);
    &:last-child {
      border-bottom-right-radius: 16px;
      border-bottom-left-radius: 16px;
    }
  }
}

.half {
  width: 50%;
}

.inputBorder {
  display: flex;
  border: 2px solid var(--color-gray-60) !important;
  padding: 10px;
  border-radius: 8px;
  color: var(--color-gray-80);
  align-items: center;
  &_active {
    border-color: var(--color-primary-90) !important;
    * {
      fill: var(--color-gray-90) !important;
    }
  }
}

.slot {
  margin-right: auto;
  color: var(--color-gray-80);
  
  &__input {
    position: absolute;
    bottom: 14px;
    left: 12px;
    color: var(--color-gray-80);
  }
}

.input {
  text-align: right;
  color: var(--color-gray-90);
  &::placeholder {
    color: var(--color-gray-80);
  }
}

.mt {
  margin-top: auto;
}