/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC } from 'react'
import classNamesBind from 'classnames/bind'
import { DeleteModal } from '@/shared/modals/delete-modal'
import { OrganizationLicense } from '@/shared/components'
import { BackButton, ButtonIcon, Loader, PageTitle } from '@/shared/ui'
import { OrganizationChildrenList } from '@/shared/components'
import { useOrganization } from './use-organization'
import { OrganizationProps } from './organization.d'
import styles from './organization.module.scss'
import { userAPI } from 'entities/employee'

const cx = classNamesBind.bind(styles)

export const Organization: FC<OrganizationProps.Props> = props => {
  const { className } = props

  const {
    organization_id,
    organization,
    isLoading,
    onStatisticsClick,
    onEditClick,
    onDeleteClick,
    onDeleteClose,
    deletedId,
    openDeleteModal,
    setOpenDeleteModal,
    from,
    isDeleteLoading,
  } = useOrganization()
  const { data: userInfo } = userAPI.useGetUserInfoQuery()
  const isShowBackButton = userInfo?.organization_id !== organization?.id && organization?.parent

  return (
    <div className={cx('wrapper', className)}>
      {isLoading && <Loader size='56' className='loader_centered' />}
      {!isLoading && organization && (
        <>
          {isShowBackButton && (
            <BackButton route={'/lk/admin/organization/' + organization?.parent?.id} />
          )}
          <div className={cx('title-wrapper')}>
            <PageTitle>{organization?.title}</PageTitle>

            {!organization.is_current && (
              <div className={cx('icons')}>
                <ButtonIcon
                  icon='editBold'
                  color='gray70'
                  size='28'
                  onClick={(e: any) => onEditClick(e, organization_id)}
                />
                <ButtonIcon
                  icon='statistics'
                  color='gray70'
                  size='28'
                  onClick={(e: any) => onStatisticsClick(e, organization_id)}
                />
                <ButtonIcon
                  icon='trashBold'
                  color='gray70'
                  size='28'
                  onClick={(e: any) => onDeleteClick(e, organization_id)}
                />
              </div>
            )}
          </div>
          <OrganizationLicense
            end_date={organization.license.end_date}
            start_date={organization.license.start_date}
            users_count={organization.license.users_count}
            users_limit={organization.license.users_limit}
          />
          <OrganizationChildrenList
            className={cx('list', { disabled: isDeleteLoading })}
            list={organization.children}
            from={from}
          />
        </>
      )}

      {deletedId && openDeleteModal && (
        <DeleteModal
          id={deletedId}
          onClose={onDeleteClose}
          title=''
          active={openDeleteModal}
          setActive={setOpenDeleteModal}
        />
      )}
    </div>
  )
}

export default Organization
