import { skipToken } from '@reduxjs/toolkit/query'
import { useState } from 'react'
import { organizationAPI } from 'entities/organization'
import { createSearchParams, useNavigate, useParams } from 'react-router-dom'
import { useUserOrganizationId } from '../../../entities/employee'

const { useGetOrganizationQuery, useDeleteOrganizationMutation } = organizationAPI

export const useOrganization = () => {
  const { organization_id = '' } = useParams()
  const userOrganizationId = useUserOrganizationId()

  const organizationID = organization_id ? organization_id : userOrganizationId

  const { data: organization, isLoading } = useGetOrganizationQuery(organizationID ?? skipToken)

  const from = organization?.id ? organization?.id : undefined

  const [deletedId, setDeletedId] = useState<UUID | null>(null)
  const navigate = useNavigate()

  const onStatisticsClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    navigate({
      pathname: `/lk/admin/organization/${id}/statistics`,
      search: createSearchParams({
        from: organizationID || '',
      }).toString(),
    })
  }

  const onEditClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    navigate({
      pathname: `/lk/admin/organization/${id}/edit`,
      search: createSearchParams({
        from: organizationID || '',
      }).toString(),
    })
  }

  const [deleteOrganization, { isLoading: isDeleteLoading }] = useDeleteOrganizationMutation()
  const [openDeleteModal, setOpenDeleteModal] = useState(false)

  const onDeleteClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.stopPropagation()

    setDeletedId(id)
    setOpenDeleteModal(true)
  }

  const onDeleteClose = async (id?: UUID | null) => {
    if (id) {
      await deleteOrganization(id).unwrap()

      navigate('/lk/admin/organization')
    }

    setDeletedId(null)
    setOpenDeleteModal(false)
  }

  return {
    organization_id,
    organization,
    isLoading,
    onStatisticsClick,
    onEditClick,
    onDeleteClick,
    onDeleteClose,
    deletedId,
    openDeleteModal,
    setOpenDeleteModal,
    from,
    isDeleteLoading,
  }
}
