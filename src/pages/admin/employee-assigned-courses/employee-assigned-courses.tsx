import { URLS } from '@/shared/configs/urls'
import { Breadcrumbs, PageTitle } from '@/shared/ui'
import { useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import styles from './employee-assigned-courses.module.scss'
import classNamesBind from 'classnames/bind'
import { employeeStatisticAPI } from 'entities/statistic'
import { AssignedCoursesByUser } from '@/shared/components/assigned-courses-by-user/assigned-courses-by-user'

const cx = classNamesBind.bind(styles)

export const EmployeeAssignedCourses = () => {
  const { employee_id = '' } = useParams()

  const { t } = useTranslation('pages__employee-assigned-courses')

  const { data: employeeInfo, isLoading: isEmployeeInfoLoading } =
    employeeStatisticAPI.useGetInfoQuery(employee_id)

  const employeeFullName = `${employeeInfo?.last_name} ${employeeInfo?.first_name} ${employeeInfo?.middle_name}`
  const employeeTitle = isEmployeeInfoLoading
    ? t('commons:loading_with_dots')
    : employeeFullName.trim()
      ? employeeFullName
      : employeeInfo?.email || ''

  const breadcrumbItems = useMemo(
    () => [
      {
        id: URLS.ADMIN_STAFF_EMPLOYEES_PAGE + `/${employee_id}`,
        text: employeeTitle,
        clickable: true,
        isLoading: isEmployeeInfoLoading,
      },
      {
        id: URLS.ADMIN_STAFF_EMPLOYEE_ASSIGNED_COURSES_PAGE,
        text: t('title'),
        clickable: false,
      },
    ],
    [t, employee_id, isEmployeeInfoLoading, employeeTitle],
  )

  return (
    <div className={cx('wrapper')}>
      <Breadcrumbs items={breadcrumbItems} className={cx('breadcrumb')} />
      <div className={cx('inner')}>
        <PageTitle className={cx('title')}>{employeeTitle}</PageTitle>
      </div>

      <div className={cx('content')}>
        <AssignedCoursesByUser employeeId={employee_id} />
      </div>
    </div>
  )
}
