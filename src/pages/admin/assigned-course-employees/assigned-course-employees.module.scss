@use "../../../shared/assets/styles/mixins/text";
@use "../../../shared/assets/styles/mixins/colors";

.wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.delete-button {
  margin-right: 24px;
  margin-left: auto;
  color: var(--button-color);
}

.employee {
  align-items: center;
  flex-direction: row !important;
  justify-content: space-between;
  margin-top: 20px;
}
.name {
  @include text.title(18px);
}

.right {
  align-items: center;
  display: flex;
  flex: 1 0;
}
.progress {
  @include text.main(20px);
  margin-right: 10px;
  width: 3em;
  &.color {
    @include colors.color using($color) {
      color: $color;
    }
  }
}

.header {
  align-items: center;
  display: flex;
  justify-content: space-between;
  .back {
    margin-bottom: 0;
  }
}

.downloadMore {
  margin-top: 30px;
}
