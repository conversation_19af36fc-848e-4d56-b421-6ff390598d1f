/* eslint-disable i18next/no-literal-string */
/* eslint-disable @typescript-eslint/no-explicit-any */
import classNamesBind from 'classnames/bind'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { DeleteConfirm } from '@/shared/modals/delete-confirm'
import { BackButton, Button, Card, Loader } from '@/shared/ui'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { List } from '@/shared/components'
import Skeleton from 'react-loading-skeleton'
import styles from './assigned-course-employees.module.scss'
import { userAPI } from 'entities/employee'
import { useTranslation } from 'react-i18next'
import { ICourseEmployees, IEmployee } from '@/shared/types/store/assigned-courses'
import { combineURLs } from '@/shared/helpers'
import { ERole } from '@/shared/types/enums'
import { withStopPropagation } from '@/shared/helpers/events'
import { ITargetData } from '@/entities/target'
import {
  useDeleteEmployeeMutation,
  useGetEmployeesQuery,
  useAddEmployeesMutation,
  useDeleteEmployeesMutation,
} from '@/store/services/assigned-course-service'
import {
  OrganizationTree,
  OrganizationTreeProvider,
  useOnSetFiltersFns,
  useResetModalSearch,
  useResetTreeCheck,
  useRestoreTreeByTargetData,
  useSetSearchVariants,
  useSetUsers,
  useUnmountOrganizationTree,
} from 'shared/modals/organization-tree'

const cx = classNamesBind.bind(styles)

export interface IAssignedCourseEmployeesProps {
  className?: string
}

const AssignedCourseEmployees: React.FC<IAssignedCourseEmployeesProps> = props => {
  const { className } = props

  const [courseID, setCourseID] = useState<UUID>()

  const { data: info } = userAPI.useGetUserInfoQuery()
  const onRestoreTree = useRestoreTreeByTargetData()
  const { onSetUsers } = useSetUsers()
  const { onSetFilters } = useOnSetFiltersFns()
  const { onSetSearchVariants } = useSetSearchVariants()
  const onResetTree = useResetTreeCheck()
  const { resetSearch } = useResetModalSearch()
  const lastModalTypeOpened = useRef<null | 'delete' | 'add'>(null)

  useEffect(() => {
    onSetSearchVariants(['employees'])

    return () => onSetSearchVariants(['employees', 'departments'])
  }, [])

  useUnmountOrganizationTree()
  useEffect(() => {
    if (courseID) return

    const url = window.location.href
    const posititon = window.location.href.lastIndexOf('/')
    const slicedUrl = url.slice(0, posititon)
    const slicedPosititon = slicedUrl.lastIndexOf('/')

    setCourseID(slicedUrl.slice(slicedPosititon + 1))
  }, [courseID])

  const { data, isLoading, isFetching } = useGetEmployeesQuery(courseID, {
    skip: !courseID,
  })
  const employees = data as ICourseEmployees
  const [deleteEmployee] = useDeleteEmployeeMutation()
  const [deleteEmployees, { isLoading: isLoadingDeleteEmployees }] = useDeleteEmployeesMutation()
  const [addEmployee] = useAddEmployeesMutation()

  const [openDeleteEmployeeId, setOpenDeleteEmployeeId] = useState<null | UUID>(null)
  const onDelete = useCallback(
    async (employee_id?: UUID) => {
      if (courseID && employee_id) {
        await deleteEmployee({
          courseID,
          targets: { target_users: [employee_id], target_departments: [] },
        }).unwrap()
      }
      setOpenDeleteEmployeeId(null)
    },
    [courseID, employees],
  )

  // const canLoadMore = !data || data.course.employees_count > data.course.employees.length

  const onEmployeeClick = (id: UUID) => {
    if (info?.role !== ERole.content_manager) {
      window.open(combineURLs(window.origin, `/lk/admin/staff/employees/${id}`), '_blank')?.focus()
    }
  }

  const renderEmployee = (e: IEmployee | undefined, index: number) => (
    <Card
      padding='normal'
      className={cx('employee')}
      key={e ? 'e__' + e.id : 'i__' + index}
      onClick={() => e && onEmployeeClick(e.id)}
    >
      <div className={cx('right')}>
        <div className={cx('progress')}>
          {e ? (e.overall_progress_percent?.toFixed(1) || 0) + '%' : <Skeleton width='3em' />}
        </div>
        <div className={cx('name')}>
          {e?.full_name ? e?.full_name : e?.email || <Skeleton width='50%' />}
        </div>
      </div>
      {e?.id && (
        <IconWrapper onClick={withStopPropagation(setOpenDeleteEmployeeId.bind(null, e?.id))}>
          <CloseBoldIcon />
        </IconWrapper>
      )}
    </Card>
  )

  const [openAddEmployees, setOpenAddEmployees] = useState(false)

  const onAddClose = async (data: ITargetData) => {
    if (courseID && (!!data.target_departments.length || !!data.target_users.length)) {
      await addEmployee({ courseID, targets: data })
    }
    setOpenAddEmployees(false)
  }

  const [openDeleteEmployees, setOpenDeleteEmployees] = useState(false)

  const onDeleteClose = async (data: ITargetData) => {
    if (courseID && (!!data.target_departments.length || !!data.target_users.length)) {
      await deleteEmployees({ courseID, targets: data }).unwrap()
    }
    setOpenDeleteEmployees(false)
  }

  const { t } = useTranslation()

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('header')}>
        <BackButton className={cx('back')} />
        <Button
          onClick={() => {
            onSetSearchVariants(['employees'])

            if (lastModalTypeOpened.current !== 'delete')
              onRestoreTree({
                target_departments: [],
                target_users: [],
                selectAll: false,
              })
            onSetUsers(
              employees?.users?.map(e => ({
                id: e.id,
                created_at: new Date().toString(),
                department: null,
                email: e.email,
                first_name: e.full_name ?? '',
                is_registered: true,
                last_name: e.last_name ?? '',
                middle_name: '',
                organization_id: '',
                position: null,
                picture: null,
                role: null,
                statistic: { phishing: 0, progress: 0, risk_level: 0 },
                tags: null,
                two_fa: false,
                updated_at: new Date().toString(),
              })),
            )
            lastModalTypeOpened.current = 'delete'
            setOpenDeleteEmployees(true)
          }}
          size='big'
          color='red'
          className={cx('delete-button')}
          disabled={isLoadingDeleteEmployees || isLoading || isFetching}
        >
          {t('modules.assigned_course_employees.remove_employees')}
        </Button>
        <Button
          onClick={() => {
            onSetUsers(undefined)
            if (lastModalTypeOpened.current !== 'add')
              onRestoreTree({
                target_departments: [],
                target_users: employees?.users.map(u => u.id),
                selectAll: false,
              })
            onRestoreTree({
              target_departments: [],
              target_users: employees?.users.map(u => u.id),
            })
            setOpenAddEmployees(true)
            onSetSearchVariants(['employees', 'departments'])

            lastModalTypeOpened.current = 'add'
          }}
          size='big'
          disabled={isLoadingDeleteEmployees || isLoading || isFetching}
        >
          {t('commons:add_employee')}
        </Button>
      </div>
      {isLoading && <Loader size='56' />}
      <List
        className={cx('employees')}
        data={employees?.users}
        itemHeight={70}
        defaultCount={100}
        // canLoadMore={canLoadMore}
        // loadMore={fetchMore}
        loading={isLoading}
        renderItem={renderEmployee}
        paginated
      />
      {openAddEmployees && employees?.users && (
        <OrganizationTreeProvider
          handleSelect={data => {
            onAddClose({
              target_departments: data?.department_ids,
              target_users: !data?.selectAll ? data?.users_ids : [],
              exclude_users_ids: data?.selectAll ? data?.users_ids : [],
            })
          }}
          open={openAddEmployees}
          setOpen={v => {
            setOpenAddEmployees(v)
            if (!v) {
              resetSearch()
            }
          }}
          requiredIds={employees?.users.map(u => u.id)}
        >
          <OrganizationTree />
        </OrganizationTreeProvider>
      )}
      {openDeleteEmployees && employees?.users && (
        <OrganizationTreeProvider
          handleSelect={data => {
            onDeleteClose({
              target_departments: data?.department_ids,
              target_users: data?.users_ids,
            })
            onResetTree()
          }}
          isDelete={true}
          open={openDeleteEmployees}
          setOpen={v => {
            setOpenDeleteEmployees(v)
            if (!v) {
              onSetFilters(undefined)
              resetSearch()
              onSetUsers(undefined)
            }
          }}
        >
          <OrganizationTree />
        </OrganizationTreeProvider>
      )}
      <DeleteConfirm id={openDeleteEmployeeId} onClose={onDelete as any} />
    </div>
  )
}

export default AssignedCourseEmployees
