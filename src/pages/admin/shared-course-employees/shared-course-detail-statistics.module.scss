.page {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
  min-height: 100%;
  margin-bottom: 64px;
}

.no-wrap {
  white-space: nowrap;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actions {
  background: white;
  padding: 16px 12px;
  bottom: 4px;
  border-radius: 12px;
  width: 100%;
  display: flex;
  align-items: center;
  gap: 24px;
  width: 100%;
  position: fixed;
  max-width: min(100vw - 255px - 30px, var(--page-container) - 64px);
  bottom: 40px;
  box-shadow:
    0 0 88px -4px rgba(24, 39, 75, 0.12),
    0 0 28px -6px rgba(24, 39, 75, 0.12);
  z-index: 100;
  &__counter {
    margin-right: auto;
    color: var(--color-primary);
    font: var(--font-text-2-normal);
  }

  &__elements {
    display: flex;
    gap: 24px;

    &__element {
      display: flex;
      gap: 8px;
      width: fit-content;
      color: var(--color-gray-80);
      font: var(--font-text-2-normal);
      &:hover {
        color: var(--color-primary);

        svg,
        path {
          fill: var(--color-primary) !important;
        }
      }
    }
  }
  &__delete {
    &__text {
      &:hover {
        color: var(--color-error);

        svg,
        path {
          fill: var(--color-error) !important;
        }
      }
      &__wrapper {
        padding: 24px !important;
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
    }
    &__footer {
      margin-top: 8px;
    }
  }

  &__cancel {
    &__wrapper {
      .description {
        white-space: pre-wrap;
      }
    }
    &__text {
      color: var(--color-warn);

      &:hover {
        color: var(--color-component-warn-hover) !important;

        svg,
        path {
          fill: var(--color-component-warn-hover) !important;
        }
      }
    }
  }
  &__padding {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

.select {
  padding: 0;
  border: none;
}

.select__list {
  margin-bottom: 34px;
  width: auto;
}

.content {
  display: flex;
  flex-direction: column;
}

.filters {
  &__wrapper {
    display: flex;
    gap: 8px;
    align-items: center;
    margin-left: auto;
    margin-bottom: 12px;
  }
  &__actions {
    font: var(--font-text-2-medium);
    color: var(--color-gray-80);
    text-transform: capitalize;
    display: flex;
    align-items: center;
    gap: 4px;
    width: auto;

    &__primary {
      color: var(--color-primary) !important;
    }
  }
}
.employees {
  &__wrapper {
    border-radius: 16px;
    background: var(--color-surface);
    border: 1px solid var(--color-gray-30);
    & > * {
      border-bottom: 1px solid var(--color-gray-30);
    }

    & > *:last-child {
      border-bottom: none;
    }
  }
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--color-surface);
    border-bottom: 1px solid var(--color-gray-30);
    border-radius: 16px 16px 0 0;

    padding: 10px 24px 10px 16px;
    &__checkbox__text {
      margin-left: 4px;
      color: var(--color-gray-70);
    }
  }

  .selectAll {
    align-items: center;

    color: var(--color-gray-70, #8e97af);

    display: flex;
    font: var(--font-caption-1-medium);
    gap: 12px;
    letter-spacing: 0.13px;
    text-transform: uppercase;

    span {
      cursor: pointer;
    }
  }
}

.empty {
  font: var(--font-caption-1-normal);
}
