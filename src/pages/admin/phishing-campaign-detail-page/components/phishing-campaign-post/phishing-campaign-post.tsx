import styles from './phishing-campaign-post.module.scss'
import classNamesBind from 'classnames/bind'
import { IPhishingCampaignInternal } from '@/entities/phishing/model/types'
import { EPhishingCampaignsStatus } from '@/shared/types/enums'
import { useTranslation } from 'react-i18next'
import { TTargetAutoCourseType } from '@/pages/admin/CreateCampaign/config'
import { useCallback, useMemo } from 'react'
import { PhishingCampaignCard } from '@/shared/components'
import { useNavigate } from 'react-router-dom'
import { phishingQueries } from '@/entities/phishing'

const cx = classNamesBind.bind(styles)

type Props = {
  campaignInternal?: IPhishingCampaignInternal
  campaignStatus?: EPhishingCampaignsStatus
  stopDelay: boolean
}

const TRANSLATION_FILE = 'pages__phishing-campaigns'

export const PhishingCampaignPost = ({ campaignInternal, campaignStatus, stopDelay }: Props) => {
  const { t } = useTranslation(TRANSLATION_FILE)
  const navigate = useNavigate()
  const TARGET_LIST = useMemo<{ id: TTargetAutoCourseType; title: string }[]>(
    () => [
      { id: 'opened', title: t('commons:letter_opened') },
      { id: 'clicked', title: t('commons:followed_link') },
      { id: 'entered_data', title: t('commons:entered_data') },
      { id: 'opened_attachment', title: t('commons:opened_attachment') },
    ],
    [t],
  )

  const renderMessageTargets = useCallback(() => {
    const renderedTargets = TARGET_LIST.filter(target =>
      campaignInternal?.post_campaign?.targets?.includes(target.id),
    )

    return renderedTargets.map(target => ` ${target.title}`)
  }, [TARGET_LIST, campaignInternal?.post_campaign?.targets])

  const { data: campaignData } = phishingQueries.useGetPhishingCampaignsByIdQuery(
    { id: campaignInternal?.post_campaign?.id || '', params: { by_tag: false } },
    {
      skip:
        !campaignInternal?.post_campaign?.id ||
        campaignStatus !== EPhishingCampaignsStatus.completed ||
        stopDelay,
    },
  )
  const onCardClick = (id: UUID) => {
    if (id) navigate('/lk/admin/phishing/campaigns/' + id)
  }

  if (!campaignInternal?.post_campaign || !campaignStatus) return null

  return (
    <div className={cx('wrapper')}>
      <div className={cx('title')}>
        {campaignStatus === EPhishingCampaignsStatus.completed ? t('post_completed') : t('post')}
        {` ` + renderMessageTargets()}:
      </div>
      {campaignData && campaignStatus === EPhishingCampaignsStatus.completed ? (
        <PhishingCampaignCard className={cx('card')} data={campaignData} onClick={onCardClick} />
      ) : (
        <>{campaignInternal?.post_campaign?.name}</>
      )}
    </div>
  )
}
