import { getPhishingRedirectDetailUrl } from '@/shared/configs/urls'
import { useNavigate } from 'react-router-dom'
import styles from './phishing-campaign-redirect-page-card.module.scss'
import classNamesBind from 'classnames/bind'
import { IPhishingCampaignInternal } from '@/entities/phishing/model/types'
import { EPhishingCampaignsStatus } from '@/shared/types/enums'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

type Props = {
  campaignInternal?: IPhishingCampaignInternal
  campaignStatus?: EPhishingCampaignsStatus
}

const TRANSLATION_FILE = 'pages__phishing-campaigns'

export const PhishingCampaignRedirectPageCard = ({ campaignInternal, campaignStatus }: Props) => {
  const navigate = useNavigate()
  const { t } = useTranslation(TRANSLATION_FILE)

  if (!campaignInternal?.redirect_page && !campaignStatus) return null

  const onClick = () => {
    if (campaignInternal?.redirect_page?.id) {
      navigate(getPhishingRedirectDetailUrl(campaignInternal.redirect_page?.id))
    }
  }

  return (
    <div className={cx('wrapper')}>
      {campaignInternal?.redirect_page && !campaignInternal.enable_redirect && (
        <>
          <div className={cx('title')}>
            {campaignStatus === EPhishingCampaignsStatus.completed
              ? t('redirect_page_completed')
              : t('redirect_page')}
          </div>
          <div className={cx('redirect__card')} onClick={onClick}>
            <p className={cx('redirect__card_name')}>{campaignInternal.redirect_page?.name}</p>
          </div>
        </>
      )}
    </div>
  )
}
