@use '../../../../../shared/assets/styles/mixins/text';
@use '../../../../../shared/assets/styles/mixins/media';

.redirect {
  &__page {
    align-items: center;
    display: flex;
    gap: 8px;
    justify-content: flex-start;

    &_domain {
      color: var(--color-gray-70);
      font: var(--font-text-2-normal);
    }

    &_input {
      flex-grow: 1;
      width: 100%;
    }
  }

  &__card {
    align-items: center;

    background-color: var(--white);
    border: 1px solid var(--gray-gray-30);

    border-radius: 12px;

    cursor: pointer;

    display: grid;
    gap: 12px;
    grid-template-columns: 1fr auto;
    height: 48px;

    padding: 10px 12px;

    transition: var(--transition);
    width: 100%;

    @include media.mqL() {
      padding: 8px;
    }

    &_name {
      @include text.max-lines(1);
      color: var(--color-gray-100);
      font: var(--font-text-2-normal);
      width: 100%;
    }
  }
}

.title {
  color: var(--color-gray-90);
  font: var(--font-text-1-medium);
  margin-bottom: 8px;
}
