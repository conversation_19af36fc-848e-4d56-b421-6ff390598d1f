import styles from './phishing-campaign-notification.module.scss'
import classNamesBind from 'classnames/bind'
import { IPhishingCampaignInternal } from '@/entities/phishing/model/types'
import { EPhishingCampaignsStatus } from '@/shared/types/enums'
import { useTranslation } from 'react-i18next'
import { TTargetAutoCourseType } from '@/pages/admin/CreateCampaign/config'
import { useCallback, useMemo } from 'react'
import { Input, Textarea } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

type Props = {
  campaignInternal?: IPhishingCampaignInternal
  campaignStatus?: EPhishingCampaignsStatus
}

const TRANSLATION_FILE = 'pages__phishing-campaigns'

export const PhishingCampaignNotification = ({ campaignInternal, campaignStatus }: Props) => {
  const { t } = useTranslation(TRANSLATION_FILE)

  const TARGET_LIST = useMemo<{ id: TTargetAutoCourseType; title: string }[]>(
    () => [
      { id: 'opened', title: t('commons:letter_opened') },
      { id: 'clicked', title: t('commons:followed_link') },
      { id: 'entered_data', title: t('commons:entered_data') },
      { id: 'opened_attachment', title: t('commons:opened_attachment') },
    ],
    [t],
  )

  const renderMessageTargets = useCallback(() => {
    const renderedTargets = TARGET_LIST.filter(target =>
      campaignInternal?.notification?.targets?.includes(target.id),
    )

    return renderedTargets.map(
      (target, index) => `${index + 1 < renderedTargets.length ? ' ' : ''}${target.title}`,
    )
  }, [TARGET_LIST, campaignInternal?.notification?.targets])

  if (!campaignInternal?.notification || !campaignStatus) return null

  return (
    <div className={cx('wrapper')}>
      <div className={cx('title')}>
        {campaignStatus === EPhishingCampaignsStatus.completed
          ? t('notification_completed')
          : t('notification')}
        {` ` + renderMessageTargets()}:
      </div>
      <Input
        fullWidth
        label={t('commons:mail_theme')}
        placeholder={t('commons:mail_theme')}
        classNameWrapper={cx('theme')}
        value={campaignInternal.notification?.subject}
        disabled
      />
      <Textarea
        fullWidth
        label={t('email_text')}
        placeholder={t('send_msg_after_campaign_text_placeholder')}
        classNameWrapper={cx('text')}
        value={campaignInternal.notification?.text}
        disabled
      />
    </div>
  )
}
