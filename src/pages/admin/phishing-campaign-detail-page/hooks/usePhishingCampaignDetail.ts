import { useMemo, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '@/entities/phishing'

export const usePhishingCampaignDetail = () => {
  const { campaign_id = '' } = useParams()
  const { t } = useTranslation()

  const [stopDelay, setStopDelay] = useState<boolean>(false)

  const { data: campaignData } = phishingQueries.useGetPhishingCampaignsByIdQuery(
    { id: campaign_id, params: { by_tag: false } },
    {
      skip: !campaign_id,
    },
  )

  const { data: templatesData, isLoading: isTemplatesLoading } =
    phishingQueries.useGetPhishingCampaignsTemplatesByIdQuery(
      {
        id: campaign_id,
      },
      { skip: !campaign_id },
    )

  const { data: campaignInternal } = phishingQueries.useGetPhishingCampaignInternalQuery(
    { id: campaign_id },
    {
      skip: !campaign_id,
    },
  )

  const { data: campaignCourse, refetch: refetchCourse } =
    phishingQueries.useGetPhishingCampaignCourseByIdQuery(
      { id: campaign_id, params: { by_tag: false } },
      {
        skip: !campaign_id,
      },
    )

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/campaigns',
        text: t('commons:campaigns'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/campaigns/' + campaignData?.id,
        text: campaignData?.name || t('current_newsletter'),
        clickable: true,
        isLoading: !campaignData,
      },
    ],
    [campaignData, t],
  )

  return {
    BREADCRUMBS,
    campaignData,
    campaignInternal,
    campaignCourse,
    stopDelay,
    templatesData,
    isTemplatesLoading,
    setStopDelay,
    refetchCourse,
  }
}
