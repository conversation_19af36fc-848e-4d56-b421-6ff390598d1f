import styles from './PhishingCampaignTemplateCards.module.scss'
import classNamesBind from 'classnames/bind'
import Skeleton from 'react-loading-skeleton'
import { PhishingCampaignTemplateCard } from '@/shared/components'
import { useNavigate, useParams } from 'react-router-dom'
import { IPhishingCampaignTemplate } from '@/entities/phishing'
import { IPhishingCampaignInternal } from '@/entities/phishing/model/types'

const cx = classNamesBind.bind(styles)

type Props = {
  data?: IPhishingCampaignTemplate[]
  campaignInternal?: IPhishingCampaignInternal
  isLoading: boolean
}

export const PhishingCampaignTemplateCards = ({ data, campaignInternal, isLoading }: Props) => {
  const { campaign_id = '' } = useParams()

  const navigate = useNavigate()

  const onClick = (v: string) =>
    navigate(`/lk/admin/phishing/campaigns/${campaign_id}/${v}/statistics`)

  return (
    <div className={cx('list')}>
      {!isLoading &&
        data &&
        data.map(item => (
          <PhishingCampaignTemplateCard
            onClick={() => onClick(item.id)}
            key={item.id}
            showRedirect={campaignInternal?.enable_redirect}
            data={item}
          />
        ))}
      {isLoading &&
        [1, 2, 3, 4].map((_, index) => <Skeleton key={index} width={'100%'} height={'68px'} />)}
    </div>
  )
}
