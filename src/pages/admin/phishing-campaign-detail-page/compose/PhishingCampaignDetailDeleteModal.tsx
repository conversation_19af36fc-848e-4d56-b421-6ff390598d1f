import { useState } from 'react'
import { useNotification } from '@/shared/contexts/notifications'
import { DeleteModal } from '@/shared/modals/delete-modal'
import { ButtonIcon, Tooltip } from '@/shared/ui'

import styles from '../phishing-campaign-detail-page.module.scss'
import classNamesBind from 'classnames/bind'
import { useNavigate, useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { phishingMutations } from '@/entities/phishing'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignDetailDeleteModal = () => {
  const { campaign_id = '' } = useParams()
  const [open, setOpen] = useState(false)

  const [deleteTrigger] = phishingMutations.useDeletePhishingCampaignsByIdMutation()
  const { handleErrorResponse } = useNotification()
  const { t } = useTranslation()
  const navigate = useNavigate()

  const onDelete = async (id?: string | null | undefined) => {
    if (!id) return

    try {
      await deleteTrigger(id)

      navigate('/lk/admin/phishing/campaigns')
    } catch (error) {
      handleErrorResponse(error)
    }
  }

  return (
    <>
      <Tooltip
        className={cx('title__trash__wrapper')}
        content={<>{t('commons:delete_phishing')}</>}
      >
        <ButtonIcon
          onClick={() => setOpen(true)}
          className={cx('delete-icon')}
          iconSize='24'
          icon='trashBold'
          size='32'
        />
      </Tooltip>
      <DeleteModal
        text={t('dialogs.phishing_campaign_detail_delete.title')}
        id={campaign_id}
        onClose={onDelete}
        active={open}
        setActive={setOpen}
      />
    </>
  )
}
