@use "../../../shared/assets/styles/mixins/icons";

.wrapper {
  display: flex;
  flex-direction: column;
  // padding: 32px 0;
  gap: 24px;
}

.tooltip {
  display: flex;
  text-align: center;
}

.title {
  color: var(--color-gray-90, #343b54);
  font: var(--font-title-2-medium);

  &__wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  &__box {
    align-items: center;
    display: flex;
    gap: 12px;
  }
  &__trash {
    &__wrapper {
      margin-left: auto;
    }
  }
}

.actions {
  &__wrapper {
    align-items: center;
    display: flex;
    gap: 8px;
    margin-left: auto;
  }
}

.templates__title {
  color: var(--color-gray-90);
  font: var(--font-title-4-medium);
}

.delete-icon:hover {
  margin-left: auto;

  transition: all 0.2s ease;

  &:hover {
    @include icons.color(red);
    background-color: rgba(241, 70, 90, 0.15) !important;
  }
}

.stop__icon {
  &:hover {
    @include icons.color(red);
    background-color: rgba(241, 70, 90, 0.15) !important;
  }
}
