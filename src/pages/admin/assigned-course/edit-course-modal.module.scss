.form {
  width: 560px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding: 0 3px;


  max-height: calc(100dvh - 64px);
  overflow-y: auto;

   &::-webkit-scrollbar {
    width: 3px;
  }

  &::-webkit-scrollbar-track {
    background: none; 
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-gray-60); 
    border-radius: 10px; 
    cursor: grab;

    &:hover {
      background: var(--color-gray-80); 
    }
  }
}

.assigned{
  color: var(--color-primary);
  font: var(--font-text-2-normal);
}

.actions{
  display: grid;
  grid-template-columns: repeat(2,1fr);
  gap: 16px;
  margin-top: 8px;
}

.list{
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload{
  max-width: 208px;
}


.picker{
  &__wrapper{
    position: relative;
  }
  position: absolute;
  bottom: 50px;
  right: 61%;
  box-shadow: none;
  border: var(--color-gray-50) 1px solid;
}

.close {
  right: 24px !important;
}