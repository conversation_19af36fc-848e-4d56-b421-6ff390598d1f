import { FC, ReactNode } from 'react'
import ChevroneBoldIcon from '@/shared/ui/Icon/icons/components/ChevroneBoldIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'

import classNamesBind from 'classnames/bind'
import styles from './chapter.module.scss'

const cx = classNamesBind.bind(styles)

type ChapterElementProps = {
  text: ReactNode
  renderTheory?: () => ReactNode
  renderTesting?: () => ReactNode
  isOpen?: boolean
  className?: string
}

export const ChapterElement: FC<ChapterElementProps> = ({
  className,
  text,
  renderTesting,
  renderTheory,
  isOpen,
}) => {
  return (
    <div className={cx('wrapper', className)}>
      <p>{text}</p>
      <div className={cx('testing__wrapper')}>
        {(renderTesting || renderTheory) && (
          <div className={cx('chapter__testing', 'testing')}>
            {renderTheory?.()}
            {renderTesting?.()}
          </div>
        )}
        <IconWrapper size='16' direction={isOpen ? 'left' : 'right'} color='gray70'>
          <ChevroneBoldIcon />
        </IconWrapper>
      </div>
    </div>
  )
}

export const ChapterBodyElement: FC<ChapterElementProps> = ({
  className,
  text,
  renderTesting,
  renderTheory,
}) => {
  return (
    <div className={cx('wrapper', className)}>
      <p>{text}</p>
      <div className={cx('testing__wrapper', 'testing')}>
        {renderTheory?.()}
        {renderTesting?.()}
      </div>
    </div>
  )
}
