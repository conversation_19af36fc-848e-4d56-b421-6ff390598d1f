import { createSlice, PayloadAction, WithSlice } from '@reduxjs/toolkit'
import { Filters } from './types'
import { rootReducer } from '@/store/reducer'

type InitialState = {
  filters: Filters
  notifyEmployeesModalOpen: boolean
  editCourseModalOpen: boolean
  reportModalOpen: boolean
  selectedCountOfPeople?: number
}

const initialState: InitialState = {
  filters: {
    education: undefined,
  },
  notifyEmployeesModalOpen: false,
  editCourseModalOpen: false,
  reportModalOpen: false,
}

export const detailStatisticsSlice = createSlice({
  name: 'assignedDetailStatistics',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Filters>) => {
      state.filters = action.payload
    },
    setSelectedCountOfPeople: (state, action: PayloadAction<number>) => {
      state.selectedCountOfPeople = action.payload
    },
    setNotifyEmployeesModalOpen: (state, action: PayloadAction<boolean>) => {
      state.notifyEmployeesModalOpen = action.payload
    },
    setEditCourseModalOpen: (state, action: PayloadAction<boolean>) => {
      state.editCourseModalOpen = action.payload
    },
    setReportModalOpen: (state, action: PayloadAction<boolean>) => {
      state.reportModalOpen = action.payload
    },
  },
  selectors: {
    selectFilters: state => state.filters,
    selectNofifyEmployeesModalOpen: state => state.notifyEmployeesModalOpen,
    selectEditCourseModalOpen: state => state.editCourseModalOpen,
    selectReportModalOpen: state => state.reportModalOpen,
    selectSelectedCountOfPeople: state => state.selectedCountOfPeople,
  },
})

declare module '@/store/reducer' {
  export interface LazyLoadedSlices extends WithSlice<typeof detailStatisticsSlice> {}
}

const injectedDetailStatisticsSlice = detailStatisticsSlice.injectInto(rootReducer)

export const {
  setFilters,
  setNotifyEmployeesModalOpen,
  setEditCourseModalOpen,
  setReportModalOpen,
  setSelectedCountOfPeople,
} = injectedDetailStatisticsSlice.actions
export const {
  selectFilters,
  selectNofifyEmployeesModalOpen,
  selectEditCourseModalOpen,
  selectReportModalOpen,
  selectSelectedCountOfPeople,
} = injectedDetailStatisticsSlice.selectors
