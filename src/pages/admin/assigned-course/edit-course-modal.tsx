import { FC } from 'react'
import { Modal } from '@/shared/components'
import { Button, Checkbox, Input, Textarea } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './edit-course-modal.module.scss'
import { ContentCourse } from './types'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import ImageUpload from '@/shared/components/image-upload'
import { SelectDateCardWithInput } from '@/shared/components/SelectDateCard/select-date-card-with-input'
import { startOfDay } from '@/shared/helpers/date'
import { useParams } from 'react-router-dom'
import { contentApi } from './content-api'

const cx = classNamesBind.bind(styles)

type EditCourseModalProps = {
  open: boolean
  setOpen: (v: boolean) => void
  initialValues?: ContentCourse
  isSharedCourse?: boolean
  onCancel?: (v: ContentCourse) => void
  onSubmit?: (v: ContentCourse) => void
}

export const EditCourseModal: FC<EditCourseModalProps> = ({
  onCancel,
  onSubmit,
  open,
  isSharedCourse,
  setOpen,
  initialValues,
}) => {
  const { t } = useTranslation('pages__learning__assigned-course-detail')
  const { course_id = '' } = useParams()
  const [updatePicture] = contentApi.useUpdateAssignedCoursePictureMutation()

  const form = useForm<ContentCourse>({
    defaultValues: initialValues,
  })
  const onFormSubmit: SubmitHandler<ContentCourse> = data => {
    if (data.picture && data.picture instanceof File) {
      updatePicture({
        id: course_id,
        image: data.picture,
      })
    }
    onSubmit?.(data)

    setOpen(false)
  }

  const onFormCancel = () => {
    const formValues = form.watch()
    onCancel?.(formValues)
    form.reset()
    setOpen(false)
  }

  return (
    <Modal closeClassName={cx('close')} active={open} setActive={v => setOpen(!!v)}>
      <form onSubmit={form.handleSubmit(onFormSubmit)} className={cx('form')}>
        <h4>{t('edit.title')}</h4>
        <Controller
          name='title'
          control={form.control}
          render={({ field }) => (
            <Input
              value={field.value}
              onChange={field.onChange}
              label={t('edit.title_label')}
              fullWidth
            />
          )}
        />
        <Controller
          name='description'
          control={form.control}
          render={({ field }) => (
            <Textarea value={field.value} onChange={field.onChange} label={t('edit.description')} />
          )}
        />
        <div className={cx('upload')}>
          <Controller
            name='picture'
            control={form.control}
            render={({ field }) => {
              const pictureValue = form.watch('picture')
              return (
                <ImageUpload
                  defaultPreview={typeof pictureValue === 'string' ? pictureValue : undefined}
                  onSelect={file => field.onChange(file)}
                  // Temporary hidden delete option
                  hasDelete={false}
                />
              )
            }}
          />
        </div>
        <ul className={cx('list')}>
          <Controller
            name='need_assigned_message'
            control={form.control}
            render={({ field }) => (
              <li>
                <Checkbox
                  onChange={field.onChange}
                  customChecked={field.value}
                  label={<span>{t('edit.assigned_notification')}</span>}
                />
              </li>
            )}
          />
          {!isSharedCourse && (
            <Controller
              name='need_notify_message'
              control={form.control}
              render={({ field }) => (
                <li>
                  <Checkbox
                    onChange={field.onChange}
                    customChecked={field.value}
                    label={<span>{t('edit.assigned_lagging')}</span>}
                  />
                </li>
              )}
            />
          )}
        </ul>
        {form.watch('employees_count') > 0 && (
          <span className={cx('assigned')}>
            {t('commons:employees_choosed', { count: form.watch('employees_count') })}
          </span>
        )}
        <Controller
          name='end_date'
          control={form.control}
          render={({ field }) => (
            <SelectDateCardWithInput
              withoutTime
              min={startOfDay(new Date())}
              className={cx('picker__wrapper')}
              text={t('edit.end_date')}
              onChange={field.onChange}
              selected={field.value ? new Date(field.value) : null}
              label={t('edit.end_date')}
              datePickerClassName={cx('picker')}
            />
          )}
        />
        <div className={cx('actions')}>
          <Button size='big' onClick={onFormCancel} type='button' color='gray'>
            {t('edit.actions.cancel')}
          </Button>
          <Button size='big' type='submit'>
            {t('edit.actions.save')}
          </Button>
        </div>
      </form>
    </Modal>
  )
}
