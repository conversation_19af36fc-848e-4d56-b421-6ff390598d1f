import { ICourseStatus, INewCourseStatus } from '@/shared/types/enums'

export function getProgress(
  leftDays: number,
  totalDays: number,
  status?: ICourseStatus | INewCourseStatus,
) {
  switch (status) {
    case undefined: {
      return 0
    }
    case ICourseStatus.Complete: {
      return 100
    }
    case INewCourseStatus.Complete: {
      return 100
    }
    case ICourseStatus.Planned: {
      return 0
    }
    case INewCourseStatus.Planned: {
      return 0
    }
    default: {
      return (1 - leftDays / totalDays) * 100
    }
  }
}

interface ICourseDates {
  startDate: Date
  endDate: Date
}

export function getProgressInDates(courseDates: ICourseDates, currentDate: Date) {
  const diffInMilliseconds = currentDate.getTime() - courseDates.startDate.getTime()
  const totalDurationInMilliseconds =
    courseDates.endDate.getTime() - courseDates.startDate.getTime()

  if (currentDate < courseDates.startDate) {
    return 0
  }

  if (currentDate > courseDates.endDate) {
    return 100
  }

  const progressPercentage = (diffInMilliseconds / totalDurationInMilliseconds) * 100
  return Math.round(progressPercentage)
}

export const isSourceByTag = (date?: Date | string) =>
  !!date && new Date(date).getFullYear() >= 2100

export function formatDate(dateString: Date | string): string {
  const date = new Date(dateString)

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')

  return `${year}-${month}-${day}`
}