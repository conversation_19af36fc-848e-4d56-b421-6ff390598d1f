.page {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.chart {
  height: 100% !important;
  & > {
    height: 100% !important;
  }
}

.title {
  margin-bottom: 8px;
  color: var(--color-gray-90);
  font: var(--font-title-2-medium);

  &__small {
    color: var(--color-gray-90);
    font: var(--font-title-4-medium);
  }
}

.breadcrumbs {
  margin-bottom: 16px;
}

.info {
  &__section {
    display: flex;
    flex-direction: column;

    &__actions {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      &__wrapper {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }

      &__end {
        cursor: pointer;
      }
    }
  }
  &__block {
    display: flex;
    gap: 24px;
    width: 100%;
    padding: 12px;

    background: var(--white);
    border-radius: 12px;
    border: 1px solid var(--color-gray-30);
    color: var(--color-gray-70);
    font: var(--font-text-1-medium);

    &__element {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &__employees {
      display: flex;
      align-items: center;
      gap: 24px;
      margin-left: auto;

      &__button {
        display: flex;
        gap: 4px;
        align-items: center;
        color: var(--color-primary);

        svg {
          transition: all 0.2s ease;
        }

        &:hover {
          text-decoration: underline;

          svg {
            transform: translateX(3px);
          }
        }

        &_disabled {
          color: var(--color-gray-50);

          &:hover {
            text-decoration: none;
          }
        }
      }
    }
  }
}

.export {
  &:hover {
    color: var(--color-primary) !important;

    path {
      fill: var(--color-primary) !important;
    }
  }
}

.education__section {
  display: flex;
  gap: 12px;
  flex-direction: column;
  height: 100%;
  padding: 16px 24px 24px 24px;

  background: var(--white);
  border-radius: 12px;

  &__rows {
    cursor: pointer;

    &_success {
      background: var(--color-statistics-good) !important;
    }
    &_progress {
      background: var(--color-statistics-complementary) !important;
    }
    &_not-started {
      background: var(--color-gray-70) !important;
    }
  }
}

.statistics {
  &__section {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__graphics {
    &__wrapper {
      display: grid;
      gap: 20px;
      grid-template-columns: repeat(2, 1fr);
    }

    &__first {
      display: flex;
      gap: 20px;
      flex-direction: column;
    }
  }

  &__title__wrapper {
    display: flex;
    justify-content: space-between;
  }

  &__export {
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--color-gray-80);
    font: var(--font-text-2-demibold);
  }
}

.chapters__section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.accordion {
  background: var(--white);
  border-radius: 12px;
  border: 1px solid var(--color-gray-30);

  & > div:last-child {
    .accordion__body {
      border-bottom-right-radius: 12px;
      border-bottom-left-radius: 12px;

      &:last-child {
        border-bottom-right-radius: 12px;
        border-bottom-left-radius: 12px;
      }
    }
  }

  .completed {
    &__wrapper {
      display: flex;
      gap: 4px;
      align-items: center;
      color: var(--color-primary);
    }
  }

  &__header {
    padding: 12px 20px;
    cursor: pointer;
  }

  &__body {
    display: flex;
    flex-direction: column;
    width: 100%;
    background: var(--color-gray-20);

    &__item {
      width: 100%;
      padding: 12px 20px 12px 40px;
      border: 1px solid var(--color-gray-30);

      &:not(:first-child) {
        border-top: none;
      }
    }
  }
}

.actions {
  &__delete {
    &__wrapper {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 24px !important;
    }
    &__footer {
      margin-top: 8px;
    }
  }
  &__padding {
    padding-top: 12px;
    padding-bottom: 12px;
  }
}

.send {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: auto;
  color: var(--color-primary);

  &__wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  &__text {
    text: var(--font-text-2-normal);
    line-height: 18px;
  }
}

.download-certificates {
  &-wrapper {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  &-tooltip {
    max-width: 300px;
    white-space: normal;
    top: calc(100% + 6px) !important;
    right: 0 !important;
    left: auto !important;
    transform: none !important;
  }
}

.tooltip-icon {
  cursor: help;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--color-gray-20);
  transition: background 0.2s ease;

  &:hover {
    background: var(--color-gray-30);
  }
}
