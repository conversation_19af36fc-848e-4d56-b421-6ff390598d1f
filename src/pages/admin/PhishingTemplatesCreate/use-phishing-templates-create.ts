/* eslint-disable @typescript-eslint/no-explicit-any */
import { SubmitHand<PERSON>, useForm } from 'react-hook-form'
import {
  type IPhishingCreateTemplatesRequest,
  phishingQueries,
  phishingMutations,
} from '@/entities/phishing'
import { useEffect, useMemo, useState } from 'react'
import { useNotification } from '@/shared/contexts/notifications'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { validateCombinedUrl, validateDomainLevel } from '../phishing-templates-edit/helper'

const VALUES = {
  url: '',
  category: null,
  name: '',
  original_url: null,
}

type IListItem = {
  id: any
  title: string
  hasPriority?: boolean
  isTitle?: boolean
}

const DOT_SIGN = '.'

export interface PhishingTemplatesCreateForm extends IPhishingCreateTemplatesRequest {
  domain?: string
}

export const usePhishingTemplatesCreate = () => {
  const { t } = useTranslation()

  const DEFAULT_CATEGORIES = useMemo(
    () => [
      {
        id: null,
        title: t('commons:no_category'),
      },
    ],
    [t],
  )

  const form = useForm<PhishingTemplatesCreateForm>({
    values: VALUES,
    mode: 'all',
    resolver: values => {
      const errors: Record<string, any> = {}

      if (!validateDomainLevel(values.url)) {
        errors.url = { type: 'domainLevel', message: t('commons:domain_level_error') }
      }

      if (!validateCombinedUrl(values)) {
        errors.url = {
          type: 'combinedLevel',
          message: t('commons:domain_level_error'),
        }
      }

      return { values, errors }
    },
  })

  const [createTemplateTrigger, { isError, isLoading }] =
    phishingMutations.useCreateTemplateMutation()
  const { data: categoriesData, isLoading: isCategoriesLoading } =
    phishingQueries.useGetCategoriesQuery()
  const { data: domainsData, isLoading: isDomainsLoading } = phishingQueries.useGetDomainsQuery()
  const { handleResponse } = useNotification()

  const [domainsList, setDomainsList] = useState<string[]>([])
  const [categoriesList, setCategoriesList] = useState<IListItem[]>(() => [...DEFAULT_CATEGORIES])

  const navigate = useNavigate()

  const onSubmit: SubmitHandler<PhishingTemplatesCreateForm> = async data => {
    const { domain, ...rest } = data

    const prepareData: IPhishingCreateTemplatesRequest = {
      ...rest,
      url: rest.url ? rest.url + DOT_SIGN + (domain || '') : domain || '',
    }

    const templateData = await createTemplateTrigger(prepareData).unwrap()

    if (!templateData.id) {
      handleResponse(`${t('commons:error_creating_template')}...`)
      return
    }

    navigate(`/lk/admin/phishing/templates/${templateData?.id}`)
  }

  useEffect(() => {
    if (!categoriesData || !categoriesData.data) {
      setCategoriesList([...DEFAULT_CATEGORIES])
      return
    }

    setCategoriesList([
      ...DEFAULT_CATEGORIES,
      ...categoriesData.data.map(c => ({ id: c.id, title: c.name })),
    ])
  }, [categoriesData])

  useEffect(() => {
    if (!domainsData || !domainsData.domains) {
      setDomainsList([])
      return
    }

    setDomainsList(domainsData.domains)
  }, [domainsData])

  return {
    form,
    onSubmit,
    isError,
    domainsList,
    categoriesList,
    isCategoriesLoading,
    isDomainsLoading,
    isLoading,
    errors: form.formState.errors,
  }
}
