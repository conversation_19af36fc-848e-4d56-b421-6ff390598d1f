import { FC, useMemo } from 'react'
import styles from './phishing-templates-create.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingTemplatesCreateProps } from './phishing-templates-create.d'
import { Breadcrumbs, Button, BreadcrumbItem, Input, Select } from '@/shared/ui'
import { usePhishingTemplatesCreate } from './use-phishing-templates-create'
import { doesNotEndWith } from './helper'
import { useTranslation } from 'react-i18next'
import { validateDomainLevel } from '../phishing-templates-edit/helper'

const cx = classNamesBind.bind(styles)

export const PhishingTemplatesCreate: FC<PhishingTemplatesCreateProps.Props> = () => {
  const { t } = useTranslation()

  const BREADCRUMBS: BreadcrumbItem[] = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/templates',
        text: t('commons:templates'),
        clickable: true,
      },
      {
        id: '2',
        text: t('commons:creating_template'),
      },
    ],
    [t],
  )

  const {
    categoriesList,
    domainsList,
    form,
    onSubmit,
    isError,
    isLoading,
    isCategoriesLoading,
    isDomainsLoading,
    errors,
  } = usePhishingTemplatesCreate()

  return (
    <div className={cx('container')}>
      <Breadcrumbs items={BREADCRUMBS} />
      <form onSubmit={form.handleSubmit(onSubmit)} className={cx('form', 'container')}>
        <Input
          fullWidth
          disabled={isLoading}
          placeholder={t('commons:enter_name_template')}
          label={t('commons:template_name')}
          register={form.register('name', { pattern: /\S+/, minLength: 1 })}
          required
          title={t('commons:enter_name_template')}
          error={errors.name ? errors.name.message : ''}
        />
        <div>
          <h2 className={cx('form__field__label')}>{t('commons:phishing_link')}</h2>
          <div className={cx('form__field')}>
            {/* eslint-disable-next-line i18next/no-literal-string */}
            <p className={cx('form__field__domain')}>https://</p>
            <Input
              fullWidth
              classNameWrapper={cx('form__field_width')}
              disabled={isLoading}
              placeholder={`${t('commons:enter_link')}...`}
              register={form.register('url', {
                pattern: /^(?![\s\S]*\.(com|ru)$)[\s\S]*$/i,
                validate: {
                  domainLevel: value => validateDomainLevel(value),
                },
              })}
              title={t('commons:correct_link_work')}
            />
            <Select
              searchable={true}
              additionalStr={form.watch('url') ? '.' : ''}
              className={cx('select__domains')}
              list={domainsList.map(d => ({ id: d, title: d }))}
              placeholder=''
              loading={isDomainsLoading}
              customValue={form.watch('domain')}
              value={form.watch('domain')}
              handleChange={v => {
                if (v?.title) form.setValue('domain', v?.title, { shouldValidate: true })
                // eslint-disable-next-line i18next/no-literal-string
                form.trigger('url')
              }}
            />
          </div>
          {form.formState.errors.url?.message && (
            <p className={`error-text ${cx('error')}`}>{form.formState.errors.url?.message}</p>
          )}
          {/* eslint-disable-next-line i18next/no-literal-string */}
          {!doesNotEndWith(form.watch('url'), ['.ru', '.com']) && (
            <p className={`error-text ${cx('error')}`}>{t('commons:end_link')}</p>
          )}
        </div>
        <div>
          <h2 className={cx('form__field__label')}>{t('commons:original_link')}</h2>
          <div className={cx('form__field')}>
            {/* eslint-disable-next-line i18next/no-literal-string */}
            <p className={cx('form__field__domain')}>https://</p>
            <Input
              classNameWrapper={cx('form__field_width')}
              fullWidth
              disabled={isLoading}
              placeholder={`${t('commons:enter_link')}...`}
              title={t('commons:original_link')}
              register={form.register('original_url')}
            />
          </div>
        </div>
        <Select
          loading={isCategoriesLoading}
          label={t('commons:template_category')}
          list={categoriesList}
          placeholder={t('commons:select_category')}
          handleChange={v => {
            if (v?.id) {
              form.setValue('category', v.id, { shouldValidate: true })
            }
          }}
        />
        {isError && <p className='error-text'>{`${t('commons:error_try_again')}...`}</p>}
        <Button disabled={!form.formState.isValid || !form.watch('name').length} type='submit'>
          {isLoading ? `${t('commons:creating')}...` : t('commons:create_template')}
        </Button>
      </form>
    </div>
  )
}

export default PhishingTemplatesCreate
