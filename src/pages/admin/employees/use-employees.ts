import { useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useEffect, useMemo, useState } from 'react'
import { useConfig } from '@/shared/hooks'
import { ITab, TTabs, useEmployees as useEmployeesSlice } from '@/entities/employee'

export interface IMenuItem {
  name?: string
  onClick?: (name?: string) => void
  text: string
  isVisible?: boolean
}

/**
 * Custom hook to manage employee-related state and actions in the admin panel.
 *
 * @returns {object} - An object containing various state variables and functions to manage employees.
 * @property {function} handleSearch - Function to handle search input changes.
 * @property {string} search - Current search string.
 * @property {TTabs} tab - Currently active tab.
 * @property {function} handleClickAddDepartment - Function to handle the action of adding a department.
 * @property {IMenuItem[]} menu - Array of menu items for employee actions.
 * @property {ITab<TTabs>[]} tabs - Array of tab configurations.
 * @property {function} handleClickTab - Function to handle tab click events.
 * @property {boolean} openEmployeesImport - State to manage the visibility of the employee import modal.
 * @property {boolean} openAddDepartment - State to manage the visibility of the add department modal.
 * @property {boolean} openAddEmployee - State to manage the visibility of the add employee modal.
 * @property {function} setOpenEmployeesImport - Function to set the state of the employee import modal.
 * @property {function} setOpenAddDepartment - Function to set the state of the add department modal.
 * @property {function} setOpenAddEmployee - Function to set the state of the add employee modal.
 * @property {boolean} isAllSelected - State indicating if all employees are selected.
 * @property {any[]} checkedEmployees - Array of checked employees.
 * @property {boolean} isADOpen - State to manage the visibility of the Active Directory modal.
 * @property {function} setIsADOpen - Function to set the state of the Active Directory modal.
 */
export const useEmployees = () => {
  const { search, setSearch, setPage, isAllSelected, checkedEmployees } = useEmployeesSlice()
  const [currentSearchParams, setSearchParams] = useSearchParams()
  const { t } = useTranslation()
  const searchParams = new URLSearchParams()

  const handleSearch = (value: string) => {
    setPage(0)
    setSearch(value)
  }

  const tabs = useMemo<ITab<TTabs>[]>(
    () => [
      {
        name: 'employees',
        value: t('commons:list'),
      },
      {
        name: 'departments',
        value: t('commons:structure'),
      },
    ],
    [t],
  )

  const [openEmployeesImport, setOpenEmployeesImport] = useState(false)
  const [openAddDepartment, setOpenAddDepartment] = useState(false)
  const [openAddEmployee, setOpenAddEmployee] = useState(false)
  const [isADOpen, setIsADOpen] = useState(false)

  const handleClickEmployeesImport = () => setOpenEmployeesImport(prev => !prev)
  const handleClickAddEmployee = () => setOpenAddEmployee(prev => !prev)
  const handleClickAD = () => {
    setIsADOpen(prev => !prev)
  }
  const handleClickAddDepartment = () => setOpenAddDepartment(true)

  const config = useConfig()

  const menu: IMenuItem[] = [
    {
      name: 'add',
      text: t('commons:add_employee'),
      onClick: handleClickAddEmployee,
    },
    {
      name: 'file',
      text: t('commons:import_from_file'),
      onClick: handleClickEmployeesImport,
    },
    {
      name: 'AD',
      text: 'Active Directory',
      onClick: handleClickAD,
      isVisible: Boolean(config?.useSSO || config?.use_admin_ad),
    },
  ]

  // Для сохранения активного таба при возвращении назад
  const tab: TTabs = (currentSearchParams.get('tab') as TTabs) || 'employees'

  // Чтобы при несовпадении вохвращалось на 1 таб
  useEffect(() => {
    if (!tabs.filter(t => t.name === tab).length) {
      handleClickTab('employees')
    }
  }, [tab])

  const handleClickTab = (value: TTabs) => {
    if (!value) return

    searchParams.set('tab', value)
    setSearchParams(searchParams)
  }

  return {
    handleSearch,
    search,
    tab,
    handleClickAddDepartment,
    menu,
    tabs,
    handleClickTab,
    openEmployeesImport,
    openAddDepartment,
    openAddEmployee,
    setOpenEmployeesImport,
    setOpenAddDepartment,
    setOpenAddEmployee,
    isAllSelected,
    checkedEmployees,
    isADOpen,
    setIsADOpen,
  }
}
