.wrapper {
  margin: 0 auto;
  // padding: var(--page-container-padding);
  max-width: var(--page-container);
  position: relative;

  &--more-padding {
    padding-bottom: 96px;
  }
}

.tabsWrapper {
  align-items: center;
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  min-height: 32px;
}

.titleWrapper {
  align-items: start;
  display: grid;
  grid-gap: 16px;
  grid-template-columns: auto 1fr auto auto;
  justify-content: space-between;
  margin-bottom: 32px;
  min-height: 42px;
  .title {
    margin-bottom: 0;
  }
  .searchWrapper {
    width: 100%;
    .search {

      background: #fff;
      width: 100%;
    }
  }
  .createDepartment {
    margin-left: 16px;
    padding-bottom: 8px;
    padding-top: 8px;
  }
  .createEmployee {
    padding-bottom: 8px;
    padding-top: 8px;
  }
}
