/* eslint-disable @typescript-eslint/no-explicit-any */
import { QueryStatus } from '@reduxjs/toolkit/query'
import { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { useDownload } from '@/shared/hooks'

import { useGetEmployessReportQuery } from '@/store/services/tags-employees-service'
import { Report } from '@/shared/types/store/settings'

const REPORT_POOLING_TIMEOUT = 1500

type Props = {
  generateReport: (setPolling: Dispatch<SetStateAction<boolean>>) => Promise<void>
  data?: Report
  reset: () => void
  isError: boolean
}

export const useEmployessExportModal = ({ generateReport, data, reset, isError }: Props) => {
  const [isPooling, setIsPooling] = useState(false)

  const { data: reportData } = useGetEmployessReportQuery((data as any)?.id, {
    skip: !isPooling || !data?.id,
    pollingInterval: isPooling ? REPORT_POOLING_TIMEOUT : 0,
  })

  const getGenerateReportId = async () => {
    await generateReport(setIsPooling)
  }

  useEffect(() => {
    getGenerateReportId()

    return () => {
      reset()
    }
  }, [])

  useEffect(() => {
    if (reportData?.status === 'error') {
      setIsPooling(false)
    }
  }, [reportData?.status])

  useEffect(() => {
    const isUrlExists = !!reportData?.url || !!data?.url

    if (!isUrlExists) return

    setIsPooling(false)
  }, [data?.url, reportData?.url])

  const [download] = useDownload()

  const onDownload = () => {
    const url = data?.url || reportData?.url
    if (!url) return
    download(url)
  }

  const isUrlExists = !!reportData?.url || !!data?.url
  const isErrorStatus = reportData?.status === 'error' || data?.status === 'error'

  const generatedStatus = isUrlExists
    ? QueryStatus.fulfilled
    : isError || isErrorStatus
      ? QueryStatus.rejected
      : status === QueryStatus.rejected
        ? QueryStatus.rejected
        : QueryStatus.pending

  return {
    generatedStatus,
    onDownload,
  }
}
