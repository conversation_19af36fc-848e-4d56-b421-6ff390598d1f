import { FC } from 'react'
import styles from './employees.module.scss'
import classNamesBind from 'classnames/bind'
import { EmployeesProps } from './employees.d'
import { EmployeesFilter, Tabs } from '@/shared/components'
import { PageTitle } from '@/shared/ui'
import { Button, RoundedButton, SearchInput } from '@/shared/ui'
import { AddDepartmentModal } from '@/shared/modals/add-department-modal'
import { CreateEmployeeModal } from '@/shared/modals/create-employees-modal'
import { useEmployees } from './use-employees'
import { useDepartments } from '@/entities/department'
import { EmployeesList, DepartmentList } from '@/shared/components'
import { ImportEmployeesModal } from '@/shared/modals/import-employess-modal'
import { ADSyncModal } from '@/shared/modals/ad-sync/ad-sync'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const Employees: FC<EmployeesProps.Props> = props => {
  return <EmployeesInner {...props} />
}

export const EmployeesInner: FC<EmployeesProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const {
    handleSearch,
    search,
    tab,
    handleClickAddDepartment,
    menu,
    tabs,
    handleClickTab,
    openEmployeesImport,
    openAddDepartment,
    openAddEmployee,
    setOpenEmployeesImport,
    setOpenAddDepartment,
    setOpenAddEmployee,
    isAllSelected,
    checkedEmployees,
    isADOpen,
    setIsADOpen,
  } = useEmployees()

  const { checkedDeparments, refetchDepartments } = useDepartments({ isSkipDepartmentsReq: true })

  return (
    <div
      className={cx('wrapper', className, {
        'wrapper--more-padding':
          isAllSelected || !!checkedEmployees.length || !!checkedDeparments.length,
      })}
    >
      <div className={cx('titleWrapper')}>
        <PageTitle className={cx('title')}>{t('commons:employees')}</PageTitle>
        {tab === 'departments' ? (
          <div />
        ) : (
          <SearchInput
            className={cx('search')}
            classNameWrapper={cx('searchWrapper')}
            placeholder={t('commons:enter_name_position_email')}
            onChange={handleSearch}
            value={search}
            // isLoading={isLoading}
          />
        )}
        <Button className={cx('createDepartment')} color='gray' onClick={handleClickAddDepartment}>
          {t('commons:create_department')}
        </Button>
        <RoundedButton size='40' menu={menu} />
      </div>
      <div className={cx('tabsWrapper')}>
        <Tabs tabs={tabs} onClick={handleClickTab} active={tab} />
        {tab === 'employees' && <EmployeesFilter />}
      </div>
      {tab === 'employees' && <EmployeesList />}
      {tab === 'departments' && <DepartmentList />}
      {openEmployeesImport && (
        <ImportEmployeesModal
          active={openEmployeesImport}
          setActive={setOpenEmployeesImport}
          refetchDepartments={refetchDepartments}
        />
      )}
      {openAddDepartment && (
        <AddDepartmentModal active={openAddDepartment} setActive={setOpenAddDepartment} />
      )}
      {openAddEmployee && (
        <CreateEmployeeModal active={openAddEmployee} setActive={setOpenAddEmployee} />
      )}
      {isADOpen && <ADSyncModal active={isADOpen} setActive={setIsADOpen} />}
    </div>
  )
}

export default Employees
