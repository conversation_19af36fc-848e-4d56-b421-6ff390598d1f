/* eslint-disable no-empty-pattern */
import React, { useLayoutEffect, useRef, useState } from 'react'
import styles from './styles.module.scss'
import classNamesBind from 'classnames/bind'
import { Loader, Pagination } from '@/shared/ui'
import BoxIcon from '@/shared/ui/Icon/icons/components/BoxIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { type IPhishingCampaignStatisticsDepartment } from '@/entities/phishing'
import { phishingQueries } from '@/entities/phishing'
import { createSearchParams, useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { DEPARTMENTS_TABLE_COLS, TABLE_LIMIT } from '@/shared/constants/statistics'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

//! Заглушка для таблицы, когда данных нет
const EmptyPlug = () => {
  const { t } = useTranslation()

  return (
    <div className={cx('plug__container')}>
      <div className={cx('plug__icon', 'box_icon')}>
        <IconWrapper color='gray60'>
          <BoxIcon />
        </IconWrapper>
      </div>
      <h2 className={cx('plug__text')}>{t('commons:no_data')}</h2>
    </div>
  )
}

interface ITableRow {
  row: IPhishingCampaignStatisticsDepartment
}

const TableRow: React.FC<ITableRow> = ({ row }) => {
  return (
    <tr>
      <td className={cx('dep-cell')}>
        <p title={row.title}>{row.title}</p>
      </td>
      <td className={cx('employee-cell')}>
        <p title={String(row.users_count)}>{row.users_count}</p>
      </td>
      <td className={cx('popular-cell')}>
        <p title={row.popular_template_name} className={cx('email')}>
          {row.popular_template_name}
        </p>
      </td>
      <td className={cx('caught-cell')}>
        <p
          className={cx(`incident-level-${row.incident_risk_level}`)}
          title={`${row.incident_risk}%`}
        >
          {row.incident_risk}%
        </p>
      </td>
    </tr>
  )
}

export const DepartmentTable = () => {
  const { campaign_id = '' } = useParams()
  const [searchParams] = useSearchParams()
  const type = searchParams.get('type') || 'departments'
  const page = searchParams.get('page') || 1
  const { t } = useTranslation()

  const tableHeaderRef = useRef<HTMLTableSectionElement>(null)
  const [wasError, setWasError] = useState(false)

  const onSetPage = (page: number) => {
    const ACTUAL_PAGE = page + 1
    if (ACTUAL_PAGE <= 0) return
    handleClickPagination(String(ACTUAL_PAGE))

    if (tableHeaderRef.current) tableHeaderRef.current.scrollIntoView()
  }

  const navigate = useNavigate()

  const handleClickPagination = (newPage: string) => {
    navigate({
      pathname: `/lk/admin/phishing/campaigns/${campaign_id}/statistics`,
      search: createSearchParams({ type, page: newPage }).toString(),
    })
  }

  const ACTUAL_PAGE = (+page || 1) - 1

  const tableParams = {
    limit: TABLE_LIMIT,
    offset: TABLE_LIMIT * ACTUAL_PAGE,
  }

  const { data, isLoading, isFetching, isError, isSuccess } =
    phishingQueries.useGetPhishingCampaignsTableDepartmentsByIdQuery(
      {
        id: campaign_id,
        params: tableParams,
      },
      { skip: type !== 'departments' },
    )

  const isEmptyData = data?.data.length === 0

  useLayoutEffect(() => {
    if (isError) setWasError(true)
  }, [isError])

  useLayoutEffect(() => {
    if (isSuccess && !!data) setWasError(false)
  }, [isSuccess, data])

  return (
    <>
      <table className={cx('table', isFetching && 'table__opacity')}>
        <thead ref={tableHeaderRef}>
          <tr>
            {DEPARTMENTS_TABLE_COLS.map(column => (
              <th key={column.key}>{t(column.label).toUpperCase()}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {isFetching && (
            <tr key={'row-first-loading-plug'}>
              <td colSpan={DEPARTMENTS_TABLE_COLS.length}>
                <div className={cx('plug__container')}>
                  <Loader size='56' />
                </div>
              </td>
            </tr>
          )}
          {!isFetching && isLoading ? (
            wasError ? (
              <tr key={'row-first-loading-plug'}>
                <td colSpan={DEPARTMENTS_TABLE_COLS.length}>
                  <div className={cx('plug__container')}>
                    <Loader size='56' />
                  </div>
                </td>
              </tr>
            ) : (
              <tr className={cx('plug__loader__container')} key={'row-loading-plug'}>
                <td className={cx('plug__loader')} colSpan={DEPARTMENTS_TABLE_COLS.length}>
                  <Loader size='56' />
                </td>
              </tr>
            )
          ) : wasError ? (
            <tr key={'row-error-plug'}>
              <td colSpan={DEPARTMENTS_TABLE_COLS.length}>
                <div className={cx('plug__container')}>
                  <Loader error loading={false} size='56' />
                  <h2 className={cx('plug__text')}>{t('commons:error_with_dots')}</h2>
                </div>
              </td>
            </tr>
          ) : isEmptyData ? (
            <tr key={'row-empty-plug'}>
              <td colSpan={DEPARTMENTS_TABLE_COLS.length}>
                <EmptyPlug />
              </td>
            </tr>
          ) : null}
          {!wasError &&
            data?.data?.map((row, index) => <TableRow key={page + '-' + index} row={row} />)}
        </tbody>
      </table>
      <Pagination
        currentPage={ACTUAL_PAGE}
        limit={TABLE_LIMIT}
        total={data?.total_count || 0}
        onChange={onSetPage}
        isLoading={isLoading || isFetching}
      />
    </>
  )
}
