/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react'
import { v4 as uuid } from 'uuid'
import { type Attachment, EAttachments } from '@/entities/phishing'

import { FileModal, FileModalProps } from '@/shared/modals/file-modal'
import { SelectCard } from '@/shared/components'
import { FileIcon } from '@/shared/ui'
import { useForm } from 'react-hook-form'
import { useTranslation } from 'react-i18next'

type PhishingTemplateEmailFormAttachemntProps = {
  attachments: Attachment[]
  setAttachments: (attach: Attachment[]) => void
}

const DEFAULT_VALUES: FileModalProps.FileModalForm = {
  name: '',
  type: EAttachments.pdf,
}

export const PhishingTemplateEmailFormAttachemnt = ({
  attachments,
  setAttachments,
}: PhishingTemplateEmailFormAttachemntProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const { t } = useTranslation()

  const form = useForm<FileModalProps.FileModalForm>({
    defaultValues: DEFAULT_VALUES,
  })

  const onSubmitForm = (data: FileModalProps.FileModalForm) => {
    const newAttachment = {
      filename: data.name,
      type: data.type.toLowerCase() as any,
      id: uuid(),
      created_at: new Date().toISOString(),
      updated_at: null as unknown as string,
    }
    setAttachments([...attachments, newAttachment])
  }

  const onClose = (v: React.SetStateAction<boolean>) => {
    form.reset()
    setIsOpen(v)
  }

  return (
    <>
      <FileModal form={form} onSubmitForm={onSubmitForm} isOpen={isOpen} setIsOpen={onClose} />
      <SelectCard
        onMainClick={() => setIsOpen(v => !v)}
        selectedItems={attachments.map(attach => ({
          id: attach.id,
          icon: <FileIcon type={attach.type.toUpperCase() as any} />,
          isDeleted: true,
          text: attach.filename,
        }))}
        onDeleteItem={(item: { id: string }) =>
          setAttachments(attachments.filter(a => a.id !== item.id))
        }
        label={t('commons:attachments')}
        text={attachments.length ? t('commons:add_attachment') : t('commons:select_attachment')}
      />
    </>
  )
}
