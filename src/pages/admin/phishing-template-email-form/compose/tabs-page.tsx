/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useRef, useState } from 'react'
import { Controller, UseFormReturn } from 'react-hook-form'
import { PagePreview } from '@/shared/components'
import { Button, HelpIcon, TabItem, TabsNew } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import { v4 as uuid } from 'uuid'

import styles from '../phishing-template-email-form.module.scss'
import { type IPhishingEmailForm } from '@/entities/phishing'
import { useNotification } from '@/shared/contexts/notifications'
import { useCKEditor, CKEditorEventAction } from '@sn.secure-t-org/ckeditor4-react'
import { config } from '@/shared/configs/editor/config'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

type TabsPageProps = {
  form: UseFormReturn<IPhishingEmailForm, any>
  onHandleBodyTracker?: () => void
}

const parser = new DOMParser()

export const TabsPage = ({ form }: TabsPageProps) => {
  const [isDisabled, setIsDisabled] = useState<boolean>(true)
  const { t } = useTranslation()

  const isInit = useRef(false)
  const data = form.watch('html')
  const element = useRef<HTMLDivElement>(null)

  const viewTabLabel = t('commons:view')
  const editTabLabel = t('commons:html_editor')

  const { add } = useNotification()

  const { editor } = useCKEditor({
    config,
    element: element.current,
    type: 'classic',
    dispatchEvent: ({ type, payload }) => {
      if (type === CKEditorEventAction.instanceReady) {
        data && payload.editor.setData(data)
      } else if (type === CKEditorEventAction.blur) {
        onChange(payload.editor.getData().trim())
      } else if (type === CKEditorEventAction.change) {
        onChange(payload.editor.getData().trim())
      }
    },
    subscribeTo: ['blur', 'instanceReady', 'change'],
    editorUrl: '/ckeditor/ckeditor.js',
  })

  // На всякий случай, а то в dispatchEvent дата может быть пустая
  useEffect(() => {
    if (!editor || !data || isInit.current) {
      return
    }

    isInit.current = true
    editor.setData(data)
  }, [data, editor])

  const onChange = (newValue: string) => {
    form.setValue('html', newValue, { shouldDirty: true })
  }

  const checkIfDisabled = useCallback(() => {
    let disabled = true

    if (editor) {
      const html = parser.parseFromString(
        editor.getData().replace(/<noscript.*?<\/noscript>/g, ''),
        'text/html',
      )

      html.querySelectorAll('a').forEach(link => {
        if (link.attributes.getNamedItem('href')?.value != '{{.URL}}') {
          disabled = false
        }
      })
    }

    setIsDisabled(disabled)
  }, [editor])

  const changeLinks = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault()

    if (editor) {
      const html = parser.parseFromString(
        editor.getData().replace(/<noscript.*?<\/noscript>/g, ''),
        'text/html',
      )

      html.querySelectorAll('a').forEach(link => {
        link.href = '{{.URL}}'
      })

      const value = html.body.innerHTML
      form.setValue('html', value, { shouldDirty: true })
      editor.setData(value)

      add({
        message: t('commons:links_successfully_replaced'),
        status: 'success',
        id: uuid(),
      })
    }
  }

  const [activeTab, setActiveTab] = useState<string>()

  return (
    <div className={cx('tabs__page')}>
      <div className={cx('helpful__wrapper')}>
        <Button disabled={isDisabled} className={cx('tabs__change-links')} onClick={changeLinks}>
          {t('commons:links_replace')}
        </Button>
        <HelpIcon text={t('commons:auto_replace_link_help')} />
      </div>
      <TabsNew
        tabsClassname={cx('tabs')}
        activeTabClassname={cx('tabs__tab_active')}
        tabClassname={cx('tabs__tab')}
        defaultTab={viewTabLabel}
        hiddenType='display'
        onChange={(activeTab: string) => {
          setActiveTab(activeTab)
        }}
      >
        <TabItem label={viewTabLabel} value={viewTabLabel} key={viewTabLabel}>
          <div className={cx('tab__wrapper', 'preview')}>
            <Controller
              name='html'
              control={form.control}
              render={({ field }) => (
                <PagePreview
                  // чтобы при изменении таба preview перерендеривался
                  // т.к. был баг что не перерендеривается
                  key={activeTab}
                  viewportWidth={600}
                  viewportHeight={580}
                  html={(editor?.current as any)?.value || field?.value}
                  checkIfDisabled={checkIfDisabled}
                />
              )}
            />
          </div>
        </TabItem>
        <TabItem label={editTabLabel} value={editTabLabel} key={editTabLabel}>
          <div className={cx('tab__wrapper', 'dark')}>
            <div className={cx('white-underlay')}>
              <div className={cx('ckeditor-wrapper')}>
                <div ref={element} />
              </div>
            </div>
          </div>
        </TabItem>
      </TabsNew>
    </div>
  )
}
