/* eslint-disable @typescript-eslint/no-explicit-any */
import { IEmail, IPhishingEmailForm } from '@/entities/phishing'
import { trackSubString } from './data'
import { prepareReplacedMapForLogos, replaceLogoUrls } from '@/shared/helpers'
import { Config } from '@/shared/types'

export const regExpDeleteUnnecessaryParts = /^https:\/\//g

export function strEndsWith(str_arr: string[], input: string) {
  for (const str of str_arr) {
    if (input.endsWith(str)) {
      return str
    }
  }
  return null
}

export const getEmailPostfix = (email: string, domains: string[] = []) => {
  const urlWithoutProtocol = email.replace(regExpDeleteUnnecessaryParts, '')

  const sortedDomains = [...domains].sort((a, b) => b.length - a.length)

  const existedDomain = strEndsWith(sortedDomains, urlWithoutProtocol)

  const domain = existedDomain ? `@${existedDomain}` : ''

  return domain
}

export const prepareFormData = (data: IPhishingEmailForm, config?: Config): IEmail => {
  // Вырезаем все ненужные поля, которые нужны конкретно для работы формы в UI
  const { mail, mail_postfix, sender, html, ...email } = data
  const postfix = mail_postfix.startsWith('@') ? mail_postfix : '@' + mail_postfix

  const replacedMap: Record<string, string> = {}

  if (config?.darkLogoUrl) {
    replacedMap[config?.darkLogoUrl] = '{{dark_logo_url}}'
  }
  if (config?.lightLogoUrl) {
    replacedMap[config?.lightLogoUrl] = '{{light_logo_url}}'
  }
  if (config?.logoUrl) {
    replacedMap[config?.logoUrl] = '{{logo_url}}'
  }

  const htmlValue = replaceLogoUrls(html, replacedMap)

  const formattedSender = `${sender.trim()} <${mail.trim() + postfix.trim()}>`

  return {
    ...email,
    html: htmlValue,
    sender: formattedSender,
  } as any
}

export const prepareValues = (
  data: Partial<IPhishingEmailForm>,
  config?: Config,
): IPhishingEmailForm => {
  const birdsRegExp = /<([^>]*)>$/
  const mailDomainRegExp = /@(.*)$/
  const mailPrefixRegExp = /^[^@]*/

  const result = { ...data } as any

  const isTrack = data?.html?.includes(trackSubString) ?? false

  const mailMatch = data?.sender?.match(birdsRegExp)
  const sender = mailMatch
    ? data?.sender?.slice(0, data?.sender?.length - mailMatch[0].length - 1)
    : ''

  if (mailMatch) {
    const [, mailBody] = mailMatch
    const mailPostfixMatch = mailBody.match(mailDomainRegExp)
    const mailPostfix = mailPostfixMatch?.[0] || ''
    const mailPrefixMatch = mailBody.match(mailPrefixRegExp)
    const mailPrefix = mailPrefixMatch?.[0] || ''

    result.sender = sender
    result.mail_postfix = mailPostfix
    result.is_track = isTrack
    result.mail = mailPrefix
  }

  result.html = config
    ? replaceLogoUrls(data?.html || '', prepareReplacedMapForLogos(config))
    : data?.html || ''

  return result
}
