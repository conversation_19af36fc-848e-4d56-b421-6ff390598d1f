/* eslint-disable @typescript-eslint/no-unused-vars */
import { useCallback, useMemo } from 'react'
import { useNotification } from '@/shared/contexts/notifications'

import { type IPhishingEmailForm, phishingMutations, phishingQueries } from '@/entities/phishing'
import { trackSubString, VALUES } from './data'
import { getEmailPostfix, prepareFormData, prepareValues } from './helpers'
import { useForm, SubmitHandler } from 'react-hook-form'
import { useNavigate, useParams } from 'react-router-dom'
import { useConfig } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'

export const usePhishingTemplateEmailForm = () => {
  const { template_id = '', email_id = '' } = useParams()
  const isEdit = !!email_id

  const { data: templateData } = phishingQueries.useGetTemplateByIdQuery(template_id)
  const { data: emailData, isLoading } = phishingQueries.useGetEmailQuery(email_id, {
    skip: !isEdit,
  })
  const { data: domains, isLoading: isDomainsLoading } = phishingQueries.useGetDomainsQuery(
    undefined,
    {
      skip: isEdit,
    },
  )

  const config = useConfig()
  const { t } = useTranslation()

  const [patchEmail, { isLoading: isPatchLoading }] = phishingMutations.usePatchEmailMutation()
  const [createEmail, { isLoading: isCreateLoading }] = phishingMutations.useCreateEmailMutation()

  const form = useForm<IPhishingEmailForm>({
    values: prepareValues(
      {
        ...VALUES,
        ...emailData,
        template_id,
        mail_postfix:
          getEmailPostfix((templateData && templateData.url) || '', domains?.domains) || '',
      },
      config,
    ),
  })

  const isDisabled = isPatchLoading || isCreateLoading

  const editButtonText = isPatchLoading
    ? `${t('commons:saving_changes')}...`
    : t('commons:save_changes')

  const createButtonText = isCreateLoading
    ? `${t('commons:creating_letter')}...`
    : t('commons:create_letter')

  const ButtonText = isEdit ? editButtonText : createButtonText

  const { handleErrorResponse } = useNotification()

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/templates',
        text: t('commons:templates'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/templates/' + template_id,
        text: templateData?.name || t('commons:current_template'),
        clickable: true,
        isLoading: !templateData,
      },
      {
        id: '1',
        text: isEdit ? t('commons:editing_letter') : t('commons:creating_letter'),
        clickable: false,
      },
    ],
    [templateData, t],
  )

  const navigate = useNavigate()

  const onSubmit: SubmitHandler<IPhishingEmailForm> = async data => {
    if (isDisabled) return

    if (!data.html) {
      handleErrorResponse({
        status: 'error',
        message: t('commons:fill_html_template'),
      })
      return
    }

    const preparedData = prepareFormData(data)

    if (isEdit) {
      await patchEmail(preparedData).unwrap()

      if (templateData?.id) {
        navigate(`/lk/admin/phishing/templates/${templateData?.id}`)
      }

      return
    }

    const { id, ...requestData } = preparedData

    const createdEmail = await createEmail(requestData).unwrap()

    if (templateData?.id) {
      navigate(`/lk/admin/phishing/templates/${templateData?.id}/email/${createdEmail.id}/edit`)
    }
  }

  const onHandleBodyTracker = useCallback(() => {
    const hasTrackerСontrol = form.getValues('is_track')

    const html = form.getValues('html')
    const has_tracker: boolean = hasTrackerСontrol

    if ((html || html === '') && has_tracker !== null) {
      const trackerPosition: number = html.indexOf(trackSubString)
      const hasTracker = trackerPosition !== -1

      if (hasTracker === has_tracker) return

      //! Нужно добавить
      if (!hasTracker && has_tracker) {
        const endBodyPosition = html.indexOf('</body>')

        const newHTML =
          endBodyPosition === -1
            ? `${html}${trackSubString}`
            : `${html.slice(0, endBodyPosition)}${trackSubString}${html.slice(endBodyPosition)}`

        form.setValue('html', newHTML)
      }
      //! Нужно удалить
      if (hasTracker && !has_tracker) {
        const newHTML = `${html.slice(0, trackerPosition)}${html.slice(
          trackerPosition + trackSubString.length,
        )}`

        form.setValue('html', newHTML)
      }
    }
  }, [])

  const isUrlExists = !!form.watch('mail')

  return {
    isDisabled,
    BREADCRUMBS,
    form,
    isEdit,
    onSubmit,
    onHandleBodyTracker,
    isUrlExists,
    domains,
    isDomainsLoading,
    isLoading,
    ButtonText,
  }
}
