import { FC } from 'react'
import styles from './phishing-template-email-form.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingTemplateEmailFormProps } from './phishing-template-email-form.d'
import { Breadcrumbs, Button, HelpIcon, Input, Switch, PageTitle } from '@/shared/ui'
import { Controller } from 'react-hook-form'
import { usePhishingTemplateEmailForm } from './use-phishing-template-email-form'
import { TabsPage } from './compose/tabs-page'
import { PhishingTemplateEmailFormAttachemnt } from './compose/form-attachment'
import { type Attachment } from '@/entities/phishing'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const PhishingTemplateEmailForm: FC<PhishingTemplateEmailFormProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const {
    BREADCRUMBS,
    form,
    isDisabled,
    isEdit,
    onHandleBodyTracker,
    onSubmit,
    isLoading,
    ButtonText,
  } = usePhishingTemplateEmailForm()

  const fromErrorMessage =
    form.formState.errors.mail?.message || form.formState.errors.sender?.message

  return (
    <div className={cx('container')}>
      <div className={cx('actions')}>
        <Breadcrumbs items={BREADCRUMBS} />
        <Button
          disabled={isLoading || isDisabled || !(form.formState.isValid && form.formState.isDirty)}
          onClick={form.handleSubmit(onSubmit)}
          type='submit'
          loading={isLoading}
        >
          {ButtonText}
        </Button>
      </div>
      <PageTitle className={cx('title')}>
        {isEdit ? t('commons:editing_letter') : t('commons:creating_letter')}
      </PageTitle>
      <form className={cx('form', className)}>
        <div className={cx('form__fields')}>
          <Input
            error={form.formState.errors.name?.message}
            register={form.register('name', {
              minLength: 1,
              required: true,
              pattern: {
                value: /\S/,
                message: t('commons:enter_title'),
              },
            })}
            disabled={isLoading}
            fullWidth
            label={t('commons:name')}
            placeholder={t('commons:enter_title')}
          />
          <div className={cx('sender__wrapper')}>
            <h2 className={cx('label')}>{t('commons:from')}</h2>
            <div className={cx('form__field')}>
              <Input
                register={form.register('sender', {
                  required: t('commons:required_field'),
                })}
                disabled={isLoading}
                fullWidth
                classNameWrapper={cx('form__field_width', 'form__field__input')}
                placeholder={t('commons:tech_support')}
              />

              <Input
                fullWidth
                register={form.register('mail', {
                  minLength: 1,
                  required: t('commons:required_field'),
                  pattern: {
                    value: /^[A-Za-z0-9\-_.#]*$/,
                    message: t('commons:emails_format'),
                  },
                })}
                classNameWrapper={cx('form__field_width')}
                disabled={isLoading}
                // eslint-disable-next-line i18next/no-literal-string
                placeholder='mail'
              />
              <p className={cx('form__field__domain')}>{form.watch('mail_postfix')}</p>
            </div>
            {!!fromErrorMessage && <div className={cx('error-text')}>{fromErrorMessage}</div>}
          </div>
          <Input
            error={form.formState.errors.subject?.message}
            register={form.register('subject', {
              required: t('commons:required_field'),
            })}
            fullWidth
            disabled={isLoading}
            label={t('commons:theme')}
            placeholder={t('commons:enter_theme')}
          />
          <PhishingTemplateEmailFormAttachemnt
            attachments={form.watch('attachments')}
            setAttachments={(v: Attachment[]) =>
              form.setValue('attachments', v, { shouldDirty: true })
            }
          />
          <div className={cx('switch__wrapper')}>
            <p className={cx('label')}>{t('commons:track_email_opening')}</p>
            <Controller
              control={form.control}
              name='is_track'
              render={({ field }) => (
                <Switch
                  customValue={field.value}
                  disabled={isLoading}
                  onChange={() => {
                    field.onChange(!field.value)
                    onHandleBodyTracker()
                  }}
                />
              )}
            />
            <HelpIcon text={t('commons:add_tracking_link')} />
          </div>
        </div>
        <div>
          <TabsPage onHandleBodyTracker={onHandleBodyTracker} form={form} />
        </div>
      </form>
    </div>
  )
}
