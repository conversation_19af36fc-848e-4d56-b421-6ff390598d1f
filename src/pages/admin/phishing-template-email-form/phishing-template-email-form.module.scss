.container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.actions {
  align-items: center;
  display: flex;
  gap: 24px;
  justify-content: space-between;
}

.helpful__wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  margin-bottom: 0;
  margin-top: -16px;
}

.select {
  &__domains {
    width: 160px;

    div {
      width: 160px;
    }
    * {
      font-weight: 500 !important;
    }
  }
}

.ckeditor-wrapper {
  height: 100%;
  > *:last-child {
    height: 100%;
    > *:last-child {
      display: grid;
      grid-template-rows: auto 1fr auto;

      height: 100%;
    }
  }
}

.tab {
  &__wrapper {
    background-color: var(--white);
    border-radius: 0 12px 12px;
    height: 600px;
    width: 100%;
  }
}

.preview {
  overflow: hidden;
  overflow-y: scroll;
}

.tabs {
  gap: 8px;
  &__tab {
    background: var(--white);
    border-radius: 12px 12px 0 0;
    display: flex;

    font: var(--font-text-2-medium);
    opacity: 0.7;
    padding: 8px 16px 6px;

    transition: 0.2s ease;

    &_active {
      opacity: 1;
    }

    &__wrapper {
      border-radius: 24px;

      display: flex;
      flex-grow: 1;
      position: relative;
      transform: translateZ(0);
      width: 100%;
      z-index: 10;
    }

    &:not(&_active):hover {
      opacity: 0.85;
    }
    &:nth-child(1) {
      background: var(--white);
    }
    &:nth-child(2) {
      background: #1a1a1a;
      color: var(--white);
    }
  }
}

.label {
  color: var(--color-gray-90);

  font: var(--font-text-2-medium);
}

.sender {
  &__wrapper {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }
}

.switch {
  &__wrapper {
    align-items: center;
    display: flex;
    gap: 12px;
  }
}

.form {
  display: flex;
  flex-direction: column;
  gap: 28px;
  width: 100%;

  &__fields {
    display: flex;
    flex-direction: column;
    gap: 16px;
    justify-content: flex-start;
    max-width: 550px;
  }

  &__field {
    align-items: center;
    display: grid;
    gap: 8px;

    grid-template-columns: 1fr 1fr auto;
    justify-content: flex-start;

    &__input {
      height: 100%;
    }

    &__label {
      color: var(--color-gray-90);
      font: var(--font-text-2-medium);
      margin-bottom: 8px;
    }

    &__domain {
      color: var(--color-gray-70);
      font: var(--font-text-2-normal);
    }
  }
}

.select {
  &__domains {
    min-width: 160px;

    div {
      height: 47px;
      width: 100%;
    }

    * {
      font-weight: 500 !important;
    }
  }

  &__selectel {
    width: 100%;

    div {
      height: 47px;
    }
  }
}

.error {
  margin-top: 8px;
}

.tabs__page {
  position: relative;
}

.format__btn {
  position: absolute;
  right: 0;
  top: 4px;

  * {
    font: var(--font-text-2-medium);
  }

  &:hover {
    path {
      stroke: var(--color-gray-90) !important;
    }
  }
}

.white-underlay {
  background-color: var(--white);
  border-radius: 0 12px 12px;
  height: 100%;
  width: 100%;
}
