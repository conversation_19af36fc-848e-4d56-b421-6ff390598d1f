import { IPhishingEmailForm } from '@/entities/phishing'

export const trackSubString = '<p>{{.Tracker}}</p>'

export const VALUES: IPhishingEmailForm = {
  attachments: [],
  html: '',
  id: '',
  name: '',
  sender: '',
  subject: '',
  mail: '',
  mail_postfix: '',
  is_track: false,
  template_id: '',
}

export const SELECTEL_ALIASES = [
  'noreply',
  'info',
  'team',
  'mail',
  'finance',
  'ofd',
  'service',
  'promo',
]

export const SELECTEL_ALIASES_LIST = SELECTEL_ALIASES.map(i => ({
  id: i,
  title: i,
}))
