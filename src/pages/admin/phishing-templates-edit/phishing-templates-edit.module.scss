.container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.logo {
  align-items: center;
  display: flex;
  gap: 12px;

  margin-bottom: 20px;

  &__img {
    height: 32px;
    width: 32px;

    img {
      height: 32px;
      width: 32px;
    }
  }

  &__title {
    color: var(--color-gray-90);
    font: var(--font-title-2-medium);
  }
}

.form {
  display: flex;
  flex-direction: column;

  gap: 24px;
  max-width: 450px;

  &__field {
    align-items: center;
    display: flex;
    gap: 8px;
    justify-content: flex-start;

    &__label {

      color: var(--color-gray-90);
      font: var(--font-text-2-medium);
      margin-bottom: 8px;
    }

    &__domain {
      color: var(--color-gray-70);
      font: var(--font-text-2-normal);
    }

    &_width {
      flex-grow: 1;
      width: 100%;
    }
  }
}

.select {
  &__domains {
    width: 160px;

    div {
      width: 160px;
    }
    * {
      font-weight: 500 !important;
    }
  }
}

.error {
  margin-top: 8px;
}
