import { FC } from 'react'
import styles from './phishing-templates-edit.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingTempltesEditProps } from './phishing-templates-edit.d'
import { Breadcrumbs, Button, Input, Select } from '@/shared/ui'
import { usePhishingTemplatesEdit } from './use-phishing-templates-edit'
import { TemplateLogo } from '@/shared/components'
import { doesNotEndWith, validateDomainLevel } from './helper'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const PhishingTemplatesEdit: FC<PhishingTempltesEditProps.Props> = () => {
  const { t } = useTranslation()

  const {
    BREADCRUMBS,
    categoriesList,
    domainsList,
    form,
    isDomainsLoading,
    isCategoriesLoading,
    isFieldsDisabled,
    onSubmit,
    data,
    errors,
    isError,
    isPatchError,
    isUrlExists,
    isLoading,
  } = usePhishingTemplatesEdit()

  return (
    <div className={cx('container')}>
      <Breadcrumbs items={BREADCRUMBS} />
      <div className={cx('logo')}>
        <TemplateLogo src={data?.logo} alt={data?.name} className={cx('logo__img')} />
        <h1 className={cx('logo__title')}>{data?.name}</h1>
      </div>
      <form onSubmit={form.handleSubmit(onSubmit)} className={cx('form')}>
        <Input
          fullWidth
          disabled={isFieldsDisabled}
          placeholder={t('commons:enter_name_template')}
          label={t('commons:template_name')}
          name='name'
          register={form.register('name', { pattern: /^(?!\s*$).+/ })}
          required
          title={t('commons:enter_name_template')}
          error={errors.name ? errors.name.message : ''}
        />
        <div>
          <h2 className={cx('form__field__label')}>{t('commons:phishing_link')}</h2>
          <div className={cx('form__field')}>
            {/* eslint-disable-next-line i18next/no-literal-string */}
            <p className={cx('form__field__domain')}>https://</p>
            <Input
              fullWidth
              classNameWrapper={cx('form__field_width')}
              disabled={isFieldsDisabled}
              placeholder={`${t('commons:enter_link')}...`}
              register={form.register('url', {
                pattern: /^(?![\s\S]*\.(com|ru)$)[\s\S]*$/i,
                validate: {
                  domainLevel: value => validateDomainLevel(value),
                },
              })}
              title={t('commons:correct_link_work')}
            />
            <Select
              searchable={true}
              additionalStr={isUrlExists ? '.' : ''}
              className={cx('select__domains')}
              list={domainsList.map(d => ({ id: d, title: d }))}
              placeholder=''
              loading={isDomainsLoading}
              customValue={form.watch('domain')}
              value={form.watch('domain')}
              handleChange={v => {
                if (v?.title) form.setValue('domain', v?.title, { shouldValidate: true })
              }}
            />
          </div>
          {form.formState.errors.url?.message && (
            <p className={`error-text ${cx('error')}`}>{form.formState.errors.url?.message}</p>
          )}
          {/* eslint-disable-next-line i18next/no-literal-string */}
          {!doesNotEndWith(form.watch('url'), ['.ru', '.com']) && (
            <p className={`error-text ${cx('error')}`}>{t('commons:end_link')}</p>
          )}
        </div>
        <div>
          <h2 className={cx('form__field__label')}>{t('commons:original_link')}</h2>
          <div className={cx('form__field')}>
            {/* eslint-disable-next-line i18next/no-literal-string */}
            <p className={cx('form__field__domain')}>https://</p>
            <Input
              classNameWrapper={cx('form__field_width')}
              fullWidth
              disabled={isFieldsDisabled}
              placeholder={`${t('commons:enter_link')}...`}
              title={t('commons:original_link')}
              register={form.register('original_url')}
            />
          </div>
        </div>
        <Select
          loading={isCategoriesLoading}
          label={t('commons:template_category')}
          list={categoriesList}
          customValue={form.watch('category')}
          value={form.watch('category')}
          placeholder={t('commons:select_category')}
          handleChange={v => {
            if (v?.id) form.setValue('category', v.id)
          }}
        />
        {isPatchError && <p className='error-text'>{t('commons:error_editing_data')}</p>}
        {isError && <p className='error-text'>{t('commons:error_loading_data')}</p>}

        <Button disabled={!form.formState.isValid} type='submit'>
          {isLoading ? `${t('commons:editing')}...` : t('commons:edit_template')}
        </Button>
      </form>
    </div>
  )
}

export default PhishingTemplatesEdit
