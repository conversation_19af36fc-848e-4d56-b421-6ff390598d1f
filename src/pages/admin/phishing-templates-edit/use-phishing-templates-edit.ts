/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { SubmitHandler, useForm } from 'react-hook-form'
import {
  IPhishingCreateTemplatesRequest,
  IPhishingTemplate,
  phishingQueries,
  phishingMutations,
} from '@/entities/phishing'
import { useEffect, useMemo, useState } from 'react'
import { BreadcrumbItem } from '@/shared/ui'
import { useNotification } from '@/shared/contexts/notifications'
import { useNavigate, useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { validateCombinedUrl, validateDomainLevel } from './helper'

type IListItem = {
  id: any
  title: string
  hasPriority?: boolean
  isTitle?: boolean
}

const regExpDeleteUnnecessaryParts = /^https:\/\//g

const DOT_SIGN = '.'

function strEndsWith(str_arr: string[], input: string) {
  for (const str of str_arr) {
    if (input.endsWith(str)) {
      return str
    }
  }
  return null
}

export interface PhishingTemplatesCreateForm extends IPhishingCreateTemplatesRequest {
  domain?: string
}

export const usePhishingTemplatesEdit = () => {
  const { template_id = '' } = useParams()
  const { t } = useTranslation()

  const { data, isError } = phishingQueries.useGetTemplateByIdQuery(template_id)

  const DEFAULT_CATEGORIES = useMemo(
    () => [
      {
        id: null,
        title: t('commons:no_category'),
      },
    ],
    [t],
  )

  const [patchTemplateTrigger, { isError: isPatchError, data: patchData, isLoading }] =
    phishingMutations.usePatchPhishingTemplateByIdMutation()
  const { data: domainsData, isLoading: isDomainsLoading } = phishingQueries.useGetDomainsQuery()

  const prepareFormValues = (data: IPhishingTemplate | undefined): PhishingTemplatesCreateForm => {
    let urlWithoutProtocol = data?.url.replace(regExpDeleteUnnecessaryParts, '')

    let domain

    if (domainsData && urlWithoutProtocol) {
      const sortedDomains = [...domainsData?.domains].sort((a, b) => b.length - a.length)

      domain =
        strEndsWith(sortedDomains, urlWithoutProtocol) ??
        strEndsWith(domainsData?.domains, urlWithoutProtocol)

      if (domain) {
        const sliceLen =
          urlWithoutProtocol.length - domain.length === 0
            ? 0
            : urlWithoutProtocol.length - domain?.length - 1
        urlWithoutProtocol = urlWithoutProtocol.slice(0, sliceLen)
      }
    }

    return {
      category: data?.category?.id || null,
      name: data?.name || '',
      original_url: data?.original_url
        ? data?.original_url.replace(regExpDeleteUnnecessaryParts, '') || ''
        : '',
      url: urlWithoutProtocol || '',
      domain: domain || '',
    }
  }

  const { data: categoriesData, isLoading: isCategoriesLoading } =
    phishingQueries.useGetCategoriesQuery()
  const { handleResponse } = useNotification()

  const [domainsList, setDomainsList] = useState<string[]>([])
  const [categoriesList, setCategoriesList] = useState<IListItem[]>(() => [...DEFAULT_CATEGORIES])

  const BREADCRUMBS: BreadcrumbItem[] = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/templates',
        text: t('commons:templates'),
        clickable: true,
      },
      {
        id: `/lk/admin/phishing/templates/${template_id}`,
        text: data?.name || t('commons:template_name'),
        clickable: true,
        isLoading: !data?.name,
      },
      {
        id: t('commons:editing'),
        text: t('commons:editing'),
      },
    ],
    [template_id, data, t],
  )
  const form = useForm<PhishingTemplatesCreateForm>({
    values: prepareFormValues(patchData ? patchData : data),
    mode: 'all',
    resolver: async values => {
      const errors: Record<string, any> = {}

      if (!validateDomainLevel(values.url)) {
        errors.url = { type: 'domainLevel', message: t('commons:domain_level_error') }
      }

      if (!validateCombinedUrl(values)) {
        errors.url = {
          type: 'combinedLevel',
          message: t('commons:domain_level_error'),
        }
      }

      return { values, errors }
    },
  })

  const navigate = useNavigate()

  const onSubmit: SubmitHandler<PhishingTemplatesCreateForm> = async data => {
    const { domain, ...rest } = data

    const prepareData: IPhishingCreateTemplatesRequest = {
      ...rest,
      url: rest.url ? rest.url + DOT_SIGN + (domain || '') : domain || '',
    }

    const templateData = await patchTemplateTrigger({
      body: prepareData,
      id: template_id,
    }).unwrap()

    if (!templateData.id) {
      handleResponse(`${t('commons:error_editing_template')}...`)
      return
    }

    navigate(`/lk/admin/phishing/templates/${templateData?.id}`)
  }

  const isFieldsDisabled = false

  useEffect(() => {
    let result: IListItem[] = [...DEFAULT_CATEGORIES]
    if (categoriesData) {
      result = [
        ...DEFAULT_CATEGORIES,
        ...categoriesData.data.map(c => ({ id: c.id, title: c.name })),
      ]
    }
    setCategoriesList(result)
  }, [categoriesData])

  useEffect(() => {
    form.setValue('category', patchData?.category?.id || data?.category?.id || null)
  }, [data, patchData])

  useEffect(() => {
    setDomainsList(domainsData?.domains ?? [])
  }, [domainsData, data])

  useEffect(() => {
    if (!domainsData || !domainsData.domains || !data) return

    const workingData = patchData || data

    const stringWithoutProtocol = workingData.url.replace(regExpDeleteUnnecessaryParts, '')

    const sortedDomains = [...domainsList].sort((a, b) => b.length - a.length)

    const existedDomain =
      strEndsWith(sortedDomains, stringWithoutProtocol) ??
      strEndsWith(domainsData?.domains, stringWithoutProtocol)

    if (existedDomain) {
      form.setValue('domain', existedDomain, { shouldValidate: true })
    } else {
      form.setValue('domain', domainsList[0])
    }
  }, [domainsList])

  const errors = form.formState.errors

  const isUrlExists = form.watch('url')
  return {
    data,
    form,
    onSubmit,
    isFieldsDisabled,
    domainsList,
    categoriesList,
    isCategoriesLoading,
    isDomainsLoading,
    BREADCRUMBS,
    errors,
    isError,
    isPatchError,
    isUrlExists,
    isLoading,
  }
}
