import { PhishingTemplatesCreateForm } from './use-phishing-templates-edit'

const MAX_DOMAIN_PARTS = 3

export function doesNotEndWith(inputString: string, substrings: string[]): boolean {
  for (const substring of substrings) {
    if (inputString.endsWith(substring)) {
      return false
    }
  }
  return true
}

export const validateDomainLevel = (value: string) => {
  if (!value) return true

  const dotCount = (value.match(/\./g) || []).length

  return dotCount <= 1
}

export const validateCombinedUrl = (values: PhishingTemplatesCreateForm) => {
  let combinedUrl = ''
  if (values.url) {
    combinedUrl = values.url + (values.domain ? '.' + values.domain : '')
  } else {
    combinedUrl = values.domain ?? ''
  }

  const parts = combinedUrl.split('.')
  return parts.length <= MAX_DOMAIN_PARTS
}
