// import * as z from 'zod'
// import { zodResolver } from '@hookform/resolvers/zod'
// import { doesNotEndWith } from '../helper'

// const phishingTemplateEditSchema = z.object({
// 	name: z.string().min(1, { message: 'Введите название шаблона...' }),
// 	category: z.string(),
// 	original_url: z.string().nullable(),
// 	url: z
// 		.string()
// 		.refine(category => doesNotEndWith(category, ['ru', 'com']), {
// 			message: 'Для корректной работы ссылка не должна заканчиваться на .ru, .com',
// 		})
// 		.nullable(),
// 	domain: z.string(),
// })

// export const phishingTemplatesEditResolver = zodResolver(phishingTemplateEditSchema)
