import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import { v4 as uuid } from 'uuid'

import { useNotification } from '@/shared/contexts/notifications'
import { Button, HelpIcon, Input } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { themeApi } from '@/entities/themeCourse/model/api'
import styles from './save-theme-as-new.module.scss'

const cx = classNamesBind.bind(styles)

interface SaveAsNewThemeButtonProps {
  onClick?: () => void
  name?: string
}

/**
 * Component that renders a button to save the current theme as a new one.
 * When clicked, it opens a modal dialog for further actions.
 *
 * @param {string} [props.name] - The name of the theme to be saved as new.
 */
export const SaveAsNewThemeButton = ({ name }: SaveAsNewThemeButtonProps) => {
  const { t } = useTranslation('pages__create-theme')
  const [isShowSaveAsNewModal, setIsShowSaveAsNewModal] = useState<boolean>(false)

  const handleSaveAsNew = () => {
    setIsShowSaveAsNewModal(true)
  }

  return (
    <>
      <div className={cx('form__actions-wrapper')}>
        <Button onClick={handleSaveAsNew}>{t('save_as_new')}</Button>
        <HelpIcon text={t('save_as_new_hint')} />
      </div>

      <SaveAsNewPromptModal
        open={isShowSaveAsNewModal}
        onClose={() => setIsShowSaveAsNewModal(false)}
        name={name}
      />
    </>
  )
}

interface SaveAsNewPromptModalProps {
  open: boolean
  onClose: () => void
  onSubmit?: () => void
  name?: string
}

/**
 * @function SaveAsNewPromptModal
 * @description The modal window for saving a new version of the theme.
 * @returns {JSX.Element} The modal window.
 */
export const SaveAsNewPromptModal = ({
  name: initialName = '',
  onClose,
  open,
}: SaveAsNewPromptModalProps) => {
  const { theme_id = '', section_id = '', course_id = '' } = useParams()
  const { t } = useTranslation('pages__create-theme')
  const { add, handleErrorResponse } = useNotification()
  const [name, setName] = useState<string>(initialName)
  const handleInput = (value: string) => setName(value)
  const [saveThemeInThemeModule, { isLoading }] = themeApi.useSaveThemeInThemeModuleMutation()

  useEffect(() => {
    setName(initialName)
  }, [initialName])

  const handlerSubmit = async () => {
    await saveThemeInThemeModule({ new_title: name, theme_id, course_id, section_id })
      .unwrap()
      .then(() => {
        add({ id: uuid(), message: t('notify.save_theme_as_new'), status: 'success' })
        setName(initialName)
        onClose()
      })
      .catch(e => handleErrorResponse(e))
    onClose?.()
  }

  return (
    <Modal setActive={onClose} active={open} className={cx('modal__content')}>
      <h2 className={cx('modal__title')}>{t('save_as_new_title')}</h2>

      <Input
        fullWidth
        value={name}
        onChange={handleInput}
        label={t('commons:name')}
        placeholder={t('commons:name')}
      />

      <div className={cx('modal__actions-wrapper')}>
        <Button size='veryBig' onClick={onClose} type='button' color='gray' fullWidth>
          {t('commons:cancel')}
        </Button>
        <Button
          size='veryBig'
          type='button'
          onClick={handlerSubmit}
          className={cx('button', 'dialogFooter')}
          loading={isLoading}
          fullWidth
        >
          {t('commons:save')}
        </Button>
      </div>
    </Modal>
  )
}
