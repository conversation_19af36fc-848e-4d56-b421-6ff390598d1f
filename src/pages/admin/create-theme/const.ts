import i18next from 'i18next'

export const getPresentationDefaultData = () => {
  const currentTime = Date.now()

  const enterTitle = i18next.t('pages__create-theme:enter_title', 'Введите заголовок...')
  const enterSlideText = i18next.t('pages__create-theme:enter_slide_text', 'Введите текст слайда')

  return {
    time: currentTime,
    blocks: [
      {
        id: 'r2SYF57Wa7',
        type: 'header',
        data: {
          text: enterTitle,
          level: 2,
        },
      },
      {
        id: 'FNEx521R7I',
        type: 'image',
        data: {
          events: [],
          block_id: '12038u12038u123',
        },
      },
      {
        id: 'TmE59enIM0',
        type: 'audio',
        data: {
          url: null,
          type: null,
        },
      },
      {
        id: 'QUjR4BxZH8',
        type: 'paragraph',
        data: {
          text: enterSlideText,
        },
      },
    ],
    version: '2.30.2',
  }
}

export const getVideoDefaultData = () => {
  const currentTime = Date.now()

  const enterTitle = i18next.t('pages__create-theme:enter_title', 'Введите заголовок...')
  const enterVideoDescription = i18next.t(
    'pages__create-theme:enter_video_description',
    'Введите описание видео...',
  )

  return {
    time: currentTime,
    blocks: [
      {
        id: 'hO0SlYG9QW',
        type: 'header',
        data: {
          text: enterTitle,
          level: 2,
        },
      },
      {
        id: '_0YJYYKWmh',
        type: 'paragraph',
        data: {
          text: enterVideoDescription,
        },
      },
      {
        id: 'pvhxmO5_d5',
        type: 'video',
        data: {
          events: [],
        },
      },
    ],
    version: '2.30.5',
  }
}

export const PresentationDefaultData = getPresentationDefaultData()
export const VideoDefaultData = getVideoDefaultData()
