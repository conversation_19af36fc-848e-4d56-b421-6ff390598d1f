.breadcrumbs {
  margin-bottom: 16px;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.block {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 16px;

  padding: 24px;
  background-color: var(--white);
  border-radius: 12px;

  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .download {
      font: var(--font-text-2-normal);
      color: var(--color-primary);
      display: flex;
      align-items: center;
      gap: 6px;
    }
  }
}

.itemWrapper {
  align-self: stretch;
}

.name {
  font: var(--font-title-3-normal);
  font-weight: 600;
}

.button {
  margin: 200px 0 0 auto;
}

.title__wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
