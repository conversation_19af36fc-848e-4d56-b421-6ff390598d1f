export declare namespace PhishingTemplateProps {
  interface Own {
    className?: string;
  }
  type StaticticsLinesMap = number[];

  interface DemoPageProps {
    id: string;
    url?: string;
    can_edit: boolean;
  }

  interface StaticticsProps {
    id: string;
    name: string;
    isExistsAttachment?: boolean;
  }

  interface EmailsProps {
    emails: Omit<IEmail, "created_at" | "updated_at" | "subject" | "html">[];
    can_edit?: boolean;
    can_delete?: boolean;
  }

  type Props = Own;
}

export {};
