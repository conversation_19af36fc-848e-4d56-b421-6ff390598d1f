@use '../../../shared/assets/styles/mixins/text';
@use '../../../shared/assets/styles/mixins/icons';

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.actions {
  &__wrapper {
    align-items: center;
    display: flex;
    justify-content: space-between;

    margin-top: -15px;
  }

  &__logo {
    height: 32px;
    width: 32px;
    &__wrapper {
      align-items: center;
      display: flex;
      gap: 12px;
    }
  }

  &__title {
    color: var(--color-gray-90);
    font: var(--font-title-2-medium);
  }
}

.template {
  &__wrapper {
    display: grid;
    gap: 20px;
    grid-template-columns: auto minmax(260px, 290px);
  }
}

.demo-page {
  &__wrapper {
    border-radius: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  &__title-wrapper {
    align-items: center;
    display: flex;
    gap: 8px;
  }

  &__card {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  &__header {
    align-items: center;

    background: var(--color-gray-50);
    border-top-left-radius: 16px;

    border-top-right-radius: 16px;

    display: flex;
    justify-content: center;
    padding: 4px 8px;
    position: relative;
  }

  &__badge {
    background-color: var(--color-gray-40);
    border-radius: 4px;

    color: var(--color-gray-70);
    font: var(--font-text-2-normal);
    padding: 0 12px;
    text-align: center;
    width: 50%;
  }
  &__controls {
    border-radius: 100%;
    height: 8px;
    width: 8px;
    &__wrapper {
      align-items: center;

      display: flex;
      gap: 4px;
      left: 16px;
      position: absolute;
      top: 12px;
    }
  }

  &__controls:nth-child(1) {
    background: #ff5f57;
  }

  &__controls:nth-child(2) {
    background: #febc2e;
  }

  &__controls:nth-child(3) {
    background: #28c840;
  }

  &__content {
    border-bottom-left-radius: 16px;

    border-bottom-right-radius: 16px;
    flex-grow: 1;
    height: 480px;
    overflow-y: auto;
    width: 100%;

    &.empty {
      align-items: center;
      display: flex;
      height: 100%;
      justify-content: center;
      width: 100%;
    }
  }
}

.statistics {
  display: flex;
  flex-direction: column;

  gap: 24px;

  &__wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;

    height: 100%;

    iframe {
      min-height: 100%;

      pointer-events: none;
      width: 100%;
    }
  }

  &__chart {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;

    &__item {
      border-radius: 4px;
      height: 24px;
      min-width: 6px;

      &:nth-child(1) {
        background: var(--color-statistics-neutral);
      }

      &:nth-child(2) {
        background: var(--color-statistics-complementary);
      }

      &:nth-child(3) {
        background: var(--color-statistics-warning);
      }

      &:nth-child(4) {
        background: var(--color-statistics-bad);
      }
    }
  }

  &__chart-descr {
    display: flex;
    flex-direction: column;
    gap: 8px;

    margin-top: auto;
    width: 100%;

    &__item {
      align-items: center;

      border-radius: 4px;
      display: flex;
      gap: 12px;
      min-width: 6px;

      p {
        color: var(--color-gray-80);
        flex-grow: 1;
        font: var(--font-caption-1-normal);
        text-align: left;
      }

      span {
        color: var(--color-gray-90);
        flex-shrink: 0;
        font: var(--font-caption-1-medium);
      }
    }
    .dot {
      border-radius: 100%;
      height: 6px;
      width: 6px;

      &.dot-1 {
        background: var(--color-statistics-neutral);
      }

      &.dot-2 {
        background: var(--color-statistics-complementary);
      }

      &.dot-3 {
        background: var(--color-statistics-warning);
      }

      &.dot-4 {
        background: var(--color-statistics-bad);
      }
    }
  }

  &__content {
    height: 100%;
    padding: 24px;

    text-align: center;

    &__descr {
      align-items: center;
      display: flex;
      gap: 12px;

      h2 {
        color: var(--color-gray-90);
        font: var(--font-title-1-medium);
      }
      p {
        color: var(--color-gray-90);
        font: var(--font-caption-1-demibold);
        text-align: left;
      }
    }

    &__wrapper {
      display: flex;
      flex-direction: column;
      gap: 24px;

      height: 100%;
    }

    &__plug {
      align-items: center;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &__icon {
        height: 48px !important;
        width: 48px !important;

        svg {
          height: 100% !important;
          width: 100% !important;
        }
      }

      &__title {
        color: var(--color-gray-70);
        font: var(--font-text-2-normal);
      }
    }

    &__bottom {
      align-items: center;
      display: flex;
      gap: 12px;
      justify-content: center;
      padding: 24px;
    }
  }
}

.emails {
  &__wrapper {
    display: flex;
    flex-direction: column;
    gap: 12px;

    height: 600px;
  }
  &__content {
    display: grid;
    flex-grow: 1;

    gap: 24px;
    grid-template-columns: minmax(281px, 1fr) 1px 2fr;
    overflow: hidden;
    padding: 24px 12px;
    position: relative;
    width: 100%;

    &__list {
      overflow-y: auto;
      width: 100%;

      &__wrapper {
        align-items: flex-start;

        display: flex;
        flex-direction: column;
        gap: 12px;
        height: 100%;
        justify-content: start;
        overflow: hidden;
        position: relative;
      }

      &__item {
        align-items: center;

        border-radius: 12px;

        cursor: pointer;
        display: flex;
        justify-content: space-between;
        padding: 12px 16px;
        width: 100%;

        &__title {
          &__wrapper {
            display: flex;
            gap: 4px;
          }
          @include text.max-lines(1);
          color: var(--color-gray-90);
          font: var(--font-text-2-normal);
        }
        &__subtitle {
          @include text.max-lines(1);
          color: var(--color-gray-70);
          font: var(--font-text-2-normal);
        }

        &__actions {
          align-items: center;
          display: none;
          margin-left: auto;
        }

        &:hover {
          background-color: var(--color-gray-40);

          .emails__content__list__item__actions {
            display: flex;
          }
        }

        &.active {
          background-color: var(--color-gray-40);
        }
      }

      &__empty {
        align-items: center;
        display: flex;
        height: 100%;
        justify-content: center;
        width: 100%;
      }
    }

    &__mail {
      border-radius: 16px;
      overflow-y: scroll;
      width: 100%;

      &_no-data {
        align-items: center;

        background-color: var(--color-gray-40);
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow-y: none;
      }
      // height: 100%;

      &__icon {
        height: 48px !important;
        width: 48px !important;

        svg {
          height: 100% !important;
          width: 100% !important;
        }
      }

      &__title {
        color: var(--color-gray-60);

        font: var(--font-text-2-normal);
      }
    }
  }

  &__attachments {
    display: flex;
    flex-direction: column;
    gap: 4px;
    justify-content: flex-start;
    margin: 10px 20px;
    &__item {
      align-items: center;
      display: flex;
      gap: 8px;
    }

    .file-name {
      color: var(--color-gray-90);
      font: var(--font-caption-1-normal);
    }
  }
}

.title {
  color: var(--color-gray-90);
  font: var(--font-title-4-medium);
}

.success {
  color: var(--color-primary, #3dbc87);
  font: var(--font-text-2-medium);
  padding-right: 2px;
}

.create-btn {
  align-items: center;

  border-radius: 6px;
  display: flex;
  gap: 4px;

  padding: 4px 8px;

  transition: 0.2s ease;

  &.hide {
    display: none;
  }

  &:hover {
    background-color: var(--color-primary-20);
  }
  &:active {
    background-color: var(--color-primary-30);
  }
}

.delete-icon:hover {
  transition: all 0.2s ease;

  &:hover {
    @include icons.color(red);
    background-color: rgba(241, 70, 90, 0.15) !important;
  }
}
