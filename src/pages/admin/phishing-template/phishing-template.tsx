/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  FC,
  JSXElementConstructor,
  Key,
  ReactElement,
  ReactNode,
  ReactPortal,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react'

import { PhishingTemplateProps } from './phishing-template.d'
import { Breadcrumbs, ButtonIcon, Card, Divider, Loader, FileIcon } from '@/shared/ui'
import PlusIcon from '@/shared/ui/Icon/icons/components/PlusIcon'
import ChartIcon from '@/shared/ui/Icon/icons/components/ChartIcon'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import ImageIcon from '@/shared/ui/Icon/icons/components/ImageIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { TemplateLogo, PagePreview } from '@/shared/components'
import { DeleteModal } from '@/shared/modals/delete-modal'

import styles from './phishing-template.module.scss'
import classNamesBind from 'classnames/bind'
import { useNavigate, useParams } from 'react-router-dom'
import { EAttachments, type IEmail, phishingQueries, phishingMutations } from '@/entities/phishing'
import { useTranslation } from 'react-i18next'
import { useAppDispatch } from '@/store'
import { UnknownAction } from '@reduxjs/toolkit'

const cx = classNamesBind.bind(styles)

export const PhishingTemplateDemoPage = ({
  id,
  url,
  can_edit,
}: PhishingTemplateProps.DemoPageProps) => {
  const { data, isLoading, isError, isSuccess } = phishingQueries.useGetPageByIdQuery(id, {
    skip: !id,
  })
  const wrapperRef = useRef<HTMLDivElement>(null)

  const { t } = useTranslation('pages__phishing')
  const { template_id } = useParams()
  const navigate = useNavigate()

  const onPageCreate = () => {
    navigate(`/lk/admin/phishing/templates/${template_id}/page/create`)
  }

  const onPageEdit = () => {
    navigate(`/lk/admin/phishing/templates/${template_id}/page/${id}/edit`)
  }

  return (
    <section className={cx('demo-page__wrapper')}>
      <div className={cx('demo-page__title-wrapper')}>
        <h2 className={cx('title')}>{t('phishing_page')}</h2>

        {can_edit && data && data?.html !== null && (
          <ButtonIcon
            onClick={onPageEdit}
            className={cx('demo-page__edit')}
            size='28'
            iconSize='20'
            title={`${t('commons:edit_phishing_page')} ${data?.name}`}
            icon='editBold'
          />
        )}
      </div>
      <Card className={cx('demo-page__card')}>
        <div className={cx('demo-page__header')}>
          <div className={cx('demo-page__controls__wrapper')}>
            <div className={cx('demo-page__controls')}></div>
            <div className={cx('demo-page__controls')}></div>
            <div className={cx('demo-page__controls')}></div>
          </div>
          <div className={cx('demo-page__badge')}>{url || t('loading_domain')}</div>
        </div>
        <div ref={wrapperRef} className={cx('demo-page__content', !data?.html && 'empty')}>
          {id ? (
            <>
              {isLoading && <Loader size='56' />}
              {isError && <Loader size='56' loading={false} error />}
              {isSuccess && data?.html && <PagePreview html={data?.html} viewportWidth={1280} />}
            </>
          ) : (
            <button
              onClick={onPageCreate}
              className={cx('create-btn', {
                hide: !can_edit,
              })}
            >
              <IconWrapper color='primary'>
                <PlusIcon />
              </IconWrapper>
              <span className={cx('success')}>{t('create_phishing_page')}</span>
            </button>
          )}
        </div>
      </Card>
    </section>
  )
}

export const PhishingTemplateStatistics = ({
  id,
  name,
  isExistsAttachment,
}: PhishingTemplateProps.StaticticsProps) => {
  const { data, isLoading, isError, isSuccess } = phishingQueries.useGetTemplateStatisticQuery(id, {
    skip: !id,
  })
  const [staticticsLines, setStaticticsLines] = useState<PhishingTemplateProps.StaticticsLinesMap>(
    [],
  )
  const { t } = useTranslation('pages__phishing')

  useEffect(() => {
    if (!data) return

    setStaticticsLines([
      100,
      Math.max(data.opened),
      Math.max(data.clicked),
      Math.max(data.entered_data),
    ])
  }, [data])

  return (
    <section className={cx('statistics')}>
      <section className={cx('statistics__wrapper')}>
        <h2 className={cx('title')}>{t('commons:statistics')}</h2>
        <Card className={cx('statistics__content')}>
          {isLoading && <Loader size='56' />}
          {isError && (
            <div>
              <Loader size='56' loading={false} error />
              <p className='error-text'>{t('commons:loading_error')}...</p>
            </div>
          )}
          {isSuccess && !data && (
            <div className={cx('statistics__content__plug')}>
              <IconWrapper color='gray70' className={cx('statistics__content__plug__icon')}>
                <ChartIcon />
              </IconWrapper>
              <h3 className={cx('statistics__content__plug__title')}>
                {t('template_not_have_statistics')}
              </h3>
            </div>
          )}
          {staticticsLines.length > 0 && (
            <div className={cx('statistics__content__wrapper')}>
              <div className={cx('statistics__content__descr')}>
                <h2>{data?.entered_data || 0}%</h2>
                <p>
                  {t('employees_fall_scams')} {name}
                </p>
              </div>

              {staticticsLines.length && (
                <ul className={cx('statistics__chart')} style={{ width: '100%' }}>
                  {staticticsLines.map((s, i) => (
                    <li
                      key={`${s}-${i}`}
                      className={cx('statistics__chart__item')}
                      style={{ width: s + '%' }}
                    ></li>
                  ))}
                </ul>
              )}
              <ul className={cx('statistics__chart-descr')}>
                <li className={cx('statistics__chart-descr__item')}>
                  <div className={cx('dot', 'dot-1')}></div>
                  <p>{t('total_letters')}</p>
                </li>
                <li className={cx('statistics__chart-descr__item')}>
                  <div className={cx('dot', 'dot-2')}></div>
                  <p>{t('open_letter')}</p>
                  <span>{staticticsLines[1]}%</span>
                </li>
                <li className={cx('statistics__chart-descr__item')}>
                  <div className={cx('dot', 'dot-3')}></div>
                  <p>{t('follow_link')}</p>
                  <span>{staticticsLines[2]}%</span>
                </li>
                <li className={cx('statistics__chart-descr__item')}>
                  <div className={cx('dot', 'dot-4')}></div>
                  <p>{t('entered_data')}</p>
                  <span>{staticticsLines[3]}%</span>
                </li>
              </ul>
            </div>
          )}
        </Card>
      </section>

      <Card className={cx('statistics__content__bottom')}>
        {!isLoading && isExistsAttachment && !Number.isNaN(data?.opened_attachment) && (
          <div className={cx('statistics__content__descr')}>
            <h2>{data?.opened_attachment || 0}%</h2>
            <p>
              {t('employees_open_attachments')} {name}
            </p>
          </div>
        )}
        {!isExistsAttachment && data && (
          <>
            <IconWrapper color='gray70' className={cx('statistics__content__plug__icon')}>
              <ChartIcon />
            </IconWrapper>
            <h3 className={cx('statistics__content__plug__title')}>{t('no_attachments')}</h3>
          </>
        )}
        {isLoading && <Loader size='56' />}
        {isError && (
          <div>
            <Loader size='56' loading={false} error />
            <p className='error-text'>{t('commons:loading_error')}...</p>
          </div>
        )}
      </Card>
    </section>
  )
}

export const PhishingTemplateEmails = ({
  emails,
  can_delete,
  can_edit,
}: PhishingTemplateProps.EmailsProps) => {
  const { t } = useTranslation('pages__phishing')
  const [activeEmail, setActiveEmail] = useState(emails[0])
  const { data, isError, isFetching, isSuccess } = phishingQueries.useGetEmailQuery(
    activeEmail?.id,
    {
      skip: !activeEmail?.id,
      refetchOnMountOrArgChange: true,
    },
  )

  const dispatch = useAppDispatch()
  const [deleteTrigger] = phishingMutations.useDeleteEmailsMutation()
  const [deleteModal, setDeleteModal] = useState(false)
  const [deletedId, setDeletedId] = useState<string>()
  const { template_id } = useParams()
  const navigate = useNavigate()

  const onEmailCreate = async () => {
    navigate(`/lk/admin/phishing/templates/${template_id}/email/create`)
  }

  const onChangePage = (e: IEmail) => setActiveEmail(e)

  const onItemEdit = (id: string) => {
    navigate(`/lk/admin/phishing/templates/${template_id}/email/${id}/edit`)
  }
  const onItemDelete = async (id: string | undefined | null) => {
    if (!id) return Promise.resolve()

    await deleteTrigger(id)

    if (!template_id) return

    dispatch(
      phishingQueries.util.updateQueryData('getTemplateById', template_id, draft => ({
        ...draft,
        emails: draft.emails.filter(item => item.id !== id),
      })) as unknown as UnknownAction,
    )
    dispatch(
      phishingQueries.util.updateQueryData(
        'getEmail',
        id,
        () => ({}) as unknown as IEmail,
      ) as unknown as UnknownAction,
    )

    setTimeout(() => setActiveEmail(emails?.[0]), 50)
  }

  return (
    <div className={cx('emails__wrapper')}>
      <h2 className={cx('title')}>{t('letters')}</h2>
      <Card className={cx('emails__content')}>
        {(!emails || emails?.length === 0) && isFetching && (
          <div className={cx('emails__content__list__empty')}>
            <Loader size='56' />
          </div>
        )}
        {can_edit && (!emails || emails?.length === 0) && isError && (
          <div className={cx('emails__content__list__empty')}>
            <button onClick={onEmailCreate} className={cx('create-btn')}>
              <IconWrapper color='primary'>
                <PlusIcon />
              </IconWrapper>
              <span className={cx('success')}>{t('commons:create_letter')}</span>
            </button>
          </div>
        )}
        {emails.length > 0 ? (
          <div className={cx('emails__content__list__wrapper')}>
            {can_edit && (
              <button onClick={onEmailCreate} className={cx('create-btn')}>
                <IconWrapper color='primary'>
                  <PlusIcon />
                </IconWrapper>
                <span className={cx('success')}>{t('commons:create_letter')}</span>
              </button>
            )}
            {can_delete && (
              <DeleteModal
                text={t('confirm_delete')}
                id={deletedId}
                active={deleteModal}
                setActive={setDeleteModal}
                onClose={onItemDelete}
              />
            )}
            <ul className={cx('emails__content__list')}>
              {emails.map(e => (
                <li
                  role='button'
                  onClick={() => onChangePage(e as IEmail)}
                  key={e.id}
                  className={cx('emails__content__list__item', e.id === activeEmail.id && 'active')}
                >
                  <div>
                    <div className={cx('emails__content__list__item__title__wrapper')}>
                      <p className={cx('emails__content__list__item__title')}>{e.name}</p>
                      {e?.attachments?.length > 0 && (
                        <IconWrapper size='20' color='gray80'>
                          <ClipBoldIcon />
                        </IconWrapper>
                      )}
                    </div>
                    <p className={cx('emails__content__list__item__subtitle')}>{e.sender}</p>
                  </div>
                  <div className={cx('emails__content__list__item__actions')}>
                    {can_delete && (
                      <ButtonIcon
                        onClick={(event: { stopPropagation: () => void }) => {
                          event.stopPropagation()
                          onItemEdit(e.id)
                        }}
                        color='gray'
                        size='32'
                        iconSize='24'
                        icon='editBold'
                      />
                    )}
                    {can_edit && (
                      <ButtonIcon
                        onClick={(event: { stopPropagation: () => void }) => {
                          event.stopPropagation()
                          setDeletedId(() => e.id)
                          setDeleteModal(() => true)
                        }}
                        className={cx('delete-icon')}
                        color='gray'
                        size='32'
                        iconSize='24'
                        icon='trashBold'
                      />
                    )}
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className={cx('emails__content__list__empty')}>
            <button
              onClick={onEmailCreate}
              className={cx('create-btn', {
                hide: !can_edit,
              })}
            >
              <IconWrapper color='primary'>
                <PlusIcon />
              </IconWrapper>
              <span className={cx('success')}>{t('commons:create_letter')}</span>
            </button>
          </div>
        )}
        <div>
          <Divider direction='vertical' />
        </div>
        <div
          className={cx('emails__content__mail', !data?.html && 'emails__content__mail_no-data')}
        >
          {isFetching && <Loader size='56' />}
          {isError && <Loader size='56' loading={false} error />}
          {isSuccess && !isFetching && data?.html && (
            <PagePreview key={emails.length} html={data.html} viewportWidth={700} />
          )}
          {((!data?.html && !isFetching && !isError && !isSuccess) || emails.length === 0) && (
            <>
              <IconWrapper className={cx('emails__content__mail__icon')} color='gray60'>
                <ImageIcon />
              </IconWrapper>
              <h3 className={cx('emails__content__mail__title')}>
                {t('commons:preview_created_emails')}
              </h3>
            </>
          )}
          {activeEmail?.attachments.length > 0 && (
            <ul className={cx('emails__attachments')}>
              {activeEmail?.attachments?.map(
                (attachment: {
                  id: Key | null | undefined
                  type: EAttachments
                  filename:
                    | string
                    | number
                    | boolean
                    | ReactElement<any, string | JSXElementConstructor<any>>
                    | Iterable<ReactNode>
                    | ReactPortal
                    | Iterable<ReactNode>
                    | null
                    | undefined
                }) => (
                  <li key={attachment.id} className={cx('emails__attachments__item')}>
                    {attachment.type === EAttachments.docx && <FileIcon size='32' type='DOCX' />}
                    {attachment.type === EAttachments.pdf && <FileIcon size='32' type='PDF' />}
                    {attachment.type === EAttachments.xlsx && <FileIcon size='32' type='XLSX' />}
                    <p className={cx('file-name')}>{attachment.filename}</p>
                  </li>
                ),
              )}
            </ul>
          )}
        </div>
      </Card>
    </div>
  )
}

export const PhishingTemplate: FC<PhishingTemplateProps.Props> = props => {
  const { className } = props
  const { template_id = '' } = useParams()
  const { data, isFetching, isError } = phishingQueries.useGetTemplateByIdQuery(template_id)
  const [deleteModal, setDeleteModal] = useState(false)
  const [deleteTrigger] = phishingMutations.useDeletePhishingTemplateMutation()
  const { t } = useTranslation('pages__phishing')

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/templates',
        text: t('commons:templates'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/templates/' + template_id,
        text: data?.name || t('commons:current_template'),
        isLoading: !data?.name,
      },
    ],
    [data?.name, template_id, t],
  )

  const navigate = useNavigate()

  const onItemEdit = () => {
    navigate(`/lk/admin/phishing/templates/${template_id}/edit`)
  }

  const onItemDelete = async (id: string) => {
    await deleteTrigger(id)
      .unwrap()
      .then(() => {
        navigate(`/lk/admin/phishing/templates`)
      })
  }

  if (isFetching) return <Loader size='56' />
  if (isError) return <Loader loading={false} error size='56' />
  if (!data) return <>{t('cannot_template_data')}</>

  return (
    <section className={cx('wrapper', className)}>
      <Breadcrumbs items={BREADCRUMBS} />
      <section className={cx('actions__wrapper')}>
        <div className={cx('actions__logo__wrapper')}>
          <TemplateLogo
            className={cx('actions__logo')}
            name={data?.name}
            alt={data?.name}
            src={data?.logo}
          />
          <h2 className={cx('actions__title')}>{data?.name}</h2>
          {data?.can_edit && (
            <ButtonIcon
              onClick={onItemEdit}
              className={cx('actions__logo__edit')}
              size='32'
              iconSize='28'
              title={`${t('commons:edit')} ${data?.name}`}
              icon='editBold'
            />
          )}
        </div>
        {data?.can_delete && (
          <div>
            <DeleteModal
              text={`${t('commons:confirm_delete_template')} ${data?.name}?`}
              active={deleteModal}
              setActive={setDeleteModal}
              id={template_id}
              onClose={(v: string) => {
                if (v) onItemDelete(v)

                return Promise.resolve()
              }}
            />
            <ButtonIcon
              onClick={() => setDeleteModal(true)}
              className={cx('actions__delete', 'delete-icon')}
              size='36'
              iconSize='28'
              title={`${t('commons:delete')} ${data?.name}`}
              color='gray'
              icon='trashBold'
            />
          </div>
        )}
      </section>
      <div className={cx('template__wrapper')}>
        <PhishingTemplateDemoPage id={data?.page?.id} url={data?.url} can_edit={data.can_edit} />
        <PhishingTemplateStatistics
          isExistsAttachment={data?.emails.some(e => e.attachments.length > 0)}
          name={data?.name}
          id={data?.id}
        />
      </div>
      <PhishingTemplateEmails
        can_delete={data.can_delete}
        can_edit={data.can_edit}
        emails={data?.emails}
      />
    </section>
  )
}
