import styles from './instructions-table.module.scss'
import classNamesBind from 'classnames/bind'
import { FileIcon, Loader } from '@/shared/ui'
import { useInstructionsTable } from './use-instructions-table'
import { memo } from 'react'
import { getExtensionFromUrl } from '@/shared/helpers'
import { getNameFromFilePath, getNameFromFileWithSpliter, getPrettyDateWithOffset } from './helper'
import { useTranslation } from 'react-i18next'
import { InstructionItem } from '@/entities/instructions'
import { useLocaleForDates } from '@/shared/hooks/use-locale-for-dates'

const cx = classNamesBind.bind(styles)

export type ReportsTableProps = {
  data?: InstructionItem[]
  isLoading?: boolean
  isError?: boolean
}

export const InstructionsTable: React.FC<ReportsTableProps> = memo(
  ({ data, isLoading, isError }) => {
    const { t } = useTranslation()
    const { download } = useInstructionsTable()
    const dateLocale = useLocaleForDates()

    return (
      <table className={cx(styles.table, isLoading && 'table__opacity')}>
        <thead>
          <tr>
            <th className={cx('name-cell')}>{t('commons:name_instruction')}</th>
            <th className={cx('status-cell')}>{t('commons:last_update')}</th>
            <th className={cx('open-cell')}>{t('commons:open')}</th>
          </tr>
        </thead>
        <tbody>
          {!isError && !isLoading && !data?.length && (
            <tr className={cx('plug__row')}>
              <td className={cx('plug')} colSpan={4}>
                <Loader error={false} size='56' loading={true} />
              </td>
            </tr>
          )}
          {isError && (
            <tr className={cx('plug__row')}>
              <td className={cx('plug')} colSpan={4}>
                <Loader error={true} size='56' loading={false} />
              </td>
            </tr>
          )}
          {!isError &&
            data?.map(item => {
              const ext = getExtensionFromUrl(item?.file_url || '')

              return (
                <tr key={item.id} className={cx('table-row')}>
                  <td className={cx('name-cell')}>
                    <p className={cx(styles.trancate)}>
                      {getNameFromFileWithSpliter(
                        getNameFromFilePath(item.file_url)?.split('.')?.[0] ?? '',
                      ) || (
                        <span className={cx('underline')}>{t('commons:failed_get_file_name')}</span>
                      )}
                    </p>
                  </td>
                  <td className={cx('status-cell')}>
                    {getPrettyDateWithOffset(new Date(item.created_at), dateLocale)}
                  </td>
                  <td className={cx('open-cell')}>
                    <div
                      className={cx('download')}
                      onClick={() => {
                        if (item?.file_url) {
                          download(
                            item.file_url,
                            item.file_url || item.id + '.' + ext?.toLocaleLowerCase(),
                          )
                        }
                      }}
                    >
                      <FileIcon type={'PDF'} size='32' />
                    </div>
                  </td>
                </tr>
              )
            })}
        </tbody>
      </table>
    )
  },
)
