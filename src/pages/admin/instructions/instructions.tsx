import { InstructionsTable } from './instructions-table'
import { PageTitle } from '@/shared/ui'
import { useTranslation } from 'react-i18next'
import { instructionsApi } from '@/entities/instructions'

export const Instructions = () => {
  const { data } = instructionsApi.useGetInstructionsQuery()
  const { t } = useTranslation()

  return (
    <div>
      <PageTitle>{t('commons:platform_instruction')}</PageTitle>
      <InstructionsTable data={data?.data} />
    </div>
  )
}
