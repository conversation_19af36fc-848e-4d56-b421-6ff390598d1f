import { FC } from 'react'
import styles from './campaign-statistics.module.scss'
import classNamesBind from 'classnames/bind'
import { CampaignStatisticsProps } from './campaign-statistics.d'
import { useCampaignStatistics } from './hooks/use-campaign-statistics'
import { Breadcrumbs } from '@/shared/ui'
import SettingsBoldIcon from '@/shared/ui/Icon/icons/components/SettingsBoldIcon'
import LightningIcon from '@/shared/ui/Icon/icons/components/LightningIcon'
import EmailingIcon from '@/shared/ui/Icon/icons/components/EmailingIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { PhishingCampaignIncidentCard } from '@/shared/components'
import CampaignStatisticsTabs from '../campaign-statistics-tabs'

const cx = classNamesBind.bind(styles)

export const CampaignStatistics: FC<CampaignStatisticsProps.Props> = props => {
  const { className } = props
  const { BREADCRUMBS, campaignData } = useCampaignStatistics()

  return (
    <div className={cx('wrapper', className)}>
      <Breadcrumbs items={BREADCRUMBS} />
      <div className={cx('title__wrapper')}>
        {campaignData?.is_testing && (
          <IconWrapper size='32' color='primary'>
            <SettingsBoldIcon />
          </IconWrapper>
        )}
        {campaignData?.is_autophish && (
          <IconWrapper size='32' color='primary'>
            <LightningIcon />
          </IconWrapper>
        )}
        {campaignData && !campaignData?.is_autophish && !campaignData?.is_testing && (
          <IconWrapper size='32' color='primary'>
            <EmailingIcon />
          </IconWrapper>
        )}
        <h2>{campaignData?.name || ''}</h2>
      </div>
      <PhishingCampaignIncidentCard data={campaignData} className={cx('incident__wrapper')} />
      <CampaignStatisticsTabs />
    </div>
  )
}
