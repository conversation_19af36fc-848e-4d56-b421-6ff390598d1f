/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { phishingMutations } from '@/entities/phishing'

export const useCampaignStatisticsExportModal = () => {
  const { campaign_id = '' } = useParams()

  const [generateReport, { isLoading, isSuccess, reset, isError }] =
    phishingMutations.useCreatePhishingCampaignReportMutation()

  const getGenerateReportId = useCallback(async () => {
    if (!campaign_id) return

    await generateReport({
      campaign_id,
    }).unwrap()
  }, [campaign_id, generateReport])

  useEffect(() => {
    getGenerateReportId()

    return () => {
      reset()
    }
  }, [])

  return {
    isLoading,
    isError,
    isSuccess,
  }
}
