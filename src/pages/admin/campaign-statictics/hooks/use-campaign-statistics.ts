import { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '@/entities/phishing'

export type CampaignStatisticsSearchParams = {
  type: 'users' | 'departments'
  page: number
}

export const useCampaignStatistics = () => {
  const { campaign_id = '' } = useParams()
  const { t } = useTranslation()

  const { data: campaignData } = phishingQueries.useGetPhishingCampaignsByIdQuery(
    { id: campaign_id, params: { by_tag: false } },
    {
      skip: !campaign_id,
    },
  )

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/campaigns',
        text: t('commons:campaigns'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/campaigns/' + campaignData?.id,
        text: campaignData?.name || t('current_newsletter'),
        clickable: true,
        isLoading: !campaignData,
      },
      {
        id: '/lk/admin/phishing/campaigns/' + campaignData?.id + '/statistics',
        text: t('commons:statistics'),
        clickable: true,
        isLoading: !campaignData,
      },
    ],
    [campaignData, t],
  )

  return {
    BREADCRUMBS,
    campaignData,
  }
}
