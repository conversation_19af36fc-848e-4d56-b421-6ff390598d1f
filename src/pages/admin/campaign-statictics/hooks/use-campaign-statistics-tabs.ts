import { useCallback, useMemo, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

type CampaignStatisticsSearchParamsType = 'users' | 'departments'

export type CampaignStatisticsSearchParams = {
  type: CampaignStatisticsSearchParamsType
  page: number
}

export const useCampaignStatisticsTabs = () => {
  const [currentQueryParameters, setSearchParams] = useSearchParams()
  const newQueryParameters: URLSearchParams = new URLSearchParams()

  const ACTUAL_PAGE = Number(currentQueryParameters.get('page')) - 1 || 0
  const type = currentQueryParameters.get('type') || ('users' as CampaignStatisticsSearchParamsType)
  const { t } = useTranslation()

  const TABS = useMemo(
    () => [
      { name: 'users', value: t('commons:employees') },
      { name: 'departments', value: t('commons:departments') },
    ],
    [t],
  )

  const [activeTab, setActiveTab] = useState(
    () => TABS.find(t => t.name === type)?.name || TABS[0].name,
  )

  const onTabChange = (newType: string) => {
    if (!newType) return

    newQueryParameters.set('type', newType)
    newQueryParameters.set('page', '1')
    setSearchParams(newQueryParameters)
  }

  const onChangeActiveTab = useCallback(
    (v: string) => {
      const tab = TABS.find(t => t.name === v)?.name || TABS[0].name || ''
      setActiveTab(tab)
      onTabChange(tab)
    },
    [TABS],
  )

  const isEmployeeTable = type === 'users'
  const isDepartmentTable = type === 'departments'

  return {
    activeTab,
    onChangeActiveTab,
    isEmployeeTable,
    isDepartmentTable,
    TOTAL_ITEMS: 125,
    page: ACTUAL_PAGE,
    TABS,
  }
}
