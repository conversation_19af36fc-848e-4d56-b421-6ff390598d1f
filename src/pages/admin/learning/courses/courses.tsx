import React, { use<PERSON><PERSON>back, useEffect, useMemo, useRef, useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import InfiniteScroll from 'react-infinite-scroll-component'

import { useAppDispatch } from '@/store'
import { Tag } from '@/shared/components'
import { ButtonIcon, Checkbox, LinkButton, SearchInput, Select } from '@/shared/ui'
import EducationMediumIcon from '@/shared/ui/Icon/icons/components/EducationMediumIcon'
import TrashSmallIcon from '@/shared/ui/Icon/icons/components/TrashSmallIcon'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import MoreIcon from '@/shared/ui/Icon/icons/components/MoreIcon'
import ChevroneSmallIcon from '@/shared/ui/Icon/icons/components/ChevroneSmallIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { URLS, getCoursePreview } from '@/shared/configs/urls'
import { OverflowMenu, OverflowProvider } from '@/shared/components/overflow'

import { setSelectedCourses } from '../assign/create/slice'

import { COURSE_TAG_COLOR } from '@/shared/constants'
import { DeleteSelectedCoursesModal } from './compose/modals/delete-all-themes-modal/delete-selected-courses-modal'

import styles from './courses.module.scss'
import classNamesBind from 'classnames/bind'
import { coursesApi } from '@/entities/courses/model/api/courses'
import { useDebounce } from '@/shared/hooks'
import { useEvent } from '@/shared/hooks'
import { IconSortDirection } from '@/shared/components/icon-sort-direction'
import { Course, CourseCard } from '@/entities/courses'
import { resetNewCourseState } from '@/store/slices/new-course'
import { useUserOrganizationId } from '../../../../entities/employee'

const cx = classNamesBind.bind(styles)

const LIMIT = 20

type SortBy = 'title' | 'created_at'
type SortOrder = 'asc' | 'desc'

const Courses: React.FC = () => {
  const { t } = useTranslation('pages__learning__courses')
  const [page, setPage] = useState(1)
  const [totalCoursesCount, setTotalCoursesCount] = useState(0)
  const [renderCourses, setRenderCourses] = useState<Course[] | null>(null)
  const [filterTags, setFilterTags] = useState<string[]>([])
  const [selectedCoursesIds, setSelectedCoursesIds] = useState<string[]>([])
  const [openDeleteSelectedCourses, setOpenDeleteSelectedCourses] = useState(false)
  const [searchInput, setSearchInput] = useState('')
  const debouncedSearch = useDebounce(searchInput, 500)
  const [sortBy, setSortBy] = useState<SortBy>()
  const [sortOrder, setSortOrder] = useState<SortOrder>()

  const navigate = useNavigate()
  const dispatch = useAppDispatch()
  const userOrganizationId = useUserOrganizationId()

  useEffect(() => {
    setRenderCourses(null)
    setPage(1)
  }, [debouncedSearch, sortBy, sortOrder])

  const coursesArgs = useMemo(() => {
    return {
      organization_id: userOrganizationId ?? '',
      limit: LIMIT,
      offset: (page - 1) * LIMIT,
      tags: filterTags,
      search: debouncedSearch,
      sort_by: sortBy,
      sort_order: sortOrder,
    }
  }, [filterTags, page, debouncedSearch, sortBy, sortOrder, userOrganizationId])

  const {
    data: coursesData,
    isLoading: isCoursesDataLoading,
    isFetching: isCoursesDataFetching,
    isError: isCoursesDataError,
  } = coursesApi.useGetCoursesByOrganizationIdQuery(coursesArgs)

  const [copyCourse, { isLoading: isCopyCourseLoading }] = coursesApi.useCopyCourseMutation()
  const [deleteCourses, { isLoading: isDeleteCoursesLoading }] =
    coursesApi.useDeleteCoursesMutation()

  const isNotDeletedCoursesSelected = useMemo(() => {
    if (!renderCourses) return false

    return selectedCoursesIds.some(selectedCourseId => {
      const courseIndex = renderCourses.findIndex(
        renderCourse => renderCourse.id === selectedCourseId,
      )

      return !renderCourses[courseIndex].can_delete
    })
  }, [renderCourses, selectedCoursesIds])

  useEffect(() => {
    if (coursesData?.data && !(isCoursesDataLoading || isCoursesDataFetching)) {
      setRenderCourses(prevCourses => {
        if (!prevCourses) return [...coursesData.data]
        const uniqueIds: { [key: string]: boolean } = {}

        return [...prevCourses, ...coursesData.data].filter(item => {
          if (uniqueIds[item.id]) {
            return false
          } else {
            uniqueIds[item.id] = true
            return true
          }
        })
      })

      setTotalCoursesCount(coursesData.total_count)
    }
  }, [coursesData, isCoursesDataLoading, isCoursesDataFetching])

  const handleCopyCourseClick = async (courseId: string) => {
    await copyCourse({ course_id: courseId })
  }

  const handleTagClick = (tagName: string) => {
    if (filterTags.includes(tagName)) return

    setFilterTags(prev => [...prev, tagName])
    setRenderCourses(null)
    setPage(1)
  }

  const handleFilterTagDelete = (tagName: string) => {
    setFilterTags(prevTags => prevTags.filter(prevTag => prevTag !== tagName))
    setRenderCourses(null)
    setPage(1)
  }

  const handleCourseCheckChange = (tagId: string) => {
    if (selectedCoursesIds.includes(tagId)) {
      setSelectedCoursesIds(prev => prev.filter(prevSelectedId => prevSelectedId !== tagId))
    } else {
      setSelectedCoursesIds(prev => [...prev, tagId])
    }
  }

  const handleDeleteSelectedCoursesClose = () => {
    setOpenDeleteSelectedCourses(false)
  }

  const handleDeleteSelectedCoursesConfirm = useCallback(async () => {
    setOpenDeleteSelectedCourses(false)

    const deleteData = await deleteCourses({
      deleteIds: selectedCoursesIds,
    })

    setRenderCourses(
      prev => prev?.filter(prevCourse => !selectedCoursesIds.includes(prevCourse.id)) ?? [],
    )

    if (!deleteData.error) {
      setSelectedCoursesIds([])
    }
  }, [deleteCourses, selectedCoursesIds])

  const selectedCourses = useMemo(() => {
    if (!renderCourses) return []
    return selectedCoursesIds.map(selectedCourseId =>
      renderCourses.find(renderCourse => renderCourse.id === selectedCourseId),
    )
  }, [renderCourses, selectedCoursesIds])

  const handleAssignCoursesClick = useCallback(() => {
    dispatch(setSelectedCourses(selectedCourses as Course[]))
    navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_CREATE_PAGE)
  }, [dispatch, navigate, selectedCourses])

  const handleSearchChange = (value: string) => {
    setSearchInput(value)
  }

  const onPreviewClick = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>, id: UUID) => {
    e.preventDefault()
    e.stopPropagation()
    navigate(getCoursePreview(id))
  }

  const isMutationLoading = isDeleteCoursesLoading || isCopyCourseLoading
  const isCanLoadMore = !!renderCourses && renderCourses.length < totalCoursesCount

  return (
    <section className={cx('page')}>
      <div className={cx('titleWrap')}>
        <h1 className={cx('title')}>{t('title')}</h1>
        <SearchInput
          className={cx('search')}
          classNameWrapper={cx('searchWrapper')}
          placeholder={t('commons:search_by_course_name')}
          onChange={handleSearchChange}
          value={searchInput}
        />
        <button style={{ padding: 0 }} onClick={() => dispatch(resetNewCourseState())}>
          <LinkButton to={URLS.ADMIN_CREATE_COURSE_PAGE} width='s' className={cx('linkBtn')}>
            {t('create_course')}
          </LinkButton>
        </button>
      </div>
      <div className={cx('sort')}>
        <CoursesSort
          activeSortBy={sortBy}
          activeSortDirection={sortOrder}
          setSortBy={setSortBy}
          setSortDirection={setSortOrder}
        />
      </div>
      {filterTags.length > 0 && (
        <div className={cx('filterTags')}>
          {filterTags.map(filterTag => (
            <Tag
              key={filterTag}
              info={{ id: filterTag, title: filterTag, color: COURSE_TAG_COLOR }}
              onDelete={() => handleFilterTagDelete(filterTag)}
              isActive
            />
          ))}
        </div>
      )}
      {(isCoursesDataLoading || isMutationLoading || isCoursesDataFetching || !renderCourses) && (
        <div className={cx('content')}>
          <CoursesLoadingSkeleton />
        </div>
      )}
      {isCoursesDataError && <p>{t('commons:error_occurred_while_receiving_data')}</p>}
      {renderCourses &&
        renderCourses.length === 0 &&
        !isCoursesDataLoading &&
        !isCoursesDataFetching && <p>{t('not_have_courses')}</p>}
      {!isCoursesDataLoading && !isMutationLoading && renderCourses && renderCourses.length > 0 && (
        <InfiniteScroll
          className={cx('content')}
          dataLength={renderCourses.length}
          next={() => setPage(prevPage => prevPage + 1)}
          hasMore={isCanLoadMore}
          loader={<CoursesLoadingSkeleton key='loader' />}
          scrollableTarget={'page-wrapper'}
        >
          {renderCourses.map(course => (
            <CourseCard
              key={course.id}
              title={course.title}
              imageUrl={course.image_path}
              TopAdornment={
                <div className={cx('courseTopAdornment')}>
                  <Checkbox
                    className={cx('checkbox')}
                    customChecked={selectedCoursesIds.includes(course.id)}
                    onChange={() => handleCourseCheckChange(course.id)}
                  />

                  {course.tags && course.tags.length > 0 && (
                    <div className={cx('courseTags')}>
                      {course.tags.map(tag => (
                        <Tag
                          key={tag}
                          info={{
                            id: tag,
                            title: tag,
                            color: COURSE_TAG_COLOR,
                          }}
                          isActive={true}
                          onClick={() => handleTagClick(tag)}
                        />
                      ))}
                    </div>
                  )}
                </div>
              }
              BottomAdornment={
                <div className={cx('courseBottomAdornment')}>
                  <p className={cx('themesCount')}>
                    {t('themes_count', { count: course.themes_count })}
                  </p>
                  <div className={cx('courseActions')}>
                    {course.can_edit && (
                      <Link to={URLS.ADMIN_EDIT_COURSE_PAGE + `${course.id}`}>
                        <ButtonIcon
                          onClick={() => dispatch(resetNewCourseState())}
                          icon='editBold'
                          iconSize='28'
                          size='28'
                          color='gray70'
                        />
                      </Link>
                    )}
                    <ButtonIcon
                      icon='copy'
                      iconSize='28'
                      size='28'
                      color='gray70'
                      onClick={() => handleCopyCourseClick(course.id)}
                    />
                    <ButtonIcon
                      onClick={e => onPreviewClick(e, course.id)}
                      icon='eye'
                      iconSize='28'
                      size='28'
                      color='gray70'
                      disabled={course.themes_count === 0}
                    />
                  </div>
                </div>
              }
            />
          ))}
        </InfiniteScroll>
      )}
      {selectedCoursesIds.length > 0 && (
        <OverflowProvider
          gapSize={24}
          additionalWidth={68}
          className={cx('actions')}
          dropdownDataId='courses-actions-dropdown'
          items={[
            {
              id: 0,
              content: (
                <div className={cx('actions__counter', 'no-wrap')} key='count'>
                  <span>
                    {t('commons:selected_course_count', { count: selectedCoursesIds.length })}
                  </span>
                </div>
              ),
            },
            {
              id: 1,
              content: (
                <button
                  onClick={handleAssignCoursesClick}
                  className={cx('actions__element', 'no-wrap')}
                  key='education'
                >
                  <IconWrapper size='20'>
                    <EducationMediumIcon />
                  </IconWrapper>
                  <span>{t('commons:assign_course')}</span>
                </button>
              ),
            },
            {
              id: 2,
              content: (
                <button
                  onClick={() => {
                    setOpenDeleteSelectedCourses(true)
                  }}
                  className={cx('actions__element', 'actions__delete', 'no-wrap', {
                    actions__disabled: isNotDeletedCoursesSelected,
                  })}
                  disabled={isNotDeletedCoursesSelected}
                  key='delete'
                >
                  <IconWrapper size='20'>
                    <TrashSmallIcon />
                  </IconWrapper>
                  <span>{t('commons:delete')}</span>
                </button>
              ),
            },
            {
              id: 3,
              content: (
                <button
                  onClick={() => {
                    setSelectedCoursesIds([])
                  }}
                  className={cx('actions__element', 'actions__cancel', 'red')}
                  key='cancel'
                >
                  <IconWrapper color='red' size='20'>
                    <CloseBoldIcon />
                  </IconWrapper>
                  <span className={cx('warn')}>{t('commons:cancel')}</span>
                </button>
              ),
            },
          ]}
        >
          <OverflowMenu
            renderDropdown={items => (
              <>
                <Select
                  disableActiveValue={true}
                  listClassName={cx('select__list')}
                  renderLabel={() => (
                    <button
                      data-id='courses-actions-dropdown'
                      className={cx('actions__element', 'no-wrap')}
                    >
                      <span>{t('commons:more')}</span>
                      <IconWrapper size='20'>
                        <MoreIcon />
                      </IconWrapper>
                    </button>
                  )}
                  wrapperClassName={cx('select')}
                  withoutIcon={true}
                  list={items?.map(i => ({ id: String(i.id), title: '', content: i.content }))}
                />
              </>
            )}
          />
        </OverflowProvider>
      )}
      {openDeleteSelectedCourses && (
        <DeleteSelectedCoursesModal
          open={openDeleteSelectedCourses}
          onClose={handleDeleteSelectedCoursesClose}
          onConfirmClick={handleDeleteSelectedCoursesConfirm}
          selectedCourseCount={selectedCoursesIds.length}
        />
      )}
    </section>
  )
}

const CoursesLoadingSkeleton = () => (
  <>
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
    <CourseCard isLoading className={cx('courseCardSkeleton')} />
  </>
)

const CoursesSort = ({
  activeSortBy = 'created_at',
  activeSortDirection = 'desc',
  setSortBy,
  setSortDirection,
}: {
  activeSortBy?: SortBy
  activeSortDirection?: SortOrder
  setSortBy: React.Dispatch<React.SetStateAction<SortBy | undefined>>
  setSortDirection: React.Dispatch<React.SetStateAction<SortOrder | undefined>>
}) => {
  const { t } = useTranslation('pages__learning__courses')
  const wrapper = useRef<HTMLDivElement>(null)

  const SORT_LABELS: Record<SortBy, string> = useMemo(() => {
    return {
      title: t('sort_by_alphabet'),
      created_at: t('sort_by_created_at'),
    }
  }, [t])

  const [open, setOpen] = useState(false)

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) setOpen(false)
  }, [])

  useEvent('click', handleOutsideClick, window)

  return (
    <div className={cx('wrapper')} ref={wrapper} onClick={() => setOpen(!open)}>
      <div className={cx('text')}>
        {SORT_LABELS[activeSortBy]}
        <IconWrapper color='gray80' className={cx('icon')} direction={!open ? 'right' : 'left'}>
          <ChevroneSmallIcon />
        </IconWrapper>
        <IconSortDirection
          startDirection={'asc'}
          direction={activeSortDirection}
          onChange={(dir, e) => {
            setSortDirection(dir)
            e?.stopPropagation()
          }}
        />
      </div>
      {open && (
        <div className={cx('listWrapper')} style={styles} onClick={e => e.stopPropagation()}>
          <div className={cx('listInner')}>
            {Object.keys(SORT_LABELS).map(s => {
              const sort = s as SortBy
              const isActive = sort === activeSortBy

              return (
                <span
                  key={`list-item-${s}`}
                  className={cx('listItem', { active: isActive })}
                  onClick={() => {
                    setSortBy(sort)
                    setOpen(false)
                  }}
                >
                  {SORT_LABELS[sort]}
                </span>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

export default Courses
