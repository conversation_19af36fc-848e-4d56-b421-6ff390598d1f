import { useTranslation } from 'react-i18next'
import classNamesBind from 'classnames/bind'
import styles from './delete-selected-courses-modal.module.scss'

import { Modal } from '@/shared/components'
import { Button } from '@/shared/ui'

const cx = classNamesBind.bind(styles)

type Props = {
  open: boolean
  selectedCourseCount: number
  onClose: () => void
  onConfirmClick: () => void
}

export const DeleteSelectedCoursesModal = ({
  open,
  selectedCourseCount,
  onClose,
  onConfirmClick,
}: Props) => {
  const { t } = useTranslation('pages__learning__courses')

  return (
    <Modal setActive={onClose} active={open} className={cx('dialog')}>
      <div className={cx('title')}>{t('delete_selected_courses_title')}</div>
      <p className={cx('description')}>
        {t('delete_selected_courses_description')}{' '}
        <b>{t('commons:course_count', { count: selectedCourseCount })}</b>
      </p>
      <div className={cx('btns')}>
        <Button size='big' className={cx('btn')} type='button' color='gray' onClick={onClose}>
          {t('commons:cancel')}
        </Button>
        <Button size='big' className={cx('btn')} type='button' color='red' onClick={onConfirmClick}>
          {t('commons:delete')}
        </Button>
      </div>
    </Modal>
  )
}
