import { <PERSON>, Mouse<PERSON>vent<PERSON><PERSON><PERSON>, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import styles from './assign-course.module.scss'
import classNamesBind from 'classnames/bind'
import { URLS, getCoursePreview } from '@/shared/configs/urls'
import { Breadcrumbs, Button, Pagination, SearchInput, Radiobox } from '@/shared/ui'
import EyeIcon from '@/shared/ui/Icon/icons/components/EyeIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  handleSelectedCoursesPublic,
  selectCoursePage,
  selectCourseSearch,
  selectSelectedCoursesEntities,
  selectSort,
  setCoursePage,
  setCourseSearch,
  setSelectedCourses,
  setSort,
} from './public-courses-slice'
import { CourseCard } from './course-card'
import { LoadingDataStatus } from '@/shared/components/loading-data-status'
import { useDebounceValue } from 'usehooks-ts'
import { coursesApi, CoursesSortSelect } from '@/entities/courses'
import { Course } from '@/pages/admin/assigned-courses/api'
import { useUserOrganizationId } from '@/entities/employee'

const cx = classNamesBind.bind(styles)

const LIMIT = 12

const ViewCourse: FC<{ id: Course['id']; disabled?: boolean }> = ({ id, disabled = false }) => {
  const navigate = useNavigate()

  const onClick: MouseEventHandler = e => {
    if (disabled) return
    e.preventDefault()
    e.stopPropagation()
    navigate(getCoursePreview(id))
  }

  return (
    <IconWrapper
      disabled={disabled}
      className={cx('icon')}
      onClick={onClick}
      color={'gray70'}
      size='28'
    >
      <EyeIcon />
    </IconWrapper>
  )
}

export const SelectPublicCourses = () => {
  const { t } = useTranslation('pages__learning__select-courses')
  const search = useAppSelector(selectCourseSearch)
  const page = useAppSelector(selectCoursePage)

  const dispatch = useAppDispatch()

  const [debouncedSearch] = useDebounceValue(search, 600)
  const coursesSort = useAppSelector(selectSort)
  const userOrganizationId = useUserOrganizationId()

  const {
    data: courses,
    status,
    isFetching,
    isLoading,
  } = coursesApi.useGetCoursesByOrganizationIdQuery(
    {
      organization_id: userOrganizationId ?? '',

      limit: LIMIT,
      search: debouncedSearch,
      offset: (page - 1) * LIMIT,
      sort_by: coursesSort.by,
      sort_order: coursesSort.order,
    },
    {
      skip: !userOrganizationId,
    },
  )
  const courseEntities = useAppSelector(selectSelectedCoursesEntities)
  const navigate = useNavigate()

  const setPage = (v: number) => {
    dispatch(setCoursePage(v))
  }

  useEffect(() => {
    dispatch(setCoursePage(1))
  }, [debouncedSearch, dispatch])

  const onSearchChange = (value: string) => {
    dispatch(setCourseSearch(value))
  }

  const breadcrumbItems = useMemo(
    () => [
      {
        id: URLS.ADMIN_LEARNING_PAGE,
        text: t('breadcrumbs.assigned_courses'),
        clickable: true,
      },
      {
        id: URLS.ADMIN_CREATE_COURSE_PAGE,
        text: t('breadcrumbs.assigning'),
        clickable: true,
      },
    ],
    [t],
  )

  const hasCheckedCourses = Object.keys(courseEntities).length > 0

  return (
    <div className={cx('page')}>
      <div className={cx('header')}>
        <Breadcrumbs items={breadcrumbItems} className={cx('breadcrumbs')} />
        <div className={cx('header__actions')}>
          <h1 className={cx('title')}>{t('title_public')}</h1>
          <SearchInput
            value={search}
            onChange={onSearchChange}
            className={cx('search')}
            classNameWrapper={cx('searchWrapper')}
            placeholder={t('search.placeholder')}
          />
        </div>
      </div>
      <LoadingDataStatus
        data={courses?.data}
        fetchStatus={status}
        createHandler={() => { }}
        dataLength={courses?.data?.length}
        showCreateHandler={false}
        texts={{
          empty: courses?.data ? t('search.empty') : t('empty'),
        }}
      />
      {courses && courses?.data?.length > 0 && (
        <div>
          <div className={cx('sort')}>
            <CoursesSortSelect
              wrapperClassName={cx('sort__wrapper')}
              setSortBy={by => dispatch(setSort({ by }))}
              setSortOrder={order => dispatch(setSort({ by: coursesSort?.by, order }))}
              sortBy={coursesSort?.by}
              sortOrder={coursesSort?.order}
            />
          </div>
          <ul className={cx('content__courses')}>
            {courses?.data?.map(course => (
              <CourseCard
                key={course.id}
                wrapperProps={{
                  onClick: () => dispatch(handleSelectedCoursesPublic(course)),
                }}
                wrapperClassName={cx('pointer', isFetching ? 'opacity' : '')}
                startActions={
                  <Radiobox
                    onClick={() => dispatch(handleSelectedCoursesPublic(course))}
                    className={cx('radio')}
                    customChecked={!!courseEntities[course.id]}
                  />
                }
                endActions={<ViewCourse id={course.id} disabled={course.themes_count === 0} />}
                course={course}
              />
            ))}
          </ul>
          <Pagination
            currentPage={page - 1}
            limit={LIMIT}
            onChange={p => setPage(p + 1)}
            total={courses?.total_count ?? 0}
            isLoading={isLoading || isFetching}
          />
        </div>
      )}
      <div className={cx('actions')}>
        <Button
          onClick={() => {
            if (!hasCheckedCourses) navigate(-1)
            if (hasCheckedCourses) dispatch(setSelectedCourses([]))
          }}
          type='button'
          size='big'
          color='gray'
        >
          {hasCheckedCourses ? t('actions.cancel') : t('actions.back')}
        </Button>
        <Button
          onClick={() => navigate(URLS.ADMIN_LEARNING_ASSIGNED_COURSE_CREATE_PUBLIC_PAGE)}
          type='button'
          size='big'
          disabled={!hasCheckedCourses}
        >
          {t('actions.continue')}
        </Button>
      </div>
    </div>
  )
}

export default SelectPublicCourses
