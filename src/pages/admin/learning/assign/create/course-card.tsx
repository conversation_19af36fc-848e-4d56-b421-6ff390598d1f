import { DetailedHTMLProps, FC, LiHTMLAttributes, ReactNode } from 'react'
import Svg2 from './svg2.svg'
import styles from './assign-course.module.scss'
import classNamesBind from 'classnames/bind'
import { Course } from '@/entities/courses'

const cx = classNamesBind.bind(styles)

type CourseCardProps = {
  startActions?: ReactNode
  endActions?: ReactNode
  content?: ReactNode
  course: Pick<Course, 'title' | 'image_path'>
  wrapperClassName?: string
  contentClassName?: string
  wrapperProps?: DetailedHTMLProps<LiHTMLAttributes<HTMLLIElement>, HTMLLIElement>
}

export const CourseCard: FC<CourseCardProps> = ({
  wrapperClassName,
  contentClassName,
  course,
  content,
  endActions,
  startActions,
  wrapperProps,
}) => {
  return (
    <li className={cx('courses__item', wrapperClassName)} {...wrapperProps}>
      <div className={cx('courses__content', contentClassName)}>
        {startActions}
        <div className={cx('courses__item__content')}>
          <img
            className={cx('courses__item__img')}
            src={course?.image_path ?? Svg2}
            alt={course.title}
          />
          <span>{course.title}</span>
        </div>
        {endActions}
      </div>
      {content}
    </li>
  )
}
