.form {
  display: flex;
  flex-direction: column;
  gap: 32px;

  &__content {
    display: flex;
    flex-direction: column;
    gap: 32px;
    max-width: 445px;
  }

  &__image {
    max-width: 208px;
  }

  height: 100%;
}

.tooltip {
  display: flex;
  flex-direction: column;
  font: var(--font-caption-2-normal);
  color: var(--white) !important;
  * {
    color: var(--white) !important;
    font-weight: 300 !important;
    font: var(--font-caption-2-normal);
  }

  width: 300px;
  padding: 4px 8px;
}

.targets {
  &__content {
    display: flex;
    gap: 6px;
  }
}

.page {
  display: flex;
  flex-direction: column;
  gap: 32px;

  padding-bottom: 40px;

  overflow-y: auto;
}

.header {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &__actions {
    display: flex;
    gap: 16px;
  }
}

.text-area {
  max-height: 140px;
}

.title {
  font: var(--font-title-2-medium);
  color: var(--color-gray-90);

  &_small {
    font: var(--font-text-2-medium);
    color: var(--color-gray-90);
  }
}

.choose {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: start;
}

.notifications {
  display: flex;
  flex-direction: column;
  gap: 8px;

  &__list {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

.actions {
  display: flex;
  gap: 16px;
  align-items: center;
  margin-left: auto;
}

.courses {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 6px;
  height: 100%;
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;

  .noThemesTooltip {
    width: max-content;
    top: 22px;
    right: 0;
  }

  &__content {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 16px;
  }

  &__actions {
    display: flex;
    gap: 12px;

    &__item {
      cursor: pointer;
    }
  }

  &__item {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 24px;
    background: var(--white);
    padding: 12px 20px;
    border-radius: 16px;
    font: var(--font-text-1-normal);
    color: var(--color-gray-90);
    width: 100%;
    // transition: 0.3s;
    justify-content: flex-start;
    align-items: flex-start;

    &__edit {
      width: 100%;
      padding: 16px 12px;
      background-color: var(--color-gray-50);
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      &__wrapper {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }
    }

    &:hover {
      background: var(--color-gray-20);
    }

    &__img {
      width: 48px;
      height: 48px;
      border-radius: 16px;
      object-fit: cover;
    }
    &__content {
      display: flex;
      align-items: center;
      gap: 24px;
      width: 100%;
    }
  }
}

.pointer {
  cursor: pointer;
}

.opacity {
  opacity: 0.7;
}

.searchWrapper {
  min-width: 316px;

  .search {
    background: #fff;
    width: 100%;
  }
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;

  &__courses {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
}

.sort {
  justify-self: flex-end;
  margin-bottom: 12px;
  margin-left: auto;
  display: flex;
  width: 100%;

  &__wrapper {
    margin-left: auto;
  }
}

.radio {
  flex-shrink: 0;
}
