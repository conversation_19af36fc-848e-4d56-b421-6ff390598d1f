import { z } from 'zod'
import { DASH_DATE_FORMAT, format, startOfDay } from '@/shared/helpers/date'

type GetCourseSchemaProps = {
  coursesRequiredMsg: string
  targetsReq: string
  startDateReq: string
  endDateReq: string
  startDateGreaterThanEndDateMsg: string
}

export const getAssignCourseSchema = (props: GetCourseSchemaProps) =>
  z
    .object({
      need_all: z.boolean().default(false),
      courses: z
        .array(
          z.object({
            course_id: z.string(),
            title: z.string().min(1),
            description: z.string().nullable(),
          }),
        )
        .min(1, { message: props.coursesRequiredMsg }),
      date_start: z.date({ message: props.startDateReq }).transform(str => new Date(str)),
      date_end: z.date({ message: props.endDateReq }).transform(str => new Date(str)),
      image: z.instanceof(File).optional(),
      notifications: z.object({
        assign: z.boolean().optional(),
        lagging: z.boolean().optional(),
      }),
      targets: z.object({
        users: z.array(z.string()).min(0),
        departments: z.array(z.string()).min(0),
        exclude_users_ids: z.array(z.string()).min(0),
      }),
    })
    .superRefine(({ date_end, date_start }, ctx) => {
      const start = format(startOfDay(new Date(date_start)), DASH_DATE_FORMAT)
      const end = format(startOfDay(new Date(date_end)), DASH_DATE_FORMAT)
      if (new Date(start) >= new Date(end)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: props.startDateGreaterThanEndDateMsg,
          path: ['end_date'],
        })
      }

      return z.NEVER
    })
    .superRefine(({ need_all, targets }, ctx) => {
      if (!need_all) {
        const isNotEmpty =
          targets.departments.length > 0 ||
          targets.users.length > 0 ||
          targets.exclude_users_ids.length > 0

        if (!isNotEmpty)
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: props.targetsReq,
            path: ['targets'],
          })
      }

      return z.NEVER
    })

type GetPublicCourseSchemaProps = {
  coursesRequiredMsg: string
}

export const getAssignPublicCourseSchema = (props: GetPublicCourseSchemaProps) =>
  z.object({
    courses: z
      .array(
        z.object({
          course_id: z.string(),
          title: z.string().min(1),
          description: z.string().nullable(),
        }),
      )
      .min(1, { message: props.coursesRequiredMsg }),
    image: z.instanceof(File).optional(),
    notifications: z.object({
      assign: z.boolean().optional(),
    }),
  })
