import { createSlice, PayloadAction, WithSlice } from '@reduxjs/toolkit'
import { ThemesSortBy, ThemesSortDirection } from './types'
import { rootReducer } from '@/store/reducer'
import { TTheme } from '@/entities/themeCourse/model/types'

type InitialState = {
  search: string
  sort: {
    by: ThemesSortBy
    order?: ThemesSortDirection
  }
  viewedTheme?: TTheme
  page: number
  activeId?: UUID
  isOpenDelete: boolean
}

const initialState: InitialState = {
  search: '',
  sort: {
    by: 'title',
  },
  page: 1,
  isOpenDelete: false
}

export const themesSlice = createSlice({
  name: 'themesSlice',
  initialState,
  reducers: {
    setSearch: (state, action: PayloadAction<string>) => {
      state.search = action.payload
    },
    setSortBy: (state, action: PayloadAction<ThemesSortBy>) => {
      state.sort.by = action.payload
    },
    setSortOrder: (state, action: PayloadAction<ThemesSortDirection>) => {
      state.sort.order = action.payload
    },
    setViewedTheme: (state, action: PayloadAction<TTheme | undefined>) => {
      state.viewedTheme = action.payload
    },
    setPage: (state, action: PayloadAction<number>) => {
      state.page = action.payload
    },
    setActiveId: (state, action: PayloadAction<UUID>) => {
      state.activeId = action.payload
    },
    setIsOpenDelete: (state, action: PayloadAction<boolean>) => {
      state.isOpenDelete = action.payload
    },
  },
  selectors: {
    selectSearch: state => state.search,
    selectSortBy: state => state.sort.by,
    selectSortOrder: state => state.sort.order,
    selectViewedTheme: state => state.viewedTheme,
    selectPage: state => state.page,
    selectActiveId: state => state.activeId,
    selectIsOpenDelete: state => state.isOpenDelete
  },
})

declare module '@/store/reducer' {
  export interface LazyLoadedSlices extends WithSlice<typeof themesSlice> {}
}

const injectedThemesSlice = themesSlice.injectInto(rootReducer)

export const { setSearch, setSortBy, setSortOrder, setViewedTheme, setPage, setActiveId, setIsOpenDelete } =
  injectedThemesSlice.actions
export const { selectSortBy, selectSortOrder, selectSearch, selectViewedTheme, selectPage, selectActiveId, selectIsOpenDelete } =
  injectedThemesSlice.selectors
