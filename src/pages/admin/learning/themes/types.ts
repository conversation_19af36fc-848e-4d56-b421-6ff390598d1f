export type ThemesSortBy = 'title' | 'created_at'
export type ThemesSortDirection = 'asc' | 'desc'

export type ThemesByOrganizationResponse = ResponseWithPagination & {
  data: Theme[]
}

export type ThemesByOrganizationRequest = RequestWithPagination & {
  organization_id: string
  search?: string
  sort_by?: ThemesSortBy
  sort_order?: ThemesSortDirection
}

export type Theme = {
  id: string
  title: string
  organization_id: string
  visibility: string
  available_in_demo: boolean
  archived: boolean
  created_at: string
  can_edit: boolean
  can_delete: boolean
}
