/* eslint-disable @typescript-eslint/no-explicit-any */
import classNamesBind from 'classnames/bind'
import React, { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import styles from './themes.module.scss'
import { Link, NavLink, useNavigate, useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useDebounceValue } from 'usehooks-ts'
import { Button, Pagination, SearchInput } from '@/shared/ui'
import ChevroneSmallIcon from '@/shared/ui/Icon/icons/components/ChevroneSmallIcon'
import CopyIcon from '@/shared/ui/Icon/icons/components/CopyIcon'
import CloseBoldIcon from '@/shared/ui/Icon/icons/components/CloseBoldIcon'
import EditBoldIcon from '@/shared/ui/Icon/icons/components/EditBoldIcon'
import EyeIcon from '@/shared/ui/Icon/icons/components/EyeIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { IconSortDirection } from '@/shared/components/icon-sort-direction'
import { useEvent } from '@/shared/hooks'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  selectActiveId,
  selectIsOpenDelete,
  selectPage,
  selectSortBy,
  selectSortOrder,
  selectViewedTheme,
  setActiveId,
  setIsOpenDelete,
  setPage,
  setSearch,
  setSortBy,
  setSortOrder,
  setViewedTheme,
} from './slice'
import { withPreventDefault } from '@/shared/helpers/events'
import { ThemesSortBy, Theme } from './types'
import { useNotification } from '@/shared/contexts/notifications'
import { v4 as uuid } from 'uuid'
import { themesApi } from './api'
import { LoadingDataStatus } from '@/shared/components/loading-data-status'
import { getThemeDetailById, URLS } from '@/shared/configs/urls'
import { themeApi } from '@/entities/themeCourse/model/api'
import { ThemePreview } from '@/entities/themeCourse/ui/theme-preview'
import DeleteConfirmModal from '@/shared/modals/delete-confirm-modal/delete-confirm-modal'
import { useUserOrganizationId } from '../../../../entities/employee'

const cx = classNamesBind.bind(styles)

const ThemesSort = () => {
  const { t } = useTranslation('pages__learning__themes')
  const wrapper = useRef<HTMLDivElement>(null)
  const activeSortDirection = useAppSelector(selectSortOrder)
  const activeSortBy = useAppSelector(selectSortBy)
  const dispatch = useAppDispatch()

  const SORT_LABELS: Record<ThemesSortBy, string> = useMemo(() => {
    return {
      title: t('sort.title'),
      created_at: t('sort.created_at'),
    }
  }, [t])
  const [open, setOpen] = useState(false)

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) setOpen(false)
  }, [])

  useEvent('click', handleOutsideClick, window)

  return (
    <div className={cx('wrapper')} ref={wrapper} onClick={() => setOpen(!open)}>
      <div className={cx('text')}>
        {SORT_LABELS[activeSortBy]}
        <IconWrapper color='gray80' className={cx('icon')} direction={!open ? 'right' : 'left'}>
          <ChevroneSmallIcon />
        </IconWrapper>
        <IconSortDirection
          startDirection={'asc'}
          direction={activeSortDirection}
          onChange={(dir, e) => {
            dispatch(setSortOrder(dir))
            e?.stopPropagation()
          }}
        />
      </div>
      {open && (
        <div className={cx('listWrapper')} style={styles} onClick={e => e.stopPropagation()}>
          <div className={cx('listInner')}>
            {Object.keys(SORT_LABELS).map(s => {
              const sort = s as ThemesSortBy
              const isActive = sort === activeSortBy

              return (
                <span
                  key={`list-item-${s}`}
                  className={cx('listItem', { active: isActive })}
                  onClick={() => {
                    dispatch(setSortBy(sort))
                    setOpen(false)
                  }}
                >
                  {SORT_LABELS[sort]}
                </span>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

const CopyTheme: FC<{ id: Theme['id'] }> = ({ id }) => {
  const { t } = useTranslation('pages__learning__themes')
  const { add } = useNotification()
  const [copyTheme] = themesApi.useCopyThemeByIdMutation()

  return (
    <IconWrapper
      className={cx('icon')}
      onClick={withPreventDefault(() => {
        copyTheme(id).then(() =>
          add({
            message: t('copy_msg'),
            status: 'success',
            id: uuid(),
          }),
        )
      })}
      color='gray70'
      size='28'
    >
      <CopyIcon />
    </IconWrapper>
  )
}

const DeleteTheme: FC<{
  id: Theme['id']
}> = ({ id }) => {
  const dispatch = useAppDispatch()

  const handleClick = withPreventDefault(() => {
    dispatch(setActiveId(id))
    dispatch(setIsOpenDelete(true))
  })
  return (
    <IconWrapper className={cx('icon', 'delete')} onClick={handleClick} color='gray70' size='28'>
      <CloseBoldIcon />
    </IconWrapper>
  )
}

const EditTheme: FC<{ id: Theme['id'] }> = ({ id }) => {
  const navigate = useNavigate()

  return (
    <IconWrapper
      className={cx('icon')}
      onClick={withPreventDefault(() => navigate(getThemeDetailById(id)))}
      color='gray70'
      size='28'
    >
      <EditBoldIcon />
    </IconWrapper>
  )
}

const ViewTheme: FC<{ id: Theme['id'] }> = ({ id }) => {
  const dispatch = useAppDispatch()
  const viewedTheme = useAppSelector(selectViewedTheme)
  const [getThemeData] = themeApi.useLazyGetThemeByIdQuery()

  const isThisTheme = Boolean(viewedTheme?.id === id)

  const handleClickTheme = async () => {
    const data = await getThemeData(id).unwrap()
    await dispatch(setViewedTheme(isThisTheme ? undefined : data))
  }

  return (
    <IconWrapper
      className={cx('icon')}
      onClick={withPreventDefault(() => handleClickTheme())}
      color={isThisTheme ? 'gray90' : 'gray70'}
      size='28'
    >
      <EyeIcon />
    </IconWrapper>
  )
}

const LIMIT = 12

const Themes: React.FC = () => {
  const { t } = useTranslation('pages__learning__themes')
  const [currentQueryParameters, setSearchParams] = useSearchParams()
  const newQueryParameters: URLSearchParams = new URLSearchParams()
  const search = currentQueryParameters.get('search') || ''
  const dispatch = useAppDispatch()
  const [debouncedSearch] = useDebounceValue(search, 600)
  const page = useAppSelector(selectPage)
  const activeSortDirection = useAppSelector(selectSortOrder)
  const activeSortBy = useAppSelector(selectSortBy)
  const activeId = useAppSelector(selectActiveId)
  const isOpenDelete = useAppSelector(selectIsOpenDelete)
  const userOrganizationId = useUserOrganizationId()

  const {
    data: themes,
    status,
    isLoading,
    isFetching,
  } = themesApi.useGetThemesByOrganizationQuery(
    {
      organization_id: userOrganizationId ?? '',
      limit: LIMIT,
      search: debouncedSearch,
      offset: (page - 1) * LIMIT,
      sort_by: activeSortBy,
      sort_order: activeSortDirection,
    },
    {
      skip: !userOrganizationId,
    },
  )
  const viewedTheme = useAppSelector(selectViewedTheme)

  const { add } = useNotification()
  const [deleteTheme] = themesApi.useDeleteThemeByIdMutation()

  const onSubmit = () => {
    if (!activeId) return
    deleteTheme(activeId).then(() =>
      add({
        message: t('delete_msg'),
        status: 'success',
        id: uuid(),
      }),
    )
  }

  useEffect(() => {
    dispatch(setPage(1))
  }, [debouncedSearch, dispatch])

  useEffect(() => {
    return () => {
      dispatch(setPage(1))
    }
  }, [dispatch])

  const onSearchChange = (value: string) => {
    dispatch(setSearch(value))
    newQueryParameters.set('search', value)
    setSearchParams(newQueryParameters)
  }

  const setOpen = (v: boolean) => dispatch(setIsOpenDelete(v))

  const renderThemeContent = (theme: Theme) => (
    <>
      <p className={cx('themes__list__item__text')}>{theme.title}</p>
      <ul className={cx('themes__list__item__actions')}>
        <li>
          <ViewTheme id={theme.id} />
        </li>
        {theme.can_edit && (
          <li>
            <EditTheme id={theme.id} />{' '}
          </li>
        )}
        <li>
          <CopyTheme id={theme.id} />
        </li>
        {theme.can_delete && (
          <li>
            <DeleteTheme id={theme.id} />
          </li>
        )}
      </ul>
    </>
  )

  return (
    <section className={cx('page')}>
      <div className={cx('header')}>
        <h2 className={cx('title')}>{t('title')}</h2>
        <SearchInput
          value={search}
          onChange={onSearchChange}
          className={cx('search')}
          classNameWrapper={cx('searchWrapper')}
          placeholder={t('search.placeholder')}
        />
        <NavLink className={cx('create-theme')} to={URLS.ADMIN_CREATE_THEME_PAGE}>
          <Button>{t('create-theme')}</Button>
        </NavLink>
      </div>
      <LoadingDataStatus
        data={themes?.data}
        fetchStatus={status}
        createHandler={() => {}}
        dataLength={themes?.data?.length}
        showCreateHandler={false}
      />
      {themes && themes?.data?.length > 0 && (
        <section className={cx('themes')}>
          <div className={cx('sort')}>
            <ThemesSort />
          </div>
          <ul className={cx('themes__list')}>
            {themes.data.map(theme => (
              <li key={theme.id} className={cx('themes__list__item')}>
                {theme.can_edit ? (
                  <Link
                    className={cx('themes__list__item__link')}
                    to={getThemeDetailById(theme.id)}
                  >
                    {renderThemeContent(theme)}
                  </Link>
                ) : (
                  <div
                    className={cx('themes__list__item__link', 'themes__list__item__link-disabled')}
                  >
                    {renderThemeContent(theme)}
                  </div>
                )}
                {viewedTheme &&
                  viewedTheme.id === theme.id &&
                  (viewedTheme.steps.length ? (
                    <div className={cx('preview')}>
                      <ThemePreview
                        contentWrapperClassName={cx('preview__content')}
                        themeData={viewedTheme}
                        showPresButton
                      />
                    </div>
                  ) : (
                    <div className={cx('placeholder')}>{t('no_steps_in_theme')}</div>
                  ))}
              </li>
            ))}
          </ul>
          <Pagination
            currentPage={page - 1}
            limit={LIMIT}
            total={themes?.total_count || 0}
            onChange={p => dispatch(setPage(p + 1))}
            isLoading={isLoading || isFetching}
          />
        </section>
      )}
      <DeleteConfirmModal
        open={isOpenDelete}
        setOpen={setOpen}
        title={t('delete_title')}
        description={t('delete_desc')}
        onConfirm={onSubmit}
      />
    </section>
  )
}

export default Themes
