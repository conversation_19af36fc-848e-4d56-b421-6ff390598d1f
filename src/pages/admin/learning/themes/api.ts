import { globalBaseApi } from '@/store/services/endpoints/base'
import { Theme, ThemesByOrganizationRequest, ThemesByOrganizationResponse } from './types'

export const themesApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getThemesByOrganization: builder.query<
      ThemesByOrganizationResponse,
      ThemesByOrganizationRequest
    >({
      query: ({ organization_id, ...params }) => ({
        url: `/learning/api/content/themes/organizations/${organization_id}`,
        params,
      }),
      providesTags: ['themes'],
    }),
    deleteThemeById: builder.mutation<unknown, string>({
      query: theme_id => ({
        url: `/learning/api/content/themes/${theme_id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['themes'],
    }),
    copyThemeById: builder.mutation<unknown, string>({
      query: theme_id => ({
        url: `/learning/api/content/themes/${theme_id}/copy`,
        method: 'POST',
      }),
      invalidatesTags: ['themes'],
    }),
    updateThemeById: builder.mutation<unknown, Theme>({
      query: body => ({
        url: `/learning/api/content/themes/${body.id}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['themes'],
    }),
  }),
  overrideExisting: true,
})
