@use '../../../../shared/assets/styles/mixins/text';
@use '../../../../shared/assets/styles/mixins/colors';
@use '../../../../shared/assets/styles/mixins/media';
@use '../../../../shared/assets/styles/mixins/icons';

.page {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 40px;
}

.title {
  font: var(--font-title-2-medium);
  color: var(--color-gray-90);
}

.preview{
  --preview-height: 900px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  
  &__content {
    max-height: var(--preview-height);
    overflow-y: auto;
  }
}

.header {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.create-theme {
  margin-left: auto;
}

.searchWrapper {
  min-width: 316px;

  .search {
    background: #fff;
    width: 100%;
  }
}

.sort {
  margin-left: auto;

  .wrapper {
    cursor: pointer;

    padding: 4px;
    position: relative;
    .text {
      align-items: center;

      color: var(--color-gray-80, #5c6585);
      display: flex;
      font: var(--font-text-2-medium);
      letter-spacing: 0.13px;
    }
  }

  .listWrapper {
    background: var(--color-surface, #fff);
    border: 1px solid var(--stroke, #ebeff2);
    border-radius: 8px;
    box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1);
    max-width: max-content;
    overflow: -moz-scrollbars-none;
    -ms-overflow-style: none;
    overflow-y: scroll;
    padding: 8px 0;
    position: absolute;
    right: 0;
    top: 100%;
    transform: translateY(4px);

    width: max-content;
    z-index: 10;

    &::-webkit-scrollbar {
      width: 0;
    }

    .listInner {
      height: max-content;
    }
    .listItem {
      color: var(--color-gray-80, #5c6585);

      cursor: pointer;

      display: block;
      font: var(--font-text-2-normal);
      padding: 8px 16px;

      transition: var(--transition);
      &:hover {
        background: var(--color-gray-40, #f0f3f7);

        transition: var(--transition);
      }
      &.active {
        background: var(--color-gray-40, #f0f3f7);

        transition: var(--transition);
      }
    }
  }
}

.themes {
  display: flex;
  flex-direction: column;
  gap: 12px;

  &__list {
    display: flex;
    flex-direction: column;
    gap: 6px;

    &__item {
      &__link {
        padding: 20px;
        display: grid;
        grid-template-columns: 1fr auto;
        align-items: center;
        border: 1px solid var(--color-gray-30);
        background-color: var(--white);
        border-radius: 12px;
        transition: 0.3s;

        &:hover {
          background-color: var(--color-gray-20);
        }

        .themes__list__item__actions {
          display: none;
        }

        &:hover {
          .themes__list__item__actions {
            display: flex;
            flex-direction: row;
            gap: 6px;
            margin-left: 24px;
          }
        }

        &-disabled {
          &:hover {
            background-color: var(--white);
          }

          .themes__list__item__actions {
            cursor: pointer;
          }
        }
      }

      &__text {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        line-height: 28px;
      }

      font: var(--font-text-1-normal);
      color: var(--color-gray-90);
    }
  }
}

.placeholder {
  padding: 8px;
}

.icon {
  border-radius: 4px;

  &:hover {
    transform: scale(1.1);
    background: var(--color-gray-50);
    transition: var(--transition);
  }
}

.delete {
  transition: 0.3s;
  &:hover {
    @include icons.color(red);
  }
}
