import { useCallback, useMemo } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '@/entities/phishing'
import { removeLastSegmentAfterDelimiter } from '@/shared/helpers'

export const usePhishingCampaignDetail = () => {
  const { campaign_id } = useParams()
  const { t } = useTranslation()

  const { data: campaignData } = phishingQueries.useGetPhishingCampaignsByIdQuery(
    { id: campaign_id || '', params: { by_tag: true } },
    {
      skip: !campaign_id,
    },
  )
  const navigate = useNavigate()

  const onTemplateCardClick = useCallback(
    (id: string) => {
      navigate(`/lk/admin/phishing/phishing-by-tags/campaigns/${campaign_id}/${id}/statistics`)
    },
    [navigate, campaign_id],
  )

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/phishing-by-tags',
        text: t('commons:phishing_by_tags'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/phishing-by-tags/' + campaignData?.id,
        text: removeLastSegmentAfterDelimiter(campaignData?.name) || t('current_newsletter'),
        clickable: true,
        isLoading: !campaignData,
      },
    ],
    [campaignData, t],
  )

  return {
    BREADCRUMBS,
    campaignData,
    onTemplateCardClick,
  }
}
