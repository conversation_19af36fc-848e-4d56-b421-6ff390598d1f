import { FC } from 'react'
import styles from './phishing-by-tags-detail-page.module.scss'
import classNamesBind from 'classnames/bind'
import { PhishingCampaignDetailPageProps } from './phishing-by-tags-detail-page.d'
import { Breadcrumbs } from '@/shared/ui'
import SettingsBoldIcon from '@/shared/ui/Icon/icons/components/SettingsBoldIcon'
import LightningIcon from '@/shared/ui/Icon/icons/components/LightningIcon'
import EmailingIcon from '@/shared/ui/Icon/icons/components/EmailingIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { usePhishingCampaignDetail } from './hooks/usePhishingCampaignDetail'
import { PhishingCampaignDetailStatistics } from './compose/PhishingCampaignDetailStatistics'
import { PhishingCampaignTemplateCards } from './compose/PhishingCampaignTemplateCards'
import { PhishingCampaignIncidentCard } from '@/shared/components'
import { useTranslation } from 'react-i18next'
import { removeLastSegmentAfterDelimiter } from '@/shared/helpers'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignByTagsDetailPage: FC<
  PhishingCampaignDetailPageProps.Props
> = props => {
  const { className } = props
  const { t } = useTranslation()
  const { BREADCRUMBS, campaignData, onTemplateCardClick } = usePhishingCampaignDetail()

  const name = removeLastSegmentAfterDelimiter(campaignData?.name)

  return (
    <div className={cx('wrapper', className)}>
      <div className={cx('title__wrapper')}>
        <Breadcrumbs items={BREADCRUMBS} />
        <div className={cx('title__box')}>
          {campaignData?.is_testing && (
            <IconWrapper size='32' color='primary'>
              <SettingsBoldIcon />
            </IconWrapper>
          )}
          {campaignData?.is_autophish && (
            <IconWrapper size='32' color='primary'>
              <LightningIcon />
            </IconWrapper>
          )}
          {campaignData && !campaignData?.is_autophish && !campaignData?.is_testing && (
            <IconWrapper size='32' color='primary'>
              <EmailingIcon />
            </IconWrapper>
          )}
          <h1 className={cx('title')} title={name}>
            {name}
          </h1>
        </div>
      </div>
      <PhishingCampaignIncidentCard
        withEndDate={false}
        dateTooltip={t('commons:start_mailing')}
        infoType='date'
        data={campaignData}
      />
      <PhishingCampaignDetailStatistics data={campaignData} />
      <h2 className={cx('templates__title')}>{t('commons:templates')}</h2>
      <PhishingCampaignTemplateCards onClick={onTemplateCardClick} />
    </div>
  )
}
