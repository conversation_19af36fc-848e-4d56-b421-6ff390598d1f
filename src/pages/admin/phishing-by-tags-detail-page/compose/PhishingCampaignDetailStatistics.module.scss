@use "../../../../shared/assets/styles/mixins/icons";
@use "../../../../shared/assets/styles/mixins/media";

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.title {
  color: var(--color-gray-90);
  font: var(--font-title-4-medium);
  &__wrapper {
    display: flex;
    gap: 24px;
    justify-content: space-between;
  }
}

.action-btn {
  &__text {
    font: var(--font-text-2-medium);
  }
  &:hover {
    transition: all 0.2s ease;

    svg,
    path {
      @include icons.color(var(--color-gray-90));
    }
  }
}

.cards__wrapper {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(4, 1fr);

  @include media.mqXl {
    grid-template-columns: repeat(3, 1fr);
  }

  @include media.mqL {
    grid-template-columns: repeat(2, 1fr);
  }
  @include media.mqM {
    grid-template-columns: repeat(1fr);
  }

  &_attachments {
    grid-template-columns: repeat(3, 1fr);
  }
}

.templates {
  &__title {

    color: var(--color-gray-90);
    font: var(--font-title-4-medium);
    margin-top: 24px;
  }
}
