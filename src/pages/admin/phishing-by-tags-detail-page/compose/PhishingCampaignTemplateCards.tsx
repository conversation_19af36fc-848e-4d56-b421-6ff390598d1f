import styles from './PhishingCampaignTemplateCards.module.scss'
import classNamesBind from 'classnames/bind'
import Skeleton from 'react-loading-skeleton'
import { PhishingCampaignTemplateCard } from '@/shared/components'
import { useNavigate, useParams } from 'react-router-dom'
import { phishingQueries } from '@/entities/phishing'

const cx = classNamesBind.bind(styles)

type Props = {
  onClick?: (id: string) => void
}

export const PhishingCampaignTemplateCards: React.FC<Props> = ({ onClick }) => {
  const { campaign_id = '' } = useParams()

  const { data, isLoading } = phishingQueries.useGetPhishingCampaignsTemplatesByIdQuery(
    { id: campaign_id!, params: { by_tag: true } },
    { skip: !campaign_id },
  )

  const navigate = useNavigate()

  const onHandleClick = (v: string) => {
    if (!onClick) {
      navigate(`/lk/admin/phishing/campaigns/${campaign_id}/${v}/statistics`)
    } else {
      onClick(v)
    }
  }

  if (!isLoading && !data?.length) {
    return null
  }

  return (
    <div className={cx('list')}>
      {!isLoading &&
        data &&
        data.map(item => (
          <PhishingCampaignTemplateCard
            onClick={() => onHandleClick(item.id)}
            key={item.id}
            data={item}
          />
        ))}
      {isLoading &&
        [1, 2, 3, 4].map((_, index) => <Skeleton key={index} width={'100%'} height={'68px'} />)}
    </div>
  )
}
