import { useEffect, useMemo } from 'react'
import styles from './PhishingCampaignDetailStatistics.module.scss'
import classNamesBind from 'classnames/bind'
import { Button } from '@/shared/ui'
import EmailPostedIcon from '@/shared/ui/Icon/icons/components/EmailPostedIcon'
import EmailOpenBoldIcon from '@/shared/ui/Icon/icons/components/EmailOpenBoldIcon'
import Web1BoldIcon from '@/shared/ui/Icon/icons/components/Web1BoldIcon'
import LockIcon from '@/shared/ui/Icon/icons/components/LockIcon'
import ClipBoldIcon from '@/shared/ui/Icon/icons/components/ClipBoldIcon'
import ArrowIcon from '@/shared/ui/Icon/icons/components/ArrowIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { Tabs, PhishingCampaignInfoCard, PhishingCampaignInfoCardProps } from '@/shared/components'
import { EAttackVectorType } from '@/shared/types/enums'
import Skeleton from 'react-loading-skeleton'
import { useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { IPhishingCapmaign } from '@/entities/phishing'

const cx = classNamesBind.bind(styles)

type Props = {
  data?: IPhishingCapmaign
}

export const PhishingCampaignDetailStatistics = ({ data }: Props) => {
  const { campaign_id } = useParams()
  const [currentQueryParameters, setSearchParams] = useSearchParams()
  const searchParams = new URLSearchParams()
  const statisticsTab =
    (currentQueryParameters.get('statisticsTab') as EAttackVectorType) || EAttackVectorType.form
  const { t } = useTranslation()

  const TABS = useMemo(() => {
    const result = []

    if (data?.attack_vectors?.find(v => v.type === EAttackVectorType.form)) {
      result.push({ name: EAttackVectorType.form, value: t('commons:phishing') })
    }
    if (data?.attack_vectors?.find(v => v.type === EAttackVectorType.attachments)) {
      result.push({ name: EAttackVectorType.attachments, value: t('commons:attachments') })
    }

    return result
  }, [data?.attack_vectors, t])

  useEffect(() => {
    const searchParams = new URLSearchParams(currentQueryParameters)
    const hasTab = searchParams.get('statisticsTab')
    if (hasTab) return
    const tab = TABS?.[0]

    if (!tab) return
    searchParams.set('statisticsTab', tab?.name)
    setSearchParams(searchParams)
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [TABS])

  const activeTabAttackVectors = useMemo(
    () => data?.attack_vectors?.find(v => v.type === statisticsTab)?.type || EAttackVectorType.form,
    [data?.attack_vectors, statisticsTab],
  )

  const isPhishingTab = activeTabAttackVectors === EAttackVectorType.form

  const navigate = useNavigate()

  const cards = useMemo<PhishingCampaignInfoCardProps.PhishingCampaignInfoCardData[]>(() => {
    const statistics = data?.statistics
    const totalUsers = data?.users_count || 0

    const baseCards = [
      {
        level: 'neutral',
        icon: (
          <IconWrapper size='20' color='white'>
            <EmailPostedIcon />
          </IconWrapper>
        ),
        rangeFrom: statistics?.sent || 0,
        rangeTo: totalUsers,
        title: t('commons:sent'),
      },
      {
        level: 'complementary',
        icon: (
          <IconWrapper size='20' color='white'>
            <EmailOpenBoldIcon />
          </IconWrapper>
        ),
        rangeFrom: statistics?.opened || 0,
        rangeTo: totalUsers,
        title: t('commons:opened'),
      },
    ]

    if (activeTabAttackVectors === EAttackVectorType.form) {
      return [
        ...baseCards,
        {
          level: 'warning',
          icon: (
            <IconWrapper size='20' color='white'>
              <Web1BoldIcon />
            </IconWrapper>
          ),
          rangeFrom: statistics?.clicked || 0,
          rangeTo: totalUsers,
          title: t('commons:transition'),
        },
        {
          level: 'bad',
          icon: (
            <IconWrapper size='20' color='white'>
              <LockIcon />
            </IconWrapper>
          ),
          rangeFrom: statistics?.entered_data || 0,
          rangeTo: totalUsers,
          title: t('commons:entered_all_data'),
        },
      ] as PhishingCampaignInfoCardProps.PhishingCampaignInfoCardData[]
    }

    if (activeTabAttackVectors === EAttackVectorType.attachments) {
      return [
        ...baseCards,
        {
          level: 'bad',
          icon: (
            <IconWrapper size='20' color='white'>
              <ClipBoldIcon />
            </IconWrapper>
          ),
          rangeFrom: statistics?.opened_attachment || 0,
          rangeTo: totalUsers,
          title: t('commons:open_attachments'),
        },
      ] as PhishingCampaignInfoCardProps.PhishingCampaignInfoCardData[]
    }

    return [] as PhishingCampaignInfoCardProps.PhishingCampaignInfoCardData[]
  }, [data, activeTabAttackVectors, t])

  const onTableNavigate = () => {
    navigate(`/lk/admin/phishing/phishing-by-tags/campaigns/${campaign_id}/statistics`)
  }

  const onTabChange = (statisticsTab: string) => {
    if (!statisticsTab) return

    searchParams.set('statisticsTab', statisticsTab)
    setSearchParams(searchParams)
  }

  return (
    <div className={cx('wrapper')}>
      <div className={cx('title__wrapper')}>
        <Tabs tabs={TABS} active={statisticsTab} onClick={onTabChange} />
        <Button onClick={onTableNavigate} className={cx('action-btn')} color='gray' size='small'>
          <span className={cx('action-btn__text')}>{t('commons:user_table')}</span>
          <IconWrapper color='gray70' size='20'>
            <ArrowIcon />
          </IconWrapper>
        </Button>
      </div>
      <div className={cx('cards__wrapper', !isPhishingTab && 'cards__wrapper_attachments')}>
        {cards.length > 0
          ? cards.map(({ icon, level, rangeFrom, rangeTo, title }) => (
              <PhishingCampaignInfoCard
                key={level + statisticsTab}
                icon={icon}
                level={level}
                title={title}
                rangeFrom={rangeFrom}
                rangeTo={rangeTo}
              />
            ))
          : [1, 2, 3, 4].map((_, index) => <Skeleton key={index} width='100%' height='240px' />)}
      </div>
    </div>
  )
}
