/* eslint-disable no-empty-pattern */
import React, { useLayoutEffect, useMemo, useRef, useState } from 'react'
import styles from './styles.module.scss'
import classNamesBind from 'classnames/bind'
import { HelpIcon, Loader, Pagination } from '@/shared/ui'
import BoxIcon from '@/shared/ui/Icon/icons/components/BoxIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { COLOR_MAP, EMPLOYESS_TABLE_COLS, TABLE_LIMIT } from '@/shared/constants/statistics'
import { useParams, useSearchParams } from 'react-router-dom'
import { fromISO } from '@/shared/helpers/date'
import { useAppSelector } from '@/store'
import { selectFilterStatuses, selectFilterOses } from '@/store/slices/phishing'
import { useTranslation } from 'react-i18next'
import { type IPhishingCampaignStatisticsUser } from '@/entities/phishing'
import { phishingQueries } from '@/entities/phishing'

const cx = classNamesBind.bind(styles)

//! Заглушка для таблицы, когда данных нет
const EmptyPlug = () => {
  const { t } = useTranslation()

  return (
    <div className={cx('plug__container')}>
      <div className={cx('plug__icon', 'box_icon')}>
        <IconWrapper color='gray60'>
          <BoxIcon />
        </IconWrapper>
      </div>
      <h2 className={cx('plug__text')}>{t('commons:no_data')}</h2>
    </div>
  )
}

interface ITableRow {
  row: IPhishingCampaignStatisticsUser
}

const TableRow: React.FC<ITableRow> = ({ row }) => {
  const { t } = useTranslation()
  const { full_name, email, department_name, position, phishing_result } = row
  const { created_at, os, status } = phishing_result
  const name = full_name || email || t('commons:name_missing')
  const date = created_at ? fromISO(created_at, 'yyyy-MM-dd HH:mm') : ''

  const USER_ACTIONS = useMemo(
    () => ({
      clicked: t('commons:followed_link'),
      entered_data: t('commons:entered_data'),
      opened: t('commons:letter_opened'),
      opened_attachment: t('commons:opened_attachment'),
      sent: t('commons:didnt_open_letter'),
      unknown: t('commons:not_sent'),
      unknown_text: t('commons:unknown_error'),
    }),
    [t],
  )

  return (
    <tr key={row.id}>
      <td className={cx('name-cell')}>
        <p title={name}>{name}</p>
      </td>
      <td title={position} className={cx('work-post-cell')}>
        <span> {position}</span>
      </td>
      <td className={cx('department-cell')}>
        <p title={department_name}>{department_name}</p>
      </td>
      <td className={cx('system-cell')}>
        <p title={os}>{os || ''}</p>
      </td>
      <td className={cx('status-cell')}>
        <p
          title={USER_ACTIONS[status]}
          className={cx('status', 'badge', `color-${COLOR_MAP[status]}`)}
        >
          {USER_ACTIONS[status]}
        </p>
      </td>
      <td className={cx('date-cell')}>
        <div className={cx('date-cell-wrapper')}>
          <p title={date}>{date}</p>
          {date && (
            <HelpIcon text={t('commons:last_action_date')} tooltipClassname={cx('tooltip')} />
          )}
        </div>
      </td>
    </tr>
  )
}

interface Props {
  isPhishing?: boolean
  byTag?: boolean
  search?: string
}

export type EmployeesTableParams = {
  limit?: number | null
  offset?: number | null
  search?: string
  template_id?: string
  by_tag?: boolean
  status?: UUID[]
  os?: UUID[]
}

export const EmployessTable = ({ isPhishing = false, byTag = false, search }: Props) => {
  const { campaign_id = '', template_id = '' } = useParams()
  const [currentQueryParameters, setSearchParams] = useSearchParams()
  const newQueryParameters: URLSearchParams = new URLSearchParams()
  const type = currentQueryParameters?.get('type') || 'users'
  const page = Number(currentQueryParameters?.get('page')) || 1
  const { t } = useTranslation()

  const tableHeaderRef = useRef<HTMLTableSectionElement>(null)
  const [wasError, setWasError] = useState(false)
  const [hasPagination, setHasPagination] = useState(true)

  const filterStatuses = useAppSelector(selectFilterStatuses)
  const filterOses = useAppSelector(selectFilterOses)

  const ACTUAL_PAGE = (+page || 1) - 1

  const tableParams = useMemo(() => {
    const params: EmployeesTableParams = {
      limit: TABLE_LIMIT,
      offset: TABLE_LIMIT * ACTUAL_PAGE,
    }
    let hasPagination = true

    if (search) {
      params.search = search
      params.limit = null
      params.offset = null
      hasPagination = false
    }

    if (filterStatuses && filterStatuses.length > 0) {
      params.status = filterStatuses.map(status => status.id)
      params.limit = null
      params.offset = null
      hasPagination = false
    }

    if (filterOses && filterOses.length > 0) {
      params.os = filterOses.map(os => os.id)
      params.limit = null
      params.offset = null
      hasPagination = false
    }

    if (isPhishing) {
      params.template_id = template_id
    }

    if (byTag) {
      params.by_tag = true
    }

    setHasPagination(hasPagination)

    return params
  }, [ACTUAL_PAGE, byTag, filterOses, filterStatuses, isPhishing, search, template_id])

  const { data, isLoading, isFetching, isError, isSuccess } =
    phishingQueries.useGetPhishingCampaignsTableUsersByIdQuery(
      {
        id: campaign_id,
        params: tableParams,
      },
      { skip: type !== 'users' },
    )

  const isEmptyData = !data?.data?.[0]?.id

  const onSetPage = (page: number) => {
    const ACTUAL_PAGE = page + 1
    if (ACTUAL_PAGE <= 0) return
    handleClickPagination(String(ACTUAL_PAGE))

    if (tableHeaderRef.current) tableHeaderRef.current.scrollIntoView()
  }

  const handleClickPagination = (newPage: string) => {
    if (newPage) {
      newQueryParameters.set('page', newPage)
      setSearchParams(newQueryParameters)
    }
  }

  useLayoutEffect(() => {
    if (isError) setWasError(true)
  }, [isError])

  useLayoutEffect(() => {
    if (isSuccess && !!data) setWasError(false)
  }, [isSuccess, data])

  return (
    <>
      <table className={cx('table', isFetching && 'table__opacity')}>
        <thead ref={tableHeaderRef}>
          <tr>
            {EMPLOYESS_TABLE_COLS.map(column => (
              <th
                key={column.key}
                className={cx('tableHeadColumn', column.helper && 'tableHeadColumn_withHelp')}
              >
                <span>{t(column.label)}</span>
                {column.helper && (
                  <HelpIcon
                    className={cx('tableHeadColumn__helpIcon')}
                    text={t(column.helper)}
                    tooltipClassname={cx('tooltip')}
                  />
                )}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {isLoading && (
            <tr key={'row-first-loading-plug'}>
              <td colSpan={EMPLOYESS_TABLE_COLS.length}>
                <div className={cx('plug__container')}>
                  <Loader size='56' />
                </div>
              </td>
            </tr>
          )}
          {isFetching ? (
            wasError ? (
              <tr key={'row-first-loading-plug'}>
                <td colSpan={EMPLOYESS_TABLE_COLS.length}>
                  <div className={cx('plug__container')}>
                    <Loader size='56' />
                  </div>
                </td>
              </tr>
            ) : (
              <tr className={cx('plug__loader__container')} key={'row-loading-plug'}>
                <td className={cx('plug__loader')} colSpan={EMPLOYESS_TABLE_COLS.length}>
                  <Loader size='56' />
                </td>
              </tr>
            )
          ) : wasError ? (
            <tr key={'row-error-plug'}>
              <td colSpan={EMPLOYESS_TABLE_COLS.length}>
                <div className={cx('plug__container')}>
                  <Loader error loading={false} size='56' />
                  <h2 className={cx('plug__text')}>{t('commons:error_with_dots')}</h2>
                </div>
              </td>
            </tr>
          ) : isEmptyData ? (
            <tr key={'row-empty-plug'}>
              <td colSpan={EMPLOYESS_TABLE_COLS.length}>
                <EmptyPlug />
              </td>
            </tr>
          ) : null}
          {!wasError &&
            data?.data?.[0]?.id &&
            data?.data?.map(row => <TableRow key={row.id} row={row} />)}
        </tbody>
      </table>
      {hasPagination && (
        <Pagination
          currentPage={ACTUAL_PAGE}
          limit={TABLE_LIMIT}
          total={data?.total_count || 0}
          onChange={onSetPage}
          isLoading={isLoading || isFetching}
        />
      )}
    </>
  )
}
