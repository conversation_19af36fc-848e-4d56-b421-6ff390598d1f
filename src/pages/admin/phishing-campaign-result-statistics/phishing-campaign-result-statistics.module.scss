@use '../../../shared/assets/styles/mixins/text';

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.title {
  &__wrapper {
    align-items: center;

    color: var(--color-gray-90);
    display: grid;
    font: var(--font-title-2-medium);
    gap: 12px;
    grid-template-columns: 32px 1fr;
    justify-content: flex-start;
    margin-top: -8px;
  }
}

.tabs__wrapper {
  align-items: center;
  display: flex;
  gap: 12px;
  justify-content: space-between;
}

.logo {
  height: 32px;
  width: 32px;
  img {
    vertical-align: top;
  }
  &__wrapper {
    align-items: center;
    display: flex;
    gap: 12px;
  }
}

.color {
  &-neutral {
    background: #e1e4eb;
    color: #8e97af;
  }
  &-complementary {
    background: rgba(134, 118, 255, 0.2);
    color: #8676ff;
  }
  &-bad {
    background: #ffdfe3;
    color: #ff708b;
  }
  &-warning {
    background: #fdf2d4;
    color: #ffba69;
  }
  &-good {
    background: #d4f0e4;
    color: #3dbc87;
  }
}

.table {
  background-color: var(--white);
  border-radius: 16px;
  padding: 12px 16px;
  width: 100%;
  &__wrapper {
    margin: 12px 0;
  }

  tbody {
    color: var(--gray-gray-90);
    font: var(--font-caption-1-medium);
    position: relative;
    .name-cell {
      width: 20%;
    }
    .work-post-cell {
      width: 15%;
    }
    .department-cell {
      width: 20%;
    }
    .system-cell {
      width: 15%;
    }
    .employee-cell {
      width: 30%;
    }
    .dep-cell {
      width: 40%;
    }
    .popular-cell {
      width: 25%;
    }
    .caught-cell {
      width: 25%;
    }
    .date-cell {
      width: 15%;
      overflow: visible;
    }
    .date-cell-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
    }
    tr {
      border-top: 1px solid var(--color-gray-30);
      padding: 12px 16px;
    }
    td {
      align-items: center;
      min-height: inherit;
      overflow: hidden;
      padding: 12px 16px;

      vertical-align: middle;
      word-wrap: break-word;

      p,
      span {
        @include text.max-lines(2);
      }
    }
  }
  thead {
    color: var(--color-gray-70);

    font: var(--font-caption-1-medium);
    padding: 12px 16px;

    th {
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      padding: 12px 16px;

      text-align: start;
    }
  }

  &__opacity {
    opacity: 0.7;
  }
}

.modal {
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 360px;

  &__title {
    color: var(--gray-gray-90);
    font: var(--font-title-4-medium);
  }

  &__content {
    align-items: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px;
  }

  &__success {
    color: var(--color-primary-90);
    font: var(--font-text-2-normal);
  }

  &__error {
    color: var(--color-error);
    font: var(--font-text-2-normal);
  }

  &__loading {
    color: var(--color-gray-90);
    font: var(--font-text-2-normal);
  }

  &__actions {
    align-items: center;
    display: flex;
    gap: 16px;
    justify-content: space-between;

    > * {
      width: 100%;
    }
  }
}

.plug {
  &__container {
    align-items: center;

    background-color: var(--white);
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 85px;

    text-align: center;

    width: 100%;
  }

  &__loader {
    align-items: center;

    display: flex;
    flex-direction: column;
    justify-content: center;
    left: 50%;
    position: absolute;
    top: 50%;

    transform: translate(-50%, -50%);
    &__container {
      align-items: center;
      height: 100%;
      justify-content: center;
      min-height: 200px;
      width: 100%;
    }
  }

  &__text {
    color: var(--gray-gray-70, #8e97af);
    font: var(--font-title-3-medium);
  }
}

.badge {
  border-radius: 8px;
  display: inline-block;

  font: var(--font-caption-2-medium);
  padding: 2px 6px;
  width: fit-content;
}

.incident-level {
  &-good {
    color: var(--color-statistics-good);
  }
  &-warning {
    color: var(--color-statistics-warning);
  }
  &-bad {
    color: var(--color-statistics-bad);
  }
}

.btn__export {
  svg,
  path {
    fill: var(--color-gray-70) !important;
  }
  &:hover {
    svg,
    path {
      fill: var(--color-gray-80) !important;
    }
  }
  &.disabled {
    svg,
    path {
      fill: var(--button-disabled-text-color) !important;
    }
  }
}

.box_icon {
  align-items: center;
  display: flex;
  height: 100%;
  justify-content: center;
  width: 100%;
  > div {
    height: 160px !important;
    width: 160px !important;

    > svg {
      height: 100% !important;
      width: 100% !important;
    }
  }
}

.tooltip {
  transform: translateX(-100%) translateY(50%);
  padding: 6px;
  border-radius: 4px;
}
