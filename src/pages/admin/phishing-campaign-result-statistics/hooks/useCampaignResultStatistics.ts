import { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '@/entities/phishing'

export type CampaignStatisticsSearchParams = {
  type: 'users' | 'departments'
  page: number
}

export const useCampaignResultStatistics = () => {
  const { campaign_id = '', template_id = '' } = useParams()
  const { t } = useTranslation()

  const { data: campaignData } = phishingQueries.useGetPhishingCampaignsByIdQuery(
    { id: campaign_id, params: { by_tag: false } },
    {
      skip: !campaign_id,
    },
  )

  const { data: templateData } = phishingQueries.useGetPhishingCampaignEmailingTemplateQuery(
    {
      campaign_id,
      campaign_template_id: template_id,
      params: { by_tag: false },
    },
    {
      skip: !template_id || !campaign_id,
    },
  )

  const title = templateData ? templateData?.name + ' - ' + templateData?.email?.name : ''

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/campaigns',
        text: t('commons:campaigns'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/campaigns/' + campaignData?.id,
        text: campaignData?.name || t('current_newsletter'),
        clickable: true,
        isLoading: !campaignData,
      },
      {
        id: `/lk/admin/phishing/campaigns/${campaignData?.id}/${templateData?.id}/statistics`,
        text: title || t('commons:statistics'),
        clickable: true,
        isLoading: !title,
      },
    ],
    [campaignData, title, t],
  )

  return {
    BREADCRUMBS,
    campaignData,
    templateData,
    title,
  }
}
