import { useCallback, useMemo, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { TabsProps } from '@/shared/components'
import { useTranslation } from 'react-i18next'

export type CampaignStatisticsSearchParams = {
  type: 'users' | 'departments'
  page: number
}

export const TABLE_LIMIT = 1

export const useCampaignStatisticsTabs = () => {
  const { t } = useTranslation()
  const [currentQueryParameters, setSearchParams] = useSearchParams()
  const type = currentQueryParameters?.get('type') || 'users'
  const page = Number(currentQueryParameters?.get('page')) || 1

  const ACTUAL_PAGE = +page - 1

  const TABS: TabsProps.TabItem[] = useMemo(
    () => [
      { name: 'users', value: t('commons:employees') },
      { name: 'departments', value: t('commons:departments') },
    ],
    [t],
  )

  const [activeTab, setActiveTab] = useState(
    () => TABS.find(t => t.name === type)?.name || TABS[0].name,
  )

  const onTabChange = useCallback(
    (newType: string) => {
      if (!newType) return
      const newQueryParameters: URLSearchParams = new URLSearchParams()

      newQueryParameters.set('type', newType)
      setSearchParams(newQueryParameters)
    },
    [setSearchParams],
  )

  const onChangeActiveTab = useCallback(
    (v: string) => {
      const tab = TABS.find(t => t.name === v)?.name || TABS[0].name || ''
      setActiveTab(tab)
      onTabChange(tab)
    },
    [onTabChange],
  )

  const isEmployeeTable = type === 'users'
  const isDepartmentTable = type === 'departments'

  return {
    activeTab,
    onChangeActiveTab,
    isEmployeeTable,
    isDepartmentTable,
    page: ACTUAL_PAGE,
  }
}
