@use './../../../shared/assets/styles/mixins/text';

.title {
  margin-bottom: 0;
  &__wrapper {
    display: flex;
    flex-direction: row;
    gap: 12px;
    align-items: center;
    margin-bottom: 32px;
  }
}

.markdown {
  ol {
    list-style: decimal;
    margin-bottom: 10px;
    padding-left: 30px;
  }
  ul {
    list-style: disc;
    margin-bottom: 10px;
    padding-left: 30px;
  }
  li {
    margin-bottom: 5px;
  }
  strong {
    font-weight: 600;
  }
  em {
    font-style: italic;
  }
}

.table {
  background-color: var(--white);
  border-radius: 16px;
  padding: 12px 16px;
  width: 100%;

  .file-cell {
    width: 0%;
    margin-left: auto;
    margin-right: auto;
  }
  .version-cell {
    width: 55%;
  }
  .type-cell {
    width: 10%;
  }
  .date-cell {
    width: 20%;
  }
  .download-cell {
    width: 5%;
  }
  .open-cell {
    width: 5%;
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }

  tbody {
    color: var(--color-gray-90);
    font: var(--font-caption-1-medium);
    position: relative;

    tr {
      border-top: 1px solid var(--color-gray-30);
      padding: 12px 16px;
    }
    td {
      padding: 12px 16px;

      vertical-align: middle;
    }
  }
  thead {
    color: var(--color-gray-70);

    font: var(--font-caption-1-medium);
    padding: 12px 16px;

    th {
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      padding: 12px 16px;

      text-align: start;
    }
  }

  &__opacity {
    opacity: 0.7;
  }
}

.details {
  &__icon {
    margin-left: auto;
    margin-right: auto;
    cursor: pointer;
    transition: 0.3s;
  }
}

.underline {
  text-decoration: underline;
}

.download__icon {
  width: 100%;
  margin-right: auto;
  margin-left: 4px;
  cursor: pointer;
}

.trancate {
  @include text.max-lines(2);
}

.plug {
  &__row {
    min-height: 300px;
  }
  width: 100%;
  text-align: center;
  font: var(--font-title-3-normal);
  color: var(--color-gray-70);
}

.error {
  color: var(--color-error);
}

.complete {
  color: var(--color-primary);
}

.in_progress {
  color: var(--color-statistics-warning-text);
}

.disabled {
  cursor: not-allowed;
}
