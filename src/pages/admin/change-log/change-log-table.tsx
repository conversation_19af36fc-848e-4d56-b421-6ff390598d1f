import styles from './change-log-table.module.scss'
import classNamesBind from 'classnames/bind'
import { Loader } from '@/shared/ui'
import ArrowDown from '@/shared/ui/Icon/icons/components/ArrowDown'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { memo, useState } from 'react'
import { getPrettyDateWithOffset } from './helper'
import ReactMarkdown from 'react-markdown'
import { useTranslation } from 'react-i18next'
import { changeLogApi, ChangeLogItem } from '@/entities/change-log'
import { useLocaleForDates } from '@/shared/hooks/use-locale-for-dates'

const cx = classNamesBind.bind(styles)

export type ReportsTableProps = {
  data?: ChangeLogItem[]
  isLoading?: boolean
  isError?: boolean
}

type RowProps = {
  item: ChangeLogItem
}

export const ChangeLogTableRow: React.FC<RowProps> = ({ item }) => {
  const [open, setOpen] = useState(false)
  const { t } = useTranslation()
  const [lazyGet, { data, isLoading, isError }] = changeLogApi.useLazyGetChangeLogQuery()
  const dateLocale = useLocaleForDates()

  return (
    <>
      <tr key={item.id} className={cx('table-row')}>
        <td className={cx('name-cell')}>
          <p className={cx(styles.trancate)}>{item.version}</p>
        </td>
        <td className={cx('status-cell')}>
          <p>{item.type}</p>
        </td>
        <td className={cx('status-cell')}>
          <p>{getPrettyDateWithOffset(new Date(item.created_at), dateLocale)}</p>
        </td>
        <td className={cx('open-cell')}>
          <IconWrapper
            onClick={() => {
              setOpen(v => !v)
              if (!data) {
                lazyGet(item.id)
              }
            }}
            direction={open ? 'down' : 'up'}
            className={cx('details__icon')}
            size='28'
            color='green'
          >
            <ArrowDown />
          </IconWrapper>
        </td>
      </tr>
      {open && (
        <tr>
          <td colSpan={4}>
            {/* style={{ whiteSpace: 'pre-wrap' }} */}
            {isLoading && t('commons:loading_with_dots')}
            {isError && t('commons:error_with_dots')}
            {data && <ReactMarkdown className={cx('markdown')}>{data?.text}</ReactMarkdown>}
          </td>
        </tr>
      )}
    </>
  )
}

export const ChangeLogTable: React.FC<ReportsTableProps> = memo(({ data, isLoading, isError }) => {
  const { t } = useTranslation('components__change-log')

  return (
    <table className={cx(styles.table, isLoading && 'table__opacity')}>
      <thead>
        <tr>
          <th className={cx('sersion-cell')}>{t('version')}</th>
          <th className={cx('type-cell')}>{t('type')}</th>
          <th className={cx('date-cell')}>{t('implementation_date')}</th>
          <th className={cx('open-cell')}>{t('more_details')}</th>
        </tr>
      </thead>
      <tbody>
        {!isError && !isLoading && !data?.length && (
          <tr className={cx('plug__row')}>
            <td className={cx('plug')} colSpan={4}>
              <Loader error={false} size='56' loading={true} />
            </td>
          </tr>
        )}
        {isError && (
          <tr className={cx('plug__row')}>
            <td colSpan={4} className={cx('plug')}>
              <Loader error={true} size='56' loading={false} />
            </td>
          </tr>
        )}
        {!isError && data?.map(item => <ChangeLogTableRow item={item} key={item.id} />)}
      </tbody>
    </table>
  )
})
