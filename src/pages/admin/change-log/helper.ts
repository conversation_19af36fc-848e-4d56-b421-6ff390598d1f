export const getCorrectFilePathWithExt = (url: string, id: string) => {
  const splittedArray = url.split('/').slice(-1)[0].split('.')

  return id + '.' + splittedArray[1]
}

export const getCorrectFilename = (filename: string) => filename?.split('_').slice(0, -1).join(' ')

export const getNameFromFilePath = (path: string) => {
  return path.split('/').pop()
}

export const getNameFromFileWithSpliter = (path: string, spliter: string = '__') => {
  return path.split(spliter).shift()
}

import { DEFAULT_LOCALE } from '@/shared/helpers/date'

const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone

export const getPrettyDateWithOffset = (date: Date = new Date(), locale?: string) => {
  const currentLocale = locale || DEFAULT_LOCALE
  return new Date(date.getTime()).toLocaleString(currentLocale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    hour12: false,
    timeZone,
  })
}
