import { HelpIcon, PageTitle } from '@/shared/ui'
import { ChangeLogTable } from './change-log-table'
import styles from './change-log-table.module.scss'
import classNamesBind from 'classnames/bind'
import { useTranslation } from 'react-i18next'
import { changeLogApi } from '@/entities/change-log'
const cx = classNamesBind.bind(styles)

export const ChangeLog = () => {
  const { data } = changeLogApi.useGetChangeLogsQuery()
  const { t } = useTranslation('components__change-log')

  return (
    <div>
      <div className={cx('title__wrapper')}>
        <PageTitle className={cx('title')}>{t('commons:change_log')}</PageTitle>
        <HelpIcon text={t('update_info')} />
      </div>
      <ChangeLogTable data={data?.data} />
    </div>
  )
}
