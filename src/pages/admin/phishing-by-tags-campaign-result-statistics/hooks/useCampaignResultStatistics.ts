import { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '@/entities/phishing'
import { removeLastSegmentAfterDelimiter } from '@/shared/helpers'

export type CampaignStatisticsSearchParams = {
  type: 'users' | 'departments'
  page: number
}

export const useCampaignResultStatistics = () => {
  const { campaign_id = '', template_id = '' } = useParams()
  const { data: campaignData } = phishingQueries.useGetPhishingCampaignsByIdQuery(
    { id: campaign_id, params: { by_tag: true } },
    {
      skip: !campaign_id,
    },
  )

  const { t } = useTranslation()

  const { data: templateData } = phishingQueries.useGetPhishingCampaignEmailingTemplateQuery(
    {
      campaign_id,
      campaign_template_id: template_id,
      params: { by_tag: true },
    },
    {
      skip: !template_id || !campaign_id,
    },
  )

  const title = templateData ? templateData?.name + ' - ' + templateData?.email?.name : ''

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/phishing-by-tags',
        text: t('commons:phishing_by_tags'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/phishing-by-tags/campaigns/' + campaignData?.id,
        text: removeLastSegmentAfterDelimiter(campaignData?.name) || t('current_newsletter'),
        clickable: true,
        isLoading: !campaignData,
      },
      {
        id: `/lk/admin/phishing/phishing-by-tags/campaigns/${campaignData?.id}/${templateData?.id}/statistics`,
        text: title || t('commons:statistics'),
        clickable: true,
        isLoading: !templateData,
      },
    ],
    [campaignData, title, templateData, t],
  )

  return {
    BREADCRUMBS,
    campaignData,
    templateData,
    title,
  }
}
