import { FC } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './phishing-by-tags-campaign-result-statistics.module.scss'

import { PhishingCampaignResultStatisticsProps } from './phishing-by-tags-campaign-result-statistics.d'
import { Breadcrumbs } from '@/shared/ui'
import { useCampaignResultStatistics } from './hooks/useCampaignResultStatistics'
import { PhishingCampaignIncidentCard, TemplateLogo } from '@/shared/components'
import CampaignStatisticsTabs from '../campaign-statistics-tabs'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignResultStatistics: FC<
  PhishingCampaignResultStatisticsProps.Props
> = props => {
  const { className } = props
  const { BREADCRUMBS, templateData, title } = useCampaignResultStatistics()

  return (
    <div className={cx('wrapper', className)}>
      <Breadcrumbs items={BREADCRUMBS} />
      <div className={cx('title__wrapper')}>
        <TemplateLogo className={cx('logo')} src={templateData?.logo} alt={templateData?.name} />
        <h2 className={cx('title')}>{title}</h2>
      </div>
      <PhishingCampaignIncidentCard
        infoType='messages'
        data={templateData}
        className={cx('incident__wrapper')}
      />
      <CampaignStatisticsTabs isPhishing byTag />
    </div>
  )
}
export default PhishingCampaignResultStatistics
