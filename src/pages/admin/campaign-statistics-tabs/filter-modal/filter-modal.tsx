import { useCallback, useMemo } from 'react'
import { useParams } from 'react-router'
import { useAppDispatch, useAppSelector } from '@/store'
import classNamesBind from 'classnames/bind'
import { Modal } from '@/shared/components'
import { Button, HelpIcon, IListItem, Loader, MultiSelect } from '@/shared/ui'
import styles from './style.module.scss'
import {
  clearFilterValues,
  selectFilterInputOses,
  selectFilterInputStatuses,
  setFilterInputOs,
  setFilterInputStatus,
  setFilterValues,
} from '@/store/slices/phishing'
import { useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { useGetTranslatePhishingTypeList } from '@/shared/hooks'
import { phishingQueries } from '@/entities/phishing'

type Props = {
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
  byTag: boolean
}

const cx = classNamesBind.bind(styles)

export const FilterModal = ({ open, setOpen, byTag }: Props) => {
  const { campaign_id = '', template_id = '' } = useParams()
  const [, setSearchParams] = useSearchParams()

  const dispatch = useAppDispatch()
  const statuses = useAppSelector(selectFilterInputStatuses)
  const oses = useAppSelector(selectFilterInputOses)
  const PHISHING_TYPE_LIST = useGetTranslatePhishingTypeList()
  const { t } = useTranslation()

  const { data: osData, isLoading } = phishingQueries.useGetPhishingCampaignsOsByIdQuery({
    id: campaign_id,
    templateId: template_id,
    byTag,
  })

  const systemsList: IListItem[] | null = useMemo(() => {
    if (!osData) return null

    return osData.data.map(system => ({ id: system, title: system }))
  }, [osData])

  const selectedStatusesIds = useMemo(() => {
    if (!statuses) return null

    return statuses.map(status => status.id)
  }, [statuses])

  const selectedSystemsIds = useMemo(() => {
    if (!oses) return null

    return oses.map(os => os.id)
  }, [oses])

  const handleChangeStatusSelect = useCallback(
    (item: IListItem) => dispatch(setFilterInputStatus(item)),
    [dispatch],
  )

  const handleChangeOsSelect = useCallback(
    (item: IListItem) => dispatch(setFilterInputOs(item)),
    [dispatch],
  )

  const handleApplyClick = useCallback(() => {
    dispatch(setFilterValues())
    setSearchParams('')
    setOpen(false)
  }, [dispatch, setOpen, setSearchParams])

  const handleClearClick = useCallback(() => {
    dispatch(clearFilterValues())
  }, [dispatch])

  return (
    <Modal active={open} setActive={setOpen}>
      <div className={cx('wrap')}>
        <h2>{t('dialogs.filter_modal.title')}</h2>

        <div className={cx('item')}>
          <MultiSelect
            label={
              <>
                {t('dialogs.filter_modal.status')}{' '}
                <HelpIcon text={t('modules.employee_statistics.status_helper')} />
              </>
            }
            placeholder={t('dialogs.filter_modal.not_important')}
            customValue={selectedStatusesIds || []}
            onChange={handleChangeStatusSelect}
            list={PHISHING_TYPE_LIST}
          />
        </div>

        {isLoading && <Loader />}
        {!isLoading && systemsList && systemsList.length > 0 && (
          <div className={cx('item')}>
            <MultiSelect
              label={t('dialogs.filter_modal.system')}
              placeholder={t('dialogs.filter_modal.not_important')}
              customValue={selectedSystemsIds || []}
              onChange={handleChangeOsSelect}
              list={systemsList}
            />
          </div>
        )}

        <div className={cx('buttonWrapper')}>
          <Button color='gray' onClick={handleClearClick} type='button'>
            {t('dialogs.filter_modal.clear')}
          </Button>
          <Button type='submit' onClick={handleApplyClick}>
            {t('dialogs.filter_modal.apply')}
          </Button>
        </div>
      </div>
    </Modal>
  )
}
