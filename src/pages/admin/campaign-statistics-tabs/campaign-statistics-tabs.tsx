import { useCallback, useEffect, useState } from 'react'
import styles from './styles.module.scss'
import classNamesBind from 'classnames/bind'
import { Tabs } from '@/shared/components'
import { Button, SearchInput } from '@/shared/ui'
import { useCampaignStatisticsTabs } from '../campaign-statictics/hooks/use-campaign-statistics-tabs'
import { CampaignStatisticsExportModal } from '../campaign-statistics-export-modal'
import { EmployessTable } from '../employess-table'
import DepartmentTable from '../department-table'
import { PhishingDepartmentTable } from '../phishing-department-table'
import { FilterModal } from './filter-modal/filter-modal'
import { useAppDispatch, useAppSelector } from '@/store'
import { clearFilterValues, selectFilterValues } from '@/store/slices/phishing'
import { useDebounce } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

interface Props {
  isPhishing?: boolean
  byTag?: boolean
}

export const CampaignStatisticsTabs = ({ isPhishing = false, byTag = false }: Props) => {
  const { activeTab, onChangeActiveTab, isDepartmentTable, isEmployeeTable, TABS } =
    useCampaignStatisticsTabs()

  const [search, setSearch] = useState('')
  const debouncedSearch = useDebounce(search, 500)
  const { t } = useTranslation()

  const [isFilterModalOpen, setIsFilterModalOpen] = useState(false)
  const [isExportModalOpen, setIsExportModalOpen] = useState(false)
  const [hasFilter, setHasFilter] = useState(false)

  const filterValues = useAppSelector(selectFilterValues)
  const dispatch = useAppDispatch()

  useEffect(() => {
    const hasFilter = Object.entries(filterValues).some(([, value]) => !!value)
    setHasFilter(hasFilter)
  }, [filterValues])

  const handleFilterClick = () => setIsFilterModalOpen(prev => !prev)

  const handleResetClick = useCallback(() => {
    dispatch(clearFilterValues())
  }, [dispatch])

  useEffect(() => {
    return () => handleResetClick()
  }, [handleResetClick])

  return (
    <div>
      <div className={cx('tabs__wrapper')}>
        <Tabs tabs={TABS} active={activeTab} onClick={onChangeActiveTab} />
        {isEmployeeTable && (
          <SearchInput
            className={cx('search')}
            classNameWrapper={cx('searchWrapper')}
            placeholder={t('commons:enter_name_position_email')}
            onChange={setSearch}
            value={search}
          />
        )}
        <div style={{ display: 'flex' }}>
          {!isPhishing && (
            <>
              <Button
                onClick={() => {
                  setIsExportModalOpen(true)
                }}
                size='small'
                color='gray'
                rightIcon={'download'}
                className={cx('btn__export', {
                  disabled: false,
                })}
              >
                {t('phishing.campaign_statistics.export_button')}
              </Button>
              {isExportModalOpen && (
                <CampaignStatisticsExportModal
                  open={isExportModalOpen}
                  setOpen={setIsExportModalOpen}
                />
              )}
            </>
          )}
          {isEmployeeTable && hasFilter && (
            <Button color='gray' size='small' onClick={handleResetClick}>
              {t('phishing.campaign_statistics.clear')}
            </Button>
          )}
          {isEmployeeTable && (
            <Button
              color='gray'
              size='small'
              rightIcon='filter'
              onClick={handleFilterClick}
              className={cx('filterButton', {
                active: hasFilter,
              })}
            >
              {t('phishing.campaign_statistics.filters')}
            </Button>
          )}
          {isFilterModalOpen && (
            <FilterModal open={isFilterModalOpen} setOpen={setIsFilterModalOpen} byTag={byTag} />
          )}
        </div>
      </div>
      <div className={cx('table__wrapper')}>
        {isEmployeeTable && (
          <EmployessTable isPhishing={isPhishing} byTag={byTag} search={debouncedSearch} />
        )}
        {isDepartmentTable && !isPhishing && <DepartmentTable />}
        {isDepartmentTable && isPhishing && <PhishingDepartmentTable byTag={byTag} />}
      </div>
    </div>
  )
}
