@use '../../../shared/assets/styles/mixins/text';

.tabs__wrapper {
  align-items: center;
  display: flex;
  gap: 12px;
  justify-content: space-between;
  min-height: 42px;
}

.table {
  background-color: var(--white);
  border-radius: 16px;
  padding: 12px 16px;
  width: 100%;
  &__wrapper {
    margin: 12px 0;
  }

  tbody {
    color: var(--gray-gray-90);
    font: var(--font-caption-1-medium);
    position: relative;
    .name-cell {
      width: 20%;
    }
    .work-post-cell {
      width: 15%;
    }
    .department-cell {
      width: 20%;
    }
    .system-cell {
      width: 15%;
    }
    .employee-cell {
      width: 20%;
    }
    .dep-cell {
      width: 30%;
    }
    .popular-cell {
      width: 25%;
    }
    .caught-cell {
      width: 25%;
    }
    .date-cell {
      width: 15%;
      overflow: visible;
    }
    .date-cell-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
    }
    tr {
      border-top: 1px solid var(--color-gray-30);
      padding: 12px 16px;
    }
    td {
      align-items: center;

      min-height: inherit;
      overflow: hidden;
      padding: 12px 16px;

      vertical-align: middle;
      word-wrap: break-word;

      span,
      p {
        @include text.max-lines(2);
      }
    }
  }
  thead {
    color: var(--color-gray-70);

    font: var(--font-caption-1-medium);
    padding: 12px 16px;

    th {
      align-items: flex-start;
      flex-direction: column;
      justify-content: center;
      padding: 12px 16px;

      text-align: start;
    }
  }

  &__opacity {
    opacity: 0.7;
  }
}

.filterButton {
  &.active {
    color: var(--color-primary, #3dbc87);
  }
}

.searchWrapper {
  width: 35%;
  margin-right: auto;
  .search {
    background: #fff;
    width: 100%;
  }
}

.btn__export {
  svg,
  path {
    fill: var(--color-gray-70) !important;
  }
  &:hover {
    svg,
    path {
      fill: var(--color-gray-80) !important;
    }
  }
  &.disabled {
    svg,
    path {
      fill: var(--button-disabled-text-color) !important;
    }
  }
}
