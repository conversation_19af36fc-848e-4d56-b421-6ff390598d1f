import React, { useEffect, useState } from 'react'
import classNamesBind from 'classnames/bind'
import { Modal } from '@/shared/components'
import { Button, Textarea } from '@/shared/ui'
import styles from './employee-statistics.module.scss'
import { useNotification } from '@/shared/contexts/notifications'
import { useParams } from 'react-router-dom'
import { INote } from '@/shared/types/store/employees'
import { useTranslation } from 'react-i18next'
import { employeeStatisticAPI } from '@/entities/statistic'

const cx = classNamesBind.bind(styles)

type Props = {
  note: INote | undefined
  open: boolean
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}

export const EditNoteModal = ({ note, open, setOpen }: Props) => {
  const [text, setText] = useState(note?.text || '')
  const { t } = useTranslation()

  const { employee_id = '' } = useParams()
  const [updateNote, { isLoading }] = employeeStatisticAPI.useUpdateNoteMutation()
  const { handleErrorResponse } = useNotification()

  useEffect(() => {
    if (note?.text) setText(note.text)
  }, [note?.text])

  const onResetText = () => {
    setText(note?.text || '')
  }

  const onSubmit = async () => {
    if (!note) return
    if (!note?.id && !employee_id) {
      handleErrorResponse({
        status: 'error',
        message: t('commons:error_user_id'),
      })
      return
    }

    if (note?.text === text) {
      handleErrorResponse({
        status: 'error',
        message: t('commons:employee_note_matches'),
      })

      return
    }

    await updateNote({ id: note?.id || employee_id, text }).unwrap()
    setOpen(false)
  }

  if (!note) return null

  return (
    <Modal
      className={cx('modal')}
      active={open}
      setActive={v => {
        onResetText()
        setOpen(v)
      }}
    >
      <h3 className={cx('modal__title')}>{t('commons:employee_note')}</h3>
      <Textarea
        disabled={isLoading}
        className={cx('modal__textarea')}
        value={text}
        onChange={(v: string) => setText(v)}
      />
      <div className={cx('modal__actions')}>
        <Button disabled={isLoading} color='gray' fullWidth onClick={onResetText}>
          {t('commons:cancel_changes')}
        </Button>
        <Button disabled={isLoading} fullWidth onClick={onSubmit}>
          {t('commons:save')}
        </Button>
      </div>
    </Modal>
  )
}
