import React, { PropsWithChildren } from "react";
import classNamesBind from "classnames/bind";
import styles from "./block.module.scss";
import { ButtonIcon, Card } from "@/shared/ui";

const cx = classNamesBind.bind(styles);

export interface IBlockProps {
  className?: string;
  title?: string;
  editable?: boolean;
  onEdit?(): void;
}

export const Block: React.FC<PropsWithChildren<IBlockProps>> = (props) => {
  const { className, title, children, editable, onEdit } = props;

  return (
    <Card
      className={cx("block", className)}
      padding="big"
      mobilePadding="normal"
      width="flex"
    >
      {title && (
        <div className={cx("title")}>
          {title}
          {editable && (
            <ButtonIcon
              hasBackground={false}
              icon="editBold"
              size="28"
              iconSize="28"
              onClick={onEdit}
            />
          )}
        </div>
      )}
      {children}
    </Card>
  );
};

export default Block;
