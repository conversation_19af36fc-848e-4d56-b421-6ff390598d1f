import { useEffect, useMemo, useState } from 'react'

import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import {
  calculateCompanyDateSummary,
  calculateDateSummary,
  DateCompanySummaryHashItem,
} from '@/shared/helpers/statistics'
import { DataPoint, RangeItem } from '@/features/graphics'
import { DEFAULT_TIME_RANGE } from '@/features/graphics/model/constants'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { calculateDays } from '@/shared/helpers/date'
import { employeeStatisticAPI, employeeGatewayAPI } from '@/entities/statistic'

const { useGetInfoQuery } = employeeStatisticAPI
const {
  useGetEmployeePhishingStatisticsQuery,
  useGetEmployeeProgressStatisticsQuery,
  useGetEmployeeProgresStatisticsQuizQuery,
  useLazyGetEmployeeProgressStatisticsQuery,
  useGetEmployeeRiskStatisticsQuery,
} = employeeGatewayAPI

export const useEmployeeStaticstics = () => {
  const { employee_id = '' } = useParams()

  const { data: info } = useGetInfoQuery(employee_id)

  const {
    data: progressStatisticsChange,
    error: isProgressStatisticsError,
    isLoading: isLoadingProgressStatisticsChange,
  } = useGetEmployeeProgressStatisticsQuery({
    user_id: employee_id,
  })

  const { data: phishingStatistics, isLoading: isLoadingPhishingStatistics } =
    useGetEmployeePhishingStatisticsQuery({
      user_id: employee_id,
    })

  const { data: progressStatisticsQuiz } = useGetEmployeeProgresStatisticsQuizQuery(employee_id)
  const [getEmployeeProgressStatistics] = useLazyGetEmployeeProgressStatisticsQuery()

  const [educationData, setEducationData] = useState<DataPoint[]>([])

  const { t } = useTranslation('pages__user-statictics')

  const isTwoStatisticsError = isProgressStatisticsError

  const progress_chart = useMemo(() => {
    if (isTwoStatisticsError) return []

    // какой-либо еще грузиться
    if (isLoadingProgressStatisticsChange) return []

    const { learningData, testingData } = calculateDateSummary(progressStatisticsChange?.progress)

    return [
      {
        title: t('commons:learning_progress'),
        color: 'blue',
        data: learningData,
      },
      {
        title: t('commons:test_progress'),
        color: 'green',
        data: testingData,
      },
    ]
  }, [progressStatisticsChange, t])

  const phishing_chart = useMemo(
    () => ({
      sent: phishingStatistics?.sent || undefined,
      opened: phishingStatistics?.opened || undefined,
      followed: phishingStatistics?.clicked || undefined,
      entered: phishingStatistics?.entered_data || undefined,
      attachments: phishingStatistics?.opened_attachment || undefined,
    }),
    [phishingStatistics],
  )

  const [educationTimeRangeItem, setEducationTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )

  const [riskLevelTimeRangeItem, setRiskLevelTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )

  const { data: riskStatistics, isLoading: isLoadingRiskStatistics } =
    useGetEmployeeRiskStatisticsQuery({
      user_id: employee_id,
      days_period: riskLevelTimeRangeItem?.value
        ? String(calculateDays(riskLevelTimeRangeItem?.value))
        : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
    })

  const risk_level_chart = useMemo(
    () => [
      {
        color: 'YELLOW',
        data: riskStatistics?.risk_levels.map(i => ({
          ...i,
          color: 'YELLOW',
          value: i.value,
        })),
      },
    ],
    [riskStatistics],
  )

  const analytics = useAnalytics()

  const [openPhishingExcelReport, setOpenPhishingExcelReport] = useState(false)

  // TODO: change after new requests

  const onPhishingExcelClick = async () => {
    analytics.event('modules.statistics.report', {
      type: 'user_phishing',
      title: String(info?.first_name) + String(info?.last_name),
    })
    setOpenPhishingExcelReport(true)
  }

  useEffect(() => {
    const getDataFn = async () => {
      const { data: progressData } = await getEmployeeProgressStatistics({
        user_id: employee_id,
        days_period: educationTimeRangeItem?.value
          ? String(calculateDays(educationTimeRangeItem?.value))
          : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
      })
      if (!progressData) {
        setEducationData([])
        return
      }

      const { learningData } = calculateCompanyDateSummary(
        progressData.progress as DateCompanySummaryHashItem[],
      )
      setEducationData(learningData)
    }
    getDataFn()
  }, [educationTimeRangeItem, getEmployeeProgressStatistics, employee_id])

  return {
    t,
    info,
    isLoadingProgressStatisticsChange,
    progress_chart,
    isLoadingPhishingStatistics,
    phishing_chart,
    phishingStatistics,
    progressStatisticsQuiz,
    risk_level_chart,
    isLoadingRiskStatistics,
    isTwoStatisticsError,
    isProgressStatisticsError,
    educationTimeRangeItem,
    setEducationTimeRangeItem,
    setRiskLevelTimeRangeItem,
    openPhishingExcelReport,
    onPhishingExcelClick,
    setOpenPhishingExcelReport,
    educationData,
  }
}
