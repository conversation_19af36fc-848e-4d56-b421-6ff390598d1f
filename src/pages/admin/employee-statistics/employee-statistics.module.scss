// @use "placeholders";
// @use "media";
@use '../../../shared/assets/styles/mixins/text';

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.title {
  font: var(--font-title-4-medium);
  color: var(--color-gray-90);
  font-weight: 500;
}

.information {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24px;

  .general {
    grid-column: 1 / 3;
  }

  .notice {
    height: min-content;
  }
}

.note {
  position: absolute;
  top: 16px;
  right: 24px;
}

.coursesByUser {
  .check {
    margin-left: auto;
  }
}

.check {
  display: flex;
  align-items: center;
  color: var(--color-gray-90);
  font-weight: 500;
  transition: 0.2s;

  &:hover {
    color: var(--color-primary);
  }
}

.phishing {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .description {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    align-items: center;
  }

  .title {
    font: var(--font-text-1-medium);
    color: var(--color-gray-90);
  }

  .link {
    font: var(--font-text-2-medium);
    color: var(--color-gray-80);
  }
}

.statistics {
  .title {
    font: var(--font-text-1-medium);
    color: var(--color-gray-90);
    margin-bottom: 16px;
  }

  .widgets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;

    .widgetInfo {
      flex-direction: column;
      gap: 8px;
    }

    .widgetHint {
      display: flex;
      gap: 6px;
      align-items: center;
      color: var(--color-gray-80);
      font-size: 12px;
      line-height: 14px;
    }

    .phishingStats {
      margin-top: auto;
    }
  }
}

.tooltip {
  transform: translateX(20px) translateY(-50%);
}

.open {
  text-decoration: underline;
  cursor: pointer;
}
