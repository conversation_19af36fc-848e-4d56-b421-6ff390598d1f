import { FC } from 'react'
import styles from './campaign-by-tags-statictics.module.scss'
import classNamesBind from 'classnames/bind'
import { CampaignStatisticsProps } from './campaign-by-tags-statictics'
import { useCampaignStatistics } from './hooks/use-campaign-statistics'
import { Breadcrumbs } from '@/shared/ui'
import SettingsBoldIcon from '@/shared/ui/Icon/icons/components/SettingsBoldIcon'
import LightningIcon from '@/shared/ui/Icon/icons/components/LightningIcon'
import EmailingIcon from '@/shared/ui/Icon/icons/components/EmailingIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { PhishingCampaignIncidentCard } from '@/shared/components'
import CampaignStatisticsTabs from '../campaign-statistics-tabs'
import { useTranslation } from 'react-i18next'
import { removeLastSegmentAfterDelimiter } from '@/shared/helpers'

const cx = classNamesBind.bind(styles)

export const CampaignByTagsStatistics: FC<CampaignStatisticsProps.Props> = props => {
  const { className } = props
  const { BREADCRUMBS, campaignData } = useCampaignStatistics()
  const { t } = useTranslation()

  return (
    <div className={cx('wrapper', className)}>
      <Breadcrumbs items={BREADCRUMBS} />
      <div className={cx('title__wrapper')}>
        {campaignData?.is_testing && (
          <IconWrapper size='32' color='primary'>
            <SettingsBoldIcon />
          </IconWrapper>
        )}
        {campaignData?.is_autophish && (
          <IconWrapper size='32' color='primary'>
            <LightningIcon />
          </IconWrapper>
        )}
        {campaignData && !campaignData?.is_autophish && !campaignData?.is_testing && (
          <IconWrapper size='32' color='primary'>
            <EmailingIcon />
          </IconWrapper>
        )}
        <h2 className={cx('title')}>{removeLastSegmentAfterDelimiter(campaignData?.name) || ''}</h2>
      </div>
      <PhishingCampaignIncidentCard
        withEndDate={false}
        dateTooltip={t('commons:start_mailing')}
        data={campaignData}
        className={cx('incident__wrapper')}
      />
      <CampaignStatisticsTabs />
    </div>
  )
}
