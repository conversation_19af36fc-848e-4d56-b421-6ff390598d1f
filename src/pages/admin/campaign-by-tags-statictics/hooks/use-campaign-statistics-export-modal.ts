/* eslint-disable @typescript-eslint/no-explicit-any */
import { QueryStatus } from '@reduxjs/toolkit/query'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useDownload } from '@/shared/hooks'
import { phishingQueries, phishingMutations } from '@/entities/phishing'

const REPORT_POOLING_TIMEOUT = 1500

export const useCampaignStatisticsExportModal = () => {
  const { campaign_id = '' } = useParams()

  const [generateReport, { data: generateReportData, reset, isError }] =
    phishingMutations.useCreatePhishingCampaignReportMutation()

  const [isPooling, setIsPooling] = useState(false)

  const { data: reportData } = phishingQueries.useGetPhishingCampaignsReportQuery(
    (generateReportData as any)?.id,
    {
      skip: !isPooling || !generateReportData?.id,
      pollingInterval: isPooling ? REPORT_POOLING_TIMEOUT : 0,
    },
  )

  const getGenerateReportId = async () => {
    if (!campaign_id) return

    await generateReport({
      campaign_id,
    })
      .unwrap()
      .then(data => {
        if (data.status !== 'error') setIsPooling(true)
      })
      .catch(() => setIsPooling(false))
  }

  useEffect(() => {
    getGenerateReportId()

    return () => {
      reset()
    }
  }, [])

  useEffect(() => {
    if (reportData?.status === 'error') {
      setIsPooling(false)
    }
  }, [reportData?.status])

  useEffect(() => {
    const isUrlExists = !!reportData?.url || !!generateReportData?.url

    if (!isUrlExists) return

    setIsPooling(false)
  }, [generateReportData?.url, reportData?.url])

  const [download] = useDownload()

  const onDownload = () => {
    const url = generateReportData?.url || reportData?.url
    if (!url) return
    download(url)
  }

  const isUrlExists = !!reportData?.url || !!generateReportData?.url
  const isErrorStatus = reportData?.status === 'error' || generateReportData?.status === 'error'

  const generatedStatus = isUrlExists
    ? QueryStatus.fulfilled
    : isError || isErrorStatus
      ? QueryStatus.rejected
      : status === QueryStatus.rejected
        ? QueryStatus.rejected
        : QueryStatus.pending

  return {
    generatedStatus,
    onDownload,
  }
}
