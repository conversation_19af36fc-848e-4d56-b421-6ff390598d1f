import { useMemo } from 'react'
import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { phishingQueries } from '@/entities/phishing'
import { removeLastSegmentAfterDelimiter } from '@/shared/helpers'

export type CampaignStatisticsSearchParams = {
  type: 'users' | 'departments'
  page: number
}

export const useCampaignStatistics = () => {
  const { campaign_id = '' } = useParams()

  const { data: campaignData } = phishingQueries.useGetPhishingCampaignsByIdQuery(
    { id: campaign_id, params: { by_tag: true } },
    {
      skip: !campaign_id,
    },
  )

  const { t } = useTranslation()

  const BREADCRUMBS = useMemo(
    () => [
      {
        id: '/lk/admin/phishing/phishing-by-tags',
        text: t('commons:phishing_by_tags'),
        clickable: true,
      },
      {
        id: '/lk/admin/phishing/phishing-by-tags/campaigns/' + campaignData?.id,
        text: removeLastSegmentAfterDelimiter(campaignData?.name) || t('current_newsletter'),
        clickable: true,
        isLoading: !campaignData,
      },
      {
        id: '/lk/admin/phishing/phishing-by-tags/campaigns/' + campaignData?.id + '/statistics',
        text: t('commons:statistics'),
        clickable: true,
      },
    ],
    [campaignData, t],
  )

  return {
    BREADCRUMBS,
    campaignData,
  }
}
