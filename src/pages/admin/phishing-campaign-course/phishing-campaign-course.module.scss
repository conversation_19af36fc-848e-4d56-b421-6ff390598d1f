.wrapper {
  color: var(--color-gray-90);
}

.title {
  font: var(--font-title-4-normal);
  margin-bottom: 12px;
}

.link {
  display: block;
  width: 300px;
}

.subtitle {
  font: var(--font-text-1-normal);
  margin-bottom: 8px;
}

.block {
  background-color: #ffffff;
  border-radius: 16px;
  border: 1px solid var(--stroke);
  width: 300px;
  padding: 16px;
  min-height: 180px;
  display: flex;
  flex-direction: column;
}

.top {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 12px;
}

.icon {
  path {
    stroke: var(--color-gray-70) !important;
  }
}

.imageWrapper {
  align-items: center;
  backdrop-filter: blur(2px);
  background: #e1e4eb;
  display: flex;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 16px;
  border: 1px solid (--stroke);
}

.course {
  font: var(--font-text-2-demibold);
}

.image {
  width: 48px;
  height: 48px;
  border-radius: 16px;
  border: 1px solid var(--stroke);
}

.bottom {
  margin-top: auto;
  color: var(--color-gray-80);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font: var(--font-caption-1-normal);
}

.disabled {
  pointer-events: none;
}
