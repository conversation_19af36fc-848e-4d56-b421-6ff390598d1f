/* eslint-disable @typescript-eslint/ban-ts-comment */
import classNamesBind from 'classnames/bind'
import React, { useMemo } from 'react'
import styles from './phishing-campaign-course.module.scss'
import { PhishingCampaignCourseProps } from './phishing-campaign-course.d'
import ArrowRightIcon from '@/shared/ui/Icon/icons/components/ArrowRightIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { DefaultImage } from './default-image'
import { NavLink } from 'react-router-dom'
import { fromISO } from '@/shared/helpers/date'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const PhishingCampaignCourse: React.FC<PhishingCampaignCourseProps.Props> = ({
  campaignCourse,
  campaignData,
}) => {
  const { t } = useTranslation()

  const targetsTranslates = useMemo(
    () => ({
      opened: t('commons:letter_opened').toLocaleLowerCase(),
      entered_data: t('commons:entered_data').toLocaleLowerCase(),
      clicked: t('commons:followed_link').toLocaleLowerCase(),
      opened_attachment: t('commons:opened_attachments').toLocaleLowerCase(),
    }),
    [t],
  )

  if (
    !campaignCourse ||
    Object.keys(campaignCourse).length === 0 ||
    !campaignData ||
    (campaignData.status === 'completed' && campaignCourse.users_count === 0)
  ) {
    return null
  }

  const { status } = campaignData
  const { targets, title, users_count, start_date, end_date, new_assigned_course_id } =
    campaignCourse
  const isActive = status === 'completed' && users_count !== 0

  const renderTargets = () =>
    targets?.map((item, index) => `${index !== 0 ? ', ' : ''}${targetsTranslates[item]}`)

  return (
    <div className={cx('wrapper')}>
      <div className={cx('title')}>
        {isActive ? t('commons:assigned_course_after_end') : t('commons:assign_course_after_end')}
      </div>
      <div className={cx('subtitle')}>
        {t('commons:for_those_who')} {renderTargets()}:
      </div>
      <NavLink
        className={cx('link', !isActive && 'disabled')}
        to={`/lk/admin/learning/assigned/${new_assigned_course_id}`}
      >
        <div className={cx('block')}>
          <div className={cx('top')}>
            <div className={cx('imageWrapper')}>
              <DefaultImage />
            </div>
            <IconWrapper className={cx('icon')} size='20'>
              <ArrowRightIcon />
            </IconWrapper>
          </div>
          <div className={cx('course')}>{title}</div>
          <div className={cx('bottom')}>
            {isActive && (
              <div className={cx('count')}>{t('commons:people_count', { count: users_count })}</div>
            )}
            {isActive && start_date && end_date && (
              <div className={cx('dates')}>
                {fromISO(start_date)}-{fromISO(end_date)}
              </div>
            )}
          </div>
        </div>
      </NavLink>
    </div>
  )
}
