/* eslint-disable no-useless-escape */
import { FC, useMemo, useState } from 'react'
import { PasswordRecoveryFormProps } from './password-recovery-form.d'

import classNamesBind from 'classnames/bind'
import styles from './password-recovery-form.module.scss'
import { FormTitle, Input, Button } from '@/shared/ui'
import { SubmitHandler, useForm } from 'react-hook-form'
import { Link } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { authAPI } from 'entities/auth'

const cx = classNamesBind.bind(styles)

interface Inputs {
  email: string
}

export const PasswordRecoveryForm: FC<PasswordRecoveryFormProps.Props> = props => {
  const { setStatus } = props
  const { t } = useTranslation()

  const [error, setError] = useState<string | null>(null)

  const resolver = useMemo(
    () =>
      zodResolver(
        z.object({
          email: z
            .string()
            .min(1, {
              message: t('commons:required_field'),
            })
            .email({ message: t('commons:email_example') }),
        }),
      ),
    [t],
  )

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setValue,
    setError: setErrorForm,
  } = useForm<Inputs>({
    mode: 'onBlur',
    defaultValues: {
      email: '',
    },
    resolver,
  })

  const [passwordRecovery, { isLoading }] = authAPI.usePasswordRecoveryMutation()

  const onSubmit: SubmitHandler<Inputs> = async data => {
    // TEMPTODO - надо подумать, как можно это улучшить (если вообще нужно)
    try {
      await passwordRecovery(data)
        .unwrap()
        .then(() => {
          setError('')
          reset()
          setStatus('success')
        })
        .catch(error => {
          if (error.originalStatus === 400) {
            setStatus('error')
          } else {
            const errorMessage =
              error?.data?.detail || error?.data?.details || error?.data?.message || error?.message
            setError(errorMessage)
          }
        })
    } catch (error) {
      setError(t('commons:error_unexpected'))
    }
  }

  const handleTrim = (str: string, name: keyof Inputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={cx('wrapper')}>
      <FormTitle>{t('commons:password_recovery')}</FormTitle>
      <Input
        label={t('commons:enter_email_send_recovery_link')}
        type='email'
        // eslint-disable-next-line i18next/no-literal-string
        placeholder='Email'
        classNameWrapper={cx('input')}
        error={errors.email?.message}
        fullWidth
        register={register('email', {
          onBlur: e => handleTrim(e.target.value, 'email', true),
        })}
      />
      <Button
        type='submit'
        color='green'
        size='big'
        loading={isLoading}
        fullWidth
        disabled={!isValid}
      >
        {t('commons:send')}
      </Button>
      {error && <div className={cx('error-text')}>{error}</div>}

      {/* TEMPTODO - надо будет скорее сделать новый или перенести */}
      <Link to='/auth/login' className={cx('restorePassword')}>
        {t('commons:return_to_entrance')}
      </Link>
    </form>
  )
}
