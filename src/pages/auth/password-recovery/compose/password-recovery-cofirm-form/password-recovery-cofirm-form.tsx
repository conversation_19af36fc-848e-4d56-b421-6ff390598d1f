import { FC } from 'react'
import { PasswordRecoveryCofirmFormProps } from './password-recovery-cofirm-form.d'

import classNamesBind from 'classnames/bind'
import styles from './password-recovery-cofirm-form.module.scss'
import { FormTitle, Button } from '@/shared/ui'
import { Link, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const PasswordRecoveryCofirmForm: FC<PasswordRecoveryCofirmFormProps.Props> = () => {
  const navigate = useNavigate()
  const { t } = useTranslation()

  const handleClick = () => {
    navigate('/lk/user/learning')
  }

  return (
    <div className={cx('wrapper')}>
      <FormTitle>{t('commons:letter_delivered')}</FormTitle>

      <div className={cx('text')}>{t('commons:follow_link_email_reset_password')}</div>

      <Button type='submit' color='green' size='big' fullWidth onClick={handleClick}>
        {t('commons:come_in')}
      </Button>

      {/* TEMPTODO - надо будет скорее сделать новый или перенести */}
      <Link to='/auth/login' className={cx('restorePassword')}>
        {t('commons:return_to_entrance')}
      </Link>
    </div>
  )
}
