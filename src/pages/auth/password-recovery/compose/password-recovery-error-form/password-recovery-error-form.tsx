import { FC } from 'react'
import { PasswordRecoveryErrorFormProps } from './password-recovery-error-form.d'

import classNamesBind from 'classnames/bind'
import styles from './password-recovery-error-form.module.scss'
import { FormTitle, Button } from '@/shared/ui'
import { Link, useNavigate } from 'react-router-dom'
import { URLS } from '@/shared/configs/urls'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const PasswordRecoveryErrorForm: FC<PasswordRecoveryErrorFormProps.Props> = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  const handleClick = () => {
    navigate(URLS.USER_MY_COURSES_PAGE)
  }

  return (
    <div className={cx('wrapper')}>
      <FormTitle>{t('commons:user_not_registered')}</FormTitle>

      <div className={cx('text')}>{t('commons:follow_link_email_reset_password')}</div>

      <Button type='submit' color='green' size='big' fullWidth onClick={handleClick}>
        {t('commons:come_in')}
      </Button>

      {/* TEMPTODO - надо будет скорее сделать новый или перенести */}
      <Link to='/auth/login' className={cx('restorePassword')}>
        {t('commons:return_to_entrance')}
      </Link>
    </div>
  )
}
