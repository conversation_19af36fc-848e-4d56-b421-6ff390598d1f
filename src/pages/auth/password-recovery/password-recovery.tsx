/* eslint-disable @typescript-eslint/no-explicit-any */
import { FC, ReactElement, useState } from "react";

import classNamesBind from "classnames/bind";
import styles from "./password-recovery.module.scss";
import { PasswordRecoveryProps } from "./password-recovery.d";

import { AuthLayout } from "@/shared/components";
import {
  PasswordRecoveryForm,
  PasswordRecoveryCofirmForm,
  PasswordRecoveryErrorForm,
} from "./compose";

const cx = classNamesBind.bind(styles);

type RecoveryStates = "default" | "error" | "success";

const RECOVERY_VARIANTS: Record<
  RecoveryStates,
  (setStatus: any) => ReactElement
> = {
  error: () => <PasswordRecoveryErrorForm />,
  success: () => <PasswordRecoveryCofirmForm />,
  default: (setStatus) => <PasswordRecoveryForm setStatus={setStatus} />,
};

export const PasswordRecovery: FC<PasswordRecoveryProps.Props> = (props) => {
  const { className } = props;

  const [status, setStatus] = useState<RecoveryStates>("default");

  return (
    <AuthLayout className={cx(className, "wrapper")}>
      {RECOVERY_VARIANTS[status](setStatus)}
    </AuthLayout>
  );
};
