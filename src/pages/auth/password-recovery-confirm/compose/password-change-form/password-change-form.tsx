import { FC, useState } from 'react'
import { PasswordChangeFormProps } from './password-change-form.d'

import classNamesBind from 'classnames/bind'
import styles from './password-change-form.module.scss'
import { FormTitle, Input, Button, PasswordGenerator } from '@/shared/ui'
import { SubmitHandler, useForm } from 'react-hook-form'
import { Link, useNavigate, useParams } from 'react-router-dom'
import { URLS } from '@/shared/configs/urls'
import { useTranslation } from 'react-i18next'
import { authAPI } from 'entities/auth'

const cx = classNamesBind.bind(styles)

interface Inputs {
  password: string
  passwordConfirm: string
}

export const PasswordChangeForm: FC<PasswordChangeFormProps.Props> = () => {
  const { recovery_token = '' } = useParams()
  const navigate = useNavigate()
  const { t } = useTranslation()

  const [restore, { isLoading }] = authAPI.useRestoreMutation()
  const { data: resetTokenInfo } = authAPI.useResetTokenQuery(recovery_token)

  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setValue,
  } = useForm<Inputs>({
    mode: 'onBlur',
    defaultValues: {
      password: '',
      passwordConfirm: '',
    },
  })

  const onSubmit: SubmitHandler<Inputs> = async data => {
    const { passwordConfirm, password } = data

    if (password !== passwordConfirm) return setError(t('commons:passwords_equal_error'))
    try {
      await restore({ password, reset_token: recovery_token })
        .unwrap()
        .then(() => {
          setError('')
          reset()

          navigate(URLS.USER_MY_COURSES_PAGE)
        })
        .catch(error => {
          const errorMessage =
            error?.data?.detail || error?.data?.details || error?.data?.message || error?.message
          setError(errorMessage)
        })
    } catch (error) {
      setError(t('commons:error_unexpected'))
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={cx('wrapper')}>
      <FormTitle className={cx('title')}>{t('commons:password_recovery')}</FormTitle>
      <Input
        type='password'
        placeholder={t('commons:password')}
        classNameWrapper={cx('input')}
        error={errors.password?.message}
        fullWidth
        register={register('password', {
          required: t('commons:required_field'),
          minLength: {
            value: 8,
            message: t('commons:password_short_error'),
          },
        })}
      />
      <Input
        type='password'
        placeholder={t('commons:repeat_password')}
        classNameWrapper={cx('input')}
        error={errors.passwordConfirm?.message}
        fullWidth
        register={register('passwordConfirm', {
          required: t('commons:required_field'),
        })}
      />

      <PasswordGenerator
        organizationId={resetTokenInfo?.organization.id || ''}
        roles={resetTokenInfo?.roles || []}
        onPasswordChange={password => {
          setValue('password', password, {
            shouldValidate: true,
          })
          setValue('passwordConfirm', password, {
            shouldValidate: true,
          })
        }}
      />

      <Button
        type='submit'
        color='green'
        size='big'
        loading={isLoading}
        fullWidth
        disabled={!isValid}
      >
        {t('commons:restore')}
      </Button>
      {error && <div className={cx('error-text')}>{error}</div>}

      {/* TEMPTODO - надо будет скорее сделать новый или перенести */}
      <Link to={URLS.PASSWORD_RECOVERY_PAGE} className={cx('restorePassword')}>
        {t('commons:forgot_password')}
      </Link>
    </form>
  )
}
