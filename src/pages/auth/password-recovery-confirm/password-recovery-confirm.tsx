import { FC } from "react";

import classNamesBind from "classnames/bind";
import styles from "./password-recovery-confirm.module.scss";
import { PasswordRecoveryConfirmProps } from "./password-recovery-confirm.d";

import { AuthLayout } from "@/shared/components";
import { PasswordChangeForm } from "./compose";

const cx = classNamesBind.bind(styles);

export const PasswordRecoveryConfirm: FC<PasswordRecoveryConfirmProps.Props> = (
  props
) => {
  const { className } = props;

  return (
    <AuthLayout className={cx(className, "wrapper")}>
      <PasswordChangeForm />
    </AuthLayout>
  );
};
