import { FC, useEffect } from 'react'

import classNamesBind from 'classnames/bind'
import styles from './login.module.scss'
import { LoginProps } from './login.d'

import { AuthLayout } from '@/shared/components'
import { LoginForm } from './login-form'
import { LoginSSOForm } from './login-sso-form'
import { useConfig } from '@/shared/hooks'
import { URLS } from '@/shared/configs/urls'
import { useAnalytics } from '@/shared/hooks/use-analytics'

const cx = classNamesBind.bind(styles)

export const Login: FC<LoginProps.Props> = props => {
  const { className } = props

  const config = useConfig()
  const analytics = useAnalytics()

  useEffect(() => {
    analytics.route(URLS.LOGIN_PAGE)
  }, [analytics])

  const isSSO = Boolean(config?.useSSO || config?.use_admin_sso)

  return (
    <AuthLayout className={cx(className, 'wrapper')}>
      {isSSO ? <LoginSSOForm /> : <LoginForm />}
    </AuthLayout>
  )
}

export default Login
