import { useEffect } from 'react'
import { useConfig } from '@/shared/hooks'
import { useAppDispatch } from '@/store'
import { logIn } from '@/store/slices/auth'
import { URLS } from '@/shared/configs/urls'
import { useNavigate } from 'react-router-dom'
import { userAPI } from 'entities/employee'
import { authAPI } from 'entities/auth'

export const useSSOLogin = () => {
  const config = useConfig()
  const [ssoLogin] = authAPI.useLazySsoLoginQuery()
  const [userTrigger] = userAPI.endpoints.getUserInfo.useLazyQuery()
  const dispatch = useAppDispatch()
  const navigate = useNavigate()

  useEffect(() => {
    const withSSO = Boolean(config?.use_sso)

    if (!withSSO) return

    const fn = async () => {
      try {
        await ssoLogin().unwrap()
        dispatch(logIn())

        await userTrigger().unwrap()
        navigate(URLS.USER_MY_COURSES_PAGE)
      } catch (err) {
        console.warn('Failed to authenticate via sso')
      }
    }
    fn()
  }, [ssoLogin, dispatch, navigate, userTrigger, config?.use_sso])

  return
}
