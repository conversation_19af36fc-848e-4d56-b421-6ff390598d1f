/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-useless-escape */
import { FC, useMemo, useState } from 'react'
import styles from './login-sso-form.module.scss'
import classNamesBind from 'classnames/bind'
import { LoginSSOFormProps } from './login-sso-form.d'
import { SubmitHandler, useForm } from 'react-hook-form'
import { Button, FormTitle, Input } from '@/shared/ui'
import { Modal } from '@/shared/components'
import { LoginForm } from '../login-form'
import { URLS } from '@/shared/configs/urls'
import { useTranslation } from 'react-i18next'
import { useSSOLogin } from '../use-sso-login'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { authAPI } from 'entities/auth'
import { userAPI } from 'entities/employee'
import { useRedirectStorage } from '@/shared/hooks/use-redirect-storage'

const cx = classNamesBind.bind(styles)

interface Inputs {
  username: string
  password: string
}

export const LoginSSOForm: FC<LoginSSOFormProps.Props> = () => {
  const [login, { isLoading }] = authAPI.useLoginLDAPMutation()
  const [error, setError] = useState<string | null>(null)
  const [userTrigger] = userAPI.endpoints.getUserInfo.useLazyQuery()
  const { redirect } = useRedirectStorage({
    storageKey: 'redirectUrl',
    fallbackPath: URLS.USER_MY_COURSES_PAGE,
  })
  const [activeSimpleLogin, setActiveSimpleLogin] = useState(false)
  const { t } = useTranslation()

  useSSOLogin()

  const onSubmit: SubmitHandler<Inputs> = async data => {
    try {
      await login(data)
        .unwrap()
        .then(() => {
          userTrigger()
            .unwrap()
            .then(() => {
              redirect()
            })
            .catch((e: any) => {
              console.error('login error', e)
            })

          setError('')
          reset()
          // window.location.reload();
        })
        .catch(error => setError(error.data.message))
    } catch (error) {
      setError(t('commons:error_unexpected'))
    }
  }

  const resolver = useMemo(
    () =>
      zodResolver(
        z.object({
          username: z.string().min(1, {
            message: t('commons:required_field'),
          }),
          // не добавлять тут валидацию по email -> ломает вход по AD для онпрема
          // .email({ message: t('commons:email_example') }),
          password: z
            .string()
            .min(1, {
              message: t('commons:required_field'),
            })
            .refine(val => !!val.trim()),
        }),
      ),
    [t],
  )

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setError: setErrorForm,
    setValue,
  } = useForm<Inputs>({
    mode: 'onBlur',
    defaultValues: {
      username: '',
      password: '',
    },
    resolver,
  })

  const handleTrim = (str: string, name: keyof Inputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={cx('wrapper')}>
      <FormTitle>{t('commons:enter_ad')}</FormTitle>
      <Input
        label={t('commons:login')}
        type='username'
        placeholder={t('commons:enter_login_domain')}
        classNameWrapper={cx('input', '')}
        error={errors.username?.message}
        fullWidth
        register={register('username', {
          onBlur: e => handleTrim(e.target.value, 'username', true),
        })}
      />
      <Input
        label={t('commons:password')}
        type='password'
        placeholder={t('commons:enter_password')}
        classNameWrapper={cx('input', 'double')}
        error={errors.password?.message}
        fullWidth
        register={register('password')}
      />
      <div className={cx('buttonWrapper')}>
        <Button
          type='submit'
          color='green'
          size='big'
          loading={isLoading}
          fullWidth
          disabled={!isValid}
        >
          {t('commons:come_in')}
        </Button>
      </div>
      {error && <div className={cx('error-text')}>{error}</div>}
      <div className={cx('loginModal')} onClick={() => setActiveSimpleLogin(true)}>
        {t('commons:regular_entrance')}
      </div>
      {activeSimpleLogin && (
        <Modal className={cx('modal')} setActive={setActiveSimpleLogin} active={activeSimpleLogin}>
          <LoginForm />
        </Modal>
      )}
    </form>
  )
}
