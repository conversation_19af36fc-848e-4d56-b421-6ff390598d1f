import { TwoFaAuth, LazyQrCode } from '@/shared/components'
import { TwoFATestingRequest } from '@/shared/types/store/twofa'
import { HelpIcon } from '@/shared/ui'
import { FC, Suspense, useCallback } from 'react'
import { useNotification } from '@/shared/contexts/notifications'
import { useAppDispatch, useAppSelector } from '@/store'
import { selectTwofaAssignCode, setAuthSteps } from '@/store/slices/auth'
import styles from './login-form.module.scss'
import classNamesBind from 'classnames/bind'
import { useTranslation } from 'react-i18next'
import { authAPI } from 'entities/auth'

const cx = classNamesBind.bind(styles)

type LoginTwofaVerify = {
  email: string
}

export const LoginTwofaVerify: FC<LoginTwofaVerify> = ({ email }) => {
  const { t } = useTranslation()
  const { handleResponse } = useNotification()
  const dispatch = useAppDispatch()
  const twofaAssignCode = useAppSelector(selectTwofaAssignCode)
  const [twofaCodeTesting, { isError: twofaError, isLoading: isTwofaTestingLoading }] =
    authAPI.useTwofaCodeTestingMutation()

  const onCallTwoFa = useCallback(
    async (values: TwoFATestingRequest) => {
      await twofaCodeTesting({
        ...values,
        notificationTip: t('commons:confirm_two_factor_auth'),
      })
        .unwrap()
        .then(async () => {
          dispatch(setAuthSteps({ twofa__assign: false }))
          dispatch(setAuthSteps({ twofa: true }))
        })
    },
    [twofaCodeTesting, handleResponse, dispatch, t],
  )

  return (
    <div className={cx('twofa__assign')}>
      <h2 className={cx('twofa__assign__title')}>{t('commons:two_factor_authentication')}</h2>
      <Suspense>
        <LazyQrCode value={twofaAssignCode} />
      </Suspense>
      <div className={cx('twofa__assign__hint')}>
        <p>{t('commons:find_more')}:</p>
        <HelpIcon text={t('commons:e2e_hint')} />
      </div>
      <TwoFaAuth
        isError={twofaError}
        inputProps={{ disabled: isTwofaTestingLoading }}
        className={cx(styles.twofa__input)}
        animationDuration={4000}
        codeLength={6}
        onSuccess={v => {
          onCallTwoFa({
            code: v,
            email: email,
          })
        }}
      />
    </div>
  )
}
