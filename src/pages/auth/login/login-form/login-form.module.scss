.wrapper {
  width: 100%;
  > *:last-child {
    margin-bottom: 0 !important;
  }
}

.twofa {
  display: flex;
  flex-direction: column;
  text-align: center;
  &__input {
    font: var(--font-title-4-medium);
    text-align: center;
  }
  &__qr {
    margin-bottom: 40px;
  }
}

.twofa__assign {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: center;

  &__title {
    font: var(--font-title-4-normal);
    text-align: center;
  }

  &__hint {
    display: flex;
    gap: 4px;
    margin-left: auto;
  }
}

.input {
  display: block;
  margin-bottom: 12px;
  &.double {
    margin-bottom: 24px;
  }
}

.errorText {
  margin-top: 12px;

  text-align: center;
}

.adfs-button {
  color: var(--gray-gray-80, #5c6585);

  cursor: pointer;
  font: var(--font-caption-1-normal);

  margin-top: 16px;
  text-align: center;

  transition: var(--transition);
  &:hover {
    color: var(--color-primary, #3dbc87);

    transition: var(--transition);
  }
}

.ssoLogin {
  color: #5c6585;

  cursor: pointer;
  font-family: 'TT Norms Pro';
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-top: 16px;
  text-align: center;
}

.restorePassword {
  color: var(--color-primary, #3dbc87);
  display: block;
  font: var(--font-caption-1-normal);
  margin-top: 16px;
  text-align: center;
  &:hover,
  &:active {
    color: var(--color-primary, #3dbc87);
  }
}
