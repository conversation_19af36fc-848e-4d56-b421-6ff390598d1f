import { useCallback, useEffect } from 'react'
import { ADFSApi } from '@/entities/adfs'
import { useConfig } from '@/shared/hooks'
import { selectAuthSteps } from '@/store/slices/auth'
import { useAppSelector } from '@/store'

export const useSAML = () => {
  const [getADFSRedirect] = ADFSApi.useLazyGetADFSRedirectQuery()
  const authSteps = useAppSelector(selectAuthSteps)
  const config = useConfig()

  const SamlLogin = useCallback(() => {
    getADFSRedirect(window.location.origin)
      .unwrap()
      .then(({ url }) => window.location.replace(url))
  }, [getADFSRedirect])

  useEffect(() => {
    if (config?.forse_saml && !authSteps.permissions) {
      SamlLogin()
    }
  }, [SamlLogin, authSteps.permissions, config])

  return {
    SamlLogin,
  }
}
