import { TwoFaAuth, LazyQrCode } from '@/shared/components'
import { HelpIcon } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './registration-form.module.scss'
import { Suspense, useCallback } from 'react'
import { TwoFATestingRequest } from '@/shared/types/store/twofa'
import { useNavigate, useParams } from 'react-router'
import { useTranslation } from 'react-i18next'
import { userAPI } from 'entities/employee'
import { authAPI } from 'entities/auth'

const cx = classNamesBind.bind(styles)

const HINT = () => {
  const { t } = useTranslation()
  return <span>{t('commons:e2e_hint')}</span>
}

export const RegistrationTwoFa = () => {
  const { registration_token = '' } = useParams()
  const { data } = authAPI.useGetRegistrationInfoQuery({
    invite_token: registration_token,
  })
  const [userTrigger] = userAPI.endpoints.getUserInfo.useLazyQuery()
  const { t } = useTranslation()

  const navigate = useNavigate()

  const [verifyTrigger, { isError: twofaError, isLoading: isTwofaTestingLoading }] =
    authAPI.useTwofaCodeVerifyMutation()

  const onCallTwoFa = useCallback(
    async (values: TwoFATestingRequest) => {
      await verifyTrigger(values).unwrap()

      await userTrigger().unwrap()
      navigate('/lk/user/learning')
    },
    [verifyTrigger, userTrigger, navigate],
  )

  return (
    <div className={cx('twofa')}>
      <h2 className={cx('twofa__title')}>{t('commons:two_factor_authentication')}</h2>
      <Suspense>
        <LazyQrCode />
      </Suspense>
      <div className={cx('twofa__hint')}>
        <p>{t('commons:find_more')}:</p>
        <HelpIcon text={HINT()} />
      </div>
      <TwoFaAuth
        isError={twofaError}
        inputProps={{ disabled: isTwofaTestingLoading }}
        className={cx(styles.twofa__input)}
        animationDuration={4000}
        codeLength={6}
        onSuccess={v => {
          if (data)
            onCallTwoFa({
              code: v,
              email: data?.email,
            })
        }}
      />
    </div>
  )
}
