.wrapper {
  width: 100%;
  > *:last-child {
    margin-bottom: 0 !important;
  }
}

.twofa {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: center;

  &__title {
    font: var(--font-title-4-normal);
    text-align: center;
  }

  &__hint {
    display: flex;
    gap: 4px;
    margin-left: auto;
  }
}

.input {
  margin-bottom: 12px;
  &.double {
    margin-bottom: 24px;
  }
}

.policyWrapper {
  align-items: center;
  display: grid;
  grid-gap: 8px;
  grid-template-columns: auto 1fr;
  margin-bottom: 24px;
  span {
    color: #343b54;
    font: var(--font-caption-1-normal);
    a {
      color: var(--color-primary, #3dbc87);
      font: var(--font-caption-1-normal);
    }
  }
}

.loader {
  align-items: center;
  display: flex;
  justify-content: center;
  padding: 50px 0;
  width: 100%;
}

.description {
  font: var(--font-caption-1-normal);
  margin-bottom: 16px;
  text-align: center;
}

.error-token {
  text-align: center;
}

.error-text {
  color: #ff8577;
  font-family: 'TT Norms Pro';
  font-size: 11px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-top: 12px;
  text-align: center;
}
