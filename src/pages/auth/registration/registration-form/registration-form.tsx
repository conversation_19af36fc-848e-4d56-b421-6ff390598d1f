import { FC, useEffect, useMemo, useState } from 'react'
import { RegistrationFormProps } from './registration-form.d'

import classNamesBind from 'classnames/bind'
import styles from './registration-form.module.scss'
import { FormTitle, Input, Button, Select, PasswordGenerator } from '@/shared/ui'
import { SubmitHandler, useForm } from 'react-hook-form'
import { Loader, IListItem } from '@/shared/ui'
import { useNavigate, useParams } from 'react-router-dom'
import { RegistrationTwoFa } from './registration-twofa'
import { Checkbox } from '@/shared/ui/checkbox'
import { URLS } from '@/shared/configs/urls'
import { useConfig } from '@/shared/hooks'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { authAPI } from 'entities/auth'

const cx = classNamesBind.bind(styles)

interface Inputs {
  middle_name: string
  first_name: string
  last_name: string
  position: string
  password: string
  passwordConfirm: string
  department: IListItem | null
}

export const RegistrationForm: FC<RegistrationFormProps.Props> = props => {
  const { className } = props
  const { registration_token = '' } = useParams()
  const { t } = useTranslation()

  const config = useConfig()

  const [isAgree, setIsAgree] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isPasswordMatch, setIsPasswordMatch] = useState(true)

  const navigate = useNavigate()

  const [step, setStep] = useState(1)
  const [registration, { isLoading }] = authAPI.useRegistrationMutation()

  const { data, error: errorGetInfo } = authAPI.useGetRegistrationInfoQuery({
    invite_token: registration_token,
  })

  const [isSetValue, setIsSetValue] = useState(false)

  useEffect(() => {
    if (isSetValue || !data) return

    setValue('middle_name', data?.middle_name || '')
    setValue('first_name', data?.first_name || '')
    setValue('last_name', data?.last_name || '')
    setValue('position', data?.position || '')

    if (data?.department?.id) {
      setValue('department', {
        title: data.department.title,
        id: data.department.id,
      })
    }

    setIsSetValue(() => true)
  }, [data, isSetValue])

  const resolver = useMemo(
    () =>
      zodResolver(
        z
          .object({
            last_name: z.string().min(1, {
              message: t('commons:required_field'),
            }),
            first_name: z.string().min(1, {
              message: t('commons:required_field'),
            }),
            middle_name: z.string(),
            position: z.string().max(255, {
              message: t('commons:length_limit', { count: 255 }),
            }),
            password: z.string().min(1, {
              message: t('commons:required_field'),
            }),
            passwordConfirm: z.string().min(1, {
              message: t('commons:required_field'),
            }),
            department: z.any(),
          })
          .refine(data => !!data.department),
      ),
    [t],
  )

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    getValues,
    setValue,
    watch,
    setError: setErrorForm,
  } = useForm<Inputs>({
    mode: 'onBlur',
    defaultValues: {
      middle_name: '',
      first_name: '',
      last_name: '',
      position: '',
      password: '',
      passwordConfirm: '',
      department: null,
    },
    resolver,
  })

  const department = watch('department')

  const goMain = () => navigate(URLS.USER_MY_COURSES_PAGE)
  const goPasswordRecovery = () => navigate(URLS.PASSWORD_RECOVERY_PAGE)

  const onSubmit: SubmitHandler<Inputs> = async submitData => {
    const { first_name, middle_name, last_name, password, position, department } = submitData

    if (!department) return

    // TEMPTODO - надо подумать, как можно это улучшить (если вообще нужно)
    try {
      await registration({
        first_name,
        middle_name,
        last_name,
        password,
        position,
        department,
        invite_token: registration_token,
        agree: isAgree,
      })
        .unwrap()
        .then(() => {
          reset()

          //TODO: проверить двухфакторку! Удалить ts-expect-error при проверке
          //@ts-expect-error - organization property exists in response but not in types
          if (data?.organization?.need_twofa) {
            setStep(2)
          } else {
            goMain()
          }
        })
        .catch(error => {
          const errorMessage =
            error?.data?.detail || error?.data?.details || error?.data?.message || error?.message
          setError(errorMessage)
        })
    } catch (error) {
      console.error('Registration error:', error)
      setError(t('commons:error_unexpected'))
    }
  }

  const handleChangeDepartment = (d: IListItem) => setValue('department', d)

  const handleCheckPassword = (password: string) => {
    setIsPasswordMatch(password === getValues('password'))
  }

  const handleTrim = (str: string, name: keyof Inputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  return (
    <>
      {!data && !errorGetInfo && <Loader className={cx('loader')} size='56' />}
      {!data && errorGetInfo && (
        <div>
          <FormTitle className={cx('error-token')}>
            {t('commons:registration_link_is_invalid')}
          </FormTitle>
          <p className={cx('description')}>
            {t('commons:registration_link_is_invalid_description')}
          </p>
          <Button fullWidth onClick={goPasswordRecovery}>
            {t('commons:password_recovery')}
          </Button>
        </div>
      )}
      {step === 1 && data && !errorGetInfo && (
        <form onSubmit={handleSubmit(onSubmit)} className={cx(className, 'wrapper')}>
          <FormTitle>{t('commons:registration')}</FormTitle>
          <Input
            placeholder={t('commons:lastname')}
            classNameWrapper={cx('input')}
            error={errors.last_name?.message}
            fullWidth
            required
            register={register('last_name', {
              onBlur: e => handleTrim(e.target.value, 'last_name', true),
            })}
          />
          <Input
            placeholder={t('commons:firstname')}
            classNameWrapper={cx('input')}
            error={errors.first_name?.message}
            fullWidth
            required
            register={register('first_name', {
              onBlur: e => handleTrim(e.target.value, 'first_name', true),
            })}
          />
          <Input
            placeholder={t('commons:middlename')}
            classNameWrapper={cx('input', 'double')}
            error={errors.middle_name?.message}
            fullWidth
            register={register('middle_name', {
              onBlur: e => handleTrim(e.target.value, 'middle_name'),
            })}
          />
          <Input
            placeholder={t('commons:position')}
            classNameWrapper={cx('input')}
            error={errors.position?.message}
            fullWidth
            register={register('position', {
              onBlur: e => handleTrim(e.target.value, 'position'),
            })}
          />
          {data && isSetValue && (
            <Select
              searchable
              className={cx('input', 'double')}
              placeholder={t('commons:department')}
              value={department?.id}
              list={data.all_departments.map(d => ({
                title: d.title,
                id: d.id,
              }))}
              handleChange={handleChangeDepartment}
            />
          )}
          <Input
            type='password'
            placeholder={t('commons:password')}
            classNameWrapper={cx('input')}
            required
            error={
              errors.password?.message ||
              (!isPasswordMatch ? t('commons:passwords_equal_error') : '')
            }
            fullWidth
            register={register('password')}
          />

          <Input
            type='password'
            placeholder={t('commons:repeat_password')}
            classNameWrapper={cx('input', 'double')}
            required
            error={
              errors.passwordConfirm?.message ||
              (!isPasswordMatch ? t('commons:passwords_equal_error') : '')
            }
            fullWidth
            register={register('passwordConfirm', {
              onBlur: e => handleCheckPassword(e.target.value),
            })}
          />

          <PasswordGenerator
            organizationId={data.organization_id}
            roles={data.roles || []}
            onPasswordChange={password => {
              setValue('password', password, {
                shouldValidate: true,
              })
              setValue('passwordConfirm', password, {
                shouldValidate: true,
              })
              setIsPasswordMatch(true)
            }}
          />

          <div className={cx('policyWrapper')}>
            <Checkbox
              label={
                <span>
                  {t('commons:accept')}{' '}
                  <a
                    href={
                      config.needPrivacyPolicyPage
                        ? URLS.PRIVACY_POLICY
                        : 'https://secure-t.ru/privacy'
                    }
                    target='_blank'
                    rel='noreferrer'
                  >
                    {t('commons:privacy_policy')}
                  </a>
                </span>
              }
              initialChecked={isAgree}
              onChange={setIsAgree}
            />
          </div>

          {isPasswordMatch}

          <Button
            type='submit'
            color='green'
            size='big'
            loading={isLoading}
            fullWidth
            disabled={!(isValid && isAgree && isPasswordMatch)}
          >
            {t('commons:register')}
          </Button>

          {error && <div className={cx('error-text')}>{error}</div>}
        </form>
      )}

      {step === 2 && <RegistrationTwoFa />}
    </>
  )
}
