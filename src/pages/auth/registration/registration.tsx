import { FC } from 'react'

import classNamesBind from 'classnames/bind'
import styles from './registration.module.scss'
import { RegistrationProps } from './registration.d'

import { AuthLayout } from '@/shared/components'
import { RegistrationForm } from './registration-form'

const cx = classNamesBind.bind(styles)

export const Registration: FC<RegistrationProps.Props> = props => {
  const { className } = props

  return (
    <AuthLayout className={cx(className)}>
      <RegistrationForm />
    </AuthLayout>
  )
}
