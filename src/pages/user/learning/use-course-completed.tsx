import { URLS } from '@/shared/configs/urls'
import { useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { userStaticticsApi } from './endpoints'

export const useCourseCompleted = () => {
  const { course_id = '' } = useParams()
  const navigate = useNavigate()
  const { data: course } = userStaticticsApi.useGetCourseStructureQuery({ course_id })

  useEffect(() => {
    if (!course) return

    if (course.status !== 'completed') return

    navigate(URLS.USER_MY_COURSES_PAGE)
  }, [course, navigate])
}
