import { TTheme as TThemeData } from "@/entities/themeCourse/model/types";

export interface TTheme {
    theme_id: UUID;
    title: string;
    passed?: boolean;
    started?: boolean;
    order_id?: number;
}

export type TSection = {
    section_id: UUID;
    title: string;
    themes: TTheme[];
    order_id: number;
}

export interface CourseProgress {
    assigned_course_id: UUID;
    passed: boolean;
    total_themes: number;
    passed_themes: number;
    sections: TSection[];
}

type TStep = {
    step_id: UUID;
    step_progress_id: UUID;
    type: 'quiz' | 'video' | 'article' | 'presentation' | 'slide' | 'scorm'
    started_at: string;
    finished_at: string;
    passed: boolean;
    started: boolean;
}

export interface ThemeProgress {
    theme_id: UUID;
    passed: boolean;
    started: boolean;
    has_quiz: boolean;
    total_theory: number;
    total_quizes: number;
    passed_theory: number;
    passed_quizes: number;
    started_at: string;
    finished_at: string;
    steps: TStep[];
}

export interface CourseStructure {
    archived: boolean; 
    available_in_demo: boolean;
    created_at: string;
    description: string;
    end_date: string;
    id: UUID;
    image_path: string;
    lock_sequence: boolean;
    need_assigned_message: boolean;
    need_notify_message: boolean;
    organization_id: UUID;
    picture: string;
    sections: {
        description: string;
        id: UUID;
        order_id: number;
        themes: TThemeData[]; 
        title: string; 
    }[];
    start_date: string;
    status: string;
    tags: string[];
    title: string;
    visibility: string;
}