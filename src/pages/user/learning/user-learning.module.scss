@use '../../../shared/assets/styles/mixins/text';

.top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.title {
  @include text.max-lines(3);

  margin-right: 30px;

  &__wrapper {
    display: flex;
    gap: 16px;
    align-items: flex-start;
    vertical-align: bottom;

    p {
      font: var(--font-text-1-normal);
      color: var(--color-gray-70);
      margin-top: 18px;
      min-width: 180px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    @media (max-width: 1024px) {
      flex-direction: column;
      gap: 8px;

      p {
        margin-top: 0;
      }
    }
  }
}

.sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: 100%;
  max-height: 100dvh;
  overflow-y: auto;

  .title {
    &__wrapper {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding-left: 20px;
      padding-right: 24px;
    }
    font: var(--font-text-1-demibold);
    font-weight: 600px;
    color: var(--color-gray-80);
  }

  .progress {
    font: var(--font-text-2-normal);
    color: var(--color-gray-80);
  }

  .progress-bar {
    width: 100%;
    background-color: var(--color-gray-50);
    border-radius: 10px;
    overflow: hidden;
  }

  .progress-bar_fill {
    height: 5px;
    width: 0;
    background-color: var(--color-primary);
    transition: width 0.3s ease;
    border-radius: 10px;
  }

  .sections {
    display: flex;
    flex-direction: column;
    overflow-y: auto;

    &__item {
      &__title {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        padding-left: 20px;
        min-height: 44px;
        cursor: pointer;

        &__status {
          margin-left: auto;
        }
      }

      &__name {
        @include text.max-lines(2);
      }
    }
  }

  .themes {
    display: flex;
    flex-direction: column;

    &__item {
      display: flex;
      align-items: center;
      gap: 8px;
      min-height: 36px;

      padding: 8px 12px;
      padding-left: 36px;
      cursor: pointer;

      &_progress {
        background-color: var(--color-primary-30);
      }

      &.active {
        background-color: var(--color-primary-30);
        color: var(--color-primary);
      }

      &__title {
        @include text.max-lines(2);
      }
    }
  }

  .disabled {
    pointer-events: none;
    opacity: 0.5;
  }

  .dot {
    width: 6px;
    height: 6px;

    * {
      width: 6px !important;
      height: 6px !important;
    }
  }
}
