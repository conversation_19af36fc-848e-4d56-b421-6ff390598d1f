import { globalBaseApi, GlobalEndpointBuilder } from '@/store/services/endpoints/base'
import {
  AnswerQuizResponse,
  AssignedCourseByUser,
  UserAssignedCoursesStatus,
  UserPassedStatResponse,
} from '../../../entities/courses/model/api/test-types'
import { CourseProgress, CourseStructure, ThemeProgress } from './types'

export const getUserStaticticsQuery = () => {
  return (build: GlobalEndpointBuilder) => ({
    getQuizesByUserId: build.query<
      {
        data: AnswerQuizResponse[]
      },
      {
        user_id: string
      }
    >({
      query: ({ user_id }) => ({
        url: `/learning/api/statistics/users/${user_id}/quizes`,
      }),
    }),
    getUserPassedStat: build.query<
      UserPassedStatResponse,
      {
        user_id: string
        status: UserAssignedCoursesStatus
      }
    >({
      query: ({ user_id, status }) => ({
        url: `/learning/api/statistics/users/${user_id}/assigned_courses/passed_stat?status=${status}`,
      }),
    }),
    getCourseProgress: build.query<CourseProgress, { course_id: UUID }>({
      query: ({ course_id }) => ({
        url: `/learning/api/learning/assigned-courses/${course_id}/my-progress`,
      }),
      transformResponse: (response: CourseProgress) => {
        const sections = [...response.sections]
        const newSec = sections.map(sec => {
          const themes = [...sec.themes]
          themes.sort((a, b) => {
            if (a.order_id && b.order_id) {
              return a.order_id - b.order_id
            }
            return 1
          })
          return {
            ...sec,
            themes: themes,
          }
        })
        newSec.sort((a, b) => a.order_id - b.order_id)
        return {
          ...response,
          sections: newSec,
        }
      },
    }),
    getCourseStructure: build.query<CourseStructure, { course_id: UUID }>({
      query: ({ course_id }) => ({
        url: `/learning/api/learning/assigned-courses/${course_id}/structure`,
      }),
      transformResponse: (response: CourseStructure) => {
        const sections = [...response.sections]
        const newSec = sections.map(sec => {
          const themes = [...sec.themes]
          const newThemes = themes.map(theme => {
            const steps = [...theme.steps]
            steps.sort((a, b) => a.order_id - b.order_id)

            return {
              ...theme,
              steps: steps,
            }
          })
          newThemes.sort((a, b) => a.order_id - b.order_id)
          return {
            ...sec,
            themes: newThemes,
          }
        })
        newSec.sort((a, b) => a.order_id - b.order_id)
        return {
          ...response,
          sections: newSec,
        }
      },
    }),
    getThemeProgress: build.query<
      ThemeProgress,
      { course_id: UUID; section_id: UUID; theme_id: UUID }
    >({
      query: ({ course_id, section_id, theme_id }) => ({
        url: `/learning/api/learning/assigned-courses/${course_id}/${section_id}/${theme_id}/my-progress`,
      }),
    }),
    getUserThemes: build.query<
      {
        data: [
          {
            title: string
            theory: number
            quiz: number
            has_quizes: boolean
            has_theory: boolean
            assigned_course_id: string
            section_id: string
            theme_id: string
            passed_theory: number
            passed_quizes: number
            total_theory: number
            total_quizes: 0
          },
        ]
      },
      {
        user_id: string
        assigned_course_id: string
      }
    >({
      query: ({ assigned_course_id, user_id }) => ({
        url: `/learning/api/statistics/users/${user_id}/assigned_courses/${assigned_course_id}/themes`,
      }),
    }),
    getUserQuizesAttempts: build.query<
      {
        data: AnswerQuizResponse[]
      },
      {
        user_id: string
        assigned_course_id: string
        section_id: string
        theme_id: string
      }
    >({
      query: ({ user_id, ...params }) => ({
        url: `/learning/api/statistics/users/${user_id}/themes/quizes-attempts`,
        params,
      }),
    }),
    getUserProgressByAssignedCourses: build.query<
      ResponseWithPagination & {
        data: AssignedCourseByUser[]
      },
      RequestWithPagination & {
        user_id: string
        status: UserAssignedCoursesStatus
      }
    >({
      query: ({ user_id, ...params }) => ({
        url: `/learning/api/statistics/users/${user_id}/assigned_courses/progress`,
        params,
      }),
    }),
    getDetailAssignedCourseStatictics: build.query<
      {
        data: [
          {
            title: string
            theory: number
            quiz: number
            themes: [
              {
                title: string
                theory: number
                quiz: number
                has_quizes: boolean
                has_theory: boolean
              },
            ]
          },
        ]
      },
      {
        assigned_course_id: string
      }
    >({
      query: ({ assigned_course_id }) => ({
        url: `/learning/api/statistics/assigned-courses/${assigned_course_id}/detail-statistics`,
      }),
    }),
  })
}

export const userStaticticsApi = globalBaseApi.injectEndpoints({
  endpoints: builder => getUserStaticticsQuery()(builder),
})
