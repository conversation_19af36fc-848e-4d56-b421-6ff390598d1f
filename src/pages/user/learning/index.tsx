/* eslint-disable react-hooks/exhaustive-deps */
import { useParams } from 'react-router-dom'
import styles from './user-learning.module.scss'
import classNamesBind from 'classnames/bind'
import { myCoursesQueryApi } from '@/entities/courses'
import { userStatictics<PERSON><PERSON> } from './endpoints'
import { useAppDispatch, useAppSelector } from '@/store'
import { ReactNode, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react'
import { setCreateContent } from '@/store/slices/sidebar-slice'
import { BackButton } from '@/shared/ui'
import BurgerIcon from '@/shared/ui/Icon/icons/components/BurgerIcon'
import Dot from '@/shared/ui/Icon/icons/components/Dot'
import SuccessIcon from '@/shared/ui/Icon/icons/components/SuccessIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { URLS } from '@/shared/configs/urls'
import { DefaultSidebarContent } from '@/shared/components'
import Skeleton from 'react-loading-skeleton'
import { TSection, TTheme } from './types'
import { ThemePreview } from '@/entities/themeCourse/ui/theme-preview'
import {
  resetUserCourseState,
  selectDisableFetch,
  selectSectionId,
  selectTheme,
  selectThemeData,
  setDisableFetch,
  setDisableNext,
  setUserCourseId,
  setUserCourseSection,
  setUserCourseStructure,
  setUserCourseTheme,
  setUserCourseThemeData,
  setUserCourseThemeProgress,
} from '@/store/slices/user-course-slice'
import { testApi } from '@/entities/courses/model/api/endpoints'
import { useTranslation } from 'react-i18next'
import { isFullscreen, pickThemeData } from './helpers'
import { useMediaQuery } from 'usehooks-ts'
import CourseProgressModal from '@/shared/modals/course-progress-modal/course-progress-modal'
import { GOALS } from '@/shared/constants'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { useCourseCompleted } from './use-course-completed'

const cx = classNamesBind.bind(styles)

const TRANSLATION_FILE = 'pages__learning__user-course'

interface TAllThemes extends TTheme {
  section_id: string
}

export const Learning = () => {
  const { course_id = '' } = useParams()
  const { t } = useTranslation(TRANSLATION_FILE)
  const dispatch = useAppDispatch()
  const currentSection = useAppSelector(selectSectionId)
  const currentTheme = useAppSelector(selectTheme)
  const themeData = useAppSelector(selectThemeData)
  const disableFetch = useAppSelector(selectDisableFetch)
  const isMobile = useMediaQuery('(max-width: 1024px)')

  const analytics = useAnalytics()
  const sentedEvent = useRef(false) // Чтобы не отправлялся ивент много раз и useRef, чтобы не ререндерилось
  useCourseCompleted()

  useEffect(() => {
    if (!course_id || !currentTheme?.title || sentedEvent.current) return

    analytics.event(GOALS['course-page'].name, { id: course_id, title: currentTheme.title })
    sentedEvent.current = true
  }, [analytics, course_id, currentTheme?.title])

  const [openProgressModal, setOpenProgressModal] = useState<boolean>(false)
  const { data } = myCoursesQueryApi.useGetMyCourseByIdQuery({ course_id })
  const [launchTheme] = testApi.useLaunchThemeMutation()
  const { data: courseProgress, refetch: refetchCourseProgress } =
    userStaticticsApi.useGetCourseProgressQuery({ course_id })
  const { data: courseStructure } = userStaticticsApi.useGetCourseStructureQuery({ course_id })
  const {
    data: themeProgress,
    refetch: refetchThemeProgress,
    isLoading,
  } = userStaticticsApi.useGetThemeProgressQuery(
    {
      course_id,
      theme_id: currentTheme?.theme_id ?? '',
      section_id: currentSection ?? '',
    },
    { skip: !course_id || !currentTheme || !currentSection || disableFetch },
  )

  useEffect(() => {
    dispatch(setDisableNext(isLoading))
  }, [isLoading])

  useEffect(() => {
    return () => {
      dispatch(resetUserCourseState())
    }
  }, [])

  useEffect(() => {
    dispatch(setUserCourseThemeProgress(themeProgress))
  }, [themeProgress])

  useEffect(() => {
    dispatch(setUserCourseId(course_id))
  }, [course_id])

  useEffect(() => {
    dispatch(setUserCourseStructure(courseStructure))
  }, [courseStructure])

  useEffect(() => {
    if (themeProgress?.started === false && currentSection && currentTheme?.theme_id) {
      launchTheme({
        body: {
          section_id: currentSection,
          theme_id: currentTheme?.theme_id,
        },
        assigned_course_id: course_id,
      }).then(() => refetchThemeProgress())
    }
  }, [themeProgress?.started, launchTheme, currentTheme?.theme_id, course_id])

  const allThemes = useMemo(() => {
    if (courseProgress) {
      return courseProgress.sections.reduce(
        (acc, item) => [
          ...acc,
          ...item.themes.map(theme => ({
            ...theme,
            section_id: item.section_id,
          })),
        ],
        [] as TAllThemes[],
      )
    }
  }, [courseProgress])

  useEffect(() => {
    if (courseProgress && allThemes && (!currentSection || !currentTheme)) {
      const index =
        courseProgress.passed_themes > 0 &&
        courseProgress.passed_themes < courseProgress.total_themes
          ? courseProgress.passed_themes
          : 0
      dispatch(setUserCourseTheme(allThemes[index]))
      dispatch(setUserCourseSection(allThemes[index].section_id))
      dispatch(setUserCourseThemeData(pickThemeData(courseStructure, allThemes[index].theme_id)))
    }
  }, [courseProgress])

  const handleThemeClick = async (theme: TTheme, sectionId: string) => {
    await dispatch(setDisableFetch(true))
    await dispatch(setUserCourseSection(sectionId))
    await dispatch(setUserCourseTheme(theme))
    analytics.event(GOALS['course-route-theme'].name, { theme, sectionId })
    dispatch(setUserCourseThemeData(pickThemeData(courseStructure, theme.theme_id)))
    await dispatch(setDisableFetch(false))
  }

  useEffect(() => {
    return () => {
      dispatch(setCreateContent(() => <DefaultSidebarContent />))
    }
  }, [])

  useLayoutEffect(() => {
    dispatch(
      setCreateContent(() => (
        <CourseProgressSidebar
          courseName={data?.assigned_course?.title ?? <Skeleton width={140} height={24} />}
          courseProgressPassed={courseProgress?.passed_themes}
          courseProgressTotal={courseProgress?.total_themes}
          sections={courseProgress?.sections ?? []}
          allThemes={allThemes}
          shouldDisable={courseStructure?.lock_sequence ?? false}
          currentTheme={currentTheme?.theme_id}
          onThemeClick={handleThemeClick}
        />
      )),
    )
  }, [dispatch, data, courseProgress?.sections, currentTheme?.theme_id, handleThemeClick])

  useEffect(() => {
    refetchCourseProgress()
  }, [currentTheme])

  const openModal = () => setOpenProgressModal(true)

  const isFullscreenEnabled = isFullscreen()

  return (
    <div className={cx('mainWrapper')}>
      {isMobile ? (
        <div className={cx('top')}>
          {!isFullscreenEnabled && (
            <BackButton route={URLS.USER_MY_COURSES_PAGE} arrowSize={'14'} />
          )}
          <div className={cx('burger')} onClick={openModal}>
            <IconWrapper color='gray70' size='16'>
              <BurgerIcon />
            </IconWrapper>
          </div>
        </div>
      ) : (
        !isFullscreenEnabled && <BackButton route={URLS.USER_MY_COURSES_PAGE} />
      )}
      <div className={cx('title__wrapper')}>
        <h1 className={cx('title')}>{currentTheme?.title}</h1>
        {themeProgress && (
          <p>
            {t('theme_progress', {
              passed: themeProgress?.passed_quizes + themeProgress?.passed_theory,
              total: themeProgress?.total_quizes + themeProgress?.total_theory,
            })}
          </p>
        )}
      </div>
      <div className={cx('wrapper')}>
        {themeData && (
          <ThemePreview themeData={themeData} refetchProgress={refetchThemeProgress} isUser />
        )}
      </div>
      {isMobile && openProgressModal && (
        <CourseProgressModal
          open={openProgressModal}
          setOpen={setOpenProgressModal}
          content={
            <CourseProgressSidebar
              courseName={data?.assigned_course?.title ?? <Skeleton width={140} height={24} />}
              courseProgressPassed={courseProgress?.passed_themes}
              courseProgressTotal={courseProgress?.total_themes}
              sections={courseProgress?.sections ?? []}
              allThemes={allThemes}
              shouldDisable={courseStructure?.lock_sequence ?? false}
              currentTheme={currentTheme?.theme_id}
              onThemeClick={handleThemeClick}
            />
          }
        />
      )}
    </div>
  )
}

type CourseProgressSidebarProps = {
  courseName: ReactNode
  courseProgressPassed?: number
  courseProgressTotal?: number
  sections: TSection[]
  shouldDisable: boolean
  allThemes?: TAllThemes[]
  currentTheme?: UUID
  onThemeClick: (theme: TTheme, section_id: string) => void
}

const CourseProgressSidebar = ({
  courseName,
  courseProgressPassed,
  courseProgressTotal,
  sections,
  allThemes,
  shouldDisable,
  currentTheme,
  onThemeClick,
}: CourseProgressSidebarProps) => {
  const { t } = useTranslation(TRANSLATION_FILE)

  const progress =
    courseProgressPassed !== undefined && courseProgressTotal !== undefined
      ? Math.max(courseProgressPassed / courseProgressTotal, 0) * 100
      : 0
  const disabledThemes = useMemo(() => {
    return shouldDisable && allThemes
      ? courseProgressPassed !== undefined
        ? allThemes.slice(courseProgressPassed + 1, allThemes.length)
        : allThemes.slice(1, allThemes.length)
      : []
  }, [shouldDisable, allThemes])

  return (
    <div className={cx('sidebar')}>
      <div className={cx('title__wrapper')}>
        <h3 className={cx('title')}>{courseName}</h3>
        <div className={cx('progress')}>
          {t('course_progress', {
            passed: courseProgressPassed,
            total: courseProgressTotal,
          })}
        </div>
        <div className={cx('progress-bar')}>
          <div className={cx('progress-bar_fill')} style={{ width: `${progress}%` }} />
        </div>
      </div>
      <ul className={cx('sections')}>
        {sections.map((section, sectIndex) => {
          const sectionIndex = sectIndex + 1
          const isPassed = section.themes.every(theme => theme.passed)
          const isProgress = !isPassed ? section.themes.some(theme => theme.passed) : false
          // const isNotStarted = !isProgress ? section.themes.every(theme => !theme.passed) : false

          return (
            <li className={cx('sections__item')} key={section.title}>
              <h4 className={cx('sections__item__title')}>
                <span>{sectionIndex} </span>
                <span className={cx('sections__item__name')}>{section.title} </span>
                <span className={cx('sections__item__title__status')}>
                  {isProgress && (
                    <IconWrapper className={cx('dot')} size='14' color='primary'>
                      <Dot />
                    </IconWrapper>
                  )}
                  {isPassed && (
                    <IconWrapper color='primary'>
                      <SuccessIcon />
                    </IconWrapper>
                  )}
                </span>
              </h4>
              <ul className={cx('themes')}>
                {section.themes.map((theme, thmIndex) => {
                  const themeIndex = thmIndex + 1
                  const isPassed = theme.passed
                  const isProgress = !isPassed && theme.started
                  const disabled =
                    !theme.started &&
                    !theme.passed &&
                    disabledThemes.find(t => t.theme_id === theme.theme_id)

                  return (
                    <li
                      key={theme.theme_id}
                      className={cx('themes__item', {
                        disabled: disabled,
                        active: theme.theme_id === currentTheme,
                      })}
                      onClick={() => {
                        if (!disabled) {
                          onThemeClick(theme, section.section_id)
                        }
                      }}
                    >
                      <span>
                        {sectionIndex}.{themeIndex}
                      </span>
                      <span className={cx('themes__item__title')}>{theme.title}</span>
                      <span className={cx('sections__item__title__status')}>
                        {isProgress && (
                          <IconWrapper className={cx('dot')} size='14' color='primary'>
                            <Dot />
                          </IconWrapper>
                        )}
                        {isPassed && (
                          <IconWrapper color='primary'>
                            <SuccessIcon />
                          </IconWrapper>
                        )}
                      </span>
                    </li>
                  )
                })}
              </ul>
            </li>
          )
        })}
      </ul>
    </div>
  )
}

export default Learning
