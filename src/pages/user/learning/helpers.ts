import { CourseStructure } from './types'
import { TTheme } from '@/entities/themeCourse/model/types'

export const pickThemeData = (
  structure: CourseStructure | undefined,
  themeId: string | undefined,
) => {
  const themes = structure?.sections.reduce((acc, item) => [...acc, ...item.themes], [] as TTheme[])
  return themes?.find(i => i.id === themeId)
}

export function isFullscreen() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const doc = document as any
  if (doc.fullscreenElement) return true

  if (typeof doc.webkitCurrentFullScreenElement !== 'undefined')
    return !!doc.webkitCurrentFullScreenElement

  if (typeof doc.webkitFullscreenElement !== 'undefined') return !!doc.webkitFullscreenElement

  return false
}
