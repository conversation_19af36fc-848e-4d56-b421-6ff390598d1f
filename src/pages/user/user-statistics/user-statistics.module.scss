// @use "placeholders";
// @use "media";
@use '../../../shared/assets/styles/mixins/text';

.title {
  @media (max-width: 768px) {
    font: var(--font-title-2-medium);
  }
}

.wrapper {
  display: flex;
  flex-direction: column;
  gap: 32px;

  @media (max-width: 1024px) {
    display: grid;
    grid-template-columns: 1;
  }
}

.row {
  display: flex;
  flex-shrink: 0;
  gap: 24px;

  @media (max-width: 992px) {
    flex-direction: column;
    gap: 12px;

    &.second {
      grid-row-start: 3;
      grid-row-end: 4;
    }

    &.third {
      grid-row-start: 2;
      grid-row-end: 3;
    }
  }
}

.passedStatWrapper {
  min-height: 176px;
  width: 100%;
}

.information {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  align-items: flex-start;

  grid-template-areas: 'general notice';

  .general {
    grid-column: 1 / 3;
  }

  .notice {
    height: min-content;
  }

  @media (max-width: 768px) {
    display: flex;
    flex-direction: column-reverse;


    .general {
      grid-column: 1;
      width: 100%;
      font: var(--font-text-2-normal);
    }
  
    .notice {
      width: 100%;
    }
  }
}

.passed_stat {
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.note {
  position: absolute;
  top: 16px;
  right: 24px;
}

.phishing {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .description {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    align-items: center;
  }

  .title {
    font: var(--font-text-1-medium);
    color: var(--color-gray-90);
  }

  .link {
    font: var(--font-text-2-medium);
    color: var(--color-primary);
  }
}

.statistics {
  .title {
    font: var(--font-text-1-medium);
    color: var(--color-gray-90);
    margin-bottom: 16px;
  }

  .widgets {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;

    .widgetInfo {
      flex-direction: column;
      gap: 8px;
    }

    .widgetHint {
      display: flex;
      gap: 6px;
      align-items: center;
      color: var(--color-gray-80);
      font-size: 12px;
      line-height: 14px;
    }

    .phishingStats {
      margin-top: auto;
    }

    @media (max-width: 1024px) {
      grid-template-columns: 2fr;
    }
  }
}

.block {
  @media (max-width: 1024px) {
    padding: 16px;
  }
}
