export const mockedEducationData = [
  {
    date: '2025-01-22',
    value: 100,
  },
  {
    date: '2025-01-21',
    value: 100,
  },
  {
    date: '2025-01-20',
    value: 100,
  },
  {
    date: '2025-01-19',
    value: 100,
  },
  {
    date: '2025-01-18',
    value: 100,
  },
  {
    date: '2025-01-17',
    value: 50,
  },
  {
    date: '2025-01-16',
    value: 50,
  },
  {
    date: '2025-01-15',
    value: 0,
  },
]

export const mocked_risk_level_chart = [
  {
    color: 'YELLOW',
    data: [
      {
        date: '2025-01-22',
        value: 9.199999809265137,
      },
      {
        date: '2025-01-21',
        value: 9.199999809265137,
      },
      {
        date: '2025-01-20',
        value: 9.199999809265137,
      },
      {
        date: '2025-01-19',
        value: 9.199999809265137,
      },
      {
        date: '2025-01-18',
        value: 9.199999809265137,
      },
      {
        date: '2025-01-17',
        value: 9.199999809265137,
      },
      {
        date: '2025-01-16',
        value: 9.199999809265137,
      },
      {
        date: '2025-01-15',
        value: 9.300000190734863,
      },
      {
        date: '2025-01-14',
        value: 9.899999618530273,
      },
      {
        date: '2025-01-13',
        value: 10,
      },
      {
        date: '2024-12-27',
        value: 10,
      },
      {
        date: '2024-12-23',
        value: 10,
      },
      {
        date: '2024-12-18',
        value: 1,
      },
      {
        date: '2024-12-17',
        value: 1,
      },
      {
        date: '2024-12-16',
        value: 1,
      },
      {
        date: '2024-12-13',
        value: 1,
      },
      {
        date: '2024-12-12',
        value: 1,
      },
      {
        date: '2024-12-11',
        value: 1,
      },
      {
        date: '2024-12-10',
        value: 1,
      },
      {
        date: '2024-12-05',
        value: 1,
      },
      {
        date: '2024-12-02',
        value: 1,
      },
      {
        date: '2024-11-28',
        value: 0,
      },
      {
        date: '2024-11-26',
        value: 0,
      },
      {
        date: '2024-11-25',
        value: 0,
      },
      {
        date: '2024-11-22',
        value: 0,
      },
      {
        date: '2024-11-21',
        value: 0,
      },
      {
        date: '2024-11-20',
        value: 0,
      },
      {
        date: '2024-11-19',
        value: 0,
      },
      {
        date: '2024-11-18',
        value: 0,
      },
      {
        date: '2024-11-15',
        value: 0,
      },
    ],
  },
]

export const mockedRiskStatistics = {
  starts_at: '2024-01-17',
  ends_at: '2026-01-17',
  license_type: 'full',
  users_count: 26,
  users_limit: 100,
  risk_level: 9.199999809265137,
  color: 'RED',
}

export const mockedProgress = {
  date: '2025-01-22',
  overall_progress_percent: 100,
  passed_theory_percent: 100,
  passed_tests_percent: 0,
  max: 100,
  color: null,
}

export const mockedPhishingChart = {
  sent: 2375,
  opened: 196,
  followed: 125,
  entered: 54,
  attachments: 42,
}
