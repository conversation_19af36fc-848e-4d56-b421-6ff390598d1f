import { useEffect, useMemo, useState } from 'react'
import { statisticApi } from '@/entities/statistic/model/endpoints'
import {
  calculateCompanyDateSummary,
  DateCompanySummaryHashItem,
} from '@/shared/helpers/statictics'
import { useTranslation } from 'react-i18next'
import { DEFAULT_TIME_RANGE } from '@/features/graphics/model/constants'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { DataPoint, RangeItem } from '@/features/graphics'
import { calculateDays } from '@/shared/helpers/date'

export const useMyStats = () => {
  const { t } = useTranslation('pages__user-statictics')
  const { data: info } = statisticApi.useGetStatisticUserInfoQuery()
  const [getEmployeeProgressStatistics] = statisticApi.useLazyGetUserProgressStatisticsChangeQuery()
  const { data: userPassedStat } = statisticApi.useGetMyPassedStatQuery()
  const { data: phishingStatistics, isLoading: isLoadingPhishingStatistics } =
    statisticApi.useGetUserPhishingStatisticsQuery()

  const [riskLevelTimeRangeItem, setRiskLevelTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )

  const { data: riskStatistics, isLoading: isLoadingRiskStatistics } =
    statisticApi.useGetUserRiskStatisticsQuery(
      riskLevelTimeRangeItem?.value
        ? String(calculateDays(riskLevelTimeRangeItem?.value))
        : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
    )

  const risk_level_chart = useMemo(
    () => [
      {
        color: 'YELLOW',
        data: riskStatistics?.risk_levels.map(i => ({
          ...i,
          color: 'YELLOW',
          value: i.value,
        })),
      },
    ],
    [riskStatistics],
  )

  const phishing_chart = useMemo(
    () => ({
      sent: phishingStatistics?.sent || undefined,
      opened: phishingStatistics?.opened || undefined,
      followed: phishingStatistics?.clicked || undefined,
      entered: phishingStatistics?.entered_data || undefined,
      attachments: phishingStatistics?.opened_attachment || undefined,
    }),
    [phishingStatistics],
  )

  const [educationData, setEducationData] = useState<DataPoint[]>([])

  const [educationTimeRangeItem, setEducationTimeRangeItem] = useState<RangeItem | undefined>(
    DEFAULT_TIME_RANGE[0],
  )

  useEffect(() => {
    const getDataFn = async () => {
      const { data: progressData } = await getEmployeeProgressStatistics(
        educationTimeRangeItem?.value
          ? String(calculateDays(educationTimeRangeItem?.value))
          : String(calculateDays(DEFAULT_TIME_RANGE[0]?.value)),
      )

      if (!progressData) {
        setEducationData([])
        return
      }

      const { learningData } = calculateCompanyDateSummary(
        progressData.progress as DateCompanySummaryHashItem[],
      )
      setEducationData(learningData)
    }
    getDataFn()
  }, [educationTimeRangeItem, getEmployeeProgressStatistics])

  const analytics = useAnalytics()

  const [openPhishingExcelReport, setOpenPhishingExcelReport] = useState(false)

  // TODO: change after new requests

  const onPhishingExcelClick = async () => {
    analytics.event('modules.statistics.report', {
      type: 'user_phishing',
      title: String(info?.first_name) + String(info?.last_name),
    })
    setOpenPhishingExcelReport(true)
  }

  return {
    phishing_chart,
    risk_level_chart,
    info,
    isLoadingPhishingStatistics,
    phishingStatistics,
    setRiskLevelTimeRangeItem,
    userPassedStat,
    isLoadingRiskStatistics,
    t,
    educationData,
    educationTimeRangeItem,
    setEducationTimeRangeItem,
    openPhishingExcelReport,
    onPhishingExcelClick,
    setOpenPhishingExcelReport,
  }
}
