export declare namespace MyStatsProps {
  interface Own {
    className?: string;

    active: boolean;
    setActive: React.Dispatch<React.SetStateAction<boolean>>;
    children: React.ReactNode;
  }

  type Props = Own;

  export interface DateSummaryHashItem {
    date: string;
    quiz: number;
    learning: number;
  }

  export interface DateSummaryHash {
    [date: string]: {
      count: number;
      quizSum: number;
      learningSum: number;
    };
  }
}

export {};
