import classNamesBind from 'classnames/bind'
import React, { useMemo } from 'react'
import { Education<PERSON>hartWidget, RiskLevelChartWidget } from '@/features/graphics'
import { Card } from '@/shared/ui'
import Skeleton from 'react-loading-skeleton'
import styles from './user-statistics.module.scss'
import { HelpIcon } from '@/shared/ui'

import { MyStatsProps } from './user-statistics.d'
import { useMyStats } from './use-user-statistics'
import { EmployeeInformation, getFullUserName } from '@/entities/employee'
import { Tags } from '@/shared/components/tags'
import { AssignedCoursesStats } from '@/shared/components/assigned-courses-stats/assigned-courses-stats'
import { PhishingStatistics } from '@/shared/components/phishing-statistics'

const cx = classNamesBind.bind(styles)

export const MyStats: React.FC<MyStatsProps.Props> = props => {
  const { className } = props

  const {
    t,
    info,
    risk_level_chart,
    phishing_chart,
    userPassedStat,
    educationData,
    educationTimeRangeItem,
    setEducationTimeRangeItem,
    setRiskLevelTimeRangeItem,
  } = useMyStats()

  const passedStat = useMemo(() => {
    if (!userPassedStat) return undefined

    return {
      courses: {
        passed: userPassedStat.passed_courses,
        total: userPassedStat.total_courses,
      },
      quizes: {
        passed: userPassedStat.passed_quizes,
        total: userPassedStat.total_quizes,
      },
    }
  }, [userPassedStat])

  return (
    <div className={cx('wrapper', className)}>
      <h1 className={cx('title')}>
        {info?.last_name} {info?.first_name} {info?.middle_name}
      </h1>
      <div className={cx('information')}>
        {info?.id ? (
          <EmployeeInformation
            wrapperClassName={cx('general')}
            attributes={[
              {
                name: t('fio'),
                content: getFullUserName({
                  firstName: info?.first_name ?? '',
                  lastName: info?.last_name ?? '',
                  middleName: info?.middle_name ?? '',
                })
                  ? getFullUserName({
                      firstName: info?.first_name ?? '',
                      lastName: info?.last_name ?? '',
                      middleName: info?.middle_name ?? '',
                    })
                  : '—',
              },
              { name: t('email'), content: `${info?.email}` },
              { name: t('department'), content: `${info?.department?.title}` },
              {
                name: t('position'),
                content: info?.position ? info?.position : '—',
              },
              { name: t('role'), content: info?.role ? t(`roles.${info?.role}`) : '—' },
              {
                name: t('tags'),
                content: <Tags tags={info?.tags ?? []} />,
              },
            ]}
          />
        ) : (
          <div className={cx('general')}>
            <Skeleton borderRadius={'16px'} height={'340px'} />
          </div>
        )}

        <Card className={cx('passedStatWrapper', 'notice')}>
          <AssignedCoursesStats className={cx('passed_stat')} stats={passedStat} />
        </Card>
      </div>
      <div className={cx('statistics')}>
        <div className={cx('widgets')}>
          <RiskLevelChartWidget
            isLoading={!risk_level_chart?.[0]?.data}
            chartProps={{
              data: risk_level_chart?.[0]?.data ?? [],
              type: 'redToGreen',
              customMax: 10,
              customMin: 0,
              dimension: '',
            }}
            descriptionProps={{
              risk: Number(info?.statistic.risk_level.toFixed(1)) ?? 0,
              showHelpIcon: false,
            }}
            onTimeRangeChange={v => setRiskLevelTimeRangeItem(v)}
            infoClassName={cx('widgetInfo')}
            bottomContent={
              <div className={cx('widgetHint')}>
                <HelpIcon text={t('hints.risk_description')} />
                {t('hints.risk')}
              </div>
            }
          />
          <EducationChartWidget
            isLoading={!educationData}
            chartProps={{
              data: educationData,
              customMin: 0,
            }}
            descriptionProps={{
              percent: educationData[0]?.value.toFixed(1) ?? 0,
            }}
            onTimeRangeChange={v => setEducationTimeRangeItem(v)}
            defaultItem={educationTimeRangeItem}
            infoClassName={cx('widgetInfo')}
          />
          <PhishingStatistics
            {...phishing_chart}
            statsClassName={cx('phishingStats')}
            onClick={() => {}}
            isHiddenDownloadBtn
          />
        </div>
      </div>
    </div>
  )
}

export default MyStats
