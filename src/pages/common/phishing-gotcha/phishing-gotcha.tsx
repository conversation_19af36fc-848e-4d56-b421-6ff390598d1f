import React, { useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './phishing-gotcha.module.scss'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export interface IPhishingGotchaProps {
  className?: string
}

export const PhishingGotcha: React.FC<IPhishingGotchaProps> = () => {
  useState()
  const { t } = useTranslation()

  return (
    <div className={cx('wrapper')}>
      <div className={cx('content')}>
        {/* eslint-disable-next-line i18next/no-literal-string */}
        <div className={cx('text', 'title')}>Ohh!</div>
        <div className={cx('text', 'subtitle')}>
          {t('commons:you_fallen_phishing')}
          <br />
          {t('commons:be_more_careful')}
        </div>
        <div className={cx('text')} style={{ marginTop: '10px' }}>
          {t('commons:this_learning_phishing')}
        </div>
      </div>
    </div>
  )
}
