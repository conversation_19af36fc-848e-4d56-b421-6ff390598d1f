@use "../../../shared/assets/styles/mixins/text";

.wrapper {
  align-items: center;
  display: flex;
  justify-content: center;
  margin: auto;
  width: 100%;
  height: 100%;
  min-height: 100dvh;
}

.text {
  @include text.title(18px);
  color: var(--gark-gray);
  text-align: center;
}

.logo-container {
  position: absolute;
  top: 50px;
}

.content {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 85px;
  max-width: 500px;
  width: 100%;
  min-height: 300px;
  width: 100%;
}

.content::after {
  color: var(--silver);

  content: "Ohh!";

  display: flex;
  font-size: 420px;
  font-stretch: normal;
  font-style: normal;
  font-weight: 800;
  justify-content: center;
  letter-spacing: -23.73px;
  line-height: 1.33;
  max-width: 100vw;

  opacity: 0.6;
  overflow-x: hidden;
  position: absolute;
  text-align: center;
  user-select: none;
  z-index: -1;
}

.title {
  font-size: 36px;
  line-height: 48px;
}

.subtitle {
  font-size: 24px;
  line-height: 30px;
}

.button {
  background-color: var(--color-primary);
  border-radius: 10px;

  color: var(--white);
  font-size: 12px;
  margin-top: 40px;
  max-width: 100%;
  padding: 20px 60px;
  text-decoration: none;
  text-transform: uppercase;
}

.attention {
  font-size: 12px;
  margin-top: 20px;
}

.courseAssignment {
  font-size: 24px;
}

@media screen and (max-width: 500px) {
  .title {
    font-size: 25px;
    line-height: 40px;
  }

  .subtitle {
    font-size: 18px;
    line-height: 24px;
  }

  .button {
    font-size: 10px;
  }

  .attention {
    font-size: 10px;
  }

  .content::after {
    font-size: 320px;
  }
}
