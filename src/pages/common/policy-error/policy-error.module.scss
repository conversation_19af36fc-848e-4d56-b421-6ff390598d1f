.wrapper {
  position: fixed;
  width: 100vw;
  min-height: 100dvh;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(142, 151, 175, 0.45);
}

.inner {
  background: #ffffff;
  border: 1px solid #ebeff2;
  border-radius: 16px;
  padding: 20px 24px;
  max-width: 340px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.text {
  font: var(--font-text-1-normal);
  color: var(--color-gray-90);
  a {
    color: var(--color-statistics-good);
  }
}

.processing-data {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

.label {
  display: flex;
  flex-direction: row-reverse;
  gap: 12px;
  align-items: center;
}

.button {
  font-family: "TT Norms Pro";
  font-style: normal;
  font-weight: 600;
  font-size: 14px;
  line-height: 18px;
  color: #ffffff;
  padding: 11px 20px;
  border-radius: 12px;
  align-self: start;
  width: 100%;
}
