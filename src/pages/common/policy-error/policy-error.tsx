import React, { useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './policy-error.module.scss'
import { Button } from '@/shared/ui'
import { Checkbox } from '@/shared/ui/checkbox'
import { useConfig } from '@/shared/hooks'
import { URLS } from '@/shared/configs/urls'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export interface IPolicyErrorProps {
  className?: string
  handleClick: (isAgree: boolean) => void
  onWrapperClick?: (e?: React.MouseEvent<HTMLDivElement, MouseEvent>) => void
}

export const PolicyError: React.FC<IPolicyErrorProps> = props => {
  const config = useConfig()
  const { t } = useTranslation()

  const { handleClick, onWrapperClick } = props

  const [isAgree, setIsAgree] = useState(false)

  const handleChange = (value: boolean) => {
    setIsAgree(value)
  }

  return (
    <div className={cx('wrapper')} onClick={onWrapperClick}>
      <div className={cx('inner')}>
        <div className={cx('text')}>
          {t('commons:accept_to_continue')}{' '}
          <a
            href={
              config.needPrivacyPolicyPage ? URLS.PRIVACY_POLICY : 'https://secure-t.ru/privacy'
            }
            target='_blank'
            rel='noreferrer'
          >
            {t('commons:privacy_policy')}
          </a>
        </div>
        <label className={cx('label')}>
          <Checkbox
            onChange={handleChange}
            label={<span>{t('commons:accept_privacy_policy')}</span>}
          />
        </label>
        <Button className={cx('button')} onClick={() => handleClick(isAgree)} disabled={!isAgree}>
          {t('commons:continue')}
        </Button>
      </div>
    </div>
  )
}
