.wrapper {
  max-width: 980px !important;
  padding: 0 20px;
  // для того, чтобы был отступ после конца белой карточки
  height: auto !important;
}

.wrapperInner {
  height: calc(100dvh - 20px);
  overflow: auto;
  padding-bottom: 20px;
}

.inner {
  max-width: 100% !important;
  padding: 24px 40px !important;
}

.text-wrapper {
  .container {
    background: #fff;
    border-radius: 32px;
    max-width: 980px;
    padding: 50px;
    @media screen and (max-width: 768px) {
      background: transparent;
      padding: 0 16px;
    }
  }
}

.title {
  color: #323037;
  font-size: 64px;
  letter-spacing: -0.03em;
  line-height: 70px;
  margin-bottom: 40px;
  text-align: center;

  font: var(--font-title-1-medium);

  @media screen and (max-width: 992px) {
    font-size: calc(28px + 36 * ((100vw - 480px) / (992 - 480)));
    line-height: calc(30px + 40 * ((100vw - 480px) / (992 - 480)));
  }
  @media screen and (max-width: 480px) {
    font-size: 28px;
    line-height: 30px;
  }
}

.blocks {
  b {
    font-weight: 700;
  }

  .block {
    margin-bottom: 20px;
  }

  .text {
    color: #3d3935;
    font-size: 15px;
    margin-bottom: 20px;

    font: var(--font-text-2-normal);

    a {
      color: #39d292;
    }
  }

  .rules {
    margin-left: 30px;

    @media (max-width: 600px) {
      margin-left: 15px;
    }
  }

  .rule {
    color: #3d3935;
    font-size: 15px;
    margin-bottom: 20px;

    span {
      font-weight: 600;
    }
  }

  .blockTitle {
    color: #3d3935;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 20px;
  }
}
