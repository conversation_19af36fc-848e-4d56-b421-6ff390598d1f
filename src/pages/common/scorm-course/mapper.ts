/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unused-vars */
export const getQuizInfo = (record: Record<string, Record<string, any>>) =>
  Object.values(record).reduce((acc, item) => {
    if ('quizInfo' in item) {
      acc.push(item.quizInfo)
    }
    return acc
  }, [])

export const getSlideInfo = (record: Record<string, Record<string, any>>) =>
  Object.values(record).reduce((acc, item) => {
    if ('quizInfo' in item) {
      acc.push({
        visited: item.visited,
        completed: item.quizInfo.state === 'completed',
      })
    } else {
      acc.push({ visited: item.visited, completed: true })
    }
    return acc
  }, [])
