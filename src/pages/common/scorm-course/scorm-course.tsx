/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useEffect, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './scorm-course.module.scss'
import Skeleton from 'react-loading-skeleton'
import { useParams } from 'react-router-dom'
import { userAPI } from 'entities/employee'
import {
  useGetMyScormCourseQuery,
  useLazyGetMyScormCourseStatementQuery,
  usePostMyScormCourseStatementMutation,
} from '@/store/services/user-courses-service'
import { getQuizInfo, getSlideInfo } from './mapper'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

const TIMEOUT = 100

/**
 * @deprecated Этот компонент устарел и будет удален в будущем
 */
export const ScormCourse = () => {
  const { course_id = '' } = useParams()
  const { t } = useTranslation()

  const [statement, setStatement] = useState<Record<string, any> | null>({})

  const { data: user } = userAPI.useGetUserInfoQuery()
  const { data: course, isLoading: isCourseLoading } = useGetMyScormCourseQuery(course_id)
  const [getMyScormCourseStatement, { isLoading: isStatementLoading }] =
    useLazyGetMyScormCourseStatementQuery()
  const [isDebounceSet, setIsDebounceSet] = useState(false)

  const getState = useCallback(async () => {
    if (!course_id) return
    if (!user?.id) return

    try {
      const state =
        (await getMyScormCourseStatement({
          courseId: course_id,
          userId: user?.id,
        }).unwrap()) || {}

      setStatement(state)
      const { courseID, courseState, ...s } = state?.statement ?? {}
      if (!courseID) {
        return
      }
      localStorage.setItem(courseID, JSON.stringify(s))
    } catch (e) {
      if ((e as any).status === 404) {
        setStatement({})
      }
    }
  }, [course_id, user, getMyScormCourseStatement])

  useEffect(() => {
    if (!user?.id) return

    Promise.all([getState()])
  }, [getState, user])

  const [updateMyScormCourseStatement] = usePostMyScormCourseStatementMutation()

  const storageChange = useCallback(
    (event: StorageEvent) => {
      if (!/^ispring::\{/.test(event.key as string)) return

      if (!event.newValue) return

      let value

      try {
        value = JSON.parse(event.newValue)
      } catch (error) {
        console.error(t('commons:error_parsing_assign_course'))
      }

      const state = {
        courseID: event.key,
        ...value,
        courseState: {
          quizInfo: getQuizInfo(value.slideStates),
          slideInfo: getSlideInfo(value.slideStates),
          viewDuration: value.viewDuration as number,
          lastViewedSlide: value.lastViewedSlide as number,
          timestamp: Date.now(),
        },
      }

      if (isDebounceSet) return

      setIsDebounceSet(true)

      setTimeout(() => {
        setStatement(state)
        updateMyScormCourseStatement({
          statement: state,
          userId: user!.id,
          courseId: course_id,
        })
          .unwrap()
          .finally(() => setIsDebounceSet(false))
      }, TIMEOUT)
    },
    [isDebounceSet, t, updateMyScormCourseStatement, user, course_id],
  )

  useEffect(() => {
    if (!user) return

    window.addEventListener('storage', storageChange)
    return () => {
      window.removeEventListener('storage', storageChange)
      Object.keys({ ...localStorage }).forEach(item => {
        if (/^ispring::\{/.test(item as string)) {
          localStorage.removeItem(item)
        }
      })
    }
  }, [user, storageChange])

  if (isCourseLoading || isStatementLoading) {
    return <div>{t('commons:download_course')}</div>
  }

  return (
    <div className={cx('wrapper', 'wrapper--scorm')}>
      {course && (
        <div className={cx('title')}>
          {course?.assigned_course.title || <Skeleton width='40%' />}
        </div>
      )}
      {course && statement && (
        <>
          {course.statistics.value === 100 ? (
            <p>{t('commons:congratulations_completed_course')}</p>
          ) : (
            <iframe
              allowFullScreen
              width='100%'
              height='100%'
              title={t('commons:course')}
              // !TODO: не отображается потому что сейчас ссылка неправильная подставляется
              // src={`https://edu.sec-t.ru/media/scorm/temp_yGRMRw/res/index.html`}
              src={`${course.assigned_course.file_link}`}
              sandbox='allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-downloads'
            />
          )}
        </>
      )}
    </div>
  )
}

export default ScormCourse
