import React, { useEffect, useState } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './privacy-policy.module.scss'
import { AuthLayout } from '@/shared/components'
import parse from 'html-react-parser'

import { useConfig } from '@/shared/hooks'
import { Navigate, useNavigate } from 'react-router-dom'
import { URLS } from '@/shared/configs/urls'
import { getCopy } from '@/shared/helpers/'

const cx = classNamesBind.bind(styles)

export interface IPrivacyPolicyProps {
  className?: string
}

interface IRule {
  text?: string
  rules?: IRule[]
}

interface IBlock {
  title?: string
  text?: string
  rules?: IRule[]
}

interface IData {
  title: string
  blocks?: IBlock[]
}

const prepareData = (test: string) => {
  return test
    .replace(/{{ privacy-page }}/g, location.origin + URLS.PRIVACY_POLICY)
    .replace(/{{ domain }}/g, location.origin)
}

const Rules = ({ rules }: { rules: IRule[] }) => {
  if (!rules?.length) return null

  return <div className={cx('rules')}>
    {rules && rules.map(({ text, rules }) => {
      return <>
        {text && <div className={cx('rule', 'text')} key={text}>
          {parse(prepareData(text))}
        </div>}
        {rules && <Rules rules={rules} />}
      </>
    })}
  </div>
}

export const PrivacyPolicy: React.FC<IPrivacyPolicyProps> = props => {
  const { className } = props

  const config = useConfig()
  const navigate = useNavigate()

  const [data, setData] = useState<Nullable<IData>>(null)

  useEffect(() => {
    fetch('/text.json')
      .then(response => response.json())
      .then(data => {
        if (data) setData(data)
        else navigate(URLS.LOGIN_PAGE)
      })
      .catch(() => navigate(URLS.LOGIN_PAGE))
  }, [navigate])

  if (!config.needPrivacyPolicyPage) {
    return <Navigate to={URLS.LOGIN_PAGE} />
  }

  if (!data) return null

  const { title } = getCopy(data)

  return (
    <AuthLayout
      classNameInner={cx('wrapper')}
      classNameWrapper={cx('wrapperInner')}
      className={cx(className, 'inner')}
      needLangSwitcher={false}
    >
      <div className={cx('text-wrapper')}>
        <div className={cx('title')}>{title}</div>
        {data.blocks && (
          <div className={cx('blocks')}>
            {data.blocks.map(block => {
              const { title, text, rules } = block

              return (
                <div className={cx('block')} key={title}>
                  {title && (
                    <div className={cx('blockTitle', 'text')}>{parse(prepareData(title))}</div>
                  )}
                  {text && (
                    <div className={cx('blockText', 'text')}>{parse(prepareData(text))}</div>
                  )}
                  {rules && (<div className={cx('rules')}>
                    <Rules rules={rules} />
                  </div>)}
                </div>
              )
            })}
          </div>
        )}
      </div>
    </AuthLayout>
  )
}

