import { HelpIcon, Switch } from '@/shared/ui'
import { Suspense, useCallback, useState } from 'react'
import { useNotification } from '@/shared/contexts/notifications'
import { Modal, TwoFaAuth, LazyQrCode } from '@/shared/components'
import styles from './profile.module.scss'
import classNamesBind from 'classnames/bind'
import { TwoFATestingRequest } from '@/shared/types/store/twofa'
import { useTranslation } from 'react-i18next'
import { authAPI } from 'entities/auth'
import { userAPI } from 'entities/employee'

const { useTwofaCodeTestingMutation, useTwofaDeactivateMutation } = authAPI

const cx = classNamesBind.bind(styles)

const E2EAuthMsg = () => {
  const { t } = useTranslation()

  return (
    <span className={cx('underline')}>
      {t('commons:functional_does_not_work_with_end_to_end_auth')}
    </span>
  )
}
const HINT = () => {
  const { t } = useTranslation()

  return (
    <span>
      {t('commons:e2e_hint')} <br />
      {E2EAuthMsg()}
    </span>
  )
}

export const ProfileTwoFaSwitch = () => {
  const { data: userInfo, isLoading: isUserLoading } = userAPI.useGetUserInfoQuery()
  const { handleResponse } = useNotification()
  const [isTwoFAActive, setIsTwoFAActive] = useState(userInfo?.two_fa)
  const [deactivateTrigger, { isError: twofaDeactivateError }] = useTwofaDeactivateMutation()
  const [testingTrigger, { isError: twofaError, isLoading: isTwofaTestingLoading }] =
    useTwofaCodeTestingMutation()

  const { t } = useTranslation()

  const onCallTwoFa = useCallback(
    (v: boolean, values: TwoFATestingRequest) => {
      if (v) {
        testingTrigger({ ...values, notificationTip: t('commons:you_enabled_tf_authentication') })
          .unwrap()
          .then(() => {
            setIsTwoFAActive(true)
            setIsQrCodeModalOpen(false)
          })
          .catch(() => {})

        return
      }

      deactivateTrigger({ code: values.code })
        .unwrap()
        .then(() => {
          setIsTwoFAActive(false)
          setIsQrCodeModalOpen(false)
        })
        .catch(() => {})
    },
    [testingTrigger, deactivateTrigger, handleResponse],
  )

  const [isQrCodeModalOpen, setIsQrCodeModalOpen] = useState(false)

  return (
    <div className={cx('twofa__switch__wrapper')}>
      <Switch
        onChange={v => {
          if (v !== undefined) {
            setIsQrCodeModalOpen(v)
          }
        }}
        className={cx('twofa__switch')}
        customValue={isTwoFAActive}
        disabled={isUserLoading}
        text={t('commons:enable_tf_authentication')}
      />
      <HelpIcon
        className={cx('twofa__switch__info')}
        tooltipClassname={cx('twoFaTooltip')}
        text={E2EAuthMsg()}
      />
      <Modal
        className={cx(styles.modal)}
        active={isQrCodeModalOpen}
        setActive={setIsQrCodeModalOpen}
        key={'qrcode-modal'}
      >
        <h3>{t('commons:two_factor_authentication')}</h3>
        <Suspense>
          <LazyQrCode />
        </Suspense>
        <div className={cx('hint')}>
          <p>{t('commons:find_more')}:</p>
          <HelpIcon text={HINT()} tooltipClassname={cx('twoFaTooltip')} />
        </div>
        <TwoFaAuth
          isError={twofaError || twofaDeactivateError}
          inputProps={{ disabled: isTwofaTestingLoading }}
          className={cx(styles.twofa__input)}
          animationDuration={8000}
          codeLength={6}
          onSuccess={v => {
            if (userInfo) onCallTwoFa(!isTwoFAActive, { code: v, email: userInfo?.email })
          }}
        />
      </Modal>
    </div>
  )
}
