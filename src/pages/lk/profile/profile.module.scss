.wrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.modal {
  padding: 30px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
}

.hint {
  display: flex;
  flex-direction: row;
  gap: 12px;
  color: var(--color-gray-70);
  font: var(--font-text-2-normal);
  margin-top: -12px;
  margin-bottom: -12px;
  margin-left: auto;
}

.underline {
  text-decoration: underline;
}

.twofa {
  &__wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  display: flex;
  flex-direction: column;
  text-align: center;

  &__input {
    font: var(--font-title-4-medium);
    text-align: center;
    padding: 12px;
  }

  &__qr {
    margin-bottom: 40px;
  }
  &__switch {
    width: fit-content;
    cursor: pointer;
    &__wrapper {
      display: flex;
      justify-content: space-between;
      max-width: 446px;
    }

    &__info {
      margin-right: 14px;
      * {
        stroke: var(--color-primary-80);
      }
    }
  }
}

.tooltip {
  width: 110px;
}
.qr-code {
  cursor: pointer;
}

.picture {
  background: #fff;
  border: 1px solid #ebeff2;
  border-radius: 12px;
  cursor: pointer;
  display: block;
  max-width: max-content;
  padding: 12px;

  &.disabled {
    opacity: 0.7;

    pointer-events: none;
  }

  input {
    left: -999vw;

    opacity: 0;

    pointer-events: none;
    position: absolute;
    z-index: -1;
  }
  img {
    border-radius: 50%;
    height: 100px;
    object-fit: cover;
    object-position: center;
    width: 100px;
  }

  &__wrapper {
    display: flex;
    align-items: flex-end;
  }
}

.name-wrapper {
  align-items: start;
  display: flex;
  justify-content: space-between;

  .name {
    color: var(--color-gray-90, #343b54);
    font: var(--font-title-2-medium);
  }
}

.statistic {
  align-items: center;
  display: flex;

  margin-bottom: 4px;

  &:last-child {
    margin-bottom: 0;
  }

  .title {
    color: var(--color-gray-80, #5c6585);
    font: var(--font-text-1-normal);

    margin-right: 8px;
  }
  .value {
    font: var(--font-text-1-normal);

    &.color {
      &--GREEN {
        color: var(--color-statistics-good, #3dbc87);
      }
      &--YELLOW {
        color: var(--color-statistics-warning, #ffc700);
      }
      &--RED {
        color: var(--color-statistics-bad, #ff8577);
      }
    }
  }
}

.change-password {
  margin-top: -12px;
  max-width: 446px;
  width: 100%;
}

.block {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 12px;

  svg {
    transition: 0.3s;
  }
  &:hover {
    span {
      text-decoration: underline;
    }
    svg {
      transform: translateX(5px);
    }
  }
}

.top__wrapper {
  display: flex;
  flex-direction: row;
  gap: 16px;

  &__actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-self: flex-end;

    span {
      font: var(--font-text-2-normal);
    }
  }
}

.twoFaTooltip {
  @media (max-width: 1024px) {
    transform: translateX(-110%) translateY(-50%);
    opacity: 1;
  }
}
