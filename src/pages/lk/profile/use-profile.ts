import { ChangeEvent, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

import { logOut } from '@/store/slices/auth/auth-slice'
import { useAppDispatch, useAppSelector } from '@/store'
import { globalBaseApi } from '@/store/services/endpoints/base'
import { URLS } from '@/shared/configs/urls'
import { authAPI } from 'entities/auth'
import { userAPI } from 'entities/employee'
import { coursesAPI } from '@/store/services/course-service'
import { myCoursesAPI } from '@/store/services/user-courses-service'
import { assignedCourseAPI } from '@/store/services/assigned-course-service'
import { departmentsTreeApi } from '@/store/services/endpoints/departments/api'
import { moduleAPI } from '@/store/services/module-service'
import { tagsEmployeesApi } from '@/store/services/tags-employees-service'
import { assignedCoursesAPI } from '@/store/services/assigned-courses-service'

export const useProfile = () => {
  const {
    data: userInfo,
    isLoading: isUserInfoLoading,
    isError: isUserInfoError,
  } = useAppSelector(userAPI.endpoints.getUserInfo.select())
  const [updateProfilePicture, { isLoading: isUpdateLoading }] =
    userAPI.useUploadProfilePictureMutation()
  const [openModal, setOpenModal] = useState(false)
  const dispatch = useAppDispatch()
  const navigate = useNavigate()

  const [triggerLogOut] = authAPI.useLogOutMutation()
  const { pathname } = useLocation()
  const isAdmin = pathname.startsWith('/lk/admin')

  const onLogoutClick = async () => {
    await triggerLogOut({ domain_url: window.location.origin })
    dispatch(logOut())

    localStorage.removeItem('common_info_v2')

    navigate(URLS.LOGIN_PAGE, { replace: true })
    dispatch(globalBaseApi.util.resetApiState())
    dispatch(userAPI.util.resetApiState())
    dispatch(coursesAPI.util.resetApiState())
    dispatch(myCoursesAPI.util.resetApiState())
    dispatch(assignedCourseAPI.util.resetApiState())
    dispatch(departmentsTreeApi.util.resetApiState())
    dispatch(moduleAPI.util.resetApiState())
    dispatch(tagsEmployeesApi.util.resetApiState())
    dispatch(assignedCoursesAPI.util.resetApiState())
  }
  const onPasswordChangeClick = () => setOpenModal(true)

  const onChangePicture = async (e: ChangeEvent<HTMLInputElement>) => {
    e.preventDefault()

    if (!e?.target?.files) return

    const newPicture = e?.target?.files[0]

    await updateProfilePicture(newPicture).unwrap()
  }

  return {
    userInfo,
    isUserInfoLoading,
    isUserInfoError,
    openModal,
    setOpenModal,
    onLogoutClick,
    onPasswordChangeClick,
    onChangePicture,
    isUpdateLoading,
    isAdmin,
  }
}
