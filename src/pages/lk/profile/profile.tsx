import { FC } from 'react'

import classNamesBind from 'classnames/bind'
import styles from './profile.module.scss'
import { ProfileProps } from './profile.d'
import { <PERSON><PERSON>, ButtonCard, Loader } from '@/shared/ui'
import ArrowIcon from '@/shared/ui/Icon/icons/components/ArrowIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { ProfileInfo } from '@/shared/components'
import { PasswordChangeModal } from '@/shared/modals/password-change-modal'
import { useProfile } from './use-profile'
import { getRickLevelColor, getFullName } from '@/shared/helpers'
import { ProfileTwoFaSwitch } from './profile-twofa-switch'
import { Link } from 'react-router-dom'
import { URLS } from '@/shared/configs/urls'
import { useTranslation } from 'react-i18next'

const cx = classNamesBind.bind(styles)

export const Profile: FC<ProfileProps.Props> = props => {
  const { className } = props
  const { t } = useTranslation()

  const {
    userInfo,
    isUserInfoLoading,
    isUserInfoError,
    openModal,
    setOpenModal,
    onLogoutClick,
    onPasswordChangeClick,
    onChangePicture,
    isUpdateLoading,
    isAdmin,
  } = useProfile()

  const { first_name, last_name, middle_name, email, statistic, picture } = userInfo || {}
  const { risk_level } = statistic || {}

  const fullname = getFullName({ first_name, last_name, middle_name, email })
  return (
    <div className={cx(className, 'wrapper')}>
      {isUserInfoLoading && <Loader size='56' className='loader_centered' />}
      {!isUserInfoLoading && (isUserInfoError || !userInfo) && (
        <div className={cx('error-text')}>{t('commons:error_occurred')} :(</div>
      )}
      {!isUserInfoLoading && !isUserInfoError && userInfo && (
        <>
          <div className={cx('name-wrapper')}>
            <h1 className={cx('name')}>{fullname}</h1>
            <Button color='red' onClick={onLogoutClick}>
              {t('commons:exit')}
            </Button>
          </div>
          <div className={cx('statistics')}>
            {typeof risk_level === 'number' && (
              <div className={cx('statistic')}>
                <div className={cx('title')}>{t('commons:risk_level')}</div>
                <div className={cx('value', `color--${getRickLevelColor(risk_level)}`)}>
                  {risk_level.toFixed(1)}
                </div>
              </div>
            )}
          </div>
          <div className={cx('top__wrapper')}>
            <div className={cx('picture__wrapper')}>
              <label
                className={cx('picture', {
                  disabled: isUpdateLoading,
                })}
              >
                <img src={picture || ''} alt='' />
                <input
                  type='file'
                  accept='image/*,.png,.jpg,.jpeg'
                  multiple={false}
                  onChange={e => onChangePicture(e)}
                />
              </label>
            </div>
            {isAdmin && (
              <div className={cx('top__wrapper__actions')}>
                <Link to={URLS.ADMIN_CHANGE_LOG_PAGE} className={cx('block')}>
                  <IconWrapper color='primary'>
                    <ArrowIcon />
                  </IconWrapper>
                  <span>{t('commons:change_log')}</span>
                </Link>
                <Link to={URLS.ADMIN_INSTRUCTIONS_PAGE} className={cx('block')}>
                  <IconWrapper color='primary'>
                    <ArrowIcon />
                  </IconWrapper>
                  <span>{t('commons:platform_instruction')}</span>
                </Link>
              </div>
            )}
          </div>
          <ProfileInfo info={userInfo} />
          <ButtonCard className={cx('change-password')} onClick={onPasswordChangeClick} icon='lock'>
            {t('commons:change_password')}
          </ButtonCard>
          {openModal && <PasswordChangeModal active={openModal} setActive={setOpenModal} />}
          <ProfileTwoFaSwitch />
        </>
      )}
    </div>
  )
}

export default Profile
