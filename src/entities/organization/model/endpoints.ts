import { BACKEND_URLS } from '@/shared/constants/urls'
import {
  ILicense,
  IOrganizationCreate,
  ICreateOrganizationResponse,
  IEditOrganizationResponse,
  IOrganizationEdit,
  IOrganizationWithChildren,
  IEmployeesGroupResponse,
  IEmployeesGroupRequest,
  IInviteReminderConfig,
} from './types'
import { globalBaseApi } from 'store/services/endpoints/base'

const PREFIX_URL_LK = '/lk/api/v2'

export const organizationAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getLicense: build.query<ILicense, void>({
      query: () => ({
        url: `${PREFIX_URL_LK}/organizations/current`,
      }),
    }),
    getOrganization: build.query<IOrganizationWithChildren, UUID>({
      query: id => ({
        url: `${PREFIX_URL_LK}/organizations/${id}`,
      }),
      providesTags: ['organization'],
    }),
    getOrganizationTitle: build.query<{ title: string }, void>({
      query: () => ({
        url: `${PREFIX_URL_LK}/organizations/my-title`,
      }),
      providesTags: ['organization'],
    }),
    deleteOrganization: build.mutation<ResponseWithNotification<void>, UUID>({
      query: id => ({
        url: `${PREFIX_URL_LK}/organizations/children/${id}`,
        method: 'DELETE',
      }),
      transformResponse: () => ({
        notificationTip: 'tips:organizations.delete',
      }),
    }),
    createOrganization: build.mutation<
      ResponseWithNotification<ICreateOrganizationResponse>,
      IOrganizationCreate
    >({
      query: body => ({
        url: `${PREFIX_URL_LK}/organizations/children`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['organization'],
      transformResponse: (response: ICreateOrganizationResponse) => ({
        ...response,
        notificationTip: 'tips:organizations.create',
      }),
    }),
    editOrganization: build.mutation<
      ResponseWithNotification<IEditOrganizationResponse>,
      IOrganizationEdit
    >({
      query: ({ id, ...body }) => ({
        url: `${PREFIX_URL_LK}/organizations/children/${id}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['organization'],
      transformResponse: (response: IEditOrganizationResponse) => ({
        ...response,
        notificationTip: 'tips:organizations.update',
      }),
    }),
    //
    organizationTwoFa: build.mutation<
      ResponseWithNotification<void>,
      { active: boolean; code: string }
    >({
      query: params => ({
        url: `${PREFIX_URL_LK}/organizations/twofa/processing`,
        method: 'PATCH',
        params: params,
      }),
      invalidatesTags: [],
      transformResponse: (_, __, { active }) => ({
        notificationTip: `tips:auth.twofa.${active ? 'on' : 'off'}`,
      }),
    }),
    notifyEmployees: build.mutation<
      ResponseWithNotification<void>,
      { text: string; employees: UUID[] }
    >({
      query: body => ({
        url: `${PREFIX_URL_LK}/emails/notify/employees`,
        method: 'POST',
        body,
      }),
      transformResponse: () => ({
        notificationTip: 'tips:employess.note.notification',
      }),
    }),
    inviteReminderConfig: build.mutation<ResponseWithNotification<void>, IInviteReminderConfig>({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/tags/default/organizations/invite-reminder/config`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['invite-reminder-config'],
    }),
  }),
  overrideExisting: true,
})

const PREFIX_URL = '/gateway/v1/statistic/'

export const organizationGatewayAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getEmployeesGroup: build.query<IEmployeesGroupResponse, IEmployeesGroupRequest>({
      query: ({ organization_id, ...rest }) => ({
        url: `${PREFIX_URL}organizations/${organization_id}/detail-progress-group`,
        params: { ...rest },
      }),
    }),
  }),
})
