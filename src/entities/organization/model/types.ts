import { ELicenseType } from 'shared/types/enums'
import { IGroupNewData, TGroup } from 'shared/types/store/groups'

export interface IOrganizationCreate {
  title: string
  employees_limit: number
  parent_id: UUID
  operator_email: string
}

export type IOrganizationEdit = Omit<IOrganizationCreate, 'operator_email'> & {
  id: UUID
}

export interface ICreateOrganizationResponse {
  id: UUID
  title: string
  license_type: ELicenseType
}

export type IEditOrganizationResponse = ICreateOrganizationResponse

export type TOrganizationWithoutChildrenAndParent = Omit<
  IOrganizationWithChildren,
  'children' | 'parent'
>

export interface IEmployeesGroupRequest extends RequestWithPagination {
  group_name: TGroup
  organization_id?: UUID
}

export interface IEmployeesGroupResponse extends ResponseWithPagination {
  data: IGroupNewData[]
  total_count: number
}

export interface IOrganizationWithChildren {
  id: UUID
  old_id: number
  title: string
  is_current: true
  need_twofa: boolean
  license: {
    title: string
    license_type: string
    users_count: number
    users_limit: number
    end_date: string
    start_date: string
  }
  parent?: TOrganizationWithoutChildrenAndParent
  children: TOrganizationWithoutChildrenAndParent[]
}

export interface ILicense {
  title: string
  license_type: string
  users_count: number
  users_limit: number
  start_date: string
  end_date: string
}

export interface IInviteReminderConfig {
  enable: boolean
  day_interval: number
  /**
   * Time ISO string
   * @example "00:00:00.000Z"
   */
  time: string
}
