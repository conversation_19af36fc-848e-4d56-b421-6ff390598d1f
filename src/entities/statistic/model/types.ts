import { ECourseStatus, ELicenseType, ERiskLevelColor, ERole, ETagType } from 'shared/types/enums'

export interface IRiskStatistics {
  starts_at: string
  ends_at: string
  license_type: ELicenseType
  users_count: number
  users_limit: number
  risk_level: number
  color: ERiskLevelColor
}

export interface IPhishingStatistics {
  value: number
  color: ERiskLevelColor
  opened_letters: number
  link_clicks: number
  entered_data: number
  opened_attachments: number
  opened_attachment?: number
  sent: number
  opened?: number
  clicked?: number
}

export interface IProgress {
  date: string
  theory: number
  quiz: number
  overall: number
}

export interface IScormProgress {
  date: string
  overall: number
  quiz: number
  learning: number
}

export interface IScormProgressWithPoints {
  points: IScormProgress[]
}

export type TProgressChange = {
  progress: IProgress[]
}

export interface IProgressChangeItem {
  date: string
  overall_progress_percent: number
  passed_theory_percent: number
  passed_tests_percent: number
  max: number
  color: ERiskLevelColor
}

export type TRiskLevelChange = {
  risk_levels: IRiskLevelChangeItem[]
}

export interface IUserPassedStat {
  passed_courses: number
  total_courses: number
  passed_quizes: number
  total_quizes: number
}

export type TRiskGroups = {
  date: string
  values: {
    low: number
    middle: number
    high: number
  }
  employees_count: number
}

export interface IRiskLevelChangeItem {
  date: string
  value: number
}

export interface IGroups {
  behind?: number
  normal?: number
}

interface ILicense {
  title: string
  license_type: string
  users_count: number
  users_limit: number
  end_date: string
}

export interface IActiveCourses {
  active: number
  completed: number
}

export interface IOrganizationInfo {
  id: UUID
  old_id: number
  title: string
  is_current: boolean
  license: ILicense
}

export interface IOrganization {
  id: UUID
  old_id: number
  title: string
  is_current: boolean
  license: ILicense
  parent?: IOrganizationInfo
  children?: IOrganizationInfo[]
}

export type TActiveScormCourses = IActiveCourses

export interface IGetDepartmentsTableRequest extends RequestWithPagination {
  organization_id?: UUID
}

export interface IGetDepartmentsTableResponse extends ResponseWithPagination {
  data: TDepartment[]
}

export interface IEmployee {
  id: UUID
  full_name: string
  email: string
  progress_statistic: { value: number }
  risk_level_statistic: {
    value: number
    color: ERiskLevelColor
    change: number
  }
  phishing_statistic: {
    color: ERiskLevelColor
    groups: {
      sent: { value: number }
      opened: { value: number }
      clicked: { value: number }
      entered_data: { value: number }
    }
  }
}

export interface IEmployeeFromBackend {
  id: UUID
  email: string
  first_name?: string
  last_name?: string
  middle_name?: string
  department: {
    id: UUID
    title: string
  }
  organization_id: UUID
  is_registered: boolean
  two_fa: boolean
  roles: ERole[]
  phone?: string
  position?: string
  picture?: string
  tags: {
    id: UUID
    title: string
    color: string
    type: ETagType
    organization_id: UUID
    is_active: boolean
  }[]
  note?: string
  statistic: {
    progress: number
    phishing: number
    risk_level: number
    phishing_events: {
      sent: number
      opened: number
      clicked: number
      entered_data: number
      opened_attachment: number
    }
  }
  created_at?: number
}

export interface IEmployee {
  id: UUID
  email: string
  first_name?: string
  last_name?: string
  middle_name?: string
  department: {
    id: UUID
    title: string
  }
  organization_id: UUID
  is_registered: boolean
  two_fa: boolean
  role: Nullable<ERole>
  phone?: string
  position?: string
  picture?: string
  tags: {
    id: UUID
    title: string
    color: string
    type: ETagType
    organization_id: UUID
    is_active: boolean
  }[]
  note?: string
  statistic: {
    progress: number
    phishing: number
    risk_level: number
    phishing_events: {
      sent: number
      opened: number
      clicked: number
      entered_data: number
      opened_attachment: number
    }
  }
  created_at?: number
}

export interface IGetEmployeesTableResponse extends ResponseWithPagination {
  data: IEmployee[]
}

export interface IGetEmployeesTableResponseFromBackend extends ResponseWithPagination {
  data: IEmployeeFromBackend[]
}

export interface IGetEmployeesTableRequest extends IGetDepartmentsTableRequest {}

export interface IDepartment {
  id: UUID
  title: string
  employees_count: number
}

export type TDepartment = {
  progress_statistic: {
    value: number
  }
  risk_level_statistic: {
    value: number
    color: ERiskLevelColor
    change: number
  }
  phishing_statistic: {
    color: ERiskLevelColor
    groups: {
      sent: {
        value: number
      }
      opened: {
        value: number
      }
      clicked: {
        value: number
      }
      entered_data: {
        value: number
      }
    }
  }
  id: UUID
  title: string
  employees_count: number
}

export interface IAssignedModule {
  id: UUID
  old_id: number
  title: string
}

export interface IMyCourse {
  id: UUID
  old_id: 0
  title: string
  description: string
  picture: string
  status: ECourseStatus
  created_date: string
  start_date: string
  end_date: string
  employees_count: number
  assigned_modules: IAssignedModule[]
  statistics: {
    value: number
    quiz: number
    learning: number
  }
}

export interface IProgress {
  date: string
  overall_progress_percent: number
  passed_theory_percent: number
  passed_tests_percent: number
  max: number
  color: ERiskLevelColor
}
export interface IOrganizationPhishingStatistics {
  value: number
  color: ERiskLevelColor
  opened_letters: number
  link_clicks: number
  entered_data: number
  opened_attachments: number
  sent: number
}

export interface IRiskLevel {
  date: string
  value: number
}

export type IProgressStatisticsChange = IProgressStatistics[]
export type IProgressScormStatisticsChange = {
  points: IProgressStatistics[]
}

export interface IProgressStatistics {
  date: string
  theory: number
  quiz: number
  overall: number
}

export interface IScormProgressStatisticsWithPoints {
  points: IScormProgressStatistics[]
}

export interface IScormProgressStatistics {
  date: string
  quiz: number
  learning: number
  overall: number
}

export interface IProgressStatisticsQuiz {
  passed: number
  total: number
}

export interface IPhishingStatisticsChart {
  opened: number
  clicked: number
  entered_data: number
  opened_attachment: number
  efficiency: number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  attack_vectors: any[]
}

export interface IOrganizationRiskStatistics {
  starts_at: string
  ends_at: string
  license_type: string
  users_count: number
  users_limit: number
  risk_level: number
  color: ERiskLevelColor
}

export interface IRiskStatistics {
  value: number
  date: string
  color: ERiskLevelColor
}
