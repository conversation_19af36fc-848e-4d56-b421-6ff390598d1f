import {
  IPhishingStatistics,
  IProgressStatistics,
  IProgressStatisticsQuiz,
  IRiskStatistics,
} from './types'
import { IUser } from 'entities/employee'
import { INote } from '@/shared/types/store/employees'
import { globalBaseApi } from '@/store/services/endpoints/base'
import { BACKEND_URLS } from '@/shared/constants/urls'
import { getHighRole } from '@/shared/helpers/employees'
import { ERole } from '@/shared/types/enums'

interface IUserNew extends Omit<IUser, 'role'> {
  roles: Nullable<ERole[]>
}

export const employeeStatisticAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getInfo: build.query<IUser, UUID>({
      query: id => ({
        url: `${BACKEND_URLS.USER_SERVICE}/users/${id}`,
      }),
      transformResponse: (response: IUserNew) => {
        const { roles, ...user } = response
        return {
          ...user,
          role: getHighRole(roles),
        }
      },
      providesTags: ['employee-statistic-info'],
      keepUnusedDataFor: Number.MAX_SAFE_INTEGER,
    }),
    updateNote: build.mutation<ResponseWithNotification<INote>, { id: UUID; text: string }>({
      query: ({ id, text }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/users/note`,
        method: 'PATCH',
        body: {
          user_id: id,
          text,
        },
      }),
      invalidatesTags: ['employee-statistic-note'],
      transformResponse: (response: INote) => ({
        ...response,
        notificationTip: 'tips:employess.note.update',
      }),
    }),
  }),
})

const PREFIX_URL = '/gateway/v1/statistic/'

export const employeeGatewayAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getEmployeeRiskStatistics: build.query<
      { risk_levels: IRiskStatistics[] },
      { user_id: UUID | void; days_period?: string }
    >({
      query: ({ user_id, ...rest }) => ({
        url: `${PREFIX_URL}users/${user_id}/risk-level`,
        params: { ...rest },
      }),
    }),
    getEmployeeProgressStatistics: build.query<
      { progress: IProgressStatistics[] },
      { user_id: UUID | void; days_period?: string }
    >({
      query: ({ user_id, ...rest }) => ({
        url: `${PREFIX_URL}users/${user_id}/progress`,
        params: { ...rest },
      }),
      // transformResponse: (result: { progress: IProgressStatistics[] }) => ({
      //   progress: result.progress.slice(-90),
      // }),
    }),
    getEmployeePhishingStatistics: build.query<
      IPhishingStatistics,
      { user_id: UUID | void; days_period?: string }
    >({
      query: ({ user_id, ...rest }) => ({
        url: `${PREFIX_URL}users/${user_id}/phishing`,
        params: { ...rest },
      }),
    }),
    getEmployeeProgresStatisticsQuiz: build.query<IProgressStatisticsQuiz, UUID>({
      query: user_id => ({
        url: `${PREFIX_URL}users/${user_id}/quizes`,
      }),
    }),
  }),
})
