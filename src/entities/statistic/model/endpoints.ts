import { globalBaseApi } from '@/store/services/endpoints/base'
import { IUser } from 'entities/employee'
import {
  IActiveCourses,
  IGetEmployeesTableRequest,
  IGroups,
  IOrganization,
  IProgress,
  TProgressChange,
  TRiskGroups,
  TRiskLevelChange,
  IPhishingStatistics,
  IProgressScormStatisticsChange,
  IProgressStatisticsChange,
  IRiskStatistics,
  IUserPassedStat,
} from './types'
import {
  IGetEmployeesTableResponse,
  IGetEmployeesTableResponseFromBackend,
} from '@/shared/types/store/organization-statistic'
import { BACKEND_URLS } from '@/shared/constants/urls'
import { getHighRole } from '@/shared/helpers/employees'

const PREFIX_URL = '/lk/api/v2/'
const GATEWAY_PREFIX = '/gateway/v1/statistic/'

export const statisticApi = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getStatisticUserInfo: build.query<IUser, void>({
      query: () => ({
        url: `${BACKEND_URLS.USER_SERVICE}/users/me`,
      }),
      transformResponse: (response: IUser) => {
        const preparedCommonInfo = {
          organizationID: response?.organization_id || '',
          userID: response?.id || '',
        }
        localStorage.setItem('common_info_v2', JSON.stringify(preparedCommonInfo))
        return response
      },
      providesTags: ['me'],
      keepUnusedDataFor: Number.MAX_SAFE_INTEGER,
    }),
    getUserProgressStatisticsChange: build.query<
      { progress: IProgressStatisticsChange },
      string | void
    >({
      query: days_period => ({
        url: `${GATEWAY_PREFIX}my/progress`,
        params: days_period
          ? {
              days_period: days_period,
            }
          : undefined,
      }),
      //! TAKE ONLY LAST 90 DAYS
      transformResponse: (response: { progress: IProgressStatisticsChange }) => ({
        progress: response.progress.slice(-90),
      }),
    }),
    getUserProgressInteractiveStatisticsChange: build.query<
      IProgressStatisticsChange,
      number | void
    >({
      query: (days_count = 90) => ({
        url: `${PREFIX_URL}scorm/analytics/users/my_progress_change?days_count=` + days_count,
      }),
      //! TAKE ONLY LAST 90 DAYS
      transformResponse: (response: IProgressScormStatisticsChange) => response.points.slice(-90),
    }),
    getMyPassedStat: build.query<IUserPassedStat, void>({
      query: () => ({
        url: `${GATEWAY_PREFIX}my/passed_stat`,
      }),
    }),
    getUserPhishingStatistics: build.query<IPhishingStatistics, string | void>({
      query: days_period => ({
        url: `${GATEWAY_PREFIX}my/phishing`,
        params: days_period
          ? {
              days_period: days_period,
            }
          : undefined,
      }),
    }),
    getUserRiskStatistics: build.query<{ risk_levels: IRiskStatistics[] }, string | void>({
      query: days_period => ({
        url: `${GATEWAY_PREFIX}my/risk-level`,
        params: days_period
          ? {
              days_period: days_period,
            }
          : undefined,
      }),
    }),
  }),
  overrideExisting: true,
})

export const organizationStatisticAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getOrganization: build.query<IOrganization, UUID>({
      query: id => ({
        url: `${PREFIX_URL}/organizations/${id}`,
      }),
    }),
    getEmployeesTable: build.query<IGetEmployeesTableResponse, IGetEmployeesTableRequest>({
      query: ({ organization_id, limit, offset }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/users/list/all${
          organization_id ? `?organization_id=${organization_id}&` : '?'
        }limit=${limit}&offset=${offset}`,
      }),
      transformResponse: (response: IGetEmployeesTableResponseFromBackend) => {
        const newEmployees = response.data.map(({ roles, ...employee }) => ({
          ...employee,
          role: getHighRole(roles),
        }))
        return {
          ...response,
          data: newEmployees,
        }
      },
    }),
  }),
  overrideExisting: true,
})

const ORG_STATICTICS_PREFIX_URL = '/gateway/v1/statistic/'

export const organizationGatewayStatisticAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getRiskStatistics: build.query<IRiskStatistics, UUID | void>({
      query: organization_id => ({
        url: `${ORG_STATICTICS_PREFIX_URL}organizations/${organization_id}/current-risk-level`,
      }),
    }),
    getPhishingStatistics: build.query<IPhishingStatistics, UUID | void>({
      query: organization_id => ({
        url: `${ORG_STATICTICS_PREFIX_URL}organizations/${organization_id}/phishing`,
      }),
    }),
    getProgress: build.query<IProgress, { organization_id: UUID; days_period?: string }>({
      query: ({ organization_id, ...rest }) => ({
        url: `${ORG_STATICTICS_PREFIX_URL}organizations/${organization_id}/current-progress`,
        params: { ...rest },
      }),
    }),
    getProgressChange: build.query<
      TProgressChange,
      { organization_id?: UUID; days_period?: string }
    >({
      query: ({ organization_id, ...rest }) => ({
        url: `${ORG_STATICTICS_PREFIX_URL}organizations/${organization_id}/progress`,
        params: { ...rest },
      }),
    }),
    getRiskLevelChange: build.query<
      TRiskLevelChange,
      { organization_id: UUID | void; days_period?: string }
    >({
      query: ({ organization_id, ...rest }) => ({
        url: `${ORG_STATICTICS_PREFIX_URL}organizations/${organization_id}/risk-levels`,
        params: { ...rest },
      }),
      transformResponse: (response: { risk_levels: IRiskStatistics[] }, _, { days_period }) => {
        if (!days_period) return response

        const daysLimit = parseInt(days_period, 10) + 2
        const today = new Date()
        const endDate = new Date(today.getTime() - 24 * 60 * 60 * 1000 * daysLimit).getTime()

        return {
          risk_levels: response.risk_levels.filter(element => {
            const elementDate = new Date(element.date).getTime()
            if (elementDate > endDate) return true
            return false
          }),
        }
      },
    }),
    getRiskGroups: build.query<TRiskGroups, UUID | void>({
      query: organization_id => ({
        url: `${ORG_STATICTICS_PREFIX_URL}organizations/${organization_id}/risk-groups`,
      }),
    }),
    getGroup: build.query<IGroups, UUID | void>({
      query: organization_id => ({
        url: `${ORG_STATICTICS_PREFIX_URL}organizations/${organization_id}/progress-groups`,
      }),
    }),
    getActiveCourse: build.query<IActiveCourses, UUID>({
      query: organization_id => ({
        url: `${ORG_STATICTICS_PREFIX_URL}organizations/${organization_id}/courses-counts`,
      }),
    }),
  }),
})
