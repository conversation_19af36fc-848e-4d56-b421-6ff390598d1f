export interface ChangeLogsResponse extends ResponseWithPagination {
  data: ChangeLogItem[]
}

export interface InstructionCreateResponse extends ChangeLogItem {}
export interface InstructionCreateRequest {
  file: File
}

export interface ChangeLogItem {
  id: string
  version: string
  type: string
  created_at: string
}

export interface ChangeLogDetailItem extends ChangeLogItem {
  text: string
}
