import { ChangeLogsResponse, ChangeLogItem, ChangeLogDetailItem } from './types'
import { globalBaseApi } from '@/store/services/endpoints/base'

const PREFIX_URL = '/lk/api/v2/'

export const changeLogApi = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getChangeLogs: build.query<ChangeLogsResponse, void>({
      query: () => ({
        url: PREFIX_URL + 'organizations/changelogs',
        method: 'GET',
      }),
      providesTags: ['changelogs'],
    }),

    deleteChangeByIdLog: build.mutation<void, string>({
      query: id => ({
        url: PREFIX_URL + `admin/changelogs/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['changelogs'],
    }),
    updateChangeLog: build.mutation<
      ChangeLogItem,
      { id: string; body: Omit<ChangeLogItem, 'id' | 'created_at'> }
    >({
      query: ({ body, id }) => ({
        url: PREFIX_URL + 'admin/changelogs/' + id,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['changelogs'],
    }),
    getChangeLog: build.query<ChangeLogDetailItem, string>({
      query: id => ({
        url: PREFIX_URL + 'organizations/changelogs/' + id,
        method: 'GET',
      }),
      providesTags: ['changelogs'],
    }),
    createChangeLog: build.mutation<void, Omit<ChangeLogDetailItem, 'id' | 'created_at'>>({
      query: body => ({
        url: PREFIX_URL + 'admin/changelogs',
        method: 'POST',
        body,
      }),
      invalidatesTags: ['changelogs'],
    }),
  }),
})
