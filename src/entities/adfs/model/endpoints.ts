import { globalBaseApi } from '@/store/services/endpoints/base'

const PREFIX_URL = '/lk/api/v2/'

export const ADFSApi = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getADFSRedirect: build.query<{ url: string }, string>({
      query: domain => ({
        url: `${PREFIX_URL}auth/adfs/redirect?domain_url=${encodeURIComponent(domain)}`,
        //! Для отчистки токена, которого еще нет
        headers: {},
      }),
    }),
  }),
})
