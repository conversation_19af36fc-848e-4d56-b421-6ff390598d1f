import {
  IDefaultSettingsKeyAndCertificateResponse,
  IDefaultSettingsPatchRequest,
  IDefaultSettingsPatchResponse,
  IDefaultSettingsResponse,
  IDefaultSettingsUploadArchiveResponse,
  IActiveDirectoryResponse,
  ISMTPRequest,
  ISMTPResponse,
  ADAutoSyncSettings,
  IADFSRequest,
  IADFSResponse,
  PasswordPoliciesResponse,
  PasswordPolicyRequest,
} from '@/shared/types/store/settings'

import { CreateSuperUserResponse, Superuser } from '@/shared/types/store/settings/superusers'
import { globalBaseApi } from '@/store/services/endpoints/base'

const PREFIX_URL = '/lk/api/v2/'
const USER_PREFIX_URL = '/users/api/v1/'

export const settingsApi = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getOrganizationsSettings: build.query<IDefaultSettingsResponse, void>({
      query: () => ({
        url: `${PREFIX_URL}settings/organization`,
      }),
      providesTags: ['organization'],
    }),
    organizationUploadArchive: build.mutation<IDefaultSettingsUploadArchiveResponse, FormData>({
      query: body => ({
        url: `${PREFIX_URL}settings/upload_pkcs12`,
        method: 'POST',
        body,
      }),
    }),
    organizationUploadCrtFile: build.mutation<IDefaultSettingsKeyAndCertificateResponse, FormData>({
      query: body => ({
        url: `${PREFIX_URL}settings/upload_cert_files`,
        method: 'POST',
        body,
      }),
    }),
    organizationPatch: build.mutation<
      ResponseWithNotification<IDefaultSettingsPatchResponse>,
      IDefaultSettingsPatchRequest
    >({
      query: body => ({
        url: `${PREFIX_URL}settings/organization`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['organization'],
      transformResponse: (response: IDefaultSettingsPatchResponse) => ({
        ...response,
        notificationTip: 'tips:settings.update',
      }),
    }),
    //! LDAP
    getLDAPSettings: build.query<IActiveDirectoryResponse, void>({
      query: () => ({
        url: `${PREFIX_URL}settings/ldap`,
      }),
      providesTags: ['LDAP'],
    }),
    LDAPPatch: build.mutation<ResponseWithNotification<IActiveDirectoryResponse>, FormData>({
      query: body => ({
        url: `${PREFIX_URL}settings/ldap`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['LDAP'],
      transformResponse: (response: IActiveDirectoryResponse) => ({
        ...response,
        notificationTip: 'tips:settings.ldap.update',
      }),
    }),
    getAutoSyncSettings: build.query<ADAutoSyncSettings, void>({
      query: () => ({
        url: `${PREFIX_URL}settings/ad/auto-sync`,
      }),
    }),
    patchAutoSyncSettings: build.mutation<
      ResponseWithNotification<ADAutoSyncSettings>,
      ADAutoSyncSettings
    >({
      query: body => ({
        url: `${PREFIX_URL}settings/ad/auto-sync`,
        method: 'PATCH',
        body,
      }),
      transformResponse: (response: ADAutoSyncSettings) => ({
        ...response,
        notificationTip: 'tips:settings.ad.update',
      }),
    }),
    //! SMTP
    getSMTPSettings: build.query<ISMTPResponse, void>({
      query: () => ({
        url: `${PREFIX_URL}settings/smtp`,
      }),
      providesTags: ['SMTP'],
    }),
    SMTPPatch: build.mutation<ResponseWithNotification<ISMTPResponse>, ISMTPRequest>({
      query: body => ({
        url: `${PREFIX_URL}settings/smtp`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['SMTP'],
      transformResponse: (response: ISMTPResponse) => ({
        ...response,
        notificationTip: 'tips:settings.smtp.update',
      }),
    }),
    // ADFS
    getADFSSettings: build.query<IADFSResponse, void>({
      query: () => ({
        url: `${PREFIX_URL}settings/adfs`,
      }),
      providesTags: ['adfs'],
    }),
    updateSettings: build.mutation<ResponseWithNotification<IADFSRequest>, IADFSRequest>({
      query: body => ({
        url: `${PREFIX_URL}settings/adfs`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['adfs'],
      transformResponse: (response: IADFSRequest) => ({
        ...response,
        notificationTip: 'tips:settings.adfs.update',
      }),
    }),
    // superusers
    getSuperusers: build.query<Superuser[], UUID>({
      query: orgId => ({
        url: `${USER_PREFIX_URL}super-users/multiple/organization/${orgId}`,
      }),
      providesTags: ['superusers'],
    }),
    createSuperuser: build.mutation<CreateSuperUserResponse, UUID>({
      query: orgId => ({
        url: `${USER_PREFIX_URL}super-users/multiple`,
        method: 'POST',
        body: { organization_id: orgId },
      }),
      invalidatesTags: ['superusers'],
    }),
    deleteSuperuser: build.mutation<void, UUID>({
      query: userId => ({
        url: `${USER_PREFIX_URL}super-users/multiple/${userId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['superusers'],
    }),
    // Password Policies
    getPasswordPolicies: build.query<PasswordPoliciesResponse, void>({
      query: () => ({
        url: `${PREFIX_URL}settings/password-policies`,
      }),
      providesTags: ['password-policies'],
    }),
    updatePasswordPolicy: build.mutation<
      ResponseWithNotification<PasswordPolicyRequest>,
      PasswordPolicyRequest
    >({
      query: body => ({
        url: `${PREFIX_URL}settings/password-policies`,
        method: 'PUT',
        body,
      }),
      invalidatesTags: ['password-policies'],
      transformResponse: (response: PasswordPolicyRequest) => response,
    }),
    createPasswordPolicy: build.mutation<
      ResponseWithNotification<PasswordPolicyRequest>,
      PasswordPolicyRequest
    >({
      query: body => ({
        url: `${PREFIX_URL}settings/password-policies`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['password-policies'],
      transformResponse: (response: PasswordPolicyRequest) => ({
        ...response,
        notificationTip: 'tips:settings.password-policy.create',
      }),
    }),
    deletePasswordPolicy: build.mutation<void, string>({
      query: roleType => ({
        url: `${PREFIX_URL}settings/password-policies/${roleType}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['password-policies'],
    }),
  }),
  overrideExisting: true,
})
