import { FC, ReactNode } from 'react'
import { Textarea, TextareaProps } from '@/shared/ui'
import classNamesBind from 'classnames/bind'
import styles from './notice.module.scss'

const cx = classNamesBind.bind(styles)
type EmployeeNoticeProps = {
  textarea?: TextareaProps.Own
  title?: string
  actions?: ReactNode
  wrapperClassName?: string
}

export const EmployeeNotice: FC<EmployeeNoticeProps> = ({
  wrapperClassName,
  actions,
  title,
  textarea,
}) => {
  return (
    <section className={cx('wrapper', wrapperClassName)}>
      {actions}
      {title && <h3 className={cx('title')}>{title}</h3>}
      <Textarea
        {...textarea}
        className={cx('textarea', textarea?.className)}
        placeholder={textarea?.placeholder}
        value={textarea?.value}
        onChange={textarea?.onChange}
      />
    </section>
  )
}
