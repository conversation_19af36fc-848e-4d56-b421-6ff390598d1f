.wrapper{
  position: relative;
  padding: 20px 24px 26px 24px;
  background: var(--white);
  border: 1px solid var(--color-gray-30);
  border-radius: 16px;

  display: flex;
  flex-direction: column;
  gap: 16px;
}

.title{
  font: var(--font-text-2-medium);
  color: var(--color-gray-90);
}

.textarea{
  padding: 16px;
  height: 100%;
  background: var(--color-gray-30);

  &:focus, &:disabled {
    background: var(--color-gray-30) !important;
    box-shadow: none !important;
  }
}