import { FC, ReactNode } from 'react'
import classNamesBind from 'classnames/bind'
import styles from './information.module.scss'

const cx = classNamesBind.bind(styles)
type EmployeeInformationAttr = {
  name: ReactNode
  content: ReactNode
}

type EmployeeInformationProps = {
  attributes: EmployeeInformationAttr[]
  actions?: ReactNode
  wrapperClassName?: string
}

export const EmployeeInformation: FC<EmployeeInformationProps> = ({
  wrapperClassName,
  attributes,
  actions,
}) => {
  return (
    <section className={cx('wrapper', wrapperClassName)}>
      {actions}
      <ul className={cx('wrapper__content')}>
        {attributes.map((attr, index) => (
          <li className={cx('wrapper__content__item')} key={index}>
            <div className={cx('wrapper__content__item__name')}>{attr.name}</div>
            <div className={cx('wrapper__content__item__content')}>{attr.content}</div>
          </li>
        ))}
      </ul>
    </section>
  )
}
