import { IFilters } from '@/shared/types/filters'
import { IFilter } from '@/shared/types/store/employees'

/**
 * Converts partial filter and search parameters into a complete IFilters object.
 *
 * @param filter - Partial filter object containing various filtering criteria.
 * @param search - Search string to filter employees.
 * @returns A complete IFilters object with default values for missing properties.
 */
export const toEmployeesQueryFiltersDTO = (
  filter: Partial<IFilter>,
  search: IFilters['search'],
): IFilters => {
  const queryFilters = {
    search: search || '',
    departments: filter.departments || [],
    phishing_events: filter.phishingEvents || [],
    role: filter.role || null,
    courses: filter.courses || [],
    courseProgress: filter.courseProgress || [],
    phishing: filter.phishing || [],
    learning: filter.learning || [],
    tags: filter.tags || [],
    riskLevelMin: filter.riskLevelMin !== undefined ? filter.riskLevelMin : 0,
    riskLevelMax: filter.riskLevelMax !== undefined ? filter.riskLevelMax : 10,
  }

  return queryFilters
}
