import { FetchBaseQueryError } from '@reduxjs/toolkit/query'
import { SerializedError } from '@reduxjs/toolkit'

import { IFilter } from '@/shared/types/store/employees'
import { SortDirection } from '@/shared/components/icon-sort-direction'
import { IUsers } from './types'
import { SORT_BY } from './sort'

export interface IUseEmployees {
  /** Employees Data */
  employees: IUsers | undefined
  isLoadingEmployees: boolean
  isFetchingEmployees: boolean
  errorEmployees: FetchBaseQueryError | SerializedError | undefined | string

  /** Pagination */
  limit: number
  page: number
  setPage: (page: number) => void

  /** State */
  sort: SORT_BY
  setSort: (sort: SORT_BY) => void
  sortDirection?: SortDirection
  setSortDirection: (direction?: SortDirection) => void
  filter: IFilter
  setFilter: (filter: Partial<IFilter>) => void
  defaultValues: IFilter
  hasFilter: boolean
  search: string
  debouncedSearch: string
  setSearch: (search: string) => void

  /** Collections */
  checkedEmployees: UUID[]
  setCheckedEmployees: (ids: UUID[]) => void
  excludeEmployees: UUID[]
  setExcludeEmployees: (ids: UUID[]) => void

  /** Helpers */
  isAllSelected: boolean
  setIsAllSelected: (isAllSelected: boolean) => void
  onSelectAllClick: () => void
  useInPhishingCampaign: boolean
  setUseInPhishingCampaign: (useInPhishingCampaign: boolean) => void
  useInCourseAssignment: boolean
  setUseInCourseAssignment: (useInCourseAssignment: boolean) => void
  getCountOfEmployess: () => number
  handleEmployeeCheck: (employeeId: string, selected: boolean) => void
}
