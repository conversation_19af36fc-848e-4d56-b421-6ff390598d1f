import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit'

import { SortDirection } from '@/shared/components/icon-sort-direction'
import { IFilter } from '@/shared/types/store/employees'
import { EMPLOYEES_LIMIT } from '@/shared/constants'
import { SORT_BY } from './sort'

interface EmployeeSliceState {
  search: string
  page: number
  sort: SORT_BY
  sortDirection?: SortDirection
  filter: IFilter
  limit: number
  checkedEmployees: UUID[]
  excludeEmployees: UUID[]
  isAllSelected: boolean
  useInPhishingCampaign: boolean
  useInCourseAssignment: boolean
}

interface EmployeeState {
  employees: EmployeeSliceState
}

const initialState: EmployeeSliceState = {
  search: '',
  page: 0,
  sort: SORT_BY.NAME,
  sortDirection: undefined,
  filter: {
    departments: [],
    phishingEvents: [],
    role: null,
    courses: [],
    courseProgress: [],
    phishing: [],
    learning: [],
    tags: [],
    riskLevelMax: 10,
    riskLevelMin: 0,
  },
  limit: EMPLOYEES_LIMIT,
  checkedEmployees: [],
  excludeEmployees: [],
  isAllSelected: false,
  useInPhishingCampaign: false,
  useInCourseAssignment: false,
}

/**
 * Slice for managing employee-related state in the application.
 */
export const employeesSlice = createSlice({
  name: 'employees',
  initialState,
  reducers: {
    setSearch(state, action: PayloadAction<string>) {
      state.search = action.payload
      state.page = 0 // Reset page on search
    },
    setPage(state, action: PayloadAction<number>) {
      state.page = action.payload
    },
    setSort(state, action: PayloadAction<SORT_BY>) {
      state.sort = action.payload
    },
    setSortDirection(state, action: PayloadAction<SortDirection | undefined>) {
      state.sortDirection = action.payload
    },
    setFilter(state, action: PayloadAction<Partial<EmployeeSliceState['filter']>>) {
      state.filter = { ...state.filter, ...action.payload }
    },
    setCheckedEmployees(state, action: PayloadAction<UUID[]>) {
      state.checkedEmployees = action.payload
    },
    setExcludeEmployees(state, action: PayloadAction<UUID[]>) {
      state.excludeEmployees = action.payload
    },
    setIsAllSelected(state, action: PayloadAction<boolean>) {
      state.isAllSelected = action.payload
    },
    setUseInPhishingCampaign(state, action: PayloadAction<boolean>) {
      state.useInPhishingCampaign = action.payload
    },
    setUseInCourseAssignment(state, action: PayloadAction<boolean>) {
      state.useInCourseAssignment = action.payload
    },
    handleEmployeeCheck(state, action: PayloadAction<{ employeeId: string; selected: boolean }>) {
      const { employeeId, selected } = action.payload
      if (state.isAllSelected) {
        const excludeIndex = state.excludeEmployees.indexOf(employeeId)
        if (selected && excludeIndex !== -1) {
          state.excludeEmployees.splice(excludeIndex, 1)
        } else if (!selected && excludeIndex === -1) {
          state.excludeEmployees.push(employeeId)
        }
      } else {
        const checkedIndex = state.checkedEmployees.indexOf(employeeId)
        if (selected && checkedIndex === -1) {
          state.checkedEmployees.push(employeeId)
        } else if (!selected && checkedIndex !== -1) {
          state.checkedEmployees.splice(checkedIndex, 1)
        }
      }
    },
    toggleSelectAll(state) {
      state.isAllSelected = !state.isAllSelected
      if (state.isAllSelected) {
        state.checkedEmployees = []
        state.excludeEmployees = []
      }
    },
  },
})

export const selectCheckedEmployees = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.checkedEmployees,
)

export const selectExcludeEmployees = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.excludeEmployees,
)

export const selectIsAllSelected = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.isAllSelected,
)

export const selectUseInPhishingCampaign = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.useInPhishingCampaign,
)

export const selectUseInCourseAssignment = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.useInCourseAssignment,
)

export const selectSearch = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.search,
)

export const selectPage = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.page,
)

export const selectSortBy = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.sort,
)

export const selectSortDirection = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.sortDirection,
)

export const selectFilter = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.filter,
)

export const selectLimit = createSelector(
  (state: EmployeeState) => state,
  state => state.employees.limit,
)

export const selectAll = createSelector(
  (state: EmployeeState) => state,
  state => state.employees,
)

export const {
  setSearch,
  setPage,
  setSort,
  setSortDirection,
  setFilter,
  setCheckedEmployees,
  setExcludeEmployees,
  setUseInPhishingCampaign,
  setUseInCourseAssignment,
  setIsAllSelected,
  toggleSelectAll,
  handleEmployeeCheck,
} = employeesSlice.actions
