import { useCallback, useMemo } from 'react'
import { useLocation } from 'react-router-dom'

import { useAppDispatch, useAppSelector } from '@/store'
import { useDebounce } from '@/shared/hooks'
import { useGetEmployeesQuery } from '@/store/services/tags-employees-service'
import { URLS } from '@/shared/configs/urls'

import {
  employeesSlice,
  setSearch as setSearchSlice,
  setCheckedEmployees as setCheckedEmployeesSlice,
  setExcludeEmployees as setExcludeEmployeesSlice,
  setFilter as setFilterSlice,
  setIsAllSelected as setIsAllSelectedSlice,
  setPage as setPageSlice,
  setSort as setSortSlice,
  setSortDirection as setSortDirectionSlice,
  setUseInPhishingCampaign as setUseInPhishingCampaignSlice,
  setUseInCourseAssignment as setUseInCourseAssignmentSlice,
  handleEmployeeCheck as handleEmployeeCheckSlice,
  toggleSelectAll as toggleSelectAllSlice,
  selectPage,
  selectLimit,
  selectFilter,
  selectSortBy,
  selectSortDirection,
  selectSearch,
  selectCheckedEmployees,
  selectExcludeEmployees,
  selectIsAllSelected,
  selectUseInPhishingCampaign,
  selectUseInCourseAssignment,
} from './employee-slice'
import { IUseEmployees } from './use-employees.types'
import { toEmployeesQueryFiltersDTO } from '..'

export const useEmployees = (): IUseEmployees => {
  const { pathname } = useLocation()
  const dispatch = useAppDispatch()

  /** Selectors */
  const limit = useAppSelector(selectLimit)
  const search = useAppSelector(selectSearch)
  const page = useAppSelector(selectPage)
  const sort = useAppSelector(selectSortBy)
  const sortDirection = useAppSelector(selectSortDirection)
  const filter = useAppSelector(selectFilter)
  const checkedEmployees = useAppSelector(selectCheckedEmployees)
  const excludeEmployees = useAppSelector(selectExcludeEmployees)
  const isAllSelected = useAppSelector(selectIsAllSelected)
  const useInPhishingCampaign = useAppSelector(selectUseInPhishingCampaign)
  const useInCourseAssignment = useAppSelector(selectUseInCourseAssignment)
  const debouncedSearch = useDebounce(search, 500)

  /** Employees Query */
  const {
    data: employees,
    isLoading: isLoadingEmployees,
    isFetching: isFetchingEmployees,
    error: errorEmployees,
  } = useGetEmployeesQuery(
    {
      page: page,
      filter: toEmployeesQueryFiltersDTO(filter, debouncedSearch),
      sort: sort,
      sortDirection: sortDirection,
    },
    {
      skip:
        pathname !== URLS.ADMIN_STAFF_PAGE &&
        pathname !== URLS.ADMIN_PHISHING_CAMPAIGNS_CREATE_PAGE &&
        pathname !== URLS.ADMIN_LEARNING_ASSIGNED_COURSE_CREATE_PAGE,
    },
  )

  /** Actions */
  const setSearch: IUseEmployees['setSearch'] = search => {
    dispatch(setSearchSlice(search))
  }

  const setPage: IUseEmployees['setPage'] = page => {
    dispatch(setPageSlice(page))
  }

  const setSort: IUseEmployees['setSort'] = sort => {
    dispatch(setSortSlice(sort))
  }

  const setFilter: IUseEmployees['setFilter'] = filter => {
    dispatch(setFilterSlice(filter))
  }

  const setCheckedEmployees: IUseEmployees['setCheckedEmployees'] = ids => {
    dispatch(setCheckedEmployeesSlice(ids))
  }

  const setExcludeEmployees: IUseEmployees['setExcludeEmployees'] = ids => {
    dispatch(setExcludeEmployeesSlice(ids))
  }

  const setIsAllSelected: IUseEmployees['setIsAllSelected'] = isAllSelected => {
    dispatch(setIsAllSelectedSlice(isAllSelected))
  }

  const setSortDirection: IUseEmployees['setSortDirection'] = direction => {
    dispatch(setSortDirectionSlice(direction))
  }

  const setUseInPhishingCampaign: IUseEmployees['setUseInPhishingCampaign'] =
    useInPhishingCampaign => {
      dispatch(setUseInPhishingCampaignSlice(useInPhishingCampaign))
    }

  const setUseInCourseAssignment: IUseEmployees['setUseInCourseAssignment'] =
    useInCourseAssignment => {
      dispatch(setUseInCourseAssignmentSlice(useInCourseAssignment))
    }

  const handleEmployeeCheck: IUseEmployees['handleEmployeeCheck'] = (employeeId, selected) => {
    dispatch(handleEmployeeCheckSlice({ employeeId, selected }))
  }

  const onSelectAllClick = () => {
    dispatch(toggleSelectAllSlice())
  }

  const getCountOfEmployess: IUseEmployees['getCountOfEmployess'] = useCallback(() => {
    if (!employees?.total_count) return 0

    if (isAllSelected) {
      return employees?.total_count - excludeEmployees?.length
    }

    return checkedEmployees?.length
  }, [checkedEmployees?.length, employees?.total_count, excludeEmployees?.length, isAllSelected])

  const hasFilter = useMemo(
    () =>
      !!filter.phishing?.length ||
      !!filter.departments?.length ||
      !!filter.role?.length ||
      !!filter.phishingEvents?.length ||
      !!filter.courses?.length ||
      !!filter.tags?.length ||
      !!filter.learning?.length ||
      !!filter.courseProgress?.length ||
      filter.riskLevelMin !== 0 ||
      filter.riskLevelMax !== 10,
    [filter],
  )

  const defaultFilterValues = useMemo(() => employeesSlice.getInitialState().filter, [])

  return {
    limit,
    search,
    page,
    sort,
    sortDirection,
    filter,
    checkedEmployees,
    excludeEmployees,
    isAllSelected,
    useInPhishingCampaign,
    useInCourseAssignment,
    employees,
    isLoadingEmployees,
    isFetchingEmployees,
    errorEmployees,
    debouncedSearch,
    hasFilter,
    defaultValues: defaultFilterValues,
    setSearch,
    setPage,
    setSort,
    setFilter,
    setCheckedEmployees,
    setExcludeEmployees,
    setIsAllSelected,
    setSortDirection,
    setUseInPhishingCampaign,
    setUseInCourseAssignment,
    handleEmployeeCheck,
    onSelectAllClick,
    getCountOfEmployess,
  }
}
