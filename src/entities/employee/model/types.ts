import { IFilters } from '@/shared/types/filters'
import { ERole } from '@/shared/types/enums'
import { SORT_BY } from '@/entities/employee'
import { SortDirection } from 'shared/components/icon-sort-direction'
import { ITag } from '@/shared/types/store/tag'

export interface IUser {
  id: UUID
  created_at?: string
  updated_at?: string
  email: string
  organization_id: string
  first_name: Nullable<string>
  last_name: Nullable<string>
  middle_name: Nullable<string>
  department: Nullable<{
    id: UUID
    title: string
  }>
  two_fa: boolean
  position: Nullable<string>
  is_registered: boolean
  picture: Nullable<string>
  role: Nullable<ERole>
  statistic: {
    progress: number
    phishing: number
    risk_level: number
  }
  tags: Nullable<ITag[]>
  note?: string
}

interface IUserNew extends Omit<IUser, 'role'> {
  roles: Nullable<ERole[]>
}

export interface IUsers extends ResponseWithPagination {
  data: IUser[]
}

export interface IUsersResponse extends ResponseWithPagination {
  data: IUserNew[]
}
export interface IEditEmployeeResponse {
  department_id: UUID
  first_name: string
  last_name: string
  middle_name: string
  position: string
  role: ERole
}

export interface IGetEmployee {
  withMerge?: boolean
  page: number
  filter: IFilters
  sort?: SORT_BY
  limit?: number
  sortDirection?: SortDirection
}

export interface IEmployeesStatUser {
  id: UUID
  old_id: number
  email: string
  first_name: string
  middle_name: string
  last_name: string
  full_name?: string
}

export interface IEmployeesStatTree {
  id: UUID
  title: string
  users_count: number
  parent_department_uuid: UUID
  users: IEmployeesStatUser[]
  departments?: IEmployeesStatTree
}

export interface IEmployeesStatList {
  id: UUID
  title: string
  departments_ids?: UUID[]
  exists_users?: boolean
}

export interface IEmployeesStatWithData extends ResponseWithPagination {
  data: IEmployeesStatList[]
  parentID?: UUID | null
}

export interface IEmployeesReinviteRequest {
  ids?: {
    include_ids?: UUID[]
    exclude_ids?: UUID[]
  }
  all?: boolean
  filter: IFilters
}

export interface IEmployeesExportRequest {
  ids?: {
    include_ids?: UUID[]
    exclude_ids?: UUID[]
  }
  all?: boolean
  filter: IFilters
}

export interface IDeleteEmployeesRequest {
  ids?: {
    include_ids?: UUID[]
    exclude_ids?: UUID[]
  }
  all?: boolean
  filter: IFilters
}

export interface IEmployeesTagImport {
  to_assign_count: number
  to_assign_users: UUID[]
  unknown_count: number
}

export interface IEmployeesImportWithFileInfo {
  metadata?: {
    content: {
      info: {
        users: {
          basic: {
            created: number
            deleted: number
            updated: number
            domain_skipped: number
          }
          delete: {
            created: number
            deleted: number
            updated: number
            domain_skipped: number
          }
          update: {
            created: number
            deleted: number
            updated: number
            domain_skipped: number
          }
          full: {
            created: number
            deleted: number
            updated: number
            domain_skipped: number
          }
          domain_skipped: {
            created: number
            deleted: number
            updated: number
            domain_skipped: number
          }
        }
        departments: {
          basic: {
            created: number
            deleted: number
          }
          delete: {
            created: number
            deleted: number
          }
          update: {
            created: number
            deleted: number
          }
          full: {
            created: number
            deleted: number
          }
        }
      }
      uploading: 'not_started' | 'in_process' | 'success'
    }
    event_type: string
    metadata: {
      file_path: string
      file_name: string
      file_content_type: string
    }
  }
  id: UUID
  type: string
  status: string
  error: {
    code?: string
    params?: {
      email?: string
      position?: string
      emails?: string[]
      employees?: {
        section_code?: string
        info?: {
          invalid: string[]
          headers: string[]
          allowed: string[]
        }
      }
      departments?: {
        section_code?: string
        info?: {
          invalid: string[]
          headers: string[]
          allowed: string[]
        }
      }
    }
  }
}

export interface IPermissionPage {
  name: string
}

export interface IPermissionGroup {
  name: string
  pages: IPermissionPage[]
}

export interface IPermissions {
  user: IPermissionGroup[]
  admin?: IPermissionGroup[]
}
