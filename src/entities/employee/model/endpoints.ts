import { BACKEND_URLS } from '@/shared/constants/urls'
import { IUser } from './types'

import { globalBaseApi } from 'store/services/endpoints/base'

export const userAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getUserInfo: build.query<IUser, void>({
      query: () => ({
        url: `${BACKEND_URLS.USER_SERVICE}/users/me`,
      }),
      providesTags: ['user'],
      keepUnusedDataFor: Number.MAX_SAFE_INTEGER,
    }),
    uploadProfilePicture: build.mutation<ResponseWithNotification<void>, File>({
      query: picture => {
        const formData = new FormData()

        formData.append('avatar', picture)

        return {
          url: `${BACKEND_URLS.USER_SERVICE}/users/avatar`,
          method: 'PATCH',
          body: formData,
        }
      },
      invalidatesTags: ['user'],
      transformResponse: () => ({
        notificationTip: 'tips:users.avatar',
      }),
    }),
  }),
})
