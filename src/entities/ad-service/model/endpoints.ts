import { IADSyncApplyRequest, IADSyncInfo } from '@/shared/types/store/ad'
import { globalBaseApi } from '@/store/services/endpoints/base'

const PREFIX_URL = '/lk/api/v2/'

export const adApi = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    ADSyncStatus: build.query<IADSyncInfo, UUID>({
      query: eventId => ({
        url: `${PREFIX_URL}webhooks/events/${eventId}`,
      }),
    }),
    postADSync: build.mutation<IADSyncInfo, void>({
      query: () => ({
        url: `${PREFIX_URL}ad/sync`,
        method: 'POST',
      }),
    }),
    ADSyncApply: build.mutation<ResponseWithNotification<void>, IADSyncApplyRequest>({
      query: ({ params }) => ({
        url: `${PREFIX_URL}ad/sync/apply`,
        params,
        method: 'POST',
      }),
      transformResponse: () => ({
        notificationTip: 'tips:ad.sync',
      }),
    }),
  }),
})
