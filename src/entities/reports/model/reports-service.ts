import {
  IReportIntervalParams,
  IReportRequestPhishing,
  IReportRequestScorm,
  IReportStatus,
  IReportsResponse,
} from '@/shared/types/store/reports'
import { IReportsRequestTypes } from '@/shared/types/store/settings/reports'
import { globalBaseApi } from '../../../store/services/endpoints/base'

const PREFIX_URL = '/lk/api/v2/'

export const reportServiceApi = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getPDFReports: build.query<IReportsResponse, { limit?: number; offset?: number }>({
      query: ({ limit, offset }) => ({
        url: `${PREFIX_URL}reports/pdf`,
        params: {
          limit,
          offset,
        },
      }),
      providesTags: ['pdf-reports'],
    }),
    getReports: build.query<
      IReportsResponse,
      { category: IReportsRequestTypes; limit?: number; offset?: number }
    >({
      query: ({ limit, offset, category }) => ({
        url: `${PREFIX_URL}reports`,
        params: {
          category,
          limit,
          offset,
        },
      }),
      providesTags: ['all-reports'],
    }),
    getReportWithReportModal: build.query<ResponseWithNotification<IReportStatus>, string>({
      query: url => ({
        url: `${PREFIX_URL}${url}`,
      }),
      transformResponse: (response: IReportStatus) => {
        if (response.status === 'complete' && response.url) {
          return {
            ...response,
            notificationTip: 'tips:report.success',
          }
        }

        return response
      },
    }),
    postReportWithReportModal: build.mutation<
      IReportStatus,
      {
        url: string
        params: IReportRequestScorm | IReportRequestPhishing | IReportIntervalParams | null
      }
    >({
      query: ({ url, params }) => ({
        url: `${PREFIX_URL}${url}`,
        params: params ?? undefined,
        method: 'POST',
      }),
    }),
  }),
})
