import { TListItem } from '@/shared/components/theme-content/helper'

export type TAnswer = {
  id: UUID
  text: string
  is_correct: boolean
  answer_tip: string
}

export type TQuestion = {
  id: UUID
  order_id: number
  text: string
  multiple_answers: boolean
  answers: TAnswer[]
}

export type TSettings = {
  need_explain: boolean
  correct_answers: number
  questions_count: number
  testing_time_duration: number
  learning_block: boolean
  attempts_limit: number | null
}

export type TQuizStepType = 'quiz' | 'video' | 'article' | 'slide' | 'scorm'

export type TQuizStep = {
  id: UUID
  order_id: number
  type: TQuizStepType
  questions: TQuestion[]
  settings: TSettings
}

export type TVideoStep = {
  id: UUID
  order_id: number
  type: TQuizStepType
  title: string
  path: string
  description?: string
}

export type TBlockType = 'text' | 'image' | 'video' | 'audio' | 'gallery' | 'code'

export type TBlock = {
  id: UUID
  order_id: number
  type: TBlockType
  content: string | string[]
  options: {
    type?: string
    level?: number
    items?: string[] | TListItem[]
    style?: string | Record<string, string>
    language?: string
    diffLines?: {
      [key: number]: 'added' | 'removed' | undefined
    }
  }
}

export type TArticleStep = {
  id: UUID
  order_id: number
  type: TQuizStepType
  blocks: TBlock[]
}

export type TPresentationStep = {
  id: UUID
  title: string | null
  description: string | null
  order_id: number
  type: TQuizStepType
  pptx_file_path: string | null
}

export type TSlideStep = {
  id: UUID
  order_id: number
  type: TQuizStepType
  title: string
  background_path: string
  audio_path: string
  description: string | null
}

export type TScormStep = {
  id: UUID
  order_id: number
  type: TQuizStepType
  title: string
  path: string
  has_quiz: boolean
  quiz_count: number
  slide_count: number
}

export type TStep = TScormStep | TSlideStep | TArticleStep | TVideoStep | TQuizStep

type TVisibility = 'public' | 'private'

export type TTheme = {
  id: UUID
  title: string
  organization_id: UUID
  visibility: TVisibility
  available_in_demo: boolean
  archived: boolean
  created_at: string
  can_edit: boolean
  can_delete: boolean
  has_steps: boolean
  steps: TStep[]
  order_id: number
  pptx_path: string | null
}

export interface IThemeWithData extends ResponseWithPagination {
  data: TTheme[]
}

export type ThemesRequest = RequestWithPagination & {
  search?: string
  organization_id: string
}

export interface ICreateThemeResponse {
  id: UUID
  title: string
  organization_id: UUID
  visibility: TVisibility
  available_in_demo: boolean
  archived: boolean
  created_at: string
}

type TSwap = {
  object_id: UUID
  order_id: number
}

export type TSlidesSwapRequest = {
  slide_from: TSwap
  slide_to: TSwap
}

export type TStepsSwapRequest = {
  step_from: TSwap
  step_to: TSwap
}

export interface IPresentation {
  id: UUID
  title: string | null
  description: string | null
  order_id: number
  type: TQuizStepType
  pptx_file_path: string | null
  slides: Array<{
    id: UUID
    order_id: number
    text: string | null
    audio: string | null
    background: string | null
  }>
}

export interface IUpdateBlocksRequest {
  blocks: TBlock[]
}

export type TAttempt = {
  id: UUID
  started_at: string
  finished_at: string
  passed: boolean
  questions: TQuestion[]
}

export interface IStepProgressId {
  id: UUID
  type: TQuizStepType
  started_at: string
  finished_at: string
  step_id: UUID
  passed: boolean
  config: {
    has_quiz: boolean
    quiz_count: number
    slide_count: number
  }
  statement?: {
    courseID: string
    courseState: {
      quizInfo: [
        {
          type: string
          state: string
          passed: boolean
          retake: boolean
          attempts: boolean
        },
      ]
      slideInfo: {
        visited: boolean
        completed: boolean
      }[]
      viewDuration: number
      lastViewedSlide: number
      timestamp: number
    }
  }
  attempts?: TAttempt[]
  settings?: TSettings
}

export type ThemeProgress = {
  title: string
  theory: number
  quiz: number
  has_quizes: boolean
  has_theory: boolean
}

export interface EventData {
  id: UUID
  status: 'created' | 'completed' | 'failed'
  metadata: Record<string, unknown>
  error: Record<string, unknown>
}

export interface ThemeInThemeModule {
  course_id: string
  section_id: string
  theme_id: string
  new_title: string
}
