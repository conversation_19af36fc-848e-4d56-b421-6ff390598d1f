import { globalBaseApi } from '@/store/services/endpoints/base'
import { EventData, TQuestion, TQuizStep, TSettings } from '../types'

export const themeQuizApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    createQuiz: builder.mutation<
      TQuizStep,
      { themeId: UUID; questions: Array<Omit<TQuestion, 'id'>> }
    >({
      query: ({ themeId, questions }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/quiz`,
        method: 'POST',
        body: {
          type: 'quiz',
          questions,
        },
      }),
      invalidatesTags: ['quiz', 'theme'],
    }),
    updateQuiz: builder.mutation<
      TQuizStep,
      { themeId: UUID; stepId: UUID; questions: Array<Omit<TQuestion, 'id'>> }
    >({
      query: ({ themeId, questions, stepId }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/${stepId}/quiz`,
        method: 'PATCH',
        body: {
          type: 'quiz',
          questions,
        },
      }),
      invalidatesTags: ['quiz', 'theme'],
    }),
    updateQuizSettings: builder.mutation<
      TQuizStep,
      { themeId: UUID; stepId: UUID; settings: TSettings }
    >({
      query: ({ themeId, settings, stepId }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/${stepId}/quiz/settings`,
        method: 'PATCH',
        body: settings,
      }),
      invalidatesTags: ['quiz'],
    }),
    addQuizFromFile: builder.mutation<EventData, { themeId: UUID; body: FormData }>({
      query: ({ themeId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/quiz/file`,
        method: 'POST',
        body,
      }),
    }),
    getQuizFromFile: builder.query<{ file_path: string }, { themeId: UUID; stepId: UUID }>({
      query: ({ themeId, stepId }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/${stepId}/quiz-json`,
        method: 'GET',
      }),
    }),
  }),
  overrideExisting: true,
})
