import { globalBaseApi } from '@/store/services/endpoints/base';
import { EventData, TTheme } from '../types'

export const themeScormApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    createScorm: builder.mutation<EventData, { themeId: UUID, body: FormData }>({
      query: ({ themeId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/scorm`,
        method: 'POST',
        body
      }),
    }),
    updateScorm: builder.mutation<TTheme, { themeId: UUID; stepId: UUID; body: FormData }>({
        query: ({ themeId, stepId, body }) => ({
          url: `/learning/api/content/themes/${themeId}/steps/${stepId}/scorm`,
          method: 'PATCH',
          body
        }),
        invalidatesTags: ['scorm', 'theme'],
    }),
  }),
  overrideExisting: true,
})
