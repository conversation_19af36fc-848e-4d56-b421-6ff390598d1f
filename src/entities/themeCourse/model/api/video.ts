import { globalBaseApi } from '@/store/services/endpoints/base';
import { TVideoStep } from '../types'

export const themeVideoApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    createVideo: builder.mutation<TVideoStep, { themeId: UUID; body: FormData }>({
      query: ({ themeId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/video`,
        method: 'POST',
        body
      }),
      invalidatesTags: ['video', 'theme'],
    }),
    updateVideo: builder.mutation<TVideoStep, { themeId: UUID; stepId: UUID; body: FormData }>({
        query: ({ themeId, body, stepId }) => ({
          url: `/learning/api/content/themes/${themeId}/steps/${stepId}/video`,
          method: 'PATCH',
          body
        }),
        invalidatesTags: ['video', 'theme'],
    }),
  }),
  overrideExisting: true,
}) 
