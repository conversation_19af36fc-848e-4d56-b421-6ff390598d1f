import { globalBaseApi } from '@/store/services/endpoints/base';
import { IPresentation, TSlideStep, TSlidesSwapRequest } from '../types'

export const themeSlideApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    createSlide: builder.mutation<TSlideStep, { themeId: UUID; body: FormData }>({
      query: ({ themeId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/slide`,
        method: 'POST',
        body
      }),
      invalidatesTags: ['slide', 'theme'],
    }),
    updateSlide: builder.mutation<TSlideStep, { themeId: UUID; stepId: UUID; body: FormData }>({
        query: ({ themeId, body, stepId }) => ({
          url: `/learning/api/content/themes/${themeId}/steps/${stepId}/slide`,
          method: 'PATCH',
          body
        }),
        invalidatesTags: ['slide', 'theme'],
      }),
    swapSlides: builder.mutation<IPresentation, { themeId: UUID; stepId: UUID; body: TSlidesSwapRequest }>({
        query: ({ themeId, body, stepId }) => ({
          url: `/learning/api/content/themes/${themeId}/steps/${stepId}/presentation/slides/swap`,
          method: 'PATCH',
          body
        }),
        invalidatesTags: ['slide'],
    }),
  }),
  overrideExisting: true,
})