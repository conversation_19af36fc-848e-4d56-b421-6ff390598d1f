import { globalBaseApi } from '@/store/services/endpoints/base'
import { EventData } from '../types'

export const eventApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getEventData: builder.query<EventData, UUID | undefined>({
      query: event_id => ({
        url: `/learning/api/events/${event_id}`,
      }),
      providesTags: ['theme'],
    }),
  }),
  overrideExisting: true,
})
