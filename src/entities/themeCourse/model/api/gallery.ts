import { globalBaseApi } from '@/store/services/endpoints/base';
import { TArticleStep, TSlideStep } from '../types'

export const themeGalleryApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    addGallery: builder.mutation<TArticleStep, { themeId: UUID; stepId: UUID; body: TSlideStep[] }>({
      query: ({ themeId, stepId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/article/${stepId}/blocks/gallery`,
        method: 'POST',
        body
      }),
      invalidatesTags: ['gallery', 'theme'],
    }),
    deleteGalleryImage: builder.mutation<void, { themeId: UUID; stepId: UUID, blockId: UUID; imageId: UUID }>({
        query: ({ themeId, stepId, blockId, imageId }) => ({
          url: `/learning/api/content/themes/${themeId}/steps/${stepId}/gallery/blocks/${blockId}/images/${imageId}`,
          method: 'DELETE',
        }),
        invalidatesTags: ['gallery', 'article', 'theme'],
    }),
    appendGalleryImage: builder.mutation<TArticleStep, { themeId: UUID; stepId: UUID, blockId: UUID; body: FormData }>({
        query: ({ themeId, body, stepId, blockId }) => ({
          url: `/learning/api/content/themes/${themeId}/steps/${stepId}/gallery/blocks/${blockId}/images/append`,
          method: 'POST',
          body
        }),
        invalidatesTags: ['gallery', 'article', 'theme'],
    }),
  }),
  overrideExisting: true
})
