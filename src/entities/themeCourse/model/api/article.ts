import { globalBaseApi } from '@/store/services/endpoints/base';
import { IUpdateBlocksRequest, TArticleStep } from '../types'

export const themeArticleApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    createArticle: builder.mutation<TArticleStep, { themeId: UUID }>({
      query: ({ themeId }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/article`,
        method: 'POST',
      }),
    }),
    updateArticleBlocks: builder.mutation<TArticleStep, { themeId: UUID; stepId: UUID; body: IUpdateBlocksRequest }>({
      query: ({ themeId, stepId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/${stepId}/article/blocks/raw`,
        method: 'PATCH',
        body
      }),
      invalidatesTags: ['article', 'theme'],
    }),
    uploadFile: builder.mutation<{ urls: string[] }, { body: FormData }>({
      query: ({ body }) => ({
        url: '/learning/api/content/themes/upload-content',
        method: 'POST',
        body
      }),
      invalidatesTags: ['article', 'theme'],
    }),
  }),
  overrideExisting: true
})
