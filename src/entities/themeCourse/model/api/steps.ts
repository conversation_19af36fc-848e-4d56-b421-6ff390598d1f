import { globalBaseApi } from '@/store/services/endpoints/base';
import { IStepProgressId, TStepsSwapRequest, TTheme } from '../types'

export const themeStepsApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    deleteStep: builder.mutation<void, { themeId: UUID; stepId: UUID }>({
      query: ({ themeId, stepId }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/${stepId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['steps', 'theme'],
  }),
    swapSteps: builder.mutation<TTheme, { themeId: UUID; body: TStepsSwapRequest }>({
        query: ({ themeId, body }) => ({
          url: `/learning/api/content/themes/${themeId}/steps/swap`,
          method: 'PATCH',
          body
        }),
        invalidatesTags: ['steps', 'theme'],
    }),
    getStepProgress: builder.query<IStepProgressId, UUID | undefined>({
      query: step_progress_id => ({
        url: `learning/api/learning/assigned-courses/step-progress/${step_progress_id}`,
      }),
      providesTags: ['theme'],
    }),
  }),
  overrideExisting: true,
})
