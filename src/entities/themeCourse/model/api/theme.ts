import { globalBaseApi } from '@/store/services/endpoints/base'
import {
  ICreateThemeResponse,
  IThemeWithData,
  ThemeInThemeModule,
  ThemesRequest,
  TTheme,
} from '../types'

export const themeApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getThemeById: builder.query<TTheme, UUID | undefined>({
      query: themeId => ({
        url: `/learning/api/content/themes/${themeId}`,
      }),
      providesTags: ['theme'],
    }),
    deleteMessage: builder.mutation<void, UUID>({
      query: themeId => ({
        url: `/learning/api/content/themes/${themeId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['theme'],
    }),
    updateTheme: builder.mutation<void, { themeId: UUID; title: string }>({
      query: ({ themeId, title }) => ({
        url: `/learning/api/content/themes/${themeId}`,
        method: 'PATCH',
        body: {
          title,
        },
      }),
      invalidatesTags: ['theme', 'themes', 'course'],
    }),
    getOrganizationThemes: builder.query<IThemeWithData, ThemesRequest>({
      query: ({ organization_id, ...params }) => ({
        url: `/learning/api/content/themes/organizations/${organization_id}`,
        params,
      }),
      providesTags: ['theme'],
    }),
    createTheme: builder.mutation<ICreateThemeResponse, { title: string; organization_id: UUID }>({
      query: body => ({
        url: `/learning/api/content/themes`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['theme', 'themes'],
    }),
    copyTheme: builder.mutation<TTheme, UUID>({
      query: themeId => ({
        url: `/learning/api/content/themes/${themeId}/copy`,
        method: 'POST',
      }),
      invalidatesTags: ['theme'],
    }),
    saveThemeInThemeModule: builder.mutation<TTheme, ThemeInThemeModule>({
      query: body => ({
        url: `/learning/api/content/courses/save-theme-in-theme-module`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['theme'],
    }),
  }),
  overrideExisting: true,
})
