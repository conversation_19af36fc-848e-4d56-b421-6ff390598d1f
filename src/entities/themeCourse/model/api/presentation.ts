import { globalBaseApi } from '@/store/services/endpoints/base'
import { EventData, IPresentation } from '../types'

export const themePresentationApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    createPresentation: builder.mutation<EventData, { themeId: UUID; body: FormData }>({
      query: ({ themeId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/presentation`,
        method: 'POST',
        body,
      }),
    }),
    updatePresentation: builder.mutation<
      IPresentation,
      { themeId: UUID; stepId: UUID; body: { title: string; description: string } }
    >({
      query: ({ themeId, body, stepId }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/${stepId}/presentation`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['presentation', 'theme'],
    }),
    deletePresentation: builder.mutation<void, { themeId: UUID; stepId: UUID; slideId: UUID }>({
      query: ({ themeId, stepId, slideId }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/${stepId}/presentation/slides/${slideId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['presentation', 'theme'],
    }),
    updatePresentationSlide: builder.mutation<
      IPresentation,
      { themeId: UUID; stepId: UUID; slideId: UUID; body: FormData }
    >({
      query: ({ themeId, stepId, slideId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/${stepId}/presentation/slides/${slideId}`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['presentation', 'theme'],
    }),
    uploadJsonQuizStep: builder.mutation<EventData, { themeId: UUID; body: FormData }>({
      query: ({ themeId, body }) => ({
        url: `/learning/api/content/themes/${themeId}/steps/quiz-json`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['quiz', 'theme'],
    }),
    uploadJsonQuizStepInCourse: builder.mutation<
      EventData,
      { courseId: UUID; sectionId: UUID; themeId: UUID; body: FormData }
    >({
      query: ({ courseId, sectionId, themeId, body }) => ({
        url: `/learning/api/content/courses/${courseId}/sections/${sectionId}/themes/${themeId}/steps/quiz-json`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['quiz', 'theme'],
    }),
  }),
  overrideExisting: true,
})
