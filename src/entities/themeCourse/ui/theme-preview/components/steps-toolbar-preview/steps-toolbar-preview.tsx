import classNamesBind from 'classnames/bind'
import styles from './steps-toolbar-preview.module.scss'
import { StepsToolbarPreviewProps } from './steps-toolbar-preview.d'
import Article from '@/shared/ui/Icon/icons/components/Article'
import Slide from '@/shared/ui/Icon/icons/components/Slide'
import Quiz from '@/shared/ui/Icon/icons/components/Quiz'
import Video from '@/shared/ui/Icon/icons/components/Video'
import Import from '@/shared/ui/Icon/icons/components/Import'
import ScormIcon from '@/shared/ui/Icon/icons/components/ScormIcon'
import StepPassedIcon from '@/shared/ui/Icon/icons/components/StepPassedIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { IIconSize, TIconColor } from '@/shared/ui/Icon/icon.d'

import { useAppSelector } from '@/store'
import { selectThemeProgress } from '@/store/slices/user-course-slice'
import { useAnalytics } from '@/shared/hooks/use-analytics'
import { GOALS } from '@/shared/constants'

const cx = classNamesBind.bind(styles)

export type TMinifiedStepType = 'article' | 'slide' | 'quiz' | 'video' | 'import' | 'scorm'

// Функция для рендеринга иконок шагов
const renderStepIcon = (
  type: TMinifiedStepType,
  size: IIconSize,
  color: TIconColor,
  className?: string,
  onClick?: () => void,
) => {
  const iconComponent = () => {
    switch (type) {
      case 'article':
        return <Article />
      case 'slide':
        return <Slide />
      case 'quiz':
        return <Quiz />
      case 'video':
        return <Video />
      case 'import':
        return <Import />
      case 'scorm':
        return <ScormIcon />
      default:
        return <Slide />
    }
  }

  return (
    <IconWrapper size={size} color={color} className={className} onClick={onClick}>
      {iconComponent()}
    </IconWrapper>
  )
}

export type Step = {
  id: UUID
  type: TMinifiedStepType
}

export const StepsToolbarPreview: React.FC<StepsToolbarPreviewProps.Props> = ({
  steps,
  activeStep,
  isUser,
  setActive,
}) => {
  const themeProgress = useAppSelector(selectThemeProgress)
  const analytics = useAnalytics()

  const onStepClick = async (item: Step) => {
    analytics.event(GOALS['course-route-theme-item'].name, { step: item })
    setActive(item)
  }

  return (
    <div className={cx('wrapper')}>
      {steps &&
        steps.length > 0 &&
        steps?.map((item, index) => {
          const stepProgress =
            isUser && themeProgress
              ? themeProgress.steps.find(s => s.step_id === item.id)
              : undefined
          return (
            <div className={cx('step')} key={item.id}>
              <div className={cx('label')}>{index + 1}</div>
              {renderStepIcon(
                item.type,
                '40' as IIconSize,
                (isUser && !stepProgress?.started ? 'gray60' : 'white') as TIconColor,
                cx('block', {
                  active: activeStep?.id === item.id,
                  disabled: isUser && !stepProgress?.started,
                }),
                () => {
                  if (isUser && !stepProgress?.started) return
                  onStepClick(item)
                },
              )}
              {stepProgress && stepProgress.passed && (
                <IconWrapper
                  size='20'
                  color='self'
                  className={cx('checkmark', { active: activeStep?.id === item.id })}
                >
                  <StepPassedIcon />
                </IconWrapper>
              )}
            </div>
          )
        })}
    </div>
  )
}
