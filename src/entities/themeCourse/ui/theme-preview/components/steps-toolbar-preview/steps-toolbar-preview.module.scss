.block {
    background-color: var(--color-gray-60);
    border-radius: 8px;
    display: block;
    cursor: pointer;

    &.active {
        cursor: initial;
        background-color: var(--color-primary);
    }

    &.disabled {
        cursor: initial;
        background-color: var(--color-gray-50);
    }
}

.wrapper {
    margin-top: 24px;
    display: flex;
    gap: 8px;
    align-items: end;
    flex-wrap: wrap;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    position: relative;
}

.label {
    font-size: 14px;
    line-height: 18px;
    color: var(--gray-gray-70, #8e97af);
}

.checkmark {
    position: absolute;
    bottom: -4px;
    right: -4px;
    
    svg {
        circle {
            fill: var(--color-primary);
        }
        path {
            stroke: var(--color-surface);
        }
    }

    &.active {
        svg {
            circle {
                fill: var(--color-surface);
            }
            path {
                stroke: var(--color-primary);
            }
        }
    }
}