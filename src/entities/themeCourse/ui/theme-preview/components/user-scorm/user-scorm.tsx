/* eslint-disable @typescript-eslint/no-explicit-any */
import { testApi } from '@/entities/courses/model/api/endpoints'
import { themeStepsApi } from '@/entities/themeCourse/model/api'
import { TScormStep } from '@/entities/themeCourse/model/types'
import useFullscreen from '@/shared/hooks/use-fullscreen'
import { CompleteModal } from '@/shared/modals/editors/complete'
import { Button } from '@/shared/ui'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  handleNextStep,
  selectCourseStructure,
  selectLastStep,
  selectSectionId,
  selectTheme,
  selectThemeProgress,
} from '@/store/slices/user-course-slice'
import classNamesBind from 'classnames/bind'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useParams } from 'react-router-dom'
import { getQuizInfo, getSlideInfo } from './helpers'
import useInjectElement from './use-inject'
import { UserScormProps } from './user-scorm.d'
import styles from './user-scorm.module.scss'
import { useNotification } from './use-notification'

const cx = classNamesBind.bind(styles)

const TIMEOUT = 100

export const UserScorm: React.FC<UserScormProps.Props> = ({ stepId, path }) => {
  const { course_id = '' } = useParams()
  const { t } = useTranslation()
  const dispatch = useAppDispatch()

  const [statement, setStatement] = useState<Record<string, any> | null>({})

  const currentSection = useAppSelector(selectSectionId)
  const currentTheme = useAppSelector(selectTheme)
  const themeProgress = useAppSelector(selectThemeProgress)
  const strucutre = useAppSelector(selectCourseStructure)
  const isLastStep = useAppSelector(selectLastStep)

  const stepProgressId = useMemo(
    () => themeProgress?.steps.find(step => step.step_id === stepId)?.step_progress_id,
    [themeProgress, stepId],
  )

  const [getMyScormCourseStatement] = themeStepsApi.useLazyGetStepProgressQuery()

  const currentStepProgress = useMemo(
    () => themeProgress?.steps.find(step => step.step_id === stepId),
    [themeProgress, stepId],
  )

  const currentStep = useMemo(() => {
    if (currentSection !== undefined && currentTheme) {
      const curSec = strucutre?.sections.find(s => s.id === currentSection)
      const curTheme = curSec?.themes.find(t => t.id === currentTheme.theme_id)
      const step = curTheme?.steps.find(s => s.id === stepId)
      return step as TScormStep
    }
  }, [strucutre, currentSection, currentTheme, stepId])

  const [isDebounceSet, setIsDebounceSet] = useState(false)
  const getState = useCallback(async () => {
    if (!course_id || !stepProgressId) return

    try {
      const state = (await getMyScormCourseStatement(stepProgressId).unwrap()) || {}

      setStatement(state.statement || {})
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { courseID, courseState, ...s } = state?.statement ?? {}
      if (!courseID) {
        return
      }
      localStorage.setItem(courseID, JSON.stringify(s))
    } catch (e) {
      if ((e as any).status === 404) {
        setStatement({})
      }
    }
  }, [course_id, getMyScormCourseStatement, stepProgressId])

  useNotification()

  useEffect(() => {
    Promise.all([getState()])
  }, [getState])

  const [updateMyScormCourseStatement] = testApi.usePatchScormStateMutation()

  const getIsPassed = useCallback((): boolean => {
    if (currentStepProgress && currentStepProgress.passed === true) {
      return true
    }

    if (statement && statement.courseState && currentStep) {
      const slides = statement?.courseState.slideInfo

      return (
        slides.every((slide: any) => slide.visited === true) &&
        slides.length === currentStep.quiz_count + currentStep.slide_count + 2 &&
        statement?.courseState.quizInfo.every((q: any) => q.passed === true)
      )
    }

    return false
  }, [currentStep, currentStepProgress, statement])

  const stateRef = useRef(null)

  const storageChange = useCallback(
    (event: StorageEvent) => {
      if (!/^ispring::\{/.test(event.key as string)) return
      if (!event.newValue || !themeProgress) return

      let value

      try {
        value = JSON.parse(event.newValue)
      } catch (error) {
        console.error(t('commons:error_parsing_assign_course'))
      }

      const state = {
        courseID: event.key,
        ...value,
        courseState: {
          quizInfo: getQuizInfo(value.slideStates),
          slideInfo: getSlideInfo(value.slideStates),
          viewDuration: value.viewDuration as number,
          lastViewedSlide: value.lastViewedSlide as number,
          timestamp: Date.now(),
        },
      }
      stateRef.current = state
      const isPassed = getIsPassed()

      if (isDebounceSet || isPassed) return

      setIsDebounceSet(true)

      setTimeout(() => {
        if (!currentSection || !currentTheme || !stepId || !stepProgressId || !stateRef.current)
          return null
        setStatement(stateRef.current)
        updateMyScormCourseStatement({
          assigned_course_id: course_id,
          body: {
            section_id: currentSection,
            theme_id: currentTheme?.theme_id,
            step_id: stepId,
            step_progress_id: stepProgressId,
            statement: stateRef.current,
          },
        })
          .unwrap()
          .finally(() => setIsDebounceSet(false))
      }, TIMEOUT)
    },
    [
      isDebounceSet,
      t,
      updateMyScormCourseStatement,
      course_id,
      themeProgress,
      currentSection,
      currentTheme,
      stepId,
      stepProgressId,
      getIsPassed,
    ],
  )

  useEffect(() => {
    window.addEventListener('storage', storageChange)
    return () => {
      window.removeEventListener('storage', storageChange)
      Object.keys({ ...localStorage }).forEach(item => {
        if (/^ispring::\{/.test(item as string)) {
          localStorage.removeItem(item)
        }
      })
    }
  }, [storageChange])

  const handleNext = () => dispatch(handleNextStep())

  function processUrl(url: string): string {
    const mediaIndex = url.indexOf('/media')

    if (mediaIndex !== -1) {
      return url.substring(mediaIndex)
    } else {
      return url
    }
  }
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const { toggleFullscreen } = useFullscreen(iframeRef)

  useInjectElement({
    iframeRef,
    element: (
      <button
        onClick={e => {
          e.preventDefault()
          e.stopPropagation()
          toggleFullscreen()
        }}
        onTouchEnd={e => {
          e.preventDefault()
          e.stopPropagation()
          toggleFullscreen()
        }}
        style={{ padding: '10px' }}
        className='uikit-secondary-button uikit-secondary-button'
      >
        <svg
          width='20'
          height='20'
          viewBox='0 0 20 20'
          fill='none'
          xmlns='http://www.w3.org/2000/svg'
          className='uikit-secondary-button__left-icon'
        >
          <path
            d='M11.293 8.707C11.1055 8.51947 11.0002 8.26516 11.0002 8C11.0002 7.73484 11.1055 7.4805 11.293 7.293L14.586 4H11C10.7348 4 10.4804 3.89464 10.2929 3.70711C10.1054 3.51957 10 3.26522 10 3C10 2.73478 10.1054 2.48043 10.2929 2.29289C10.4804 2.10536 10.7348 2 11 2H17C17.2652 2 17.5196 2.10536 17.7071 2.29289C17.8946 2.48043 18 2.73478 18 3V9C18 9.26522 17.8946 9.51957 17.7071 9.70711C17.5196 9.89464 17.2652 10 17 10C16.7348 10 16.4804 9.89464 16.2929 9.70711C16.1054 9.51957 16 9.26522 16 9V5.414L12.707 8.707C12.5195 8.89447 12.2652 8.99979 12 8.99979C11.7348 8.99979 11.4805 8.89447 11.293 8.707ZM3 18H9C9.26522 18 9.51957 17.8946 9.70711 17.7071C9.89464 17.5196 10 17.2652 10 17C10 16.7348 9.89464 16.4804 9.70711 16.2929C9.51957 16.1054 9.26522 16 9 16H5.414L8.707 12.707C8.88916 12.5184 8.98995 12.2658 8.98767 12.0036C8.9854 11.7414 8.88023 11.4906 8.69482 11.3052C8.50941 11.1198 8.2586 11.0146 7.9964 11.0123C7.7342 11.01 7.4816 11.1108 7.293 11.293L4 14.586V11C4 10.7348 3.89464 10.4804 3.70711 10.2929C3.51957 10.1054 3.26522 10 3 10C2.73478 10 2.48043 10.1054 2.29289 10.2929C2.10536 10.4804 2 10.7348 2 11V17C2 17.2652 2.10536 17.5196 2.29289 17.7071C2.48043 17.8946 2.73478 18 3 18Z'
            fill='currentColor'
          ></path>
        </svg>
      </button>
    ),
    options: {},
  })

  return (
    <div className={cx('iframeWrapper')} key={stepId}>
      {path && statement && (
        <>
          <iframe
            allowFullScreen
            ref={iframeRef}
            width='100%'
            height='100%'
            title={t('commons:course')}
            // !TODO: не отображается потому что сейчас ссылка неправильная подставляется
            // src={`https://edu.sec-t.ru/media/scorm/temp_yGRMRw/res/index.html`}
            src={processUrl(path)}
            sandbox='allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-downloads'
          />
          {isLastStep ? (
            <CompleteModal />
          ) : (
            <Button onClick={handleNext} size='big' color='green' className={cx('button')}>
              {t('commons:next')}
            </Button>
          )}
        </>
      )}
    </div>
  )
}
