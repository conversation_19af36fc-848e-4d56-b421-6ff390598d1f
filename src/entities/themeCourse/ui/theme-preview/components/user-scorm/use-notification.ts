import { useNotification as useNotificationHook } from '@/shared/contexts/notifications'
import { isNativeFullscreenSupported } from '@/shared/hooks/use-fullscreen'
import { useEffect } from 'react'
import { useTranslation } from 'react-i18next'

const LS_KEY = 'user-scorm-iphone-notication'
const ONE_DAY_IN_MS = 24 * 60 * 60

export const useNotification = () => {
  const { t } = useTranslation()
  const { add } = useNotificationHook()

  useEffect(() => {
    const isNativeFS = isNativeFullscreenSupported()

    if (isNativeFS) return

    const lastShownTime = localStorage.getItem(LS_KEY)

    if (lastShownTime) {
      const lastShownDate = new Date(Number(lastShownTime)).getTime()
      const currentTime = new Date().getTime()
      const isLessThanOneDayAgo = currentTime - lastShownDate < ONE_DAY_IN_MS

      if (isLessThanOneDayAgo) return
    }

    add({
      id: crypto.randomUUID(),
      message: t('commons:device_not_support_all_features'),
      status: 'warning',
    })

    localStorage.setItem(LS_KEY, new Date().getTime().toString())
  }, [add, t])
}
