import React, { ReactNode, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import { useMediaQuery } from 'usehooks-ts'

interface UseInjectElementOptions {
  position?: {
    top?: string | number
    left?: string | number
    right?: string | number
    bottom?: string | number
  }
  containerStyle?: React.CSSProperties
}

type UseInjectElementProps = {
  iframeRef: React.RefObject<HTMLIFrameElement>
  element: ReactNode
  options: UseInjectElementOptions
}

const useInjectElement = ({ element, iframeRef, options = {} }: UseInjectElementProps) => {
  const [isReady] = useState(false)
  const isMobile = useMediaQuery('(max-width: 1024px)')

  useEffect(() => {
    const iframe = iframeRef?.current
    if (!iframe) return

    const handleLoad = () => {
      if (!isMobile) return
      const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
      if (!iframeDoc) return
      if (!element) return

      const [body] = iframeDoc.querySelectorAll('body')

      const container: HTMLElement =
        iframeDoc.querySelector('#scorm-custom-full-screen') || document.createElement('div')
      const containerLandscape: HTMLElement =
        iframeDoc.querySelector('#scorm-custom-full-screen-landscape') ||
        document.createElement('div')
      const defaultContainer: HTMLElement =
        iframeDoc.querySelector('#scorm-custom-default-container') || document.createElement('div')

      const [bottomPanel] = body.querySelectorAll('.bottom-panel')
      const [landscape] = body.querySelectorAll(
        '.landscape-bottom-panel',
      ) as unknown as HTMLElement[]

      container.innerHTML = ''
      containerLandscape.innerHTML = ''
      defaultContainer.innerHTML = ''

      container.setAttribute('id', 'scorm-custom-full-screen')
      containerLandscape.setAttribute('id', 'scorm-custom-full-screen-landscape')
      defaultContainer.setAttribute('id', 'scorm-custom-default-container')

      defaultContainer.style.position = 'absolute'
      defaultContainer.style.top = options.position?.top?.toString() || '8px'
      defaultContainer.style.left = options.position?.left?.toString() || '8px'
      defaultContainer.style.zIndex = '9999'
      Object.assign(defaultContainer.style, options.containerStyle)

      let hasInjected = false

      if (bottomPanel) {
        container.style.marginLeft = '10px'
        const children = bottomPanel.children
        if (children.length >= 2) {
          bottomPanel.insertBefore(container, children[2])
        } else {
          bottomPanel.appendChild(container) // если меньше 3, добавим в конец
        }
        const renderer = createRoot(container)
        renderer.render(element)
        hasInjected = true
      }
      if (landscape) {
        landscape.style.overflowY = 'scroll'
        containerLandscape.style.marginBottom = '10px'
        const children = landscape.children
        if (children.length >= 3) {
          landscape.insertBefore(containerLandscape, children[3])
        } else {
          landscape.appendChild(containerLandscape) // если меньше 4, добавим в конец
        }
        const renderer = createRoot(containerLandscape)
        renderer.render(element)
        hasInjected = true
      }

      if (!hasInjected) {
        body.appendChild(defaultContainer)
        const renderer = createRoot(defaultContainer)
        renderer.render(element)
      }
    }

    iframe.addEventListener('load', handleLoad)

    if (iframe.contentDocument?.readyState === 'complete') handleLoad()

    return () => {
      iframe.removeEventListener('load', handleLoad)
    }
  }, [iframeRef, options, element, isMobile])

  return {
    isReady,
  }
}

export default useInjectElement
