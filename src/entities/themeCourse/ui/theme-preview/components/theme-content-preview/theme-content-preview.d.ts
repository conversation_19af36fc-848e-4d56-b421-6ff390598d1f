import { SetStateAction } from 'react';
import { TMinifiedStepType } from '../steps-toolbar-preview'
import { TTheme  } from '../../../../model/types';

export declare namespace ThemeContentPreviewProps {
    interface Own {
        data?: DataType;
        setData: React.Dispatch<SetStateAction<DataType | undefined>>;
        type?: TMinifiedStepType;
        stepId?: string;
        themeData?: TTheme;
        isNewStep?: boolean;
        isUser?: boolean;
        wrapperClassName?: string
    }
  
    type Props = Own;
  }
  
  export {};
  