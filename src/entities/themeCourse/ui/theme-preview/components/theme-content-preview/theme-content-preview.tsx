import { ThemeContentPreviewProps } from './theme-content-preview.d'
import React, { useMemo } from 'react'
import {
  getEditorJsTools,
  getEditorPresentationTools,
  getEditorVideoTools,
} from '@/shared/configs/tools'
import { ManageQuestionsForm } from '@/features/manage-questions'
import styles from './theme-content-preview.module.scss'
import classNamesBind from 'classnames/bind'
import { useTranslation } from 'react-i18next'
import { Editor } from '@/shared/components/editor'
import { UserQuiz } from '../user-quiz'
import { UserScorm } from '../user-scorm/user-scorm'
import {
  ThemeContentControlButtonsProvider,
  useThemeContentControlButtons,
} from '@/shared/components/theme-content/theme-content-context'

const cx = classNamesBind.bind(styles)

const Article: React.FC<ThemeContentPreviewProps.Props> = props => {
  const { data, setData, stepId, isUser } = props

  if (!data) return null

  return (
    <Editor
      data={data}
      setData={setData}
      //@ts-expect-error TODO: Write type for custom tools (Type - Tools)
      tools={getEditorJsTools(true)}
      stepId={stepId}
      readOnly={true}
      isUserSide={isUser}
    />
  )
}

const Presentation: React.FC<ThemeContentPreviewProps.Props> = props => {
  const { data, setData, stepId, isUser } = props

  if (!data) return null

  return (
    <Editor
      data={data}
      setData={setData}
      //@ts-expect-error TODO: Write type for custom tools (Type - Tools)
      tools={getEditorPresentationTools(true)}
      stepId={stepId}
      readOnly={true}
      isUserSide={isUser}
    />
  )
}

const Video: React.FC<ThemeContentPreviewProps.Props> = props => {
  const { data, setData, stepId, isUser } = props

  if (!data) return null

  return (
    <Editor
      data={data}
      setData={setData}
      //@ts-expect-error TODO: Write type for custom tools (Type - Tools)
      tools={getEditorVideoTools(true)}
      stepId={stepId}
      readOnly={true}
      isUserSide={isUser}
    />
  )
}

const Scorm: React.FC<ThemeContentPreviewProps.Props> = props => {
  const { data, stepId } = props
  const { t } = useTranslation('pages__create-theme')

  if (!data) return null

  return (
    <div className={cx('iframeWrapper')} key={stepId}>
      <iframe
        allowFullScreen
        width='100%'
        height='100%'
        title={t('commons:course')}
        src={`${data.path}`}
        sandbox='allow-storage-access-by-user-activation allow-scripts allow-same-origin allow-popups allow-downloads'
      />
    </div>
  )
}

export const ThemeContentPreview: React.FC<ThemeContentPreviewProps.Props> = props => {
  const { data, type, stepId, isUser, themeData } = props

  const steps = {
    article: <Article {...props} />,
    slide: <Presentation {...props} />,
    video: <Video {...props} />,
    quiz: isUser ? (
      stepId ? (
        <UserQuiz stepId={stepId} />
      ) : null
    ) : !data || !themeData ? null : (
      <ManageQuestionsForm
        initialState={data}
        stepId={stepId}
        readonly={true}
        themeId={themeData?.id}
      />
    ),
    import: null,
    scorm: isUser ? <UserScorm path={data?.path ?? ''} stepId={stepId} /> : <Scorm {...props} />,
  }

  return type ? (
    <ThemeContentControlButtonsProvider>
      <ThemeContentPreviewInner {...props} steps={steps} />
    </ThemeContentControlButtonsProvider>
  ) : null
}

const ThemeContentPreviewInner: React.FC<
  ThemeContentPreviewProps.Props & { steps: Record<string, React.ReactNode> }
> = props => {
  const { type, stepId, steps } = props
  const { controlButtons } = useThemeContentControlButtons()

  const controlButtonsArray = useMemo(
    () =>
      Object.entries(controlButtons).map(([key, button]) =>
        React.cloneElement(button as React.ReactElement, { key }),
      ),
    [controlButtons],
  )

  return (
    <div className={cx('wrapper', props?.wrapperClassName)} key={stepId}>
      <div className={cx('controlButtonsContainer')}>{controlButtonsArray}</div>
      {steps[type!]}
    </div>
  )
}
