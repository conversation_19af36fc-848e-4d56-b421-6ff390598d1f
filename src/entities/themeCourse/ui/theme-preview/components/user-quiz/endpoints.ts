import { GlobalEndpointBuilder, globalBaseApi } from '@/store/services/endpoints/base';
import { AnswerQuestionRequest, CompleteQuizRequest, CreateAttemptRequest, QuizResponse } from './types';

export const getQuizEndpoints = () => {
  return (build: GlobalEndpointBuilder) => ({
    answerQuestion: build.mutation<QuizResponse, { assigned_course_id: UUID, body: AnswerQuestionRequest }>({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/quiz/answer`,
        method: 'POST',
        body
      }),
      invalidatesTags: ['quiz'],
    }),
    completeQuiz: build.mutation<QuizResponse, { assigned_course_id: UUID; body: CompleteQuizRequest }>({
        query: ({ assigned_course_id, body }) => ({
          url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/quiz/attempt/complete`,
          method: 'POST',
          body
        }),
        invalidatesTags: ['quiz'],
    }),
    createQuizAttempt: build.mutation<QuizResponse, { assigned_course_id: UUID; body: CreateAttemptRequest }>({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/quiz/attempt/create`,
        method: 'POST',
        body
      }),
      invalidatesTags: ['quiz'],
  }),
  })
}

export const userQuizApi = globalBaseApi.injectEndpoints({
    endpoints: builder => getQuizEndpoints()(builder),
  })
  