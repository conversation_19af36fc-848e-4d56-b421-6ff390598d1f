export declare namespace UserQuizProps {
  interface Own {
    stepId: string
  }

  type Props = {
    stepId?: string
    progressId?: UUID
    courseId: UUID
    sectionId: UUID
    themeId?: UUID
    currentData?: QuizResponse
    isStarted?: boolean
    isQuizStarted?: boolean
    isLoading?: boolean
    setProgressId: React.Dispatch<React.SetStateAction<UUID | undefined>>
    getState: () => void
    refetch: () => void
    newAttempt: () => Promise<void>
    setCurrentData: React.Dispatch<React.SetStateAction<QuizResponse | undefined>>
    setIsStarted: React.Dispatch<React.SetStateAction<boolean>>
    setForceShowResult?: React.Dispatch<React.SetStateAction<boolean>>
  }
}

export type Answers = {
  [key: string]: UUID[]
}

export {}
