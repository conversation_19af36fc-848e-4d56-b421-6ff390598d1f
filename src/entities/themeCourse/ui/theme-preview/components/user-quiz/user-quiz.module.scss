.start {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  margin-top: 24px;
}

.welcomeText {
  font: var(--font-text-1-normal);
}

.buttons {
  display: flex;
  gap: 12px;
}

.noData {
  text-align: center;
  font: var(--font-text-1-normal);
  margin-top: 24px;
}

.cardsWrapper {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.finishQuizButton {
  margin: 16px 0 0 auto;

  @media (max-width: 1024px) {
    margin: 16px auto 0;
  }
}

.loader {
  height: 300px;
}
