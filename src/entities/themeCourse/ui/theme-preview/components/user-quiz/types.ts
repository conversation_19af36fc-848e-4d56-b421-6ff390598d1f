import { TQuizStepType, TSettings } from '@/entities/themeCourse/model/types'

export interface CreateAttemptRequest {
  section_id: UUID
  theme_id: UUID
  step_id: UUID
  step_progress_id?: UUID
}

export interface CompleteQuizRequest extends CreateAttemptRequest {
  attempt_id: UUID
}

export interface AnswerQuestionRequest extends CompleteQuizRequest {
  questions: {
    question_id: UUID
    answers_ids: UUID[]
  }[]
}

type TAnswer = {
  id: UUID
  text: string
  is_correct: boolean
  is_selected: boolean
  tip: string
}

export type TQuestion = {
  id: UUID
  text: string
  passed: boolean
  multiple_answers: boolean
  answers: TAnswer[]
}

type TAttempt = {
  id: UUID
  started_at: string
  finished_at: string
  passed: boolean
  questions: TQuestion[]
}

export interface QuizResponse {
  id: UUID
  type: TQuizStepType
  started_at: string
  finished_at: string
  step_id: UUID
  passed: boolean
  attempts: TAttempt[]
  settings: TSettings
}
