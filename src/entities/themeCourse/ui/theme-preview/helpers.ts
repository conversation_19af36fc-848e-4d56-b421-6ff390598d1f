/* eslint-disable no-case-declarations */
import { Step } from './components/steps-toolbar-preview'
import {
  transformArticleData,
  transformPresentationData,
  transformVideoData,
} from '@/shared/components/theme-content/helper'
import {
  TArticleStep,
  TQuizStep,
  TScormStep,
  TSlideStep,
  TStep,
  TTheme,
  TVideoStep,
} from '@/entities/themeCourse/model/types'
import { Dispatch } from '@reduxjs/toolkit'
import { setData } from '@/store/slices/user-course-slice'

export const handleActiveData = ({
  activeStep,
  stepData,
  dispatch,
  themeData,
}: {
  activeStep?: Step
  stepData?: TStep
  themeData?: TTheme
  dispatch: Dispatch
}) => {
  const step = stepData ?? themeData?.steps.find((s: TStep) => s?.id === activeStep?.id)

  if (step) {
    switch (step.type) {
      case 'slide':
        const slideData = transformPresentationData(step as TSlideStep)
        dispatch(setData(slideData))
        break
      case 'video':
        const videoData = transformVideoData(step as TVideoStep)
        dispatch(setData(videoData))
        break
      case 'quiz':
        dispatch(
          setData({
            questions: (step as TQuizStep).questions,
            settings: (step as TQuizStep).settings,
          }),
        )
        break
      case 'article':
        const articleData = transformArticleData(step as TArticleStep)
        dispatch(setData(articleData))
        break
      case 'scorm':
        dispatch(
          setData({
            path: (step as TScormStep).path,
          }),
        )
    }
  } else {
    dispatch(setData(undefined))
  }
}
