
// @use "../../../../shared/assets/styles/mixins/text";

.wrapper {
  align-items: center;
  background: var(--color-surface);
  display: flex;
  flex-direction: column;

  border-radius: 16px;
  border: 1px solid var(--color-gray-30);
}

.history__item {
  display: flex;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;

  &__image{
    width: 32px;
    height: 32px;
    border-radius: 10000px;
    object-fit: cover;
  }

  &__content {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }

  &__description {
    display: flex;
    flex-direction: column;
    gap: 4px;
  }

  &__time {
    font: var(--font-text-2-normal);
    color: var(--color-gray-80);
  }

  &__title {
    font: var(--font-text-1-medium);
    color: var(--color-gray-90);
  }

  &__types {
    display: flex;
    gap: 4px;
    font: var(--font-caption-2-medium);
    font-weight: 300;

    &_warning {
      color: var(--snackbars-warning);
    }
    
    &_error {
      color: var(--snackbars-error);
    }

    &_success {
      color: var(--snackbars-success);
    }

    &_neutral {
      color: var(--snackbars-neutral);
    }
  }

  border-bottom: 1px solid var(--color-gray-30);

  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;

  &:first-child {
    border-radius: 0;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
  }

  &:last-child {
    border-bottom: none;
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
  }

  &:hover {
    background-color: var(--color-gray-20);
    transition: var(--transition);
  }

  cursor: pointer;
}