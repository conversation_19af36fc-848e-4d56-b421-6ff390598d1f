import { FC } from 'react'
import {
  PhishingEvent,
  PhishingEventType,
  PhishingHistoryItem as TPhishingHistoryItem,
} from './types'
import classNamesBind from 'classnames/bind'
import styles from './styles.module.scss'

const cx = classNamesBind.bind(styles)

const transformPhishingEventToHistoryItem = (event: PhishingEvent): TPhishingHistoryItem => ({
  ...event,
  description: (
    <div className={cx('history__item__content')}>
      <div className={cx('history__item__description')}>
        <h3 className={cx('history__item__title')}>{event.action}</h3>
        <PhishingEventsTypes types={event.types} />
      </div>
      <time className={cx('history__item__time')}>{event.time}</time>
    </div>
  ),
})

export const PhishingHistoryItem: FC<{ event: TPhishingHistoryItem }> = ({ event }) => {
  return (
    <li className={cx('history__item')}>
      <img className={cx('history__item__image')} src={event.photoSrc} />
      {event.description}
    </li>
  )
}

export const PhishingEventsTypes: FC<{ types: PhishingEventType[] }> = ({ types }) => {
  return (
    <ul className={cx('history__item__types')}>
      {types.map((type, index) => (
        <li key={index} className={cx(`history__item__types_${type.type}`)}>
          {type.name}
        </li>
      ))}
    </ul>
  )
}

type PhishingHistoryProps = {
  events?: PhishingEvent[]
}
export const PhishingHistory: FC<PhishingHistoryProps> = ({ events }) => {
  if (!events || !events?.length) return null

  return (
    <ul className={cx('wrapper')}>
      {events?.map(event => (
        <PhishingHistoryItem key={event?.id} event={transformPhishingEventToHistoryItem(event)} />
      ))}
    </ul>
  )
}
