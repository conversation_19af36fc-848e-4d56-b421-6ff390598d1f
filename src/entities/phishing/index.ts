export { phishingMutations, phishingQueries } from './model/api'
export {
  type IPhishingCampaignTemplate,
  type IPhishingCampaignStatisticsDepartment,
  type IPhishingCampaignStatisticsUser,
  type IPhishing<PERSON>apmaign,
  type IPhishingStatus,
  type <PERSON>hishingCapmaignsStatusesResponse,
  type Attachment,
  type IPhishingEmailForm,
  type IPhishingTemplate,
  type IPhishingTemplateByCategory,
  type IPhishingCampaignCourse,
  type IPhishingCreateTemplatesRequest,
  type IEmail,
  type RedirectPage,
  EAttachments,
  type SortBy,
  type SortOrder,
} from './model/types'

export { PhishingHistory } from './views/cards/list'
export { transformDateToTime } from './views/cards/list/transformers'
export {
  type PhishingEvent,
  type PhishingEventType,
  type PhishingHistoryItem,
} from './views/cards/list/types'