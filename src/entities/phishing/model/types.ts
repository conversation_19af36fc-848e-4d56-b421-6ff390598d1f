import {
  ECourseProgress,
  EIncidentRiskLevel,
  ELearning,
  EPhishingCampaignsStatus,
  EPhishingEvent,
  ERole,
  ETarget,
  IAttackVectors,
} from '@/shared/types/enums'

export type CampaignEventType =
  | 'sent'
  | 'opened'
  | 'entered_data'
  | 'clicked'
  | 'opened_attachment'
  | 'unknown'

export interface IPhishingCapmaignWithData extends ResponseWithPagination {
  data: IPhishingCapmaign[]
}

export interface IPhishingCampaign {
  id: UUID
  start_date: string
  end_date: string
  status: string
  statistic: {
    campaign_created: number
    unknown: number
    incident_risk: number
    incident_risk_level: string
    sent: number
    opened: number
    clicked: number
    entered_data: number
    opened_attachment: number
  }
}

export interface IPhishingCampaignCourse {
  id: UUID
  course_id: UUID
  title: string
  image: string
  new_assigned_course_id: UUID
  targets: Array<'opened' | 'entered_data' | 'clicked' | 'opened_attachment'>
  users_count: number
  start_date: string
  end_date: string
}

type Targets = 'opened' | 'clicked' | 'entered_data' | 'opened_attachment'

export interface IPhishingCampaignInternal {
  notification: {
    targets: Targets[]
    subject: string
    text: string
  }
  post_campaign: {
    id: UUID
    name: string
    targets: Targets[]
    emails_templates: UUID[]
  }
  redirect_page: {
    id: UUID
    name: string
    html: string
  }
  enable_redirect: boolean
}

export interface IPhishingCapmaign {
  id: UUID
  created_at: string
  updated_at: string
  name: string
  start_date: string
  end_date: string
  by_tag?: boolean
  email_send_end_date: string
  status: EPhishingCampaignsStatus
  is_testing: boolean
  is_autophish: boolean
  statistics: {
    incident_risk: number
    incident_risk_level: EIncidentRiskLevel
    sent: number
    opened: number
    clicked: number
    entered_data: number
    opened_attachment: number
  }
  attack_vectors: IAttackVectors[]
  users_count: number
  settings: Settings
  course: {
    id: UUID
    title: string
    image: string
    modules_count: number
    assigned_id: UUID
    old_assigned_id: number
    target: ETarget
    users_count: number
    start_date: string
    end_date: string
  }
  notification: PhishingNotification
}

export type Settings = {
  id: string
  display_course_link: boolean
  auto_course: AutoCourse
  post_campaign: PostCampaign
  notification: Notification
}

export type AutoCourse = {
  id: string
  title: string
  image: string
  modules_count: number
  assigned_id: string
  old_assigned_id: number
  target: string
  users_count: number
  start_date: string
  end_date: string
}

export type PostCampaign = {
  targets: string[]
  name: string
  emails_templates: string[]
  is_testing: boolean
  settings: PostCampaignSettings
  enable_redirect: boolean
  exclude_users_by_tags_ids: string[]
}

export type PostCampaignSettings = {
  display_course_link: boolean
  auto_course: {
    target: string[]
    course_id: string
    period: number
  }
  notification: Notification
}

export type PhishingNotification = {
  id: UUID
  target: ETarget
  subject: string
  text: string
}

export enum EAttachments {
  pdf = 'pdf',
  docx = 'docx',
  xlsx = 'xlsx',
}

export interface Attachment {
  id: UUID
  created_at: string
  updated_at: string
  filename: string
  type: EAttachments
}

export interface IPhishingLandingPage {
  id: string
  created_at: string
  updated_at: string
  html: string
}

export interface IEmail {
  id: UUID
  name: string
  sender: string
  created_at: string
  updated_at: string
  subject: string
  html: string
  attachments: Attachment[]
}

export interface IPhishingEmailForm
  extends PartialFields<IEmail, 'updated_at' | 'created_at' | 'id'> {
  mail: string
  mail_postfix: string
  is_track: boolean
  template_id: string
}

export interface IPhishingTemplateWithData extends ResponseWithPagination {
  data: IPhishingTemplate[]
}

export interface IPhishingTemplate {
  id: UUID
  created_at: string
  updated_at: string
  can_read: boolean
  can_edit: boolean
  can_delete: boolean
  name: string
  url: string
  original_url: string
  logo: string
  emails: Omit<IEmail, 'created_at' | 'updated_at' | 'subject' | 'html'>[]
  category: Nullable<{
    id: UUID
    created_at: string
    updated_at: Nullable<string>
    name: string
  }>
  page: {
    id: UUID
  }
}

export interface IPhishingTemplateByCategory {
  id: UUID | null
  category: TemplateCategory | null
  templates: IPhishingTemplate[]
}

export interface IPhishingDomainsResponse {
  domains: string[]
}

export interface TemplateCategory extends BaseModel {
  name: string
}

export interface BaseModel {
  id: UUID
  created_at: string
  updated_at: string | null
}

export interface IPhishingCategoriesReponse extends ResponseWithPagination {
  data: TemplateCategory[]
}

export interface IPhishingDomainsResponse {
  domains: string[]
}

export interface IPhishingCreateTemplatesRequest {
  name: string
  original_url: Nullable<string>
  url: string
  category: Nullable<string>
}

export interface ICreatePhishingCampaignBody {
  name: string
  start_date: string
  end_date: string
  emails_templates: string[]
  is_testing: boolean
  targets: {
    organizations: string[]
    departments: string[]
    employees: string[]
  }
  course: {
    target: string
    course_id: string
    period: number
  }
  notification: PhishingNotification
  display_course_link: boolean
  enable_redirect: boolean
  exclude_users_by_tags_ids: string[]
}
export interface ICreatePhishingCampaignByUsersParams {
  need_all: boolean
  search: string
  in_phishing?: string[]
  phishing_events?: EPhishingEvent[]
  position?: string[]
  roles?: ERole | null
  departments?: string[]
  tags?: string[]
  in_course?: string[]
  learning?: ELearning[]
  course_progress?: ECourseProgress[]
  risk_level_from: number
  risk_level_to: number
  include_ids: string[]
  exclude_ids: string[]
}

export interface IAuthphishResponse {
  enabled: boolean
}

export type IAuthphishUpdateRequest = IAuthphishResponse
export type IAuthphishUpdateResponse = IAuthphishUpdateRequest

export interface IPhishingCampaignStatisticsUser {
  id: string
  full_name: string
  position: string
  email: string
  department_name: string
  phishing_result: IPhishingCampaignStatisticsUserPhishingResult
}

export interface IPhishingCampaignStatisticsUserPhishingResult {
  os: string
  browser: string
  message: string
  status: CampaignEventType
  created_at: string
}

export interface IIPhishingCampaignStatisticsUserResponse extends ResponseWithPagination {
  data: IPhishingCampaignStatisticsUser[]
}

export interface IPhishingCampaignStatisticsDepartment {
  title: string
  users_count: number
  popular_template_name: string
  incident_risk: number
  incident_risk_level: string
}

export interface IPhishingCampaignStatisticsDepartmentResponse extends ResponseWithPagination {
  data: IPhishingCampaignStatisticsDepartment[]
}

export interface IPhishingCapmaignsStatusesResponse {
  active: number
  completed: number
  planned: number
}

export type SortBy = 'START_DATE' | 'END_DATE' | 'TARGETS' | 'INDICATOR'

export type SortOrder = 'asc' | 'desc'

export type IPhishingStatus = keyof IPhishingCapmaignsStatusesResponse

export type PhishingCampaignsFilters = {
  search?: string | null
  status?: IPhishingStatus | null
  organization_id?: string | null
  by_tag: boolean
  sort_by?: SortBy
  sort_order?: SortOrder

  start_date_from?: string | null
  start_date_to?: string | null
  end_date_from?: string | null
  end_date_to?: string | null

  incident_percent_from?: number | null
  incident_percent_to?: number | null

  targets_from?: number | null
  targets_to?: number | null

  templates_ids?: string[]
  phishing_events?: string[]
}

export interface IPhishingCampaignRequest extends RequestWithPagination {
  status?: IPhishingStatus | null
  organization_id?: UUID
  filters?: PhishingCampaignsFilters
}

export interface IPhishingCampaignResult {
  id: string
  name: string
  url: string
  original_url: string
  logo: string
  category: IPhishingCampaignResultCategory
  email: IPhishingCampaignResultEmailTemplate
  page: IPhishingCampaignResultPage
  users_count: number
  statistics: IPhishingCampaignResultStatistics
}

export interface IPhishingCampaignResultCategory {
  id: string
  created_at: string
  updated_at: string
  name: string
}

export interface IPhishingCampaignResultEmailTemplate {
  id: string
  name: string
  sender: string
  attachments: Attachment[]
}

export interface IPhishingCampaignResultAttachment {
  id: string
  created_at: string
  updated_at: string
  filename: string
  type: string
}

export interface IPhishingCampaignResultPage {
  id: string
}

export interface IPhishingCampaignResultStatistics {
  incident_risk: number
  incident_risk_level: string
  sent: number
  opened: number
  clicked: number
  entered_data: number
  opened_attachment: number
}

export interface IPhishingCampaignTemplatesResponse {
  data: IPhishingCampaignTemplate[]
  total_count: number
  limit: number
  offset: number
}

export interface IPhishingCampaignTemplate {
  id: string
  name: string
  url: string
  original_url: string
  logo: string
  category: IPhishingCampaignCategory
  email: IPhishingCampaignEmailTemplate
  page: Page
  users_count: number
  statistics: Statistics
}

export interface IPhishingCampaignCategory {
  id: string
  created_at: string
  updated_at: string
  name: string
}

export interface IPhishingCampaignEmailTemplate {
  id: string
  name: string
  sender: string
  attachments: IPhishingCampaignAttachment[]
}

export interface IPhishingCampaignAttachment {
  id: string
  created_at: string
  updated_at: string
  filename: string
  type: string
}

export interface Page {
  id: string
}

export interface Statistics {
  incident_risk: number
  incident_risk_level: string
  sent: number
  opened: number
  clicked: number
  entered_data: number
  opened_attachment: number
}

export interface Report {
  id: string
  url: string
  status: string
}

export interface IPhishingStatisticsChart {
  opened: number
  clicked: number
  entered_data: number
  opened_attachment: number
  efficiency: number
  attack_vectors: unknown[]
}

export interface AutophishingTemplatesResponse {
  id: string
  tag_id: string
  enabled: boolean
  campaign_id: string
  created_at: string
  templates_ids: UUID[]
  defer_by: number
}

export interface AutophishPhishingTemplate {
  uuid: string
  template_name: string
  page_name: string
}

export interface RedirectPage {
  id?: string
  name: string
  html: string
  created_at?: string
  updated_at?: string
  can_read?: boolean
  can_edit?: boolean
  can_delete?: boolean
}
