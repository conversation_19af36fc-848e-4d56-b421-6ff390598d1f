import {
  IActionLogAuditRequest,
  IActionLogRequest,
  IActionLogsGenerateReportResponse,
  IActionLogsGetReportRequest,
  IActionLogsGetReportResponse,
  IActionLogsResponse,
} from '@/shared/types/store/settings'
import { globalBaseApi } from '@/store/services/endpoints/base'

const PREFIX_URL = '/lk/api/v2/'

export const actionLogApi = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getActiosByType: build.query<IActionLogsResponse, IActionLogRequest>({
      query: requestParams => ({
        url: `${PREFIX_URL}audit/logs`,
        params: requestParams,
      }),
    }),
    generateReportByTypes: build.mutation<
      IActionLogsGenerateReportResponse,
      IActionLogAuditRequest
    >({
      query: params => ({
        url: `${PREFIX_URL}reports/audit`,
        method: 'POST',
        params,
      }),
    }),
    getReportById: build.query<
      ResponseWithNotification<IActionLogsGetReportResponse>,
      IActionLogsGetReportRequest
    >({
      query: ({ id }) => ({
        url: `${PREFIX_URL}reports/` + id,
        method: 'GET',
      }),
      transformResponse(response: IActionLogsGetReportResponse) {
        if (response.status === 'complete' && response.url) {
          return {
            ...response,
            notificationTip: 'tips:report.success',
          }
        }

        return response
      },
    }),
  }),
})
