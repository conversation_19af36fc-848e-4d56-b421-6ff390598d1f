export type CoursesResponse = ResponseWithPagination & {
  data: Course[]
}

export type SharedCoursesResponse = ResponseWithPagination & {
  data: SharedCourse[]
}

export type MySharedCoursesResponse = ResponseWithPagination & {
  data: MySharedCourse[]
}

export interface MySharedCourse {
  id: UUID
  assigned_course: {
    id: UUID
    title: string
    description: string
    organization_id: UUID
    picture: string
    created_at: string
    start_date: string
    end_date: string
    status: string
    themes_count: number
    finished_at: string
  }
  statistics: {
    theory: number
    quiz: number
    overall: number
  }
}
export interface SharedCourse {
  id: UUID
  assigned_course: {
    id: UUID
    title: string
    description: string
    organization_id: UUID
    picture: string
    visibility: string
    created_at: string
    start_date: string
    end_date: string
    status: string
    employees_count: 0
    need_assigned_message: true
    need_notify_message: true
  }
}

export type CoursesSortBy = 'title' | 'created_at'
export type CoursesSortOrder = 'asc' | 'desc'

export type CoursesRequest = RequestWithPagination & {
  search?: string
  tags?: string[]
  organization_id: string
  sort_by?: CoursesSortBy
  sort_order?: CoursesSortOrder
}

export type SharedCoursesRequest = RequestWithPagination & {
  search?: string
  organization_id: string
  status?: string
}

export type ShareCourseResponse = {
  id: UUID
  ref_course_id: UUID
  assigned_course_id: UUID
}

export type MySharedCoursesRequest = RequestWithPagination & {
  status?: string
}

export type ShareCourseRequest = {
  course_id: UUID
  title: string
  description: string
  need_assigned_message: boolean
}

export type CompleteSharedCourseRequest = {
  shared_id: UUID
}

export type CourseByIdRequest = {
  course_id: string
}

export type CourseByIdResponse = Course & {
  sections: CourseSection[]
}

export type CourseSection = {
  id: string
  title: string
  description: string
  order_id: number
  themes: CourseTheme[]
}

export type CourseTheme = {
  id: string
  title: string
  organization_id: string
  visibility: string
  order_id: number
}

export type MyCourse = {
  id: string
  title: string
  description: string
  organization_id: string
  picture: string
  created_at: string
  finished_at?: string
  start_date: string
  end_date: string
  themes_count: number
  status: string
  tags?: Array<string | null>
}

export type CourseStatistics = {
  theory: number
  quiz: number
  overall: number
}

export type MyCoursesData = {
  assigned_course: MyCourse
  statistics: CourseStatistics
}

export type MyCoursesResponse = ResponseWithPagination & {
  data: MyCoursesData[]
}

export type MyCoursesStatus = 'active' | 'completed' | 'shared'

export type MyCoursesRequest = RequestWithPagination & { status?: MyCoursesStatus }

export interface Course {
  id: string
  title: string
  description: string | null
  organization_id: string
  visibility: string
  archived: boolean
  available_in_demo: boolean
  created_at: string
  image_path: string
  tags?: string[]
  lock_sequence: boolean
  can_edit: boolean
  can_delete: boolean
  themes_count: number
}

export type AssignCoursesRequest = {
  courses: CourseForAssign[]
  need_assigned_message: boolean
  need_notify_message: boolean
  start_date: string
  period: number
  targets: Targets
}

export type AssignCourseRequest = CourseForAssign & {
  need_assigned_message: boolean
  need_notify_message: boolean
  start_date: string
  period: number
  targets: Targets
}

export type AssignCoursesRequestWithParams = {
  body: AssignCoursesRequest
  params?: AssignCoursesByFilteredUsersParams
}

export type AssignCourseRequestWithParams = {
  body: AssignCourseRequest
  params?: AssignCoursesByFilteredUsersParams
}

export type AssignCourseAttachPictureRequest = {
  assigned_course_id: string
  image: File
}

export type ReinviteCourseRequest = {
  assigned_course_id: string
  users_ids: string[]
  need_all: boolean
  exclude_users_ids: string[]
  progress_groups?: ('behind' | 'normal')[]
  process_groups?: ('completed' | 'in_process' | 'not_started')[]
  is_not_registered?: boolean
  newbie?: boolean
}

export type CourseForAssign = {
  course_id: string
  title: string
  description: string
}

export type Targets = {
  users: string[]
  departments: string[]
  exclude_users_ids: string[]
}

export type TargetsWithAllSelected = Targets & {
  isAllSelected: boolean
}

export type ManualCompleteCourseRequest = {
  assigned_course_id: string
  users_ids: string[]
  exclude_users_ids: string[]
  need_all: boolean
}

export type AssignCoursesByFilteredUsersParams = {
  need_all: boolean
  search?: string
  in_phishing?: string[]
  phishing_events?: string[]
  roles?: string
  departments?: string[]
  risk_level_from?: number
  risk_level_to?: number
  tags?: string[]
  in_course?: string[]
  course_progress?: string[]
  learning?: string[]
  include_ids: string[]
  exclude_ids: string[]
}

export type AssignCoursesRequestWithFilters = {
  courses: CourseForAssign[]
  need_assigned_message: boolean
  need_notify_message: boolean
  start_date: string
  period: number
}

export type AssignCourseRequestWithFilters = CourseForAssign & {
  need_assigned_message: boolean
  need_notify_message: boolean
  start_date: string
  period: number
}
