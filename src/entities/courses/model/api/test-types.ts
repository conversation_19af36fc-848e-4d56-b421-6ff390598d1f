export type AnswerQuizResponse = {
  id: string
  type: string
  started_at: string
  finished_at: string
  step_id: string
  passed: boolean
  attempts: Attempt[]
  settings: Settings
}

export type AnswerQuizRequestBody = {
  section_id: string
  theme_id: string
  step_id: string
  attempt_id: string
  question_id: string
  step_progress_id: string
  answer_ids: string[]
}

export type UserPassedStatResponse = {
  quizes: {
    passed: number
    total: number
  }
  courses: {
    passed: number
    total: number
  }
}

export type AssignedCourseByUser = {
  assigned_course_id: string
  theory: number
  quiz: number
  overall: number
  passed: boolean
  finished_at: string | null
  course_title: string
  course_end_date: string | null
  picture: string | null
  visibility?: 'public' | 'private' | 'shared'
  tags?: Array<string | null>
}

export type UserAssignedCoursesStatus = 'completed' | 'active'

export type AttemptQuizRequestBody = {
  section_id: string
  theme_id: string
  step_id: string
  step_progress_id: string
  attempt_id: string
}

export type TheoryCompleteRequestBody = {
  section_id: string
  theme_id: string
  step_id: string
  step_progress_id: string
}

export type TheoryCompleteResponse = {
  id: string
  type: string
  started_at: string
  finished_at: string
  step_id: string
  passed: boolean
}

export type ScormStateRequestBody = {
  section_id: string
  theme_id: string
  step_id: string
  step_progress_id: string
  statement: Record<string | number, unknown>
}

export type Attempt = {
  id: string
  started_at: string
  finished_at: string
  passed: boolean
  questions: Question[]
}

export type Question = {
  id: string
  text: string
  passed: boolean
  multiple_answers: boolean
  answers: Answer[]
}

export type Answer = {
  id: string
  text: string
  is_correct: boolean
  is_selected: boolean
  tip: string
}

export type Settings = {
  need_explain: boolean
  correct_answers: number
  questions_count: number
  testing_time_duration: number
  learning_block: boolean
  attempts_limit: number
}
