import { globalBaseApi, GlobalEndpointBuilder } from '@/store/services/endpoints/base'
import {
  CourseByIdRequest,
  CourseByIdResponse,
  MyCoursesRequest,
  MyCoursesResponse,
} from '../types/courses'
import {
  AnswerQuizRequestBody,
  AnswerQuizResponse,
  AttemptQuizRequestBody,
  TheoryCompleteRequestBody,
  TheoryCompleteResponse,
  ScormStateRequestBody,
} from './test-types'
import { QuizResponse } from '@/entities/themeCourse/ui/theme-preview/components/user-quiz/types'

export const getQueryMyLearningEndpoints = () => {
  return (build: GlobalEndpointBuilder) => ({
    getMyCourses: build.query<MyCoursesResponse, MyCoursesRequest>({
      query: params => ({
        url: `/learning/api/learning/assigned-courses/my-courses`,
        params,
      }),
    }),
    getMyCourseById: build.query<
      {
        assigned_course: CourseByIdResponse
      },
      CourseByIdRequest
    >({
      query: ({ course_id }) => ({
        url: `/learning/api/learning/assigned-courses/my-courses/${course_id}`,
      }),
    }),
  })
}

export const testEndpoints = () => {
  return (build: GlobalEndpointBuilder) => ({
    launchTheme: build.mutation<
      {
        user_id: string
        assigned_course_id: string
        section_id: string
        theme_id: string
        passed: boolean
        has_quiz: boolean
        total_theory: number
        passed_theory: number
        passed_quizes: number
        total_quizes: number
        started_at: string
        finished_at: string
        statistics: {
          theory: number
          quiz: number
          overall: number
        }
        steps_progress: string[]
      },
      {
        body: {
          section_id: string
          theme_id: string
        }
        assigned_course_id: string
      }
    >({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/theme/launch`,
        method: 'POST',
        body,
      }),
    }),
    initSteps: build.mutation<
      {
        id: string
        type: 'quiz'
        started_at: '2024-10-31T16:14:38.307Z'
        finished_at: '2024-10-31T16:14:38.308Z'
        step_id: string
        passed: boolean
      } | QuizResponse,
      {
        assigned_course_id: string
        body: {
          step_id: string
          section_id: string
          theme_id: string
        }
      }
    >({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/init`,
        method: 'POST',
        body,
      }),
    }),
    answerQuiz: build.mutation<
      AnswerQuizResponse,
      {
        assigned_course_id: string
        body: AnswerQuizRequestBody
      }
    >({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/quiz/answer`,
        method: 'POST',
        body,
      }),
    }),
    attemptQuiz: build.mutation<
      AnswerQuizResponse,
      {
        assigned_course_id: string
        body: AttemptQuizRequestBody
      }
    >({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/quiz/attempt/complete`,
        method: 'POST',
        body,
      }),
    }),
    theoryComplete: build.mutation<
      TheoryCompleteResponse,
      {
        assigned_course_id: string
        body: TheoryCompleteRequestBody
      }
    >({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/theory/complete`,
        body,
        method: 'POST',
      }),
    }),
    patchScormState: build.mutation<
      {
        section_id: string
        theme_id: string
        step_id: string
        step_progress_id: string
      },
      {
        assigned_course_id: string
        body: ScormStateRequestBody
      }
    >({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/scorm/statement`,
        method: 'PATCH',
        body,
      }),
    }),
    createAttempt: build.mutation<
      AnswerQuizResponse,
      {
        assigned_course_id: string
        body: {
          section_id: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
          theme_id: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
          step_id: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
          step_progress_id: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
        }
      }
    >({
      query: ({ assigned_course_id, body }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/steps/quiz/attempt/create`,
        method: 'POST',
        body,
      }),
    }),
    completeCourse: build.mutation<
      void,
      {
        assigned_course_id: string
      }
    >({
      query: ({ assigned_course_id }) => ({
        url: `/learning/api/learning/assigned-courses/${assigned_course_id}/complete`,
        method: 'POST',
      }),
    }),
  })
}

export const testApi = globalBaseApi.injectEndpoints({
  endpoints: builder => testEndpoints()(builder),
})
