import { globalBaseApi } from '@/store/services/endpoints/base'
import { isNumber } from '@/shared/helpers'
import {
  ShareCourseRequest,
  SharedCoursesRequest,
  MySharedCoursesRequest,
  ShareCourseResponse,
  CompleteSharedCourseRequest,
  SharedCoursesResponse,
  MySharedCoursesResponse,
} from '../types/courses'

export const sharedCoursesApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getSharedCoursesByOrganizationId: builder.query<SharedCoursesResponse, SharedCoursesRequest>({
      query: ({ organization_id, offset, limit, status, search }) => {
        const params = new URLSearchParams()
        if (search) params.append('search', search)
        if (status) params.append('status', status)
        if (isNumber(offset)) params.append('offset', String(offset))
        if (isNumber(limit)) params.append('limit', String(limit))

        return {
          url: `/learning/api/learning/shared-courses/organizations/${organization_id}`,
          params,
        }
      },
      providesTags: ['shared-courses'],
    }),
    getMySharedCourses: builder.query<MySharedCoursesResponse, MySharedCoursesRequest>({
      query: ({ offset, limit, status }) => {
        const params = new URLSearchParams()
        if (status) params.append('status', status)
        if (isNumber(offset)) params.append('offset', String(offset))
        if (isNumber(limit)) params.append('limit', String(limit))

        return {
          url: `/learning/api/learning/shared-courses/my-courses`,
          params,
        }
      },
      providesTags: ['shared-courses'],
    }),
    shareCourse: builder.mutation<
      ResponseWithNotification<ShareCourseResponse>,
      ShareCourseRequest
    >({
      query: body => ({
        url: `/learning/api/learning/shared-courses`,
        method: 'POST',
        body: body,
      }),
      transformResponse: (response: ShareCourseResponse) => ({
        ...response,
        notificationTip: 'tips:course.course_will_be_shared',
      }),
    }),
    completeSharedCourse: builder.mutation<unknown, CompleteSharedCourseRequest>({
      query: ({ shared_id }) => ({
        url: `/learning/api/learning/shared-courses/${shared_id}/complete`,
        method: 'POST',
      }),
      invalidatesTags: ['shared-courses'],
    }),
    deleteSharedCourse: builder.mutation<unknown, CompleteSharedCourseRequest>({
      query: ({ shared_id }) => ({
        url: `/learning/api/learning/shared-courses/${shared_id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['shared-courses'],
    }),
  }),
})
