import { globalBaseApi } from '@/store/services/endpoints/base'
import {
  CoursesResponse,
  CoursesRequest,
  Course,
  AssignCourseAttachPictureRequest,
  CourseByIdRequest,
  CourseByIdResponse,
  CourseSection,
  ReinviteCourseRequest,
  ManualCompleteCourseRequest,
  AssignCoursesByFilteredUsersParams,
  AssignCoursesRequestWithParams,
  AssignCourseRequestWithParams,
} from '../types/courses'
import { getQueryMyLearningEndpoints } from './endpoints'
import { isNumber } from '@/shared/helpers'
import { UnknownAction } from '@reduxjs/toolkit'
import { RootState } from '@/store'

export const coursesApi = globalBaseApi.injectEndpoints({
  endpoints: builder => ({
    getCoursesByOrganizationId: builder.query<CoursesResponse, CoursesRequest>({
      query: ({ organization_id, offset, limit, tags, sort_by, search, sort_order }) => {
        const params = new URLSearchParams()
        const tagsArray = tags && tags.length > 0 ? tags : undefined
        if (tagsArray) {
          for (const tag of tagsArray) {
            params.append('tags', tag)
          }
        }
        if (sort_by) params.append('sort_by', sort_by)
        if (sort_order) params.append('sort_order', sort_order)
        if (search) params.append('search', search)
        if (isNumber(offset)) params.append('offset', String(offset))
        if (isNumber(limit)) params.append('limit', String(limit))

        return {
          url: `/learning/api/content/courses/organizations/${organization_id}`,
          params,
        }
      },
      providesTags: ['courses'],
    }),
    getCourseById: builder.query<CourseByIdResponse, CourseByIdRequest>({
      query: ({ course_id }) => ({
        url: `/learning/api/content/courses/${course_id}`,
      }),
      transformResponse: (response: CourseByIdResponse) => {
        const sections = [...response.sections]
        const newSec = sections.map(sec => {
          const themes = [...sec.themes]
          themes.sort((a, b) => {
            if (a.order_id && b.order_id) {
              return a.order_id - b.order_id
            }
            return 1
          })
          return {
            ...sec,
            themes: themes,
          }
        })
        newSec.sort((a, b) => a.order_id - b.order_id)
        return {
          ...response,
          sections: newSec,
        }
      },
      providesTags: ['course'],
    }),
    copyCourse: builder.mutation<unknown, { course_id: string }>({
      query: ({ course_id }) => ({
        url: `/learning/api/content/courses/${course_id}/copy`,
        method: 'POST',
        body: { course_id },
      }),
      transformResponse: () => ({
        notificationTip: 'tips:course.course_will_be_copied',
      }),
    }),
    deleteCourses: builder.mutation<unknown, { deleteIds: string[] }>({
      query: ({ deleteIds }) => ({
        url: `learning/api/content/courses/bulk-delete`,
        method: 'POST',
        body: { ids: deleteIds },
      }),
      transformResponse: (_, __, { deleteIds }) => ({
        notificationTip:
          deleteIds.length > 1
            ? 'tips:course.courses_will_be_deleted'
            : 'tips:course.course_will_be_deleted',
      }),
      onQueryStarted({ deleteIds }, { getState, dispatch, queryFulfilled }) {
        queryFulfilled.then(() => {
          const cachedArgs = coursesApi.util.selectCachedArgsForQuery(
            getState() as RootState,
            'getCoursesByOrganizationId',
          )
          cachedArgs.forEach(args => {
            dispatch(
              coursesApi.util.updateQueryData('getCoursesByOrganizationId', args, draft => {
                return {
                  data: draft.data.filter(course => !deleteIds.includes(course.id)),
                  total_count: draft.total_count - deleteIds.length,
                  limit: draft.limit,
                  offset: draft.offset,
                }
              }) as unknown as UnknownAction,
            )
          })
        })
      },
    }),
    assignCourses: builder.mutation<unknown, AssignCoursesRequestWithParams>({
      query: ({ body, params }) => {
        const searchParams = new URLSearchParams()

        if (params) {
          searchParams.append('need_all', params.need_all.toString())
          if (params.search) searchParams.append('search', params.search)
          if (params.roles !== undefined && params.roles !== null)
            searchParams.append('roles', params.roles.toString())
          if (params.risk_level_from !== undefined)
            searchParams.append('risk_level_from', params.risk_level_from.toString())
          if (params.risk_level_to !== undefined)
            searchParams.append('risk_level_to', params.risk_level_to.toString())

          const arrayFields: (keyof AssignCoursesByFilteredUsersParams)[] = [
            'in_phishing',
            'phishing_events',
            'departments',
            'tags',
            'in_course',
            'learning',
            'course_progress',
            'include_ids',
            'exclude_ids',
          ]

          arrayFields.forEach(field => {
            const value = params[field]
            if (Array.isArray(value)) {
              value.forEach(item => {
                if (item !== undefined && item !== null) {
                  searchParams.append(field as string, item.toString())
                }
              })
            }
          })
        }

        return {
          url: `/learning/api/learning/assigned-courses/bulk-assign`,
          method: 'POST',
          body,
          params: params ? searchParams : undefined,
        }
      },
    }),
    assignCourse: builder.mutation<Course, AssignCourseRequestWithParams>({
      query: ({ body, params }) => {
        const searchParams = new URLSearchParams()

        if (params) {
          searchParams.append('need_all', params.need_all.toString())
          if (params.search) searchParams.append('search', params.search)
          if (params.roles !== undefined && params.roles !== null)
            searchParams.append('roles', params.roles.toString())
          if (params.risk_level_from !== undefined)
            searchParams.append('risk_level_from', params.risk_level_from.toString())
          if (params.risk_level_to !== undefined)
            searchParams.append('risk_level_to', params.risk_level_to.toString())

          const arrayFields: (keyof AssignCoursesByFilteredUsersParams)[] = [
            'in_phishing',
            'phishing_events',
            'departments',
            'tags',
            'in_course',
            'learning',
            'course_progress',
            'include_ids',
            'exclude_ids',
          ]

          arrayFields.forEach(field => {
            const value = params[field]
            if (Array.isArray(value)) {
              value.forEach(item => {
                if (item !== undefined && item !== null) {
                  searchParams.append(field as string, item.toString())
                }
              })
            }
          })
        }

        return {
          url: `/learning/api/learning/assigned-courses`,
          method: 'POST',
          body,
          params: params ? searchParams : undefined,
        }
      },
    }),
    attachPictureToCourse: builder.mutation<unknown, AssignCourseAttachPictureRequest>({
      query: ({ assigned_course_id, image }) => {
        const formData = new FormData()
        formData.append('image', image)

        return {
          url: `/learning/api/learning/assigned-courses/${assigned_course_id}/picture`,
          method: 'POST',
          body: formData,
          formData: true,
        }
      },
    }),
    reinviteCourse: builder.mutation<unknown, ReinviteCourseRequest>({
      query: ({
        assigned_course_id,
        progress_groups,
        process_groups,
        is_not_registered,
        newbie,
        ...body
      }) => {
        const params = new URLSearchParams()

        if (is_not_registered !== undefined) {
          params.append('is_not_registered', String(is_not_registered))
        }
        if (newbie !== undefined) {
          params.append('newbie', String(newbie))
        }
        if (progress_groups && progress_groups.length > 0) {
          for (const group of progress_groups) {
            params.append('progress_groups', group)
          }
        }
        if (process_groups && process_groups.length > 0) {
          for (const group of process_groups) {
            params.append('process_groups', group)
          }
        }

        return {
          url: `/learning/api/learning/assigned-courses/${assigned_course_id}/reinvite`,
          method: 'POST',
          body,
          params,
        }
      },
    }),
    createCourse: builder.mutation<Course, FormData>({
      query: body => ({
        url: `/learning/api/content/courses`,
        method: 'POST',
        body,
        formData: true,
      }),
      invalidatesTags: ['courses'],
    }),
    updateCourse: builder.mutation<Course, { courseId: string; body: FormData }>({
      query: ({ courseId, body }) => ({
        url: `/learning/api/content/courses/${courseId}`,
        method: 'PATCH',
        body,
        formData: true,
      }),
      invalidatesTags: ['course', 'courses'],
    }),
    deleteCourseSections: builder.mutation<Course, { courseId: string; sectionIds: string[] }>({
      query: ({ courseId, sectionIds }) => ({
        url: `/learning/api/content/courses/${courseId}/delete-sections`,
        method: 'POST',
        body: { sections_ids: sectionIds },
      }),
      invalidatesTags: ['courses'],
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const respQueryFulfilled = await queryFulfilled

          if (respQueryFulfilled.meta?.response?.ok) {
            dispatch(
              coursesApi.util.updateQueryData(
                'getCourseById',
                { course_id: arg.courseId },
                course => ({
                  ...course,
                  sections: course.sections.filter(section => !arg.sectionIds.includes(section.id)),
                }),
              ),
            )
          }
        } catch (err) {
          console.error(err)
        }
      },
    }),
    swapSectionsInCourse: builder.mutation<
      Course,
      {
        courseId: string
        body: {
          section_from: { object_id: string; order_id: number }
          section_to: { object_id: string; order_id: number }
        }
      }
    >({
      query: ({ courseId, body }) => ({
        url: `/learning/api/content/courses/${courseId}/sections/swap`,
        body,
        method: 'PATCH',
      }),
      invalidatesTags: ['course'],
    }),
    swapThemeInSection: builder.mutation<
      Course,
      {
        courseId: string
        sectionId: string
        body: {
          theme_from: { object_id: string; order_id: number }
          theme_to: { object_id: string; order_id: number }
        }
      }
    >({
      query: ({ courseId, sectionId, body }) => ({
        url: `/learning/api/content/courses/${courseId}/sections/${sectionId}/swap`,
        body,
        method: 'PATCH',
      }),
      invalidatesTags: ['course'],
    }),
    transportCourseThemeInDiffSection: builder.mutation<
      Course,
      {
        courseId: string
        sectionId: string
        body: {
          target_section_id: string
          themes_ids: string[]
        }
      }
    >({
      query: ({ courseId, sectionId, body }) => ({
        url: `/learning/api/content/courses/${courseId}/sections/${sectionId}/themes/transport`,
        body,
        method: 'POST',
      }),
      invalidatesTags: ['course'],
    }),
    updateTags: builder.mutation<Course, { courseId: string; tags: string[] }>({
      query: ({ courseId, tags }) => ({
        url: `/learning/api/content/courses/${courseId}/tags`,
        method: 'PATCH',
        body: { tags },
      }),
      invalidatesTags: ['courses', 'course'],
    }),
    updateCourseSections: builder.mutation<
      Course,
      { courseId: string; sectionId: string; title: string }
    >({
      query: ({ courseId, sectionId, title }) => ({
        url: `/learning/api/content/courses/${courseId}/sections/${sectionId}`,
        method: 'PATCH',
        body: { title },
      }),
      invalidatesTags: ['courses', 'course'],
    }),
    createSections: builder.mutation<
      CourseSection[],
      { courseId: string; sections: { title: string }[] }
    >({
      query: ({ courseId, sections }) => ({
        url: `/learning/api/content/courses/${courseId}/sections`,
        method: 'POST',
        body: { sections },
      }),
      invalidatesTags: ['course', 'courses'],
    }),
    appendThemesInSection: builder.mutation<
      CourseSection[],
      { courseId: string; sectionId: string; themes: string[] }
    >({
      query: ({ courseId, sectionId, themes }) => ({
        url: `/learning/api/content/courses/${courseId}/sections/themes/append`,
        method: 'POST',
        body: { section_id: sectionId, themes_ids: themes },
      }),
      invalidatesTags: ['course', 'courses'],
    }),
    deleteSectionThemes: builder.mutation<
      CourseSection[],
      { courseId: string; sectionId: string; themesIds: string[] }
    >({
      query: ({ courseId, sectionId, themesIds }) => ({
        url: `/learning/api/content/courses/${courseId}/sections/${sectionId}/themes/delete`,
        method: 'POST',
        body: { themes_ids: themesIds },
      }),
      invalidatesTags: ['course', 'courses'],
    }),
    completeCourseForUsers: builder.mutation<void, ManualCompleteCourseRequest>({
      query: body => ({
        url: `/learning/api/learning/assigned-courses/manual-complete-course`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['assigned-courses-employees'],
    }),
    copyOrganizationCourses: builder.mutation<void, { organizationId: UUID; coursesIds: UUID[] }>({
      query: ({ organizationId, coursesIds }) => ({
        url: `/learning/api/content/courses/copy-to-organizations`,
        method: 'POST',
        body: {
          courses_ids: coursesIds,
          organizations_ids: [organizationId],
        },
      }),
      invalidatesTags: ['organization'],
    }),
    copyOrganizationThemes: builder.mutation<void, { organizationId: UUID; themesIds: UUID[] }>({
      query: ({ organizationId, themesIds }) => ({
        url: `/learning/api/content/themes/copy-to-organizations`,
        method: 'POST',
        body: {
          themes_ids: themesIds,
          organizations_ids: [organizationId],
        },
      }),
      invalidatesTags: ['organization'],
    }),
  }),
  overrideExisting: true,
})

export const myCoursesQueryApi = globalBaseApi.injectEndpoints({
  endpoints: builder => getQueryMyLearningEndpoints()(builder),
})
