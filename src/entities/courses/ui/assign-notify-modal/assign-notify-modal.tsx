import { useParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { v4 as uuid } from 'uuid'
import classNamesBind from 'classnames/bind'

import { useAppDispatch, useAppSelector } from '@/store'
import {
  selectIsAllSelected,
  selectAssignNotificationModalOpen,
  selectSelectedUsers,
  setAssignNotificationModalOpen,
  selectExcludedUsers,
  selectFilters,
} from '@/pages/admin/employee-course/slice'
import { ConfirmModal } from '@/shared/modals/confirm-modal'
import { useNotification } from '@/shared/contexts/notifications'
import { coursesApi } from '@/entities/courses'
import styles from './assign-notify-modal.module.scss'

const cx = classNamesBind.bind(styles)

export const AssignNotifyModal = () => {
  const dispatch = useAppDispatch()
  const { t } = useTranslation('pages__assigned-course-detail-statistics')
  const { add } = useNotification()
  const { course_id = '' } = useParams()

  const selectedUsers = useAppSelector(selectSelectedUsers)
  const excludedUsers = useAppSelector(selectExcludedUsers)
  const isAllSelected = useAppSelector(selectIsAllSelected)
  const filters = useAppSelector(selectFilters)
  const assignNotificationModalOpen = useAppSelector(selectAssignNotificationModalOpen)
  const [reinviteMutation, { isLoading: isLoadingReinvite }] =
    coursesApi.useReinviteCourseMutation()

  return (
    <ConfirmModal
      wrapperClassname={cx('actions__wrapper')}
      open={assignNotificationModalOpen}
      setOpen={v => dispatch(setAssignNotificationModalOpen(v))}
      title={t('actions.notify.action')}
      description={t('actions.notify.description')}
      /**
       * слева кнопка "Отменить" -> confirmText, confirmProps, а экшн onClose
       */
      confirmText={t('notify.cancel')}
      confirmProps={{ color: 'gray' }}
      onClose={() => {
        dispatch(setAssignNotificationModalOpen(false))
      }}
      /**
       * справа кнопка "Отправить" -> closeText, closeProps, а экшн onConfirm
       */
      closeText={t('notify.send')}
      closeProps={{
        color: 'green',
        loading: isLoadingReinvite,
      }}
      onConfirm={() => {
        reinviteMutation({
          assigned_course_id: course_id,
          need_all: isAllSelected,
          users_ids: isAllSelected ? [] : selectedUsers,
          exclude_users_ids: isAllSelected ? excludedUsers : [],
          progress_groups: filters.education,
          process_groups: filters.status,
        }).then(res => {
          if (!res.error) {
            add({ id: uuid(), message: t('actions.notify.success'), status: 'success' })
          }
          dispatch(setAssignNotificationModalOpen(false))
        })
      }}
    />
  )
}
