@use '../../../../shared/assets/styles/mixins/text';


.wrapper {
  background: #fff;
  border: 1px solid #ebeff2;
  border-radius: 16px;

  display: grid;
  grid-template-rows: auto 1fr;
  overflow: hidden;
  position: relative;
  .checkbox {
    position: absolute;
    right: 18px;
    top: 18px;
    z-index: 10;
  }
  .imageWrapper {
    align-items: center;
    backdrop-filter: blur(2px);

    background: #e1e4eb;
    display: flex;
    height: var(--tag-image-height);
    justify-content: center;
    > img {
      height: 100%;
      object-fit: cover;
      object-position: center;
      width: 100%;
      opacity: 0.8;
    }
    > svg {
      max-height: 91px;
      max-width: 110px;
    }
  }
  .textWrapper {
    align-content: space-between;
    display: grid;
    grid-template-rows: auto auto;
    height: 100%;
    padding: 20px 20px 16px;
  }
  .title {
    color: #343b54;
    font-family: 'TT Norms Pro';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;

    @include text.max-lines(3);
  }
  .info {
    display: flex;
    gap: 8px;
    justify-content: space-between;
    margin-bottom: 4px;
    &:last-child {
      margin-bottom: 0;
    }
    .infoItem {
      color: #5c6585;
      font-family: 'TT Norms Pro';
      font-size: 13px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      &.green {
        align-items: end;

        color: var(--color-primary, #3dbc87);
        display: flex;
      }
    }
  }
}

.actions {
  display: flex;
  gap: 4px;
}
