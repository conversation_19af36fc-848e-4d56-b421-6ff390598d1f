import styles from './course-card.module.scss'
import classNamesBind from 'classnames/bind'
import Skeleton from 'react-loading-skeleton'
import { ReactElement, ReactNode } from 'react'

const cx = classNamesBind.bind(styles)

const DefaultImage: React.FC = () => {
  return (
    <svg
      width='110'
      height='91'
      viewBox='0 0 110 91'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
    >
      <path
        d='M18.3333 74.4897V55.3596L39.6009 68.8351C48.9568 74.7635 61.0432 74.7635 70.3989 68.8351L91.6666 55.3596V74.4897C91.6666 75.3142 91.3911 76.1164 90.8809 76.7758L90.8752 76.7829L90.8689 76.7915L90.8526 76.8118L90.8097 76.8662L90.7463 76.9439L90.6772 77.0277C90.5687 77.1578 90.4194 77.332 90.2277 77.5444C89.8448 77.9686 89.2933 78.5457 88.5662 79.2228C87.1121 80.5762 84.9493 82.3374 82.0217 84.0845C76.1404 87.5948 67.2503 91 55 91C42.7495 91 33.8595 87.5948 27.9782 84.0845C25.0507 82.3374 22.8879 80.5762 21.4339 79.2228C20.7066 78.5457 20.155 77.9686 19.7721 77.5444C19.5806 77.332 19.431 77.1578 19.3225 77.0277C19.2683 76.9622 19.2243 76.9083 19.1904 76.8662L19.1472 76.8118L19.1312 76.7915L19.1246 76.7829C18.6147 76.1235 18.3333 75.3142 18.3333 74.4897ZM108.22 35.7688L66.1016 62.456C59.3565 66.7299 50.6435 66.7299 43.8983 62.456L7.85714 39.6195V66.8696C7.85714 68.9738 6.09824 70.6797 3.92857 70.6797C1.7589 70.6797 0 68.9738 0 66.8696V35.1191C0 34.6732 0.0789907 34.2452 0.224086 33.8476C0.0758478 33.4406 -0.00193802 33.0066 5.24521e-05 32.5626C0.00581435 31.2703 0.686609 30.069 1.80845 29.3715L44.046 3.11271C50.7215 -1.03757 59.2785 -1.03757 65.9539 3.11271L108.192 29.3715C109.313 30.069 109.994 31.2703 110 32.5626C110.005 33.6934 109.492 34.7589 108.617 35.4813C108.492 35.5844 108.359 35.6804 108.22 35.7688Z'
        fill='white'
      />
    </svg>
  )
}

type Props = {
  title?: string
  imageUrl?: string
  isLoading?: boolean
  className?: string
  TopAdornment?: ReactElement
  BottomAdornment?: ReactNode | (() => JSX.Element)
}

export const CourseCard = ({
  title = '',
  imageUrl,
  className,
  isLoading = false,
  TopAdornment,
  BottomAdornment,
}: Props) => {
  return (
    <div className={cx('wrapper', className)}>
      {TopAdornment && TopAdornment}
      {isLoading ? (
        <Skeleton height={'160px'} />
      ) : (
        <div className={cx('imageWrapper')}>
          {imageUrl ? <img src={imageUrl} alt='' /> : <DefaultImage />}
        </div>
      )}

      <div className={cx('textWrapper')}>
        <div className={cx('title')}>{isLoading ? <Skeleton width={'50%'} /> : title}</div>

        {isLoading && <Skeleton />}
        {typeof BottomAdornment === 'function' ? BottomAdornment() : BottomAdornment}
      </div>
    </div>
  )
}
