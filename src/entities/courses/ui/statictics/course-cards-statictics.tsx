import { useTranslation } from 'react-i18next'
import styles from './course-cards-statictics.module.scss'
import classNamesBind from 'classnames/bind'
import SuccessIcon from '@/shared/ui/Icon/icons/components/SuccessIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { isNumber } from '@/shared/helpers'
import { formatDateDifference } from '@/shared/helpers/date'

const cx = classNamesBind.bind(styles)

type Props = {
  themes?: number
  theory?: number
  testing?: number
  start_date?: string
  end_date?: string
  isCompleted?: boolean
}

export const CourseStatistics = ({
  end_date,
  start_date,
  testing,
  themes,
  theory,
  isCompleted,
}: Props) => {
  const { t } = useTranslation('entities__courses__course-cards-statictics')

  const dateDiff =
    end_date && start_date ? formatDateDifference(new Date(start_date), new Date(end_date)) : null

  return (
    <div className={cx('courses__testing')}>
      <div className={cx('courses__info')}>
        {isNumber(themes) && <div>{t('theme', { count: Number(themes.toFixed(0)) })}</div>}
        {dateDiff && (
          <div>
            {dateDiff?.day && t('days', { count: Number(dateDiff?.days) })}
            {!dateDiff?.day &&
              dateDiff?.hour &&
              isNumber(dateDiff?.hours) &&
              t('hour', { count: Number(dateDiff?.hours) })}
          </div>
        )}
      </div>
      <div className={cx('courses__testing__progress')}>
        {!isCompleted ? (
          <>
            {isNumber(theory) && <p>{t('theory', { count: Number(theory.toFixed(0)) })}</p>}
            {isNumber(testing) && <p>{t('testing', { count: Number(testing.toFixed(0)) })}</p>}
          </>
        ) : (
          <div className={cx('completed')}>
            {t('completed')}
            <IconWrapper size='16' color='green'>
              <SuccessIcon />
            </IconWrapper>
          </div>
        )}
      </div>
    </div>
  )
}
