import { useCallback, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { CoursesSortBy, CoursesSortOrder } from '../../model/types/courses'
import { useEvent } from '@/shared/hooks'
import styles from './courses-sort-select.module.scss'
import classNamesBind from 'classnames/bind'
import ChevroneSmallIcon from '@/shared/ui/Icon/icons/components/ChevroneSmallIcon'
import { IconWrapper } from '@/shared/ui/Icon/IconWrapper'
import { IconSortDirection } from '@/shared/components/icon-sort-direction'

export type CoursesSortSelectProps = {
  sortBy: CoursesSortBy
  sortOrder?: CoursesSortOrder
  setSortOrder: (v: CoursesSortOrder) => void
  setSortBy: (v: CoursesSortBy) => void
  wrapperClassName?: string
}

const cx = classNamesBind.bind(styles)

export const CoursesSortSelect = ({
  sortBy,
  sortOrder,
  setSortBy,
  setSortOrder,
  wrapperClassName,
}: CoursesSortSelectProps) => {
  const { t } = useTranslation('entities__courses__courses-sort-select')
  const wrapper = useRef<HTMLDivElement>(null)

  const SORT_LABELS: Record<CoursesSortBy, string> = useMemo(() => {
    return {
      title: t('sort.title'),
      created_at: t('sort.created_at'),
    }
  }, [t])
  const [open, setOpen] = useState(false)

  const handleOutsideClick = useCallback((e: Event) => {
    if (!wrapper.current) return

    if (e.composedPath().indexOf(wrapper.current) === -1) setOpen(false)
  }, [])

  useEvent('click', handleOutsideClick, window)

  return (
    <div className={cx('wrapper', wrapperClassName)} ref={wrapper} onClick={() => setOpen(!open)}>
      <div className={cx('text')}>
        {SORT_LABELS[sortBy]}
        <IconWrapper color='gray80' className={cx('icon')} direction={!open ? 'right' : 'left'}>
          <ChevroneSmallIcon />
        </IconWrapper>
        <IconSortDirection
          startDirection={'asc'}
          direction={sortOrder}
          onChange={(dir, e) => {
            setSortOrder(dir)
            e?.stopPropagation()
          }}
        />
      </div>
      {open && (
        <div className={cx('listWrapper')} style={styles} onClick={e => e.stopPropagation()}>
          <div className={cx('listInner')}>
            {Object.keys(SORT_LABELS).map(s => {
              const sort = s as CoursesSortBy
              const isActive = sort === sortBy

              return (
                <span
                  key={`list-item-${s}`}
                  className={cx('listItem', { active: isActive })}
                  onClick={() => {
                    setSortBy(sort)
                    setOpen(false)
                  }}
                >
                  {SORT_LABELS[sort]}
                </span>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
