export enum EmailTemplateTypes {
  'reset' = 'reset',
  'pass_changed' = 'pass_changed',
  'slow_progress' = 'slow_progress',
  'completion' = 'completion',
  'invite' = 'invite',
  'notify' = 'notify',
  'course_assignment' = 'course_assignment',
  'expired_license' = 'expired_license',
  'tag_message' = 'tag_message',
  'pdf_report' = 'pdf_report',
  'learning_certificate' = 'learning_certificate',
  'tag_action_learning_report' = 'tag_action_learning_report',
}

export type EmailTemplate = {
  id: UUID
  notification_type: EmailTemplateTypes
  html_data: string
}

export type EmailTemplates = EmailTemplate[]

export type CreateEmailTemplateRequest = {
  template_data: string
  template_type: EmailTemplateTypes
}

export type EditEmailTemplateRequest = Omit<CreateEmailTemplateRequest, 'template_type'> & {
  template_id: UUID
}
