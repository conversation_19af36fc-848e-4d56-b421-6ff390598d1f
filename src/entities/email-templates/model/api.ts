import { globalBaseApi } from '@/store/services/endpoints/base'
import { getMutationsEmailTemplatesEndpoints, getQueryEmailTemplatesEndpoints } from './endpoints'

export const queryEmailTemplatesApi = globalBaseApi.injectEndpoints({
  endpoints: builder => getQueryEmailTemplatesEndpoints()(builder),
})

export const mutationEmailTemplatesApi = globalBaseApi.injectEndpoints({
  endpoints: builder => getMutationsEmailTemplatesEndpoints()(builder),
})
