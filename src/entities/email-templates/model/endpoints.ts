import { GlobalEndpointBuilder } from '@/store/services/endpoints/base'
import {
  CreateEmailTemplateRequest,
  EditEmailTemplateRequest,
  EmailTemplate,
  EmailTemplates,
} from './types'

const PREFIX_URL = '/lk/api/v2/'

export const getQueryEmailTemplatesEndpoints = () => {
  return (build: GlobalEndpointBuilder) => ({
    getEmailTemplates: build.query<EmailTemplates, void>({
      query: () => ({
        url: PREFIX_URL + `/settings/email/template`,
      }),
      providesTags: ['email-templates'],
    }),
    getDefaultEmailTemplates: build.query<EmailTemplates, void>({
      query: () => ({
        url: PREFIX_URL + `/settings/email/default_templates`,
      }),
      providesTags: ['default-emails-templates'],
    }),
    getEmailTemplate: build.query<EmailTemplate, UUID>({
      query: template_id => ({
        url: PREFIX_URL + `/settings/email/template/${template_id}`,
      }),
      providesTags: ['email-template'],
    }),
  })
}

export const getMutationsEmailTemplatesEndpoints = () => {
  return (build: GlobalEndpointBuilder) => ({
    createEmailTemplate: build.mutation<
      ResponseWithNotification<EmailTemplate>,
      CreateEmailTemplateRequest
    >({
      query: body => ({
        url: PREFIX_URL + `/settings/email/template`,
        method: 'POST',
        body,
      }),
      invalidatesTags: ['email-templates'],
      transformResponse: (response: EmailTemplate) => ({
        ...response,
        notificationTip: 'tips:templates.create',
      }),
    }),
    updateEmailTemplate: build.mutation<
      ResponseWithNotification<EmailTemplate>,
      EditEmailTemplateRequest
    >({
      query: ({ template_data, template_id }) => ({
        url: PREFIX_URL + `/settings/email/template/${template_id}`,
        method: 'PATCH',
        body: { template_data },
      }),
      invalidatesTags: ['email-templates', 'email-template'],
      transformResponse: (response: EmailTemplate) => ({
        ...response,
        notificationTip: 'tips:templates.update',
      }),
    }),
    deleteEmailTemplate: build.mutation<ResponseWithNotification<void>, UUID>({
      query: template_id => ({
        url: PREFIX_URL + `/settings/email/template/${template_id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['email-templates'],
      transformResponse: () => ({
        notificationTip: 'tips:templates.delete',
      }),
    }),
  })
}
