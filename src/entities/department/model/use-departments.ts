import { useCallback, useEffect, useMemo } from 'react'
import { useLocation, useSearchParams } from 'react-router-dom'
import { useTranslation } from 'react-i18next'

import { useAppDispatch, useAppSelector } from '@/store'
import { URLS } from '@/shared/configs/urls'

import {
  setPage as setPageSlice,
  setSortType as setSortTypeSlice,
  setSortDirection as setSortDirectionSlice,
  setActiveDepartment as setActiveDepartmentSlice,
  setCheckedDeparments as setCheckedDeparmentsSlice,
  addBreadcrumb as addBreadcrumbSlice,
  deleteBreadcrumb as deleteBreadcrumSlice,
  handleDepartmentCheck as handleDepartmentCheckSlice,
  selectPage,
  selectSortType,
  selectSortDirection,
  selectLimit,
  selectActiveDepartment,
  selectCheckedDeparments,
  selectBreadcrumbs,
} from './departments-slice'
import {
  DepartmentSortItem,
  DepartmentsSortFields,
  GetDepartmentsParams,
  IUseDepartments,
} from './use-departments.types'
import { departmentAPI } from './endpoints'

const { useGetDepartmentsQuery, useGetDepartmentsStatQuery } = departmentAPI

type Props = {
  isSkipDepartmentsReq?: boolean
}

export const useDepartments = ({ isSkipDepartmentsReq = false }: Props = {}): IUseDepartments => {
  const { t } = useTranslation()
  const dispatch = useAppDispatch()
  const { pathname } = useLocation()
  const [searchParams] = useSearchParams()
  const tab = searchParams.get('tab') || 'employees'

  /** Selectors */
  const page = useAppSelector(selectPage)
  const sortType = useAppSelector(selectSortType)
  const sortDirection = useAppSelector(selectSortDirection)
  const limit = useAppSelector(selectLimit)
  const activeDepartment = useAppSelector(selectActiveDepartment)
  const checkedDeparments = useAppSelector(selectCheckedDeparments)
  const breadcrumbs = useAppSelector(selectBreadcrumbs)

  const sortItems: DepartmentSortItem[] = useMemo(
    () => [
      { id: '1', title: t('commons:name'), field: 'title' },
      { id: '2', title: t('commons:number_of_people'), field: 'users_count' },
      { id: '3', title: t('commons:learning_progress'), field: 'progress' },
      { id: '4', title: t('commons:risk_level'), field: 'risk_level' },
      { id: '5', title: t('commons:probability_of_incident'), field: 'phishing_indicator' },
    ],
    [t],
  )

  useEffect(() => {
    dispatch(setSortTypeSlice(sortItems[0]))
  }, [sortItems, dispatch])

  const {
    data: departments,
    refetch: refetchDepartments,
    isLoading: isLoadingDepartments,
    isFetching: isFetchingDepartments,
    error: errorDepartments,
  } = useGetDepartmentsQuery(
    { limit: Number.MAX_SAFE_INTEGER },
    {
      skip: isSkipDepartmentsReq,
    },
  )

  const getSortParams = useCallback((): GetDepartmentsParams | undefined => {
    if (!sortType) return

    return {
      sort_by: sortType?.field,
      sort_order: (sortDirection as DepartmentsSortFields) ?? 'desc',
    }
  }, [sortType?.field, sortDirection])

  const {
    data: departmentsStat,
    isLoading: isLoadingDepartmentsStat,
    isFetching: isFetchingDepartmentsStat,
    error: errorDepartmentsStat,
  } = useGetDepartmentsStatQuery(
    // TODO: return sort params
    { parent_id: activeDepartment?.id, page: page, params: getSortParams() },
    {
      // TODO FIX: Временный костыль, чтобы отображалось при входе через adfs.
      // Не понял, как пределать значение из angular в react
      // При переходе на react и переделке авторизации по идее будет норм проверка тут
      skip: pathname !== URLS.ADMIN_STAFF_EMPLOYEES_PAGE || tab !== 'departments' || !sortType,
    },
  )

  /** Actions */

  const setPage: IUseDepartments['setPage'] = page => {
    dispatch(setPageSlice(page))
  }

  // const setSortItems: IUseDepartments['setSortItems'] = items => {
  //   dispatch(setSortItemsSlice(items))
  // }

  const setSortType: IUseDepartments['setSortType'] = type => {
    dispatch(setSortTypeSlice(type))
  }

  const setSortDirection: IUseDepartments['setSortDirection'] = dir => {
    dispatch(setSortDirectionSlice(dir))
  }

  const handleDeparmentCheck: IUseDepartments['handleDeparmentCheck'] = departmentId => {
    dispatch(handleDepartmentCheckSlice(departmentId))
  }

  const setCheckedDeparments: IUseDepartments['setCheckedDeparments'] = checkedDeparments => {
    dispatch(setCheckedDeparmentsSlice(checkedDeparments))
  }

  const setActiveDepartment: IUseDepartments['setActiveDepartment'] = activeDepartment => {
    dispatch(setActiveDepartmentSlice(activeDepartment))
  }

  const addBreadcrumb: IUseDepartments['addBreadcrumb'] = breadcrumb => {
    dispatch(addBreadcrumbSlice(breadcrumb))
  }

  const deleteBreadcrumbs: IUseDepartments['deleteBreadcrumbs'] = (id, name) => {
    dispatch(deleteBreadcrumSlice({ id, name }))
  }

  return {
    page,
    sortItems,
    sortType,
    sortDirection,
    limit,
    activeDepartment,
    checkedDeparments,
    breadcrumbs,

    departments: departments?.data,
    isLoadingDepartments,
    isFetchingDepartments,
    errorDepartments,
    refetchDepartments,

    departmentsStat,
    isLoadingDepartmentsStat,
    isFetchingDepartmentsStat,
    errorDepartmentsStat,

    setPage,
    setSortType,
    setSortDirection,
    handleDeparmentCheck,
    setCheckedDeparments,
    setActiveDepartment,
    addBreadcrumb,
    deleteBreadcrumbs,
  }
}
