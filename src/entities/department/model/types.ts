import { SORT_BY } from '@/entities/employee'

export interface IDepartmentsWithCount extends ResponseWithPagination {
  data: IDepartment[]
}
export interface IDepartment {
  id: UUID
  title: string
  organization_id: UUID
  statistic: {
    progress: number
    phishing: number
    risk_level: number
    phishing_events: {
      sent: number
      opened: number
      clicked: number
      entered_data: number
      opened_attachment: number
    }
  }
  users_count: number
}

export interface ICreateDepartmentBody {
  title: string
  parent_department_uuid: UUID
}

export interface ICreateDepartmentResponse {
  id: UUID
  parent_department_uuid: UUID
  title: string
  users_count: number
  users_count_with_nested: number
}

export interface IDepartmentNew {
  id: UUID
  title: string
  users_count: number
  users_count_with_nested: number
}

export interface IDepartmentStat {
  id: UUID
  title: string
  organization_id: UUID
  users_count: number | null
  statistic: {
    progress: number | null
    risk_level: number | null
    phishing: number | null
  }
}

export interface IDepartmentStatWithData extends ResponseWithPagination {
  data: IDepartmentStat[]
}

export interface IDepartmentModalUsersRequest {
  search?: string | null
  sort_by?: SORT_BY
  department_id: UUID
  limit?: number
  offset?: number
}

export interface IEmployeeTree {
  id: UUID
  old_id: number
  email: string
  full_name: string
}

export interface IDepartmentsTree {
  id: UUID
  parent_department_uuid: null | UUID
  title: string
  users: IEmployeeTree[]
}

export interface IDepartmentsTreeWithData extends ResponseWithPagination {
  data: IDepartmentsTree[]
}

export type DepartmentsSortFields =
  | 'title'
  | 'users_count'
  | 'progress'
  | 'risk_level'
  | 'phishing_indicator'
export type DepartmentsSortOrder = 'asc' | 'desc'

export type GetDepartmentsParams = {
  sort_by: DepartmentsSortFields
  sort_order: DepartmentsSortFields
}
