import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit'
import {
  DepartmentBreadcrumb,
  DepartmentSliceState,
  // DepartmentSortItem,
  DepartmentState,
} from './use-departments.types'
import { DEPARTMENTS_LIMIT } from '@/shared/constants'
import { BreadcrumbItem } from '@/shared/ui'

const DEFAULT_BREADCRUMBS: BreadcrumbItem[] = [
  {
    id: 'all',
    text: 'commons:all_departments',
    clickable: true,
  },
]

const initialState: DepartmentSliceState = {
  page: 0,
  sortItems: [],
  sortType: null,
  sortDirection: null,
  limit: DEPARTMENTS_LIMIT,
  activeDepartment: null,
  checkedDeparments: [],
  breadcrumbs: DEFAULT_BREADCRUMBS,
}

export const departmentsSlice = createSlice({
  name: 'departments',
  initialState,
  reducers: {
    setPage(state, action: PayloadAction<DepartmentSliceState['page']>) {
      state.page = action.payload
    },
    setSortItems(state, action: PayloadAction<DepartmentSliceState['sortItems']>) {
      state.sortItems = action.payload
    },
    setSortType(state, action: PayloadAction<DepartmentSliceState['sortType']>) {
      if (action.payload !== null) {
        state.sortType = action.payload
      }
    },
    setSortDirection(state, action: PayloadAction<DepartmentSliceState['sortDirection']>) {
      state.sortDirection = action.payload
    },
    setLimit(state, action: PayloadAction<DepartmentSliceState['limit']>) {
      state.limit = action.payload
    },
    setActiveDepartment(state, action: PayloadAction<DepartmentSliceState['activeDepartment']>) {
      state.activeDepartment = action.payload
    },
    handleDepartmentCheck(state, action: PayloadAction<UUID>) {
      const departmentId = action.payload
      if (!departmentId) return
      const position = state.checkedDeparments.indexOf(departmentId)

      if (position === -1) {
        state.checkedDeparments.push(departmentId)
      } else {
        // setCheckedDeparments(prev => [...prev.slice(0, position), ...prev.slice(position + 1)])
        state.checkedDeparments.splice(position, 1)
      }
    },
    setCheckedDeparments(state, action: PayloadAction<DepartmentSliceState['checkedDeparments']>) {
      state.checkedDeparments = action.payload
    },
    addBreadcrumb(state, action: PayloadAction<DepartmentBreadcrumb>) {
      state.breadcrumbs.push({ ...action.payload, clickable: true })
    },
    deleteBreadcrumb(state, action: PayloadAction<{ id: UUID; name: string }>) {
      const { id, name } = action.payload

      if (id === 'all') {
        state.breadcrumbs = departmentsSlice.getInitialState().breadcrumbs
        state.activeDepartment = null
        return
      }

      state.activeDepartment = { id, title: name }
      state.breadcrumbs = state.breadcrumbs.slice(
        0,
        state.breadcrumbs.findIndex(b => b.id === id) + 1,
      )
    },
  },
})

export const selectPage = createSelector(
  (state: DepartmentState) => state,
  state => state.departments.page,
)

export const selectSortItems = createSelector(
  (state: DepartmentState) => state,
  state => state.departments.sortItems,
)

export const selectSortType = createSelector(
  (state: DepartmentState) => state,
  state => state.departments.sortType,
)

export const selectSortDirection = createSelector(
  (state: DepartmentState) => state,
  state => state.departments.sortDirection,
)

export const selectLimit = createSelector(
  (state: DepartmentState) => state,
  state => state.departments.limit,
)

export const selectActiveDepartment = createSelector(
  (state: DepartmentState) => state,
  state => state.departments.activeDepartment,
)

export const selectCheckedDeparments = createSelector(
  (state: DepartmentState) => state,
  state => state.departments.checkedDeparments,
)

export const selectBreadcrumbs = createSelector(
  (state: DepartmentState) => state,
  state => state.departments.breadcrumbs,
)

export const {
  setPage,
  setSortItems,
  setSortType,
  setSortDirection,
  setLimit,
  setActiveDepartment,
  setCheckedDeparments,
  addBreadcrumb,
  deleteBreadcrumb,
  handleDepartmentCheck,
} = departmentsSlice.actions
