import {
  GetDepartmentsParams,
  IDepartment,
  IDepartmentModalUsersRequest,
  IDepartmentStatWithData,
} from './types'

import { IEmployeesStatList, IEmployeesStatWithData, IUser } from 'entities/employee'
import { IUsersResponse } from '@/entities/employee/model/types'
import { DEPARTMENTS_LIMIT } from '@/shared/constants'
import { globalBaseApi } from 'store/services/endpoints/base'
import { BACKEND_URLS } from '@/shared/constants/urls'
import { getHighRole } from '@/shared/helpers/employees'

const WITHOUT_DEP = 'Без отдела'

export const departmentAPI = globalBaseApi.injectEndpoints({
  endpoints: build => ({
    getDepartments: build.query<
      ResponseWithPagination & { data: IDepartment[] },
      RequestWithPagination & { organization_id?: UUID; search?: string }
    >({
      query: params => ({ url: `${BACKEND_URLS.USER_SERVICE}/departments/all`, params }),
      transformResponse: (response: ResponseWithPagination & { data: IDepartment[] }) => ({
        ...response,
        data: response.data?.sort(a => (a.title === WITHOUT_DEP ? -1 : 1)), // сортируем только data
      }),
      providesTags: ['department'],
    }),
    getDepartmentsTitles: build.query<
      ResponseWithPagination & { data: IDepartment[] },
      RequestWithPagination & { search?: string }
    >({
      query: params => ({ url: `${BACKEND_URLS.USER_SERVICE}/departments/titles`, params }),
      providesTags: ['department'],
    }),
    getDepartmentsModalUsers: build.query<IUser[], IDepartmentModalUsersRequest>({
      query: ({ department_id }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/departments/${department_id}/modal/users`,
        params: {
          limit: Number.MAX_SAFE_INTEGER,
        },
      }),
      transformResponse: (response: IUsersResponse) => {
        const newUsers = response.data.map(({ roles, ...user }) => ({
          ...user,
          role: getHighRole(roles),
        }))
        return newUsers
      },
      providesTags: ['employees'],
      keepUnusedDataFor: 30,
    }),
    deleteDepartments: build.mutation<ResponseWithNotification<void>, UUID[]>({
      query: ids => {
        return {
          url: `${BACKEND_URLS.USER_SERVICE}/departments/bulk-delete`,
          method: 'POST',
          body: {
            departments_ids: ids,
          },
        }
      },
      invalidatesTags: ['department', 'department-stat'],
      transformResponse: () => ({
        notificationTip: 'tips:department.delete',
      }),
    }),
    editDepartment: build.mutation<ResponseWithNotification<void>, { id: UUID; title: string }>({
      query: body => ({
        url: `${BACKEND_URLS.USER_SERVICE}/departments`,
        method: 'PATCH',
        body,
      }),
      invalidatesTags: ['department', 'department-stat'],
      transformResponse: () => ({
        notificationTip: 'tips:department.update',
      }),
    }),
    createDepartment: build.mutation<
      ResponseWithNotification<void>,
      { title: string; parentID?: UUID }
    >({
      query: ({ title, parentID: parent_department_uuid }) => ({
        url: `${BACKEND_URLS.USER_SERVICE}/departments`,
        method: 'POST',
        body: {
          title,
          parent_id: parent_department_uuid,
        },
      }),
      invalidatesTags: ['department', 'department-stat'],
      transformResponse: () => ({
        notificationTip: 'tips:department.create',
      }),
    }),
    getDepartmentsStat: build.query<
      IDepartmentStatWithData,
      { parent_id: UUID | void | null; page: number; params?: GetDepartmentsParams }
    >({
      query: ({ parent_id, page = 0, params }) => {
        const limit = DEPARTMENTS_LIMIT

        return {
          url: `${BACKEND_URLS.USER_SERVICE}/departments/hierarchy/v1/stat`,
          params: {
            ...params,
            limit,
            offset: page * limit,
            parent_id: parent_id ?? undefined,
          },
        }
      },
      providesTags: ['department-stat'],
    }),
    getDepartmentsTree: build.query<
      IEmployeesStatList[],
      {
        department_id?: string
        department_search?: string
        user_search?: string
        sort_order?: 'asc' | 'desc'
      } | void
    >({
      query: params => ({
        url: `${BACKEND_URLS.USER_SERVICE}/departments/modal/v1/tree`,
        params: {
          limit: 10000,
          ...(params ?? {}),
        },
      }),
      providesTags: ['department-tree'],
      transformResponse: (response: IEmployeesStatWithData) => response.data,
    }),
    //
  }),
})

export const {
  useGetDepartmentsQuery,
  useGetDepartmentsTitlesQuery,
  useEditDepartmentMutation,
  useCreateDepartmentMutation,
  useDeleteDepartmentsMutation,
  useGetDepartmentsStatQuery,
  useGetDepartmentsTreeQuery,
  useLazyGetDepartmentsModalUsersQuery,
  useLazyGetDepartmentsQuery,
} = departmentAPI
