import { FetchBaseQueryError } from '@reduxjs/toolkit/query'
import { SerializedError } from '@reduxjs/toolkit'

import { BreadcrumbItem, IListItem } from '@/shared/ui'
import { SortDirection } from '@/shared/components/icon-sort-direction'

export interface DepartmentSliceState {
  page: number
  sortItems: DepartmentSortItem[]
  sortType: DepartmentSortItem | null
  sortDirection: SortDirection | null
  limit: number
  activeDepartment: {
    id: UUID
    title: string
  } | null
  checkedDeparments: UUID[]
  breadcrumbs: BreadcrumbItem[]
}

export interface DepartmentState {
  departments: DepartmentSliceState
}

export interface IUseDepartments extends DepartmentSliceState {
  sortItems: DepartmentSortItem[]
  departments: IDepartment[] | undefined
  isLoadingDepartments: boolean
  isFetchingDepartments: boolean
  errorDepartments: FetchBaseQueryError | SerializedError | undefined | string
  refetchDepartments: () => void

  departmentsStat: IDepartmentStatWithData | undefined
  isLoadingDepartmentsStat: boolean
  isFetchingDepartmentsStat: boolean
  errorDepartmentsStat: FetchBaseQueryError | SerializedError | undefined | string

  setPage: (page: IUseDepartments['page']) => void
  setSortType: (item: IUseDepartments['sortType']) => void
  setSortDirection: (dir: IUseDepartments['sortDirection']) => void
  handleDeparmentCheck: (departmentId: UUID) => void
  setCheckedDeparments: (checkedDeparments: IUseDepartments['checkedDeparments']) => void
  setActiveDepartment: (activeDepartment: IUseDepartments['activeDepartment']) => void
  addBreadcrumb: (breadcrumb: DepartmentBreadcrumb) => void
  deleteBreadcrumbs: (id: BreadcrumbItem['id'], name: BreadcrumbItem['text']) => void
}

export type DepartmentBreadcrumb = Pick<BreadcrumbItem, 'id' | 'text'> &
  Partial<Pick<BreadcrumbItem, 'clickable'>>

export type DepartmentSortItem = IListItem & {
  field: DepartmentsSortFields
}

export interface IDepartmentsWithCount extends ResponseWithPagination {
  data: IDepartment[]
}

export interface IDepartment {
  id: UUID
  title: string
  organization_id: UUID
  statistic: {
    progress: number
    phishing: number
    risk_level: number
    phishing_events: {
      sent: number
      opened: number
      clicked: number
      entered_data: number
      opened_attachment: number
    }
  }
  users_count: number
}

export interface ICreateDepartmentBody {
  title: string
  parent_department_uuid: UUID
}

export interface ICreateDepartmentResponse {
  id: UUID
  parent_department_uuid: UUID
  title: string
  users_count: number
  users_count_with_nested: number
}

export interface IDepartmentNew {
  id: UUID
  title: string
  users_count: number
  users_count_with_nested: number
}

export interface IDepartmentStat {
  id: UUID
  title: string
  organization_id: UUID
  users_count: number | null
  statistic: {
    progress: number | null
    risk_level: number | null
    phishing: number | null
  }
}

export interface IDepartmentStatWithData extends ResponseWithPagination {
  data: IDepartmentStat[]
}

export interface IEmployeeTree {
  id: UUID
  old_id: number
  email: string
  full_name: string
}

export interface IDepartmentsTree {
  id: UUID
  parent_department_uuid: null | UUID
  title: string
  users: IEmployeeTree[]
}

export interface IDepartmentsTreeWithData extends ResponseWithPagination {
  data: IDepartmentsTree[]
}

export type DepartmentsSortFields =
  | 'title'
  | 'users_count'
  | 'progress'
  | 'risk_level'
  | 'phishing_indicator'
export type DepartmentsSortOrder = 'asc' | 'desc'

export type GetDepartmentsParams = {
  sort_by: DepartmentsSortFields
  sort_order: DepartmentsSortFields
}
