import { globalBaseApi, GlobalEndpointBuilder } from '@/store/services/endpoints/base'
import { InstructionCreateResponse, InstructionsResponse } from './types'

const PREFIX_URL = '/lk/api/v2/'

export const getInstructionsQueries = () => {
  return (build: GlobalEndpointBuilder) => ({
    getInstructions: build.query<InstructionsResponse, void>({
      query: () => ({
        url: PREFIX_URL + 'organizations/instructions',
        method: 'GET',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }),
      providesTags: ['instructions'],
    }),
    uploadInstructions: build.mutation<InstructionCreateResponse, FormData>({
      query: body => ({
        url: PREFIX_URL + 'admin/instructions',
        method: 'POST',
        formData: true,
        body,
      }),
      invalidatesTags: ['instructions'],
    }),
    deleteByIdInstructions: build.mutation<void, string>({
      query: id => ({
        url: PREFIX_URL + `admin/instructions/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['instructions'],
    }),
  })
}

export const instructionsApi = globalBaseApi.injectEndpoints({
  endpoints: builder => getInstructionsQueries()(builder),
})
