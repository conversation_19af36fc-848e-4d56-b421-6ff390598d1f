// vite.config.mts
import { defineConfig, loadEnv } from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/vite/dist/node/index.js";
import react from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/@vitejs/plugin-react/dist/index.mjs";
import tsconfigPaths from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/vite-tsconfig-paths/dist/index.mjs";
import handlebars from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/vite-plugin-handlebars/dist/index.js";
import postcssNesting from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/postcss-nesting/dist/index.mjs";
import { viteStaticCopy } from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/vite-plugin-static-copy/dist/index.js";
import fixReactVirtualized from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/esbuild-plugin-react-virtualized/dist/index.mjs";
import { viteCommonjs, esbuildCommonjs } from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/@originjs/vite-plugin-commonjs/lib/index.js";
import { visualizer } from "file:///A:/WORK/SECURE_GITLAB/edu-frontend/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";

// src/shared/utils/colors/colors.ts
var Rgb = class {
  r = 0;
  g = 0;
  b = 0;
  constructor(r, g, b) {
    this.r = r;
    this.g = g;
    this.b = b;
  }
  toString() {
    return `rgb(${Math.round(this.r)}, ${Math.round(this.g)}, ${Math.round(
      this.b
    )})`;
  }
};
function hexToRgb(hex) {
  hex = hex.trimLeft().trimRight().replace("#", "");
  let r = 0, g = 0, b = 0;
  if (hex.length == 3) {
    r = Number("0x" + hex[0] + hex[0]);
    g = Number("0x" + hex[1] + hex[1]);
    b = Number("0x" + hex[2] + hex[2]);
  } else if (hex.length == 6) {
    r = Number("0x" + hex[0] + hex[1]);
    g = Number("0x" + hex[2] + hex[3]);
    b = Number("0x" + hex[4] + hex[5]);
  }
  return new Rgb(r, g, b);
}
function colorMix(color1, color2, amount = 50) {
  const p = amount / 100;
  const r = (color2.r - color1.r) * p + color1.r;
  const g = (color2.g - color1.g) * p + color1.g;
  const b = (color2.b - color1.b) * p + color1.b;
  return new Rgb(r, g, b);
}
function generatePalette(color, palette) {
  const colorRgb = hexToRgb(color);
  const lightBaseRgb = hexToRgb("#ffffff");
  return Object.entries(palette).map(([key, ratio]) => {
    const p = colorMix(lightBaseRgb, colorRgb, ratio);
    return [key, p.toString()];
  }).reduce(
    (all, [key, value]) => ({
      ...all,
      [key]: value
    }),
    {}
  );
}
function generatePrimaryPalette(primary) {
  const palette = {
    primary: 100,
    "primary-90": 88.9,
    "primary-80": 77.9,
    "primary-70": 66.7,
    "primary-60": 55.9,
    "primary-50": 44.5,
    "primary-40": 33.5,
    "primary-30": 22.4,
    "primary-20": 11.1
  };
  return generatePalette(primary, palette);
}

// src/shared/configs/app-config/get-default-branding-config.ts
var getDefaultBrandingConfig = (env = "development") => ({
  id: "secure-t",
  url: "edu.sec-t.ru",
  title: "Secure-T",
  description: "Secure-T",
  logo: {
    light: "https://storage.yandexcloud.net/secure-t-frontend-develop/secure-t.png",
    dark: "https://storage.yandexcloud.net/secure-t-frontend-develop/secure-t.png"
  },
  theme: {
    id: "default",
    colors: {
      ...generatePrimaryPalette("#3dbc87")
    }
  },
  layout: false,
  analytics_enabled: true,
  yandex_metrika_id: "87651235",
  google_analytics_id: "",
  lang: "ru",
  useSSO: env === "offline" || env === "offline-local" || false,
  apiUrl: process.env.API_HOST ?? "https://edu.sec-t.ru",
  needPrivacyPolicyPage: true,
  needAgreementPage: true
});

// vite.config.mts
function i18nHotReload() {
  return {
    name: "i18n-hot-reload",
    handleHotUpdate({ file, server }) {
      if (file.includes("locales") && file.endsWith(".json")) {
        server.ws.send({
          type: "custom",
          event: "locales-update"
        });
      }
    }
  };
}
function getPlugins(mode) {
  const plugins = [
    react(),
    viteCommonjs(),
    tsconfigPaths(),
    viteStaticCopy({
      targets: [
        {
          src: "src/shared/assets/legacy_files/*",
          dest: "assets/legacy_files"
        }
      ]
    }),
    i18nHotReload()
  ];
  if (mode === "report") {
    plugins.push(
      visualizer({
        emitFile: true,
        gzipSize: true,
        filename: "reports/bundle.html"
      })
    );
  }
  if (mode === "development") {
    const brandingConfig = getDefaultBrandingConfig(mode);
    plugins.push(
      handlebars({
        context: {
          ...brandingConfig,
          config_json: JSON.stringify(brandingConfig, null, 4)
        }
      })
    );
  }
  return plugins;
}
var vite_config_default = defineConfig(({ mode }) => {
  process.env = { ...process.env, ...loadEnv(mode, process.cwd(), "") };
  return {
    define: {
      process: {
        env: {
          ...process.env,
          VITE_MANUAL_DEV: !!process.env.VITE_MANUAL_DEV
        }
      },
      VITE_MANUAL_DEV: !!process.env.VITE_MANUAL_DEV
    },
    optimizeDeps: {
      esbuildOptions: {
        plugins: [
          fixReactVirtualized,
          esbuildCommonjs([
            "react-editor-js",
            "@react-editor-js/client",
            "@react-editor-js/server"
          ])
        ]
      }
    },
    build: {
      minify: "esbuild"
    },
    mode,
    server: {
      port: 4200,
      proxy: {
        "/media": {
          target: "https://static.sec-t.ru/",
          changeOrigin: true,
          secure: false
        }
      }
    },
    plugins: getPlugins(mode),
    css: {
      postcss: {
        plugins: [postcssNesting]
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
