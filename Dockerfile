# PREPARATION
FROM node:20.14.0-alpine as preparation
COPY package.json package-lock.json ./
# Create temporary package.json where version is set to 0.0.0
# – this way the cache of the build step won't be invalidated
# if only the version changed.
RUN ["node", "-e", "\
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf-8'));\
    const pkgLock = JSON.parse(fs.readFileSync('package-lock.json', 'utf-8'));\
    fs.writeFileSync('package.json', JSON.stringify({ ...pkg, version: '0.0.0' }));\
    fs.writeFileSync('package-lock.json', JSON.stringify({ ...pkgLock, version: '0.0.0', packages: { ...pkgLock.packages, '': { ...pkgLock.packages[''], version: '0.0.0' }} }));\
    "]

FROM node:20.14.0-alpine as build

WORKDIR /app

COPY --from=preparation package.json package-lock.json ./

RUN rm -f /node_modules

RUN npm i --force

COPY . .

# TODO https://docs.sentry.io/product/sentry-basics/guides/integrate-frontend/upload-source-maps/

ARG VERSION=development
ENV VERSION=${VERSION}

ENV NODE_OPTIONS="--max-old-space-size=4096"

RUN npm run build

RUN npm run server:build

FROM node:20.14.0-alpine as runtime

COPY secure-t.crt /usr/local/share/ca-certificates/
RUN cat /usr/local/share/ca-certificates/secure-t.crt >> /etc/ssl/cert.pem

WORKDIR /app

COPY --from=build /app/dist /app/dist
COPY --from=build /app/server /app/server

ENTRYPOINT [ "node", "/app/server/index.js" ]

