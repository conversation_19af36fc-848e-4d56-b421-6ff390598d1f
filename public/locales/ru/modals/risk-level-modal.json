{"title": "Расчёт уровня риска", "risk_level": "Уровень риска", "phishing": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "learning": "Обучение", "risk_level_points": {"first": "коэффициент для расчёта", "second": "показатель сотрудника по фишингу", "third": "показатель сотрудника по обучению"}, "risk_level_subtitle": "При расчете показателя для организации используются средние значения для <green>E</green> и <yellow>Ф</yellow> по всем сотрудникам", "phishing_points": {"first": "коэффициент веса перехода по ссылке", "second": "коэффициент веса ввода данных", "third": "коэффициент веса открытия вложения", "fourth": "количество векторов, когда пользователь выявил фишинг (т.е. не перешел по ссылке, не ввел данные и не открыл вложение)", "fifth": "общее количество векторов, которые использовались (например, если ушло 1 письмо, в котором была ссылка и вложение, то векторов - 2)", "sixth": "общее количество отправленных писем с вектором 'ссылка'", "seventh": "количество векторов с типом 'ссылка', когда пользователь перешел по ссылке, но не ввел данные", "eighth": "количество векторов с типом 'ссылка', когда пользователь ввел данные", "ninth": "количество отправленных писем с вектором 'вложение'", "tenth": "количество раз, когда пользователь открыл вложение"}, "learning_points": {"first": "признак сданного теста (если сдал, то d = 1, если нет, то d = 0)", "second": "количество неудачных попыток прохождения i-ого теста", "third": "коэффициент веса неправильной попытки", "fourth": "максимальная оценка за тест"}}