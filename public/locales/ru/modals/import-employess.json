{"title": "Подтверждение", "loaded": "Загружено", "employees_data": "Данные о сотрудниках", "employees_create": "Будет добавлено сотрудников: {{count}}", "employees_skip": "Будет пропущено сотрудников (неверный домен): {{count}}", "employees_update": "Будет обновлено сотрудников: {{count}}", "employees_delete": "Будет удалено сотрудников: {{count}}", "departments_add": " Будет добавлено отделов: {{count}}", "departments_delete": "Будет удалено отделов: {{count}}", "delete_employess_and_departments": "Удалить сотрудников и отделы которых нет в таблице", "update_employess_and_departments": "Обновить данные, если сотрудник уже есть на платформе", "import_process": "Процесс импорта", "import_success": "Импорт завершен", "subtitle": "При добавлении удалятся {{employeesCount}} и {{departmentsCount}}, которых нет в загружаемой таблице, но есть на платформе.", "and": " и ", "import_from_file": "Импорт из файла", "get_file": "Выберите файл", "loading": "Загрузка", "change_file": "Заменить файл", "file_format": "формат xlsx, json", "dowload_template": "Скачать шаблон", "cancel_button": "Отменить", "confirm_button": "Подтвердить", "confirm_title": "Тег сбрасывается с тех, кого нет в загружаемом файле, при повторной загрузке файла", "end": "Завершить", "agree": "Подтвердить", "add": "Добавить", "reset": "Сбросить", "employees_interval": "(0-1)[{{count}} сотрудников];(1)[{{count}} сотрудник];(2-4)[{{count}} сотрудника];(5-inf)[{{count}} сотрудников]", "departments_interval": "(0-1)[{{count}} отделов];(1)[{{count}} отдел];(2-4)[{{count}} отдела];(5-inf)[{{count}} отделов]", "mail_interval": "(0-1)[Отправить письмо о регистрации добавленному сотруднику];(2-inf)[Отправить письмо о регистрации добавленным сотрудникам]", "error": {"plain": "Ошибка", "table_loading": "Ошибки при загрузке таблицы", "import": "Произошла ошибка при загрузке", "invalid_extension": "Некорректное расширение файла", "invalid_email": "Неверная почта, проверьте почту со значением {{email}}", "invalid_position": "Длина должности превышает 255 символов для должности {{position}}", "public_api_limit": "Слишком много пользователей для загрузки через публичное апи", "departments_titles_duplicate": "В оргструктуре не может быть двух или более отделов с одинаковым названием", "users_already_exist": "Пользователь с такой почтой уже существует: {{emails}}", "unknown_error": "Произошла непревиденная ошибка", "empty_sheet": "Файл не содержит данных. Пожалуйста, загрузите файл с заполненными данными.", "invalid_headers_upload_file": "В файле обнаружены ошибки в заголовках. Проверьте корректность и загрузите файл повторно.", "invalid_headers_users": "Файл содержит ошибки в заголовках раздела \"Сотрудники\": \"{{invalid}}\"", "invalid_headers_departments": "Файл содержит ошибки в заголовках раздела \"Структура отделов\": \"{{invalid}}\"", "empty_headers_employees": "В файле в разделе \"Сотрудники\" присутствуют пустые заголовки", "empty_headers_departments": "В файле в разделе \"Структура отделов\" присутствуют пустые заголовки"}, "info": {"departments_name": "При загрузке пользователей через таблицу не может быть двух или более отделов с одинаковым названием (даже если они находятся в разных родительских).", "departments_name_uniq": "Название отдела должно быть уникальным."}}