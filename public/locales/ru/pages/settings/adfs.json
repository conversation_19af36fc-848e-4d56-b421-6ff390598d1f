{"sp_entity_id": "это URI (Uniform Resource Identifier), который представляет собой уникальный идентификатор для службы-партнера (Service Provider). Пример: https://www.example.com/sso, https://sso.example.com/myapp", "sp_assertion_consumer_service_url": "адрес, который служба-партнер указывает в метаданных как точку, куда ADFS должна отправлять SAML-подтверждения после успешной аутентификации пользователя. Этот URL-адрес будет обрабатывать SAML-подтверждения и предоставлять доступ к защищенным ресурсам или приложениям на стороне службы-партнера. Пример: https://www.example.com/sso/consumer", "sp_single_logout_service_url": "адре<PERSON>, который служба-партнер указывает в метаданных для обработки запросов на выход из системы. Этот URL-адрес используется для завершения сеансов пользователя на стороне службы-партнера, когда пользователь завершает сеанс на стороне службы идентификации (например, ADFS). Пример: https://www.example.com/slo", "x509_cert": "cертификат X.509, представленный в виде текстовой структуры данных, они обычно выглядят как длинные строки, начинающиеся с \"-----BEGIN CERTIFICATE-----\" и заканчивающиеся \"-----END CERTIFICATE-----\".", "cert_private_key": "это секретный криптографический ключ, который ассоциирован с сертификатом и используется для различных операций шифрования и дешифрования, а также для подписи и проверки подписи данных. Частный ключ обычно представляется в виде длинной последовательности случайных байтов или шестнадцатеричных символов. После сохранения ключа система не будет отображать его в целях безопасности, поле останется с пустой заглушкой.", "idp_entity_id": "уникальный идентификато<PERSON> (URI) службы идентификации (Identity Provider), который используется для однозначной идентификации этой службы в системе единого входа (Single Sign-On, SSO). Пример: https://adfs.example.com/adfs/services/trust", "idp_sso_service_url": "URL-адрес конечной точки, куда служба-партнер (Service Provider) направляет аутентификационные запросы (или запросы на единый вход, SSO) для пользователей, которых необходимо аутентифицировать на службе идентификации (Identity Provider). Пример:https://adfs.example.com/adfs/ls", "idp_single_logout_service_url": "URL-адрес конечной точки, на которую служба-партнер (Service Provider) отправляет запросы на выход из системы (Single Logout) для завершения сеансов пользователей на стороне службы идентификации (Identity Provider). Пример: https://adfs.example.com/adfs/ls/"}