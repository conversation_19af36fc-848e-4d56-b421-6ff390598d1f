{"archive_extension": "Расширение ключа - .key,.der,.pem", "password_if_need": "Пароль (если нужен)", "generalSettings": "Общие настройки платформы", "organization_name": "Название организации", "domain_name": "Название домена", "ssl_certificate": "SSL сертификат", "select_certificate": "Выбрать сертификат", "select_archive": "Выбрать архив", "select_key": "Выб<PERSON>а<PERSON>ь ключ", "replace_certificate": "Заменить сертификат", "replace_key": "Замени<PERSON>ь ключ", "replace_archive": "Заменить архив", "cert_extension": "Расширение сертификата - .pem,.der,.crt", "cert_valid_until": "Сертификат годен до:", "key_extension": "Расширение ключа - .key,.der,.pem", "error_loading_cert_and_key": "Возникла ошибка загрузки сертификата и ключа, попробуйте еще раз...", "error_loading_archive": "Возникла ошибка загрузки архива, попробуйте еще раз...", "error": "Возникла ошибка, попробуйте еще раз...", "password_policy_title": "Парольная политика для операторов и сотрудников", "password_policy_hint": "Минимальные требования к паролю: 8 символов, символы в разных регистрах, цифра или спец символ. При необходимости парольную политику можно сделать более строгой.", "for_operators": "Для опера<PERSON><PERSON><PERSON>ов", "for_employees": "Для сотрудников", "for_operator": "для оператора", "for_employee": "для сотрудника", "min_password_length": "Минимальная длина пароля", "require_digits": "Цифры (0 – 9)", "require_uppercase": "Символы в верхнем регистре", "require_lowercase": "Символы в нижнем регистре", "require_special_chars": "Спец символы ($, #, % и т.д.)", "max_password_age": "Максимальный срок действия пароля", "forbidden_words": "Пароль не включает в себя общепринятые наименования", "forbidden_words_hint": "Например: <PERSON>r, <PERSON><PERSON>, <PERSON> и т.д.", "prevent_sequences": "Пароль не имеет очевидной последовательности символов", "prevent_sequences_hint": "Пароль не должен иметь последовательности символов набираемых в очевидном порядке (например: qwerty, asdfg, 1234567890, poiuy, qazwsxedc и т.д.)", "password_length_error": "Длина пароля не может быть менее 8 символов", "password_digits_or_special_error": "Пароль должен содержать цифры или спец символ", "password_max_age_error": "Максимальный срок действия пароля не может быть более 365 дней", "password_policy_saved_successfully": "Парольная политика {{role}} успешно сохранена"}