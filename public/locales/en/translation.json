{"name": "Name", "work_post": "Position", "department": "Department", "system": "System", "status": "Status", "date": "Date", "1_mon": "1 month", "3_mon": "3 months", "6_mon": "6 months", "year": "Year", "modules": {"assigned_course": {"ahead": "Ahead of plan", "complete": "Complete training", "completed": "Course completed", "course_report": "Course report", "description": "description", "employees_zero": "employees", "employees_one": "employee", "employees_two": "employees", "employees_few": "employees", "employees_many": "employees", "end_date": "end date", "excel_export": "Export to Excel", "far_behind": "Significantly behind the plan", "passed": "course passed", "planned": "Course has not started", "progress_zero": "0 days left", "progress_one": "1 day left", "progress_few": "{{count}} days left", "progress_many": "{{count}} days left", "start_date": "start date", "managment": "Employee management"}, "assigned_course_employees": {"remove_employees": "Remove employees"}, "assigned_courses": {"active": "Active", "assign": "Assign course", "completed": "Completed", "employees_count": "Number of employees", "end_date": "end date", "planned": "Planned", "start_date": "start date"}, "course": {"error": "An error occurred while loading the course"}, "courses": {"completed_courses": "Completed courses", "no_courses": "No active courses", "title": "My courses"}, "employee": {"department": "Department", "email": "E-Mail", "first_name": "First Name", "last_name": "Last Name", "middle_name": "Middle Name", "organization": "Company", "position": "Position"}, "employee_course": {"no_data": "There are no modules in this course"}, "employee_courses": {"courses_report": "Courses report", "excel_export": "Export to Excel", "excel_scorm_export": "Export interactive courses to Excel", "no_data": "No assigned courses", "days_left_zero": "{{count}} days left", "days_left_one": "{{count}} day left", "days_left_few": "{{count}} days left", "days_left_many": "{{count}} days left"}, "employee_statistics": {"clicked": "Link clicks", "details": "Details", "entered_data": "Personal data entered", "note": "Note", "note_no_data": "Add a note about the employee...", "opened": "Opened emails", "passed": "Tests passed"}, "login": {"error": "Unknown error. Please try again later", "login": "<PERSON><PERSON>", "password": "Password", "recovery": "Forgot password?", "sso_login": "Login via AD", "title": "Login to personal account"}, "module": {"error": "An error occurred while loading the training module", "start_test": "Take the test"}, "my_stats": {"clicked": "Link clicks", "entered_data": "Personal data entered", "opened": "Opened emails", "passed": "Tests passed"}, "not_found": {"back_to_main": "Back to home", "title": "This page does not exist"}, "organization": {"childrens": "Child organizations", "employees": "Licenses used", "limit": "Total licenses", "no_childrens": "No child organizations."}, "organization_statistics": {"ahead": "Ahead of plan", "clicked": "Link clicks", "entered_data": "Personal data entered", "export_excel": "Export to Excel", "far_behind": "Significantly behind the plan", "opened": "Opened emails", "phishing_report": "Phishing report", "report_error": "An error occurred while generating the report. Please try again later"}, "password_change": {"back": "Back", "change": "Change", "confirm_password": "Confirm new password", "current_password": "Current password", "different_passwords": "Passwords do not match", "error": "Unknown error. Please try again later", "new_password": "New password", "text": "Enter new password", "title": "Change password"}, "password_recovery": {"continue": "Continue", "error": "Unknown error. Please try again later", "login": "<PERSON><PERSON>", "submit": "Recover", "success_text": "The password recovery link has been sent to", "text": "Enter your E-mail and we will send you a recovery link", "title": "Password Recovery"}, "password_recovery_confirm": {"confirm_password": "Confirm password", "different_passwords": "Passwords do not match", "error": "Unknown error. Please try again later", "login": "<PERSON><PERSON>", "password": "Password", "submit": "Recover", "text": "Enter new password", "title": "Password Recovery"}, "phishing_gotcha": {"attention": "Please be more careful in the future!", "phishing_link": "how to recognize phishing", "text_default": "You have fallen for a phishing attempt - we advise you to retake the course", "text_with_course": "You have fallen for a phishing email. A course will be assigned to you on the training platform, after which you will need to take a test. You will receive an email regarding the course assignment."}, "profile": {"change_password": "change", "department": "DEPARTMENT", "email": "E-MAIL", "exit": "logout", "first_name": "FIRST NAME", "groups": {"ahead": "Ahead of plan", "behind": "Behind plan", "far_behind": "Significantly behind plan", "no_data": "No data available", "normal": "On track"}, "last_name": "LAST NAME", "middle_name": "MIDDLE NAME", "password": "PASSWORD", "position": "POSITION"}, "quiz": {"complete": "Complete the test", "error": "An error occurred while loading the test", "mins": "mins", "next": "Next", "progress": "Question {{current}} of {{max}}", "timer_one": "left", "timer_two": "left", "timer_few": "left", "timer_many": "left", "test": {"key_zero": "zero", "key_one": "singular", "key_two": "two", "key_few": "few", "key_many": "many", "key_other": "other"}}, "quiz_result": {"back_to_learning": "Back to learning", "back_to_test": "Complete the test", "error": "An error occurred while loading the test results", "fail": "Failed", "result": "You answered correctly {{correct_answers}} out of {{total}} questions", "result_repeated": "You answered correctly {{correct_answers}} out of {{total}} questions, retake the material and try again", "success": "Congratulations!"}, "registration": {"back_to_main": "To main", "confirm": "Complete", "confirm_password": "Repeat password", "different_passwords": "Passwords do not match", "error": "Unknown error. Please try again later", "first_name": "First Name", "incorrect_link": "Registration link is invalid", "last_name": "Last Name", "middle_name": "Middle Name", "password": "Password", "position": "Position", "text": "Fill out your profile", "title": "Get Started"}, "start_test_button": {"go": "Take the test"}, "statistics": {"ahead": "Ahead of plan", "clicked": "Link clicks", "entered_data": "Personal data entered", "export_excel": "Export to Excel", "far_behind": "Significantly behind the plan", "opened": "Opened emails", "phishing_report": "Phishing report", "progress_report": "Progress report", "report_error": "An error occurred while generating the report. Please try again later", "new_theory": "Theory", "new_testing": "Testing"}, "store_courses": {"tabs": {"old": "Module-based", "scorm": "Interactive"}, "create_course": "Create course"}, "store_modules": {"add_module": "Add module"}, "hint_report_modal": {"loader_title": "Generating report"}}, "audit": {"export_button": "Export to Excel", "table": {"action": "Action", "date": "Date", "details": "Details", "ip": "IP", "user": "Employee"}, "types": {"admin": "Administration", "auth": "Authorization"}}, "common": {"code-editor": {"format": "Format", "html_tab": "HTML", "open_in_new_window_tab": "Open in new window"}, "date_picker": {"select_placeholder": "Time", "submit_button": "Select"}, "paginator": {"next_button": "Next", "page": "Page", "prev_button": "Back"}, "file-upload": {"choose_file": "Upload file", "replace_file": "Replace file"}}, "default_snackbar_action": "Ok", "errors": {"loading": {"text": "Check your internet connection and try again later.", "title": "An error occurred while loading."}, "startup": "An error occurred during startup. Please try again later."}, "components": {"course": {"modules_zero": "{{count}} modules", "modules_one": "{{count}} module", "modules_few": "{{count}} modules", "modules_many": "{{count}} modules", "progress-passed": "Passed", "progress-other": "{{progress}}% passed", "status-completed": "Completed", "status_zero": "{{count}} days left", "status_one": "1 day left", "status_few": "{{count}} days left", "status_many": "{{count}} days left"}, "departments_table": {"clicked_tooltip": "Link clicks", "columns": {"employees_count": "Employees", "progress": "Progress", "risk_level_change": "Changes", "title": "Department"}, "entered_data_tooltip": "Personal data entered", "sent_suffix": "pcs.", "sent_tooltip": "Emails sent"}, "editable_audio": {"no_audio": "No audio selected"}, "employee_course": {"learning": "Theory: {{progress}}%", "testing": "Testing: {{progress}}%"}, "employees_table": {"clicked_tooltip": "Link clicks", "columns": {"name": "Full Name", "progress": "Progress"}, "entered_data_tooltip": "Personal data entered", "sent_suffix": "pcs.", "sent_tooltip": "Emails sent"}, "module_with_attempts": {"attempt": "Attempt ", "attempts": "Attempts:", "date": "Date:", "duration": "Duration:", "h": "h", "in_progress": "In progress", "learning": "Theory:", "m": "m", "progress-passed": "Passed", "progress-not": "Not passed", "progress": "{{- progress}}", "s": "s", "score": "Score:", "testing": "Testing:"}, "organization": {"licenses": "licenses"}, "preview_button": {"preview": "Preview"}, "question": {"subtitle": "Select the correct option:"}, "quiz_review": {"correct": "Correct answer", "incorrect": "Incorrect answer", "question": "Question", "unselected": "Unselected correct answer"}, "store_course": {"modules_zero": "No modules", "modules_one": "{{count}} module", "modules_few": "{{count}} modules", "modules_many": "{{count}} modules"}}, "dialogs": {"answers": {"add_answer": "add answer", "answer": "Answer", "text": "Answer text", "tip": "Explanation for the answer"}, "template_choose": {"action_button": "Add templates", "no_category": "Without category", "no_data": "Templates not found", "search_placeholder": "Search templates", "title": "Adding templates"}, "new_assign_course": {"title": "Select course", "select": "Select", "back": "Cancel"}, "assign_employees": {"assign": "Assign", "assign_tag": "Assign tag", "selected": "Selected", "not_selected": "Not selected", "import": "Upload employee list as a table"}, "assign_course": {"1": {"back": "Back", "next": "Next", "title": "Select course"}, "2": {"assign": "Assign", "back": "Back", "choose_employees": "Select employees", "count": {"departments": "{count, plural, one{{count} department} few{{count} departments} other{{count} departments}}", "employees": "{count, plural, one{{count} employee} few{{count} employees} other{{count} employees}}"}, "date": "Course start date", "description": "Course description", "name": "Course name", "period": "Course duration (days)", "picture": "Image", "title": "Course settings", "unselected": "Not selected"}}, "assign_learning": {"from_course": "Select ready course", "from_modules": "Assemble from modules", "or": "or", "title": "Learning assignment"}, "assign_modules": {"assign": "Assign", "back": "Back", "choose_employees": "Select employees", "choose_modules": "Select modules", "count": {"departments_one": "{{count}} department", "departments_few": "{{count}} departments", "departments_many": "{{count}} departments", "employees_one": "{{count}} employee", "employees_few": "{{count}} employees", "employees_many": "{{count}} employees"}, "description": "Course description", "modules_zero": "Not selected", "modules_one": "{{count}} module", "modules_few": "{{count}} modules", "modules_many": "{{count}} modules", "name": "Course name", "notselected": "Not selected", "period": "Course duration (days)", "picture": "Image", "start_date": "Course start date", "title": "Course creation", "need_assigned_messages": "Send course assignment notification", "need_notify_messages": "Notify lagging users about the course"}, "continue_incompleted_course": {"continue": "Continue", "start_new": "Start new test", "title": "You have an unfinished test. Continue it?"}, "create_course": {"name_required": "Fill out this field", "add_modules": "Adding modules", "description": "Description", "image": "Image", "course": "Upload a course archive (cmi5 format)", "name": "Course name", "title": "Creating a new course"}, "create_employee": {"cancel": "Cancel", "content_manager": "Content manager", "create": "Adding employee", "create_error": "An error occurred while creating the employee. Check the entered values", "csv_error": "An error occurred while uploading the table. Check the table formatting", "download_template": "Download template", "edit": "Editing employee", "edit_error": "An error occurred while saving the employee. Check the entered values", "employee": "Employee", "first_name": "First Name", "last_name": "Last Name", "middle_name": "Middle Name", "operator": "Operator", "position": "Position", "save": "Save", "upload_xlsx": "Upload xlsx"}, "create_module": {"1": {"description": "Description", "name": "Module name", "next": "Next", "title": "Creating a new module"}, "2": {"go_to_test": "Go to test setup", "title": "Adding slides", "without_test": "Create without test"}, "3": {"passing": "Number of correct answers required", "timer": "Time to complete the course (min)", "title": "Testing setup", "total": "Number of questions"}}, "create_question": {"answers": "Answers", "multiple": "Multiple correct answers", "text": "Question text", "title": "Creating a new question"}, "create_quiz": {"cancel": "Cancel", "passing": "Number of correct answers required", "save": "Save", "timer": "Time to complete the course (min)", "title": "Testing setup", "total": "Number of questions"}, "create_slide": {"text": "Slide text", "title": "Creating a new slide"}, "delete_confirm": {"cancel": "Cancel", "delete": "Delete", "title": "Are you sure you want to delete this item? The statistics for this course will be removed from the calculation."}, "replace_slide_confirm": {"cancel": "Cancel", "confirm": "Move", "title": "Are you sure you want to move the slide?"}, "phishing_campaign_detail_delete": {"title": "Are you sure you want to delete the mailing? The statistics for this mailing will be removed from the calculation."}, "download": {"download": "Download", "title": "Download"}, "edit_assigned_course": {"cancel": "Cancel", "description": "Description", "end_date": "Course end date", "image": "Image", "name": "Course name", "save": "Save", "start_date": "Course start date", "title": "Editing course"}, "edit_course": {"cancel": "Cancel", "name_required": "Fill out this field", "description": "Description", "image": "Image", "name": "Course name", "save": "Save", "title": "Editing course", "titleView": "Viewing course information"}, "edit_module": {"add_test": "Add test", "cancel": "Cancel", "configure_test": "Configure test", "description": "Description", "name": "Module name", "questions_zero": "Test not added", "questions_one": "{{count}} question", "questions_few": "{{count}} questions", "questions_many": "{{count}} questions", "save": "Save", "title": "Editing module"}, "edit_question": {"answers": "Answers", "cancel": "Cancel", "multiple": "Multiple correct answers", "save": "Save", "text": "Question text", "title": "Editing question"}, "edit_quiz": {"cancel": "Cancel", "passing": "Number of correct answers required", "save": "Save", "timer": "Time to complete the course (min)", "title": "Testing setup", "total": "Number of questions"}, "edit_slide": {"cancel": "Cancel", "save": "Save", "text": "Slide text", "title": "Editing slide"}, "employees_in_group": {"group": "Group:", "load_more": "Load more", "loading": "Loading", "notify": "Notify"}, "import_questions_from_excel": {"download_template": "Download template", "file": "Excel file with questions", "import": "Import", "title": "Import questions"}, "modules_selector": {"add_modules": "add modules"}, "notify_employees": {"cancel": "Cancel", "group": "Group:", "send": "Send", "text": "Enter notification text", "title": "Employee notification", "empty": "No employees in this group"}, "questions_selector": {"add_question": "add question", "or": "or", "question": "Question", "upload_from_excel": "upload from excel"}, "slides_selector": {"import_from_pptx": "Import presentation file (pptx, pdf)", "slide": "slide"}, "store_module_choose": {"choose": "Select", "no_slides": "Add at least one slide to the module", "title": "Select modules"}, "target_choose": {"assign": "Assign", "count": {"departments": "{count, plural, one{{count} department} few{{count} departments} other{{count} departments}}", "employees": "{count, plural, one{{count} employee} few{{count} employees} other{{count} employees}}"}, "departments_search": "Search departments", "employees_search": "Search employees", "fetch_more": "Load more", "select_all": "Select all", "title": "Learning assignment"}, "target_choose_modal": {"title": "Select employees", "cancel_selection": "Cancel selection", "select_all": "Select all", "error_message": "An unexpected error occurred", "not_found": "No results found :(", "cancel": "Cancel", "delete": "Delete", "select": "Select", "no_employees": "No employees", "search_placeholder": "Quick search"}, "ad-sync": {"apply": "Apply", "complete": "Complete", "delete-checkbox": "Delete employees and departments not in Active Directory", "departments": {"add": "Departments to be added:", "delete": "Departments to be deleted:", "update": "Departments to be updated:"}, "employees": {"add": "Employees to be added:", "delete": "Employees to be deleted:", "update": "Employees to be updated:"}, "employees-data": "Employee data", "filters-filter-string": "Filter String:", "filters-search-dn": "Search DN:", "filters-title": "Active Directory Filters", "progress": "Synchronized { current } of { total } employees", "resync-button": "Re-sync", "sync-button": "Synchronize", "sync-completed": "Synchronization completed", "sync-error": "Error during synchronization", "title": "Active Directory", "update-checkbox": "Update existing employee data", "invites-checkbox": "Send invitation emails to new users"}, "filter_modal": {"title": "General", "status": "Status", "system": "System", "not_important": "Not important", "apply": "Apply", "clear": "Clear"}, "common": {"cancel": "Cancel"}, "course_choose": {"action_button": "Add course"}, "create_attachment": {"action_button": "Add", "filename": "File name", "filetype": "File type", "filetype_placeholder": "Select type...", "title": "Adding attachment"}, "delete": {"confirm_button": "Delete"}, "report": {"download": "Download"}, "report_modal": {"close": "Close", "cancel": "Cancel", "download": "Download", "report_generate": "Generating report", "report_generated": "Report generated", "certificate": "Certificate", "report": "Report", "will_be_sent": "will be sent to email", "error": "An unexpected error occurred", "loader_title": "Generating report", "export_report": "Export report"}, "sso_login": {"confirm": "<PERSON><PERSON>", "login": "<PERSON><PERSON>", "password": "Password", "title": "Login via Active Directory"}, "users_choose": {"action_button": "Add mailing targets", "search_placeholder": "Search", "selected_count": "{count, plural, one{selected {count} person} few{selected {count} people} other{selected {count} people}}", "title": "Choosing mailing targets"}}, "departments": {"choosed_zero": "{{count}} departments selected", "choosed_one": "{{count}} department selected", "choosed_few": "{{count}} departments selected", "choosed_many": "{{count}} departments selected"}, "old": {"back_button": {"back": "Back"}, "data_item": {"not_found": "Currently, there are no active courses"}, "departments_list": {"new_department": "New department..."}, "employees_list": {"no_data": "Not found"}, "file_uploader": {"files": "{count} {count, plural, one{file} few{files} other{files}}"}, "list": {"error": "An error occurred while loading data", "no_data": "Not found"}, "select": {"choose": "Select"}}, "use_auth": {"error": "Unknown error. Please try again later"}, "opened": "Opened", "phishing": {"campaign": {"assigned_course_target": {"clicked": "Those who clicked the link:", "entered_data": "Those who entered data:", "opened": "Those who opened the email:", "opened_attachment": "Those who opened the attachment:"}, "assigned_course_title": "The course will be assigned after the mailing is completed:", "delete_subject": "the mailing", "incident": "Incident", "info_card_value": "{value} of {total}", "info_cards": {"clicked": "<PERSON>licks", "entered_data": "Data entered", "opened": "Opened", "opened_attachment": "Attachments opened", "sent": "<PERSON><PERSON>"}, "users_count": "people", "users_table": "Employee table"}, "campaign_statistics": {"export_button": "Export to Excel", "incident": "Incident", "table": {"department": "Department", "incident_risk": "Caught in phishing", "name": "Name", "position": "Position", "status": "Status", "system": "System", "template": "Popular template", "users": "Employees"}, "users_count": "people", "clear": "Clear", "filters": "Filters", "search_placeholder": "Enter Full Name / position / email..."}, "campaigns": {"autophish": "Automatic mailing", "completed_tab": "Completed", "no_data_button": "Create new mailing", "no_data_title": "You have no mailings."}, "create_email": {"attachments_placeholder": "Add attachments", "name": "Name", "sender_placeholder": "Enter sender", "subject_placeholder": "Enter subject"}, "create_page": {"title": "Creating page"}, "create_template": {"category": "Template category", "category_placeholder": "Select category...", "create_button": "Create template", "name": "Template name", "name_placeholder": "Enter template name...", "no_category": "Without category", "original_link": "Link to the original site", "original_link_placeholder": "Enter link...", "title": "Creating new template", "url": "Phishing link", "url_placeholder": "Enter link..."}, "edit_email": {"attachments_placeholder": "Add attachments", "name": "Name", "sender_placeholder": "Enter sender", "subject_placeholder": "Enter subject"}, "edit_page": {"title": "Editing page"}, "edit_template": {"category": "Template category", "category_placeholder": "Select category...", "name": "Template name", "name_placeholder": "Enter template name...", "no_category": "Without category", "original_url": "Link to the original site", "original_url_placeholder": "Enter template name...", "url": "Phishing link", "url_placeholder": "Enter link..."}, "snacks": {"autophish": "Autophishing successfully {enabled, select, true{enabled} false{disabled} other{}}", "copy_template": "Successfully created a copy of the template", "create_campaign": "Mailing successfully created", "create_email": "Email successfully created", "create_page": "<PERSON> successfully created", "create_template": "Template successfully created", "delete_campaign": "Mailing successfully deleted", "delete_email": "Email successfully deleted", "delete_template": "Template successfully deleted", "edit_email": "Email successfully updated", "edit_page": "Page successfully updated", "edit_template": "Template successfully updated", "stop_campaign": "Mailing successfully completed"}, "template": {"create_page_button": "Create page", "delete_email_subject": "email", "delete_template_subject": "template", "emails_title": "Emails", "statistics": {"entered_data": "Caught in phishing", "no_attachments": "This template has no emails with attachments", "sent": "Total emails"}}, "templates": {"no_category": "Without category"}, "user_actions": {"clicked": "Clicked the link", "entered_data": "Entered data", "opened": "Opened email", "opened_attachment": "Opened attachment", "sent": "Did not open email", "unknown": "Not sent", "unknown_text": "Unknown error"}}, "reports": {"audit": "Export \"{type, select, learning{Training} phishing{Phishing} auth{Authorization} admin{Administration} other{}}\"", "default_title": "Export report", "statuses": {"complete": "Report generated", "error": "Failed to generate report", "loading": "Generating report"}}, "user_role": "Employee", "settings": {"title": "General platform settings", "tabs": {"organization": "Basic", "ldap": "Active Directory", "email": "Email"}, "ldap": {"title": "Server URL", "use_tls": "Use TLS", "bind_dn": "Bind DN", "password": "Password", "search_dn": "Search DN", "filter_string": "Filter string", "sso_login": "Login via SSO", "keytab_file": "Keytab file", "success_update": "LDAP settings updated successfully"}, "organization": {"title": "Organization name", "url": "Platform domain", "ssl": "SSL certificate", "ssl_info": {"first_tab": "Certificate + Key", "helps": {"archive": "Archive extensions - .pkcs12, .pfx, .p12", "cert": "Certificate extensions - .pem, .der.", "key": "Key extensions - .key, .pem, .der."}, "second_tab": "Archive", "unselected_key_file": "Select key", "selected_key_file": "Replace key", "unselected_certificate_file": "Select certificate", "selected_certificate_file": "Replace certificate", "unselected_archive_file": "Select archive", "selected_archive_file": "Replace archive", "ssl_valid_date": "Certificate valid until ", "password_placeholder": "Password (if needed)"}, "success_update": "Settings updated successfully"}, "smtp": {"host": "Host", "email_from": "Email from", "use_tls": "Use TLS", "anonymous_authentication": "Anonymous authentication", "login": "<PERSON><PERSON>", "password": "Password", "success_update": "SMTP settings updated successfully"}}}