{"sp_entity_id": "This is a URI (Uniform Resource Identifier) that represents a unique identifier for the Service Provider. Example: https://www.example.com/sso, https://sso.example.com/myapp", "sp_assertion_consumer_service_url": "The address that the Service Provider specifies in the metadata as the endpoint where ADFS should send SAML assertions after successful user authentication. This URL will handle SAML assertions and provide access to protected resources or applications on the Service Provider's side. Example: https://www.example.com/sso/consumer", "sp_single_logout_service_url": "The address that the Service Provider specifies in the metadata for handling logout requests. This URL is used to terminate user sessions on the Service Provider side when the user logs out on the Identity Provider side (e.g., ADFS). Example: https://www.example.com/slo", "x509_cert": "An X.509 certificate represented in a text data structure, usually appearing as long strings that start with '-----BEGIN CERTIFICATE-----' and end with '-----END CERTIFICATE-----'.", "cert_private_key": "This is a secret cryptographic key associated with the certificate, used for various encryption and decryption operations, as well as for signing and verifying data signatures. The private key is typically represented as a long sequence of random bytes or hexadecimal characters. After the key is saved, the system will not display it for security reasons; the field will remain as an empty placeholder.", "idp_entity_id": "A unique identifier (URI) for the Identity Provider, used for unambiguous identification of this service in the Single Sign-On (SSO) system. Example: https://adfs.example.com/adfs/services/trust", "idp_sso_service_url": "The URL endpoint to which the Service Provider sends authentication requests (or SSO requests) for users that need to be authenticated by the Identity Provider. Example: https://adfs.example.com/adfs/ls", "idp_single_logout_service_url": "The URL endpoint to which the Service Provider sends Single Logout requests to terminate user sessions on the Identity Provider side. Example: https://adfs.example.com/adfs/ls/"}