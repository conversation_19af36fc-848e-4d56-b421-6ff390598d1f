{"archive_extension": "Key extension - .key, .der, .pem", "password_if_need": "Password (if needed)", "generalSettings": "General platform settings", "organization_name": "Organization name", "domain_name": "Domain name", "ssl_certificate": "SSL certificate", "select_certificate": "Select certificate", "select_archive": "Select archive", "select_key": "Select key", "replace_certificate": "Replace certificate", "replace_key": "Replace key", "replace_archive": "Replace archive", "cert_extension": "Certificate extension - .pem, .der, .crt", "cert_valid_until": "Certificate valid until:", "key_extension": "Key extension - .key, .der, .pem", "error_loading_cert_and_key": "Error loading certificate and key, please try again...", "error_loading_archive": "Error loading archive, please try again...", "error": "An error occurred, please try again...", "password_policy_title": "Password policy for operators and employees", "password_policy_hint": "Minimum password requirements: 8 characters, characters in different cases, a digit or special character. If necessary, the password policy can be made stricter.", "for_operators": "For operators", "for_employees": "For employees", "for_operator": "for operator", "for_employee": "for employee", "min_password_length": "Minimum password length", "require_digits": "Digits (0 – 9)", "require_uppercase": "Uppercase characters", "require_lowercase": "Lowercase characters", "require_special_chars": "Special characters ($, #, % etc.)", "max_password_age": "Maximum password validity period", "forbidden_words": "Password does not include common names", "forbidden_words_hint": "For example: User, Admin, Root, etc.", "prevent_sequences": "Password does not have an obvious sequence of characters", "prevent_sequences_hint": "The password should not have a sequence of characters typed in an obvious order (for example: qwerty, asdfg, 1234567890, poiuy, qazwsxedc, etc.)", "password_length_error": "Password length cannot be less than 8 characters", "password_digits_or_special_error": "Password must contain digits or special characters", "password_max_age_error": "Maximum password validity period cannot be more than 365 days", "password_policy_saved_successfully": "Password policy {{role}} successfully saved"}