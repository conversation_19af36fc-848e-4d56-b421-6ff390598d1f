{"breadcrumbs": {"assigned_courses": "Assigned Courses", "assigning": "Course Assignment", "assign_course": "Assign Course", "assigning_public": "Public Course Assignment", "assign_course_public": "Public Course Assignment"}, "title": "Course Assignment", "title_public": "Public Course Assignment", "form": {"choose_course": "Choose a Course", "add_course": "Add Course", "description": "Course Description", "start_date": "Course Start Date", "date_choose": "<PERSON>ose <PERSON>", "start_date_placeholder": "Select Course Start Date", "end_date": "Course End Date", "end_date_placeholder": "Select Course End Date", "picture_change": "Change Cover", "notifications": {"assign": "Send notification about course assignment", "lagging": "Notify lagging students about the course", "title": "Who to assign the course to"}, "editing": {"title": "Course Title", "description": "Course Description"}}, "select_targets": "Select Targets", "learning": {"start": "Course Start Date", "end": "Course End Date"}, "actions": {"assing": "Assign", "back": "Back"}, "validation": {"courses_required": "Please choose a course!", "end_date_required": "Please choose an end date for the course!", "start_date_required": "Please choose a start date for the course!", "targets_required": "Please select targets!", "start_date_gt_end": "The start date of the course cannot be later than the end date!", "no_themes": "Courses without themes cannot be assigned."}, "tooltip": {"no_themes": "This course cannot be assigned, number of themes: 0"}, "success": "The course will be assigned. To view the course card, please refresh the page.", "rules": {"1": "- 1000 employees will be added at once, the rest will be added 100 people every 100 seconds;", "2": "- notifications will be sent according to the following schedule: for each added user, an email will be sent after:", "3": "current time + X minutes, where X = 2 * (Random number from 1 to 20)"}}