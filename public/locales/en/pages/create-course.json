{"enter_description": "Enter description", "tag": "Tag", "ready_theme": "Select ready", "new_theme": "Create new", "replace_all_themes": "Move all themes", "themes": "Themes", "add_image": "Add cover", "section_name": "Section name", "enter_tag_name": "Enter tag name", "tag_already_exist": "Tag already exists", "add_section": "Add section", "section": "Section", "move_another_section": "Move to another section", "settings_title": "Course settings", "public_course": "Public course", "public_course_description": "The course can be taken by any user", "sequence_course": "Must be taken in a specified sequence", "sequence_description": "Sections cannot be taken out of order", "delete_all_theme": "Deleting themes in", "delete_all_theme_description": "This action will permanently delete all themes along with the section.", "delete_last_theme_description": "This action will permanently delete all themes, <strong>except the first</strong>, in this section.", "delete_theme": "Delete theme?", "delete_theme_description": "This action will permanently delete the theme.", "delete_theme_and_section_description": "This action will permanently delete the theme along with the section.", "delete_sections": "Delete sections in the course?", "delete_sections_description": "All sections will be deleted, except the first one that has themes.", "replace_all_theme": "Move all themes to", "delete_theme_not_possible_title": "The theme cannot be deleted", "delete_theme_not_possible_description": "It is not possible to leave the course without content", "description_replace_all_theme": "This action will move all themes from one section to another", "delete_section_with_last_themes": "The last section with themes cannot be deleted", "replace": "Move", "select_ready_theme": "Select ready themes", "fast_search": "Quick search", "themes_choosed_zero": "Will be added {{count}} themes", "themes_choosed_one": "Will be added {{count}} theme", "themes_choosed_few": "Will be added {{count}} themes", "themes_choosed_many": "Will be added {{count}} themes", "validation_error": "Fill in mandatory fields.", "validation_title_error": "Course title must be filled", "validation_section_title_error": "Section title must be filled", "validation_theme_error": "Cannot create a course without a theme", "validation_theme_in_section_error": "Cannot create a section without a theme", "delete_section_description": "This action will delete", "no_steps_in_theme": "There are currently no steps added in the selected theme", "cancel_title": "Cancel changes?", "cancel_description": "All changes will be lost", "reset": "Reset", "tags_delete_title": "Delete existing tags?", "hold_to_move": "Hold to move"}