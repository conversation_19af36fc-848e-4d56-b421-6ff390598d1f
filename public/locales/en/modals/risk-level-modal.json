{"title": "Risk Level Calculation", "risk_level": "Risk Level", "phishing": "<PERSON><PERSON>", "learning": "Learning", "risk_level_points": {"first": "coefficient for calculation", "second": "employee's phishing indicator", "third": "employee's learning indicator"}, "risk_level_subtitle": "The organization-wide indicator is calculated using average values for <green>E</green> and <yellow>P</yellow> across all employees", "phishing_points": {"first": "weight coefficient for clicking the link", "second": "weight coefficient for entering data", "third": "weight coefficient for opening an attachment", "fourth": "number of vectors where the user identified phishing (i.e. did not click the link, did not enter data, and did not open the attachment)", "fifth": "total number of vectors used (e.g., if one email contained both a link and an attachment, there are 2 vectors)", "sixth": "total number of emails sent with a 'link' vector", "seventh": "number of 'link' vectors where the user clicked the link but did not enter data", "eighth": "number of 'link' vectors where the user entered data", "ninth": "number of emails sent with an 'attachment' vector", "tenth": "number of times the user opened an attachment"}, "learning_points": {"first": "test completion flag (if passed, d = 1; if not, d = 0)", "second": "number of failed attempts for the i-th test", "third": "weight coefficient for a wrong attempt", "fourth": "maximum score for the test"}}