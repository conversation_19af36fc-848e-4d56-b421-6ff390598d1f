{"title": "Confirmation", "loaded": "Loaded", "employees_data": "Employees Data", "employees_create": "Employees to be added: {{count}}", "employees_skip": "Employees to be skipped (invalid domain): {{count}}", "employees_update": "Employees to be updated: {{count}}", "employees_delete": "Employees to be deleted: {{count}}", "departments_add": "Departments to be added: {{count}}", "departments_delete": "Departments to be deleted: {{count}}", "delete_employess_and_departments": "Delete employees and departments not in the table", "update_employess_and_departments": "Update data if the employee already exists on the platform", "import_process": "Import Process", "import_success": "Import completed", "subtitle": "When adding, {{employeesCount}} and {{departmentsCount}}, which are not in the uploaded table but are on the platform, will be deleted.", "and": " and ", "import_from_file": "Import from file", "get_file": "Choose file", "loading": "Loading", "change_file": "Replace file", "file_format": "xlsx, json format", "dowload_template": "Download template", "cancel_button": "Cancel", "confirm_button": "Confirm", "confirm_title": "The tag will be removed from those not in the uploaded file when the file is re-uploaded", "end": "Complete", "agree": "Confirm", "add": "Add", "reset": "Reset", "employees_interval": "(0-1)[{{count}} employee];(1)[{{count}} employee];(2-4)[{{count}} employees];(5-inf)[{{count}} employees]", "departments_interval": "(0-1)[{{count}} departments];(1)[{{count}} department];(2-4)[{{count}} departments];(5-inf)[{{count}} departments]", "mail_interval": "(0-1)[Send registration email to added employee];(2-inf)[Send registration email to added employees]", "error": {"plain": "Error", "table_loading": "Errors loading the table", "import": "An error occurred during upload", "invalid_extension": "Invalid file extension", "invalid_email": "Invalid email, check the email with value {{email}}", "invalid_position": "Position length exceeds 255 characters for position {{position}}", "public_api_limit": "Too many users to upload via public API", "departments_titles_duplicate": "The organizational structure cannot have two or more departments with the same name", "users_already_exist": "User with this email already exists: {{emails}}", "unknown_error": "An unexpected error occurred", "empty_sheet": "The file contains no data. Please upload a file with filled data.", "invalid_headers_upload_file": "Errors found in file headers. Check correctness and upload the file again.", "invalid_headers_users": "File contains errors in \"Employees\" section headers: \"{{invalid}}\"", "invalid_headers_departments": "File contains errors in the \"Department structure\" section headers: \"{{invalid}}\"", "empty_headers_employees": "The file contains empty headers in the \"Employees\" section", "empty_headers_departments": "The file contains empty headers in the \"Department structure\" section"}, "info": {"departments_name": "When uploading users via a table, there cannot be two or more departments with the same name (even if they are in different parents).", "departments_name_uniq": "Department name must be unique."}}