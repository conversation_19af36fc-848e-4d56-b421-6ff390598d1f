{"title": "Tasdiqlash", "loaded": "Yuklandi", "employees_data": "<PERSON>od<PERSON><PERSON> ma'l<PERSON>i", "employees_create": "Qo'shil<PERSON>gan xodimlar soni: {{count}}", "employees_skip": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> y<PERSON><PERSON><PERSON><PERSON> xodimlar soni (noto'g'ri domen): {{count}}", "employees_update": "Yangilanadigan xodimlar soni: {{count}}", "employees_delete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> xodimlar soni: {{count}}", "departments_add": "<PERSON>o's<PERSON><PERSON><PERSON> bo'limlar soni: {{count}}", "departments_delete": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bo'lim<PERSON> soni: {{count}}", "delete_employess_and_departments": "<PERSON><PERSON><PERSON><PERSON> bo'l<PERSON><PERSON> xodimlar va bo'l<PERSON><PERSON><PERSON> o'chirish", "update_employess_and_departments": "<PERSON><PERSON><PERSON> all<PERSON>on <PERSON>ada mavjud bo'l<PERSON>, ma'l<PERSON><PERSON><PERSON><PERSON> yang<PERSON>sh", "import_process": "Import jarayoni", "import_success": "Import yakunlandi", "subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, y<PERSON><PERSON><PERSON><PERSON><PERSON> jad<PERSON> bo'<PERSON><PERSON><PERSON>, lekin <PERSON>ada mavjud bo'lgan {{employeesCount}} va {{departmentsCount}} o'chiriladi.", "and": " va ", "import_from_file": "Fayldan import", "get_file": "<PERSON><PERSON><PERSON>", "loading": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "change_file": "<PERSON><PERSON><PERSON>", "file_format": "xlsx, json formati", "dowload_template": "<PERSON><PERSON><PERSON><PERSON> yuk<PERSON> o<PERSON>", "cancel_button": "Bekor qilish", "confirm_button": "Tasdiqlash", "confirm_title": "Yuklanayotgan faylda bo'l<PERSON>gan xodim<PERSON>an teg olib ta<PERSON>, fayl qayta yuk<PERSON>anda", "end": "Ya<PERSON><PERSON><PERSON>", "agree": "Tasdiqlash", "add": "<PERSON><PERSON><PERSON><PERSON><PERSON>h", "reset": "<PERSON><PERSON><PERSON>'<PERSON>", "employees_interval": "(1)[{{count}} xodim];(2-inf)[{{count}} xodimlar]", "departments_interval": "(1)[{{count}} bo'lim];(2-inf)[{{count}} bo'limlar]", "mail_interval": "(1)[Qo'shilgan xodimga ro'yxatdan o'tish xatini yuborish];(2-inf)[Qo'shilgan xodimlarga ro'yxatdan o'tish xatini yuborish]", "error": {"plain": "Xatolik", "table_loading": "<PERSON><PERSON><PERSON><PERSON> yuk<PERSON>atolik<PERSON>", "import": "Yuklashda xatolik yuz berdi", "invalid_extension": "Noto'g'ri fayl kengay<PERSON>i", "invalid_email": "Noto'g'ri email, {{email}} qiymatiga ega emailni tekshiring", "invalid_position": "Lavozim uzunligi {{position}} lavozimi uchun 255 belgidan oshib ketdi", "public_api_limit": "Ochiq API orqali yuklash uchun juda ko'p foydalanuv<PERSON>lar", "departments_titles_duplicate": "Tash<PERSON>liy tuzilmada bir xil nomli ikki yoki undan ortiq bo'limlar bo'lishi mumkin emas", "users_already_exist": "B<PERSON>y emailga ega foydalanuvchi allaqachon mavjud: {{emails}}", "unknown_error": "Kutilmagan xatolik yuz berdi", "empty_sheet": "<PERSON><PERSON> ma'lumot o'z ichiga o<PERSON>ydi. <PERSON><PERSON><PERSON>, to'l<PERSON><PERSON><PERSON> ma'lumotlar bilan faylni yuklang.", "invalid_headers_upload_file": "<PERSON><PERSON> xato<PERSON> topildi. To'g'riligini tekshiring va faylni qayta yuklang.", "invalid_headers_users": "<PERSON><PERSON> \"<PERSON>od<PERSON><PERSON>\" bo'limi sa<PERSON><PERSON><PERSON> x<PERSON> o'z ichiga oladi: \"{{invalid}}\"", "invalid_headers_departments": "<PERSON><PERSON> \"Bo'limlar tuzil<PERSON>i\" bo'limi sarlav<PERSON> xatoliklar mavjud: \"{{invalid}}\"", "empty_headers_employees": "<PERSON><PERSON> \"<PERSON><PERSON><PERSON><PERSON>\" bo'limida bo'sh sarl<PERSON><PERSON>r mavjud", "empty_headers_departments": "<PERSON><PERSON> \"<PERSON><PERSON>l<PERSON><PERSON> tuz<PERSON>\" bo'limida bo'sh sarlav<PERSON>r mavjud"}, "info": {"departments_name": "<PERSON><PERSON><PERSON> or<PERSON><PERSON> foydalanuvchilarni yuklashda bir xil nomli ikki yoki undan ortiq bo'limlar bo'lishi mumkin emas (hatto ular turli ota-bo'lim<PERSON>a bo'lsa ham).", "departments_name_uniq": "<PERSON>'lim nomi noyob bo'lishi kerak."}}