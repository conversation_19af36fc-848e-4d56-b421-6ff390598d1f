{"sp_entity_id": "bu <PERSON><PERSON> (Uniform Resource Identifier) bo'lib, xizmat-ham<PERSON> (Service Provider) uchun noyob identifikator hisoblanadi. Misol: https://www.example.com/sso, https://sso.example.com/myapp", "sp_assertion_consumer_service_url": "xizmat-hamkor metama'lumotlarda ADFS foydalanuvchi muvaffaqiyatli autentifikatsiyadan o'tgandan so'ng SAML-tasdiqlashlarni yuborishi kerak bo'lgan nuqta sifatida ko'rsatadigan manzil. Bu URL-manzil SAML-tasdiqlashlarni qayta ishlaydi va xizmat-hamkor tomonida himoyalangan resurslarga yoki ilovalarga kirishni ta'minlaydi. Misol: https://www.example.com/sso/consumer", "sp_single_logout_service_url": "xizmat-hamkor tizimdan chiqish so'rovlarini qayta ishlash uchun metama'lumotlarda ko'rsatadigan manzil. Bu URL-manzil foydalanuvchi identifikatsiya xizmati (masalan, ADFS) tomonida seansni yakunlaganda, xizmat-hamkor tomonida foydalanuvchi seanslarini yakunlash uchun ishlatiladi. Misol: https://www.example.com/slo", "x509_cert": "matn ma'lumotlar tuzilmasi shaklida taqdim etilgan X.509 sertif<PERSON>ti, ular odatda \"-----BEGIN CERTIFICATE-----\" bilan boshlanib, \"-----END CERTIFICATE-----\" bilan tugaydigan uzun satrlar ko'rinishida bo'ladi.", "cert_private_key": "bu sertifikat bilan bog'liq bo'lgan maxfiy kriptografik kalit bo'lib, turli xil shifrlash va deshifrlash operatsiyalari, shuning<PERSON> ma'lumotlarni imzolash va imzoni tekshirish uchun ishlatiladi. Shax<PERSON>y kalit odatda tasodifiy baytlar yoki o'n oltilik belgilarning uzun ketma-ketligi shaklida taqdim etiladi. <PERSON><PERSON><PERSON> saqlash<PERSON> so'ng, tizim xavfsizlik maqsadida uni ko'rsatmaydi, maydon bo'sh to'l<PERSON>u<PERSON><PERSON> bilan qoladi.", "idp_entity_id": "ya<PERSON>a kirish tizim<PERSON> (Single Sign-On, SSO) ushbu xizmatni aniq identifikatsiya qilish uchun ishlatiladigan identifikatsiya xizmati (Identity Provider) ning noyob identifikatori (URI). Misol: https://adfs.example.com/adfs/services/trust", "idp_sso_service_url": "xizmat-ham<PERSON> (Service Provider) identifikatsiya xizmatida (Identity Provider) autentifikatsiya qilish kerak bo'lgan foydalanuvchilar uchun autentifikatsiya so'rovlar<PERSON> (yoki ya<PERSON> kiri<PERSON>, SSO so'rovlarini) yo'naltirgan oxirgi nuqta URL-manzili. Misol: https://adfs.example.com/adfs/ls", "idp_single_logout_service_url": "xizmat-ham<PERSON> (Service Provider) identifikatsiya xizmati (Identity Provider) tomonida foydalanuvchi seanslarini yakunlash uchun tizimdan chiqish so'rovlarini (Single Logout) yuboradigan oxirgi nuqta URL-manzili. Misol: https://adfs.example.com/adfs/ls/"}