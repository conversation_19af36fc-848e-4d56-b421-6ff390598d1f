module.exports = {
  root: true,
  env: { browser: true, es2020: true },
  extends: [
    'eslint:recommended',
    'plugin:prettier/recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
    'plugin:i18next/recommended',
    'plugin:storybook/recommended',
  ],
  ignorePatterns: [
    'dist',
    '.eslintrc.cjs',
    'public/ckeditor/**',
    '*.stories.tsx',
    '*.stories.mdx',
    '*.test.tsx',
    '*.test.ts',
    '*.spec.ts',
    '*.spec.tsx',
    'public/locales/ru',
    '**/__mocks__/**',
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['react-refresh', 'unused-imports', 'i18next', 'i18n'],
  rules: {
    'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
    '@typescript-eslint/no-unused-vars': ['error'],
    'unused-imports/no-unused-imports': 'error',
    'i18n/no-russian-character': 2,
    'prettier/prettier': [
      'error',
      {
        endOfLine: 'auto',
      },
    ],
    'i18next/no-literal-string': [
      'error',
      {
        mode: 'jsx-only',
        'jsx-attributes': {
          exclude: [
            'name',
            'className',
            'accept',
            'type',
            'color',
            'size',
            'icon',
            'leftIcon',
            'key',
            'dataKey',
            'style',
            'interval',
            'domain',
            'tick',
            'padding',
            'width',
            'view',
            'to',
            'route',
            'rightIcon',
            'direction',
            'mobilePadding',
            'scale',
            'orientation',
            'stroke',
            'fill',
            'role',
            'id',
            'hiddenType',
            'variant',
            'infoType',
            'height',
            'viewType',
            'controls',
            'viewBox',
            'startDirection',
            'borderRadius',
            'prefix',
            'fit',
            'scrollableTarget',
            'data-testid',
            'containerTestId',
            'dropdownDataId',
            'form',
            'leftIconColor',
            'statPosition',
            'listWrapperId',
          ],
        },
        'object-properties': {
          exclude: [
            'status',
            'fill',
            'fill-stroke',
            'stroke',
            'color',
            'type',
            'name',
            'id',
            'size',
          ],
        },
        callees: {
          exclude: [
            'register',
            'resetField',
            'watch',
            't',
            'setValue',
            'cx',
            'fromISO',
            'transform',
            'handleTrim',
            'getValues',
            'startsWith',
            'handleChangeAnswers',
          ],
        },
        'should-validate-template': true,
      },
    ],
    'react/jsx-uses-react': 'off',
    'react/react-in-jsx-scope': 'off',
  },
}
