variables:
  APP_NAME: edu-frontend
  UBUNTU_BASE_VERSION: 22.04-v1.0.0
  CONFIG_REPO: gitlab.sec-t.ru/secure-t/helm-config
  CHART_VER: v1.0.1

include:
  - project: 'common/gitversion'
    ref: v1.0.3
    file: 'gitversion/gitversion.yaml'
  - project: "secure-t/helm-config"
    ref: v1.0.1
    file: ".deploy-job-ci.yml"

stages:
  - tests
  - build docker # TODO: remove after migrate to k8s prod
  - deploy # TODO: remove after migrate to k8s prod
  - Build:docker
  - Deploy:asap-dev-01
  - Deploy:asap-dev-02
  - Deploy:asap-release-01
  - Deploy:asap-prod-01

Version:
  extends: .gitversion_function
  tags:
    - develop
  rules:
    - if: '$CI_COMMIT_AUTHOR != "${GIT_USER_NAME} <${GIT_USER_EMAIL}>" && $CI_COMMIT_TAG == "" '
      when: never
    - when: on_success

.build-docker: # TODO: remove after migrate to k8s prod
  image: docker:25.0.3
  tags:
    - develop
  services:
    - docker:25.0.3-dind
  variables:
    VERSION: ${FullSemVer}
  before_script:
    - mkdir -p /root/.docker
    - sed -i 's/${FullSemVer}/'$SemVer'/g' package.json
    - cat $AUTH_KEY | docker login --username json_key --password-stdin $REGISTRY_URL
  script:
    - docker build -t "${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:${VERSION}" .
    - docker push "${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:${VERSION}"
  after_script:
    - docker rmi "${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:${VERSION}"

.deploy: # TODO: remove after migrate to k8s prod
  image: ${REGISTRY_URL}/crpcv114gercm1cgl29m/ubuntu-base:${UBUNTU_BASE_VERSION}
  variables:
    VERSION: ${FullSemVer}
  before_script:
    #- set -xv
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 400 ~/.ssh
    - echo "$SSH_PRIVATE_KEY" | tr -d '\r' | ssh-add -
  script:
    - set -xv
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "mkdir -p $APP_NAME"
    - cp $ENV_EDU_FRONTEND .env
    - scp -o StrictHostKeyChecking=no .env $SSH_USER@$SSH_HOST:$APP_NAME/.env
    - sed -i 's/${VERSION}/'$VERSION'/g' docker-compose.yml
    - scp -o StrictHostKeyChecking=no docker-compose.yml $SSH_USER@$SSH_HOST:$APP_NAME/docker-compose.yml
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "cd $APP_NAME && docker compose -f docker-compose.yml up -d --pull always --quiet-pull --force-recreate --remove-orphans"

Tests:recreate-test-image:
  image: docker:25.0.3
  stage: tests
  tags:
    - develop
  services:
    - docker:25.0.3-dind
  before_script:
    - mkdir -p /root/.docker
    - cat $AUTH_KEY | docker login --username json_key --password-stdin $REGISTRY_URL
  script:
    - sed -i 's/${FullSemVer}/'$FullSemVer'/g' package.json
    - docker build -f Dockerfile.tests -t "${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:tests" .
    - docker push "${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:tests"
  rules:
    - if: $CI_COMMIT_REF_PROTECTED == "true" || $CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED == "true"
      when: manual
    - if: $CI_COMMIT_REF_PROTECTED == "false"
      when: manual
  allow_failure: true

Tests:unit:
  image: ${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:tests
  stage: tests
  tags:
    - develop
  script:
    - ln -s $WORKDIR/node_modules node_modules
    - npm run test:unit
  rules:
    - if: $CI_COMMIT_REF_PROTECTED == "true" || $CI_MERGE_REQUEST_TARGET_BRANCH_PROTECTED == "true"
    - if: $CI_COMMIT_REF_PROTECTED == "false"
      when: manual

#----------------------------
.build-docker-kaniko:
  image:
    name: gcr.io/kaniko-project/executor:v1.23.2-debug
    entrypoint: [""]
  script:
    #- set -xv
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"${REGISTRY_URL}\":{\"auth\":\"$(echo -n "json_key:$(cat $AUTH_KEY)" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - export KANIKO_ARGS="--context $CI_PROJECT_DIR
      --dockerfile ${CI_PROJECT_DIR}/Dockerfile
      --destination ${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:${CI_COMMIT_SHORT_SHA}
      --destination ${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:${FullSemVer}
      --destination ${REGISTRY_URL}/crpcv114gercm1cgl29m/${APP_NAME}:${SemVer}"
    - /kaniko/executor ${KANIKO_ARGS}
  needs:
    - Version
  dependencies:
    - Version

#----------------------------

Build: # TODO: remove after migrate to k8s prod
  extends: .build-docker
  stage: build docker
  needs:
    - job: Version
      artifacts: true
    - Tests:unit
  rules:
    - if: $CI_COMMIT_REF_PROTECTED == "true"
      when: manual
    - if: $CI_COMMIT_TAG
      when: manual

Deploy:release: # TODO: remove after migrate to k8s prod
  extends: .deploy
  stage: deploy
  variables:
    SSH_USER: ${SSH_USER}
    SSH_HOST: ${SSH_HOST_PROD_RELEASE}
    ENV_EDU_FRONTEND: ${ENV_RELEASE_EDU_FRONTEND}
    APP_NAME: release/frontend
  script:
    - set -xv
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "mkdir -p $APP_NAME"
    - cp $ENV_EDU_FRONTEND .env
    - scp -o StrictHostKeyChecking=no .env $SSH_USER@$SSH_HOST:$APP_NAME/.env
    - sed -i 's/${VERSION}/'$VERSION'/g' docker-compose.yml
    - sed -i 's/8000:/8001:/g' docker-compose.yml # 8000 used by event-keeper
    - scp -o StrictHostKeyChecking=no docker-compose.yml $SSH_USER@$SSH_HOST:$APP_NAME/docker-compose.yml
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "cd $APP_NAME && docker compose -f docker-compose.yml up -d --pull always --quiet-pull --force-recreate --remove-orphans"
  needs:
    - job: Version
      artifacts: true
    - Build
  rules:
    - if: '$CI_COMMIT_BRANCH == "release"'
      when: manual

Deploy:prod: # TODO: remove after migrate to k8s prod
  extends: .deploy
  stage: deploy
  variables:
    SSH_USER: ${SSH_USER}
    SSH_HOST: ${SSH_HOST_PROD_FRONTEND}
    ENV_EDU_FRONTEND: ${ENV_PROD_EDU_FRONTEND}
  needs:
    - job: Version
      artifacts: true
    - Build
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_TAG'
      when: manual

Deploy:cern-01:
  extends: .deploy
  stage: deploy
  variables:
    SSH_USER: ${SSH_USER}
    SSH_HOST: ${SSH_HOST_ONPREM}
    ENV_EDU_FRONTEND: ${ENV_ONPREM_EDU_FRONTEND}
    ONPREM_PATH: /home/<USER>/onprem-latest
  script:
    - set -xv
    - cp $ENV_EDU_FRONTEND .env
    - scp -o StrictHostKeyChecking=no .env $SSH_USER@$SSH_HOST:$ONPREM_PATH/envs/frontend/.env
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "sed -i 's/^\(FRONTEND_VER=\).*/\1'"$VERSION"'/' $ONPREM_PATH/.env"
    - ssh -o StrictHostKeyChecking=no $SSH_USER@$SSH_HOST "cd $ONPREM_PATH && docker compose -f docker-compose.yml up -d --pull always --quiet-pull --force-recreate --remove-orphans"
  needs:
    - job: Version
      artifacts: true
    - Build
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_TAG'
      when: manual

# Build kaniko
#----------------------------
Build:asap-dev-01:
  extends: .build-docker-kaniko
  stage: Build:docker
  tags:
    - development
  needs:
    - Version
    - Tests:unit
  dependencies:
    - Version
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: on_success

Build:asap-dev-02:
  extends: .build-docker-kaniko
  stage: Build:docker
  tags:
    - development
  needs:
    - Version
    - Tests:unit
  dependencies:
    - Version
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: '$CI_COMMIT_BRANCH =~ /^dev(elop(ment)?)?$/'
      when: on_success
    - if: $CI_COMMIT_TAG
      when: on_success

Build:asap-release-01:
  extends: .build-docker-kaniko
  stage: Build:docker
  tags:
    - development
  needs:
    - Version
    - Tests:unit
  dependencies:
    - Version
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_BRANCH == "release"
      when: on_success

Build:asap-prod-01:
  extends: .build-docker-kaniko
  stage: Build:docker
  tags:
    - production
  needs:
    - Version
    - Tests:unit
  dependencies:
    - Version
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_TAG
      when: on_success

#----------------------------

# Deploy k8s
#----------------------------
asap-dev-01:edu-frontend:
  extends: .deploy-k8s
  stage: Deploy:asap-dev-01
  tags:
    - development
  variables:
    NAMESPACE: asap-dev-01
    KUBE_CONFIG: ${KUBE_CONFIG_DEV}
    VERSION: ${FullSemVer}
  needs:
    - Version
    - Build:asap-dev-01
  dependencies:
    - Version
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - when: manual

asap-dev-02:edu-frontend:
  extends: .deploy-k8s
  stage: Deploy:asap-dev-02
  tags:
    - development
  variables:
    NAMESPACE: asap-dev-02
    KUBE_CONFIG: ${KUBE_CONFIG_DEV}
    VERSION: ${FullSemVer}
  needs:
    - Version
    - Build:asap-dev-02
  dependencies:
    - Version
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: '$CI_COMMIT_BRANCH =~ /^dev(elop(ment)?)?$/'
      when: manual
    - if: $CI_COMMIT_TAG
      when: manual

asap-release-01:edu-frontend:
  extends: .deploy-k8s
  stage: Deploy:asap-release-01
  tags:
    - development
  variables:
    NAMESPACE: asap-release-01
    KUBE_CONFIG: ${KUBE_CONFIG_DEV}
    VERSION: ${FullSemVer}
  needs:
    - Version
    - Build:asap-release-01
  dependencies:
    - Version
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_BRANCH == "release"
      when: manual

asap-prod-01:edu-frontend:
  extends: .deploy-k8s
  stage: Deploy:asap-prod-01
  tags:
    - production
  variables:
    NAMESPACE: asap-prod-01
    KUBE_CONFIG: ${KUBE_CONFIG_PROD}
    VERSION: ${SemVer}
  needs:
    - Version
    - Build:asap-prod-01
  dependencies:
    - Version
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_PIPELINE_SOURCE == "merge_request_event"'
      when: never
    - if: $CI_COMMIT_TAG
      when: manual

#----------------------------