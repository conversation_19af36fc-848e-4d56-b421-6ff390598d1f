{"name": "secure-t-frontend-app", "private": true, "version": "${SemVer}", "type": "commonjs", "scripts": {"dev": "vite --config vite.config.mts", "start:local": "vite --config vite.config.mts", "start:offline": "vite --mode offline --config vite.config.mts", "start:offline-local": "vite --mode offline-local", "build": "tsc && vite build --config vite.config.mts", "build:win": "tsc && vite build --mode win", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "prepare": "husky install", "server:start": "cross-env NODE_ENV=development AUTOPREFIXER_GRID=true env-cmd -f .env.local webpack --config=webpack.server.config.js", "server:build": "NODE_ENV=production AUTOPREFIXER_GRID=true rm -rf server && webpack --config=webpack.server.config.js", "new:component": "hygen new component", "sb": "npm run storybook", "storybook": "storybook dev -p 9009", "build-storybook": "storybook build", "test:unit": "jest", "test:unit:coverage": "npx jest --coverage", "test:unit:coverage:changed": "npx jest --coverage --findRelatedTests", "test:sb": "npx loki test", "test:sb:withStatic": "npx loki test --requireReference --reactUri file:./storybook-static --chromeFlags=\"--headless --disable-gpu --hide-scrollbars --no-sandbox\"", "test:sb:withBuild": "npm run build-storybook && npx loki test --requireReference --reactUri file:./storybook-static --chromeFlags=\"--headless --disable-gpu --hide-scrollbars --no-sandbox\"", "test:sb:update": "npx loki update", "test:sb:update:withStatic": "npx loki update --reactUri file:./storybook-static --chromeFlags=\"--headless --disable-gpu --hide-scrollbars --no-sandbox\"", "test:sb:update:withBuild": "npm run build-storybook && npx loki update --reactUri file:./storybook-static --chromeLoadTimeout=600000 --chromeDockerImage=yukinying/chrome-headless-browser-stable:118.0.5993.117 --chromeFlags=\"--headless --disable-gpu --hide-scrollbars --no-sandbox\"", "test:sb:approve": "npx loki approve", "test:sb:ci": "npm run test:sb:withBuild", "test:visual": "npx playwright test", "test:visual:update": "npx playwright test --update-snapshots", "test:visual:ci": "npx playwright test --reporter=html,line --output=playwright-report --workers=1", "reports:bundle": "vite build --config vite.config.mts --mode report"}, "dependencies": {"@codemirror/lang-cpp": "^6.0.2", "@codemirror/lang-go": "^6.0.1", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-javascript": "^6.2.3", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.1.7", "@codemirror/legacy-modes": "^6.4.3", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.3", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@editorjs/checklist": "^1.6.0", "@editorjs/code": "^2.9.0", "@editorjs/delimiter": "^1.4.0", "@editorjs/embed": "^2.7.4", "@editorjs/header": "^2.8.7", "@editorjs/image": "^2.9.3", "@editorjs/inline-code": "^1.5.1", "@editorjs/link": "^2.6.2", "@editorjs/list": "^2.0.2", "@editorjs/marker": "^1.4.0", "@editorjs/paragraph": "^2.11.6", "@editorjs/quote": "^2.6.0", "@editorjs/raw": "^2.5.0", "@editorjs/simple-image": "^1.6.0", "@editorjs/table": "^2.3.0", "@editorjs/warning": "^1.4.0", "@hookform/resolvers": "^3.6.0", "@internationalized/date": "^3.8.0", "@originjs/vite-plugin-commonjs": "^1.0.3", "@reduxjs/toolkit": "^2.2.7", "@replit/codemirror-lang-csharp": "^6.2.0", "@sn.secure-t-org/ckeditor4-react": "^1.1.5", "@storybook/preview-api": "^8.1.10", "@testing-library/react-hooks": "^8.0.1", "@types/d3": "^7.4.3", "@types/react-redux": "^7.1.33", "@types/uuid": "^9.0.7", "@uiw/codemirror-theme-bbedit": "^4.23.8", "@uiw/react-codemirror": "^4.23.8", "axios": "^1.6.2", "babel-preset-vite": "^1.1.3", "classnames": "^2.3.2", "copy": "^0.3.2", "d3": "^7.9.0", "d3-time-format": "^4.1.0", "editorjs-audio-tool": "^1.0.1", "editorjs-drag-drop": "^1.1.14", "editorjs-text-color-plugin": "git+https://github.com/dariox64/editorjs-text-color-plugin.git", "editorjs-undo": "^2.0.28", "embla-carousel": "^8.3.0", "embla-carousel-react": "^8.3.0", "esbuild-plugin-react-virtualized": "^1.0.4", "express": "^4.18.2", "express-handlebars": "^7.1.2", "html-react-parser": "^5.1.10", "i": "^0.3.7", "i18next": "^23.7.6", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.5.2", "i18next-intervalplural-postprocessor": "^3.0.0", "jest-environment-jsdom": "^29.7.0", "marked": "^15.0.3", "npm": "^10.2.5", "path": "^0.12.7", "rc-slider": "^11.1.8", "rc-tree": "^5.8.8", "react": "^18.2.0", "react-aria": "^3.39.0", "react-aria-components": "^1.9.0", "react-dom": ">=16.8.0", "react-dropzone": "^14.2.3", "react-editor-js": "^2.1.0", "react-hook-form": "^7.54.2", "react-i18next": "^13.5.0", "react-infinite-scroll-component": "^6.1.0", "react-loading-skeleton": "^3.3.1", "react-markdown": "^9.0.1", "react-player": "^2.16.0", "react-qr-code": "^2.0.12", "react-redux": "^8.1.3", "react-router-dom": "^6.19.0", "react-shadow": "^20.4.0", "react-stately": "^3.37.0", "react-swipeable": "^7.0.1", "react-virtualized": "^9.22.5", "recharts": "2.10.1", "reselect": "^5.1.1", "screenfull": "^6.0.2", "storybook-css-modules": "^1.0.8", "usehooks-ts": "^3.1.0", "uuid": "^8.3.2", "webpack": "^5.90.0", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/plugin-transform-react-jsx": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@chromatic-com/storybook": "^1.5.0", "@playwright/experimental-ct-react": "^1.50.1", "@playwright/test": "^1.50.1", "@storybook/addon-actions": "8.2.9", "@storybook/addon-essentials": "^8.1.5", "@storybook/addon-interactions": "^8.1.5", "@storybook/addon-links": "^8.1.5", "@storybook/addon-onboarding": "^8.1.5", "@storybook/addon-themes": "^8.1.7", "@storybook/blocks": "^8.1.5", "@storybook/builder-vite": "^8.1.6", "@storybook/mdx2-csf": "^1.1.0", "@storybook/react": "^8.1.5", "@storybook/react-vite": "^8.1.5", "@storybook/test": "^8.1.5", "@storybook/test-runner": "^0.21.0", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/brainhubeu__react-carousel": "^2.0.8", "@types/express": "^4.17.21", "@types/express-handlebars": "^6.0.0", "@types/jest": "^29.5.12", "@types/rc-slider": "^8.6.6", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-virtualized": "^9.21.30", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "babel-jest": "^29.7.0", "clean-webpack-plugin": "^4.0.0", "cross-env": "^7.0.3", "env-cmd": "^10.1.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-i18n": "^2.3.1", "eslint-plugin-i18next": "^6.0.3", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-storybook": "^0.8.0", "eslint-plugin-unused-imports": "^3.1.0", "husky": "^8.0.3", "hygen": "^6.2.11", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-css-modules-transform": "^4.4.2", "jest-html-reporters": "^3.1.7", "lint-staged": "^15.2.2", "loki": "^0.35.0", "nodemon-webpack-plugin": "^4.8.2", "postcss-nesting": "^12.0.1", "prettier": "^3.2.5", "react-docgen": "^7.1.0", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.69.5", "storybook": "8.2.9", "storybook-addon-pseudo-states": "^4.0.2", "storybook-addon-themes": "^6.1.0", "storybook-react-i18next": "^3.1.1", "ts-jest": "^29.1.4", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "typescript": "^5.2.2", "url": "^0.11.3", "vite": "^5.0.0", "vite-jest": "^0.1.4", "vite-plugin-copy": "^0.1.6", "vite-plugin-handlebars": "^2.0.0", "vite-plugin-static-copy": "^1.0.1", "vite-tsconfig-paths": "^4.2.1", "webpack-cli": "^5.1.4"}, "peerDependencies": {"react-dom": ">=16.8.0"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5"}, "lint-staged": {"*.(js|jsx|ts|tsx|json|css|scss|md)": ["prettier --write"], "*.(js|ts|tsx)": ["eslint --fix"]}, "packageManager": "pnpm@9.5.0+sha512.140036830124618d624a2187b50d04289d5a087f326c9edfc0ccd733d76c4f52c3a313d4fc148794a2a9d81553016004e6742e8cf850670268a7387fc220c903"}