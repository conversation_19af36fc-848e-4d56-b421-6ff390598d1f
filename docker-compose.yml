version: '3.9'

services:
  frontend:
    image: 'cr.yandex/crpcv114gercm1cgl29m/edu-frontend:${VERSION}'
    container_name: frontend
    networks:
      - asap
    restart: always
    logging:
      driver: fluentd
      options:
        fluentd-address: "prod-logging.ru-central1.internal:24224"
        tag: "$FLUENTBIT_TAG"
        fluentd-async: "true"
        labels: "com.docker.compose.service"
    env_file:
      - .env
    ports:
      - 8000:8080

networks:
  asap:
    name: asap
    external: true
