{
  "compilerOptions": {
    "baseUrl": "./src",
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    /* Linting */
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "types": ["@testing-library/jest-dom", "@testing-library/react", "@testing-library/user-event", "./global.d.ts"],
    "paths": {
      "@/*": ["./*"],
      "@assets/*": ["/assets/*"]
    },
    "preserveConstEnums": true
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
